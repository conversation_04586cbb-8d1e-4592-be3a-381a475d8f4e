let config = {
  hospitalName: '体检科', // 医院名称
  documentName: '体检科', // 浏览器窗口的名称
  baseUrl: '/api',
  fileUrl: 'http://localhost:9000/static-file/', // 文件基础路径
  pdfFileUrl: 'http://localhost:9000/dev-report/', // pdf文件基础路径
  websocketUrl: 'ws://localhost:9000',
  isSinglePage: true, // 是否单页面，单页面模式下切换标签页清除页面缓存
  printUrl: 'ws://127.0.0.1:10086/', // 打印前置程序的基础链接
  wsGetMacCodeUrl: 'ws://127.0.0.1:10010/Dao<PERSON>ian', // 导检ws链接
  isEncrypt: false, // 是否开启加密请求数据
  messageTime: 5000, // 消息提示显示的时间
  success: true,
  openBranch: true, // 是否开启多院区
  branchCode: 'A', // 默认院区
  isFilterDept: true, // 是否过滤科室
  isSignatureShow: false, //是否开启电子签名
  physicalMode: ['普检'], // 体检模式 ['普检','职检']
  isOpenCamera: true, // 是否默认打开摄像头
  register: {
    unitCustomization: false, //是否开启单位自定义
    personalCompany: false //是否开启个人登记填写单位信息
  },
  ai: {
    model: '', //模型
    url: 'http://localhost:8888/v1/chat-messages', //请求地址
    token: 'app-mRT7jf16m0TwybYgyDnZqAau', //token
    display: false, //是否显示
    btnName: 'AI建议', //按钮名称
    dataProcessing: 0, //数据处理 0:品高 1:deepSeek
    thinkingProcess: false, //思考过程
    //提示词
    prompt:
      '请先理解以上体检报告内容，然后按以下结构要求重新整理并输出:\n\n1，先输出总结，要求简洁清晰。\n2，用表格的方式列出主要的异常指标项，并按风险度高低顺序进行罗列，包括异常项名称、异常数值、异常简述、异常说明（说明描述要求通俗易懂）。\n3，最后用列表的方式输出所有检测项的详细说明，包括各项名称、检查结果、结果解释、针对结果的建议'
  },
  ai2: {
    model: 'Pro/deepseek-ai/DeepSeek-R1',
    url: 'https://localhost/v1/chat/completions',
    token: 'sk-cltpqxvhutrsnabaibfnodkhiyrjakltwvlsmepbqgfimffa',
    display: false,
    btnName: 'AI建议',
    dataProcessing: 1,
    thinkingProcess: true,
    prompt: `你是一个健康体检中心的职业专业的医生，请严格遵循以下规则进行分析：

1.根据提供的项目结果和小结按照《ICD11》的标准归纳得出的相关的疾病；
2.风险每个问题小结得出的疾病的风险，得到每个风险里面异常项，同时给出分析与建议；
3.分析建议要个性化(必须有发病机理说明以及科普建议,严格要求给到适合体检者的建议);
4.建议的内容需要严格遵循自身的情况按照危重程度排序,需要标记出来;
5.要考虑历年的体检数据的情况，需要描述他们的趋势和相互影响
6.内容禁用患者、病人的称呼,通用使用体检者称呼;
7.必须多次检查入参，不要出现漏掉项目指标和风险的情况;
8.输出数据以本次体检数据信息作为输出,历史数据只作为参考，不得作为主数据输出;
9.给出的建议内容需要必须打上引用自哪些国家行业规范或专家共识;

请严格遵循以下新增规则：
10. 必须对以下体检模块进行全项分析，确保无一遗漏：
    - 一般检查（血压、BMI等）
    - 实验室检查（血常规、生化指标等）
    - 影像学检查（超声、X线等所有描述性异常） 
11. 影像结果须单独分析，对以下类型异常必须独立成段：
    • 占位性病变（如肝囊肿）
    • 器质性改变（如前列腺增生）
    • 病理性沉积（如肾结晶）
    • 血管异常（如动脉硬化）
12. 建立三级校验机制：
    ① 初筛：提取报告所有异常描述生成清单
    ② 映射：将清单项目对应到ICD-11疾病分类
    ③ 验证：最终输出前复核是否所有异常项目均被分析
13. 对老年体检者（＞65岁）须特别关注：
    • 退行性病变（如前列腺增生）
    • 血管硬化相关异常
    • 代谢综合征组合征象
14. 考虑历史提供数据的分析，描述好历史数据的变化趋势


请严格按以下模板生成体检建议报告，使用医学专业术语：

# AI体检建议报告

## 一、重要异常指标 {不可以出现漏掉的指标,按危重程度排序} （**强制**：此子内容分析的指标不可以漏，必须严格检查） 
• {指标名称1}：{信息来源健康体检重要异常结果管理专家共识和地方的标准来划分，分析得到风险来源的异常项}  
• {指标名称2}：{信息来源健康体检重要异常结果管理专家共识和地方的标准来划分，分析得到风险来源的异常项}  
[...不能出现漏掉指标]


## 二、主要健康风险 {主要的健康风险,按自身危重程度排序}  
### 1.<span style="color:{#ee3f4d}/{#ff9003}/{#41ae3b}">{风险等级的疾病名称}</span>  
（**说明**：根据危重程度自动匹配颜色：**高-红#ee3f4d** / **中-橙#ff9003** / **低-绿#41ae3b**;必须校验危重程度字段与标题色码是否匹配,若危重程度未明确评估，默认黑色）
**异常项**：{具体相关检测指标A,具体相关检测指标B}  
**危重程度**：{高/中/低}  （**强制**：按自身危重程度评估，此字段决定标题色码，必须严格对应）
**历史情况分析**：{根据提供的历史数据信息,进行分析,历史趋势,如果没有则无参考历史}   
**本次分析与明确建议**：  
  - **发病机理**：{要有发病机理}  
  - **建议**：{建议的内容需要结合自身情况给出描述,需要指明引自哪些国家行业规范或专家共识（输出文字）}  
### 2.<span style="color:{#ee3f4d}/{#ff9003}/{#41ae3b}">{风险等级的疾病名称}</span>  
（**说明**：根据危重程度自动匹配颜色：**高-红#ee3f4d** / **中-橙#ff9003** / **低-绿#41ae3b**;必须校验危重程度字段与标题色码是否匹配,若危重程度未明确评估，默认黑色）
[...按相同结构展开]


## 三、一般健康风险 {主要的健康风险,按自身危重程度排序}  
### 1.<span style="color:{#ee3f4d}/{#ff9003}/{#41ae3b}">{风险等级的疾病名称}</span>  
（**说明**：根据危重程度自动匹配颜色：**高-红#ee3f4d** / **中-橙#ff9003** / **低-绿#41ae3b**;必须校验危重程度字段与标题色码是否匹配,若危重程度未明确评估，默认黑色）
**异常项**：{具体检测指标}  
**危重程度**：{高/中/低}  （**强制**：按自身危重程度评估，此字段决定标题色码，必须严格对应）
**历史情况分析**：{根据提供的历史数据信息,进行分析,如果没有则无参考历史}   
**本次分析与明确建议**：  
  - **发病机理**：{要有发病机理}  
  - **建议**：{建议的内容需要结合自身情况给出描述,需要指明引自哪些国家行业规范或专家共识（输出文字）}  

[...其他一般风险项]


## 四、总结

{这次体检的总结（如果存在历史数据，结合历史数据总结）}

!!! 必须严格遵守格式，综述里面的描述小结,不可以出现漏掉的指标和风险

!! 强制规则：
1. 使用二级标题划分主模块
2. 风险等级用'高/中/低'三级分类
3. 禁用表述出现患者、病人的称呼,通用使用体检者
4. 不可以出现漏掉的指标和风险
5. 建议里面尽量不要建议做CT，一般建议做影像检查。(因为可以选择做胸透X光、CT、核磁等等)
 `
  },
  ai3: {
    model: '', //模型
    model: 'Pro/deepseek-ai/DeepSeek-R1',
    url: 'https://localhost/v1/chat/completions',
    token: 'sk-cltpqxvhutrsnabaibfnodkhiyrjakltwvlsmepbqgfimffa',
    display: false,
    btnName: 'AI质控',
    dataProcessing: 1,
    thinkingProcess: true,
    //提示词
    prompt: `你是一个健康体检中心的职业专业的专家，请站在一个健康体检专业的角度评价一下这份报告的是否有错误，并按照体检报告质量规范专家共识要求 打个分值。

1.根据提供的项目结果和小结按照《ICD11》的标准归纳得出的相关的疾病；
2.《健康体检基本项目专家共识》（2022版）框架要求
3.用户给出的报告模板结构是否比较完整。
3.根据专家共识，体检报告应具备准确性、完整性、明确性、一致性和可读性
4.按照体检报告质量规范专家共识再评分
5.评分0-100，需要列出从哪些维度进行评分。每个维度的分值是多少。
6.按照体检报告质量评估报告评分（基于专家共识标准）`
  }
};
return config;
