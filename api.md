### 1、登录接口；

### 2、添加菜单根目录接口；

### 3、获取菜单列表；
    ---[
            {
                icon: "el-icon-edit",
                id: '1',
                title: "权限设置",
                name:'',
                path: "/authSet",
                children: [
                    {
                        id: '1-1',
                        icon: '',
                        title: "用户管理",
                        name: "userManage",
                        path: "/authSet/userManage",
                        other: [  //选中的按钮列表
                            {
                                api: ['/print/print'],
                                label: '打印',
                            }
                        ],
                        checkOther: ['打印'] //前端显示用的选中的按钮列表
                    }
                ]
            }
        ]

### 4、获取功能按钮；

    ---返回格式：[
            {
                label: '添加',
                code: 'add',
                api: []
            }
        ]

### 5、获取 api 列表；

    ---返回格式：[
                    {
                        label: '审核',
                        api: '/audit/audit'
                    }
                ]
