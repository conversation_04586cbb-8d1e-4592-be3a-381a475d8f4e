<template>
  <div class="navDialog">
    <div
      class="navBody"
      v-for="(item, index) in dataMenus"
      :key="index"
      v-show="dataMenus.length > 0"
    >
      <div class="titleDiv">
        <span class="titleSpan">{{ item.title }}</span>
        <span class="iconfont icon-xiayige"></span>
      </div>
      <ul class="navUl">
        <li
          v-for="(val, idx) in item.children"
          :key="idx"
          @click="navClick(val.name)"
        >
          {{ val.title }}
        </li>
      </ul>
    </div>
    <el-empty :image-size="200" v-show="dataMenus.length < 1"></el-empty>
  </div>
</template>

<script> 
export default {
  name: 'navDialog',
  props: {
    dataMenus: {
      type: Array,
      require: false,
      default: []
    },
    navTitleClick: {
      type: Function,
      default: () => {
        return () => {};
      }
    }
  },

 

  methods: {
    navClick(name) {
      this.navTitleClick(name);
    }

  }
};
</script>

<style lang="less" scoped>
.navDialog {
  overflow: auto;
  border: 0.5px solid #ccc;
  flex: 1;
  .navBody {
    display: flex;
    flex-direction: row;
    padding: 3px 15px 3px 15px;
    border-bottom: 0.5px solid #ccc;
  }
  .titleDiv {
    min-width: 145px;
    font-size: 16px;
    display: flex;
    justify-content: space-between;
    .titleSpan {
      color: #000;
      font-weight: bold;
    }
    .iconfont {
      padding-right: 15px;
      margin-top: 4px;
      font-size: 16px;
    }
  }
  .navUl {
    font-size: 14px;
    //color: #1770df;

    li {
      padding: 0 10px;
      border-left: 1px solid #ccc;
      cursor: pointer;
      margin-bottom: 10px;
      float: left;
    }
  }
}
</style>
