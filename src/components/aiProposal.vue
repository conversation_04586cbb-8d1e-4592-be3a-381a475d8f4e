<template>
  <el-popover
    placement="bottom"
    width="750"
    trigger="click"
    popper-class="ai-popover"
    @show="sendMessage"
  >
    <div class="ai-txt">
      <div v-if="aiContent || reflection" class="aiBox">
        <div class="reflection-container" v-if="reflection && thinkingProcess">
          <div
            class="header"
            @click="isReflectionExpanded = !isReflectionExpanded"
          >
            <span>思考过程</span>
            <el-icon :class="['arrow', { expanded: isReflectionExpanded }]">
              <arrow-down />
            </el-icon>
          </div>
          <div v-show="isReflectionExpanded" class="reflection-content">
            <div
              v-html="reflection"
              class="aiContent markdown-body markdown reflection"
            ></div>
          </div>
        </div>

        <div v-html="aiContent" class="aiContent markdown-body markdown"></div>
      </div>

      <el-empty
        style="width: 100%; flex: 1"
        v-else
        :description="aiLoading ? '正在生成中...' : '暂无数据'"
      ></el-empty>
      <div class="btn">
        <el-button
          style="
            background-color: rgba(23, 112, 223, 0.2) !important;
            color: #409eff !important;
          "
          class="btn-ai"
          size="mini"
          :icon="aiLoading ? 'el-icon-loading' : 'el-icon-s-opportunity'"
          @click="regenerate"
          :disabled="aiLoading"
          >{{ aiLoading ? '正在生成中...' : '重新生成' }}</el-button
        >
        <el-button
          style="
            background-color: rgba(23, 112, 223, 0.2) !important;
            color: #409eff !important;
          "
          class="btn-ai"
          size="mini"
          icon="el-icon-video-play"
          @click="abort"
          :disabled="!aiLoading"
          >中断生成</el-button
        >
      </div>
    </div>
    <el-button
      :disabled="btnDisabled"
      slot="reference"
      style="
        background-color: rgba(23, 112, 223, 0.2) !important;
        color: #409eff !important;
      "
      class="btn-ai"
      size="mini"
      icon="el-icon-s-opportunity"
      >{{ btnName }}</el-button
    >
  </el-popover>
</template>

<script>
import { sendChatMessage } from '@/common/aiAip.js';
import MarkdownIt from 'markdown-it';
import hljs from 'highlight.js';
export default {
  name: '',
  components: {},
  props: {
    url: {
      type: String,
      required: true
    },
    //参数配置
    parameter: {
      type: Object,
      required: true
    },
    //数据处理
    dataProcessing: {
      type: Number,
      required: true
    },
    //按钮
    btnName: {
      type: String,
      default: 'AI建议'
    },
    //ai建议状态
    btnDisabled: {
      type: Boolean,
      default: true
    },
    // processingData: {
    //   type: Function,
    //   required: true,
    // },
    thinkingProcess: {
      type: Boolean,
      required: false
    }
  },
  data() {
    return {
      aiLoading: false,
      isReflectionExpanded: true,
      aiContent: '',
      displayText: '',
      reflectionText: '',
      controller: null,
      reflection: ''
    };
  },
  watch: {},
  computed: {},
  methods: {
    processingData(res, type) {
      const dataProcessors = {
        0: (res) => {
          if (res.event !== 'ping') {
            let obj = JSON.parse(res.data);
            if (obj.event == 'message') {
              return {
                reflection: '',
                content: obj.answer
              };
            }
          }
        },
        1: (res) => {
          let obj = JSON.parse(res.data);
          let data = {
            reflection: '',
            content: ''
          };
          data.reflection = obj.choices[0].delta.reasoning_content
            ? obj.choices[0].delta.reasoning_content
            : '';

          data.content = obj.choices[0].delta.content
            ? obj.choices[0].delta.content
            : '';

          return data;
        }
      };
      return dataProcessors[type](res);
    },
    //重新生成
    regenerate() {
      this.clearText();

      this.sendMessage();
    },
    async sendMessage() {
      if (this.aiContent || this.aiLoading) return;
      this.aiLoading = true;
      this.aiContent = '';
      this.controller = new AbortController();
      try {
        let body = {
          ...this.parameter,
          signal: this.controller.signal
        };
        sendChatMessage(this.url, body, this.onMessage, this.onClose);
      } catch (error) {
        console.error('Failed to send message:', error);
        this.aiLoading = false;
      }
    },
    onMessage(res) {
      const aiContent = document.querySelectorAll('.aiBox');
      if (aiContent.length)
        aiContent.forEach((item) => {
          let currentScrollTop = item.scrollTop;
          let currentMaxScroll = item.scrollHeight - item.clientHeight;
          let isAtBottom = currentScrollTop >= currentMaxScroll - 100;
          if (isAtBottom) {
            item.scrollTop = item.scrollHeight;
          }
        });
      const md = new MarkdownIt({
        highlight: function (str, lang) {
          if (lang && hljs.getLanguage(lang)) {
            try {
              return (
                '<pre class="hljs"><code>' +
                hljs.highlight(lang, str, true).value +
                '</code></pre>'
              );
            } catch (__) {}
          }
          return (
            '<pre class="hljs"><code>' +
            md.utils.escapeHtml(str) +
            '</code></pre>'
          );
        },
        html: true,
        breaks: false
      }).use(require('markdown-it-highlightjs'));
      let { reflection, content } = this.processingData(
        res,
        this.dataProcessing
      );

      if (reflection) {
        // this.isReflectionExpanded = true;
        this.reflectionText += reflection;
        this.reflection = md.render(this.reflectionText);
      }
      if (content) {
        this.displayText += content;
        this.aiContent = md.render(this.displayText);
      }
    },
    onClose() {
      this.aiLoading = false;
    },
    //中断请求
    abort() {
      if (this.controller) {
        this.controller.abort();
        this.controller = null;
        this.aiLoading = false;
      }
    },
    //清空
    clear() {
      this.abort();
      this.clearText();
    },
    clearText() {
      this.aiContent = '';
      this.displayText = '';
      this.isReflectionExpanded = true;
      this.reflectionText = '';
      this.reflection = '';
    },
    //动态设置标题颜色
    setRiskLevelColors(dom) {
      if (!dom) return;
      try {
        const riskClassMap = { 高: 'red', 中: 'yellow', 低: 'green' };

        dom.querySelectorAll('li').forEach((l) => {
          const riskElement = l.querySelector('strong');
          if (!riskElement?.textContent.includes('危重程度')) return;

          const riskLevel = riskElement.nextSibling?.textContent.trim();
          if (!riskLevel) return;

          // 查找前序标题元素
          let titleElement = null;
          // let current = l.previousElementSibling;
          let parentNode = l.parentNode;
          titleElement = parentNode.previousElementSibling;
          // let pCount = 0;

          // while (current && pCount < 4) {
          //   if (current.tagName === 'P') {
          //     if (++pCount === 4) {
          //       titleElement = current;
          //       break;
          //     }
          //   }
          //   current = current.previousElementSibling;
          // }

          const titleStrong = titleElement?.querySelector('strong');
          if (!titleStrong) return;

          // 匹配颜色类别
          const colorKey = Object.keys(riskClassMap).find((k) =>
            riskLevel.includes(k)
          );
          if (colorKey) titleStrong.classList.add(riskClassMap[colorKey]);
        });
      } catch (e) {
        console.error('setRiskLevelColors error:', e);
      }
    }
  },
  created() {},
  mounted() {},
  beforeDestroy() {
    this.clear();
  }
};
</script>
<style scoped></style>
<style lang="less">
.ai-popover {
  // 新增折叠样式
  .reflection-container {
    border: 1px solid #ebeef5;
    border-radius: 4px;
    margin-bottom: 12px;

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 12px;
      background-color: #f8f9fa;
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background-color: #f1f2f3;
      }

      .arrow {
        transition: transform 0.2s;
        font-size: 12px;
        color: #606266;

        &.expanded {
          transform: rotate(180deg);
        }
      }
    }

    .reflection-content {
      padding: 12px;
      background: #fafafa;
      border-top: 1px solid #ebeef5;

      .reflection {
        color: #666;
        font-size: 13px;
        line-height: 1.3;
        background: #fafafa;
      }
    }
  }
  .red {
    color: #ee3f4e;
  }

  .yellow {
    color: #ff9003;
  }

  .green {
    color: #41ae3b;
  }
  .reflection {
  }
  .ai-txt {
    width: 100%;
    display: flex;
    // white-space: pre-wrap;
    overflow: hidden;
    flex-direction: column;
    gap: 10px;
    height: calc(100vh - 80px);
    .aiBox {
      overflow-y: auto;
    }
    & > .aiContent {
      width: 100%;
      flex: 1;
      // overflow-wrap: break-word;
      // overflow-y: auto;
      // white-space: normal;
    }
  }

  // table {
  //   width: 100%;
  //   border-collapse: collapse;
  //   margin: 20px 0;
  //   font-size: 14px;
  //   text-align: left;
  // }

  // th,
  // td {
  //   padding: 5px;
  //   border: 1px solid #ddd;
  // }

  th {
    // background-color: #f2f2f2;
    white-space: nowrap;
  }

  // tr:nth-child(even) {
  //   background-color: #f9f9f9;
  // }

  // h1,
  // h2,
  // h3,
  // h4,
  // h5,
  // h6{
  //   margin: 10px 0 20px 0;
  // }
  // p,
  // ol,
  // li {
  //   margin: 10px 0;
  // }
  // li,ol{
  //   padding-left: 20px;
  // }

  .btn {
    margin-top: auto;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  pre {
    background-color: #f5f5f5;
    padding: 10px;
    border-radius: 5px;
  }
}
</style>
