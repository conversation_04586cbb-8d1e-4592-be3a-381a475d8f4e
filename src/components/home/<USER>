<template>
  <div class="homeStackBar">
    <!-- 堆叠柱状图 -->
    <div :id="className" class="chart"></div>
  </div>
</template>
<script>
import * as echarts from 'echarts';
require('echarts/theme/macarons');
export default {
  name: 'homeStackBar',
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    title: {
      type: String,
      default: ''
    },
    legendData: {
      type: Array,
      default: []
    },
    xAxisData: {
      type: Array,
      default: []
    },
    seriesData: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      chart: null
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.drawChart();
    });
  },
  methods: {
    drawChart() {
      const that = this;
      that.chart = echarts.init(document.getElementById(`${that.className}`));
      that.chart.setOption({
        color: ['#3CB34F', '#1770DF', '#FAB63B', '#B2BEC3'],
        title: {
          text: that.title
          // left: "20"
        },
        legend: {
          data: that.legendData,
          right: '10'
        },
        tooltip: {},
        grid: {
          left: '30',
          bottom: '100',
          top: '60',
          right: '10'
        },
        xAxis: {
          data: that.xAxisData,
          axisTick: {
            //y轴刻度线
            show: false
          },
          axisLabel: {
            // interval: 0,
            // rotate: 30,
            color: '#2D3436',
            formatter: function (value) {
              //x轴的文字改为竖版显示
              return value
                .replace('(', '')
                .replace(')', '')
                .split('')
                .join('\n');
            }
          },
          axisLine: {
            //x轴
            lineStyle: {
              color: '#B2BEC3'
            }
          }
        },
        yAxis: {
          type: 'value',
          axisTick: {
            //y轴刻度线
            show: false
          },
          axisLine: {
            //y轴
            show: false
          },
          splitLine: {
            // y轴虚线
            show: false
          },
          axisLabel: {
            show: false
          }
        },
        series: that.seriesData
      });
    }
  }
};
</script>
<style lang="less" scoped>
.homeStackBar {
  background: #fff;
  border-radius: 4px;
  padding: 18px;
  margin-bottom: 20px;
  .chart {
    width: 704px;
    height: 320px;
  }
}
</style>
