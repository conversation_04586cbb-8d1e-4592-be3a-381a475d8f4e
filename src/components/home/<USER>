<template>
  <div class="homeLine">
    <!-- 折线图 -->
    <div :id="className" class="chart"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
require('echarts/theme/macarons');
export default {
  name: 'homeLine',
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    title: {
      type: String,
      default: ''
    },
    xAxisData: {
      type: Array,
      default: []
    },
    seriesData: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      chart: null
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.drawChart();
    });
  },
  methods: {
    drawChart() {
      const that = this;
      that.chart = echarts.init(document.getElementById(`${that.className}`));
      that.chart.setOption({
        color: ['#3CB34F', '#1770DF', '#FAB63B', '#B2BEC3'],
        title: {
          text: that.title
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        grid: {
          left: '30',
          bottom: '20',
          top: '60',
          right: '10'
        },
        xAxis: {
          data: that.xAxisData,
          axisTick: {
            //y轴刻度线
            show: false
          },
          axisLabel: {
            color: '#2D3436'
          },
          axisLine: {
            //x轴
            lineStyle: {
              color: '#B2BEC3'
            }
          }
        },
        yAxis: {
          type: 'value',
          axisTick: {
            //y轴刻度线
            show: false
          },
          axisLine: {
            //y轴
            show: false
          },
          splitLine: {
            // y轴虚线
            show: true,
            lineStyle: {
              type: 'dashed',
              color: '#B2BEC3'
            }
          }
        },
        series: that.seriesData
        // [
        //   {
        //     name: "销量2",
        //     type: "line",
        //     smooth: true,
        //     // areaStyle: {},
        //     itemStyle: {
        //       normal: {
        //         color: "#FAB63B",
        //         lineStyle: {
        //           color: "#FAB63B"
        //         }
        //       }
        //     },
        //     areaStyle: {
        //       color: {
        //         type: "linear",
        //         x: 0,
        //         y: 0,
        //         x2: 0,
        //         y2: 1,
        //         colorStops: [
        //           {
        //             offset: 0,
        //             color: "#FAB63B" // 0% 处的颜色
        //           },
        //           {
        //             offset: 1,
        //             color: "#fff" // 100% 处的颜色
        //           }
        //         ],
        //         global: false // 缺省为 false
        //       }
        //     },
        //     data: [5, 20, 36, 10, 10, 20]
        //   }
        // ]
      });
      // 基于准备好的dom，初始化echarts实例  这个和上面的main对应
      // let myChart = echarts.init(document.getElementById("main3"));
      // // 指定图表的配置项和数据
      // let option = {
      //   color: ["#3CB34F", " #1770DF", "#FAB63B", "#B2BEC3"],
      //   title: {
      //     text: "第一个 ECharts 实例"
      //   },
      //   tooltip: {
      //     trigger: "axis",
      //     axisPointer: {
      //       type: "cross",
      //       label: {
      //         backgroundColor: "#6a7985"
      //       }
      //     }
      //   },
      //   grid: {
      //     left: "30",
      //     bottom: "60",
      //     top: "60",
      //     right: "10"
      //   },
      //   xAxis: {
      //     data: ["衬衫", "羊毛衫", "雪纺衫", "裤子", "高跟鞋", "袜子"],
      //     axisTick: {
      //       //y轴刻度线
      //       show: false
      //     },
      //     axisLabel: {
      //       textStyle: {
      //         color: "#2D3436"
      //       }
      //     },
      //     axisLine: {
      //       //x轴
      //       lineStyle: {
      //         color: "#B2BEC3"
      //       }
      //     }
      //   },
      //   yAxis: {
      //     type: "value",
      //     axisTick: {
      //       //y轴刻度线
      //       show: false
      //     },
      //     axisLine: {
      //       //y轴
      //       show: false
      //     },
      //     splitLine: {
      //       // y轴虚线
      //       show: true,
      //       lineStyle: {
      //         type: "dashed",
      //         color: "#B2BEC3"
      //       }
      //     }
      //   },
      //   series: [
      //     {
      //       name: "销量2",
      //       type: "line",
      //       smooth: true,
      //       // areaStyle: {},
      //       itemStyle: {
      //         normal: {
      //           color: "#FAB63B",
      //           lineStyle: {
      //             color: "#FAB63B"
      //           }
      //         }
      //       },
      //       areaStyle: {
      //         color: {
      //           type: "linear",
      //           x: 0,
      //           y: 0,
      //           x2: 0,
      //           y2: 1,
      //           colorStops: [
      //             {
      //               offset: 0,
      //               color: "#FAB63B" // 0% 处的颜色
      //             },
      //             {
      //               offset: 1,
      //               color: "#fff" // 100% 处的颜色
      //             }
      //           ],
      //           global: false // 缺省为 false
      //         }
      //       },
      //       data: [5, 20, 36, 10, 10, 20]
      //     }
      //   ]
      // };
      // // 使用刚指定的配置项和数据显示图表。
      // myChart.setOption(option);
    }
  }
};
</script>

<style lang="less" scoped>
.homeLine {
  background: #fff;
  border-radius: 4px;
  padding: 18px;
  margin-bottom: 20px;
  .chart {
    width: 704px;
    height: 320px;
  }
}
</style>
