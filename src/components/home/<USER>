<template>
  <div class="homePressureBar">
    <!-- 压力图 -->
    <div :id="className" class="chart"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
require('echarts/theme/macarons');
export default {
  name: 'homePressureBar',
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    title: {
      type: String,
      default: ''
    },
    dataset: {
      type: Array,
      default: []
    },
    encode: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      chart: null
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.drawChart();
    });
  },
  methods: {
    drawChart() {
      const that = this;
      that.chart = echarts.init(document.getElementById(`${that.className}`));
      that.chart.setOption({
        title: {
          text: that.title
          // left: "20"
        },
        dataset: that.dataset,
        grid: {
          left: '0',
          bottom: '100',
          top: '60',
          right: '0'
        },
        tooltip: {},
        xAxis: {
          // name: "product",
          type: 'category',
          axisLabel: {
            // interval: 0,
            // rotate: 30,
            formatter: function (value) {
              //x轴的文字改为竖版显示
              return value
                .replace('(', '')
                .replace(')', '')
                .split('')
                .join('\n');
            },
            color: '#2D3436'
          },
          axisTick: {
            //y轴刻度线
            show: false
          },
          axisLine: {
            //x轴
            lineStyle: {
              color: '#B2BEC3'
            }
          }
        },
        yAxis: {
          type: 'value',
          axisTick: {
            //y轴刻度线
            show: false
          },
          axisLine: {
            //y轴
            show: false
          },
          splitLine: {
            // y轴虚线
            show: false
          },
          axisLabel: {
            show: false
          }
        },
        visualMap: {
          orient: 'horizontal',
          inverse: true,
          top: 'top',
          left: 'right',
          min: 0,
          max: 29,
          text: ['大', '小'],
          dimension: 0,
          inRange: {
            color: ['#1770DF', '#3CB34F', '#FAB63B', '#D63031']
          }
        },
        series: [
          {
            type: 'bar',
            barWidth: 14,
            datasetIndex: 1,
            encode: that.encode,
            label: {
              show: true,
              position: 'top',
              valueAnimation: true
            }
          }
        ]
      });
    }
  }
};
</script>

<style lang="less" scoped>
.homePressureBar {
  background: #fff;
  border-radius: 4px;
  padding: 18px;
  margin-bottom: 20px;
  .chart {
    width: 704px;
    height: 320px;
  }
}
</style>
