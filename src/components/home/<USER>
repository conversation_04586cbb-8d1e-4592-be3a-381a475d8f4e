<template>
  <div class="homeProgress">
    <!-- 进度条组件 -->
    <div class="progress-box">
      <div class="progress-info">
        <div class="title">{{ title }}</div>
        <div class="number">{{ number }}<span>人</span></div>
      </div>
      <el-progress
        class="progress"
        type="circle"
        :percentage="percentage"
        :width="72"
        :color="color"
      ></el-progress>
    </div>
  </div>
</template>

<script>
export default {
  name: 'homeProgress',
  props: {
    color: {
      type: String,
      default: '#FAB63B'
    },
    title: {
      type: String,
      default: ''
    },
    number: {
      type: Number,
      default: 20
    },
    percentage: {
      type: Number,
      default: 25
    }
  },
  data() {
    return {};
  },
  methods: {}
};
</script>

<style lang="less" scoped>
.homeProgress {
  display: inline-block;
  margin-bottom: 20px;
  .progress-box {
    width: 360px;
    height: 120px;
    display: flex;
    align-items: flex-end;
    padding: 18px;
    color: #2d3436;
    font-family: PingFangSC-Regular;
    background: #fff;
    border-radius: 4px;
  }
  .progress-info {
    width: 233px;
  }
  .title {
    opacity: 0.6;
    font-size: 14px;
    margin-bottom: 10px;
  }
  .number {
    font-size: 42px;
    span {
      font-size: 14px;
      margin-left: 10px;
    }
  }
  // .progress {
  //   margin-left: 140px;
  // }
}
</style>
