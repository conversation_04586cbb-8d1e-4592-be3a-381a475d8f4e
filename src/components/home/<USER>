<template>
  <div class="homeBar">
    <!-- 柱状图 -->
    <div :id="className" class="chart"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
require('echarts/theme/macarons');
export default {
  name: 'homeBar',
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    title: {
      type: String,
      default: ''
    },
    legendData: {
      type: Array,
      default: []
    },
    xAxisData: {
      type: Array,
      default: []
    },
    seriesData: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      chart: null
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.drawChart();
    });
  },
  methods: {
    drawChart() {
      const that = this;
      that.chart = echarts.init(document.getElementById(`${that.className}`));
      that.chart.setOption({
        color: ['#3CB34F', '#1770DF', '#FAB63B', '#B2BEC3'],
        title: {
          text: that.title
          // left: "20"
        },
        legend: {
          data: that.legendData,
          right: '10'
        },
        tooltip: {},
        grid: {
          left: '30',
          bottom: '20',
          top: '60',
          right: '10'
        },
        xAxis: {
          data: that.xAxisData,
          axisTick: {
            //y轴刻度线
            show: false
          },
          axisLabel: {
            color: '#2D3436'
          },
          axisLine: {
            //x轴
            lineStyle: {
              color: '#B2BEC3'
            }
          }
        },
        yAxis: {
          type: 'value',
          axisTick: {
            //y轴刻度线
            show: false
          },
          axisLine: {
            //y轴
            show: false
          },
          splitLine: {
            // y轴虚线
            show: true,
            lineStyle: {
              type: 'dashed',
              color: '#B2BEC3'
            }
          }
        },
        series: that.seriesData
      });
      // 基于准备好的dom，初始化echarts实例  这个和上面的main对应
      // let myChart = echarts.init(document.getElementById("main"));
      // 指定图表的配置项和数据
      // let option = {
      //   color: ["#3CB34F", " #1770DF", "#FAB63B", "#B2BEC3"],
      //   title: {
      //     text: "预约人数"
      //     // left: "20"
      //   },
      //   legend: {
      //     data: ["本周人数", "上周人数"],
      //     right: "10"
      //   },
      //   tooltip: {},
      //   grid: {
      //     left: "30",
      //     bottom: "60",
      //     top: "60",
      //     right: "10"
      //   },
      //   xAxis: {
      //     data: ["2.10", "5.11", "5.11", "5.13", "5.14", "5.15"],
      //     axisTick: {
      //       //y轴刻度线
      //       show: false
      //     },
      //     axisLabel: {
      //       textStyle: {
      //         color: "#2D3436"
      //       }
      //     },
      //     axisLine: {
      //       //x轴
      //       lineStyle: {
      //         color: "#B2BEC3"
      //       }
      //     }
      //   },
      //   yAxis: {
      //     type: "value",
      //     axisTick: {
      //       //y轴刻度线
      //       show: false
      //     },
      //     axisLine: {
      //       //y轴
      //       show: false
      //     },
      //     splitLine: {
      //       // y轴虚线
      //       show: true,
      //       lineStyle: {
      //         type: "dashed",
      //         color: "#B2BEC3"
      //       }
      //     }
      //   },
      //   series: [
      //     {
      //       name: "本周人数",
      //       type: "bar",
      //       barGap: 0,
      //       barWidth: 14,
      //       data: [5, 20, 36, 10, 10, 20]
      //     },
      //     {
      //       name: "上周人数",
      //       type: "bar",
      //       barWidth: 14,
      //       data: [15, 23, 56, 30, 13, 24]
      //     }
      //   ]
      // };
      // 使用刚指定的配置项和数据显示图表。
      // myChart.setOption(option);
    }
  }
};
</script>

<style lang="less" scoped>
.homeBar {
  background: #fff;
  border-radius: 4px;
  padding: 18px;
  margin-bottom: 20px;
  .chart {
    width: 704px;
    height: 320px;
  }
}
</style>
