<!--
 * @FilePath: \shenshan\KrPeis\src\components\printSelection.vue
 * @Description: 打印组件
 * @Version: 2.0
 * @Author: key
 * @Date: 2024-07-09 10:03:42
 * @LastEditors: key
 * @LastEditTime: 2024-08-29 15:15:30
-->
<template>
  <div>
    <el-dialog
      title="打印"
      class="print-dialog"
      width="500px"
      :close-on-click-modal="false"
      :show-close="false"
      :close-on-press-escape="false"
      :before-close="
        () => {
          $emit('update:displaySwitches', false);
        }
      "
      :visible.sync="displaySwitches"
    >
      <div
        class="main"
        v-loading="printParameters.progressData.loading"
        :element-loading-text="`正在打印中：${printParameters.progressData.progress}`"
      >
        <div class="print">
          <div class="print-type">
            <p>打印机</p>
            <el-select v-model="printParameters.printer" placeholder="请选择">
              <el-option
                v-for="item in G_printerList"
                :key="item"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
          </div>
          <div>
            <p>条码打印机</p>
            <el-select
              v-model="printParameters.barcodePrinters"
              placeholder="请选择"
            >
              <el-option
                v-for="item in G_printerList"
                :key="item"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="selectAll">
          <el-checkbox
            :indeterminate="isIndeterminate"
            v-model="checkAll"
            @change="handleCheckAllChange"
            >全选</el-checkbox
          >
        </div>
        <div class="check-box">
          <el-checkbox-group
            size="small"
            v-model="printParameters.selectedPrint"
            v-for="item in printParameters.printType"
            :key="item.type"
          >
            <el-checkbox :label="item.type" :disabled="item.disabled">{{
              item.typeName
            }}</el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="printCancel">{{
          printParameters.printStatus ? '取消打印' : '取消'
        }}</el-button>
        <el-button
          type="primary"
          @click="print(false, callback)"
          v-loading="this.printParameters.loading"
          :disabled="printParameters.printStatus"
          >确定打印</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import { usePrint } from "./printHooks";
import newPrintMixins from './newPrintMixins';

// const {
//   printParameters,
//   printObj,
//   print,
//   setPrinterData,
//   breakTheLink,
//   setThePrintType,
//   getAPrintableList,
//   setPrintingTypeManyPeople,
//   connectPrint,
//   setPrinterType,
//   updateProgress,
//   retrieveFileStream,
//   startPrinting,
//   defaultSelectionOfPrintingType,
//   setPrintTypeList
// } = usePrint();

export default {
  mixins: [newPrintMixins],
  props: {
    // //控制显示
    // displaySwitches: {
    //   type: Boolean,
    //   default: false,
    // },
    //是否勾选所有可打印类型
    defaultCheck: {
      type: Boolean,
      default: false
    },
    //打印回调
    callback: {
      type: Function
    }
  },
  data() {
    return {
      checkAll: false,
      isIndeterminate: false
    };
  },
  methods: {
    // print,
    // setPrinterData,
    // breakTheLink,
    // setThePrintType,
    // getAPrintableList,
    // setPrintingTypeManyPeople,
    // connectPrint,
    // setPrinterType,
    // updateProgress,
    // retrieveFileStream,
    // startPrinting,
    // defaultSelectionOfPrintingType,
    // setPrintTypeList,
    printCancel() {
      if (this.printParameters.printStatus) {
        this.printParameters.cancelPrint = true;
        this.printParameters.printStatus = false;
        this.printParameters.progressData.loading = false;
        console.log('取消打印');
      } else {
        this.$emit('update:displaySwitches', false);
      }
    },
    handleCheckAllChange(val) {
      let globalFilterList = this.globalFilterList.filter((item) =>
        this.printParameters.printType.some(
          (i) => i.type === item && !i.disabled
        )
      );
      this.printParameters.selectedPrint = val ? globalFilterList : [];
      this.isIndeterminate = false;
    }
  },
  created() {
    this.ws = {};
  },

  destroyed() {
    // this.breakTheLink();
  },
  watch: {
    displaySwitches: {
      handler(newVal) {
        if (!newVal) {
          this.printParameters.selectedPrint = [];
          this.printParameters.printStatus = false;
          this.printParameters.progressData.loading = false;
        } else {
          this.setPrinterData();
        }
      }
    },
    'printParameters.selectedPrint': {
      handler(newVal) {
        let globalFilterList = this.globalFilterList.filter((item) =>
          this.printParameters.printType.some(
            (i) => i.type === item && !i.disabled
          )
        );
        let checkedCount = newVal.length;
        this.checkAll = checkedCount === globalFilterList.length;
        this.isIndeterminate =
          checkedCount > 0 && checkedCount < globalFilterList.length;
      }
    }
  }
};
</script>
<style lang="less" scoped>
/deep/ .el-dialog {
  height: none !important;
  height: auto !important;
  padding: 0 !important;
}
.main {
  display: flex;
  flex-direction: column;
  //   gap: 15px;
  .print {
    display: flex;
    justify-content: space-between;
    .print-type {
      margin-bottom: 10px !important;
    }
  }
  .selectAll {
    margin: 10px 0;
  }
  .check-box {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    & > div {
      width: 25%;
      margin: 10px 0;
    }
  }
}
</style>
