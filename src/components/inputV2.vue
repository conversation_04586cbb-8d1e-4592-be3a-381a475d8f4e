<template>
  <div class="input_wrap">
    <label
      :class="{
        abnormal_result: item.abnormalType > 0,
        active_label: activeLabelIdx === idx
      }"
      v-for="(item, idx) in labelList"
      :key="item.id"
    >
      <el-popover
        placement="right"
        title=""
        width="200"
        trigger="hover"
        popper-class="spanInput_popover"
        @hide="onPopoverHide"
      >
        <ul class="input_popover_ul">
          <li @click="abnormal(idx, item)">
            {{ item.abnormalType === 0 ? '异常' : '取消异常' }}
          </li>
          <li @click="labelDel(idx, item)">删除结果</li>
          <li @click="createSummary(item)">生成小结</li>
        </ul>
        <span
          slot="reference"
          contenteditable="true"
          :ref="'inputSpan_Ref_' + itemCode + '_' + idx"
          @keydown.left.stop="labelLeft($event, idx)"
          @keydown.right.stop="labelRight($event, idx)"
          @keydown="labelKeydown"
          @keyup="labelKeyup($event, idx, item)"
          @keypress.enter.prevent="inputSpanEnter(idx)"
          @focus="labelFocus(item, idx)"
          @blur="resultSpanBlur(item, idx)"
          @input="resultSpanInput"
          @mousedown.stop
          >{{ item.tag }}</span
        >
      </el-popover>
    </label>
    <div
      class="input_div"
      contenteditable="true"
      ref="input_Ref"
      v-html="inputVal"
      @keydown.tab.stop.prevent="inputComTab"
      @keydown.delete.stop="inputComDel"
      @keyup="inputKeyup"
      @keydown="inputKeydown"
      @input="inputChange"
      @blur="inputBlur"
      @focus="inputFocus"
    ></div>
    <!-- @keyup="inputKeyup"  -->
  </div>
</template>

<script>
import { dataUtils } from '../common';

export default {
  props: {
    labelList: {
      type: Array,
      default: () => {
        return [
          {
            id: 1,
            label: '测试标签'
          }
        ];
      }
    },
    itemCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      inputVal: '',
      cursorNum: 1,
      activeLabelIdx: null
    };
  },
  methods: {
    wrapClick() {
      this.$refs.input_Ref.focus();
    },
    inputHandle() {
      this.$refs.input_Ref.blur();
    },
    inputChange(e) {
      // console.log(this.$refs.input_Ref);
      this.$emit('inputChange');
    },
    inputFocus() {
      this.activeLabelIdx = null;
    },
    inputBlur() {
      let inputText = this.$refs.input_Ref.innerText;
      this.$emit('inputBlur', inputText);
      this.$refs.input_Ref.innerHTML = '';
    },
    testLeft(e) {
      if (this.labelList.length === 0) return;
      let labels =
        this.$refs[
          'inputSpan_Ref_' + this.itemCode + '_' + (this.labelList.length - 1)
        ][0];
      let cursorIdx = dataUtils.getCaretPosition(this.$refs.input_Ref);
      console.log(labels.textContent.length);
      if (cursorIdx === 0) {
        labels.focus();
        dataUtils.setCursorPosition(labels, labels.textContent.length);
      }
    },
    // 输入框的tab回调
    inputComTab() {
      this.$emit('inputComTab');
    },
    // 输入框的删除回调
    inputComDel() {
      let cursorIdx = dataUtils.getCaretPosition(this.$refs.input_Ref);
      if (cursorIdx == 0) {
        let tag = this.labelList[this.labelList.length - 1];
        if (tag) {
          this.$emit('labelDel', tag);
        }
        this.labelList.splice(-1, 1);
      }
    },
    // 输入框键盘按下的回调
    inputKeydown(e) {
      let inputText = this.$refs.input_Ref.innerText;
      switch (true) {
        case e.keyCode == 13:
          e.preventDefault();
          // if(inputText.trim()==='')return;
          this.$emit('inputComEnter', inputText);
          // this.labelList.push({
          //     id:this.labelList.length+1,
          //     label:inputText
          // });
          this.$refs.input_Ref.innerHTML = '';
          break;
        case e.keyCode == 37:
          e.stopPropagation();
        default:
          break;
      }
      this.$nextTick(() => {
        this.$emit('inputChange');
      });
    },
    // 输入框的键盘弹起事件的回调
    inputKeyup(e) {
      if (this.labelList.length === 0) return;
      let labels =
        this.$refs[
          'inputSpan_Ref_' + this.itemCode + '_' + (this.labelList.length - 1)
        ][0];
      let cursorIdx = dataUtils.getCaretPosition(this.$refs.input_Ref);
      let input_RefTextLen = this.$refs.input_Ref.textContent.length;
      let inputText = this.$refs.input_Ref.innerText;
      if (input_RefTextLen !== 0) {
        this.cursorNum = 0;
      }
      switch (true) {
        case e.ctrlKey && e.keyCode === 37:
          e.stopPropagation();
          e.preventDefault();

          labels.focus();
          dataUtils.setCursorPosition(labels, labels.textContent.length);
          let idx = this.labelList.length - 2;
          if (input_RefTextLen === 0) {
            idx = this.labelList.length - 1;
          }
          this.activeLabelIdx = idx < 0 ? 0 : idx;
          this.cursorNum = 0;
          return;
        case e.keyCode == 37:
          e.stopPropagation();
          e.preventDefault();
          if (this.cursorNum == 1 || input_RefTextLen === 0) {
            labels.focus();
            dataUtils.setCursorPosition(labels, labels.textContent.length);
            let idx = this.labelList.length - 2;
            if (input_RefTextLen === 0) {
              idx = this.labelList.length - 1;
            }
            this.activeLabelIdx = idx < 0 ? 0 : idx;
            this.cursorNum = 0;
            return;
          }
          if (cursorIdx === 0) {
            this.cursorNum = 1;
          }
          break;

        default:
          break;
      }
    },
    // 删除小标签
    labelDel(idx, tag) {
      return new Promise((resolve, reject) => {
        this.labelList.splice(idx, 1);
        this.$emit('labelDel', tag);
        this.$emit('inputChange');
        resolve();
      });
    },
    // 生成小结
    createSummary(tag) {
      this.$emit('createSummary', tag);
    },
    // 异常
    abnormal(idx, tag) {
      console.log(idx, tag);
      if (tag.abnormalType === 0) {
        tag.abnormalType = 1;
      } else {
        tag.abnormalType = 0;
      }
      this.$emit('abnormal', tag);
    },
    // 编辑结果
    resultSpanBlur(tag, idx) {
      tag.tag =
        this.$refs['inputSpan_Ref_' + this.itemCode + '_' + idx][0].innerText;
      if (tag.tag.trim() === '') {
        this.labelList.splice(idx, 1);
        this.$emit('labelDel', tag);
        return;
      }
      this.$emit('resultSpanBlur', tag);
    },
    // 标签的聚焦回调
    labelFocus(tag, idx) {
      setTimeout(() => {
        this.$parent.$parent.$parent.clearRowResultFocus().then((r) => {
          this.activeLabelIdx = idx;
          this.cursorNum = 0;
        });
      }, 100);
    },
    // 编辑结果的实时输入回调
    resultSpanInput() {
      this.$emit('inputChange');
    },
    // 编辑结果的回车回调
    inputSpanEnter(idx) {
      this.$refs['inputSpan_Ref_' + this.itemCode + '_' + idx][0].blur();
      this.$nextTick(() => {
        this.$refs.input_Ref.focus();
        this.cursorNum = 0;
        this.activeLabelIdx = null;
      });
    },
    // 标签输入框的键盘按下事件回调
    labelKeydown(e) {
      switch (true) {
        case e.ctrlKey && e.keyCode == 8:
          e.stopPropagation();
          e.preventDefault();
          return;
        case e.ctrlKey && e.keyCode === 37:
          e.stopPropagation();
          e.preventDefault();
          return;
        case e.ctrlKey && e.keyCode === 39:
          e.stopPropagation();
          e.preventDefault();
          return;
        case e.keyCode == 37:
          e.stopPropagation();
          break;
        default:
          break;
      }
    },
    // 标签输入框的键盘弹起事件的回调
    async labelKeyup(e, idx, tag) {
      console.log(e);
      let currentLabel =
        this.$refs['inputSpan_Ref_' + this.itemCode + '_' + idx][0];
      let cursorIdx = await dataUtils.getCaretPosition(currentLabel);
      console.log(currentLabel, cursorIdx, this.cursorNum);
      switch (true) {
        case e.ctrlKey && e.keyCode === 37:
          e.stopPropagation();
          e.preventDefault();
          console.log(123456);
          if (idx === 0) return;
          this.labelCtrlAndLeftRightKey(idx - 1);
          return;
        case e.ctrlKey && e.keyCode === 39:
          e.stopPropagation();
          e.preventDefault();
          if (idx === this.labelList.length - 1) return;
          this.labelCtrlAndLeftRightKey(idx + 1);
          return;
        case e.ctrlKey && e.keyCode == 8:
          e.stopPropagation();
          e.preventDefault();
          await this.$refs[
            'inputSpan_Ref_' + this.itemCode + '_' + idx
          ][0].blur();
          setTimeout(async () => {
            this.labelDel(idx, tag).then((r) => {
              if (this.labelList.length === 0) {
                this.$refs.input_Ref.focus();
                this.cursorNum = 0;
                return;
              }
              console.log(idx);
              if (idx === 0) {
                this.$nextTick(() => {
                  let nextLabel =
                    this.$refs['inputSpan_Ref_' + this.itemCode + '_' + idx][0];
                  nextLabel.focus();
                  dataUtils.setCursorPosition(
                    nextLabel,
                    nextLabel.textContent.length
                  );
                  this.cursorNum = 0;
                  this.activeLabelIdx = idx;
                });
              } else {
                this.$nextTick(() => {
                  let nextLabel =
                    this.$refs[
                      'inputSpan_Ref_' + this.itemCode + '_' + (idx - 1)
                    ][0];
                  nextLabel.focus();
                  dataUtils.setCursorPosition(
                    nextLabel,
                    nextLabel.textContent.length
                  );
                  this.cursorNum = 0;
                  this.activeLabelIdx = idx - 1;
                });
              }
            });
          }, 50);

          break;
        default:
          break;
      }
    },
    // 标签的左键回调
    async labelLeft(e, idx) {
      console.log(e, idx);
      if (e.ctrlKey) return;
      let currentLabel =
        this.$refs['inputSpan_Ref_' + this.itemCode + '_' + idx][0];
      let cursorIdx = await dataUtils.getCaretPosition(currentLabel);
      if (idx === 0) return;
      if (cursorIdx == 0) {
        this.labelLeftAndRightFun(e, currentLabel, idx - 1, cursorIdx === 0);
      }
    },
    // 标签的右键回调
    async labelRight(e, idx) {
      if (e.ctrlKey) return;
      let currentLabel =
        this.$refs['inputSpan_Ref_' + this.itemCode + '_' + idx][0];
      let cursorIdx = await dataUtils.getCaretPosition(currentLabel);
      if (
        idx === this.labelList.length - 1 &&
        currentLabel.textContent.length === cursorIdx
      ) {
        this.$refs.input_Ref.focus();
        this.cursorNum = 0;
        this.activeLabelIdx = null;
        return;
      }

      if (cursorIdx == currentLabel.textContent.length) {
        this.labelLeftAndRightFun(e, currentLabel, idx + 1);
      }
    },
    // 标签的ctrl + 左键
    labelCtrlLeft(idx) {
      console.log('ctrl+left');
      if (idx === this.labelList.length - 1) return;
      this.labelCtrlAndLeftRightKey(idx + 1);
    },
    // 标签的左右键通用函数
    labelLeftAndRightFun(e, currentLabel, nextNum, condition) {
      let nextLabel =
        this.$refs['inputSpan_Ref_' + this.itemCode + '_' + nextNum][0];
      let len = nextLabel.textContent.length;
      setTimeout(() => {
        dataUtils.setCursorPosition(nextLabel, e.keyCode == 37 ? len : 0);
      }, 10);
    },
    // 标签的ctrl + 左右键的通用函数
    labelCtrlAndLeftRightKey(nextNum) {
      let nextLabel =
        this.$refs['inputSpan_Ref_' + this.itemCode + '_' + nextNum][0];
      nextLabel.focus();
      dataUtils.setCursorPosition(nextLabel, nextLabel.textContent.length);
      this.activeLabelIdx = nextNum;
      this.cursorNum = 0;
    },
    // 操作菜单隐藏的回调
    labelDropVisibleChange(flag) {
      console.log(flag);
      if (!flag) {
        this.$nextTick(() => {
          let dom = document.querySelector('.labelDrop_menu');
          console.log(dom);
          dom.remove();
        });
      }
    },
    // 销毁前的回调
    onPopoverHide() {
      let node = document.querySelector('body>.spanInput_popover'); //隐藏时删除
      node?.remove();
    }
  }
};
</script>

<style lang="less" scoped>
.input_wrap {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  // border: 2px solid #1abc9c;
  // padding: 2px;
  label {
    padding: 2px 3px;
    background: #ecf5ff;
    border: 1px solid #d9ecff;
    color: #409eff;
    display: inline-block;
    margin-bottom: 3px;
    margin-right: 3px;
    word-break: break-all;
    position: relative;
    min-height: 25px;
    line-height: 21px;
    vertical-align: middle;
    border-radius: 4px;
    // padding-right: 20px;
    // i{
    //     position: absolute;
    //     top:-5px;
    //     right: -5px;
    //     color: #f56c6c;
    //     cursor: pointer;
    //     display: none;
    //     &:last-child{
    //         top: 15px;
    //     }
    // }
    // &:hover i{
    //     display: block;
    // }
    span.el-popover__reference {
      display: inline-block;
      word-break: break-all;
      padding-right: 8px;
      &:focus-visible {
        outline: none;
      }
    }
    .label_dropdown {
      position: absolute;
      right: 0;
      top: 0;
      width: 20px;
      font-size: 14px;
      cursor: pointer;
      height: 100%;
      color: #bbb;
      span {
        height: 100%;
        padding-top: 3px;
        display: block;
        text-align: center;
        width: 100%;
      }
    }
  }
  .abnormal_result {
    // background: #d54343 !important;
    color: #d54343 !important;
  }
  .input_div {
    display: inline-block;
    min-width: 50px;
    padding: 2px;
    word-break: break-all;
    flex: 1;
    flex-shrink: 0;
    &:focus-visible {
      outline: none;
    }
  }
  .active_label {
    border-color: #1770df;
    background: #fff;
    color: #000;
    font-weight: normal;
  }
}
</style>
<style scoped lang="less">
.labelDrop_menu {
  span {
    display: block;
  }
}
.input_popover_ul {
  li {
    line-height: 30px;
    cursor: pointer;
    &:hover {
      background-color: #ecf5ff;
      color: #66b1ff;
    }
  }
}
</style>
