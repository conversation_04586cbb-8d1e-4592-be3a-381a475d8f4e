<template>
  <div class="">
    <el-select
      v-model="value"
      filterable
      remote
      reserve-keyword
      size="mini"
      placeholder="请输入关键词"
      :remote-method="remoteMethod"
      :loading="loading"
      popper-class="disease-search"
      value-key="diseaseCode"
      popper-append-to-body
      @change="handleSelectChange"
      :clearable="clearable"
      :disabled="disabled"
      v-el-select-loadmore="getMoreList"
      @keyup.down.native="getMoreList"
    >
      <el-option
        v-for="item in options"
        :key="item.diseaseCode"
        :label="item.label"
        :value="item.diseaseCode"
      >
        <div class="disease">
          <div class="disease-name">{{ item.diseaseName }}</div>
          <div v-show="isDiseaseEntries">
            <el-tooltip
              class="item"
              effect="light"
              placement="left"
              v-if="item.diseaseEntries.length > 0"
            >
              <template slot="content">
                <div style="max-width: 500px">
                  <el-tag
                    style="margin: 3px"
                    v-for="(tag, index) in item.diseaseEntries"
                    :key="index"
                    type="success"
                    >{{ tag }}</el-tag
                  >
                </div>
              </template>
              <div class="disease-entries">
                <el-tag
                  style="margin: 5px"
                  v-for="(tag, index) in item.diseaseEntries"
                  :key="index"
                  type="success"
                  >{{ tag }}</el-tag
                >
              </div>
            </el-tooltip>
            <div class="disease-entries" v-else>
              <span>-</span>
            </div>
          </div>
          <el-tooltip
            v-show="isSugContent"
            class="item"
            effect="dark"
            placement="left"
          >
            <template slot="content">
              <p style="max-width: 500px">{{ item.suggestContent }}</p>
            </template>
            <div class="sug-content">{{ item.suggestContent }}</div>
          </el-tooltip>
        </div>
      </el-option>
    </el-select>
  </div>
</template>

<script>
export default {
  name: 'diseaseSearch',
  components: {},
  props: {
    //是否显示疾病词条
    isDiseaseEntries: {
      type: Boolean,
      default: true
    },
    //是否显示疾病详情
    isSugContent: {
      type: Boolean,
      default: true
    },
    //是否清空
    clearable: {
      type: Boolean,
      default: false
    },
    //是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    //是否开启自定义远程搜索
    enableCustomRemoteSearch: {
      type: Boolean,
      default: false
    },
    //自定义远程搜索回调函数
    customRemoteSearchCallback: {
      type: Function,
      default: null
    },
    //选中值改变回调
    change: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      value: '',
      options: [],
      diseaseList: [],
      loading: false,
      pageData: {
        pageNumber: 1,
        pageSize: 20
      }
    };
  },
  directives: {
    /** 下拉框分页加载 */
    'el-select-loadmore': {
      bind(el, binding) {
        const SELECTWRAP_DOM = el.querySelector(
          '.el-select-dropdown .el-select-dropdown__wrap'
        );
        SELECTWRAP_DOM.addEventListener('scroll', function () {
          const condition =
            this.scrollHeight - this.scrollTop <= this.clientHeight;
          if (condition) {
            binding.value();
          }
        });
      }
    }
  },
  watch: {},
  computed: {},
  methods: {
    //清空表单
    clearForm() {
      this.pageData = {
        pageNumber: 1,
        pageSize: 20
      };
      this.options = [];
      this.diseaseList = [];
    },
    //远程搜索回调函数
    remoteMethod(query) {
      if (this.enableCustomRemoteSearch) {
        this.$emit('customRemoteSearchCallback', query);
        return;
      }

      this.clearForm();
      this.loading = true;
      this.$ajax
        .post(this.$apiUrls.GetDiseaseInfo, '', { query: { keyword: query } })
        .then((res) => {
          let { success, returnData } = res.data;
          if (!success) return;

          this.diseaseList = returnData.map((item) => {
            return {
              label: item.diseaseName,
              ...item
            };
          });
          this.options = this.diseaseList.slice(0, 20);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //疾病词条说明
    diseaseEntriesText(data) {
      let text = '';
      data.forEach((item) => {
        text += item + '、';
      });
      return text.substring(0, text.length - 1);
    },
    getMoreList() {
      if (
        this.pageData.pageNumber >=
        Math.ceil(this.diseaseList.length / this.pageData.pageSize)
      ) {
        return;
      }
      this.pageData.pageNumber++;
      let startIndex = (this.pageData.pageNumber - 1) * this.pageData.pageSize;
      let endIndex = this.pageData.pageNumber * this.pageData.pageSize;
      this.options.push(...this.diseaseList.slice(startIndex, endIndex));
    },
    //选中值改变回调
    handleSelectChange(data) {
      let returnValue = this.diseaseList.find(
        (item) => item.diseaseCode === data
      );
      this.$emit('change', returnValue);
    }
  },
  created() {},
  mounted() {}
};
</script>
<style lang="less" scoped>
.el-select-dropdown__list > li {
  height: unset;
}
.disease {
  border-bottom: 1px solid #95959536;
  & > div {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .disease-name {
    font-size: 16px;
    font-weight: 600;
  }
  .disease-entries,
  .sug-content {
    font-size: 14px;
  }
}
</style>
<style lang="less">
.disease-search {
  max-width: 500px;
  max-height: 550px;
  .el-select-dropdown__wrap {
    max-height: 550px;
  }
}
</style>
