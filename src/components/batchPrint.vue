<template>
  <div class="my_mask">
    <el-progress type="circle" :percentage="percentage"></el-progress>
    <p class="tips_p">{{ message }}</p>
  </div>
</template>

<script>
export default {
  name: 'batchPrint',
  data() {
    return {
      percentage: 0,
      message: '0/0',
      time: 300,
      fileStream: '',
      totalPages: 0,
      currentNum: 0
    };
  },
  methods: {
    batchPrint(list) {
      this.message = `${this.currentNum}/${list.length}`;
      this.percentage = Math.floor(
        (this.currentNum / list.length).toFixed(2) * 100
      );
      console.log(this.currentNum);
      if (this.currentNum >= list.length) {
        setTimeout(() => {
          this.$message({
            message: '打印完成',
            type: 'success',
            showClose: true
          });
          this.$parent.batchPrintShow = false;
        }, this.time * this.currentNum);
        return;
      }
      this.getPDF(list[this.currentNum]).then((r) => {
        setTimeout(() => {
          this.currentNum += 1;
          this.batchPrint(list);
        }, this.time * this.currentNum);
      });
    },
    getPDF(dataInfo) {
      console.log(dataInfo);
      return new Promise((resolve, reject) => {
        let datas = {
          reportCode: dataInfo.reportType || dataInfo.guidanceType,
          queryString: 'regNo=' + dataInfo.regNo
        };
        console.log(datas);
        this.$ajax
          .post(`${this.$config.pdfFileUrl}Home/ExportToPdf`, datas, {
            responseType: 'arraybuffer'
          })
          .then((r) => {
            // let blob = new Blob([r.data],{
            //     type:'application/pdf'
            // })
            // console.log(blob);
            // this.pdfSrc = URL.createObjectURL(blob);
            const blob = new Blob([r.data], { type: 'application/pdf' });
            let reader = new FileReader();

            reader.onload = (e) => {
              console.log(e);
              let data;
              if (typeof e.target.result === 'object') {
                data = window.URL.createObjectURL(e.target.result);
              } else {
                data = e.target.result;
              }
              this.pdfSrc = data;
              this.fileStream = data;
              this.print();
            };
            //转化为base64
            reader.readAsDataURL(blob);

            let file = new File([r.data], '', {
              type: 'application/pdf'
            });
            let fileReader = new FileReader();
            fileReader.readAsBinaryString(file);
            fileReader.onloadend = (e) => {
              console.log(fileReader);
              var count = fileReader.result.match(
                /\/Type[\s]*\/Page[^s]/g
              ).length;
              console.log('Number of Pages:', count);
              this.totalPages = count;
              resolve(this.totalPages);
            };
          });
      });
    },
    print() {
      console.log(this.fileStream);
      let fileStream = this.fileStream.split(',')[1];
      // return
      // console.log(this.fileStream);
      let obj2 = {
        PrinterName: this.$parent.printSetInfo.PrinterName,
        PrintTimes: this.$parent.printSetInfo.PrintTimes,
        FileBase64: fileStream,
        FileFormat: 'pdf'
      };
      let obj = {
        Action: 'print',
        Data: JSON.stringify(obj2)
      };
      var data = JSON.stringify(obj);
      console.log(data);
      // let T = "{'Action':'getPrinterList','Data':''}";
      this.$parent.ws.sendSock(data);
      this.isPrint = true;
    }
  }
};
</script>

<style lang="less" scoped>
.my_mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  /deep/.el-progress__text {
    color: #fff;
  }
  .tips_p {
    color: #fff;
    font-size: 16px;
    margin-top: 10px;
  }
}
</style>
