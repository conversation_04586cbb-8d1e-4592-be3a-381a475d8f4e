<template>
  <div class="box">
    <div class="top" v-if="showToolbar">
      <span class="text">Theme:</span>
      <el-select
        v-model="defaultOptions.theme"
        placeholder="请选择编辑器主题"
        class="select"
        @change="changeTheme"
      >
        <el-option
          v-for="(theme, index) in themes"
          :key="index"
          :label="theme.label"
          :value="theme.value"
        ></el-option>
      </el-select>
      <span class="text">Language:</span>
      <el-select
        v-model="defaultOptions.language"
        placeholder="请选择格式化语言"
        class="select"
      >
        <el-option
          v-for="(item, index) in languageList"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
      <el-button type="primary" plain class="btn" @click="formatCode"
        >格式化</el-button
      >
      <el-button type="primary" plain class="btn" @click="clearSelection"
        >清空</el-button
      >
    </div>
    <div
      :style="{ height, width }"
      :id="`monacoEditorContainer${index}`"
      class="container"
    ></div>
  </div>
</template>

<script>
import * as monaco from 'monaco-editor';
// 代码高亮（将所有支持的语言全部显示）
import 'monaco-editor/esm/vs/basic-languages/monaco.contribution';
import prettyData from 'pretty-data/pretty-data';

export default {
  name: 'MonacoEditor',
  props: {
    showToolbar: {
      //顶部按钮是否显示
      type: Boolean,
      default: true
    },
    options: {
      type: Object,
      default: () => {}
    },
    code: {
      type: String
    },
    height: {
      type: String,
      default: '100%'
    },
    width: {
      type: String,
      default: '100%'
    },
    index: {
      type: String,
      default: '01'
    }
  },
  data() {
    return {
      defaultOptions: {
        value: '', // 编辑器的值
        language: 'javascript', //语言
        folding: true, // 是否折叠
        theme: 'vs-dark', // 编辑器主题：vs, hc-black, or vs-dark
        autoIndent: true, // 自动缩进
        automaticLayout: true, // 自动布局
        wordWrap: 'on', // 启用自动换行
        readOnly: false, // 是否只读
        fontSize: 14, // 字体大小
        tabSize: 2, // 制表符大小
        scrollBeyondLastLine: false, // 是否允许滚动超过最后一行
        minimap: {
          enabled: true // 是否显示缩略图
        }
      },
      languageList: [
        { value: 'javascript', label: 'JSON' },
        { value: 'xml', label: 'XML' },
        { value: 'sql', label: 'SQL' }
      ],
      themes: [
        { value: 'vs', label: 'vs' },
        { value: 'vs-dark', label: 'vs-dark' },
        { value: 'hc-black', label: 'hc-black' }
      ],
      monacoEditor: null
    };
  },
  mounted() {
    this.createMonacoEditor();
  },
  watch: {
    options: {
      handler() {
        this.$nextTick(() => {
          this.monacoEditor.updateOptions(this.assignOptions);
        });
      },
      deep: true
    },
    code: {
      handler(newVal, oldVal) {
        this.$nextTick(() => {
          this.monacoEditor.setValue(newVal);
          this.formatCode();
        });
      },
      immediate: true
    }
  },
  computed: {
    assignOptions() {
      const options = Object.assign(this.defaultOptions, this.options);
      if (options.language.toUpperCase() === 'JSON') {
        options.language = 'javascript';
      }
      return options;
    }
  },
  methods: {
    formatCode() {
      try {
        const code = this.monacoEditor.getValue();
        if (!code || !this.defaultOptions.language) {
          //  值为空或者语言为空，不执行格式化操作
          return;
        }

        let formattedCode;
        switch (this.defaultOptions.language) {
          case 'javascript':
            formattedCode = prettyData.pd.json(code);
            break;
          case 'xml':
            formattedCode = prettyData.pd.xml(code);
            break;
          case 'sql':
            formattedCode = prettyData.pd.sql(code);
          default:
            formattedCode = this.code;
            return;
        }

        if (this.assignOptions.readOnly) {
          this.monacoEditor.setValue(formattedCode);
        } else {
          // 设置格式化后的代码
          monaco.editor.setModelLanguage(
            this.monacoEditor.getModel(),
            this.defaultOptions.language
          );
          const model = this.monacoEditor.getModel();
          const formattedContent = {
            range: model.getFullModelRange(),
            text: formattedCode
          };

          this.monacoEditor.executeEdits('format', [formattedContent]);
        }
      } catch (err) {
        this.$message.error(`格式化代码时发生错误: ${err}`);
      }
    },
    createMonacoEditor() {
      const container = document.getElementById(
        `monacoEditorContainer${this.index}`
      );
      this.monacoEditor = monaco.editor.create(container, this.assignOptions);
    },
    clearSelection() {
      this.monacoEditor.setValue('');
    },
    changeTheme() {
      monaco.editor.setTheme(this.defaultOptions.theme);
    }
  }
};
</script>

<style scoped lang="less">
.box {
  width: 100%;
  height: 100%;

  .top {
    margin-bottom: 10px;
    .text {
      margin-right: 4px;
    }

    .btn {
      margin-right: 20px;
      margin-left: 0px;
    }

    .select {
      width: 200px !important;
      margin-right: 20px;
    }
  }

  .container {
    // width: 100%;
    // height: 90%;
    // margin-top: 20px;
  }
}
</style>
