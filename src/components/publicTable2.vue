<!--
 * @FilePath: \sansui\KrPeis\src\components\publicTable2.vue
 * @Description:  通用表格数据展示组件2.0，新增异步远程分页功能
 * @Author: justin
 * @Date: 2024-04-29 16:58:04
 * @Version: 2.0.0
 * @LastEditors: key
 * @LastEditTime: 2025-03-03 16:36:58
*
-->
<template>
  <div class="publicTable_com2" v-loading.fullscreen.lock="loadingFullScreen">
    <el-table
      ref="tableCom_Ref"
      size="small"
      :reserve-selection="reserveSelection"
      :data="filterTableData"
      v-loading="loading"
      :style="_elStyle.style"
      :height="_elStyle.height"
      :border="_elStyle.border"
      :stripe="_elStyle.stripe"
      :header-cell-style="_elStyle['header-cell-style']"
      :show-header="_elStyle['show-header']"
      :highlight-current-row="_elStyle['highlight-current-row']"
      :default-expand-all="_elStyle['default-expand-all']"
      :show-summary="_elStyle['show-summary']"
      :tooltip-effect="_elStyle['tooltip-effect']"
      :cell-class-name="setCellClassName"
      :row-class-name="setRowClassName"
      v-el-table-infinite-scroll="loadMore"
      :default-sort="defaultSort"
      :row-key="rowKey"
      @row-dblclick="rowDblclick"
      @row-click="rowClick"
      @cell-click="cellClick"
      @current-change="currentChange"
      @sort-change="sortChange"
    >
      <el-table-column
        v-if="_elStyle['show-selection']"
        prop="$selection"
        width="50"
        class-name="el-table-column--selection"
      >
        <template slot="header" slot-scope="scope">
          <el-checkbox
            v-model="isAllSelected"
            :indeterminate="isIndeterminate"
            :disabled="filterTableData.length === 0"
            @click.prevent.stop.native="selectAll(scope)"
          >
          </el-checkbox>
        </template>
        <template slot-scope="scope">
          <el-checkbox
            v-model="scope.row.$selection"
            @click.prevent.stop.native="select(scope.row)"
          >
          </el-checkbox>
        </template>
      </el-table-column>

      <el-table-column
        v-if="_elStyle['show-sort']"
        prop="$sort"
        :width="_elStyle['sort-width']"
        label="序号"
      >
      </el-table-column>
      <slot name="columnLeft"></slot>

      <el-table-column
        v-for="(item, index) in theads"
        :key="index"
        :prop="item.prop"
        :label="item.label"
        :align="item.align"
        :width="item.width"
        :min-width="item.minWidth || ''"
        :sortable="item.sortable ? (remoteByPage ? 'custom' : true) : false"
        :show-overflow-tooltip="item.showOverflowTooltip"
      >
        <template #header="scope">
          <slot :name="item.prop + 'Header'" :scope="scope">{{
            item.label
          }}</slot>
        </template>

        <template slot-scope="scope">
          <slot :name="item.prop" :scope="scope">{{
            scope.row[item.prop]
          }}</slot>
        </template>
      </el-table-column>
      <slot name="columnRight"></slot>
    </el-table>
  </div>
</template>

<script>
import { dataUtils } from '@/common';
import { export2Excel } from '@/common/excelUtil';
import exportExcelJs from '../common/excel/exportExcel';

export default {
  name: 'publicTable',
  mixins: [exportExcelJs],
  props: {
    // 请求地址
    url: {
      type: String,
      required: true
    },
    // 请求方法
    method: {
      type: String,
      default: 'post',
      validator: function (value) {
        return ['post', 'paramsPost', 'get'].includes(value);
      }
    },
    // 请求参数
    params: {
      type: [Object, Array],
      default: () => {
        return {};
      }
    },
    // 行主键
    rowKey: {
      type: String,
      default: 'id'
    },
    //数据更新保留勾选
    reserveSelection: {
      type: Boolean,
      default: false
    },
    // 表格数据本地过滤函数
    tableDataFilter: {
      type: Function,
      default: (data) => {
        return data;
      }
    },
    // 表格数据取值函数
    tableDataMap: {
      type: Function,
      default: (data) => {
        return data;
      }
    },
    // 表头数据
    theads: {
      type: Array,
      required: true
    },
    // 是否开启分页
    isOpenPage: {
      type: Boolean,
      default: () => {
        return true;
      }
    },
    // 是否开启滚动加载
    isScrollLoad: {
      type: Boolean,
      default: true
    },
    // 是否远程分页获取数据
    remoteByPage: {
      type: Boolean,
      default: false
    },
    // 每页数量
    pageSize: {
      type: Number,
      default: 50
    },
    // 初始排序
    defaultSort: {
      type: Object,
      default: () => {
        return {};
      }
    },
    // 单元格的 className 的回调方法，也可以使用字符串为所有单元格设置一个固定的 className
    cellClassName: {
      type: [Function, String],
      default: ''
    },
    // 行的 className 的回调方法，也可以使用字符串为所有行设置一个固定的 className。
    rowClassName: {
      type: [Function, String],
      default: ''
    },
    // 遍历判断checkbox是否可选
    selectable: {
      type: Function,
      default: () => {
        return true;
      }
    },
    // 配置el-table样式
    elStyle: {
      type: Object,
      default: () => {
        return {};
      }
    },
    // 导出excel列映射函数
    excelColumnsMap: {
      type: Function,
      default: (data) => {
        return data;
      }
    },
    // 导出excel数据映射函数
    excelDataMap: {
      type: Function,
      default: (data) => {
        return data;
      }
    }
  },
  data() {
    return {
      tableData: [], // 表格数据
      totalNumber: 0, // 总条数
      pageNumber: 1, // 当前页码
      totalPage: 0, // 总页数
      tableDataWatchCount: 0, // 表格数据更新次数（间接做computed响应式）
      loading: false, // 加载动画
      loadingFullScreen: false, // 全屏加载动画
      isAllSelected: false, // 是否全选

      startIndex: 0, // 虚拟分页起始索引
      vEle: undefined, // 虚拟元素，用于撑开table的高度
      pageSizeView: 0, // 虚拟分页条数
      elTableBodyWrapper: {}, // el-table的body-wrapper
      isIndeterminate: false, // 头部半选状态
      filterList: [], // 表格数据过滤后列表
      currentRow: null //  当前高亮行
    };
  },
  created() {
    if (!this.isScrollLoad) return;
    // 非响应式数据
    this.rowHeight = this.elStyle.rowHeight || 40; // 行高
    this.checkedList = []; // 已选中的行数据
    this.scrollTopOld = 0; // 上一次滚动条位置
    this.orderByList = []; // 排序数组
    this.orderByTypeMapping = {
      // 按后端枚举映射，如果不传排序类型，后端默认升序
      ascending: 0,
      descending: 1
    };
    this.scrollPosition = { left: 0, top: 0 }; // 当前滚动条位置

    // 创建一个空元素，这个空元素用来撑开 table 的高度，模拟所有数据的高度
    this.vEle = document.createElement('div');
  },
  mounted() {
    if (!this.isScrollLoad) return;
    const that = this;
    if (that.pageSize <= 0) console.error('pageSize must be greater than 0');
    if (that.defaultSort && that.defaultSort.prop)
      that.addOrderList(that.defaultSort.prop, that.defaultSort.order);

    // 绑定滚动事件
    that.elTableBodyWrapper = that.$refs.tableCom_Ref.bodyWrapper;
    that.elTableBodyWrapper.addEventListener('scroll', that.tableScroll, {
      passive: true
    });
  },
  computed: {
    /**
     * @author: justin
     * @description: el-table的Props属性样式
     * @return {*}
     */
    _elStyle() {
      return {
        style: {
          width: '100%',
          color: '#2d3436',
          'font-size': '14px'
        },
        height: '100%', // 表格高度
        border: false, // 是否显示边框
        stripe: true, // 是否显示斑马纹
        'show-header': true, // 是否显示表头
        'show-selection': true, // 是否显示选择框
        'show-sort': true, // 是否显示排序列
        'sort-width': 60, // 排序列宽度
        'highlight-current-row': true, // 是否高亮当前行
        'default-expand-all': false, // 默认全部展开
        'tooltip-effect': 'light', // tooltip效果
        'show-summary': false, // 是否显示合计行
        'header-cell-style': {
          background: 'rgba(23,112,223,.2)',
          fontSize: '14px',
          color: '#2D3436'
        }, // 表头样式

        ...this.elStyle
      };
    },
    /**
     * @author: justin
     * @description: 表格数据过滤，eg：分页、排序、筛选等
     * @return {*}
     */
    filterTableData() {
      // 注意：使用数组的方法来更新数组，会触发响应式更新（避免死循环）
      // 比如 push, pop, shift, unshift, splice, sort, reverse 等方法
      const that = this;
      const aa = that.tableDataWatchCount; // 占用监听
      that.filterList = that.tableDataFilter(that.tableData);
      if (!that.filterList || that.filterList.length == 0) return [];

      that.filterList = that.tableDataSort(that.filterList);
      that.filterList = that.filterList.map((item, index) => {
        item.$sort = index + 1;
        return item;
      });
      // that.pageSizeView 由计算得出，可能由于获取不到表格高度问题
      const spliceLength =
        that.filterList.length - that.startIndex >
        (that.pageSizeView || that.pageSize)
          ? that.startIndex + (that.pageSizeView || that.pageSize)
          : that.filterList.length;

      if (!that.isOpenPage) return that.filterList; // 关闭分页，加载全部数据
      return that.filterList.slice(that.startIndex, spliceLength);
    }
  },
  methods: {
    /**
     * @author: justin
     * @description:  加载数据
     * @return {*}
     */
    // 通过接口加载表格数据
    loadData() {
      if (!this.loadingFullScreen) {
        this.tableData = [];
        this.totalNumber = 0;
        this.pageNumber = 1;
        this.scrollTopOld = 0;
        this.currentRow = null;
      }

      this.$nextTick(() => {
        this.clearSelection();
        this.remoteLoadData();
      });
    },
    // 静态加载表格数据
    staticLoad(list) {
      if (!this.loadingFullScreen) {
        this.tableData = [];
        this.totalNumber = 0;
        this.pageNumber = 1;
        this.scrollTopOld = 0;
        this.currentRow = null;
      }
      this.$nextTick(() => {
        this.clearSelection();
        this.tableDataInit({ returnData: list });
      });
    },

    /**
     * @author: justin
     * @description: 滚动加载
     * @return {*}
     */
    loadMore() {
      const that = this;
      if (!that.isOpenPage || that.loading || that.loadingFullScreen) return;
      if (that.pageNumber * that.pageSize >= that.totalNumber) return;
      // 如果上次滚动条位置>0，重置查询后（调用方法->loadData），
      // 会触发loadMore事件（具体原因待查），故做以下处理
      if (that.scrollPosition.top == 0) return;
      // 滚动过快时，偶发一次loadMore，联动多次loadMore触发问题
      // 发现只是滚动条位移距离+1，故做以下处理
      if (that.scrollPosition.top - that.scrollTopOld < 5) return;

      that.pageNumber++;
      that.scrollTopOld = that.scrollPosition.top;
      that.remoteLoadData();
    },

    /**
     * @author: justin
     * @description:  远程加载数据
     * @return {*}
     */
    remoteLoadData() {
      const that = this;
      // 已加载全部不发生请求
      if (that.totalNumber > 0 && that.tableData.length >= that.totalNumber) {
        that.loadingFullScreen = false;
        return;
      }

      let params = {
        pageSize: that.loadingFullScreen ? 0 : that.pageSize,
        pageNumber: that.loadingFullScreen ? 1 : that.pageNumber,
        orderByList: that.orderByList,
        ...that.params
      };
      if (!that.remoteByPage) params = that.params;

      that.loading = true;
      switch (that.method) {
        case 'post':
          that.postRequest(params);
          break;
        case 'paramsPost':
          that.paramsPostRequest(params);
          break;
        case 'get':
          that.getRequest(params);
          break;
      }
    },

    /**
     * @author: justin
     * @description:  发送post请求
     * @param {*} params 请求参数
     * @return {*}
     */
    postRequest(params) {
      this.$ajax
        .post(this.url, params)
        .then((res) => {
          let { success } = res.data;
          if (!success) return;

          this.tableDataInit(res.data);
        })
        .catch((err) => {
          this.requestError(err);
        })
        .finally((_) => {
          this.requestFinally();
        });
    },

    /**
     * @author: justin
     * @description:  发送post-url参数请求
     * @param {*} params 请求参数
     * @return {*}
     */
    paramsPostRequest(params) {
      this.$ajax
        .paramsPost(this.url, params)
        .then((res) => {
          let { success } = res.data;
          if (!success) return;

          this.tableDataInit(res.data);
        })
        .catch((err) => {
          this.requestError(err);
        })
        .finally((_) => {
          this.requestFinally();
        });
    },

    /**
     * @author: justin
     * @description:  发送get请求
     * @param {*} params 请求参数
     * @return {*}
     */
    getRequest(params) {
      this.$ajax
        .get(this.url, params)
        .then((res) => {
          let { success } = res.data;
          if (!success) return;

          this.tableDataInit(res.data);
        })
        .catch((err) => {
          this.requestError(err);
        })
        .finally((_) => {
          this.requestFinally();
        });
    },

    /**
     * @author: justin
     * @description: 请求成功处理
     * @param {*} responseData 响应数据
     * @return {*}
     */
    tableDataInit(responseData) {
      const that = this;
      try {
        let list = that.tableDataMap(responseData.returnData) || [];
        if (that._elStyle['show-selection']) {
          list.map((x) => {
            x.$selection = that.isAllSelected;
            return x;
          });
        }

        if (that.loadingFullScreen) {
          that.tableData = list;
        } else {
          if (that.pageNumber <= 1) that.tableData = []; // 解决重置查询，异步请求累计数据问题

          that.tableData.push(...list);
        }

        that.totalNumber = responseData.totalNumber || that.tableData.length;
        that.tableScroll();
      } finally {
        that.$emit('request-success', responseData);
      }
    },

    /**
     * @author: justin
     * @description: 请求失败处理
     * @param {*} err 错误信息
     * @return {*}
     */
    requestError(err) {
      if (this.remoteByPage) {
        this.pageNumber--;
        this.scrollTopOld = 0;
      }

      this.$emit('request-error', err);
    },

    /**
     * @author: justin
     * @description: 请求结束处理 不管是否成功都执行
     * @return {*}
     */
    requestFinally() {
      this.loading = false;
      this.loadingFullScreen = false;
      this.$emit('request-finally');
    },

    /**
     * @author: justin
     * @description: 界面已选集合
     * @return {*}
     */
    getSelection() {
      return (this.filterList || []).filter((item) => item.$selection);
    },

    /**
     * @author: justin
     * @description: 复选框改变事件：全选&单选都触发
     * @return {*}
     */
    selectionChange() {
      const that = this;
      that.checkedList = that.getSelection();
      that.isAllSelected =
        that.checkedList.length > 0 &&
        that.filterList.length == that.checkedList.length;
      that.isIndeterminate =
        that.checkedList.length > 0 &&
        that.filterList.length != that.checkedList.length;

      that.$emit('selectionChange', that.checkedList, that.checkedList);
    },

    /**
     * @author: justin
     * @description: 单行选择的回调
     * @param {*} row 当前行数据
     * @return {*}
     */
    select(row) {
      const that = this;
      const index = that.tableData.indexOf(row);
      if (index === -1) return;

      that.$set(
        this.tableData[index],
        '$selection',
        !that.tableData[index]['$selection']
      );
      const selection = that.getSelection();
      that.$emit('select', selection, row);
    },

    /**
     * @author: justin
     * @description: 当用户手动勾选全选 Checkbox 时触发的事件
     * @param {*} scope 表头数据
     * @return {*}
     */
    selectAll(scope) {
      const that = this;
      if (that.tableData.length == 0) return;

      that.isAllSelected = !that.isAllSelected;
      that.isIndeterminate = false;
      for (let i = 0; i < that.tableData.length; i++) {
        that.$set(that.tableData[i], '$selection', that.isAllSelected);
      }
      if (!that.remoteByPage || !that.isAllSelected) return;

      that.loadingFullScreen = true;
      that.loadData();
    },

    /**
     * @author: key
     * @description: 全部导出
     * @return {*}
     */
    exportToExcelAll() {
      this.loadingFullScreen = true;
      this.$ajax
        .post(this.url, this.params)
        .then((res) => {
          let { success, returnData } = res.data;
          if (!success) return;
          this.checkedList = dataUtils.deepCopy(returnData);
          this.exportToExcel();
        })
        .catch((err) => {
          this.requestError(err);
        })
        .finally((_) => {
          this.requestFinally();
        });
    },

    /**
     * @author: justin
     * @description: 清空用户的选择
     * @return {*}
     */
    clearSelection() {
      for (let i = 0; i < this.tableData.length; i++) {
        this.$set(this.tableData[i], '$selection', this.isAllSelected);
      }
      this.selectionChange();
    },

    /**
     * @author: justin
     * @description: 表格的双击事件
     * @param {*} row
     * @param {*} column
     * @param {*} event
     * @return {*}
     */
    rowDblclick(row, column, event) {
      this.$emit('rowDblclick', row, column, event);
    },

    /**
     * @author: justin
     * @description:  表格的单击事件
     * @param {*} row
     * @return {*}
     */
    rowClick(row) {
      this.$emit('rowClick', row);
    },

    /**
     * @author: justin
     * @description:  单元格的单击事件
     * @param {*} row
     * @param {*} column
     * @param {*} cell
     * @param {*} event
     * @return {*}
     */
    cellClick(row, column, cell, event) {
      this.$emit('cellClick', row, column, cell, event);
    },

    /**
     * @author: justin
     * @description: 当表格的当前行发生变化的时候会触发该事件，如果要高亮当前行，请打开表格的 highlight-current-row 属性
     * @param {*} currentRow
     * @param {*} oldCurrentRow
     * @return {*}
     */
    currentChange(currentRow, oldCurrentRow) {
      this.currentRow = currentRow;
      this.$emit('currentChange', currentRow, oldCurrentRow);
    },

    /**
     * @author: justin
     * @description: 当sortable='custom'时，触发远程排序事件
     * @param {*} column 列对象
     * @param {*} prop 列属性
     * @param {*} order 排序规则
     * @return {*}
     */
    sortChange({ column, prop, order }) {
      // todo：目前组件只支持单列排序，后续升级支持多列排序
      this.addOrderList(prop, order);
      if (this.remoteByPage) {
        this.loadData();
      } else {
        this.tableDataWatchCount++;
      }
    },

    /**
     * @author: justin
     * @description: 本地排序
     * @param {*} arr
     * @return {*}
     */
    tableDataSort(arr) {
      if (!arr) return [];
      const that = this;
      if (!that.orderByList || that.orderByList.length === 0) return arr;

      // arr.slice() 新数组避免响应式
      return arr.slice().sort((a, b) => {
        for (let field of that.orderByList) {
          if (!field.fieldName || !field.orderByType) return 0;
          if (a[field.fieldName] < b[field.fieldName]) {
            return field.orderByType === that.orderByTypeMapping.ascending
              ? -1
              : 1;
          } else if (a[field.fieldName] > b[field.fieldName]) {
            return field.orderByType === that.orderByTypeMapping.ascending
              ? 1
              : -1;
          }
        }
        return 0;
      });
    },

    /**
     * @author: justin
     * @description: 添加排序内容
     * @param {*} fieldName 字段名
     * @param {*} order 排序方式
     * @return {*}
     */
    addOrderList(fieldName, order) {
      // todo：目前组件只支持单列排序，后续升级支持多列排序
      this.orderByList = [];
      if (fieldName) {
        this.orderByList.push({
          fieldName: fieldName,
          orderByType: this.orderByTypeMapping[order]
        });
      }
    },

    /**
     * @author: justin
     * @description: 重置数据
     * @return {*}
     */
    resetData() {
      this.tableData = [];
      this.totalNumber = 0;
      this.pageNumber = 1;
      this.totalPage = 0;
      this.tableDataWatchCount = 0;
      this.isAllSelected = false;
      this.orderByList = [];
      this.scrollTopOld = 0;
      this.checkedList = [];
    },

    /**
     * @author: justin
     * @description: 导出数据到excel
     * @param {*} fileName 文件名
     * @return {*}
     */
    exportToExcel(options = { fileName: '列表', title: '', countText: '' }) {
      const that = this;
      let checkedList = [];
      if (!that.checkedList || that.checkedList.length > 0) {
        checkedList = this.checkedList;
      } else if (this.currentRow) {
        checkedList.push(this.currentRow);
      }
      // if (!that.checkedList || that.checkedList.length <= 0) {
      //   return that.$message.warning("请至少选择一条记录进行Excel导出！");
      // }
      if (checkedList.length <= 0) {
        return that.$message.warning('请至少选择一条记录进行Excel导出！');
      }

      that
        .$confirm(
          `确定导出<font color='red'>${checkedList.length}</font>条列表数据到Excel文件吗？`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
        )
        .then(() => {
          const notify = that.$notify.success('正在导出Excel文件，请稍候...');
          try {
            let columns = (
              that._elStyle['show-sort']
                ? [{ prop: 'index', label: '序号' }]
                : []
            )
              .concat(that.theads)
              .map((item) => {
                return {
                  title: item.label,
                  key: item.prop
                };
              });
            columns = that.excelColumnsMap(columns);
            let exportList = that.excelDataMap(dataUtils.deepCopy(checkedList));
            if (that._elStyle['show-sort']) {
              exportList.map((item, index) => {
                item.index = index + 1;
              });
            }
            console.log(that.theads, exportList);
            let theads = {};
            that.theads.forEach((item) => {
              theads[item.prop] = item.label;
            });
            const fileName =
              options.fileName + '_' + dataUtils.getNowDateTiemNo();
            that.$nextTick(() => {
              // export2Excel(columns, exportList, title);
              this.exportExcel(
                fileName,
                options.title || options.fileName,
                theads,
                exportList,
                options.countText
              );
              exportList = null;
            });
          } catch (err) {
            console.error(err);
            notify.close();
            that.$notify.error('导出失败，请稍后重试！');
          }
        });
    },

    /**
     * @author: justin
     * @description: 设置单元格的className
     * @param {*} row 行数据
     * @param {*} rowIndex 行索引
     * @param {*} column 列对象
     * @param {*} columnIndex 列索引
     * @return {*}
     */
    setCellClassName({ row, rowIndex, column, columnIndex }) {
      if (!this.cellClassName)
        return (
          this.theads.find((x) => x.prop == column.property)?.cellClassName ||
          ''
        );

      if (this.cellClassName instanceof Function)
        return this.cellClassName(row, rowIndex, column, columnIndex);

      return this.cellClassName;
    },

    /**
     * @author: justin
     * @description: 设置行的className
     * @param {*} row 行数据
     * @param {*} rowIndex 行索引
     * @return {*}
     */
    setRowClassName({ row, rowIndex }) {
      let rowClassName = '';
      if (this.rowClassName instanceof Function)
        rowClassName = this.rowClassName(row, rowIndex);
      else rowClassName = this.rowClassName;

      return rowClassName;
    },

    /**
     * @description: table 滚动事件
     * @return {*}
     */
    tableScroll() {
      if (!this.isScrollLoad) return;
      const that = this;
      that.scrollPosition.top = that.elTableBodyWrapper.scrollTop;
      that.scrollPosition.left = that.elTableBodyWrapper.scrollLeft;

      // 下一次开始的索引
      that.startIndex = Math.floor(that.scrollPosition.top / that.rowHeight);
      // 滚动操作
      that.elTableBodyWrapper.querySelector('.el-table__body').style.transform =
        `translateY(${that.startIndex * that.rowHeight}px)`;
    },

    /**
     * @author: justin
     * @description: 追加占用高度内容到表格内容至尾部
     * @return {*}
     */
    appendChildToTBodyWrapper() {
      if (!this.isScrollLoad) return;
      const that = this;
      // 设置成绝对定位，这个元素需要我们去控制滚动
      that.elTableBodyWrapper.querySelector('.el-table__body').style.position =
        'absolute';
      // 计算表格所有数据所占内容的高度
      that.vEle.style.height = that.filterList.length * that.rowHeight + 'px';
      // 把这个节点加到表格中去，用它来撑开表格的高度
      that.elTableBodyWrapper.appendChild(that.vEle);
    },

    /**
     * @author: justin
     * @description: 设置虚拟分页显示数量
     * @return {*}
     */
    setPageSizeView() {
      if (!this.isScrollLoad) return;
      const that = this;
      if (!that.pageSizeView) {
        const bodyWrapperHeight =
          parseFloat(that.elTableBodyWrapper.style.height) || 0;
        const bodyHeight =
          parseFloat(
            that.elTableBodyWrapper.querySelector('.el-table__body').style
              .height
          ) || 0;
        const contentHeight =
          bodyWrapperHeight + bodyWrapperHeight - bodyHeight;
        that.pageSizeView = Math.ceil(contentHeight / that.rowHeight);
      }
    },

    /**
     * @author: justin
     * @description: 重新渲染表格，解决抖动等问题（eg:列宽自适应遇到v-show会）
     * @return {*}
     */
    doLayout() {
      this.$nextTick(() => {
        this.$refs.tableCom_Ref.doLayout();
      });
    },

    /**
     * @author: justin
     * @description: 恢复滚动条位置
     * @param {*} top Y轴位置
     * @param {*} left X轴位置
     * @return {*}
     */
    recoverScroll(top = null, left = null) {
      const that = this;
      setTimeout(() => {
        that.elTableBodyWrapper.scrollTop = top || that.scrollPosition.top;
        that.elTableBodyWrapper.scrollLeft = left || that.scrollPosition.left;
      }, 50);
    }
  },
  watch: {
    pageSize: {
      handler(newVal, oldVal) {
        if (newVal <= 0) {
          console.error('pageSize must be greater than 0');
          return;
        }

        this.totalPage = Math.ceil(this.totalNumber / newVal);
      }
    },
    tableData: {
      handler(newVal, oldVal) {
        const that = this;
        that.tableDataWatchCount++;
        that.$nextTick(() => {
          that.selectionChange();
          that.appendChildToTBodyWrapper();
          that.setPageSizeView();
        });
      },
      immediate: true,
      deep: true
    },
    filterTableData: {
      handler(newVal, oldVal) {
        const that = this;
        if (!newVal || newVal.length == 0) return;
        // 检查rowKey是否存在
        if (that.rowKey && !newVal[0].hasOwnProperty(that.rowKey)) {
          console.error(
            `rowKey-[${that.rowKey}] is not exist in filterTableData`
          );
        }
      },
      immediate: true
    },
    filterList: {
      handler(newVal, oldVal) {
        if ((newVal?.length || 0) >= (oldVal?.length || 0)) return;
        const that = this;
        that.$nextTick(() => {
          // 重置内容布局
          that.elTableBodyWrapper.scrollTop = 0;
          that.appendChildToTBodyWrapper();
        });
      },
      immediate: true
    }
  },
  activated() {
    const that = this;
    that.setPageSizeView();
    that.recoverScroll();
  },
  deactivated() {
    const that = this;
    that.scrollPosition.top = that.elTableBodyWrapper.scrollTop;
    that.scrollPosition.left = that.elTableBodyWrapper.scrollLeft;
  }
};
</script>

<style lang="less">
.publicTable_com2 {
  height: 100%;
  width: 100%;
  .el-table__row--striped td.el-table__cell,
  .vxe-table--render-default .vxe-body--row.row--stripe {
    background-color: #e7f0fb !important;
  }

  .vxe-table--render-default .vxe-body--row.row--current {
    background-color: #e6f7ff;
  }
  .el-table__cell {
    vertical-align: top;
  }
  .el-table__body tr.current-row > td.el-table__cell {
    background: no-repeat;
  }
  .el-table .have_pay {
    background: rgba(60, 179, 79, 0.1);
  }
  .el-table .no_pay {
    background: rgba(250, 182, 59, 0.1);
  }
  .el-table .have_meal {
    background: rgba(115, 100, 244, 0.1);
  }
  input[type='number'] {
    -moz-appearance: textfield;
  }
  input[type='number']::-webkit-inner-spin-button,
  input[type='number']::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  .el-table .tr_red {
    color: #d63031 !important;
  }
  .el-table .tr_violet {
    color: #7364f4;
  }
  .el-table .tr_black {
    color: #2d3436 !important;
  }
  .el-table .tr_gray {
    background: rgba(178, 190, 195, 0.2);
  }
  .el-table .tr_lightRed {
    background: rgba(214, 48, 49, 0.1);
  }
  .el-table .tr_lightBlue {
    background: rgba(23, 112, 223, 0.1);
  }
  .el-table__cell {
    vertical-align: middle !important;
  }
  /* 修改表格勾选框样式 */
  .el-table .el-checkbox__inner::after {
    left: 6px;
    top: 3px;
  }
  .el-table .el-checkbox__inner,
  .el-table .el-checkbox__input.is-disabled .el-checkbox__inner {
    width: 18px;
    height: 18px;
  }
  .el-table .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    background-color: #409eff !important;
    border-color: #409eff !important;
    font-size: 18px;
  }
  .el-table
    .el-checkbox__input.is-disabled.is-checked
    .el-checkbox__inner::after {
    border-color: #fff !important;
  }

  .el-table__row--hidden {
    display: none;
  }
}
</style>
