import { storage } from '../common';
import WS from '../common/websocket';
import { mapMutations, mapGetters } from 'vuex';
import Vue from 'vue';
export default {
  props: {
    //控制显示
    displaySwitches: {
      type: Boolean,
      default: false
    },
    globalFilterList: {
      type: Array,
      default: () => [
        'Guidance',
        'Gastroscope',
        'Colonoscopy',
        'AnesthesiaCost',
        'PeLabel',
        'BloodBarcode',
        'NonBloodBarcode'
      ]
    }
  },
  components: {
    'report-loading': {
      template: `<div class="my_mask" v-if="reportLoadingShow">
                <el-progress type="circle" :percentage="percentage"></el-progress>
                <p class="tips_p">{{ message }}</p>
            </div>`,
      props: {
        reportLoadingShow: {
          type: Boolean,
          default: false,
          require: true
        },
        percentage: {
          type: Number,
          default: 0
        },
        message: {
          type: String,
          default: ''
        }
      }
    }
  },
  data() {
    return {
      printParameters: {
        printer: '', //打印机
        barcodePrinters: '', //条码打印机
        printList: [], //打印机列表
        printType: [], //打印类型
        selectedPrint: [], //打印选中
        regNo: '', //体检号
        regNoList: [], //体检号列表
        printableList: [], //可打印列表
        loading: false,
        progressData: {
          progress: '', //打印进度
          loading: false
        },
        notActive: 0,
        printStatus: false,
        cancelPrint: false
      },
      time: 300, // 设置每页打印的时间
      totalPages: 0, // 每份报告的总页数
      currentNum: 0, // 当前正在打印的人
      reportPrintSchedule: '',
      percentage: 0,
      reportLoadingShow: false,
      reportPrintVal: ''
    };
  },
  computed: {
    ...mapGetters(['G_printerList']),
    printObj() {
      const obj = [
        {
          type: 'Guidance',
          typeName: '指引单',
          disabled: false,
          printerType: 0
        },
        {
          type: 'Gastroscope',
          typeName: '胃镜申请单',
          disabled: false,
          printerType: 0
        },
        {
          type: 'Colonoscopy',
          typeName: '肠镜申请单',
          disabled: false,
          printerType: 0
        },
        {
          type: 'AnesthesiaCost',
          typeName: '胃肠镜材料费单',
          disabled: false,
          printerType: 0
        },
        // {
        //     type: "Report",
        //     typeName: "报告",
        //     disabled: false,
        //     printerType: 0
        // },
        {
          type: 'PeLabel',
          typeName: '体检标签',
          disabled: false,
          printerType: 1
        },
        // {
        //     type: "Barcode",
        //     typeName: "检验条码",
        //     disabled: false,
        //     printerType: 1
        // },

        {
          type: 'BloodBarcode',
          typeName: '采血条码',
          disabled: false,
          printerType: 1
        },
        {
          type: 'NonBloodBarcode',
          typeName: '非采血条码',
          disabled: false,
          printerType: 1
        }
      ];
      return obj.filter((item) => this.globalFilterList.includes(item.type));
    }
  },
  methods: {
    ...mapMutations(['M_printerList']),
    /**
     * @description: 确定打印
     * @param {*} isSingleCopy 是否单打
     * @param {*} callback 回调
     * @return {*}
     * @author: key
     */
    async print(isSingleCopy, callback) {
      if (
        !this.printParameters.printer &&
        !this.printParameters.barcodePrinters
      ) {
        this.$message({
          message: '请选择打印类型',
          type: 'warning',
          showClose: true
        });
        return;
      }
      try {
        let data;
        let totalTasks = 0; //打印数量
        let completedTasks = 0; //当前已打印数量
        this.progress = 0; //进度条
        this.printParameters.progressData.progress = '';
        this.printParameters.printStatus = true;
        this.printParameters.cancelPrint = false;
        if (isSingleCopy) {
          //单打
          totalTasks = 1;
          //设置打印列表的打印机类型
          data = this.setPrinterType(this.printParameters.printableList);
          const temp = data.find(
            (item) => item.type === this.printParameters.selectedPrint
          );
          //更新打印状态
          this.printParameters.progressData.loading = true;

          this.startPrinting(this.pdfSrc, temp.printerType);
          this.updatePrintFlag(this.printParameters.regNo, temp.type);
          completedTasks++;
          this.updateProgress(completedTasks, totalTasks);
        } else {
          //批打

          let regNoList = this.printParameters.regNoList.map(
            (item) => item.regNo
          );
          console.log(
            '🚀 ~ print ~ this.printParameters.regNoList:',
            this.printParameters.regNoList
          );
          console.log('🚀 ~ print ~ regNoList:', regNoList);

          totalTasks = regNoList.length;
          this.printParameters.progressData.loading = true;

          // if (this.printParameters.notActive > 0) {
          //     this.$message({
          //         message: `已跳过未激活人数:${this.printParameters.notActive}人`,
          //         type: "warning",
          //         showClose: true,
          //     });
          // }

          let res, isEmpty;
          for (const item of regNoList) {
            if (this.printParameters.cancelPrint) return;
            res = await this.getAPrintableList(item);

            data = this.setPrinterType(res);
            data = data.filter((item) =>
              this.printParameters.selectedPrint.includes(item.type)
            );
            if (data.length > 0) {
              for (const item of data) {
                if (this.printParameters.cancelPrint) return;
                const fileStream = await this.retrieveFileStream(
                  item.pdfUrl + '?updatePrintStatus=true'
                );
                this.startPrinting(fileStream, item.printerType);
              }
              isEmpty = true;
            }
            completedTasks++;
            this.updateProgress(completedTasks, totalTasks);
          }

          if (!isEmpty) {
            this.$message({
              message: '没有可打印数据',
              type: 'warning',
              showClose: true
            });
            this.printParameters.printStatus = false;
            this.printParameters.progressData.loading = false;
            return;
          }
        }
        if (callback && typeof callback === 'function') {
          callback();
        } else {
          this.$emit('update:displaySwitches', false);
        }
      } catch (error) {
        console.log('🚀 ~ print ~ error:', error);
        this.printParameters.progressData.loading = false;
        this.printParameters.printStatus = false;
        this.$message({
          message: '打印出错啦！',
          type: 'error',
          showClose: true
        });
      }
    },
    /**
     * @description:更新打印状态
     * @param {*} regNo
     * @return {*}
     * @author: key
     */
    updatePrintFlag(regNo, printType) {
      this.$ajax
        .paramsPost(this.$apiUrls.UpdatePrintStatus, { regNo, printType })
        .then((res) => {});
    },
    /**
     * @description: 打印进度条
     * @param {*} completedTasks 已完成的任务
     * @param {*} totalTasks 总任务数
     */
    updateProgress(completedTasks, totalTasks) {
      // this.printParameters.progressData.progress = Math.floor((completedTasks / totalTasks) * 100);
      this.printParameters.progressData.progress = `${completedTasks}/${totalTasks}`;
      if (completedTasks == totalTasks) {
        this.printParameters.progressData.loading = false;
        this.printParameters.printStatus = false;
        this.$message({
          message: '打印完成',
          type: 'success',
          showClose: true
        });
      }
    },
    /**
     * @description: 设置可打印列表打印机类型
     * @param {*} data
     * @return {*}
     * @author: key
     */
    setPrinterType(data) {
      return data
        .flat()
        .map((item) => {
          const foundData = this.printObj.find((d) => d.type === item.type);
          if (foundData) {
            return {
              ...item,
              printerType: foundData.printerType
            };
          }
          return null;
        })
        .filter((item) => item !== null);
    },
    /**
     * @description: 开始打印
     * @param {*} fileStream 文件流
     * @param {*} printerType 打印机类型 0:打印机 1:条码打印机
     * @return {*}
     * @author: key
     */
    startPrinting(fileStream, printerType, reportPagePrintFlag = false) {
      if (
        (this.$w_print &&
          this.$w_print?.websock?.readyState === WebSocket.CLOSED) ||
        (this.$w_print && !this.$w_print?.websock)
      ) {
        this.$w_print.closeWebSocket();
        Vue.prototype.$w_print = new WS(`${this.$config.printUrl}printer`);
        switch (this.$w_print.websock?.readyState) {
          case 1:
            this.$message({
              message: '重连打印机成功！',
              type: 'success'
            });
            break;
          case 0:
            this.$message({
              message: '重连打印机失败！',
              type: 'error'
            });
            break;
        }
      }
      const fileStreams = fileStream.split(',')[1];
      const data = {
        PrinterName:
          printerType === 0
            ? this.printParameters.printer
            : this.printParameters.barcodePrinters,
        PrintTimes: 1,
        FileBase64: fileStreams,
        FileFormat: 'pdf'
      };
      // 是否报告分页打印
      if (reportPagePrintFlag) {
        data.PrinterName = this.reportPrintVal;
      }
      const message = {
        Action: 'print',
        Data: JSON.stringify(data)
      };
      this.$w_print.sendSock(JSON.stringify(message));
    },

    /**
     * @description: 获取文件流
     * @param {*} url 文件地址
     * @return {*}
     * @author: key
     */
    async retrieveFileStream(url, isPreview = false) {
      const res = await this.$ajax.get(url, { responseType: 'arraybuffer' });
      this.previewBuffer = res.data;
      const blob = new Blob([res.data], { type: 'application/pdf' });
      const base64Data = new Promise((resolve) => {
        const reader = new FileReader();
        reader.onload = (e) => {
          let data;
          if (typeof e.target.result === 'object') {
            data = window.URL.createObjectURL(e.target.result);
          } else {
            data = e.target.result;
          }
          resolve(data);
        };
        // 转化为 base64
        reader.readAsDataURL(blob);
      });

      if (isPreview) {
        return {
          pdfSrc: await base64Data,
          pdfDate: URL.createObjectURL(blob)
        };
      } else {
        return await base64Data;
      }
    },
    /**
     * @description: 导出PDF文件
     * @return {*}
     * @author: key
     */
    async exportPdf() {
      const temp = this.printParameters.printableList.find(
        (item) => item.type === this.printParameters.selectedPrint
      );
      const base64Data = this.pdfSrc.split(',')[1];
      const response = await fetch(`data:application/pdf;base64,${base64Data}`);
      const blob = await response.blob();
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = `${this.printParameters.regNo}-${temp.typeName}.pdf`;
      link.click();
    },
    /**
     * @description: 设置打印机数据 setPrinterData
     * @return {*}
     * @author: key
     */
    setPrinterData() {
      console.log(this.$w_print);
      this.connectPrint((r) => {
        console.log('🚀 ~ this.connectPrint ~ r:', r);
        // const print = storage.local.get("print");
        const dataInfo = JSON.parse(r.data);
        if (dataInfo?.Data?.length) {
          this.M_printerList(dataInfo.Data);
          // this.printParameters.printList = dataInfo.Data;
          // this.printParameters.printer = print ? print.printer : dataInfo.Data[0];
          // this.printParameters.barcodePrinters = print ? print.barcodePrinters : dataInfo.Data[0];
        }
      });
    },

    /**
     * @description: 获取打印机数据
     * @param {*} websocketonmessage  服务器响应数据
     * @return {*}
     * @author: key
     */
    connectPrint(websocketonmessage) {
      if (
        !this.$w_print ||
        !this.$w_print?.websock ||
        this.$w_print?.websock?.readyState === WebSocket.CLOSED
      ) {
        this.$w_print?.closeWebSocket();
        Vue.prototype.$w_print = new WS(`${this.$config.printUrl}printer`);
        // this.$w_print.onerror = (r) => {
        //     this.$message({
        //         message: '连接失败，请先打开本地打印机程序！',
        //         type: 'error',
        //         showClose: true,
        //         duration: 5000
        //     });
        // };
      }
      console.log(this.$w_print);
      var data = "{'Action':'getPrinterList','Data':''}";
      this.$w_print.websocketonmessage = websocketonmessage;
      this.$w_print.sendSock(data);
    },

    /**
     * @description: 断开链接
     * @return {*}
     * @author: key
     */
    breakTheLink() {
      if (JSON.stringify(this.$w_print) == '{}') return;
      this.$w_print.closeWebSocket && this.$w_print.closeWebSocket();
      this.$w_print.reConnect = () => {};
      this.$w_print.createWebSocket = () => {};
      this.$w_print.heartCheck.stop && this.$w_print.heartCheck.stop();
    },

    /**
     * @description: 设置打印类型列表
     * @param {*} data 查询可打印接口返回的数据
     * @return {*}
     * @author: key
     */
    setThePrintType(data) {
      this.printParameters.printType = this.printObj.map((item) => {
        const foundData = data?.find((d) => d.type === item.type) || {
          pdfUrl: '',
          type: ''
        };

        return {
          ...item,
          disabled: !foundData.type
        };
      });
    },

    /**
     * @description: 默认勾选打印类型
     * @return {*}
     * @author: key
     */
    defaultSelectionOfPrintingType() {
      //7.24需求  去掉采血条码打印勾选
      this.printParameters.selectedPrint = this.printParameters.printType
        .filter(
          (item) =>
            !['PeLabel', 'BloodBarcode', 'NonBloodBarcode'].includes(
              item.type
            ) && !item.disabled
        )
        .map((item) => item.type);
    },

    /**
     * @description: 多人设置打印类型列表
     * @return {*}
     * @author: key
     */
    setPrintingTypeManyPeople() {
      this.printParameters.printType = [...this.printObj];
    },
    /**
     * @description: 获取可打印列表
     * @param {*} regNo 必传--体检号
     * @param {*} filterList 可不传--过滤
     * @return {*}
     * @author: key
     */
    async getAPrintableList(regNo, filterList = this.globalFilterList) {
      const singleRegNo = Array.isArray(regNo) ? regNo[0] : regNo;
      const res = await this.$ajax.post(
        this.$apiUrls.QueryPrintFileList + `/${singleRegNo}`,
        filterList
      );
      const { success, returnData } = res.data;
      if (success) {
        return returnData;
      } else {
        return [];
      }
    },

    /**
     * @description: 设置打印类型列表
     * @param {*} data 体检人员数据
     * @return {*}
     * @author: key
     */
    async setPrintTypeList(data) {
      this.printParameters.loading = true;
      try {
        if (Array.isArray(data)) {
          // let temp = data.filter(item => item.isActive);
          // this.printParameters.notActive = data.length - temp.length;
          this.printParameters.regNoList = data;
          if (data.length === 1) {
            const res = await this.getAPrintableList(data[0].regNo);
            this.printParameters.printableList = res;
            this.setThePrintType(res);
          } else {
            this.setPrintingTypeManyPeople();
          }
        } else {
          let regNo = Array.isArray(data) ? data[0].regNo : data;
          this.printParameters.regNo = regNo;
          const res = await this.getAPrintableList(regNo);
          this.printParameters.printableList = res;
          this.setThePrintType(res);
        }

        //是否默认勾选
        if (this?.defaultCheck) {
          this.defaultSelectionOfPrintingType();
        }
      } catch (error) {
        console.error('Error in setPrintTypeList:', error);
      } finally {
        this.printParameters.loading = false;
      }
    },
    // 报告打印开始
    reportPrintStart(fileStream) {
      if (
        (this.$w_print &&
          this.$w_print?.websock?.readyState === WebSocket.CLOSED) ||
        (this.$w_print && !this.$w_print?.websock)
      ) {
        this.$w_print.closeWebSocket();
        Vue.prototype.$w_print = new WS(`${this.$config.printUrl}printer`);
      }
      let fileStreams = fileStream.split(',')[1];
      let obj2 = {
        PrinterName: this.reportPrintVal,
        PrintTimes: 1,
        FileBase64: fileStreams,
        FileFormat: 'pdf'
      };
      let obj = {
        Action: 'print',
        Data: JSON.stringify(obj2)
      };
      var data = JSON.stringify(obj);
      this.$w_print.sendSock(data);
    },
    // 报告打印
    reportPrint(list, isOccupation) {
      this.reportPrintSchedule = `${this.currentNum}/${list.length}`;
      this.percentage = Math.floor(
        (this.currentNum / list.length).toFixed(2) * 100
      );
      console.log(this.currentNum);
      if (this.currentNum >= list.length) {
        setTimeout(() => {
          this.$message({
            message: '打印完成',
            type: 'success',
            showClose: true
          });
          this.reportLoadingShow = false;
          this.currentNum = 0;
        }, this.time * this.currentNum);
        return;
      }
      this.batchGetPDF(list[this.currentNum], isOccupation).then((r) => {
        setTimeout(() => {
          this.currentNum += 1;
          this.reportPrint(list, isOccupation);
        }, this.time * this.totalPages);
      });
    },
    // 获取报告PDF
    batchGetPDF(row, isOccupation) {
      return new Promise((resolve, reject) => {
        let url = `/PrintFile/ReportPdf/${row.regNo}?updatePrintStatus=${true}&reportCode=${this.dataInfo.lockingReportType ? this.dataInfo.guidanceType : row.reportType}${isOccupation === undefined ? '' : `&isOccupation=${isOccupation}`}`;
        this.$ajax
          .get(url, {
            responseType: 'arraybuffer'
          })
          .then((r) => {
            const blob = new Blob([r.data], { type: 'application/pdf' });
            let reader = new FileReader();

            reader.onload = (e) => {
              console.log(e);
              let data;
              if (typeof e.target.result === 'object') {
                data = window.URL.createObjectURL(e.target.result);
              } else {
                data = e.target.result;
              }
              this.reportPrintStart(data);
            };
            //转化为base64
            reader.readAsDataURL(blob);

            let file = new File([r.data], '', {
              type: 'application/pdf'
            });
            let fileReader = new FileReader();
            fileReader.readAsBinaryString(file);
            fileReader.onloadend = (e) => {
              console.log(fileReader);
              var count = fileReader.result.match(
                /\/Type[\s]*\/Page[^s]/g
              ).length;
              console.log('Number of Pages:', count);
              this.totalPages = count;
              console.log(this.totalPages);
              resolve(this.totalPages);
            };
            this.updatePrintStatus(row.regNo);
          });
      });
    },
    updatePrintStatus(regNo) {
      if (!regNo) return;
      let idx = this.tableData.findIndex((item) => item.regNo == regNo);
      this.tableData[idx].reportPrinted = true;
    }
  },
  mounted() {
    // this.setPrinterData();
  },
  watch: {
    G_printerList: {
      handler(newVal, oldVal) {
        const print = storage.local.get('print');
        // console.log(print);
        this.printParameters.printList = newVal;
        this.printParameters.printer = print?.printer || newVal[0];
        this.printParameters.barcodePrinters =
          print?.barcodePrinters || newVal[0];
      },
      immediate: true,
      deep: true
    }
  }
};
