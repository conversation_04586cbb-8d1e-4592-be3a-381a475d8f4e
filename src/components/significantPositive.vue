<template>
  <el-dialog
    :title="recordComb.combName"
    :visible.sync="dialogVisible"
    width="300px"
    :close-on-click-modal="false"
    :show-close="false"
    :close-on-press-escape="false"
  >
    <el-form ref="form" :model="form" label-width="80px" :rules="rules">
      <el-form-item label="重大类型" prop="positiveType">
        <el-radio-group
          @change="changePositiveType"
          v-model="form.positiveType"
          class="majorTypes-box"
        >
          <el-radio
            :label="item.value"
            v-for="(item, idx) in G_PositiveType"
            :key="idx"
            >{{ item.label }}</el-radio
          >
        </el-radio-group>
      </el-form-item>

      <el-form-item label="重大阳性" prop="positiveName">
        <el-select
          default-first-option
          :filter-method="filterMethod"
          :disabled="form.positiveType === ''"
          v-model="form.positiveName"
          allow-create
          filterable
          placeholder="请选择重大阳性"
        >
          <el-option
            v-for="item in significantPositivePullDown"
            :key="item.positiveCode"
            :label="item.positiveName"
            :value="item.positiveName"
          >
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialogVisible">取 消</el-button>
      <el-button type="primary" @click="submit" :disabled="determineDisabled"
        >确 定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  name: 'significantPositive',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
      required: true
    },
    recordComb: {
      type: Object,
      default: () => ({}),
      required: true
    },
    major: {
      type: Object,
      default: () => ({}),
      required: true
    }
  },
  data() {
    return {
      form: {
        regNo: '',
        positiveCode: '',
        positiveName: '',
        positiveType: '',
        combCode: ''
      },
      determineDisabled: false,
      rules: {
        positiveType: [
          { required: true, message: '请选择重大类型', trigger: 'change' }
        ],
        positiveName: [
          { required: true, message: '请选择重大阳性', trigger: 'change' }
        ]
      },
      significantPositivePullDown: [],
      significantPositivePullDownCopy: []
    };
  },
  watch: {
    major: {
      handler(val, oldVal) {
        this.determineDisabled = val?.id ? true : false;
        this.form.positiveType = val?.positiveType || '';
        this.form.positiveName = val?.positiveName || '';
        this.form.positiveCode = val?.positiveCode || '';
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    ...mapGetters(['G_PositiveType'])
  },
  methods: {
    filterMethod(val) {
      this.form.positiveName = val;
      this.significantPositivePullDown =
        this.significantPositivePullDownCopy.filter(
          (item) =>
            item.positiveName.includes(val) || item.positiveCode.includes(val)
        );
    },
    formReset() {
      this.form = {
        regNo: '',
        positiveCode: '',
        positiveName: '',
        positiveType: '',
        combCode: ''
      };
      this.determineDisabled = false;
    },
    changePositiveType(val) {
      this.form.positiveName = '';
      this.significantPositivePullDown =
        this.significantPositivePullDownCopy.filter(
          (item) => item.positiveType === val
        );
    },
    closeDialogVisible() {
      this.$emit('update:dialogVisible', false);
    },
    //获取重大阳性数据
    getSignificantPositiveData() {
      this.$ajax
        .post(this.$apiUrls.RD_MajorPositive + '/Read', [])
        .then((r) => {
          this.significantPositivePullDown = JSON.parse(
            JSON.stringify(r.data.returnData)
          );
          this.significantPositivePullDownCopy = JSON.parse(
            JSON.stringify(r.data.returnData)
          );
        });
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.form.regNo = this.recordComb.regNo;
          this.form.combCode = this.recordComb.combCode;
          this.form.positiveCode =
            this.significantPositivePullDown.find(
              (item) => item.positiveName === this.form.positiveName
            )?.positiveCode || '';
          this.$ajax
            .post(this.$apiUrls.ManualSaveMajorPositive, this.form)
            .then((r) => {
              const { success } = r.data;
              if (success) {
                this.$message.success('保存成功');
                this.closeDialogVisible();
                this.determineDisabled = true;
                this.$emit('successCallback', this.recordComb.combCode);
              }
            });
        } else {
          return false;
        }
      });
    }
  },
  created() {},
  mounted() {
    this.getSignificantPositiveData();
  }
};
</script>
<style lang="less" scoped>
.majorTypes-box {
  label {
    margin-right: 15px;
  }
}

/deep/ .el-dialog__title {
  display: inline-block;
  margin: 12px 0 0 0;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}
</style>
