<template>
  <el-dialog
    class="preview_popup"
    @open="openDialog"
    title="提示"
    width="100%"
    :show-close="false"
    :visible.sync="C_previewShow"
    :close-on-click-modal="false"
  >
    <header slot="title">
      <span>打印</span>
      <i class="el-icon-close" @click="cancelClick"></i>
    </header>
    <div class="print_body">
      <div class="tool_div">
        <!-- <div class="tool_wrap">
          <span>次数：</span>
          <div style="width: 100px">
            <el-input
              size="small"
              v-model="printSetInfo.PrintTimes"
              placeholder="请输入内容"
            ></el-input>
          </div>
        </div> -->
        <div class="tool_wrap">
          <span>打印机：</span>
          <div>
            <el-select
              size="small"
              v-model="printSetInfo.PrinterName"
              placeholder="请选择"
            >
              <el-option
                v-for="item in printList"
                :key="item"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <span style="width: 56px"></span>
        <!-- <el-button size="mini" type="primary" @click="ws.closeWebSocket()">关闭</el-button>
                <el-button size="mini" type="primary" @click="linkBtn">连接</el-button> -->
      </div>
      <div class="tool_div">
        <!-- <div class="tool_wrap">
          <span>次数：</span>
          <div style="width: 100px">
            <el-input
              size="small"
              v-model="printSetInfo.PrintTimes"
              placeholder="请输入内容"
            ></el-input>
          </div>
        </div> -->
        <div class="tool_wrap">
          <span>条码打印机：</span>
          <div>
            <el-select
              size="small"
              v-model="printSetInfo.barcodeName"
              placeholder="请选择"
            >
              <el-option
                v-for="item in printList"
                :key="item"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <el-button size="mini" type="primary" @click="print">打印</el-button>
        <!-- <el-button size="mini" type="primary" @click="ws.closeWebSocket()">关闭</el-button>
                <el-button size="mini" type="primary" @click="linkBtn">连接</el-button> -->
      </div>
      <div class="print_content">
        <el-menu
          default-active="0"
          class="el-menu-vertical-demo"
          style="width: 200px"
        >
          <el-menu-item
            @click="menuClick(item)"
            :index="idx + ''"
            v-for="(item, idx) in checkPrintTypeList"
            :key="idx"
          >
            <div>
              <el-checkbox
                class="checkBox_dom"
                v-model="item.checked"
                :disabled="item.disabled"
              ></el-checkbox>
              <span slot="title">{{ item.label }}</span>
            </div>
          </el-menu-item>
        </el-menu>

        <div v-loading="loading" style="width: 100%">
          <embed
            class="embed_dom"
            type="application/pdf"
            width="100%"
            :src="pdfSrc"
            height="100%"
          />
        </div>
      </div>
    </div>
    <div class="my_mask" v-if="batchMaskShow">
      <el-progress type="circle" :percentage="percentage"></el-progress>
      <p class="tips_p">{{ message }}</p>
    </div>
  </el-dialog>
</template>

<script>
import printMixins from './printMixins';
import { mapGetters } from 'vuex';
import { storage } from '../common';

export default {
  mixins: [printMixins],
  model: {
    prop: 'previewShow',
    event: 'previewShowChange'
  },
  props: {
    previewShow: {
      typeof: Boolean,
      default: false
    },
    dataInfo: {
      type: Object,
      default: {}
    },
    printerTypeList: {
      type: Array,
      default: () => {
        return [
          {
            printerType: 0, //打印机类型 0 普通打印机，1 条码打印机
            label: '指引单',
            checked: true
          }
        ];
      }
    },
    batchPrintList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    // 是否团体结算记录
    isBalanceRecord: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapGetters(['G_userInfo']),
    C_previewShow: {
      get() {
        if (this.previewShow) {
          this.connectPrint((r) => {
            let dataInfo = JSON.parse(r.data);
            let printDefault = storage.local.get('printDefault');
            this.printList = dataInfo.Data;
            this.printSetInfo.PrinterName = printDefault
              ? printDefault.general
                ? printDefault.general
                : dataInfo.Data[0]
              : dataInfo.Data[0];
            this.printSetInfo.barcodeName = printDefault
              ? printDefault.barcodeName
                ? printDefault.barcodeName
                : dataInfo.Data[0]
              : dataInfo.Data[0];
          });
        } else {
          if (JSON.stringify(this.ws) == '{}') return;
          this.ws.closeWebSocket && this.ws.closeWebSocket();
          this.ws.reConnect = () => {};
          this.ws.createWebSocket = () => {};
          this.ws.heartCheck.stop && this.ws.heartCheck.stop();
        }
        return this.previewShow;
      },
      set() {
        this.hideFun();
      }
    }
  },
  data() {
    return {
      printSetInfo: {
        PrintTimes: 1,
        PrinterName: '',
        barcodeName: '',
        barcodeTime: 1
      },
      printList: [],
      pdfSrc: '',
      fileStream: '', //文件流
      isPrint: false,
      // 批打印
      percentage: 0,
      message: '0/0',
      time: 300,
      fileStream: '',
      totalPages: 0,
      currentNum: 0,
      batchMaskShow: false,
      printObj: {
        指引单: 'Guidance',
        胃镜申请单: 'Gastroscope',
        肠镜申请单: 'Colonoscopy',
        报告: 'Report',
        体检标签: 'PeLabel',
        检验条码: 'Barcode',
        采血条码: 'BloodBarcode',
        非采血条码: 'NonBloodBarcode'
      },
      checkPrintTypeList: [],
      loading: false
    };
  },
  methods: {
    hideFun() {
      this.$emit('previewShowChange', false);
    },
    // 确定
    confirmClick() {
      this.hideFun();
    },
    // 取消
    cancelClick() {
      this.hideFun();
    },
    /**
     * <AUTHOR> justin
     * @description  : 获取预览pdf
     * @param         {*} item:
     * @return        {*}
     */
    async getPdfPreview(item) {
      this.pdfSrc = '';
      const notExistMsgFunc = () => {
        this.$message({
          message: '此类型报告暂无记录！',
          type: 'warning',
          showClose: true
        });
      };
      let isPreview = item && !item.disabled;
      if (!isPreview) return notExistMsgFunc();

      const printTypeCode = this.printObj[item.label];
      let reportCode = '';
      let queryString = '';
      if (item) {
        switch (item.printerType) {
          case 0:
            reportCode = this.dataInfo.reportType || this.dataInfo.guidanceType;
            break;
          case 1:
            reportCode = this.G_userInfo.systemParams.peLabelType;
            break;
          default:
            reportCode = this.dataInfo.reportType || this.dataInfo.guidanceType;
            break;
        }
        queryString = 'regNo=' + this.dataInfo.regNo + '&customizePrintTimes=0';
      }
      let datas = {
        reportCode: item ? reportCode : this.dataInfo.guidanceType,
        queryString:
          item && item.printerType == 1
            ? queryString
            : 'regNo=' + this.dataInfo.regNo
      };

      if (item?.type) {
        datas = {
          reportCode: '30232e1288c544aba755930cbd7a6029',
          queryString: 'regNo=' + this.dataInfo.regNo + '&type=' + item.type
        };
      }

      if (printTypeCode == 'Gastroscope') {
        datas = {
          reportCode: '2671f91f2e5a46dca739696557c19b19',
          queryString: 'regNo=' + this.dataInfo.regNo
        };
      }
      if (printTypeCode == 'Colonoscopy') {
        datas = {
          reportCode: '320b2821bd784e99ae9423b9d8e75d3d',
          queryString: 'regNo=' + this.dataInfo.regNo
        };
      }
      // 团体结算记录使用的参数
      if (this.isBalanceRecord) {
        datas = {
          reportCode: 'b3b3589831154f7eba4367d634258e03',
          queryString: `seqNo=${this.dataInfo.billSeqNo}`
        };
      }

      this.loading = true;
      this.$ajax
        .post(`${this.$config.pdfFileUrl}Home/ExportToPdf`, datas, {
          responseType: 'arraybuffer'
        })
        .then((r) => {
          const blob = new Blob([r.data], { type: 'application/pdf' });
          let reader = new FileReader();
          reader.onload = (e) => {
            let data;
            if (typeof e.target.result === 'object') {
              data = window.URL.createObjectURL(e.target.result);
            } else {
              data = e.target.result;
            }
            this.pdfSrc = data;
            this.fileStream = data;
            this.loading = false;
          };
          //转化为base64
          reader.readAsDataURL(blob);
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    print() {
      if (!this.checkPrintTypeList.some((x) => x.checked)) {
        this.$message({
          message: '请选择打印的类型',
          type: 'warning',
          showClose: true
        });
        return;
      }
      storage.local.set('printDefault', {
        general: this.printSetInfo.PrinterName,
        barcodeName: this.printSetInfo.barcodeName
      });
      this.currentNum = 0;
      // 批打印
      let batchPrintList = this.batchPrintList;

      if (batchPrintList && batchPrintList.length > 1) {
        this.batchMaskShow = true;
        this.batchPrint(batchPrintList);
        return;
      }
      // 单人打印
      let list = [this.dataInfo];
      this.batchPrint(list);
      return;
    },

    /**
     * @author: justin
     * @description: ws通知打印程序
     * @param {*} fileStream
     * @param {*} printerType
     * @return {*}
     */
    printStart(fileStream, printerType) {
      let fileStreams = fileStream.split(',')[1];
      let obj2 = {
        PrinterName:
          printerType === 0
            ? this.printSetInfo.PrinterName
            : this.printSetInfo.barcodeName,
        PrintTimes: 1,
        FileBase64: fileStreams,
        FileFormat: 'pdf'
      };
      let obj = {
        Action: 'print',
        Data: JSON.stringify(obj2)
      };
      var data = JSON.stringify(obj);
      this.ws.sendSock(data);
      this.isPrint = true;
    },

    /**
     * @author: justin
     * @description: 批打印
     * @param {*} list
     * @return {*}
     */
    batchPrint(list) {
      this.message = `${this.currentNum}/${list.length}`;
      this.percentage = Math.floor(
        (this.currentNum / list.length).toFixed(2) * 100
      );

      if (this.currentNum >= list.length) {
        setTimeout(() => {
          this.$message({
            message: '打印完成',
            type: 'success',
            showClose: true
          });
          this.batchMaskShow = false;
        }, this.time);
        return;
      }
      if (this.isBalanceRecord) {
        this.batchGetPDF(this.checkPrintTypeList[0], this.dataInfo);
        return;
      }

      // 按人头打印，同步打印
      const regInfo = list[this.currentNum];
      this.checkBarcodeAndPeLabel(regInfo).then(async (res) => {
        for (let index = 0; index < this.checkPrintTypeList.length; index++) {
          const item = this.checkPrintTypeList[index];
          if (!res.data.returnData || !res.data.returnData.includes(item.code))
            continue;

          await this.batchGetPDF(item, regInfo);
        }

        this.currentNum++;
        this.batchPrint(list);
      });
    },

    /**
     * @author: justin
     * @description: 下载pdf
     * @param {*} item
     * @param {*} dataInfo
     * @return {*}
     */
    async batchGetPDF(item, dataInfo) {
      return await new Promise((resolve, reject) => {
        if (!item.checked) return resolve(0);

        let reportCode = '';
        let queryString = '';

        if (item) {
          switch (item.printerType) {
            case 0:
              reportCode = dataInfo.reportType || dataInfo.guidanceType;
              break;
            case 1:
              reportCode = this.G_userInfo.systemParams.peLabelType;
              break;
            default:
              reportCode = dataInfo.reportType || dataInfo.guidanceType;
              break;
          }
          queryString = 'regNo=' + dataInfo.regNo + '&customizePrintTimes=0';
        }
        let datas = {
          reportCode: item ? reportCode : dataInfo.guidanceType,
          queryString:
            item && item.printerType == 1
              ? queryString
              : 'regNo=' + dataInfo.regNo
        };
        if (item?.type) {
          datas = {
            reportCode: '30232e1288c544aba755930cbd7a6029',
            queryString: 'regNo=' + dataInfo.regNo + '&type=' + item.type
          };
        }
        const printTypeCode = this.printObj[item.label];
        if (printTypeCode == 'Gastroscope') {
          datas = {
            reportCode: '2671f91f2e5a46dca739696557c19b19',
            queryString: 'regNo=' + dataInfo.regNo
          };
        }
        if (printTypeCode == 'Colonoscopy') {
          datas = {
            reportCode: '320b2821bd784e99ae9423b9d8e75d3d',
            queryString: 'regNo=' + dataInfo.regNo
          };
        }
        // 团体结算记录使用的参数
        if (this.isBalanceRecord) {
          datas = {
            reportCode: 'b3b3589831154f7eba4367d634258e03',
            queryString: `seqNo=${dataInfo.billSeqNo}`
          };
        }

        this.$ajax
          .post(`${this.$config.pdfFileUrl}Home/ExportToPdf`, datas, {
            responseType: 'arraybuffer'
          })
          .then((r) => {
            const blob = new Blob([r.data], { type: 'application/pdf' });
            let reader = new FileReader();

            reader.onload = (e) => {
              let data;
              if (typeof e.target.result === 'object') {
                data = window.URL.createObjectURL(e.target.result);
              } else {
                data = e.target.result;
              }
              // 调用ws通知打印程序
              this.printStart(data, item.printerType);
            };
            //转化为base64
            reader.readAsDataURL(blob);

            let file = new File([r.data], '', {
              type: 'application/pdf'
            });
            let fileReader = new FileReader();
            fileReader.readAsBinaryString(file);
            fileReader.onloadend = (e) => {
              var count = fileReader.result.match(
                /\/Type[\s]*\/Page[^s]/g
              ).length;

              this.totalPages = count;
              resolve(this.totalPages);
            };
          });
      });
    },

    /**
     * @author: justin
     * @description:
     * @return {*}
     */
    batchPrintFun() {
      let fileStream = this.fileStream.split(',')[1];
      let obj2 = {
        PrinterName: this.$parent.printSetInfo.PrinterName,
        PrintTimes: this.$parent.printSetInfo.PrintTimes,
        FileBase64: fileStream,
        FileFormat: 'pdf'
      };
      let obj = {
        Action: 'print',
        Data: JSON.stringify(obj2)
      };
      var data = JSON.stringify(obj);
      this.$parent.ws.sendSock(data);
      this.isPrint = true;
    },
    menuClick(item) {
      this.getPdfPreview(item);
    },
    // 查询有无体检标签打印
    checkBarcodeAndPeLabel(userInfo) {
      let datas = {
        regNo: userInfo.regNo,
        filterList: this.checkPrintTypeList.map((x) => x.code)
      };

      return this.$ajax.post(this.$apiUrls.CheckPrintables, datas);
    },

    /**
     * <AUTHOR> justin
     * @description  : 初始化打印类型列表
     * @return        {*}
     */
    initCheckPrintTypeList() {
      this.checkPrintTypeList = [];
      this.printerTypeList?.forEach((item) => {
        this.checkPrintTypeList.push({
          ...item,
          code: this.printObj[item.label],
          checked: item.checked,
          disabled: !item.checked
        });
      });
    },

    /**
     * <AUTHOR> justin
     * @description  :  dialog打开回调
     * @return        {*}
     */
    async openDialog() {
      const that = this;
      that.initCheckPrintTypeList();
      let defaultPreview = null;
      if (that.dataInfo.regNo) {
        // 个人打印类型控制
        if (!that.batchPrintList || that.batchPrintList.length <= 1) {
          await that.checkBarcodeAndPeLabel(that.dataInfo).then((res) => {
            for (
              let index = 0;
              index < that.checkPrintTypeList.length;
              index++
            ) {
              let item = that.checkPrintTypeList[index];
              if (
                !res.data.returnData ||
                !res.data.returnData.includes(item.code)
              ) {
                item.disabled = true;
                item.checked = false;
              } else {
                item.disabled = false;
                item.checked = true;
              }
              // 采血条码默认不勾
              if (item.code == 'BloodBarcode') item.checked = false;
            }
          });
        }

        defaultPreview = that.checkPrintTypeList.find((x) => x.checked);
      } else if (that.isBalanceRecord && that.checkPrintTypeList.length > 0) {
        // 团体结算
        defaultPreview = that.checkPrintTypeList[0];
      }

      that.getPdfPreview(defaultPreview);
    }
  },
  created() {},
  deactivated() {
    this.C_previewShow = false;
    this.batchMaskShow = false;
  }
};
</script>
<style lang="less" scoped>
.preview_popup {
  /deep/ .el-dialog {
    height: 100% !important;
    margin: 0 !important;
    display: flex;
    flex-direction: column;
  }

  /deep/ .el-dialog__body {
    flex: 1;
    flex-shrink: 0;
    overflow: hidden;
    padding: 0;
  }

  /deep/ .el-dialog__header {
    padding: 0;
  }

  header {
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    i {
      cursor: pointer;
      font-size: 28px;
    }
  }

  .tool_div {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 5px;

    .tool_wrap {
      display: flex;
      align-items: center;
      margin-right: 20px;

      > span {
        font-size: 14px;
        line-height: 14px;
        width: 85px;
      }
    }
  }

  .print_body {
    height: 100%;
    display: flex;
    flex-direction: column;

    .print_content {
      flex: 1;
      flex-shrink: 0;
      display: flex;
      .embed_dom {
        flex: 1;
        flex-shrink: 0;
        background: #000;
      }
      .checkBox_dom {
        margin-top: -5px;
        margin-right: 10px;
      }
    }
  }
}
</style>
