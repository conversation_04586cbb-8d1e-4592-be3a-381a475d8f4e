/**
 * @FilePath: \KrPeis\src\components\pushMixinsAct.js
 * @Description: 打印相关混入
 * @Author:
 * @Date:
 * @Version: 0.0.1
 * @LastEditors: justin
 * @LastEditTime: 2024-06-18 10:26:59
 
 */
import WS from '@/common/websocket';
import Vue from 'vue';

export default {
  data() {
    return {};
  },
  created() {
    // this.$sample_ws = {}; // 放在此处，取消响应式（被用在computed时，会发生循环响应）
  },
  methods: {
    connectPrint(websocketonmessage) {
      // this.$sample_ws.reConnect = () => {};
      // this.$sample_ws.createWebSocket = () => {};
      // this.$sample_ws = {};
      console.log(this.$sample_ws);
      if (
        !this.$sample_ws ||
        !this.$sample_ws?.websock ||
        this.$sample_ws?.websock?.readyState === WebSocket.CLOSED
      ) {
        this.$sample_ws?.closeWebSocket();
        Vue.prototype.$sample_ws = new WS(`${this.$config.printUrl}sample`);
      }
      //   this.$sample_ws.onerror = (r) => {
      //     // this.$message({
      //     //     message: '连接失败，请先打开本地打印机程序！',
      //     //     type: 'error',
      //     //     showClose:true,
      //     //     duration:5000
      //     // });
      //   };
    }
  },
  // 组件激活
  activated() {
    this.connectPrint();
  },
  // 组件失活
  // deactivated() {
  //   if (JSON.stringify(this.$sample_ws) == "{}") return;
  //   this.$sample_ws.closeWebSocket();
  // },
  // 销毁
  beforeDestroy() {
    // if (JSON.stringify(this.$sample_ws) == "{}") return;
    // this.$sample_ws?.closeWebSocket();
  }
};
