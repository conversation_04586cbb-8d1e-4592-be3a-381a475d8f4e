/**
 * @FilePath: \KrPeis\src\components\pushMixins.js
 * @Description: 打印相关混入
 * @Author:
 * @Date:
 * @Version: 0.0.1
 * @LastEditors: justin
 * @LastEditTime: 2024-06-18 10:26:00
 */

import WS from '@/common/websocket';

export default {
  data() {
    return {};
  },
  created() {
    this.ws = {}; // 放在此处，取消响应式（被用在computed时，会发生循环响应）
  },
  methods: {
    connectPrint(websocketonmessage) {
      this.ws.reConnect = () => {};
      this.ws.createWebSocket = () => {};
      this.ws = {};

      this.ws = new WS(`${this.$config.printUrl}sample`);
      this.ws.onerror = (r) => {
        // this.$message({
        //     message: '连接失败，请先打开本地打印机程序！',
        //     type: 'error',
        //     showClose:true,
        //     duration:5000
        // });
      };
    }
  },
  // 组件激活
  activated() {
    this.connectInit();
  },
  // 组件失活
  deactivated() {
    if (JSON.stringify(this.ws) == '{}') return;
    this.ws.closeWebSocket();
  },
  // 销毁
  beforeDestroy() {
    if (JSON.stringify(this.ws) == '{}') return;
    this.ws.closeWebSocket();
  }
};
