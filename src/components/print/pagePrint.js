import pdf from 'vue-pdf';
export default {
  components: {
    pdf
  },
  data() {
    return {
      pagePrintShow: 1,
      pageTotalNum: [], // 总页数
      checkPageNum: [],
      previewBuffer: new ArrayBuffer(), // 预览的pdf  buffer
      printModel: [
        {
          label: '全部',
          value: 1
        },
        {
          label: '单页',
          value: 2
        }
      ],
      activePage: 0,
      scrollFlag: true
    };
  },
  methods: {
    //获取pdf页码
    async getNumPages(url) {
      const pdf = await pdf.createLoadingTask(url);
      this.pageTotalNum = Array.from({ length: pdf.numPages }, (_, i) => ({
        pageNum: i + 1,
        checked: false
      }));
    },
    // 选择页码的回调
    checkPageNumChange(row) {
      this.anchorPointClick('#pageNum_' + row.pageNum, row.pageNum);
      if (row.checked) {
        this.checkPageNum.push(row.pageNum);
      } else {
        let idx = this.checkPageNum.indexOf(row.pageNum);
        if (idx != -1) {
          this.checkPageNum.splice(idx, 1);
        }
      }
      console.log(this.checkPageNum);
    },
    // 删除已选页码的回调
    tagClose(row, index) {
      this.checkPageNum.splice(index, 1);
      this.pageTotalNum.some((item) => {
        if (item.pageNum == row) {
          item.checked = false;
          return true;
        }
      });
    },
    // 获取已选页码的PDF, 并打印
    getCheckPagePdf(printFlag, reportPagePrintFlag = false) {
      if (this.checkPageNum.length == 0 && this.activePage === 0) {
        this.$message({
          message: '请先选择需要打印的页码！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let checkPageNum = [];
      if (this.checkPageNum.length !== 0) {
        checkPageNum = this.checkPageNum;
      } else {
        checkPageNum.push(this.activePage);
      }
      console.log(checkPageNum);
      const blob = new Blob([this.previewBuffer], { type: 'application/pdf' });
      const files = new File([blob], 'test.pdf', { type: 'application/pdf' });
      const formData = new FormData();
      formData.append('files', files);
      this.$ajax
        .post(this.$apiUrls.GetSplitPdfByPages, formData, {
          query: {
            pages: JSON.stringify(checkPageNum)
          },
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: ''
          },
          responseType: 'arraybuffer'
        })
        .then((r) => {
          let printData = this.setPrinterType(
            this.printParameters.printableList
          );
          const temp = printData.find(
            (item) => item.type === this.printParameters.selectedPrint
          );

          let pageBlob = new Blob([r.data], { type: 'application/pdf' });
          if (printFlag) {
            this.pagePrint(pageBlob, temp, reportPagePrintFlag);
          } else {
            this.pageExport(pageBlob, temp, reportPagePrintFlag);
          }
        });
    },
    // 分页打印
    pagePrint(pageBlob, temp, reportPagePrintFlag) {
      const reader = new FileReader();
      // 定义读取完成后的处理函数
      let that = this;
      reader.onload = function (event) {
        // 当读取操作完成时，event.target.result将包含Base64编码的字符串
        const base64String = event.target.result;
        console.log(base64String);
        that.startPrinting(
          base64String,
          temp?.printerType,
          reportPagePrintFlag
        );
      };

      // 使用readAsDataURL方法读取Blob对象
      reader.readAsDataURL(pageBlob);
    },
    // 分页导出
    pageExport(pageBlob, temp, reportPagePrintFlag) {
      const link = document.createElement('a');
      link.href = URL.createObjectURL(pageBlob);
      if (reportPagePrintFlag) {
        link.download = `${this.selectRow.name + this.selectRow.regNo}报告.pdf`;
      } else {
        link.download = `${this.printParameters.regNo}-${temp.typeName}.pdf`;
      }

      link.click();
    },
    // 锚点定位
    anchorPointClick(id, pageNum) {
      this.scrollFlag = false;
      let dom = document.querySelectorAll(id);
      console.log(dom);
      dom[0].scrollIntoView();
      this.activePage = pageNum;
    },
    //滚动
    handleScroll() {
      if (!this.scrollFlag) return;
      const scrollPercentage =
        (this.$refs.pdfWrap.scrollTop + 10) /
        (this.$refs.pdfWrap.scrollHeight / this.pageTotalNum.length);
      this.activePage = Math.ceil(scrollPercentage);
      const scrollHeight1 = this.$refs.pdfNav.scrollHeight;
      this.$refs.pdfNav.scrollTop =
        (this.activePage - 1.5) * (scrollHeight1 / this.pageTotalNum.length);
    }
  },
  directives: {
    scroll: {
      inserted(el, binding) {
        // 为元素添加滚动事件监听
        el.addEventListener('scroll', () => {
          // 判断用户滚动的距离
          if (el.scrollTop > 0) {
            binding.value(); // 调用绑定的方法
          }
        });
      }
    },
    removeAriaHidden: {
      bind(el, binding) {
        let ariaEls = el.querySelectorAll('.el-radio__original');
        ariaEls.forEach((item) => {
          item.removeAttribute('aria-hidden');
        });
      }
    }
  }
};
