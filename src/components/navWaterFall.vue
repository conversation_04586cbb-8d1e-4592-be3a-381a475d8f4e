<template>
  <div>
    <div class="box">
      <ul v-for="(item, index) in dataMenus" :key="index" class="boxItem">
        <li>
          <b> {{ item.title }} </b>
        </li>
        <li class="navLi" v-for="(val, idx) in item.children" :key="idx">
          <span @click="navClick(val.name)">{{ val.title }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
var items = document.getElementsByClassName('boxItem');
var gap = 20;
export default {
  props: {
    dataMenus: {
      type: Array,
      require: false,
      default: []
    }
  },
  data() {
    return {
      windowHeight: document.body.clientHeight,
      windowWidth: document.body.clientWidth
    };
  },
  watch: {
    // 监听页面高度
    windowHeight(val) {
      //console.log("实时屏幕高度：", val, this.windowHeight);
      this.waterFall();
    },
    // 监听页面宽度
    windowWidth(val) {
      //console.log("实时屏幕宽度：", val, this.windowHeight);
      this.waterFall();
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.waterFall();
    });
    window.onresize = () => {
      return (() => {
        this.windowHeight = document.documentElement.clientHeight; // 高
        this.windowWidth = document.documentElement.clientWidth; // 宽
      })();
    };
  },

  methods: {
    navClick(name) {
      this.$parent.$parent.navClick(name);
    },
    //流式布局计算高度
    waterFall() {
      //没有内容不用计算
      if (items.length < 1) {
        return;
      }
      // 1.确定1行有多少列  1)获取到页面的宽度 2)获取到每个盒子的宽度 3)需要显示的列数=页面宽度/盒子宽度
      // 显示的列数(column)=页面宽度(pageWidth)/(盒子宽度(itemWidth)+间隙(gap))
      var pageWidth = document.querySelector('.box').clientWidth;
      var itemWidth = items[0].offsetWidth;
      var columns = parseInt(pageWidth / (itemWidth + gap));
      // console.log('pageWidth', pageWidth, itemWidth, columns)
      // 定义一个数组arr来保存高度
      var arr = [];
      for (let i = 0; i < items.length; i++) {
        // 排列第一行
        if (i < columns) {
          // 满足条件则在第一行
          items[i].style.top = 0;
          items[i].style.left = (itemWidth + gap) * i + 'px';
          arr.push(items[i].offsetHeight);
        } else {
          // 排列第二行
          var minHeight = arr[0];
          var index = 0;
          // 找最小高度
          for (var j = 0; j < arr.length; j++) {
            if (minHeight > arr[j]) {
              minHeight = arr[j];
              index = j;
            }
          }
          // 设置下一行的第一个盒子的位置
          items[i].style.top = arr[index] + gap + 'px';
          items[i].style.left = items[index].offsetLeft + 'px';
          arr[index] = arr[index] + items[i].offsetHeight + gap;
        }
      }
    }
  }
};
</script>
<style lang="less" scoped>
ul,
li {
  padding: 0;
  margin: 0;
  list-style: none;
  line-height: 28px;
}
.box {
  box-sizing: border-box;
  overflow: auto;
  position: relative;
  flex: 1;
  overflow: auto;
  .boxItem {
    min-width: 200px;
    position: absolute;
    li {
      box-sizing: border-box;
      font-size: 18px;
    }
    li:hover {
      background: none !important;
    }
    li:not(:first-child):hover {
      color: #5c73c8;
      cursor: pointer;
    }
    .navLi {
      color: #1770df;
      font-size: 16px;
    }
  }
}
</style>
