/**
 * @FilePath: \KrPeis\src\components\IdCardMixins.js
 * @Description:  读取身份证卡的混入
 * @Author:
 * @Date:
 * @Version: 0.0.1
 * @LastEditors: justin
 * @LastEditTime: 2024-06-18 11:24:27
 
 */
import WS from '@/common/websocket';

export default {
  data() {
    return {
      isConnectInit: false
    };
  },
  created() {
    this.idcardWS = {}; // 放在此处，取消响应式（被用在computed时，会发生循环响应）
  },
  methods: {
    connectIdcard(websocketonmessage) {
      this.idcardWS.reConnect = () => {};
      this.idcardWS.createWebSocket = () => {};
      this.idcardWS = {};

      this.$nextTick(() => {
        this.idcardWS = new WS(`${this.$config.printUrl}idCardReader`);
        let hintFlag = true;
        this.idcardWS.onerror = (r) => {
          // if(hintFlag){
          //     this.$message({
          //         message: '连接身份证读卡器失败，请先打开本地应用程序！',
          //         type: 'error',
          //         showClose: true,
          //         duration: 5000
          //     });
          // }
          hintFlag = false;
        };
        this.idcardWS.websocketonmessage = websocketonmessage;
      });
    },

    /**
     * @author: justin
     * @description: 连接身份证读卡器
     * @return {*}
     **/
    createConnectWS() {
      setTimeout(() => {
        if (this.isConnectInit) return;
        window.$wsHub.removeConnected(`${this.$config.printUrl}idCardReader`);
        this.connectInit();
        this.isConnectInit = true;
      }, 50);
    },
    /**
     * @author: justin
     * @description: 关闭连接
     * @return {*}
     **/
    closeConnectWS() {
      if (JSON.stringify(this.idcardWS) == '{}') return;
      this.idcardWS.closeWebSocket();
      this.isConnectInit = false;
    }
  },
  // 组件激活
  activated() {
    this.createConnectWS();
  },
  mounted() {
    this.createConnectWS();
  },
  // 组件失活
  deactivated() {
    this.closeConnectWS();
  },
  // 销毁
  beforeDestroy() {
    this.closeConnectWS();
  }
};
