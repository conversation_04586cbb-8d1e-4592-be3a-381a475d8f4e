<template>
  <div class="input_wrap">
    <label
      :class="{ abnormal_result: item.abnormalType > 0 }"
      v-for="(item, idx) in labelList"
      :key="item.id"
    >
      <span
        contenteditable="true"
        :ref="'inputSpan_Ref_' + itemCode + '_' + idx"
        @keypress.enter.prevent="inputSpanEnter(idx)"
        @blur="resultSpanBlur(item, idx)"
        @input="resultSpanInput"
        >{{ item.tag }}</span
      >
      <i
        class="el-icon-error"
        title="删除结果"
        @click.stop="labelDel(idx, item)"
      ></i>
      <i
        class="el-icon-success"
        title="生成小结"
        @click.stop="createSummary(item)"
      ></i>
    </label>
    <div
      class="input_div"
      contenteditable="true"
      ref="input_Ref"
      v-html="inputVal"
      @keydown="inputKeydown"
      @input="inputChange"
      @blur="inputBlur"
    ></div>
  </div>
</template>

<script>
export default {
  props: {
    labelList: {
      type: Array,
      default: () => {
        return [
          {
            id: 1,
            label: '测试标签'
          }
        ];
      }
    },
    itemCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      inputVal: ''
    };
  },
  methods: {
    wrapClick() {
      this.$refs.input_Ref.focus();
    },
    inputHandle() {
      this.$refs.input_Ref.blur();
    },
    inputChange(e) {
      // console.log(this.$refs.input_Ref);
      this.$emit('inputChange');
    },
    inputBlur() {
      console.log(88888999);
      let inputText = this.$refs.input_Ref.innerText;
      console.log(inputText);
      this.$emit('inputBlur', inputText);
      this.$refs.input_Ref.innerHTML = '';
    },
    inputKeydown(e) {
      let inputText = this.$refs.input_Ref.innerText;
      switch (e.keyCode) {
        case 8:
          if (inputText.length == 0) {
            let tag = this.labelList[this.labelList.length - 1];
            if (tag) {
              this.$emit('labelDel', tag);
            }
            this.labelList.splice(-1, 1);
          }
          break;
        case 13:
          e.preventDefault();
          // if(inputText.trim()==='')return;
          this.$emit('inputComEnter', inputText);
          // this.labelList.push({
          //     id:this.labelList.length+1,
          //     label:inputText
          // });
          this.$refs.input_Ref.innerHTML = '';
          break;
        default:
          break;
      }
      this.$nextTick(() => {
        this.$emit('inputChange');
      });
    },
    // 删除小标签
    labelDel(idx, tag) {
      this.labelList.splice(idx, 1);
      this.$emit('labelDel', tag);
      this.$emit('inputChange');
    },
    // 生成小结
    createSummary(tag) {
      this.$emit('createSummary', tag);
    },
    // 编辑结果
    resultSpanBlur(tag, idx) {
      tag.tag =
        this.$refs['inputSpan_Ref_' + this.itemCode + '_' + idx][0].innerText;
      if (tag.tag.trim() === '') {
        this.labelList.splice(idx, 1);
        this.$emit('labelDel', tag);
        return;
      }
      this.$emit('resultSpanBlur', tag);
    },
    // 编辑结果的实时输入回调
    resultSpanInput() {
      this.$emit('inputChange');
    },
    // 编辑结果的回车回调
    inputSpanEnter(idx) {
      this.$refs['inputSpan_Ref_' + this.itemCode + '_' + idx][0].blur();
    }
  }
};
</script>

<style lang="less" scoped>
.input_wrap {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  // border: 2px solid #1abc9c;
  // padding: 2px;
  label {
    padding: 2px 3px;
    background: #ecf5ff;
    border: 1px solid #d9ecff;
    color: #409eff;
    display: inline-block;
    margin-bottom: 3px;
    margin-right: 3px;
    word-break: break-all;
    position: relative;
    min-height: 25px;
    line-height: 21px;
    vertical-align: middle;
    border-radius: 4px;
    i {
      position: absolute;
      top: -5px;
      right: -5px;
      color: #f56c6c;
      cursor: pointer;
      display: none;
      &:last-child {
        top: 15px;
      }
    }
    &:hover i {
      display: block;
    }
    span {
      display: inline-block;
      word-break: break-all;
      &:focus-visible {
        outline: none;
      }
    }
    // &:hover span{
    //     padding-right: 10px;
    // }
  }
  .abnormal_result {
    background: #d54343 !important;
    color: #fff !important;
  }
  .input_div {
    display: inline-block;
    min-width: 50px;
    padding: 2px;
    word-break: break-all;
    flex: 1;
    flex-shrink: 0;
    &:focus-visible {
      outline: none;
    }
  }
}
</style>
