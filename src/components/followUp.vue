<template>
  <el-dialog
    title="添加随访"
    :visible.sync="dialogVisible"
    width="50%"
    :close-on-click-modal="false"
    @close="followUpClose"
    @open="followUpOpen"
  >
    <el-form ref="form_Ref" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="随访内容" prop="followUpData">
        <el-input
          type="textarea"
          :autosize="{ minRows: 4, maxRows: 8 }"
          placeholder="请输入随访内容"
          v-model="form.followUpData"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="登记人" prop="region">
        <el-select
          v-model="form.recorder"
          size="small"
          filterable
          allow-create
          default-first-option
          placeholder="请选择"
        >
          <el-option
            v-for="item in G_sysOperator"
            :key="item.index"
            :label="item.label"
            :value="item.label"
          >
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="confirmFun">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { mapGetters } from 'vuex';
export default {
  props: {
    followUpShow: {
      type: Boolean,
      default: () => {
        return false;
      }
    },
    regNo: {
      type: String,
      default: ''
    }
  },
  computed: {
    ...mapGetters(['G_sysOperator', 'G_userInfo'])
  },
  data() {
    return {
      dialogVisible: this.followUpShow,
      form: {
        followUpData: '',
        recorder: ''
      },
      rules: {
        followUpData: [
          { required: true, message: '请输入随访内容', trigger: 'blur' }
        ]
      }
    };
  },
  methods: {
    followUpClose() {
      this.$emit('followUpClose', false);
    },
    followUpOpen() {
      this.form.recorder = this.G_userInfo.codeOper.name;
    },
    // 确定添加随访
    confirmFun() {
      this.$refs['form_Ref'].validate((valid) => {
        if (valid) {
          this.$ajax
            .post(this.$apiUrls.ManualInsertPeFollowUp, {
              regNo: this.regNo,
              followUpData: this.form.followUpData,
              recorder: this.form.recorder
            })
            .then((r) => {
              let { success } = r.data;
              this.dialogVisible = false;
              if (!success) return;
              this.$message({
                message: '添加随访成功！',
                type: 'success',
                showClose: true
              });
              this.$parent.getFollowUpState();
            });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    resetForm() {
      this.$refs['form_Ref'].resetFields();
    }
  },
  watch: {
    followUpShow() {
      this.dialogVisible = this.followUpShow;
      this.$nextTick(() => {
        this.resetForm();
      });
    }
  }
};
</script>
<style lang="less" scoped>
.majorTypes-box {
  label {
    margin-right: 15px;
  }
}
</style>
