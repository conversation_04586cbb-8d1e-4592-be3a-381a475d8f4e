<!--
 * @FilePath: \shenshan\KrPeis\src\components\registerForThePreviewV2.vue
 * @Description: 
 * @Version: 2.0
 * @Author: key
 * @Date: 2024-07-09 16:58:49
 * @LastEditors: key
 * @LastEditTime: 2024-11-07 15:40:52
-->
<template>
  <el-dialog
    title="预览"
    :fullscreen="true"
    :close-on-click-modal="false"
    destroy-on-close
    :before-close="
      () => {
        $emit('update:displaySwitches', false);
      }
    "
    :visible.sync="displaySwitches"
    v-loading="printParameters.progressData.loading"
    :element-loading-text="`正在打印中：${printParameters.progressData.progress}`"
  >
    <div class="main" v-loading="this.printParameters.loading">
      <div class="preview" v-loading="loading">
        <embed
          class="embed_dom"
          type="application/pdf"
          width="100%"
          :src="pdfData"
          height="100%"
          v-if="pdfData && pagePrintShow == 1"
        />
        <!-- 分页打印 -->
        <div class="page_print" v-else-if="pdfData && pagePrintShow == 2">
          <!-- 页码 -->
          <ul class="pageNum_wrap" ref="pdfNav">
            <li
              v-for="i in pageTotalNum"
              :key="i.pageNum"
              :class="{ active_page: activePage == i.pageNum }"
            >
              <el-checkbox
                @change="checkPageNumChange(i)"
                v-model="i.checked"
                class="page_checkbox"
                size="medium"
                removeAriaHidden
              ></el-checkbox>
              <a @click="anchorPointClick('#pageNum_' + i.pageNum, i.pageNum)">
                <div class="pdf_wrap">
                  <pdf
                    :ref="'myPdfComponent_' + i.pageNum"
                    :src="pdfData"
                    :page="i.pageNum"
                  ></pdf>
                </div>
                <div class="pageNum">第 {{ i.pageNum }} 页</div>
              </a>
            </li>
          </ul>
          <!-- PDF显示区域 -->
          <ul
            class="preview_pdf"
            ref="pdfWrap"
            v-scroll="handleScroll"
            @mouseover="scrollFlag = true"
          >
            <li
              v-for="i in pageTotalNum"
              :key="i.pageNum"
              :id="'pageNum_' + i.pageNum"
            >
              <pdf
                :ref="'pdf_' + i.pageNum"
                :src="pdfData"
                :page="i.pageNum"
              ></pdf>
            </li>
          </ul>
        </div>
        <el-empty v-else description="暂无数据" style="height: 100%"></el-empty>
      </div>
      <div class="actionsOnTheRight">
        <div>
          <p>打印机</p>
          <el-select v-model="printParameters.printer" placeholder="请选择">
            <el-option
              v-for="item in G_printerList"
              :key="item"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
        </div>
        <div>
          <p>条码打印机</p>
          <el-select
            v-model="printParameters.barcodePrinters"
            placeholder="请选择"
          >
            <el-option
              v-for="item in G_printerList"
              :key="item"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
        </div>

        <div class="group">
          <el-radio-group
            size="small"
            v-model="printParameters.selectedPrint"
            v-for="item in printParameters.printType"
            :key="item.type"
          >
            <el-radio :disabled="item.disabled" :label="item.type">{{
              item.typeName
            }}</el-radio>
          </el-radio-group>
        </div>
        <div>
          <p style="margin-bottom: 20px">打印模式</p>
          <el-radio-group
            v-model="pagePrintShow"
            size="small"
            style="display: flex; flex-direction: column; gap: 10px"
          >
            <el-radio :label="i.value" v-for="i in printModel" :key="i.value">{{
              i.label
            }}</el-radio>
          </el-radio-group>
        </div>
        <div style="display: flex; justify-content: flex-end; margin-top: 50px">
          <el-button
            v-if="pagePrintShow == 1"
            type="primary"
            @click="exportPdf"
            :disabled="loading"
            size="small"
            >下载</el-button
          >
          <el-button
            type="primary"
            v-if="pagePrintShow == 1"
            style="margin-left: 15px"
            @click="print(true, callback)"
            :disabled="loading"
            size="small"
            >打印</el-button
          >
          <el-button
            v-if="pagePrintShow == 2"
            type="primary"
            @click="getCheckPagePdf(false)"
            :disabled="loading"
            size="small"
            >下载</el-button
          >
          <el-button
            type="primary"
            style="margin-left: 15px"
            @click="getCheckPagePdf(true)"
            :disabled="loading"
            v-if="pagePrintShow == 2"
            size="small"
            >打印</el-button
          >
        </div>
        <div class="check_wrap" v-if="pagePrintShow == 2">
          <label>已选页码：</label>
          <el-tag
            class="check_tag"
            @close="tagClose(item, idx)"
            size="medium"
            closable
            v-for="(item, idx) in checkPageNum"
            :key="item"
            >第{{ item }}页</el-tag
          >
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import newPrintMixins from './newPrintMixins';
import pagePrintJS from './print/pagePrint';

export default {
  mixins: [newPrintMixins, pagePrintJS],
  props: {
    //打印回调
    callback: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      pdfSrc: '',
      pdfData: '',
      loading: false,
      cacheKeyList: []
    };
  },
  methods: {},
  created() {
    this.ws = {};
  },
  watch: {
    displaySwitches: {
      handler(newVal) {
        // console.log(newVal);
        if (!newVal) {
          this.printParameters.selectedPrint = '';
          this.pdfSrc = '';
          this.pdfData = '';
          this.checkPageNum = [];
          this.pagePrintShow = 1;
          this.activePage = 0;
          this.pageTotalNum = [];
        } else {
          this.setPrinterData();
        }
      }
    },
    'printParameters.selectedPrint': {
      async handler(newVal) {
        {
          this.checkPageNum = [];
          this.activePage = 0;
          this.$refs.pdfNav?.scrollTo(0, 0);
          this.$refs.pdfWrap?.scrollTo(0, 0);
          const temp = this.printParameters.printableList
            ?.flat()
            .find((item) => item.type == newVal);
          if (!temp) return;
          this.loading = true;
          try {
            // const res = await this.retrieveFileStream(temp.pdfUrl,true);
            this.pageTotalNum = [];
            this.retrieveFileStream(temp.pdfUrl, true).then((res) => {
              this.pdfSrc = res.pdfSrc;
              this.pdfData = res.pdfDate;
              this.loading = false;
              this.getNumPages(this.pdfData);
            });
          } catch (_) {
            this.loading = false;
          }
        }
      }
    },
    'printParameters.printType': {
      immediate: true,
      handler(newVal) {
        //设置默认勾选
        this.printParameters.selectedPrint =
          newVal.find((item) => item.disabled === false)?.type || '';
      }
    }
  }
};
</script>
<style lang="less" scoped>
/deep/.el-dialog__body {
  height: calc(100% - 54px) !important;
  padding-top: 15px;
}
/deep/ .el-dialog {
  padding: 0 !important;
  width: 100% !important;
  height: 100% !important;
}
.main {
  display: flex;
  height: 100%;
  .preview {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    .page_print {
      height: 100%;
      overflow: auto;
      display: flex;
      .pageNum_wrap {
        width: 250px;
        height: 100%;
        overflow: auto;
        li {
          text-align: center;
          border-radius: 5px;
          background: #ccc;
          padding: 10px;
          margin-bottom: 10px;
          position: relative;
          border: 3px solid #fff;
          .pageNum {
            padding-top: 5px;
          }
          .page_checkbox {
            position: absolute;
            top: 0;
            left: 0;
            /deep/ .el-checkbox__inner {
              height: 20px;
              width: 20px;
              &::after {
                top: 3px;
                left: 7px;
              }
            }
          }
        }
        .active_page {
          border-color: #1770df;
        }
      }
      .preview_pdf {
        flex: 1;
        flex-shrink: 0;
        overflow: auto;
        background: #000;
        padding: 10px;
        border-radius: 5px;
        li {
          width: 80%;
          margin: 0 auto;
          margin-bottom: 20px;
          min-width: 595px;
        }
      }
    }
  }
  .actionsOnTheRight {
    width: 290px;
    box-sizing: border-box;
    padding: 0 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    .group {
      display: flex;
      flex-direction: column;
      //   gap: 15px;
      /deep/ .el-radio__input {
        & > span:nth-child(1) {
          display: none;
        }
      }
      /deep/ .el-radio {
        width: 100%;
        padding: 10px 0;
      }
      /deep/ .el-radio:hover {
        background: #40a0ff42;
      }
      /deep/.el-radio__label {
        padding: 0 !important;
        width: 100%;
        display: inline-block;
      }
    }
  }
  .check_wrap {
    .check_tag {
      margin-right: 5px;
      margin-bottom: 5px;
    }
  }
}
</style>
