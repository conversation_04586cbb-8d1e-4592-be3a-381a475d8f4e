<template>
  <div class="publicTable_com">
    <el-table
      ref="tableCom_Ref"
      class="table_dom"
      :border="border"
      v-loading="tableLoading"
      :show-header="showHeader"
      style="width: 100%; color: #2d3436; font-size: 14px"
      size="small"
      :stripe="isStripe"
      :data="tableData"
      :row-key="rowKey"
      @row-dblclick="rowDblclick"
      @row-click="rowClick"
      @cell-click="cellClick"
      v-el-table-infinite-scroll="load"
      :cell-class-name="cellClassName"
      :row-class-name="tableRowClassName"
      :header-cell-style="headerCellStyle"
      height="100%"
      :highlight-current-row="highlightCurrentRow"
      :default-expand-all="expandAll"
      :cell-style="tableCellClassName"
      :row-style="rowStyle"
      :show-summary="showSummary"
      :summary-method="summaryMethod"
      @current-change="handleCurrentChange"
      @selection-change="handleSelectionChange"
      @select-all="selectAll"
      @select="select"
    >
      <el-table-column
        type="selection"
        width="50"
        :selectable="selectable"
        :reserve-selection="reserveSelection"
        v-if="isCheck"
      ></el-table-column>
      <el-table-column
        type="index"
        v-if="isSortShow"
        :width="isOnlySortShow ? '40' : '60'"
        label="序号"
      ></el-table-column>
      <slot name="columnLeft"></slot>

      <el-table-column
        :prop="th"
        :label="theads[th]"
        v-for="th in Object.keys(theads)"
        :key="th"
        :align="columnAlign[th]"
        :width="columnWidth[th]"
        :min-width="columnMinWidth[th]"
        :sortable="columnSort.includes(th)"
        :show-overflow-tooltip="showOverflowTooltip"
      >
        <template #header="scope">
          <slot :name="th + 'Header'" :scope="scope">{{ theads[th] }}</slot>
        </template>
        <template slot-scope="scope">
          <slot :name="th" :scope="scope">{{ scope.row[th] }}</slot>
        </template>
      </el-table-column>
      <slot name="columnRight"></slot>
    </el-table>
  </div>
</template>

<script>
import { dataUtils } from '../common';
export default {
  name: 'publicTable',
  model: {
    prop: 'checkSelection',
    event: 'emptySelection'
  },
  props: {
    rowStyle: {
      type: Function,
      default: () => {
        return () => {};
      }
    },
    // 自定义v-model
    checkSelection: {
      type: Array,
      default: () => {
        return [];
      }
    },
    // 排序
    columnSort: {
      type: Array,
      default: () => {
        return [];
      }
    },
    // 表头数据
    theads: {
      type: Object,
      default: () => {
        return {};
      }
    },
    // 列宽
    columnWidth: {
      type: Object,
      default: () => {
        return {};
      }
    },
    // 最小列宽
    columnMinWidth: {
      type: Object,
      default: () => {
        return {};
      }
    },
    // 表格数据
    viewTableList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    // 是否可选
    isCheck: {
      type: Boolean,
      default: () => {
        return false;
      }
    },
    // 是否开启分页
    isOpenPage: {
      type: Boolean,
      default: () => {
        return true;
      }
    },
    // 是否显示序号
    isSortShow: {
      type: Boolean,
      default: () => {
        return true;
      }
    },
    //是否单独设立一个序号宽度
    isOnlySortShow: {
      type: Boolean,
      default: () => {
        return false;
      }
    },
    //是否合计
    showSummary: {
      type: Boolean,
      default: () => {
        return false;
      }
    },
    summaryMethod: {
      type: Function,
      default: undefined
    },
    // 加载动画
    tableLoading: {
      type: Boolean,
      default: () => {
        return false;
      }
    },
    // 字体蓝色
    cell_blue: {
      type: Array,
      default: () => {
        return [];
      }
    },
    // 字体红色
    cell_red: {
      type: Array,
      default: () => {
        return [];
      }
    },
    // 字体黄色
    cell_yellow: {
      type: Array,
      default: () => {
        return [];
      }
    },
    // 字体绿色
    cell_green: {
      type: Array,
      default: () => {
        return [];
      }
    },
    // 是否高亮当前行
    highlightCurrentRow: {
      type: Boolean,
      default: true
    },
    // 是否显示表头
    showHeader: {
      type: Boolean,
      default: true
    },
    // 是否显示斑马纹
    isStripe: {
      type: Boolean,
      default: false
    },
    // 表格行的类名
    tableRowClassName: {
      type: Function,
      default: () => {
        return () => {};
      }
    },
    //表格列的类名
    tableCellClassName: {
      type: Function,
      default: () => {
        return () => {};
      }
    },
    // 表头样式
    headerCellStyle: {
      type: Object,
      default: () => {
        return {
          background: 'rgba(23,112,223,.2)',
          fontSize: '14px',
          color: '#2D3436'
        };
      }
    },
    // 默认全部展开
    expandAll: {
      type: Boolean,
      default: false
    },
    // 默认选中的
    checkRowCode: {
      type: Array,
      default: () => {
        return [];
      }
    },
    // 遍历判断checkbox是否可选
    selectable: {
      type: Function,
      default: () => {
        return true;
      }
    },
    // 每一行的唯一值
    rowID: {
      type: String,
      default: ''
    },
    // 单独设置列的内容对齐方式
    columnAlign: {
      type: Object,
      default: () => {
        return {};
      }
    },
    // 数据更新之后保留之前选中的数据
    reserveSelection: {
      type: Boolean,
      default: false
    },
    // 行数据key
    rowKey: {
      type: String,
      default: ''
    },
    // 唯一id字段，用来获取选中行的位置
    onlyId: {
      type: String,
      default: 'regNo'
    },
    border: {
      type: Boolean,
      default: false
    },
    // 每页显示的数量
    pageSize: {
      type: Number,
      default: 50
    },
    // 内容过长显示省略号
    showOverflowTooltip: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableData: [],
      tableDataCodeObj: {},
      // pageSize: 50,
      currentPage: 1,
      colNames: [],
      selection: [],
      isSelectAllFlag: false // 是否点击了全选
    };
  },
  methods: {
    // 当前行选中
    handleCurrentChange(val) {
      this.$emit('currentChange', val);
    },
    // 复选框选择
    handleSelectionChange(val) {
      this.$nextTick(() => {
        this.$emit('selectionChange', val, this.selection);
      });
    },
    // 全选的回调
    selectAll(rows) {
      if (rows.length == 0) {
        this.selection = [];
        this.isSelectAllFlag = false;
      } else {
        this.selection = dataUtils.deepCopy(this.viewTableList);
        this.isSelectAllFlag = true;
      }

      this.$emit('selectAll', rows, this.selection);
    },
    // 单行选择的回调
    select(selection, row) {
      let isSelect = false;
      let idx = -1;
      this.selection.some((item, index) => {
        if (item[this.onlyId] == row[this.onlyId]) {
          isSelect = true;
          idx = index;
          return true;
        }
      });

      if (this.isSelectAllFlag) {
        if (!isSelect) {
          this.selection.push(row);
        } else {
          this.selection.splice(idx, 1);
        }
      } else {
        this.selection = selection;
      }
      this.$emit('select', selection, row, this.selection);
    },
    // 滚动加载
    load() {
      this.currentPage++;
      // if (this.currentPage * this.pageSize >= this.viewTableList.length) return;
      let loadSize = this.currentPage * this.pageSize;
      if (loadSize - this.viewTableList.length >= this.pageSize) return;
      let start = (this.currentPage - 1) * this.pageSize;
      let end = this.currentPage * this.pageSize;
      let pageData = this.viewTableList.slice(start, end);
      this.tableData.push(...pageData);
      let tableRef = this.$refs.tableCom_Ref;
      this.defaultCheckRow(pageData);
      if (tableRef.store.states.isAllSelected) {
        tableRef.toggleAllSelection();
      }
      if (!tableRef.store.states.isAllSelected && this.isSelectAllFlag) {
        let start = (this.currentPage - 1) * this.pageSize;
        let end = this.currentPage * this.pageSize;
        let arr = this.tableData.slice(start, end);
        arr.map((row) => {
          this.$refs.tableCom_Ref.toggleRowSelection(row);
        });
      }
    },
    // 单元格样式
    cellClassName({ row, rowIndex, column, columnIndex }) {
      if (this.cell_blue.includes(column.property)) {
        return 'cell_blue';
      }
      if (this.cell_red.includes(column.property)) {
        return 'cell_red';
      }
      if (this.cell_yellow.includes(column.property)) {
        return 'cell_yellow';
      }
      if (this.cell_green.includes(column.property)) {
        return 'cell_green';
      }
    },
    // 表格的双击事件
    rowDblclick(row, column, event) {
      this.$emit('rowDblclick', row, column, event);
    },
    rowClick(row) {
      this.$emit('rowClick', row);
    },
    cellClick(row, column, cell, event) {
      this.$emit('cell-click', row, column, cell, event);
    },
    // 默认选中
    defaultCheckRow(pageData = []) {
      let tableRef = this.$refs.tableCom_Ref;
      if (
        this.checkRowCode.length == 0 &&
        tableRef.selection.length >= this.checkRowCode.length
      )
        return;

      pageData.map((item) => {
        if (this.checkRowCode.includes(item[this.rowID])) {
          tableRef.toggleRowSelection(item);
        }
      });
    }
  },
  watch: {
    viewTableList: {
      handler(n, o) {
        this.isSelectAllFlag = false;
        this.selection = [];
        this.tableData = [];
        this.$emit('emptySelection', []);
        this.currentPage = 1;
        this.$nextTick(() => {
          if (!this.isOpenPage) {
            this.tableData = n;
            this.defaultCheckRow(n);
            return;
          }
          if (this.currentPage * this.pageSize >= n.length) {
            this.tableData.push(...n);
            this.defaultCheckRow(n);
            return;
          }
          let start = (this.currentPage - 1) * this.pageSize;
          let end = this.currentPage * this.pageSize;
          let pageData = n.slice(start, end);
          this.tableData.push(...pageData);
          this.defaultCheckRow(pageData);
        });
      },
      immediate: true
    }
  }
};
</script>
<style lang="less" scoped></style>
<style lang="less">
.publicTable_com {
  height: 100%;
  width: 100%;
  .el-table__row--striped td.el-table__cell,
  .vxe-table--render-default .vxe-body--row.row--stripe {
    background-color: #e7f0fb !important;
  }

  .vxe-table--render-default .vxe-body--row.row--current {
    background-color: #e6f7ff;
  }
  .el-table__cell {
    vertical-align: top;
  }
  // .cell_blue {
  //   color: #1770df;
  // }

  // .cell_red {
  //   color: #d63031;
  // }
  // .cell_yellow {
  //   color: #fab63b;
  // }
  // .cell_green {
  //   color: #3cb34f;
  // }
  // .el-table__body tr.current-row > td.el-table__cell input {
  //   color: #fff !important;
  //   background-color: #1770df !important;
  // }
  // .el-table__body tr.current-row > td.el-table__cell .icon-xuanzhong {
  //   color: #fff !important;
  // }
  .el-table__body tr.current-row > td.el-table__cell {
    background: no-repeat;
  }
  .el-table .have_pay {
    background: rgba(60, 179, 79, 0.1);
  }
  .el-table .no_pay {
    background: rgba(250, 182, 59, 0.1);
  }
  .el-table .have_meal {
    td {
      background: rgba(115, 100, 244, 0.1) !important;
    }
  }
  // th,td{
  //   width: 200px !important;
  //   word-wrap: break-word;
  //   word-break: break-all;
  // }
  input[type='number'] {
    -moz-appearance: textfield;
  }
  input[type='number']::-webkit-inner-spin-button,
  input[type='number']::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  .el-table .tr_red {
    color: #d63031 !important;
  }
  .el-table .tr_violet {
    color: #7364f4;
  }
  .el-table .tr_black {
    color: #2d3436 !important;
  }
  .el-table .tr_gray {
    background: rgba(178, 190, 195, 0.2);
  }
  .el-table .tr_lightRed {
    background: rgba(214, 48, 49, 0.1);
  }
  .el-table .tr_lightBlue {
    background: rgba(23, 112, 223, 0.1);
  }
  .el-table__cell {
    vertical-align: middle !important;
  }
  /* 修改表格勾选框样式 */
  .el-table .el-checkbox__inner::after {
    left: 6px;
    top: 3px;
  }
  .el-table .el-checkbox__inner,
  .el-table .el-checkbox__input.is-disabled .el-checkbox__inner {
    width: 18px;
    height: 18px;
  }
  .el-table .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    background-color: #409eff !important;
    border-color: #409eff !important;
    font-size: 18px;
  }
  .el-table
    .el-checkbox__input.is-disabled.is-checked
    .el-checkbox__inner::after {
    border-color: #fff !important;
  }
}
</style>
