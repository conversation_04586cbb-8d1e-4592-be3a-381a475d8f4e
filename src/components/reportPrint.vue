<template>
  <el-dialog
    class="preview_popup"
    @open="getPdfPreview(printerTypeList[0])"
    title="提示"
    width="100%"
    :show-close="false"
    :visible.sync="C_previewShow"
    :close-on-click-modal="false"
  >
    <header slot="title">
      <span>打印</span>
      <i class="el-icon-close" @click="cancelClick"></i>
    </header>
    <div class="print_body">
      <div class="tool_div">
        <!-- <div class="tool_wrap">
          <span>次数：</span>
          <div style="width: 100px">
            <el-input
              size="small"
              v-model="printSetInfo.PrintTimes"
              placeholder="请输入内容"
            ></el-input>
          </div>
        </div> -->
        <div class="tool_wrap">
          <span>打印机：</span>
          <div>
            <el-select
              size="small"
              v-model="printSetInfo.PrinterName"
              placeholder="请选择"
            >
              <el-option
                v-for="item in printList"
                :key="item"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <span style="width: 56px"></span>
        <!-- <el-button size="mini" type="primary" @click="ws.closeWebSocket()">关闭</el-button>
                <el-button size="mini" type="primary" @click="linkBtn">连接</el-button> -->
      </div>
      <div class="tool_div">
        <!-- <div class="tool_wrap">
          <span>次数：</span>
          <div style="width: 100px">
            <el-input
              size="small"
              v-model="printSetInfo.PrintTimes"
              placeholder="请输入内容"
            ></el-input>
          </div>
        </div> -->
        <div class="tool_wrap">
          <span>条码打印机：</span>
          <div>
            <el-select
              size="small"
              v-model="printSetInfo.barcodeName"
              placeholder="请选择"
            >
              <el-option
                v-for="item in printList"
                :key="item"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <el-button size="mini" type="primary" @click="print">打印</el-button>
        <!-- <el-button size="mini" type="primary" @click="ws.closeWebSocket()">关闭</el-button>
                <el-button size="mini" type="primary" @click="linkBtn">连接</el-button> -->
      </div>
      <div class="print_content">
        <el-menu
          default-active="0"
          class="el-menu-vertical-demo"
          style="width: 200px"
        >
          <el-menu-item
            @click="menuClick(item)"
            :index="idx + ''"
            v-for="(item, idx) in printerTypeList"
            :key="idx"
          >
            <div>
              <el-checkbox
                class="checkBox_dom"
                v-model="item.checked"
              ></el-checkbox>
              <span slot="title">{{ item.label }}</span>
            </div>
          </el-menu-item>
        </el-menu>
        <embed
          class="embed_dom"
          type="application/pdf"
          width="100%"
          :src="pdfSrc"
          height="100%"
        />
      </div>
    </div>
    <div class="my_mask" v-if="batchMaskShow">
      <el-progress type="circle" :percentage="percentage"></el-progress>
      <p class="tips_p">{{ message }}</p>
    </div>
  </el-dialog>
</template>

<script>
import printMixins from './printMixins';
import { mapGetters } from 'vuex';
import { storage } from '../common';

export default {
  mixins: [printMixins],
  model: {
    prop: 'previewShow',
    event: 'previewShowChange'
  },
  props: {
    previewShow: {
      typeof: Boolean,
      default: false
    },
    dataInfo: {
      type: Object,
      default: {}
    },
    printerTypeList: {
      type: Array,
      default: () => {
        return [
          {
            printerType: 0, //打印机类型 0 普通打印机，1 条码打印机
            label: '指引单',
            checked: true
          }
        ];
      }
    },
    batchPrintList: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  computed: {
    ...mapGetters(['G_userInfo']),
    C_previewShow: {
      get() {
        if (this.previewShow) {
          this.connectPrint((r) => {
            let dataInfo = JSON.parse(r.data);
            let printDefault = storage.local.get('printDefault');
            this.printList = dataInfo.Data;
            this.printSetInfo.PrinterName = printDefault
              ? printDefault.general
              : dataInfo.Data[0];
            this.printSetInfo.barcodeName = printDefault
              ? printDefault.barcodeName
              : dataInfo.Data[0];
            this.getPrintType();
          });
        } else {
          console.log(888);
          if (JSON.stringify(this.ws) == '{}') return;
          this.ws.closeWebSocket && this.ws.closeWebSocket();
          this.ws.reConnect = () => {};
          this.ws.createWebSocket = () => {};
          this.ws.heartCheck.stop && this.ws.heartCheck.stop();
        }
        return this.previewShow;
      },
      set() {
        this.hideFun();
      }
    }
  },
  data() {
    return {
      printSetInfo: {
        PrintTimes: 1,
        PrinterName: '',
        barcodeName: '',
        barcodeTime: 1
      },
      printList: [],
      pdfSrc: '',
      fileStream: '', //文件流
      isPrint: false,
      // 批打印
      percentage: 0,
      message: '0/0',
      time: 0,
      fileStream: '',
      totalPages: 0,
      currentNum: 0,
      batchMaskShow: false,
      printObj: {
        指引单: 'Guidance',
        体检标签: 'PeLabel',
        检验条码: 'Barcode',
        报告: 'Report'
      },
      checkPrintTypeList: [],
      checkPrintTypeCode: [] //选中的打印类型代码
    };
  },
  methods: {
    hideFun() {
      this.$emit('previewShowChange', false);
    },
    // 确定
    confirmClick() {
      this.hideFun();
    },
    // 取消
    cancelClick() {
      this.hideFun();
    },
    getPdfPreview(item) {
      console.log(this.dataInfo, item);
      let reportCode = '';
      let queryString = '';
      if (item) {
        switch (item.printerType) {
          case 0:
            reportCode = this.dataInfo.reportType || this.dataInfo.guidanceType;
            break;
          case 1:
            reportCode = this.G_userInfo.systemParams.peLabelType;
            break;
          default:
            reportCode = this.dataInfo.reportType || this.dataInfo.guidanceType;
            break;
        }
        queryString = 'regNo=' + this.dataInfo.regNo + '&customizePrintTimes=0';
      }
      let datas = {
        reportCode: item ? reportCode : this.dataInfo.guidanceType,
        queryString:
          item && item.printerType == 1
            ? queryString
            : 'regNo=' + this.dataInfo.regNo
      };
      console.log(datas);
      this.$ajax
        .post(`${this.$config.pdfFileUrl}Home/ExportToPdf`, datas, {
          responseType: 'arraybuffer'
        })
        .then((r) => {
          // let blob = new Blob([r.data],{
          //     type:'application/pdf'
          // })
          // console.log(blob);
          // this.pdfSrc = URL.createObjectURL(blob);
          const blob = new Blob([r.data], {
            type: 'application/pdf;charset=utf-8'
          });
          console.log(blob);
          let data = URL.createObjectURL(blob);
          this.pdfSrc = data;
          this.fileStream = data;
          // const blob = new Blob([r.data], { type: "application/pdf" });
          // let reader = new FileReader();
          // reader.onload = e => {
          //   console.log(e);
          //   let data;
          //   if (typeof e.target.result === "object") {
          //     data = window.URL.createObjectURL(e.target.result);
          //   } else {
          //     data = e.target.result;
          //   }
          //   this.pdfSrc = data;
          //   this.fileStream = data;
          // };
          // //转化为base64
          // reader.readAsDataURL(blob);
        });
    },
    print() {
      if (this.checkPrintTypeList.length == 0) {
        this.$message({
          message: '请选择打印的类型',
          type: 'warning',
          showClose: true
        });
        return;
      }
      storage.local.set('printDefault', {
        general: this.printSetInfo.PrinterName,
        barcodeName: this.printSetInfo.barcodeName
      });
      this.currentNum = 0;
      // 批打印
      let batchPrintList = this.batchPrintList;
      console.log(batchPrintList);
      if (batchPrintList && batchPrintList.length != 0) {
        this.batchMaskShow = true;
        this.batchPrint(batchPrintList);
        return;
      }
      // 单人打印
      this.checkBarcodeAndPeLabel(this.dataInfo).then((r) => {
        console.log(this.dataInfo);
        this.checkPrintTypeList.map((item, idx) => {
          if (!r.data.returnData.includes(item.code)) {
            this.$message({
              message: `${this.dataInfo.name}没有${item.label}`,
              type: 'warning',
              showClose: true
            });
            return;
          }
          this.batchGetPDF(item, this.dataInfo).then((r) => {
            if (idx == this.checkPrintTypeList.length - 1) {
              setTimeout(() => {
                this.$message({
                  message: '打印完成',
                  type: 'success',
                  showClose: true
                });
                this.hideFun();
              }, 300);
            }
          });
        });
      });

      // console.log(this.fileStream);

      // let fileStream = this.fileStream.split(",")[1];
      // // return
      // // console.log(this.fileStream);
      // let obj2 = {
      //   PrinterName: this.printSetInfo.PrinterName,
      //   PrintTimes: 1,
      //   FileBase64: fileStream,
      //   FileFormat: "pdf"
      // };
      // let obj = {
      //   Action: "print",
      //   Data: JSON.stringify(obj2)
      // };
      // var data = JSON.stringify(obj);
      // console.log(data);
      // // let T = "{'Action':'getPrinterList','Data':''}";
      // this.ws.sendSock(data);
      // this.isPrint = true;
    },
    // 批打印开始
    printStart(fileStream, printerType) {
      let fileStreams = fileStream.split(',')[1];
      // return
      // console.log(this.fileStream);
      let obj2 = {
        PrinterName:
          printerType === 0
            ? this.printSetInfo.PrinterName
            : this.printSetInfo.barcodeName,
        PrintTimes: 1,
        FileBase64: fileStreams,
        FileFormat: 'pdf'
      };
      let obj = {
        Action: 'print',
        Data: JSON.stringify(obj2)
      };
      var data = JSON.stringify(obj);
      console.log(data);
      // let T = "{'Action':'getPrinterList','Data':''}";
      this.ws.sendSock(data);
      this.isPrint = true;
    },
    // 批打印
    batchPrint(list) {
      this.message = `${this.currentNum}/${list.length}`;
      this.percentage = Math.floor(
        (this.currentNum / list.length).toFixed(2) * 100
      );
      console.log(this.currentNum);
      if (this.currentNum >= list.length) {
        setTimeout(() => {
          this.$message({
            message: '打印完成',
            type: 'success',
            showClose: true
          });
          this.batchMaskShow = false;
          this.$parent.printFinishInit();
          // setTimeout(() => {
          //   this.hideFun();
          // }, this.time * this.currentNum);
        }, this.time * this.currentNum);
        return;
      }
      this.checkBarcodeAndPeLabel(list[this.currentNum]).then((r) => {
        this.checkPrintTypeList.map((item, idx) => {
          console.log(item);
          if (!r.data.returnData.includes(item.code)) {
            if (idx == this.checkPrintTypeList.length - 1) {
              this.currentNum += 1;
              this.batchPrint(list);
            }
            return;
          }
          this.batchGetPDF(item, list[this.currentNum]).then((r) => {
            this.updatePrintFlag(list[this.currentNum].regNo);
            setTimeout(() => {
              if (idx == this.checkPrintTypeList.length - 1) {
                this.currentNum += 1;
                this.batchPrint(list);
              }
            }, this.time * this.totalPages);
          });
        });
      });
    },
    batchGetPDF(item, row) {
      return new Promise((resolve, reject) => {
        let reportCode = '';
        let queryString = '';
        console.log(item);
        if (item) {
          switch (item.printerType) {
            case 0:
              reportCode = this.dataInfo.lockingReportType
                ? this.dataInfo.guidanceType
                : row.reportType;
              break;
            case 1:
              reportCode = this.G_userInfo.systemParams.peLabelType;
              break;
            default:
              reportCode = this.dataInfo.lockingReportType
                ? this.dataInfo.guidanceType
                : row.reportType;
              break;
          }
          queryString = 'regNo=' + row.regNo + '&customizePrintTimes=0';
        }
        let datas = {
          reportCode: reportCode,
          queryString:
            item && item.printerType == 1 ? queryString : 'regNo=' + row.regNo
        };
        console.log(datas);
        this.$ajax
          .post(`${this.$config.pdfFileUrl}Home/ExportToPdf`, datas, {
            responseType: 'arraybuffer'
          })
          .then((r) => {
            // let blob = new Blob([r.data],{
            //     type:'application/pdf'
            // })
            // console.log(blob);
            // this.pdfSrc = URL.createObjectURL(blob);
            const blob = new Blob([r.data], { type: 'application/pdf' });
            let reader = new FileReader();

            reader.onload = (e) => {
              console.log(e);
              let data;
              if (typeof e.target.result === 'object') {
                data = window.URL.createObjectURL(e.target.result);
              } else {
                data = e.target.result;
              }
              // this.pdfSrc = data;
              // this.fileStream = data;
              this.printStart(data, item.printerType);
            };
            //转化为base64
            reader.readAsDataURL(blob);

            let file = new File([r.data], '', {
              type: 'application/pdf'
            });
            let fileReader = new FileReader();
            fileReader.readAsBinaryString(file);
            fileReader.onloadend = (e) => {
              console.log(fileReader);
              var count = fileReader.result.match(
                /\/Type[\s]*\/Page[^s]/g
              ).length;
              console.log('Number of Pages:', count);
              this.totalPages = count;
              resolve(this.totalPages);
            };
          });
      });
    },
    batchPrintFun() {
      console.log(this.fileStream);
      let fileStream = this.fileStream.split(',')[1];
      // return
      // console.log(this.fileStream);
      let obj2 = {
        PrinterName: this.$parent.printSetInfo.PrinterName,
        PrintTimes: this.$parent.printSetInfo.PrintTimes,
        FileBase64: fileStream,
        FileFormat: 'pdf'
      };
      let obj = {
        Action: 'print',
        Data: JSON.stringify(obj2)
      };
      var data = JSON.stringify(obj);
      console.log(data);
      // let T = "{'Action':'getPrinterList','Data':''}";
      this.$parent.ws.sendSock(data);
      this.isPrint = true;
    },
    // linkBtn(){
    //     this.ws.createWebSocket();
    // }
    menuClick(item) {
      console.log(item);
      this.getPdfPreview(item);
      setTimeout(() => {
        this.getPrintType();
      }, 100);
    },
    // 查询有无体检标签打印
    checkBarcodeAndPeLabel(userInfo) {
      let datas = {
        regNo: userInfo.regNo,
        filterList: this.checkPrintTypeCode
      };
      console.log(datas);
      return this.$ajax.post(this.$apiUrls.CheckPrintables, datas);
    },
    // 获取选中的打印类型
    getPrintType() {
      let checkPrintTypeList = [];
      let checkPrintTypeCode = [];
      this.printerTypeList.map((item) => {
        if (item.checked) {
          checkPrintTypeList.push({
            ...item,
            code: this.printObj[item.label]
          });
          checkPrintTypeCode.push(this.printObj[item.label]);
        }
      });
      console.log(checkPrintTypeList, checkPrintTypeCode);
      this.checkPrintTypeList = checkPrintTypeList;
      this.checkPrintTypeCode = checkPrintTypeCode;
    },
    // 打印状态更新
    updatePrintFlag(regNo) {
      this.$ajax
        .paramsPost(this.$apiUrls.UpdateReportPrinted, { regNo: regNo })
        .then((r) => {
          console.log('UpdateReportPrinted: ', r);
          let { success, returnData } = r.data;
          if (!success) return;
        });
    }
  },
  created() {
    // this.getPdfPreview();
  }
};
</script>
<style lang="less" scoped>
.preview_popup {
  /deep/ .el-dialog {
    height: 100% !important;
    margin: 0 !important;
    display: flex;
    flex-direction: column;
  }

  /deep/ .el-dialog__body {
    flex: 1;
    flex-shrink: 0;
    overflow: hidden;
    padding: 0;
  }

  /deep/ .el-dialog__header {
    padding: 0;
  }

  header {
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    i {
      cursor: pointer;
      font-size: 28px;
    }
  }

  .tool_div {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 5px;

    .tool_wrap {
      display: flex;
      align-items: center;
      margin-right: 20px;

      > span {
        font-size: 14px;
        line-height: 14px;
        width: 85px;
      }
    }
  }

  .print_body {
    height: 100%;
    display: flex;
    flex-direction: column;

    .print_content {
      flex: 1;
      flex-shrink: 0;
      display: flex;
      .embed_dom {
        flex: 1;
        flex-shrink: 0;
        background: #000;
      }
      .checkBox_dom {
        margin-top: -5px;
        margin-right: 10px;
      }
    }
  }
}
</style>
