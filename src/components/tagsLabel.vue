<template>
  <div class="tags_component">
    <el-scrollbar
      class="tags_warp scroll-container tags-view-container"
      ref="scrollbarDom"
      @wheel.prevent="handleScroll"
    >
      <span
        class="tag_item"
        ref="tag"
        :class="{ active: tagActive.name == tag.name }"
        to=""
        @click="navClick(tag)"
        v-for="(tag, index) in tags"
        :key="tag.path"
        @click.right.prevent="openMenu(tag, $event)"
        tag="span"
      >
        {{ tag.meta.title }}
        <i
          class="tag_close el-icon-circle-close"
          @click.stop="deleteTag(tag)"
          v-if="index !== 0"
        >
        </i>
      </span>
    </el-scrollbar>
    <!-- 右键菜单 -->
    <ul
      v-show="visible"
      :style="{ left: left + 'px', top: top + 'px' }"
      class="contextmenu"
    >
      <li @click="pageReload">刷新</li>
      <li v-if="selectedTag.path !== '/'" @click.stop="deleteTag(selectedTag)">
        关闭
      </li>
      <li @click="delOther">关闭其他页面</li>
      <li @click="delAllTag">关闭所有页面</li>
    </ul>
  </div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex';

export default {
  name: 'tagsLabel',
  components: {},
  computed: {
    ...mapGetters(['G_headShow']),
    $config() {
      return this.$config;
    }
  },
  data() {
    return {
      left: 0,
      top: 0,
      visible: false,
      selectedTag: {},
      tags: [
        {
          path: '/',
          name: 'home',
          meta: {
            title: '首页',
            keepAlive: true,
            auth: false
          }
        }
      ],
      tagActive: {},
      keepAliveArr: [],
      defaultTag: {
        path: '/',
        name: 'home',
        meta: {
          title: '首页',
          keepAlive: true,
          auth: false
        }
      }
    };
  },
  methods: {
    ...mapMutations(['M_setKeepAlive', 'M_reloadFlag']),
    navClick(item) {
      this.$router.push(item.path);
      this.$nextTick(() => {
        this.setPosition();
      });
    },
    // 打开自定义右键菜单
    openMenu(tag, e) {
      let scrollbarDom = this.$refs.scrollbarDom;
      const menuMinWidth = 105;
      const offsetLeft = scrollbarDom.$el.getBoundingClientRect().left; // container margin left
      const offsetWidth = scrollbarDom.$el.offsetWidth; // container width
      const maxLeft = offsetWidth - menuMinWidth; // left boundary
      const left = e.clientX - offsetLeft + 15; // 15: margin right
      if (left > maxLeft) {
        this.left = maxLeft;
      } else {
        this.left = left;
      }

      this.top = e.clientY - 54;
      if (this.G_headShow) {
        this.top = this.top - 40;
      }
      this.visible = true;
      this.selectedTag = tag;
    },

    // tab 鼠标滚轮事件
    handleScroll(e) {
      const eventDelta = e.wheelDelta || -e.deltaY * 40;
      const $scrollWrapper = this.$refs.scrollbarDom.$refs.wrap;
      $scrollWrapper.scrollLeft = $scrollWrapper.scrollLeft + eventDelta / 4;
    },
    /** 设置当前滚动条应该在的位置 */
    setPosition() {
      let scrollbarDom = this.$refs.scrollbarDom;
      if (scrollbarDom) {
        const domBox = {
          scrollbar: scrollbarDom.$refs.wrap,
          activeDom: scrollbarDom.$el.querySelector('.active'),
          activeFather: scrollbarDom.$refs.resize
        };
        for (let i in domBox) {
          if (!domBox[i]) {
            return;
          }
        }
        const domData = {
          scrollbar: domBox.scrollbar.getBoundingClientRect(),
          activeDom: domBox.activeDom.getBoundingClientRect(),
          activeFather: domBox.activeFather.getBoundingClientRect()
        };
        const num =
          domData.activeDom.x -
          domData.activeFather.x +
          (1 / 2) * domData.activeDom.width -
          (1 / 2) * domData.scrollbar.width;
        domBox.scrollbar.scrollLeft = num;
      }
    },
    // 关闭右键菜单
    closeMenu() {
      this.visible = false;
    },

    // 设置tag高亮
    setTagLight(route) {
      this.tagActive = route;
      this.$nextTick(() => {
        this.setPosition();
      });
    },
    // 添加tag
    addTag(route) {
      let { path, name, meta } = route;
      let hasTag = this.tags.some((item) => {
        return item.name === route.name;
      });
      if (!hasTag) {
        this.tags.push({
          path,
          name,
          meta
        });
      }
      // 设置keep-alive缓存页面
      let keepAliveArr = [];
      this.tags.map((item) => {
        if (item.meta.keepAlive) {
          keepAliveArr.push(item.name);
        }
      });
      this.keepAliveArr = keepAliveArr;
      this.M_setKeepAlive(keepAliveArr);
    },
    // 删除单个tag
    deleteTag(tag) {
      let i = 0;
      this.tags.some((item, index) => {
        if (item.name === tag.name) {
          this.tags.splice(index, 1);
          i = index;
          return true;
        }
      });
      this.visible = false;
      this.$router.push({
        name: this.tags[i - 1].name
      });
    },
    // 当前页面组件重新加载
    pageReload() {
      let keepAliveArr = [...this.keepAliveArr];
      this.keepAliveArr.some((item, index) => {
        if (item === this.selectedTag.name) {
          keepAliveArr.splice(index, 1);
          return true;
        }
      });
      this.M_setKeepAlive(keepAliveArr);
      if (this.selectedTag.path === this.$route.path) {
        this.M_reloadFlag(false);
        this.$nextTick(() => {
          this.M_reloadFlag(true);
          this.M_setKeepAlive(this.keepAliveArr);
        });
      }
    },
    // 删除其他页面
    delOther() {
      this.tags = [this.defaultTag];
      if (this.$route.path !== this.defaultTag.path) {
        this.addTag(this.$route);
      }
      this.visible = false;
    },
    // 删除所有页面
    delAllTag() {
      this.tags = [this.defaultTag];
      this.$router.push(this.defaultTag.path);
    }
  },
  created() {
    this.addTag(this.$route);
    this.setTagLight(this.$route);
  },
  watch: {
    $route: {
      handler: function (val, oldVal) {
        this.addTag(this.$route);
        this.setTagLight(this.$route);
      },

      deep: true
    },
    visible(n, o) {
      if (n) {
        document.body.addEventListener('click', this.closeMenu);
      } else {
        document.body.removeEventListener('click', this.closeMenu);
      }
    }
  }
};
</script>
<style lang="less" scoped>
.tags_component {
  width: 100%;
  position: relative;

  .tags_warp {
    position: relative;
    // box-shadow: 0 1px 4px rgba(0, 21, 41, .2);
    width: 100%;
    height: 34px;
    background: #fff;
    white-space: nowrap;
    overflow-x: auto;
    padding-right: 5px;

    .tag_item {
      margin: 5px 0 0 5px;
      cursor: pointer;
      background: #fff;
      border: 1px solid #d8dce5;
      display: inline-block;
      height: 26px;
      line-height: 24px;
      padding: 0 8px;
      font-size: 12px;

      .tag_close {
        display: inline-block;
        font-size: 14px;
        &:hover {
          background-color: #b4bccc;
          color: #fff;
        }
      }
    }

    .active {
      background: #42b983;
      border-color: #42b983;
      color: #fff;
    }
  }

  .scroll-container {
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    width: 100%;

    :deep(.el-scrollbar__bar) {
      bottom: 0px;
    }

    :deep(.el-scrollbar__wrap) {
      height: 49px;
    }
  }

  .tags-view-container {
    height: 34px;
    flex: 1;
    width: 100%;
    display: flex;
  }

  .contextmenu {
    margin: 0;
    background: #fff;
    z-index: 3000;
    position: absolute;
    list-style-type: none;
    padding: 5px 0;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    color: #333;
    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);

    li {
      margin: 0;
      padding: 7px 16px;
      cursor: pointer;

      &:hover {
        background: #eee;
      }
    }
  }
}
@media screen and(max-width: 1440px) {
  .tags_component {
    .tags_warp {
      height: 28px;
    }
    .tag_item {
      height: 24px !important;
      line-height: 24px !important;
      margin-top: 2px !important;
    }
  }
}
</style>
<style lang="less">
.tags_component {
  .el-scrollbar__wrap {
    scroll-behavior: smooth;
    overflow: hidden;
  }
}
</style>
