/**
 * @FilePath: \KrPeis\src\components\printMixins.js
 * @Description:  打印相关混入
 * @Author:
 * @Date:
 * @Version: 0.0.1
 * @LastEditors: justin
 * @LastEditTime: 2024-06-18 10:24:23
 
 */
import WS from '@/common/websocket';
import Vue from 'vue';

export default {
  data() {
    return {};
  },
  created() {
    // this.ws = {}; // 放在此处，取消响应式（被用在computed时，会发生循环响应）
  },
  methods: {
    connectPrint(websocketonmessage) {
      if (
        !this.$w_print ||
        !this.$w_print?.websock ||
        this.$w_print?.websock?.readyState === WebSocket.CLOSED
      ) {
        this.$w_print?.closeWebSocket();
        Vue.prototype.$w_print = new WS(`${this.$config.printUrl}printer`);
      }
      var data = "{'Action':'getPrinterList','Data':''}";
      this.$w_print.websocketonmessage = websocketonmessage;
      this.$w_print.sendSock(data);
    }
  }
};
