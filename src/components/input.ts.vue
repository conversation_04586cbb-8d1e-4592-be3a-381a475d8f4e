<script setup lang="ts">
import { computed, ref, nextTick } from 'vue';

interface LabelItem {
  id: number;
  tag?: string;
  label?: string;
  abnormalType?: number;
  [key: string]: any;
}

const props = defineProps<{ labelList: LabelItem[]; itemCode: string }>();
const emit = defineEmits<{
  (e: 'inputChange'): void;
  (e: 'inputBlur', txt: string): void;
  (e: 'inputComEnter', txt: string): void;
  (e: 'labelDel', tag: LabelItem): void;
  (e: 'createSummary', tag: LabelItem): void;
  (e: 'resultSpanBlur', tag: LabelItem, idx: number): void;
  (e: 'resultSpanInput'): void;
}>();

const inputVal = ref('');
const input_Ref = ref<HTMLDivElement | null>(null);

const typedLabelList = computed(() => {
  return props.labelList as LabelItem[];
});

/**
 * 输入变化时触发
 */
function inputChange(_e: Event): void {
  emit('inputChange');
}

/**
 * 输入框失焦时触发
 */
function inputBlur(): void {
  if (input_Ref.value) {
    const inputText = input_Ref.value.innerText;
    emit('inputBlur', inputText);
    input_Ref.value.innerHTML = '';
  }
}

/**
 * 处理键盘按下事件
 */
function inputKeydown(e: KeyboardEvent): void {
  if (input_Ref.value) {
    const inputText = input_Ref.value.innerText;
    const labelList = props.labelList;

    // 使用 key 属性代替已弃用的 keyCode
    switch (e.key) {
      case 'Backspace': // Backspace键
        if (inputText.length === 0 && labelList.length > 0) {
          const tag = labelList[labelList.length - 1];
          if (tag) {
            emit('labelDel', tag);
          }
          // 注意：在setup中不应该直接修改props
          // 应该通过emit事件通知父组件修改
          // 这里保留原有逻辑，但实际应该由父组件处理
        }
        break;
      case 'Enter': // Enter键
        e.preventDefault();
        emit('inputComEnter', inputText);
        input_Ref.value.innerHTML = '';
        break;
      default:
        break;
    }

    nextTick(() => {
      emit('inputChange');
    });
  }
}

/**
 * 删除标签
 */
function labelDel(_idx: number, tag: LabelItem): void {
  // 注意：在setup中不应该直接修改props
  // 应该通过emit事件通知父组件修改
  emit('labelDel', tag);
  emit('inputChange');
}

/**
 * 生成小结
 */
function createSummary(tag: LabelItem): void {
  emit('createSummary', tag);
}

/**
 * 输入框回车事件
 */
function inputSpanEnter(idx: number): void {
  const refName = `inputSpan_Ref_${props.itemCode}_${idx}`;
  const el = document.getElementById(refName) as HTMLElement;
  if (el) {
    el.blur();
  }
}

/**
 * 结果输入框失焦事件
 */
function resultSpanBlur(item: LabelItem, idx: number): void {
  const refName = `inputSpan_Ref_${props.itemCode}_${idx}`;
  const el = document.getElementById(refName) as HTMLElement;
  if (el) {
    item.tag = el.innerText;
    emit('resultSpanBlur', item, idx);
  }
}

/**
 * 结果输入框输入事件
 */
function resultSpanInput(): void {
  emit('resultSpanInput');
}
</script>

<template>
  <div class="input_wrap">
    <label
      :class="{ abnormal_result: (item as LabelItem).abnormalType > 0 }"
      v-for="(item, idx) in typedLabelList"
      :key="(item as LabelItem).id"
    >
      <span
        contenteditable="true"
        :id="'inputSpan_Ref_' + itemCode + '_' + idx"
        @keypress.enter.prevent="inputSpanEnter(idx)"
        @blur="resultSpanBlur(item, idx)"
        @input="resultSpanInput"
        >{{ (item as LabelItem).tag }}</span
      >
      <i
        class="el-icon-error"
        title="删除结果"
        @click.stop="labelDel(idx, item)"
      ></i>
      <i
        class="el-icon-success"
        title="生成小结"
        @click.stop="createSummary(item)"
      ></i>
    </label>
    <div
      class="input_div"
      contenteditable="true"
      ref="input_Ref"
      v-html="inputVal"
      @keydown="inputKeydown"
      @input="inputChange"
      @blur="inputBlur"
    ></div>
  </div>
</template>

<style lang="less" scoped>
.input_wrap {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  // border: 2px solid #1abc9c;
  // padding: 2px;
  label {
    padding: 2px 3px;
    background: #ecf5ff;
    border: 1px solid #d9ecff;
    color: #409eff;
    display: inline-block;
    margin-bottom: 3px;
    margin-right: 3px;
    word-break: break-all;
    position: relative;
    min-height: 25px;
    line-height: 21px;
    vertical-align: middle;
    border-radius: 4px;
    i {
      position: absolute;
      top: -5px;
      right: -5px;
      color: #f56c6c;
      cursor: pointer;
      display: none;
      &:last-child {
        top: 15px;
      }
    }
    &:hover i {
      display: block;
    }
    span {
      display: inline-block;
      word-break: break-all;
      &:focus-visible {
        outline: none;
      }
    }
    // &:hover span{
    //     padding-right: 10px;
    // }
  }
  .abnormal_result {
    background: #fef0f0;
    border-color: #fbc4c4;
    color: #f56c6c;
  }
  .input_div {
    flex: 1;
    min-width: 50px;
    min-height: 25px;
    line-height: 25px;
    outline: none;
    word-break: break-all;
    &:focus-visible {
      outline: none;
    }
  }
}
</style>
