/*
 * @FilePath: \KrPeis\src\components\printHooks.js
 * @Description: 打印相关的hooks
 * @Version: 2.0
 * @Author: key
 * @Date: 2024-07-09 17:43:55
 * @LastEditors: key
 * @LastEditTime: 2024-07-24 18:15:32
 */
import { storage } from '../common';
import WS from '../common/websocket';
import Vue from 'vue';
export const usePrint = () => {
  const printParameters = {
    printer: '', //打印机
    barcodePrinters: '', //条码打印机
    printList: [], //打印机列表
    printType: [], //打印类型
    selectedPrint: [], //打印选中
    regNo: '', //体检号
    regNoList: [], //体检号列表
    printableList: [], //可打印列表
    loading: false,
    progressData: {
      progress: 0, //打印进度
      loading: false
    }
  };

  const printObj = [
    {
      type: 'Guidance',
      typeName: '指引单',
      disabled: false,
      printerType: 0
    },
    {
      type: 'Gastroscope',
      typeName: '胃镜申请单',
      disabled: false,
      printerType: 0
    },
    {
      type: 'Colonoscopy',
      typeName: '肠镜申请单',
      disabled: false,
      printerType: 0
    },
    // {
    //     type: "Report",
    //     typeName: "报告",
    //     disabled: false,
    //     printerType: 0
    // },
    {
      type: 'PeLabel',
      typeName: '体检标签',
      disabled: false,
      printerType: 1
    },
    // {
    //     type: "Barcode",
    //     typeName: "检验条码",
    //     disabled: false,
    //     printerType: 1
    // },

    {
      type: 'BloodBarcode',
      typeName: '采血条码',
      disabled: false,
      printerType: 1
    },
    {
      type: 'NonBloodBarcode',
      typeName: '非采血条码',
      disabled: false,
      printerType: 1
    }
  ];
  /**
   * @description: 确定打印
   * @param {*} isSingleCopy 是否单打
   * @param {*} callback 回调
   * @return {*}
   * @author: key
   */
  async function print(isSingleCopy, callback) {
    try {
      let data;
      let totalTasks = 0; //打印数量
      let completedTasks = 0; //当前已打印数量
      this.progress = 0; //进度条
      this.printParameters.progressData.progress = 0;
      if (isSingleCopy) {
        //单打
        totalTasks = 1;
        //设置打印列表的打印机类型
        data = this.setPrinterType(this.printParameters.printableList);
        const temp = data.find(
          (item) => item.type === this.printParameters.selectedPrint
        );
        //更新打印状态
        this.printParameters.progressData.loading = true;

        this.startPrinting(this.pdfSrc, temp.printerType);
        this.updatePrintFlag(this.printParameters.regNo, temp.type);
        completedTasks++;
        this.updateProgress(completedTasks, totalTasks);
      } else {
        //批打
        if (!this.printParameters.selectedPrint.length > 0)
          return this.$message({
            message: '请选择打印类型',
            type: 'warning',
            showClose: true
          });
        totalTasks = this.printParameters.regNoList.length;
        this.printParameters.progressData.loading = true;
        let res;
        for (const item of this.printParameters.regNoList) {
          res = await this.getAPrintableList(item);

          data = this.setPrinterType(res);
          data = data.filter((item) =>
            this.printParameters.selectedPrint.includes(item.type)
          );
          for (const item of data) {
            const fileStream = await this.retrieveFileStream(
              item.pdfUrl + '?updatePrintStatus=true'
            );
            this.startPrinting(fileStream, item.printerType);
          }
          completedTasks++;
          this.updateProgress(completedTasks, totalTasks);
        }

        // if (data.length === 0) {
        //     return this.$message({
        //         message: "没有可打印数据",
        //         type: "warning",
        //         showClose: true,
        //     });
        // }
      }
      if (callback && typeof callback === 'function') {
        callback();
      }
    } catch (error) {
      console.log('🚀 ~ print ~ error:', error);
      this.printParameters.progressData.loading = false;
      this.$message({
        message: '打印出错啦！',
        type: 'error',
        showClose: true
      });
    }
  }

  /**
   * @description:更新打印状态
   * @param {*} regNo
   * @return {*}
   * @author: key
   */
  function updatePrintFlag(regNo, printType) {
    this.$ajax
      .paramsPost(this.$apiUrls.UpdateReportPrinted, { regNo, printType })
      .then((res) => {});
  }
  /**
   * @description: 打印进度条
   * @param {*} completedTasks 已完成的任务
   * @param {*} totalTasks 总任务数
   */
  function updateProgress(completedTasks, totalTasks) {
    this.printParameters.progressData.progress = Math.floor(
      (completedTasks / totalTasks) * 100
    );
    if (completedTasks == totalTasks) {
      this.printParameters.progressData.loading = false;
      this.$message({
        message: '打印完成',
        type: 'success',
        showClose: true
      });
    }
  }
  /**
   * @description: 设置可打印列表打印机类型
   * @param {*} data
   * @return {*}
   * @author: key
   */
  function setPrinterType(data) {
    return data
      .flat()
      .map((item) => {
        const foundData = printObj.find((d) => d.type === item.type);
        if (foundData) {
          return {
            ...item,
            printerType: foundData.printerType
          };
        }
        return null;
      })
      .filter((item) => item !== null);
  }
  /**
   * @description: 开始打印
   * @param {*} fileStream 文件流
   * @param {*} printerType 打印机类型 0:打印机 1:条码打印机
   * @return {*}
   * @author: key
   */
  function startPrinting(fileStream, printerType) {
    const fileStreams = fileStream.split(',')[1];
    const data = {
      PrinterName:
        printerType === 0
          ? this.printParameters.printer
          : this.printParameters.barcodePrinters,
      PrintTimes: 1,
      FileBase64: fileStreams,
      FileFormat: 'pdf'
    };
    const message = {
      Action: 'print',
      Data: JSON.stringify(data)
    };
    this.$w_print.sendSock(JSON.stringify(message));
  }

  /**
   * @description: 获取文件流
   * @param {*} url 文件地址
   * @return {*}
   * @author: key
   */
  async function retrieveFileStream(url) {
    const res = await this.$ajax.get(url, { responseType: 'arraybuffer' });
    const blob = new Blob([res.data], { type: 'application/pdf' });
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        let data;
        if (typeof e.target.result === 'object') {
          data = window.URL.createObjectURL(e.target.result);
        } else {
          data = e.target.result;
        }
        resolve(data);
      };
      // 转化为 base64
      reader.readAsDataURL(blob);
    });
  }

  /**
   * @description: 导出PDF文件
   * @return {*}
   * @author: key
   */
  async function exportPdf() {
    const temp = this.printParameters.printableList[0].find(
      (item) => item.type === this.printParameters.selectedPrint
    );
    const base64Data = this.pdfSrc.split(',')[1];
    const response = await fetch(`data:application/pdf;base64,${base64Data}`);
    const blob = await response.blob();
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `${this.printParameters.regNo}-${temp.typeName}.pdf`;
    link.click();
  }
  /**
   * @description: 设置打印机数据 setPrinterData
   * @return {*}
   * @author: key
   */
  function setPrinterData() {
    let that = this;
    this.connectPrint((r) => {
      const print = storage.local.get('print');
      const dataInfo = JSON.parse(r.data);
      console.log('🚀 ~ this.connectPrint ~ dataInfo:', dataInfo);

      that.printParameters.printList = dataInfo.Data;
      that.printParameters.printer = print
        ? print.printer
        : that.printParameters.printList[0];
      that.printParameters.barcodePrinters = print
        ? print.barcodePrinters
        : that.printParameters.printList[0];
    });
  }

  /**
   * @description: 获取打印机数据
   * @param {*} websocketonmessage  服务器响应数据
   * @return {*}
   * @author: key
   */
  function connectPrint(websocketonmessage) {
    if (!this.$w_print || this.$w_print?.readyState === WebSocket.CLOSED) {
      Vue.prototype.$w_print = new WS(`${this.$config.printUrl}printer`);
      this.$w_print.onerror = (r) => {
        this.$message({
          message: '连接失败，请先打开本地打印机程序！',
          type: 'error',
          showClose: true,
          duration: 5000
        });
      };
    }

    var data = "{'Action':'getPrinterList','Data':''}";
    this.$w_print.websocketonmessage = websocketonmessage;
    this.$w_print.sendSock(data);
  }

  /**
   * @description: 断开链接
   * @return {*}
   * @author: key
   */
  function breakTheLink() {
    if (JSON.stringify(this.$w_print) == '{}') return;
    this.$w_print.closeWebSocket && this.$w_print.closeWebSocket();
    this.$w_print.reConnect = () => {};
    this.$w_print.createWebSocket = () => {};
    this.$w_print.heartCheck.stop && this.$w_print.heartCheck.stop();
  }

  /**
   * @description: 设置打印类型列表
   * @param {*} data 查询可打印接口返回的数据
   * @return {*}
   * @author: key
   */
  function setThePrintType(data) {
    this.printParameters.printType = printObj.map((item) => {
      const foundData = data?.find((d) => d.type === item.type) || {
        pdfUrl: '',
        type: ''
      };

      return {
        ...item,
        disabled: !foundData.type
      };
    });
  }

  /**
   * @description: 默认勾选打印类型
   * @return {*}
   * @author: key
   */
  function defaultSelectionOfPrintingType() {
    //7.24需求  去掉采血条码打印勾选
    this.printParameters.selectedPrint = this.printParameters.printType
      .filter((item) => !item.disabled)
      .map((item) => item.type)
      .filter((item) => item !== 'BloodBarcode');
  }

  /**
   * @description: 多人设置打印类型列表
   * @return {*}
   * @author: key
   */
  function setPrintingTypeManyPeople() {
    this.printParameters.printType = [...printObj];
  }

  const globalFilterList = [
    'Guidance',
    'Gastroscope',
    'Colonoscopy',
    'PeLabel',
    'BloodBarcode',
    'NonBloodBarcode'
  ];
  /**
   * @description: 获取可打印列表
   * @param {*} regNo 必传--体检号
   * @param {*} filterList 可不传--过滤
   * @return {*}
   * @author: key
   */
  async function getAPrintableList(regNo, filterList = globalFilterList) {
    // this.printParameters.loading = true;
    // let requests = [];
    //判断是否多人
    // if (Array.isArray(regNo) && regNo.length > 1)
    // requests = regNo.map(reg => this.$ajax.post(this.$apiUrls.QueryPrintFileList + `/${reg}`, filterList));
    // else {
    //     const singleRegNo = Array.isArray(regNo) ? regNo[0] : regNo;
    //     this.printParameters.regNo = singleRegNo;
    //     requests.push(this.$ajax.post(this.$apiUrls.QueryPrintFileList + `/${singleRegNo}`, filterList));
    // }

    // const responses = await Promise.all(requests);
    // this.printParameters.printableList = []; // 清空可打印列表
    // responses.forEach(res => {
    //     const { success, returnData } = res.data;
    //     if (success) {
    //         this.printParameters.printableList.push(returnData);
    //     }
    // });

    // //设置组件渲染的打印类型列表
    // if (Array.isArray(regNo) && regNo.length > 1)
    //     this.setPrintingTypeManyPeople();
    // else
    //     this.setThePrintType(this.printParameters.printableList[0]);

    // //是否默认勾选
    // if (this?.defaultCheck) {
    //     this.defaultSelectionOfPrintingType();
    // }
    // this.printParameters.loading = false;

    const singleRegNo = Array.isArray(regNo) ? regNo[0] : regNo;
    const res = await this.$ajax.post(
      this.$apiUrls.QueryPrintFileList + `/${singleRegNo}`,
      filterList
    );
    const { success, returnData } = res.data;
    if (success) {
      return returnData;
    } else {
      return [];
    }
  }

  /**
   * @description: 设置打印类型列表
   * @param {*} regNo 体检号
   * @return {*}
   * @author: key
   */
  async function setPrintTypeList(regNo) {
    this.printParameters.loading = true;
    if (Array.isArray(regNo) && regNo.length >= 1) {
      this.printParameters.regNoList = regNo;
      this.setPrintingTypeManyPeople();
    } else {
      const res = await this.getAPrintableList(regNo);
      this.printParameters.printableList = res;
      this.setThePrintType(res);
    }

    //是否默认勾选
    if (this?.defaultCheck) {
      this.defaultSelectionOfPrintingType();
    }
    this.printParameters.loading = false;
  }

  return {
    printParameters,
    printObj,
    print,
    setPrinterData,
    breakTheLink,
    setThePrintType,
    getAPrintableList,
    setPrintingTypeManyPeople,
    connectPrint,
    exportPdf,
    startPrinting,
    retrieveFileStream,
    setPrinterType,
    updateProgress,
    updatePrintFlag,
    defaultSelectionOfPrintingType,
    setPrintTypeList
  };
};
