<template>
  <div
    ref="viewer"
    style="height: 100%"
    data-bind="dxReportViewer: $data"
  ></div>
</template>

<script>
import ko from 'knockout';
import 'devexpress-reporting/dx-webdocumentviewer';

export default {
  name: 'WebDocumentViewer',
  data() {
    return {
      previewModel: ko.observable()
    };
  },
  methods: {
    openReport() {
      // console.log(this.previewModel());
      // // this.previewModel().OpenReport("b7e7e5eb18774e10b117fdfb96dd6fd9");
      // this.previewModel().StartBuild();
      this.previewModel().GetParametersModel()['regNo']('230531000002');
      this.previewModel().StartBuild();
    }
  },
  mounted() {
    // var reportUrl = ko.observable("TestReport");
    // var requestOptions = {
    //     host: "https://localhost:44370/",
    //     invokeAction: "DXXRDV"
    // };
    var callbacks = {
      DocumentReady(s) {
        // s.GetReportPreview().zoom('Whole Page');
      }
    };

    const reportUrl = ko.observable(
      'ReportCode=b7e7e5eb18774e10b117fdfb96dd6fd9&regNo=230531000001'
    );
    console.log(11);
    const requestOptions = {
      host: 'http://*************:8088/',
      invokeAction: 'DXXRDV'
    };
    ko.applyBindings(
      {
        reportUrl: reportUrl,
        viewerModel: this.previewModel,
        requestOptions: requestOptions,
        callbacks: callbacks
      },
      this.$refs.viewer
    );
    setTimeout(() => {
      this.previewModel().StartBuild();
    }, 3000);
  },
  beforeDestroy() {
    ko.cleanNode(this.$refs.viewer);
  }
};
</script>
