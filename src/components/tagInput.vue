<!--
 * @FilePath: \shenshan\KrPeis\src\components\tagInput.vue
 * @Description:  封装标签输入框组件
 * @Author: justin
 * @Date: 2024-07-09 15:45:53
 * @Version: 0.0.1
 * @LastEditors: key
 * @LastEditTime: 2025-01-06 11:33:51
*
-->
<template>
  <div class="tag-input-container">
    <div
      :class="'body' + (divIsFocus ? ' body--focus' : '')"
      @click.stop="boxClick"
    >
      <!-- 标签 -->
      <div class="left-tag-box">
        <label
          v-for="(tag, index) in tags"
          :key="index"
          class="tag el-tag el-tag--light"
          @dragstart="tagDragStart(index)"
          @dragover.prevent
          @drop="tagDrop(index)"
          :draggable="draggable"
        >
          <!-- <i v-if="showMoveIcon" class="iconfont icon-tuozhuai tag-draggable">
          </i>
          <span
            :contenteditable="contenteditable"
            class="tag-content"
            :ref="'tag_content_ref' + index"
            @mouseover.stop="tagIsEdit = true"
            @mouseout.stop="tagIsEdit = spanIsFocus"
            @focus.stop="tagFoucs($event, tag, index)"
            @blur.stop="tagBlur($event, tag, index)"
          >
            <slot name="tag" :scope="tag" v-text="tag"></slot>
          </span> -->
          <!-- <i
            v-if="!disabled"
            class="el-tag__close el-icon-close"
            @click.stop="tagRemove(index)"
          ></i> -->
          <!-- <el-dropdown class="label_dropdown">
            <span class="el-dropdown-link">
                <i slot="reference" class="vxe-icon-ellipsis-v"></i>
            </span>
            <el-dropdown-menu slot="dropdown" class="labelDrop_menu">
              <el-dropdown-item @click.native="abnormal(tag)">
                <span>{{tag.abnormalType===0?'异常':'取消异常'}}</span>
              </el-dropdown-item>
              <el-dropdown-item @click.native="tagRemove(index)">
                  <span>删除小结</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown> -->
          <el-popover
            placement="top-start"
            title=""
            width="200"
            trigger="hover"
            popper-class="spanInput_popover"
            @hide="onPopoverHide"
          >
            <ul class="input_popover_ul">
              <li @click="abnormal(tag)">
                {{ tag.abnormalType === 0 ? '异常' : '取消异常' }}
              </li>
              <li @click="tagRemove(index)">删除小结</li>
            </ul>
            <!-- <span slot="reference"> -->
            <!-- <i v-if="showMoveIcon" class="iconfont icon-tuozhuai tag-draggable"></i> -->
            <span
              slot="reference"
              :contenteditable="contenteditable"
              class="tag-content"
              :class="{ abnormalColor: tag.abnormalType > 0 }"
              :ref="'tag_content_ref' + index"
              @mouseover.stop="tagIsEdit = true"
              @mouseout.stop="tagIsEdit = spanIsFocus"
              @focus.stop="tagFoucs($event, tag, index)"
              @blur.stop="tagBlur($event, tag, index)"
            >
              <slot name="tag" :scope="tag" v-text="tag"></slot>
            </span>
            <!-- </span> -->
          </el-popover>
        </label>

        <el-input
          v-if="!disabled"
          ref="input_ref"
          v-model.trim="inputValue"
          size="medium"
          class="input-box"
          @focus.stop="inputFocus"
          @blur.stop="inputBlur"
          @keydown.tab.stop.prevent.native="inputEnter"
          @keydown.enter.native="inputEnter"
          @keydown.delete.native="inputRemove"
          :autofocus="true"
        />
      </div>

      <div class="right-btn-box">
        <!-- 批量删除标签 -->
        <i
          v-if="!disabled && clearable && tags.length > 0"
          class="el-tag__close el-icon-close batch-clear"
          @click="allTagsRemove"
        ></i>
      </div>
    </div>

    <!-- 标签容器遮罩层 -->
    <div class="mask" v-if="divIsFocus" @click.stop="divIsFocus = false"></div>
  </div>
</template>

<script>
import { message } from '@/common/resetMessage';

export default {
  name: 'tagInput',
  props: {
    // 标签数组
    value: {
      type: Array,
      default: () => []
    },
    // 禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 最大标签数量
    maxTags: {
      type: Number,
      default: Infinity
    },
    // 允许重复标签
    allowDuplicates: {
      type: Boolean,
      default: true
    },
    // 标签可拖拽排序
    draggable: {
      type: Boolean,
      default: true
    },
    // 失焦自动回车添加标签
    blurOfAddTag: {
      type: Boolean,
      default: false
    },
    // 批量删除标签
    clearable: {
      type: Boolean,
      default: true
    },
    // 标签编辑回调函数
    tagEdit: {
      type: Function,
      default: (tags, index, newTag) => {
        tags.splice(index, 1, newTag);
        return tags;
      }
    },
    // 新增标签回调函数
    addTag: {
      type: Function,
      default: (tags, newTag) => {
        tags.push(newTag);
        return tags;
      }
    },
    // 标签编辑回调函数
    editTag: {
      type: Function,
      default: (tags, index, newTag) => {
        tags.splice(index, 1, newTag);
        return tags;
      }
    }
  },
  data() {
    return {
      tags: [...this.value], // 标签数组
      inputValue: '', // 输入框内容
      dragIndex: -1, // 拖拽标签索引
      divIsFocus: false, // 标签容器是否焦点
      tagIsEdit: false, // span是否被鼠标悬停（临时解决在y轴上点击父级元素，会触发编辑状态问题）
      spanIsFocus: false // span是否被聚焦（临时解决在y轴上点击父级元素，会触发编辑状态问题）
    };
  },
  computed: {
    /**
     * @author: justin
     * @description: 显示拖动图标
     * @return {*}
     */
    showMoveIcon() {
      return !this.disabled && this.draggable && this.divIsFocus;
    },
    /**
     * @author: justin
     * @description: 是否启用编辑状态
     * @return {*}
     */
    contenteditable() {
      return !this.disabled && this.tagIsEdit;
    }
  },
  methods: {
    // 销毁前的回调
    onPopoverHide() {
      let node = document.querySelector('body>.spanInput_popover'); //隐藏时删除
      node?.remove();
    },
    // 修改小结的异常状态
    abnormal(tag) {
      if (tag.abnormalType === 0) {
        tag.abnormalType = 1;
      } else {
        tag.abnormalType = 0;
      }
    },
    /**
     * @author: justin
     * @description: 点击盒子回调
     * @return {*}
     */
    boxClick(e) {
      if (
        e.target.classList.contains('left-tag-box') ||
        e.target.classList.contains('body')
      ) {
        this.tagIsEdit = false;
        this.focus();
      }

      this.divIsFocus = true;
    },

    /**
     * @author: justin
     * @description: 标签聚焦回调
     * @param {*} e
     * @param {*} oldTag
     * @param {*} index
     * @return {*}
     */
    tagFoucs(e, oldTag, index) {
      this.spanIsFocus = true;
      this.$emit('onTagFocus', e, oldTag, index);
    },

    /**
     * @author: justin
     * @description: 标签失焦回调
     * @param {*} e
     * @param {*} oldTag
     * @param {*} index
     * @return {*}
     */
    tagBlur(e, oldTag, index) {
      this.spanIsFocus = false;
      const newTag = e.target.innerText.trim();
      if (newTag) this.editTag(this.tags, index, newTag);
      else this.tags.splice(index, 1);
      this.$emit('onTagBlur', e, oldTag, index);
    },

    /**
     * @author: justin
     * @description: 删除标签
     * @param {*} index 索引
     * @return {*}
     */
    tagRemove(index) {
      if (this.tags && this.tags.length > 0) {
        this.tags.splice(index, 1);
        this.$emit('onTagRemove', this.tags, index);
      }
    },

    /**
     * @author: justin
     * @description: 新增标签
     * @return {*}
     */
    tagAdd() {
      if (this.tags.length >= this.maxTags) {
        return message.warning(`最多只能添加${this.maxTags}个标签！`);
      }
      if (this.inputValue) {
        if (this.tags.includes(this.inputValue) && !this.allowDuplicates) {
          return message.warning(`标签[${this.inputValue}]已存在！`);
        }

        this.addTag(this.tags, this.inputValue);
        this.inputValue = '';
        this.$emit('onAdd', this.tags);
      }
    },

    /**
     * @author: justin
     * @description: 删除所有标签
     * @return {*}
     */
    allTagsRemove(e) {
      if (!this.clearable) return;

      this.$confirm('确定要删除所有标签吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tags = [];
        this.inputValue = '';
        this.focus();
        this.$emit('onTagRemove', this.tags, -1);
      });
    },

    /**
     * @author: justin
     * @description: 开始拖拽标签
     * @param {*} index
     * @return {*}
     */
    tagDragStart(index) {
      this.dragIndex = index;
    },

    /**
     * @author: justin
     * @description:  标签拖放下
     * @param {*} index
     * @return {*}
     */
    tagDrop(index) {
      const dragIndex = this.dragIndex;
      const dropIndex = index;
      const tags = [...this.tags];
      const [draggedTag] = tags.splice(dragIndex, 1);
      tags.splice(dropIndex, 0, draggedTag);
      this.tags = tags;
      this.dragIndex = -1;
    },

    /**
     * @author: justin
     * @description: 设置输入框内容
     * @param {*} value
     * @return {*}
     */
    setInputValue(value) {
      this.inputValue = (value || '').trim();
    },

    /**
     * @author: justin
     * @description: 输入框聚焦回调
     * @param {*} e 事件对象
     * @return {*}
     */
    inputFocus(e) {
      this.divIsFocus = true;
      this.$emit('onInputFocus', e);
    },

    /**
     * @author: justin
     * @description: 输入框失去焦点回调
     * @param {*} e 事件对象
     * @return {*}
     */
    inputBlur(e) {
      this.$emit('onInputBlur', e, this.tags, this.inputValue);
      if (!this.blurOfAddTag) return;
      this.tagAdd();
      this.$nextTick(() => {
        this.$emit('onAddTag', e, this.tags, this.inputValue);
      });
    },

    /**
     * @author: justin
     * @description: 输入框回车回调
     * @param {*} e 事件对象
     * @return {*}
     */
    inputEnter(e) {
      this.tagAdd();
      this.$nextTick(() => {
        this.$emit('onAddTag', e, this.tags, this.inputValue);
      });
    },

    /**
     * @author: justin
     * @description: 输入框删除内容回调
     * @param {*} e
     * @return {*}
     */
    inputRemove(e) {
      if (!this.inputValue) {
        this.tagRemove(this.tags.length - 1);
        this.$nextTick(() => {
          this.blur();
          this.focus();
        });
      }
    },

    /**
     * @author: justin
     * @description: 输入聚焦
     * @return {*}
     */
    focus() {
      if (!this.$refs.input_ref) return;
      this.$refs.input_ref.focus();
      this.divIsFocus = true;
    },

    /**
     * @author: justin
     * @description: 输入失焦
     * @return {*}
     */
    blur() {
      if (!this.$refs.input_ref) return;
      this.$refs.input_ref.blur();
      this.divIsFocus = false;
    },

    /**
     * @author: justin
     * @description: 标签聚焦
     * @param {*} index 标签索引
     * @return {*}
     */
    focusTag(index) {
      if (this.disabled || !this.tags[index]) return;

      this.tagIsEdit = true;
      this.$nextTick(() => {
        if (!this.$refs[`tag_content_ref${index}`]) return;
        this.$refs[`tag_content_ref${index}`][0].focus();
        this.divIsFocus = true;
      });
    },

    /**
     * @author: justin
     * @description: 标签失焦
     * @param {*} index 标签索引
     * @return {*}
     */
    blurTag(index) {
      if (this.disabled || !this.tags[index]) return;

      this.tagIsEdit = false;
      this.$nextTick(() => {
        if (!this.$refs[`tag_content_ref${index}`]) return;
        this.$refs[`tag_content_ref${index}`][0].blur();
        this.divIsFocus = false;
      });
    }
  },
  watch: {
    value: {
      handler(newVal, oldVal) {
        this.tags = newVal;
      },
      immediate: true,
      deep: true
    },
    tags: {
      handler(newVal, oldVal) {
        this.$nextTick(() => {
          this.$emit('update:value', newVal);
          this.$emit('onChanged', newVal);
        });
      },
      immediate: true,
      deep: true
    }
  }
};
</script>

<style lang="less" scoped>
@tag-color: #409eff;
.tag-input-container {
  width: 100%;
  height: 100%;

  .label_dropdown {
    height: 100%;
    color: #bbb;
    span {
      display: block;
      height: 100%;
    }
  }
  .body {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    padding: 0 5px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;

    position: relative;
    z-index: 2;

    &:hover {
      border-color: #c0c4cc;
    }

    &.body--focus {
      border-color: @tag-color;
    }

    .left-tag-box {
      flex: 1;
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
      overflow: auto;
      height: 100%;

      .tag {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 3px 5px 3px 0;
        height: auto !important;
        padding: 0 5px !important;
        line-height: 25px !important;

        .tag-draggable {
          margin: 0 3px 0 -3px;
          cursor: move;

          &:has(~ .tag-content:focus) {
            display: none;
          }

          &:has(~ .tag-content:blur) {
            display: block;
          }
        }

        .tag-content {
          display: inline-block;
          outline: none;
          cursor: text;
          caret-color: black;
          padding: 2px;
          font-size: 16px;
          font-weight: bold;
          overflow-wrap: break-word;
          white-space: pre-wrap;
          word-break: break-all;

          &:focus {
            margin: 0 -5px;
            color: #606266;
            transition: border-color 0.3s ease;
            border: 1px solid @tag-color;
            border-radius: 4px;
            min-width: 50px;

            & ~ i {
              display: none;
            }
          }

          &:blur ~ i {
            display: block;
          }
        }
        // 异常文字颜色
        .abnormalColor {
          color: #d63031;
        }
        .el-icon-close {
          border-radius: 50%;
          position: relative;
          cursor: pointer;
          font-size: 12px;
          top: 0;
          right: -2px;
        }
      }

      .input-box {
        flex: 1;
        min-width: 50px;

        /deep/ .el-input__inner {
          border: unset !important;
          padding: unset !important;
          font-size: 14px;
          color: #606266;

          &:focus {
            border-color: unset !important;
            background-color: unset !important;
          }
        }
      }
    }
  }

  .right-btn-box {
    .batch-clear {
      border-radius: 50%;
      text-align: center;
      position: relative;
      cursor: pointer;
      font-size: 14px;
      height: 20px;
      width: 20px;
      line-height: 20px;
      vertical-align: middle;
      top: 0;
      right: 0;
      color: #c0c4cc;
      background-color: #d6dde991;
    }
  }

  .el-tag__close:hover {
    color: #fff;
    background-color: @tag-color;
  }

  .mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
  }
}
.input_popover_ul {
  li {
    line-height: 30px;
    cursor: pointer;
    &:hover {
      background-color: #ecf5ff;
      color: #66b1ff;
    }
  }
}
</style>
