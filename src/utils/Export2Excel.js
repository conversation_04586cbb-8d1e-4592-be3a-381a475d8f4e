// xlsx插件包参考文档： https://xlsx.nodejs.cn/

/* eslint-disable */
require('script-loader!file-saver');
require('./Blob.js');
require('script-loader!xlsx/dist/xlsx.core.min');

function datenum(v, date1904) {
  if (date1904) v += 1462;
  var epoch = Date.parse(v);
  return (epoch - new Date(Date.UTC(1899, 11, 30))) / (24 * 60 * 60 * 1000);
}

function sheet_from_array_of_arrays(data, opts) {
  var ws = {};
  var range = { s: { c: 10000000, r: 10000000 }, e: { c: 0, r: 0 } };
  for (var R = 0; R != data.length; ++R) {
    for (var C = 0; C != data[R].length; ++C) {
      if (range.s.r > R) range.s.r = R;
      if (range.s.c > C) range.s.c = C;
      if (range.e.r < R) range.e.r = R;
      if (range.e.c < C) range.e.c = C;
      var cell = { v: data[R][C] };
      if (cell.v == null) continue;
      var cell_ref = XLSX.utils.encode_cell({ c: C, r: R });

      if (typeof cell.v === 'number') cell.t = 'n';
      else if (typeof cell.v === 'boolean') cell.t = 'b';
      else if (cell.v instanceof Date) {
        cell.t = 'n';
        cell.z = XLSX.SSF._table[14];
        cell.v = datenum(cell.v);
      } else cell.t = 's';

      ws[cell_ref] = cell;
    }
  }
  if (range.s.c < 10000000) ws['!ref'] = XLSX.utils.encode_range(range);
  return ws;
}

function Workbook() {
  if (!(this instanceof Workbook)) return new Workbook();
  this.SheetNames = [];
  this.Sheets = {};
}

function s2ab(s) {
  var buf = new ArrayBuffer(s.length);
  var view = new Uint8Array(buf);
  for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xff;
  return buf;
}

/**
 * @author: justin
 * @description: 设置列
 * @param {*} ws worksheet
 * @link https://xlsx.nodejs.cn/docs/csf/features/colprops
 **/
function setColum(ws) {
  if (!ws) return;
  const rang = ws['!ref'].split(':'); // 'A1:E33'
  const startCell = XLSX.utils.decode_cell(rang[0]);
  const endCell = XLSX.utils.decode_cell(rang[1]);
  let cols = [];
  for (let i = startCell.c; i <= endCell.c; i++) {
    let val = '';
    for (let j = startCell.r; j <= endCell.r; j++) {
      const cell = ws[XLSX.utils.encode_cell({ c: i, r: j })];
      if (cell && cell.v && cell.v.toString().length > val.length)
        val = cell.v.toString();
    }

    let style = {};
    if (!val) {
      style = { wch: 10 };
    } else if (val.charCodeAt(0) > 255) {
      /*再判断是否为中文*/
      style = { wch: val.length * 2 };
    } else {
      style = { wch: val.length * 1.5 };
    }

    cols.push(style);
  }

  ws['!cols'] = cols;
}

export function export_json_to_excel(th, jsonData, defaultTitle) {
  /* original data */

  var data = jsonData;
  data.unshift(th);
  var ws_name = 'SheetJS';

  var wb = new Workbook(),
    ws = sheet_from_array_of_arrays(data);

  /*设置worksheet每列的最大宽度*/
  const colWidth = data.map((row) =>
    row.map((val) => {
      /*先判断是否为null/undefined*/
      if (val == null) {
        return { wch: 10 };
      } else if (val.toString().charCodeAt(0) > 255) {
        /*再判断是否为中文*/
        return { wch: val.toString().length * 2 };
      } else {
        return { wch: val.toString().length };
      }
    })
  );
  /*以第一行为初始值*/
  let result = colWidth[0];
  for (let i = 1; i < colWidth.length; i++) {
    for (let j = 0; j < colWidth[i].length; j++) {
      if (result[j]['wch'] < colWidth[i][j]['wch']) {
        result[j]['wch'] = colWidth[i][j]['wch'];
      }
    }
  }
  ws['!cols'] = result;
  /* add worksheet to workbook */
  wb.SheetNames.push(ws_name);
  wb.Sheets[ws_name] = ws;

  var wbout = XLSX.write(wb, {
    bookType: 'xlsx',
    bookSST: false,
    type: 'binary'
  });
  var title = defaultTitle || '列表';
  saveAs(
    new Blob([s2ab(wbout)], { type: 'application/octet-stream' }),
    title + '.xlsx'
  );
}

/**
 * @author: justin
 * @description: 单个el-table组件导出excel文件
 * @param {*} $ref el-table组件的ref
 * @param {*} excelName 导出的文件名
 * @return {*}
 * @link https://xlsx.nodejs.cn/docs/api/utilities/html
 **/
export function export_table_to_excel($ref, excelName = '列表') {
  try {
    if (!$ref) return;
    const $e = $ref.$el;
    let $table = $e.querySelector('.el-table__fixed') || $e;
    if (!$table) return;

    const ws = XLSX.utils.table_to_sheet($table, { raw: true });
    if (!ws) return;

    setColum(ws);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, `${excelName}`);
    const wbout = XLSX.write(wb, {
      bookType: 'xlsx',
      bookSST: false,
      type: 'binary'
    });
    saveAs(
      new Blob([s2ab(wbout)], { type: 'application/octet-stream' }),
      `${excelName}.xlsx`
    );
  } catch (e) {
    if (typeof console !== 'undefined') console.error(e);
  }
}

/**
 * @author: justin
 * @description: 多个el-table组件导出excel文件（只有一个页签）
 * @param {*} $refs el-table组件的ref数组
 * @param {*} tableNames 表格名称数组
 * @param {*} excelName 导出的文件名
 * @param {*} nrows 表格间的间隔行数
 * @return {*}
 * @link https://xlsx.nodejs.cn/docs/api/utilities/html
 **/
export function export_multi_table_to_excel(
  $refs,
  tableNames,
  excelName = '列表',
  nrows = 2
) {
  try {
    if (!$refs || !tableNames) return;

    // 表格间的间隔行数
    const create_gap_rows = (ws, nrows) => {
      var ref = XLSX.utils.decode_range(ws['!ref']); // get original range
      ref.e.r += nrows; // add to ending row
      ws['!ref'] = XLSX.utils.encode_range(ref); // reassign row
    };

    const ws = XLSX.utils.aoa_to_sheet([]);
    for (let i = 0; i < $refs.length; i++) {
      const $e = $refs[i].$el;
      let $table = $e.querySelector('.el-table__fixed') || $e;
      if (!$table) continue;

      //origin 默认值是从表格的最开始即A1开始追加，会覆盖前一个表格
      //origin：-1 表示从上一个表格的末尾行追加
      //origin: {c:4, r:0} cellAddress 会从这个单元格的位置开始追加
      XLSX.utils.sheet_add_aoa(ws, [[tableNames[i]]], {
        origin: i > 0 ? -1 : 0
      });
      XLSX.utils.sheet_add_dom(ws, $table, {
        raw: true,
        origin: -1
      });
      create_gap_rows(ws, nrows);
    }

    if (!ws) return;

    setColum(ws);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, `${excelName}`);
    const wbout = XLSX.write(wb, {
      bookType: 'xlsx',
      bookSST: false,
      type: 'binary'
    });
    saveAs(
      new Blob([s2ab(wbout)], { type: 'application/octet-stream' }),
      `${excelName}.xlsx`
    );
  } catch (e) {
    if (typeof console !== 'undefined') console.error(e);
  }
}
