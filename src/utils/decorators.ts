/**
 * 自定义装饰器工具集
 * 这个文件展示了如何创建和使用自定义TypeScript装饰器
 */

// ====== 类装饰器 ======

/**
 * 为类添加版本信息的装饰器
 * @param version 版本号
 */
export function Version(version: string) {
  return function <T extends { new (...args: any[]): {} }>(constructor: T) {
    return class extends constructor {
      __version = version;
    };
  };
}

/**
 * 为类添加时间戳的装饰器
 */
export function Timestamped<T extends { new (...args: any[]): {} }>(
  constructor: T
) {
  return class extends constructor {
    timestamp = new Date();
  };
}

/**
 * 为类添加日志功能的装饰器
 */
export function Logger<T extends { new (...args: any[]): {} }>(constructor: T) {
  // 保存原始构造函数的引用
  const original = constructor;

  // 创建一个新的构造函数
  const newConstructor: any = function (...args: any[]) {
    console.log(`创建新的 ${original.name} 实例`);
    return new original(...args);
  };

  // 复制原型链
  newConstructor.prototype = original.prototype;

  // 返回新的构造函数
  return newConstructor;
}

// ====== 方法装饰器 ======

/**
 * 记录方法执行时间的装饰器
 */
export function Measure(
  target: any,
  propertyKey: string,
  descriptor: PropertyDescriptor
) {
  const originalMethod = descriptor.value;

  descriptor.value = function (...args: any[]) {
    const start = performance.now();
    const result = originalMethod.apply(this, args);
    const finish = performance.now();
    console.log(`${propertyKey} 执行时间: ${finish - start} 毫秒`);
    return result;
  };

  return descriptor;
}

/**
 * 防抖装饰器
 * @param delay 延迟时间（毫秒）
 */
export function Debounce(delay: number = 300) {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;
    let timer: number | null = null;

    descriptor.value = function (...args: any[]) {
      const context = this;
      if (timer) {
        clearTimeout(timer);
      }
      timer = window.setTimeout(() => {
        originalMethod.apply(context, args);
      }, delay);
    };

    return descriptor;
  };
}

/**
 * 节流装饰器
 * @param limit 时间限制（毫秒）
 */
export function Throttle(limit: number = 300) {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;
    let lastExecution = 0;

    descriptor.value = function (...args: any[]) {
      const now = Date.now();
      if (now - lastExecution >= limit) {
        lastExecution = now;
        return originalMethod.apply(this, args);
      }
    };

    return descriptor;
  };
}

/**
 * 重试装饰器
 * @param attempts 尝试次数
 * @param delay 尝试间隔（毫秒）
 */
export function Retry(attempts: number = 3, delay: number = 500) {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      let error: any;
      for (let i = 0; i < attempts; i++) {
        try {
          return await originalMethod.apply(this, args);
        } catch (e) {
          error = e;
          console.log(
            `尝试 ${i + 1}/${attempts} 失败，等待 ${delay}ms 后重试...`
          );
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      }
      throw error;
    };

    return descriptor;
  };
}

// ====== 属性装饰器 ======

/**
 * 为属性添加前缀的装饰器
 * @param prefix 前缀
 */
export function Prefix(prefix: string) {
  return function (target: any, propertyKey: string) {
    let value: string = target[propertyKey];

    const getter = function () {
      return value;
    };

    const setter = function (newValue: string) {
      value = `${prefix}${newValue}`;
    };

    Object.defineProperty(target, propertyKey, {
      get: getter,
      set: setter,
      enumerable: true,
      configurable: true
    });
  };
}

/**
 * 将属性值转换为大写的装饰器
 */
export function Uppercase(target: any, propertyKey: string) {
  let value: string = target[propertyKey];

  const getter = function () {
    return value;
  };

  const setter = function (newValue: string) {
    value = newValue.toUpperCase();
  };

  Object.defineProperty(target, propertyKey, {
    get: getter,
    set: setter,
    enumerable: true,
    configurable: true
  });
}

// ====== 参数装饰器 ======

/**
 * 验证参数不为空的装饰器
 */
export function Required(
  target: any,
  propertyKey: string,
  parameterIndex: number
) {
  const originalMethod = target[propertyKey];

  target[propertyKey] = function (...args: any[]) {
    if (args[parameterIndex] === undefined || args[parameterIndex] === null) {
      throw new Error(
        `参数 ${parameterIndex} 在方法 ${propertyKey} 中是必需的`
      );
    }
    return originalMethod.apply(this, args);
  };
}

/**
 * 验证参数类型的装饰器
 * @param type 期望的类型
 */
export function TypeCheck(type: string) {
  return function (target: any, propertyKey: string, parameterIndex: number) {
    const originalMethod = target[propertyKey];

    target[propertyKey] = function (...args: any[]) {
      if (typeof args[parameterIndex] !== type) {
        throw new Error(
          `参数 ${parameterIndex} 在方法 ${propertyKey} 中必须是 ${type} 类型`
        );
      }
      return originalMethod.apply(this, args);
    };
  };
}
