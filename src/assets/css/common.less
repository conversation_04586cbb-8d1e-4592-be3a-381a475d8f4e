.blue_btn {
  background-color: #1770df !important;
  // border-color: #1770df !important;
  color: #fff !important;
}

// 紫色按钮
.violet_btn {
  background: #7364f4 !important;
  // border-color: #7364f4 !important;
  color: #fff !important;
}
// 黄色按钮
.yellow_btn {
  background: #fab63b !important;
  // border-color: #FAB63B !important;
  color: #fff !important;
}
// 绿色按钮
.green_btn {
  background: #3cb34f !important;
  color: #fff !important;
}
// 红色按钮
.red_btn {
  background: #d63031 !important;
  // border-color: #D63031 !important;
  color: #fff !important;
}
// 棕色按钮
.brown_btn {
  background: #a96d00 !important;
  // border-color: #A96D00 !important;
  color: #fff !important;
}
//白色按钮
.white_btn {
  background: #fff !important;
  // border-color: #fff !important;
  color: rgba(23, 112, 223, 1) !important;
}

.blue_btn:active,
.violet_btn:active,
.yellow_btn:active,
.green_btn:active,
.red_btn:active,
.brown_btn:active,
.white_btn:active,
.red_btn:active {
  opacity: 0.8;
}
.blue_btn.is-disabled,
.violet_btn.is-disabled,
.yellow_btn.is-disabled,
.green_btn.is-disabled,
.red_btn.is-disabled,
.brown_btn.is-disabled {
  opacity: 0.6;
}
.cell_blue {
  color: #1770df;
}

.cell_red {
  color: #d63031;
}
.cell_yellow {
  color: #fab63b;
}
.cell_green {
  color: #3cb34f;
}
.cell_violet {
  color: #7364f4;
}

// 已支付的字体颜色
.havePay_text {
  color: #3cb34f;
}
// 未支付的字体颜色
.noPay_text {
  color: #fab63b;
}

.current-row td {
  border-top: 2px solid #1770df;
  border-bottom: 2px solid #1770df !important;
  &:first-child {
    border-left: 2px solid #1770df;
  }
  &:last-child {
    border-right: 2px solid #1770df;
  }
}
.mask {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
}
// .el-button {
//   background: #079c66 !important;
//   color: #fff !important;
// }

// 男女图标
.el-icon-male {
  border-radius: 100%;
  color: #fff;
  text-align: center;
  padding: 3px;
  background: #037bff;
}
.el-icon-female {
  border-radius: 100%;
  color: #fff;
  text-align: center;
  padding: 3px;
  background: #fc73a7;
}
@media screen and (max-width: 1440px) {
  .el-button {
    padding: 5px !important;
  }
  .el-input input {
    height: 26px !important;
    line-height: 26px !important;
    padding-left: 5px !important;
  }
  .el-table,
  .vxe-table {
    .el-table__cell,
    .vxe-body--column,
    .vxe-header--column {
      padding: 3px 0 !important;
      .cell,
      .vxe-cell {
        padding: 0 3px !important;
      }
    }
  }
  .el-tag {
    height: 22px !important;
    line-height: 22px !important;
    padding: 0 5px !important;
  }
}
