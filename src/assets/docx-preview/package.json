{"name": "docx-preview", "version": "0.3.2", "license": "Apache-2.0", "keywords": ["word", "docx"], "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "repository": {"type": "git", "url": "git+https://github.com/zVolodymyr/docxjs.git"}, "dependencies": {"jszip": ">=3.0.0"}, "devDependencies": {"rollup": "^4.9.1", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^11.1.5", "diff": "^5.0.0", "jasmine-core": "^5.1.0", "karma": "^6.3.9", "karma-chrome-launcher": "^3.1.0", "karma-firefox-launcher": "^2.1.2", "karma-jasmine": "^5.0.0", "tslib": "^2.4.0", "typescript": "^5.0.3"}, "scripts": {"build": "rollup --config rollup.config.mjs", "build-prod": "rollup --config rollup.config.mjs --environment BUILD:production", "watch": "rollup --config rollup.config.mjs --watch", "e2e": "karma start karma.conf.cjs --single-run", "e2e-watch": "karma start karma.conf.cjs"}, "files": ["dist"], "exports": {".": {"import": "./dist/docx-preview.mjs", "require": "./dist/docx-preview.js", "types": "./dist/docx-preview.d.ts"}}, "types": "dist/docx-preview.d.ts"}