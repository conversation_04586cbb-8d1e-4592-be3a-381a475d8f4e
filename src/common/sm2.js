const sm2 = require('sm-crypto').sm2;

const publicKey = `04FC2AD64BE14D3C2858F33D5218B3A499FFAE6803F66F31C1E25C7F6B0A2A43B5120B49B8B8C892BDFFB27499A8E35134FFF20EDB7E1DF68AFE0B14C57C8493A2`;
const cipherMode = 0; // 1 - C1C3C2，0 - C1C2C3，默认为1
export const smEncrypt = {
  //加密方法
  EncryptData(str) {
    let encryptData = sm2.doEncrypt(str, publicKey, cipherMode); // 加密结果
    return '04' + encryptData;
  },
  // 解密
  DecryptData(str) {
    let needval = str.substring(2); //val是后台传过来的加密字段，将其前边的‘04’截取掉
    let decryptData = sm2.doDecrypt(needval, privceKey, cipherMode); // 解密结果

    return decryptData;
  }
};
