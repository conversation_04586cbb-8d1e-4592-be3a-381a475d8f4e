class TaskQueue {
  queue = [];
  isProcessing = false;
  constructor() {
    this.queue = [];
    this.isProcessing = false;
  }
  /**
   * <AUTHOR> justin
   * @description  :  添加任务到队列
   * @param         {*} task:
   * @return        {*}
   **/
  enqueue(task) {
    this.queue.push(task);
    this.processQueue();
  }

  /**
   * <AUTHOR> justin
   * @description  :  处理队列中的任务
   * @return        {*}
   **/
  async processQueue() {
    if (this.isProcessing) return; // 若正在处理，则返回
    this.isProcessing = true;

    while (this.queue.length > 0) {
      const task = this.queue.shift(); // 从队列中取出任务
      try {
        await task(); // 等待任务执行完成
      } catch (error) {
        console.error('任务执行出错:', error);
      }
    }

    this.isProcessing = false;
  }

  /**
   * <AUTHOR> justin
   * @description  : 是否是Promise对象
   * @param         {*} obj:
   * @return        {*}
   **/
  isPromise(obj) {
    return obj instanceof Promise;
  }
}

export default TaskQueue;
