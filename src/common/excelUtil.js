import ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';

function export_table_to_excel_no_grid(th, jsonData, defaultTitle) {
  var data = jsonData;
  data.unshift(th);
  var ws_name = 'SheetJS';

  // 创建一个新的工作簿
  var workbook = new ExcelJS.Workbook();
  var worksheet = workbook.addWorksheet(ws_name, {
    views: [{ showGridLines: false }]
  });

  // 设置每列的最大宽度
  const colWidth = data.map((row) =>
    row.map((val) => {
      if (val == null) {
        return { wch: 10 };
      } else if (val.toString().charCodeAt(0) > 255) {
        return { wch: val.toString().length * 2 };
      } else {
        return { wch: val.toString().length };
      }
    })
  );
  let result = colWidth[0];
  for (let i = 1; i < colWidth.length; i++) {
    for (let j = 0; j < colWidth[i].length; j++) {
      if (result[j]['wch'] < colWidth[i][j]['wch']) {
        result[j]['wch'] = colWidth[i][j]['wch'];
      }
    }
  }
  result.forEach((width, index) => {
    worksheet.getColumn(index + 1).width = width.wch;
  });

  // 设置行高
  const rowHeight = 20;
  data.forEach((row, rowIndex) => {
    worksheet.getRow(rowIndex + 1).height = rowHeight;
  });

  // 添加数据到工作表
  data.forEach((row, rowIndex) => {
    row.forEach((cell, colIndex) => {
      worksheet.getCell(rowIndex + 1, colIndex + 1).value = cell;
    });
  });

  // 生成Excel文件并下载
  workbook.xlsx.writeBuffer().then((buffer) => {
    saveAs(
      new Blob([buffer], { type: 'application/octet-stream' }),
      defaultTitle + '.xlsx'
    );
  });
}

export function export2Excel(columns, list, excelName) {
  list = list.map((item, index) => {
    if (index > 0) {
      if (item.sex == 2) {
        item.sex = '女';
      } else if (item.sex == 1) {
        item.sex = '男';
      }
    }
    return item;
  });
  require.ensure([], () => {
    let tHeader = [];
    let filterVal = [];
    if (!columns) {
      return;
    }
    columns.forEach((item) => {
      tHeader.push(item.title);
      filterVal.push(item.key);
    });
    const data = list.map((v) => filterVal.map((j) => v[j]));
    export_table_to_excel_no_grid(tHeader, data, excelName);
  });
}
