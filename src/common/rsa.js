/* 引入jsencrypt实现数据RSA加密 */
import JSEncrypt from 'jsencrypt'; // 处理长文本数据时报错 jsencrypt.js Message too long for RSA
/* 引入encryptlong实现数据RSA加密 */
import Encrypt from 'encryptlong'; // encryptlong是基于jsencrypt扩展的长文本分段加解密功能。

// 公钥key
// const publicKey =
//     "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBANL378k3RiZHWx5AfJqdH9xRNBmD9wGD\n" +
//     "2iRe41HdTNF8RUhNnHit5NpMNtGL0NPTSSpPjjI1kJfVorRvaQerUgkCAwEAAQ==";

const publicKey =
  'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsT2DEK56Xaojch9vhkbIF19eU8kY21EpZw5dXiTYvRvLoUC2F0jRJASPyvVUmJQmAwfnoqafRuYSxXqCzdW+ON51u5peeOM2N9tpASJ7AnGK05/VgY/gdInoYKFJsXzEftIGLNsiy9h4StyqvdnYmegHk1JMtEuriBo78RY5JUS08SiyTNXAl4zEyswaZPSq8r2/pCy795TuYgRREORu7+Z95XOKVZL5G64SgU0J3sAKs868cPjfk5ZogVC+0tFpM+ZAeIKEhPfFr+XzatUaoqFdli/8c85KeejJww2Z3vuCx5S9tczQbbHKx7pTNUan9GgE4I/vO47zq1nkM2xtDwIDAQAB';
// 私钥key
const privateKey =
  'MIIBUwIBADANBgkqhkiG9w0BAQEFAASCAT0wggE5AgEAAkEA0vfvyTdGJkdbHkB8\n' +
  'mp0f3FE0GYP3AYPaJF7jUd1M0XxFSE2ceK3k2kw20YvQ09NJKk+OMjWQl9WitG9p\n' +
  'B6tSCQIDAQABAkA2SimBrWC2/wvauBuYqjCFwLvYiRYqZKThUS3MZlebXJiLB+Ue\n' +
  '/gUifAAKIg1avttUZsHBHrop4qfJCwAI0+YRAiEA+W3NK/RaXtnRqmoUUkb59zsZ\n' +
  'UBLpvZgQPfj1MhyHDz0CIQDYhsAhPJ3mgS64NbUZmGWuuNKp5coY2GIj/zYDMJp6\n' +
  'vQIgUueLFXv/eZ1ekgz2Oi67MNCk5jeTF2BurZqNLR3MSmUCIFT3Q6uHMtsB9Eha\n' +
  '4u7hS31tj1UWE+D+ADzp59MGnoftAiBeHT7gDMuqeJHPL4b+kC+gzV4FGTfhR9q3\n' +
  'tTbklZkD2A==';

export const Rsa = {
  // RSA分段加密，支持中文
  RsaEncryptUnicodeLong(data) {
    let string = '';
    if (typeof data == 'string') {
      string = data;
    } else {
      string = JSON.stringify(data);
    }

    let encryptor = new JSEncrypt();
    encryptor.setPublicKey(publicKey); // 设置公钥
    var k = encryptor.getKey();
    //根据key所能编码的最大长度来定分段长度。key size - 11：11字节随机padding使每次加密结果都不同。
    var maxLength = ((k.n.bitLength() + 7) >> 3) - 11;
    try {
      var subStr = '',
        encryptedString = '';
      var subStart = 0,
        subEnd = 0;
      var bitLen = 0,
        tmpPoint = 0;
      for (var i = 0, len = string.length; i < len; i++) {
        //js 是使用 Unicode 编码的，每个字符所占用的字节数不同
        var charCode = string.charCodeAt(i);
        if (charCode <= 0x007f) {
          bitLen += 1;
        } else if (charCode <= 0x07ff) {
          bitLen += 2;
        } else if (charCode <= 0xffff) {
          bitLen += 3;
        } else {
          bitLen += 4;
        }
        //字节数到达上限，获取子字符串加密并追加到总字符串后。更新下一个字符串起始位置及字节计算。
        if (bitLen > maxLength) {
          subStr = string.substring(subStart, subEnd);
          encryptedString += k.encrypt(subStr);
          subStart = subEnd;
          bitLen = bitLen - tmpPoint;
        } else {
          subEnd = i;
          tmpPoint = bitLen;
        }
      }
      subStr = string.substring(subStart, len);
      encryptedString += k.encrypt(subStr);
      return this.hex2b64(encryptedString);
    } catch (ex) {
      return '';
    }
  },
  // RSA分段解密，支持中文
  RsaDecryptUnicodeLong(string, privateKey) {
    //     let defaultPrivateKey = `MIICWwIBAAKBgH801AwoxqsS/GYmtUJvUEKG+CazWkXtmjmNoJ4ssZQg3aUwomAA
    // hBBv5GmYwIilXVBDz5mA8qyRxO+pkjYj+/AZlDZIF1ees17GPL0wHsP1FBdI0ohD
    // c7CZFV70K+zTKw2xLG2CCCkR1N9xXDCm+L5Qpzfwv7vu0T7SQD8IJgsDAgMBAAEC
    // gYBnpYzNZ4AQrkSXmxx/yCEWQ9D3/5Ujeyj5kgt4NiRu9KSET29OV71Dg1gSLlNa
    // Q5sXplkF00poD9HuETXABWvmHVSDueLe8f/eWP/FSYuZVOS4OKdqXd3/pQEbzzsM
    // EwFdj312fDk7K8Th9qhDFR4zSW8zCT5tY7p+Y4BLpypygQJBALk9wUp7jWLbLssC
    // 07sfbt+x38v0F7AeXRYiXCkM2ok33c4swDQdRs9QRyX79TH3Ibz7iMDbSDG/XmLR
    // awXZaMMCQQCvy/+XRB8CJzij31Xvs+5vS/ULad3LFQVB6K9xD2XoQi51caRyRcN9
    // xwwS96ybAzX+l0/b75mimO4/QjCIaLDBAkBTLnT+sk6CBrSTeviC/ZF3J9O8LSb5
    // 8hiQ2OsTj+8OUSTr8VJ51G+4pm7ckrC/OB9RUo5NM+rOVAXZT/rEDKWrAkEArVAe
    // LKBhPbszMQQG8infIOSuslDt88BGjaL8DCfVHTTaHrkqGerDf6YUNkLtbWmt+tBs
    // T/WY7t98yurTdDhaQQJAfaeiyZCqGjzWE5BrofGUxr1hjbdxYtdSPk6yiuBOgMnK
    // 3eenlnAqi3SZXEVS3gAUBou5s9vygeyibZEHWwaADw==`;//私钥

    let encryptor = new JSEncrypt();
    encryptor.setPrivateKey(privateKey || defaultPrivateKey);
    var k = encryptor.getKey();
    //解密长度=key size.hex2b64结果是每字节每两字符，所以直接*2
    var maxLength = ((k.n.bitLength() + 7) >> 3) * 2;
    try {
      var hexString = this.b64tohex(string);
      var decryptedString = '';
      var rexStr = '.{1,' + maxLength + '}';
      var rex = new RegExp(rexStr, 'g');
      var subStrArray = hexString.match(rex);
      if (subStrArray) {
        subStrArray.forEach(function (entry) {
          decryptedString += k.decrypt(entry);
        });
        return decryptedString;
      }
    } catch (ex) {
      return false;
    }
  },
  hex2b64(h) {
    var b64map =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
    var b64pad = '=';
    var i;
    var c;
    var ret = '';
    for (i = 0; i + 3 <= h.length; i += 3) {
      c = parseInt(h.substring(i, i + 3), 16);
      ret += b64map.charAt(c >> 6) + b64map.charAt(c & 63);
    }
    if (i + 1 == h.length) {
      c = parseInt(h.substring(i, i + 1), 16);
      ret += b64map.charAt(c << 2);
    } else if (i + 2 == h.length) {
      c = parseInt(h.substring(i, i + 2), 16);
      ret += b64map.charAt(c >> 2) + b64map.charAt((c & 3) << 4);
    }
    while ((ret.length & 3) > 0) {
      ret += b64pad;
    }
    return ret;
  },
  b64tohex(str) {
    for (
      var i = 0, bin = atob(str.replace(/[ \r\n]+$/, '')), hex = [];
      i < bin.length;
      ++i
    ) {
      var tmp = bin.charCodeAt(i).toString(16);
      if (tmp.length === 1) tmp = '0' + tmp;
      hex[hex.length] = tmp;
    }
    return hex.join('');
  }
};
