class WebStorage {
  constructor(instance) {
    this.instance = instance;
  }

  get(key) {
    // return encrypt.DecryptStorage(this.instance.getItem(key));
    try {
      var keys = JSON.parse(this.instance.getItem(key));
      return keys;
    } catch (error) {
      return this.instance.getItem(key);
    }
  }

  set(key, value) {
    if (typeof value === 'string') {
      this.instance.setItem(key, value);
    } else {
      this.instance.setItem(key, JSON.stringify(value));
    }
    return;
    // 发布环境下的加密
    if (typeof value === 'string') {
      this.instance.setItem(key, encrypt.EncryptStorage(value));
    } else {
      this.instance.setItem(key, encrypt.EncryptStorage(JSON.stringify(value)));
    }
    // this.instance.setItem(key, encrypt.EncryptStorage(value));
  }

  delete(key) {
    this.instance.removeItem(key);
  }

  clear() {
    this.instance.clear();
  }
}

const memoryMap = new Map();

export const storage = {
  local: new WebStorage(localStorage),
  session: new WebStorage(sessionStorage)
};
