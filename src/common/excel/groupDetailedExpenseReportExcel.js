import ExcelJS from 'exceljs';
import { mapGetters } from 'vuex';
import { dataUtils } from '@/common';
export default {
  computed: {
    ...mapGetters(['G_EnumList'])
  },
  data() {
    return {
      dataList: [
        {
          activeTime: '2024-07-16 16:56:20',
          age: 34,
          chargeTime: null,
          clusterName: '青年套餐(女)',
          companyName: '',
          name: '测试病理0326',
          price: 1553.46,
          regNo: '624071600001',
          sex: 2,
          tel: '13413413134'
        }
      ]
    };
  },
  methods: {
    /**
     * @Description 获取列的字母
     *
     * @param {Number} numm
     * @returns {String}
     */
    getAlphabet(numm) {
      var char = [];
      var numToStringAction = function (nnum) {
        var num = nnum - 1;
        var a = parseInt(num / 26);
        var b = num % 26;
        char.push(String.fromCharCode(64 + parseInt(b + 1)));
        if (a > 0) {
          numToStringAction(a);
        }
      };
      numToStringAction(numm);
      return char.reverse().join('');
    },

    /**
     * @Description 导出函数
     * @param {object} thead {key: value}
     * @param {Array} tableData 是多少
     * @param {String} countText 头部的合计
     * @param {String} tableNameOrder 每个表格的标题栏JS命令
     * @param {String} fileName  文件名
     * @param {String} tableDataKey 表格数据的字段
     */
    async exportExcel(
      thead,
      tableData,
      countText = '',
      tableNameOrder = '`体检号：${item.regNo}   姓名：${item.name}`',
      fileName = '团体明细费用报表',
      tableDataKey = 'personCombs'
    ) {
      // 创建Excel工作簿
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet(fileName);

      // let ExcelTheadKeys = Object.keys(thead); // 按照顺序获取表格的头部key；
      // let ExcelTheadVal = Object.values(thead);
      // let maxColumn = await this.getAlphabet(ExcelTheadKeys.length+1); // 加上序号列，获取最大列的字母
      console.log(worksheet);

      const tableTheadKeys = Object.keys(thead);
      const tableTheadNames = Object.values(thead);
      console.log(tableTheadKeys);
      let maxColumn = await this.getAlphabet(tableTheadKeys.length); // 加上序号列，获取最大列的字母
      console.log(maxColumn);
      // return
      // 设置表头
      worksheet.mergeCells(`A1:${maxColumn}1`);
      let title = worksheet.getCell('A1');
      title.value = fileName;

      title.font = {
        bold: true,
        size: 24
      };
      // 统计
      worksheet.mergeCells(`A2:${maxColumn}2`);
      let countRow = worksheet.getCell('A2');
      countRow.value = countText;
      countRow.font = {
        bold: true,
        size: 14,
        color: { argb: '1770df' }
      };
      // return
      // worksheet.properties.defaultRowHeight = 20; // 默认行高
      worksheet.properties.defaultColWidth = 20;
      // 创建表的样式
      // const style = {
      //     theme: 'Table2', // 表格主题
      //     showRowStripes: true, // 是否显示行条纹
      //     showColumnStripes: false, // 是否显示列条纹
      //     showRowHeaders: true, // 是否显示行头
      //     showColumnHeaders: true // 是否显示列头
      // };
      // 设置表头样式
      // const headerStyle = {
      //     font: { bold: true, color: { argb: 'FFFF0000' } }, // 字体加粗，颜色为红色
      //     fill: {
      //         type: 'pattern',
      //         pattern: 'solid',
      //         fgColor: { argb: 'c9e4b4' }
      //     }, // 设置背景色为黄色 }, // 填充样式，颜色为灰色
      //     border: { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } } // 边框样式
      // };
      let width_5 = ['数量', '折扣', '组合价格'];
      const columns = tableTheadNames.map((item) => {
        let width = 20;
        if (width_5.includes(item)) {
          width = 5;
        }
        return {
          name: item,
          width
        };
      });

      let refNum = 3;
      tableData.forEach((item, idx) => {
        let tableTitle = new Function('item', 'return ' + tableNameOrder)(item);
        worksheet.mergeCells(`A${refNum}:${maxColumn}${refNum}`);
        let titleRow = worksheet.getCell(`A${refNum}`);
        titleRow.value = tableTitle;

        if (tableDataKey) {
          let rows = [];
          item[tableDataKey].forEach((twoItem) => {
            let rowItem = [];
            tableTheadKeys.forEach((threeItem) => {
              rowItem.push(twoItem[threeItem]);
            });
            rows.push(rowItem);
          });
          console.log(rows);
          refNum += 1;
          worksheet.addTable({
            name: 'MyTable' + idx,
            ref: 'A' + refNum,
            displayName: 'My Table' + idx,
            // headerRow: true,
            style: {},
            columns: columns,
            rows: rows
          });
          refNum += item[tableDataKey].length + 2;
          let table = worksheet.getTable('MyTable' + idx);

          let everyTitle = worksheet.getRow(table.model.tl.row);
          everyTitle.eachCell((cell) => {
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'c9e4b4' } // 设置背景色为黄色
            };
            cell.font = {
              bold: true, // 字体加粗
              color: { argb: '00000000' }, // 字体颜色
              size: 12
            };
          });
          return;
        }
        refNum += 1;
      });

      // console.log(table);
      // let rows = worksheet.getRow(table.model.tl.row);
      // console.log(rows);
      // rows.eachCell(column  => {
      //     console.log(column);
      // });
      worksheet.getRow(1).height = 32;
      title.alignment = {
        horizontal: 'center'
      };
      worksheet.views = [
        {
          state: 'frozen',
          xSplit: 0,
          ySplit: 2
        }
      ];
      // 导出Excel文件
      workbook.xlsx.writeBuffer().then((buffer) => {
        const blob = new Blob([buffer], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${fileName + dataUtils.getNowDateTiemNo()}.xlsx`;
        link.click();
        window.URL.revokeObjectURL(url);
      });
    }
  }
};
