import ExcelJS from 'exceljs';
import { mapGetters } from 'vuex';
import { dataUtils } from '@/common';
export default {
  computed: {
    ...mapGetters(['G_EnumList'])
  },
  data() {
    return {};
  },
  methods: {
    /**
     * @Description 获取列的字母
     *
     * @param {Number} numm
     * @returns {String}
     */
    getAlphabet(numm) {
      var char = [];
      var numToStringAction = function (nnum) {
        var num = nnum - 1;
        var a = parseInt(num / 26);
        var b = num % 26;
        char.push(String.fromCharCode(64 + parseInt(b + 1)));
        if (a > 0) {
          numToStringAction(a);
        }
      };
      numToStringAction(numm);
      return char.reverse().join('');
    },

    /**
         * @Description 导出函数
         * @param {String} fileName //文件名称
         * @param {String} title //标题
         * @param {object} thead {key: value}
         * @param {Array} tableData
         * @param {String} countText // 标题下面的统计行
         
         */
    async exportExcel(fileName, title, thead, tableData, countText) {
      // 创建Excel工作簿
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet(fileName);

      let ExcelTheadKeys = Object.keys(thead); // 按照顺序获取表格的头部key；
      let ExcelTheadVal = Object.values(thead);
      let maxColumn = await this.getAlphabet(ExcelTheadKeys.length + 1); // 加上序号列，获取最大列的字母
      console.log(worksheet);
      // return
      // worksheet.properties.defaultColWidth = 16; // 默认列宽
      worksheet.properties.defaultRowHeight = 20; // 默认行高
      // 设置表头
      worksheet.mergeCells(`A1:${maxColumn}1`);
      let titles = worksheet.getCell('A1');
      titles.value = title;

      titles.font = {
        bold: true,
        size: 24
      };

      // 设置统计行
      worksheet.mergeCells(`A2:${maxColumn}2`);
      let countTd = worksheet.getCell('A2');
      countTd.value = countText;

      worksheet.addRow(['序号', ...ExcelTheadVal]);
      dataUtils.deepCopy(tableData).map((item, idx) => {
        let rows = [];
        if (!isNaN(item.sex)) {
          item.sex = this.G_EnumList['Sex'][item.sex];
        }
        item.activeTime = item.activeTime?.split(' ')[0];
        ExcelTheadKeys.forEach((twoItem) => {
          rows.push(item[twoItem]);
        });
        worksheet.addRow([idx + 1, ...rows]);
      });

      worksheet.views = [
        {
          state: 'frozen',
          xSplit: 0,
          ySplit: 3,
          topLeftCell: 'B1',
          activeCell: 'A1'
          // showGridLines: false
        }
      ];

      // 获取所有列的最大宽度
      let columnWidths = worksheet.columns.map((column) => {
        return {
          column: column.key,
          width: 5 // 假设列头的长度就是初始宽度
        };
      });
      // 遍历所有行并设置样式
      worksheet.eachRow({ includeEmpty: true }, (row, rowNumber) => {
        row.height = 20;
        row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
          if (cell.row < 3) return;

          cell.alignment = {
            horizontal: 'center'
          };

          if (cell.row == 3) {
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'c9e4b4' } // 设置背景色为黄色
            };
            cell.font = {
              bold: true, // 字体加粗
              color: { argb: '00000000' }, // 字体颜色为白色
              size: 12
            };
          }
          const length = cell.value ? cell.value.toString().length : 0;
          // 更新对应列的宽度
          const columnWidth = columnWidths[colNumber - 1];
          if (length > columnWidth.width) {
            columnWidth.width = length;
          }
        });
      });
      // 设置每列的宽度为自动
      worksheet.columns.forEach((column, index) => {
        column.width = columnWidths[index].width + 4;
      });

      worksheet.getRow(1).height = 32;
      titles.alignment = {
        horizontal: 'center'
      };
      countTd.alignment = {
        horizontal: 'right'
      };
      // 导出Excel文件
      workbook.xlsx.writeBuffer().then((buffer) => {
        const blob = new Blob([buffer], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${fileName}.xlsx`;
        link.click();
        window.URL.revokeObjectURL(url);
      });
    }
  }
};
