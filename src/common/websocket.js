/**
 * @FilePath: \KrPeis\src\common\websocket.js
 * @Description:  websocket 封装
 * @Author:
 * @Date:
 * @Version: 0.0.2
 * @LastEditors: justin
 * @LastEditTime: 2024-07-22 11:37:17
 
 */
import { storage } from './storage';
import store from '@/store';
import { Message } from 'element-ui';

class WS {
  constructor(url, isConnectFlag = false) {
    let that = this;
    that.websock = null;
    that.url = url;
    that.rec; //断线重连后，延迟5秒重新创建WebSocket连接  rec用来存储延迟请求的代码
    that.isConnect = isConnectFlag; //连接标识 避免重复连接
    that.checkMsg = 'heartbeat'; //心跳发送/返回的信息 服务器和客户端收到的信息内容如果如下 就识别为心跳信息 不要做业务处理
    that.errTipNum = 0;
    //心跳设置
    that.heartCheck = {
      timeout: 20000, //每段时间发送一次心跳包 这里设置为20s
      timeoutObj: null, //延时发送消息对象（启动心跳新建这个对象，收到消息后重置对象）
      start: function () {
        let userInfo = storage.session.get('userInfo');
        let sendInfo = {
          SendUser: userInfo?.codeOper.operatorCode,
          StatusCode: 207
        };

        this.timeoutObj && clearInterval(this.timeoutObj);
        this.timeoutObj = setInterval(function () {
          if (!that.websock || that.websock.readyState !== that.websock.OPEN) {
            that.createWebSocket();
          }

          if (that.isConnect) {
            that.websock.send(JSON.stringify(sendInfo));
          }
        }, this.timeout);
      },
      reset: function () {
        clearInterval(this.timeoutObj);
        this.start();
      },
      stop: function () {
        clearInterval(this.timeoutObj);
      }
    };
    that.initWebSocket();
  }
  globalCallback() {}
  createWebSocket() {
    try {
      this.initWebSocket(); //初始化websocket连接
    } catch (e) {
      this.reConnect(); //如果无法连接上webSocket 那么重新连接！可能会因为服务器重新部署，或者短暂断网等导致无法创建连接
    }
  }
  //定义重连函数
  reConnect() {
    //如果已经连上就不在重连了||如果正在等待重连就不在重连了
    if (this.isConnect || this.rec) return;

    this.closeWebSocket();
    this.rec = setTimeout(() => {
      // 延迟20秒重连  避免过多次过频繁请求重连
      this.createWebSocket();
      this.rec = null;
    }, 20000);
  }
  //设置关闭连接
  closeWebSocket() {
    this.rec && clearTimeout(this.rec);
    this.heartCheck.stop();
    this.websock && this.websock.close();
    this.websock = null;
  }

  // 初始化websocket
  initWebSocket() {
    if (this.websock && this.websock.readyState === this.websock.OPEN) return;

    // ws地址 -->这里是你的请求路径
    setTimeout(() => {
      this.websock =
        window.$wsHub.getConnected(this.url) || new WebSocket(this.url);
      this.websock.onmessage = (e) => {
        this.websocketonmessage(e);
      };
      this.websock.onclose = (e) => {
        if (this.url.includes('printer') && this.errTipNum < 1) {
          this.errTipNum++;
          Message({
            message: '打印机连接已断开',
            type: 'error',
            duration: 3000,
            showClose: true
          });
          store.commit('M_printerState', 3);
        }
        this.websocketclose(e);
      };
      this.websock.onopen = () => {
        if (this.url.includes('printer')) {
          this.errTipNum = 0;
          store.commit('M_printerState', 1);
        }
        this.websocketOpen();
        this.heartCheck.start();
      };

      // 连接发生错误的回调方法
      this.websock.onerror = (r) => {
        this.isConnect = false; //连接断开修改标识
        this.onerror && this.onerror(r);
        this.reConnect(); //连接错误 需要重连
      };
    }, 500);
  }

  // 实际调用的方法
  sendSock(agentData, callback = () => {}) {
    this.globalCallback = callback;
    if (!this.websock) return;
    if (this.websock.readyState === this.websock.OPEN) {
      // 若是ws开启状态
      this.websocketsend(agentData);
    } else if (this.websock.readyState === this.websock.CONNECTING) {
      // 若是 正在开启状态，则等待1s后重新调用
      setTimeout(() => {
        this.sendSock(agentData, callback);
      }, 1000);
    } else {
      // 若未开启 ，则等待1s后重新调用
      // setTimeout( ()=> {
      //     this.sendSock(agentData, callback)
      // }, 1000)
    }
  }

  // 数据接收
  websocketonmessage(e) {
    // let O_o = JSON.parse(decodeUnicode(e.data))
    //
    // if (!O_o) {
    //     this.heartCheck.reset();
    // } else {
    //     this.globalCallback(O_o);
    // }
    // // globalCallback(JSON.parse(e.data))
    // function decodeUnicode(str) {
    //     str = str.replace(/\\/g, "%");
    //     //转换中文
    //     str = unescape(str);
    //     //将其他受影响的转换回原来
    //     str = str.replace(/%/g, "\\");
    //     //对网址的链接进行处理
    //     str = str.replace(/\\/g, "");
    //     return str;
    // }
  }
  // 连接错误的回调
  onerror(r) {
    console.error('连接发生错误', r);
  }
  // 数据发送
  websocketsend(agentData) {
    this.websock.send(agentData);
    this.globalCallback(agentData);
  }

  // 关闭
  websocketclose(e) {
    this.isConnect = false; //断开后修改标识
    window.$wsHub.removeConnected(this.url);
  }

  // 创建 websocket 连接
  websocketOpen(e) {
    window.$wsHub.addConnected(this.websock);
  }
}

window.$wsHub = {
  instConnecteds: [], // 存放已连接成功的实例化的websocket对象
  /**
   * @author: justin
   * @description: 添加已成功连接的实例
   * @param {*} ws
   * @return {*}
   **/
  addConnected(ws) {
    if (
      !ws ||
      ws.readyState !== ws.OPEN ||
      this.instConnecteds.some((x) => x.url === ws.url)
    )
      return false;

    this.instConnecteds.push(ws);
    return true;
  },

  /**
   * @author: justin
   * @description: 移除已成功连接的实例
   * @param {*} url
   * @return {*}
   **/
  removeConnected(url) {
    if (!url) return false;
    const index = this.instConnecteds.findIndex((x) => x.url === url);
    if (index < 0) return false;

    this.instConnecteds.splice(index, 1);
    return true;
  },

  /**
   * @author: justin
   * @description: 获取已成功连接的实例
   * @param {*} url
   * @return {*}
   **/
  getConnected(url) {
    return this.instConnecteds.find((x) => x.url === url);
  },

  /**
   * @author: justin
   * @description: 关闭所有已连接的实例
   * @return {*}
   **/
  closeAllConnected() {
    this.instConnecteds.forEach((x) => x.closeWebSocket());
  }
};

// 页面卸载时关闭所有连接：刷新、关闭标签页、关闭浏览器
window.addEventListener('unload', (e) => {
  window.$wsHub.closeAllConnected();
});

export default WS;
