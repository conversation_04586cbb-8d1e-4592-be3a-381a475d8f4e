import axios from 'axios';
import { encrypt } from './encrypt';
import store from '@/store';
import Vue from 'vue';
import { storage } from './storage';
import { refreshTokenRequest, retryCacheRequest } from '@/apis/refreshToken';
import { message } from './resetMessage';
import proxyConfig from '../../proxy.config.json';

const encryptWhiteList = [
  'UploadPhoto',
  'UploadTeamImport',
  'ExportToPdf',
  'SaveCollectedPacsImage',
  'GetSplitPdfByPages',
  'UploadAuditoryPicture',
  'UploadSignature',
  'UploadReportGraphFile'
]; // 不加密的接口白名单

// axios配置参数
let settings = {
  baseURL: '',
  timeout: 60000,
  headers: {
    'Content-Type': 'application/json',
    Authorization: ''
  }
};

// 创建axios实例
const instance = axios.create(settings);

/**
 * @author:
 * @description:  axios请求封装，data参数形式
 * @param {*} method 请求方法
 * @param {*} url 请求地址
 * @param {*} data data请求参数
 * @param {*} options 请求配置
 * @return {*}
 **/
function request(method, url, data, options = {}) {
  return instance({
    method,
    url,
    data,
    baseURL: options.baseURL || settings.baseURL,
    headers: options.headers || settings.headers,
    crossDomain: true,
    timeout: options.timeout === undefined ? settings.timeout : options.timeout,
    responseType: options.responseType,
    params: options.query || null,
    signal: options?.signal || undefined
  });
}

function paramsRequest(method, url, data, options = {}) {
  return instance({
    method,
    url,
    params: data,
    baseURL: options.baseURL || settings.baseURL,
    headers: options.headers || settings.headers,
    crossDomain: true,
    timeout: options.timeout === undefined ? settings.timeout : options.timeout,
    responseType: options.responseType,
    signal: options?.signal || undefined
  });
}

instance.interceptors.request.use(
  async function (config) {
    // 开发环境代理替换全路径
    if (
      process.env.NODE_ENV === 'development' &&
      config.url.startsWith('http') &&
      Array.isArray(proxyConfig)
    ) {
      for (let i = 0; i < proxyConfig.length; i++) {
        if (config.url.includes(proxyConfig[i].target)) {
          config.url = config.url.replace(proxyConfig[i].target, '');
          config.baseURL = '';
          break;
        }
      }
    }

    // token
    config.headers['Authorization'] = getToken();

    // 数据加密
    if (config.method == 'post') {
      config = await encryptData(config);
    }

    return config;
  },
  function (error) {
    return Promise.reject(error);
  }
);

instance.interceptors.response.use(
  function (response) {
    let { status, data, headers } = response;
    let msg = null;
    switch (status) {
      case 200:
        if (!data.success && headers['content-type'] !== 'application/pdf')
          msg = data.returnMsg;
        else if (response.config.url.includes('/Login')) {
          setTimeout(() => {
            // 登录成功，重试失败的请求
            retryCacheRequest.trigger();
          }, 0);
        }
        break;
    }

    if (msg) {
      message({
        message: msg,
        type: 'error',
        dangerouslyUseHTMLString: true,
        duration: store.getters.G_config.messageTime,
        showClose: true
      });
    }

    return response;
  },
  async function (error) {
    const errorMessages = {
      ERR_NETWORK: '连接服务器失败，请稍后重试！',
      ERR_TIMEOUT: '连接超时，请稍后重试！',
      ECONNABORTED: '连接超时，请稍后重试！',
      ERR_ABORTED: '连接已取消，请稍后重试！',
      ERR_CANCELED: '已取消请求！'
    };
    let { status, data } = error.response || {};
    let msg;
    switch (status) {
      case 401:
        return refreshTokenRequest(error);
      case 404:
        msg = '404 服务不存在！';

        message({
          message: msg,
          type: 'error',
          dangerouslyUseHTMLString: true,
          duration: store.getters.G_config.messageTime,
          showClose: true
        });
        return Promise.reject(error);
      default:
        msg =
          errorMessages[error.code] ||
          data?.returnMsg ||
          error?.response?.data ||
          error?.message ||
          '未知错误！';

        message({
          message: msg,
          type: 'error',
          dangerouslyUseHTMLString: true,
          duration: store.getters.G_config.messageTime,
          showClose: true
        });

        return Promise.reject(error);
    }
  }
);

/**
 * @author: justin
 * @description: 获取token
 * @return {String} token
 **/
function getToken() {
  const { token } = storage.session.get('userInfo') || {};
  if (!token || !token.access_token) return '';

  return `${token.token_type} ${token.access_token}`;
}

async function encryptData(config) {
  if (config.isEncrypted)
    return config; // 已经加密过的数据，不再加密
  else if (typeof config.isEncrypt === 'boolean') {
    if (!config.isEncrypt) return config;
  } else if (!Vue.prototype.$config.isEncrypt) return config;

  // 白名单接口不加密
  if (encryptWhiteList.some((x) => config.url.includes(x))) return config;

  if (config.data) {
    config.data = {
      encStr: encrypt.EncryptData(JSON.stringify(config.data))
    };
  }
  if (config.params) {
    let qstr = '';
    for (const key in config.params) {
      qstr = `${qstr}key=${config.params[key]}&`;
    }

    config.params = {
      encStr: encrypt.EncryptData(qstr)
    };
  }

  // 标记加密过的数据，防止重复加密（重试请求问题）
  config.isEncrypted = true;

  return config;
}

export const ajax = {
  setbaseURL(baseURL) {
    settings.baseURL = baseURL;
  },
  get(url, options) {
    return request('get', url, null, options);
  },
  post(url, data, options) {
    return request('post', url, data, options);
  },
  paramsPost(url, data, options) {
    return paramsRequest('post', url, data, options);
  },
  delete(url, data, options) {
    return request('delete', url, data, options);
  },
  put(url, data, options) {
    return request('put', url, data, options);
  },
  instance
};
