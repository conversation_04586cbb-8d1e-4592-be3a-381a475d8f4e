/**
 * @FilePath: \KrPeis\src\common\resetMessage.js
 * @Description: 解决element 弹出多个message消息提示 问题
 * @Author: justin
 * @Date: 2024-04-23 15:02:29
 * @Version: 0.0.1
 * @LastEditors: justin
 * @LastEditTime: 2024-04-23 15:10:07
 
 */

import { Message } from 'element-ui';
let messageInstance = null;
const resetMessage = (options) => {
  if (messageInstance) {
    messageInstance.close();
  }
  messageInstance = Message(options);
};
['error', 'success', 'info', 'warning'].forEach((type) => {
  resetMessage[type] = (options) => {
    if (typeof options === 'string') {
      options = {
        message: options
      };
    }
    options.type = type;
    return resetMessage(options);
  };
});
export const message = resetMessage;
