import CryptoJS from 'crypto-js';
import { smEncrypt } from './sm2';

var settings = {
  // key : CryptoJS.enc.Utf8.parse("2123a6cdef342431"),  //十六位十六进制数作为密钥
  iv: CryptoJS.enc.Utf8.parse('rVV3lWe74u1fcnDd') //十六位十六进制数作为密钥偏移量
};
export const encrypt = {
  //加密方法
  EncryptData(word) {
    // let key = `${Math.floor(Math.random() * 0xffffffff).toString(16)}${Math.floor(Math.random() * 0xffffffff).toString(16)}`;
    var result = '';
    for (var i = 0; i < 16; i++) {
      result += Math.floor(Math.random() * 16).toString(16); //获取0-15并通过toString转16进制
    }
    let key = result.toUpperCase();
    let encryptKey = smEncrypt.EncryptData(key);
    let srcs = CryptoJS.enc.Utf8.parse(word);
    let encrypted = CryptoJS.AES.encrypt(srcs, CryptoJS.enc.Utf8.parse(key), {
      iv: settings.iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });
    return encryptKey + '@' + encrypted.ciphertext.toString().toUpperCase();
  },
  // 解密
  DecryptData(word, key) {
    console.log(word, key);
    let encryptedHexStr = CryptoJS.enc.Hex.parse(word);
    let srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr);
    let decrypt = CryptoJS.AES.decrypt(srcs, CryptoJS.enc.Utf8.parse(key), {
      iv: settings.iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });
    let decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
    return decryptedStr.toString();
  }
};
