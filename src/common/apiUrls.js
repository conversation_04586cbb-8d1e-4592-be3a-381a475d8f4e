export const apiUrls = {
  // 后台接口
  login: '/UserManage/Login',
  RefreshToken: '/UserManage/RefreshToken', // 刷新token
  GetUserMenus: '/UserManage/GetUserMenus',
  GetAllMenu: '/SystemMenu/GetAllMenu',
  GetAllApiList: '/SystemMenu/GetAllApiList',
  GetAllRole: '/UserManage/GetAllRole',
  //用户管理userManage页
  GetAllOperator: '/UserManage/GetAllOperator', //获取操作员列表/
  AddOperator: '/UserManage/AddOperator', //添加管理员信息/
  UpdateOperator: '/UserManage/UpdateOperator', //修改管理员信息
  DeleteOperators: '/UserManage/DeleteOperators', //根据id集合批量删除操作员
  GetUserRole: '/UserManage/GetUserRole', //根据操作员编码获取操作员角色
  //  角色管理roleManage页面
  AddRole: '/UserManage/AddRole', //添加角色信息/
  UpdateRole: '/UserManage/UpdateRole', //编辑角色信息/
  DeleteRoles: '/UserManage/DeleteRoles', //删除角色信息/
  GetRoleMenu: '/UserManage/GetRoleMenu', //获取角色权限/
  SetRolePermissions: '/UserManage/SetRolePermissions', //修改角色权限/

  //  菜单管理menuManage页面
  AddMenu: '/SystemMenu/AddMenu', // 添加根目录菜单(viewFlag:0) , 添加子菜单(viewFlag:1) ,
  DeleteMenu: '/SystemMenu/DeleteMenu', // 删除根目录菜单
  UpdateMenu: '/SystemMenu/UpdateMenu', // 修改根目录菜单(viewFlag:0) , 修改子菜单(viewFlag:1)
  RelevanceApi: '/SystemMenu/RelevanceApi', // 关联API
  AddOrUpdateFunction: '/SystemMenu/AddOrUpdateFunction', // 添加/修改子菜单功能
  GetFuncByMenuCode: '/SystemMenu/GetFuncByMenuCode', // 获取子菜单功能
  DeleteFunction: '/SystemMenu/DeleteFunction', //删除页面功能

  // 基础代码
  CU_CodeGatherPlace: '/BasicCode/CU_CodeGatherPlace', // /CU_CodeItemCls/Create 新增检查地点信息 /CU_CodeGatherPlace/Update 更新检查地点信息
  RD_CodeGatherPlace: '/BasicCode/RD_CodeGatherPlace', // /RD_CodeGatherPlace/Read 获取检查地点信息 /RD_CodeGatherPlace/Delete 删除检查地点信息
  CU_CodeJob: '/BasicCode/CU_CodeJob', //CU_CodeJob/Create 新增职业工种信息//CU_CodeJob/Update 更新职业工种信息
  RD_CodeJob: '/BasicCode/RD_CodeJob', //RD_CodeJob/Read 获取职业工种信息//RD_CodeJob/Delete 删除职业工种信息
  CU_CodeNation: '/BasicCode/CU_CodeNation', //CU_CodeNation/Create 新增民族信息 //CU_CodeNation/Update 更新民族信息
  RD_CodeNation: '/BasicCode/RD_CodeNation', //RD_CodeNation/Read   获取民族信息//RD_CodeNation/Delete 删除民族信息
  CU_CodeNativePlace: '/BasicCode/CU_CodeNativePlace', //CU_CodeNativePlace/Create 新增籍贯信息/CU_CodeNativePlace/Update 更新籍贯信息
  RD_CodeNativePlace: '/BasicCode/RD_CodeNativePlace', //RD_CodeNativePlace/Read   获取籍贯信息/RD_CodeNativePlace/Delete 删除籍贯信息
  CU_CodeSample: '/BasicCode/CU_CodeSample', //CU_CodeSample/Create 新增标本信息/CU_CodeSample/Update 更新标本信息
  RD_CodeSample: '/BasicCode/RD_CodeSample', //RD_CodeSample/Read   获取标本信息/RD_CodeSample/Delete 删除标本信息
  CU_CodeHospital: '/BasicCode/CU_CodeHospital', //CU_CodeHospital/Create 新增院区代码信息/CU_CodeHospital/Update 更新院区代码信息
  RD_CodeHospital: '/BasicCode/RD_CodeHospital', //RD_CodeHospital/Read   获取院区代码信息/RD_CodeHospital/Delete 删除院区代码信息
  ReadDiseaseExpressionList: '/DiseaseMgt/ReadDiseaseExpressionList', //疾病判断计算式列表(UI左边的接口)
  CU_CodeDiseaseExpression: '/DiseaseMgt/CU_CodeDiseaseExpression', ///CU_CodeDiseaseExpression/Create 新增疾病判断计算式 /CU_CodeDiseaseExpression/Update 更新疾病判断计算式
  RD_CodeDiseaseExpression: '/DiseaseMgt/RD_CodeDiseaseExpression', ///RD_CodeDiseaseExpression/Read 获取疾病判断计算式 /RD_CodeDiseaseExpression/Delete 删除疾病判断计算式
  // OperationAndFunction: "/DiseaseMgt/OperationAndFunction", //运算符及函数列表
  ReadCodeDepartmentByHosp: '/BasicCode/ReadCodeDepartmentByHosp', //根据院区获取科室信息 (Auth)

  // 体检类代码
  CU_CodeCluster: '/BasicCode/CU_CodeCluster', // /CU_CodeCluster/Create 新增体检套餐信息 /CU_CodeCluster/Update 更新体检套餐信息
  RD_CodeCluster: '/BasicCode/RD_CodeCluster', // /RD_CodeCluster/Read 获取体检套餐信息 /RD_CodeCluster/Delete 删除体检套餐信息
  CU_CodeItemCls: '/BasicCode/CU_CodeItemCls', // /CU_CodeItemCls/Create 新增项目分类信息 /CU_CodeItemCls/Update 更新项目分类信息
  RD_CodeItemCls: '/BasicCode/RD_CodeItemCls', // /RD_CodeItemCls/Read 获取项目分类信息 /RD_CodeItemCls/Delete 删除项目分类信息
  CU_CodeDepartment: '/BasicCode/CU_CodeDepartment', ///CU_CodeDepartment/Create 新增科室代码信息 /CU_CodeDepartment/Update 更新科室代码信息
  RD_CodeDepartment: '/BasicCode/RD_CodeDepartment', // /RD_CodeDepartment/Read 获取科室代码信息 /RD_CodeDepartment/Delete 删除科室代码信息
  CU_CodeBound: '/BasicCode/CU_CodeBoundType', // /CU_CodeBound/Create 新增参考范围分类信息 /CU_CodeBound/Update 更新参考范围分类信息
  RD_CodeBound: '/BasicCode/RD_CodeBoundType', // /RD_CodeBound/Read 获取参考范围分类信息 /RD_CodeBound/Delete 删除参考范围分类信息
  CU_CodeArchiveItem: '/BasicCode/CU_CodeArchiveItem', // /CU_CodeArchiveItem/Create 新增档案项目信息 /CU_CodeArchiveItem/Update 更新档案项目信息
  RD_CodeArchiveItem: '/BasicCode/RD_CodeArchiveItem', // /RD_CodeArchiveItem/Read 获取档案项目信息 /RD_CodeArchiveItem/Delete 删除档案项目信息
  CU_CodeItemCombGroup: '/BasicCode/CU_CodeItemCombGroup', // /CU_CodeItemCombGroup/Create 新增组合分组信息 /CU_CodeItemCombGroup/Update 更新组合分组信息
  RD_CodeItemCombGroup: '/BasicCode/RD_CodeItemCombGroup', //  /RD_CodeItemCombGroup/Read 获取组合分组信息 /RD_CodeItemCombGroup/Delete 删除组合分组信息
  CU_CodeItemCombClass: '/BasicCode/CU_CodeItemCombClass', //  /CU_CodeItemCombClass/Create 新增组合分类信息 /CU_CodeItemCombClass/Update 更新组合分类信息
  RD_CodeItemCombClass: '/BasicCode/RD_CodeItemCombClass', //  /RD_CodeItemCombClass/Read 获取组合分类信息 /RD_CodeItemCombClass/Delete 删除组合分类信息
  CU_CodeMutexComb: '/BasicCode/CU_CodeMutexComb', // /CU_CodeMutexComb/Create 新增互斥组合设置 /CU_CodeMutexComb/Update 更新互斥组合设置
  RD_CodeMutexComb: '/BasicCode/RD_CodeMutexComb', //   /RD_CodeMutexComb/Read 获取互斥组合设置 /RD_CodeMutexComb/Delete 删除互斥组合设置
  CU_CodeConclusionTemplate: '/BasicCode/CU_CodeConclusionTemplate', // /CU_CodeConclusionTemplate/Create 新增体检结论模板设置 /CU_CodeConclusionTemplate/Update 更新体检结论模板设置
  RD_CodeConclusionTemplate: '/BasicCode/RD_CodeConclusionTemplate', ///RD_CodeConclusionTemplate/Read 获取体检结论模板设置 /RD_CodeConclusionTemplate/Delete 删除体检结论模板设置
  CU_CodeNormalResult: '/BasicCode/CU_CodeNormalResult', // /CU_CodeNormalResult/Create 新增正常结果 /CU_CodeNormalResult/Update 更新正常结果
  RD_CodeNormalResult: '/BasicCode/RD_CodeNormalResult', //  /RD_CodeNormalResult/Read 获取正常结果 /RD_CodeNormalResult/Delete 删除正常结果
  CU_CodeBarcodeType: '/BasicCode/CU_CodeBarcodeType', ///CU_CodeBarcodeType/Create 新增条码分类信息 /CU_CodeBarcodeType/Update 更新条码分类信息
  RD_CodeBarcodeType: '/BasicCode/RD_CodeBarcodeType', ///RD_CodeBarcodeType/Read 获取条码分类信息 /RD_CodeBarcodeType/Delete 删除条码分类信息
  PageQuery_CodeItem: '/BasicCode/PageQuery_CodeItem', //  /PageQuery_CodeItem 分页查询体检项目信息
  CU_CodeItem: '/BasicCode/CU_CodeItem', //  /CU_CodeItem/Create 新增体检项目信息 /CU_CodeItem/Update 更新体检项目信息
  Delete_CodeItem: '/BasicCode/Delete_CodeItem', // /Delete_CodeItem 删除体检项目信息
  CU_CodeItemComb: '/BasicCode/CU_CodeItemComb', // /CU_CodeItemComb/Create 新增体检组合信息 /CU_CodeItemComb/Update 更新体检组合信息Comb
  PageQuery_CodeItemComb: '/BasicCode/PageQuery_CodeItemComb', //  /PageQuery_CodeItemComb 分页查询体检组合信息
  Delete_CodeItemComb: '/BasicCode/Delete_CodeItemComb', //  /Delete_CodeItemComb 删除体检组合信息

  // 收费类代码
  CU_CodeFeeCls: '/BasicCode/CU_CodeFeeCls', // /CU_CodeFeeCls/Create 新增费用分类信息 /CU_CodeFeeCls/Update 更新费用分类信息
  RD_CodeFeeCls: '/BasicCode/RD_CodeFeeCls', // /RD_CodeFeeCls/Read   获取费用分类信息 /RD_CodeFeeCls/Delete 删除费用分类信息
  CU_CodeFeeClsGroup: '/BasicCode/CU_CodeFeeClsGroup', // /CU_CodeFeeClsGroup/Create 新增费用分类分组信息(收费类代码) /CU_CodeFeeClsGroup/Update 更新费用分类分组信息(收费类代码)
  RD_CodeFeeClsGroup: '/BasicCode/RD_CodeFeeClsGroup', // /RD_CodeFeeClsGroup/Read   获取费用分类分组信息(收费类代码) /RD_CodeFeeClsGroup/Delete 删除费用分类分组信息(收费类代码)
  CU_CodeFee: '/BasicCode/CU_CodeFee', // /CU_CodeFee/Create 新增结账模式 /CU_CodeFee/Update 更新结账模式
  RD_CodeFee: '/BasicCode/RD_CodeFee', // /RD_CodeFee/Read   获取结账模式 /RD_CodeFee/Delete 删除结账模式
  CU_CodePayment: '/BasicCode/CU_CodePayment', // /CU_CodePayment/Create 新增结账类型信息 /CU_CodePayment/Update 更新结账类型信息
  RD_CodePayment: '/BasicCode/RD_CodePayment', // /RD_CodePayment/Read   获取结账类型信息 /RD_CodePayment/Delete 删除结账类型信息
  CU_CodeRound: '/BasicCode/CU_CodeRound', // /CU_CodeRound/Create 新增凑整规则信息 /CU_CodeRound/Update 更新凑整规则信息
  RD_CodeRound: '/BasicCode/RD_CodeRound', // /RD_CodeRound/Read   获取凑整规则信息 /RD_CodeRound/Delete 删除凑整规则信息

  // api管理页面
  AddApi: '/SystemMenu/AddApi', // 添加api数据
  UpdateApi: '/SystemMenu/UpdateApi', // 修改api数据
  DeleteApi: '/SystemMenu/DeleteApi', // 删除api数据

  // 下拉列表数据
  GetGuidanceType: '/EnumData/GuidanceType', // 获取指引单样式（代码、名称）
  GetReportType: '/EnumData/ReportType', //获取报告样式（代码、名称）
  Department: '/EnumData/Department', //获取科室
  MarriageStatus: '/EnumData/MarriageStatus', //婚姻状态
  Sex: '/EnumData/Sex', //性别
  Company: '/EnumData/Company', //单位列表
  Cluster: '/EnumData/Cluster', // 获取体检套餐
  ItemComb: '/EnumData/ItemComb', //获取组合列表
  Operator: '/EnumData/Operator', //获取操作员
  CardType: '/EnumData/CardType', //获取证件类型
  Job: '/EnumData/Job', //获取工种
  NativePlace: '/EnumData/NativePlace', //获取籍贯
  CheckCls: '/EnumData/CheckCls', //获取体检分类
  ItemCls: '/EnumData/ItemCls', //获取项目分类
  ItemComb_ItemCls: '/EnumData/ItemComb_ItemCls', //获取组合-项目分类
  GetEnumList: '/EnumData/GetEnumList', //获取常用枚举列表
  BarcodeType: '/EnumData/BarcodeType', //条码分类
  Item: '/EnumData/Item', //获取项目
  CompanyAndTimes: '/EnumData/CompanyAndTimes', //获取所有单位及体检次数

  // 单位维护
  ReadCompanyLevel: '/Company/ReadCompanyLevel', //获取单位分类及其单位的层级信息
  R_CodeCompanyCls: '/Company/R_CodeCompanyCls', // /R_CodeCompanyCls 获取单位分类代码
  CUD_CodeCompanyCls: '/Company/CUD_CodeCompanyCls', // /CUD_CodeCompanyCls/Create 新增单位分类代码 CUD_CodeCompanyCls/Update 更新单位分类代码 CUD_CodeCompanyCls/Delete 删除单位分类代码
  R_CodeCompany: '/Company/R_CodeCompany', // 获取单位信息
  CUD_CodeCompany: '/Company/CUD_CodeCompany', // /CUD_CodeCompany/Create 新增单位信息 /CUD_CodeCompany/Update 更新单位信息 /CUD_CodeCompany/Delete 删除单位信息
  R_CodeCompanyDepartment: '/Company/R_CodeCompanyDepartment', //  获取单位部门信息
  CUD_CodeCompanyDepartment: '/Company/CUD_CodeCompanyDepartment', //  //CUD_CodeCompanyDepartment/Create 新增单位部门信息 /CUD_CodeCompanyDepartment/Update 更新单位部门信息 /CUD_CodeCompanyDepartment/Delete 删除单位部门信息
  CUD_CodeCompanyTimes: '/Company/CUD_CodeCompanyTimes', //CUD_CodeCompanyTimes/Create 新增单位体检次数信息/CUD_CodeCompanyTimes/Update 更新单位体检次数信息 CUD_CodeCompanyTimes/Delete 删除单位体检次数信息
  R_CodeCompanyTimes: '/Company/R_CodeCompanyTimes', // 获取单位体检次数信息
  ReadCompanyClusSimpleInfos: '/Company/ReadCompanyClusSimpleInfos', // 获取单位套餐概要信息列表
  CopyCompanyClusterCombs: '/Company/CopyCompanyClusterCombs', // 复制单位套餐的组合列表
  ReadCompanyClusAndCombs: '/Company/ReadCompanyClusAndCombs', //获取单位套餐信息及组合列表
  DeleteCompanyCluster: '/Company/DeleteCompanyCluster', //删除单位套餐及组合信息
  CompanyClusterAndComb: '/Company/CompanyClusterAndComb', // /CompanyClusterAndComb/Create 新建单位套餐及单位套餐组合信息 /CompanyClusterAndComb/Update 更新单位套餐及单位套餐组合信息
  UpdateCompanyCluster: '/Company/UpdateCompanyCluster', //更新单位套餐组合、已分配此套餐的团体名单
  GetCompanyCluster: '/Company/GetCompanyCluster', // 获取最新的单位套餐
  CompanyImport: '/Company/CompanyImport', //团体名单导入
  GetCompanyRegList: '/Company/GetCompanyRegList', //根据单位获取导入的数据
  DelCompanyRegList: '/Company/DelCompanyRegList', //删除导入的数据
  SetCompanyCluster: '/Company/SetCompanyCluster', //分配套餐
  CancelSetCompanyCluster: '/Company/CancelSetCompanyCluster', //取消分配套餐
  R_CodeCompanySimple: '/Company/R_CodeCompanySimple',
  GetCompanyClusWithDetails: '/Company/GetCompanyClusWithDetails', //获取单位套餐及其明细内容 (Auth)

  //项目列表
  Read_CodeItemBound: '/BasicCode/Read_CodeItemBound', //获取参考范围
  CU_CodeItemBound: '/BasicCode/CU_CodeItemBound', //CU_CodeItemBound/Create 新增参考范围/CU_CodeItemBound/Update 更新参考范围
  RD_CodeBoundType: '/BasicCode/RD_CodeBoundType', //RD_CodeBoundType/Read   获取参考范围分类信息
  Delete_CodeItemBound: '/BasicCode/Delete_CodeItemBound', //Delete_CodeItemBound 删除参考范围
  Read_CodeItemResult: '/BasicCode/Read_CodeItemResult', //Read_CodeItemResult  获取项目结果
  Delete_CodeItemResult: '/BasicCode/Delete_CodeItemResult', //Delete_CodeItemResult 删除项目结果
  CU_CodeItemResult: '/BasicCode/CU_CodeItemResult', //CU_CodeItemResult/Create 新增项目结果/CU_CodeItemResult/Update 更新项目结果
  Item_ItemCls: '/EnumData/Item_ItemCls',
  ItemGroupByItemCls: '/BasicCode/ItemGroupByItemCls', //项目根据项目分类分组
  Disease: '/EnumData/Disease', //获取疾病列表
  Read_ItemBanResult: '/BasicCode/Read_ItemBanResult', //Read_ItemBanResult//项目结果禁止规则
  CUD_CodeItemBanResult: '/BasicCode/CUD_CodeItemBanResult', //CUD_CodeItemBanResult/Create 新增项目结果禁止规则/CUD_CodeItemBanResult/Delete 删除项目结果禁止规则/CUD_CodeItemBanResult/Update 修改项目结果禁止规则

  // 中医管理
  R_CodeConstitution: '/BasicCode/R_CodeConstitution', // R_CodeConstitution 获取体质信息（中医管理）
  CUD_CodeConstitution: '/BasicCode/CUD_CodeConstitution', // CUD_CodeConstitution/Create 新增体质信息（中医管理） CUD_CodeConstitution/Update 更新体质信息（中医管理） CUD_CodeConstitution/Delete 删除体质信息（中医管理）

  // 系统参数
  R_CodeSystemParameter: '/BasicCode/R_CodeSystemParameter', //R_CodeSystemParameter 获取系统参数
  CUD_CodeSystemParameter: '/BasicCode/CUD_CodeSystemParameter', //CUD_CodeSystemParameter/Create 新增系统参数 CUD_CodeSystemParameter/Update 更新系统参数 CUD_CodeSystemParameter/Delete 删除系统参数
  ReadSystemParameters: '/SystemParam/ReadSystemParameters', //获取系统参数
  ReadSystemParameterList: '/SystemParam/ReadSystemParameterList', //获取系统参数 (Auth)
  UpdateSystemParameter: '/SystemParam/UpdateSystemParameter', //更新系统参数 (Auth)

  // 预约
  BookReadCandidateCluster: '/Book/ReadCandidateCluster', //获取候选套餐列表（套餐、绑定的组合、互斥的组合）
  BookReadCandidateComb: '/Book/ReadCandidateComb', //获取候选组合列表（组合、绑定的组合、互斥的组合）
  ReadPatientList: '/Book/ReadPatientList', // 获取预约个人信息列表
  ReadPeBook: '/Book/ReadPeBook', // 按预约号获取预约的信息
  SaveBook: '/Book/SaveBook', // 保存预约信息
  DeleteBook: '/Book/DeleteBook', // 删除预约信息

  // 个人登记
  ReadCandidateCluster: '/Register/ReadCandidateCluster', //获取候选套餐列表（套餐、绑定的组合、互斥的组合）
  ReadCandidateComb: '/Register/ReadCandidateComb', //获取候选组合列表（组合、绑定的组合、互斥的组合）
  SaveRegisterOrder: '/Register/SaveRegisterOrder', // SaveRegisterOrder/person 保存个人订单 SaveRegisterOrder/Company 保存单位订单
  GetRegOrderByRegNo: '/Register/GetRegOrderByRegNo', //获取订单(资料、套餐、组合)
  GetArchivesInfo: '/Register/GetArchivesInfo', //查询档案信息
  RecycleRegisterOrder: '/Register/RecycleRegisterOrder', //RecycleRegisterOrder/person  软删除订单记录(个检)RecycleRegisterOrder/company 软删除订单记录(团检)
  DeleteRegisterOrder: '/Register/DeleteRegisterOrder', //DeleteRegisterOrder/person  永久删除订单记录(个检)DeleteRegisterOrder/company 永久删除订单记录(团检)
  GetRegOrderByRegNo: '/Register/GetRegOrderByRegNo', //获取套餐组合
  GetRegistersByMultipleFilter: '/Register/GetRegistersByMultipleFilter', //获取登记资料列表（多筛查条件）
  GetRegisterClusterAndComb: '/Register/GetRegisterClusterAndComb', //获取订单获取套餐组合
  GetHistoryArchives: '/Register/GetHistoryArchives', //获取体检人的历史档案资料
  GetRecycleRegisterOrder: '/Register/GetRecycleRegisterOrder', // 查询回收订单(多筛查条件)
  GetRecycleRegisterOrderNew: '/RegisterNew/GetRecycleRegisterOrder', // 查询回收订单(多筛查条件)
  RestoreRegisterOrder: '/Register/RestoreRegisterOrder', // 恢复订单记录
  GetRegisterByQueryType: '/Register/GetRegisterByQueryType', //获取登记资料列表
  GetBatchUpdate: '/Register/GetBatchUpdate', //批量修改查询
  BatchUpdateByRegNo: '/Register/BatchUpdateByRegNo', //批量修改
  GetActiveRecord: '/Register/GetActiveRecord', //查询可激活/可取消激活记录
  GetActiveRecordNew: '/RegisterNew/GetActiveRecord', //查询可激活/可取消激活记录
  WhetherToActivate: '/Register/WhetherToActivate', //WhetherToActivate/Active激活记录WhetherToActivate/Deactive  取消激活记录
  ActivationOrCancel: '/Register/ActivationOrCancel', //ActivationOrCancel/Active激活记录 ActivationOrCancel/Deactive  取消激活记录
  GetBatchAddOrDelete: '/Register/GetBatchAddOrDelete', // GetBatchAddOrDelete    获取批增加/批删除数据
  BatchAddOrDelete: '/Register/BatchAddOrDelete', //BatchAddOrDelete/batchadd    批增加/批删除BatchAddOrDelete/batchdelete 批增加/批删除
  BatchAddOrDeleteNew: '/RegisterNew/BatchAddOrDelete', //BatchAddOrDelete/batchadd    批增加/批删除BatchAddOrDelete/batchdelete 批增加/批删除
  GetRegisterOrder: '/Register/GetRegisterOrder', //获取订单的资料、套餐、组合
  GetCompanyCluster: '/Company/GetCompanyCluster', //获取最新的单位套餐
  GetSimpleCompanyTimes: '/Company/GetSimpleCompanyTimes', ///GetSimpleCompanyTimes  获取体检次数简要信息
  AlterRegisterPatient: '/Register/AlterRegisterPatient', //修改体检人信息
  ReadCompanyCandidateCluster: '/RegisterNew/ReadCompanyCandidateCluster', //获取单位候选套餐列表（套餐、绑定的组合）
  CalculateTestTubeMaterialFees: '/Register/CalculateTestTubeMaterialFees', //计算组合的试管的材料费
  CheckPrintables: '/Register/CheckPrintables', //查询打印数据 (Auth)

  // 日志
  ReadLogBusiness: '/Log/ReadLogBusiness', //获取业务日志

  // 上传
  UploadTeamImport: '/CommonApi/UploadTeamImport', //上传团体导入文件
  UploadPhoto: '/CommonApi/UploadPhoto', //上传体检人相片
  UploadAuditoryPicture: '/CommonApi/UploadAuditoryPicture', //上传听力测试图

  // 折扣管理
  GetOperatorDiscount: '/UserManage/GetOperatorDiscount', //获取操作员折扣
  GetPersonInfo: '/CheckStand/GetPersonInfo', //获取个人信息列表
  GetRegisterInfoAndComb: '/CheckStand/GetRegisterInfoAndComb', //获取组合/材料费(折扣管理)
  FixedPrice: '/CheckStand/FixedPrice', //固定金额(一口价)
  ReductioProjectsPrice: '/CheckStand/ReductioProjectsPrice', //减免项目金额(折扣)
  RecoverCombPrice: '/CheckStand/RecoverCombPrice', //恢复项目单价

  //发票管理
  PurchaseInvoice: '/InvoiceManage/PurchaseInvoice', //发票购入
  ReadInvoiceWarehouse: '/InvoiceManage/ReadInvoiceWarehouse', //获取发票购入列表
  DeleteInvoiceWarehouse: '/InvoiceManage/DeleteInvoiceWarehouse', //删除发票入库记录
  AllocateInvoice: '/InvoiceManage/AllocateInvoice', //发票发放
  ReadInvoiceAllocation: '/InvoiceManage/ReadInvoiceAllocation', //获取发票发放列表
  DeleteInvoiceAllocation: '/InvoiceManage/DeleteInvoiceAllocation', //删除发票发放记录
  ReadInvoiceWriteOffReport: '/InvoiceManage/ReadInvoiceWriteOffReport', //发票核销
  TransferInvoice: '/InvoiceManage/TransferInvoice', //发票转交
  TakeBackInvoice: '/InvoiceManage/TakeBackInvoice', //收回发票

  // 个人收费
  ConfirmNewInvoice: '/CheckStand/ConfirmNewInvoice', //确认新发票号
  QueryCurrentInvoice: '/CheckStand/QueryCurrentInvoice', //根据操作员查询当前发票号
  ReadInvoiceSegment: '/CheckStand/ReadInvoiceSegment', //查询发票段
  ReadUnpaidList: '/CheckStand/ReadUnpaidList', //个人收费---获取未缴费列表
  ReadUnpaidCombsAndBasicCls: '/CheckStand/ReadUnpaidCombsAndBasicCls', //个人收费---获取未缴费组合及基础分类
  GetInvoiceNoInfo: '/CheckStand/GetInvoiceNoInfo', //获取发票号信息，用于修改发票号
  UpdateInvoiceNo: '/CheckStand/UpdateInvoiceNo', //修改发票
  ReadSettlementRecordsByMultipleFilter:
    '/CheckStand/ReadSettlementRecordsByMultipleFilter', //获取结算记录列表(多筛查条件)
  ReadSettlementCombs: '/CheckStand/ReadSettlementCombs', //获取结算组合
  Refund: '/CheckStand/Refund', //退款
  CreateSettlement: '/CheckStand/CreateSettlement', //创建结算
  SinglePay: '/CheckStand/SinglePay', //单个支付
  FinishSettlement: '/CheckStand/FinishSettlement', //完成结算
  CancelPayAll: '/CheckStand/CancelPayAll', //取消所有支付
  CancelPaySingle: '/CheckStand/CancelPaySingle', //取消支付
  GetReportSettlementDaily: '/CheckStand/GetReportSettlementDaily', //获取日缴报表/
  SaveSettlementDaily: '/CheckStand/SaveSettlementDaily', //保存日缴报表/
  GetSettlementDaily: '/CheckStand/GetSettlementDaily', //查询日缴报表/
  DeleteSettlementDaily: '/CheckStand/DeleteSettlementDaily', //删除日缴报表/

  //采集配管
  TestTubeCategoryList: '/Sample/TestTubeCategoryList', //试管类别列表
  ReadSampleData: '/Sample/ReadSampleData', //读取标本数据
  ReadSampleDetail: '/Sample/ReadSampleDetail', //标本配管详情
  ReadSampleBarcodeDetails: '/Sample/ReadSampleBarcodeDetails', //标本条码详情
  CreateSampleNo: '/Sample/CreateSampleNo', //创建标本条码号
  PrefabricatedBarcode: '/Sample/PrefabricatedBarcode', //标本绑定条码(预制条码配管)
  GeneratBarcodeBySystem: '/Sample/GeneratBarcodeBySystem', //标本绑定条码(系统生成条码配管)
  CancelSampleBarCode: '/Sample/CancelSampleBarCode', //取消标本条码(取消配管)

  //标本运送
  ReadUnPackedSample: '/Sample/ReadUnPackedSample', //标本运送：按条件查询未打包标本信息/
  ReadUnPackedSampleBySampleNo: '/Sample/ReadUnPackedSampleBySampleNo', //标本运送：条码号获取未打包标本信息/
  PackTransportSample: '/Sample/PackTransportSample', //标本运送：打包运送/
  ReadPackageBySampleNo: '/Sample/ReadPackageBySampleNo', //标本运送：条码号获取取条码号所在包/sampleNo
  ReadSamplePackage: '/Sample/ReadSamplePackage', //标本运送：获取标本包列表/
  DeletePackage: '/Sample/DeletePackage', //标本运送：删除包信息/
  ReadSampleByPackageNo: '/Sample/ReadSampleByPackageNo', //标本运送：根据包号获取标本列表/
  DeletePackageSample: '/Sample/DeletePackageSample', //标本运送：删除包标本/
  ReadSamplePackageTime: '/Sample/ReadSamplePackageTime', //标本运送：获取包标本编码与打包时间

  // 报表模板维护
  ReadReportList: '/ReportTemplate/ReadReportList', //获取报表模板列表
  NewEmptyReport: '/ReportTemplate/NewEmptyReport', //新建空报表模板
  DeleteReport: '/ReportTemplate/DeleteReport', //删除报表模板

  //医生工作站
  //会诊
  CreateDoctorChat: '/DoctorChat/CreateDoctorChat', //发起会诊
  ReadDoctorChat: '/DoctorChat/ReadDoctorChat', //会诊列表
  ReadDoctorChatReply: '/DoctorChat/ReadDoctorChatReply', //会诊回复列表(关于此会诊的所有记录)
  UpdateChatMsgStauts: '/DoctorChat/UpdateChatMsgStauts', //更新消息状态为已读(医生会诊)
  ReadLatestChat: '/DoctorChat/ReadLatestChat', //读取最新的会诊
  ReadContactRecord: '/Record/ReadContactRecord', //获取电话联系记录
  CreateContactRecord: '/Record/CreateContactRecord', //新增电话联系记录
  DeleteContactRecord: '/Record/DeleteContactRecord', //删除电话联系记录
  ReadPrintImage: '/Record/ReadPrintImage', //获取打印图例
  SendDoctorMsg: '/DoctorChat/SendDoctorMsg', //发送医生信息
  ConfirmCriticalValue: '/Record/ConfirmCriticalValue', //确认危急值

  //图像
  GetPacsComb: '/Record/GetPacsComb', //获取pacs组合
  SaveCollectedPacsImage: '/Record/SaveCollectedPacsImage', //保存采集的pacs图像
  GetRecordImage: '/Record/GetRecordImage', //获取结果图像
  GetCollectedPacsImage: '/Record/GetCollectedPacsImage', //获取采集的pacs图像
  DeleteCollectedPacsImage: '/Record/DeleteCollectedPacsImage', //删除采集的pacs图像
  CheckCollectedPacsImage4Comb: '/Record/CheckCollectedPacsImage4Comb', //选中采集的pacs图像中指定组合
  UncheckCollectedPacsImage4Comb: '/Record/UncheckCollectedPacsImage4Comb', //组合取消选中采集的pacs图像
  CheckPacsImage2Print: '/Record/CheckPacsImage2Print', //选中组合中的采集的pacs图像用于报告打印
  UncheckPacsImage2Print: '/Record/UncheckPacsImage2Print', //取消选中组合中的采集的pacs图像用于报告打印
  GetPacsImage2Print: '/Record/GetPacsImage2Print', //获取用于报告打印的pacs图像
  GetPatientListByDoctor: '/Record/GetPatientListByDoctor', //录入结果人员列表(医生工作站)
  GetHistoryReport: '/Record/GetHistoryReport', //历史报告
  GetExamItemsByDoctor: '/Record/GetExamItemsByDoctor', //获取项目导航
  ReadRecordComb: '/Record/ReadRecordComb', //获取组合项目结果记录
  ReadRecordCombNew: '/RecordNew/ReadRecordComb', //获取组合项目结果记录
  GetDefaultItemResultByComb: '/Record/GetDefaultItemResultByComb', //定义的项目结果及其疾病，用于医生工作站录入项目结果时选择
  GetDefaultItemResultByItem: '/Record/GetDefaultItemResultByItem', //按项目获取定义的项目结果及其疾病，用于医生工作站录入项目结果时选择
  SaveRecordComb: '/Record/SaveRecordComb', //保存组合项目结果
  GetPacsDept: '/Record/GetPacsDept', //获取pacs科室信息
  DeleteRecordComb: '/Record/DeleteRecordComb', //删除组合项目结果记录
  EditItemTag: '/Record/EditItemTag', //编辑项目标签
  GetItemHistoryResult: '/Record/GetItemHistoryResult', //获取项目历史结果
  RecordGetPatientList: '/Record/GetPatientList', //录入结果人员列表(结果录入)
  GetExamItems: '/Record/GetExamItems', //获取项目导航(结果录入)

  //疾病管理
  GetDiseaseInDept: '/DiseaseMgt/GetDiseaseInDept', //获取科室包含的疾病
  CU_CodeDiseaseCls: '/DiseaseMgt/CU_CodeDiseaseCls', ///CU_CodeDiseaseCls/Create 新增疾病分类信息/CU_CodeDiseaseCls/Update 更新疾病分类信息
  RD_CodeDiseaseCls: '/DiseaseMgt/RD_CodeDiseaseCls', //RD_CodeDiseaseCls/Read 获取疾病分类信息/RD_CodeDiseaseCls/Delete 删除疾病分类信息
  Query_MapDiseaseClsDisease: '/DiseaseMgt/Query_MapDiseaseClsDisease', //获取疾病分类对应疾病
  CD_MapDiseaseClsDisease: '/DiseaseMgt/CD_MapDiseaseClsDisease', ///CD_MapDiseaseClsDisease/Create 新增疾病分类对应疾病/CD_MapDiseaseClsDisease/Delete 删除疾病分类对应疾病
  Query_MapDiseaseDisease: '/DiseaseMgt/Query_MapDiseaseDisease', //获取疾病包含关系对应
  CD_MapDiseaseDisease: '/DiseaseMgt/CD_MapDiseaseDisease', ///CD_MapDiseaseDisease/Create 新增疾病包含关系对应/CD_MapDiseaseDisease/Delete 删除疾病包含关系对应
  GetDeptDisease4DiseaseCls: '/DiseaseMgt/GetDeptDisease4DiseaseCls', //获取科室疾病用于疾病分类对应
  GetParentDeptDisease: '/DiseaseMgt/GetParentDeptDisease', //获取科室疾病用于疾病包含关系中的父疾病
  GetChildDeptDisease: '/DiseaseMgt/GetChildDeptDisease', //获取科室疾病用于疾病包含关系中的子疾病
  CU_CodeDisease: '/DiseaseMgt/CU_CodeDisease', //CU_CodeDisease/Create 新增疾病信息 CU_CodeDisease/Update 更新疾病信息
  RD_CodeDisease: '/DiseaseMgt/RD_CodeDisease', //RD_CodeDisease/Read   获取疾病信息 RD_CodeDisease/Delete 删除疾病信息
  CU_CodeDiseaseCriteria: '/DiseaseMgt/CU_CodeDiseaseCriteria', // /CU_CodeDiseaseCriteria/Create 新增疾病逻辑值 /CU_CodeDiseaseCriteria/Update 更新疾病逻辑值
  ReadCodeDiseaseCriteria: '/DiseaseMgt/ReadCodeDiseaseCriteria', // 获取疾病逻辑值
  CreateCodeDiseaseCriteriaItem: '/DiseaseMgt/CreateCodeDiseaseCriteriaItem', //新增疾病审核条件包含的项目
  UpdateCodeDiseaseCriteriaItem: '/DiseaseMgt/UpdateCodeDiseaseCriteriaItem', //更新疾病审核条件包含的项目
  DeleteCodeDiseaseCriteriaItem: '/DiseaseMgt/DeleteCodeDiseaseCriteriaItem', //删除疾病审核条件包含的项目
  ReadCodeDiseaseCriteriaItem: '/DiseaseMgt/ReadCodeDiseaseCriteriaItem', //获取疾病审核条件包含的项目
  CreateCodeDeptWord: '/BasicCode/CreateCodeDeptWord', //CreateCodeDeptWord 新增科室常用词
  DeleteCodeDeptWord: '/BasicCode/DeleteCodeDeptWord', //DeleteCodeDeptWord 删除科室常用词
  ReadCodeDeptWord: '/BasicCode/ReadCodeDeptWord', //ReadCodeDeptWord 获取科室常用词
  VerifyDiseaseExpression: '/DiseaseMgt/VerifyDiseaseExpression', //校验疾病计算公式
  ReadMapDiseaseDisease: '/DiseaseMgt/ReadMapDiseaseDisease', //获取有疾病对应关系的数据
  DeleteParentDisease: '/DiseaseMgt/DeleteParentDisease', //删除父疾病

  //报告结论
  GetReportConclution: '/ReportConclusion/GetReportConclution', //获取报告结论
  SaveReportConclusion: '/ReportConclusion/SaveReportConclusion', //保存报告结论
  CancelReportConclusion: '/ReportConclusion/CancelReportConclusion', //取消保存报告结论
  ReadCheckPerson: '/ReportConclusion/ReadCheckPerson', //主检(不分配)----查询
  GetRecordCombs: '/ReportConclusion/GetRecordCombs', //获取已录入结果的组合，用于综述
  EditSumTag: '/ReportConclusion/EditSumTag', //编辑小结标签
  GetPatientList: '/ReportConclusion/GetPatientList', //获取主检的人员列表
  GetPatientList4Allocate: '/ReportConclusion/GetPatientList4Allocate', //获取主检的人员列表（分配）
  GetPatientList4NotAllocate: '/ReportConclusionNew/GetPatientList4NotAllocate', //获取主检的人员列表（不分配）、审核
  AuditReportConclusion: '/ReportConclusion/AuditReportConclusion', //审核报告结论
  CancelAuditReportConclusion: '/ReportConclusion/CancelAuditReportConclusion', //取消审核报告结论
  PriorityAllocation: '/ReportConclusion/PriorityAllocation', //优先分配主检
  GenerateSummarySuggestions: '/ReportConclusion/GenerateSummarySuggestions', //生成综述建议
  QueryDiseaseSuggestions: '/ReportConclusion/QueryDiseaseSuggestions', //关键词查询疾病建议，用于添加新建议
  QueryCheckedPatientList: '/ReportConclusion/QueryCheckedPatientList', //查询已检的人员列表（主检分配）
  ReturnChecked: '/ReportConclusion/ReturnChecked', //撤回主检
  GetSuggestion: '/ReportConclusion/GetSuggestion', //获取建议
  GetNotReplyReturn: '/ReportConclusion/GetNotReplyReturn', //获取未答复的撤回主检的信息
  ReplyReturn: '/ReportConclusion/ReplyReturn', //答复撤回主检
  GetReturnChecked: '/ReportConclusion/GetReturnChecked', //获取撤回主检记录（主检回复）
  ReplyCriticalValue: '/ReportConclusion/ReplyCriticalValue', //回复危急值（主检和审核权限者用）
  GetReportPeRegisters: '/ReportConclusionNew/GetReportPeRegisters', //登记、患者信息查询：主检、审核
  GetReportPeRegistersV2: '/ReportConclusionV2/GetReportPeRegisters', //登记、患者信息查询：主检、审核

  // 回收指引单
  RecycleGuidanceQuery: '/GuideSheet/RecycleGuidanceQuery', //回收指引单查询
  WhetherRecycleGuidance: '/GuideSheet/WhetherRecycleGuidance', // /WhetherRecycleGuidance/recycle 回收单 /WhetherRecycleGuidance/norecycle 取消回收指单
  ReadCombInfo: '/GuideSheet/ReadCombInfo', //获取组合情况
  AbandonComb: '/GuideSheet/AbandonComb', //弃检组合
  CancleAbandonComb: '/GuideSheet/CancleAbandonComb', //取消 弃检组合
  RefuseCombsByAssayType: '/GuideSheet/RefuseCombsByAssayType', //根据化验类型弃检组合

  // 报告邮寄打印
  UpdateReportPrinted: '/ReportMailPrint/UpdateReportPrinted', //更新打印标志
  GetReportPrintList: '/ReportMailPrint/GetReportPrintList', //获取报告打印列表
  GetReportBatchPrintAndExportList:
    '/ReportMailPrint/GetReportBatchPrintAndExportList', //报告批打印导出列表
  SaveReportReceiveMode: '/ReportMailPrint/SaveReportReceiveMode', //保存报告领取模式
  DeleteReportReceiveMode: '/ReportMailPrint/DeleteReportReceiveMode', //删除报告领取模式
  GetReportMailList: '/ReportMailPrint/GetReportMailList', //获取报告邮寄列表
  UnPrintSendRepor: '/ReportMailPrint/UnPrintSendReport', //未打印--邮寄报告
  UnMailedSendReport: '/ReportMailPrint/UnMailedSendReport', //未邮寄--邮寄报告
  UpdateReportMail: '/ReportMailPrint/UpdateReportMail', //修改报告邮寄数据

  // 问题中心
  IsExistQueue: '/QueueHub/IsExistQueue', //是否提出过问题
  Question: '/QueueHub/Question', //提出问题
  ReadQuestionListByCheck: '/QueueHub/ReadQuestionListByCheck', //获取主检本人的咨询列表
  GetPeQuestionToDoctors: '/QueueHub/GetPeQuestionToDoctors', //获取医生本人的待答复列表
  ReplyQuestion: '/QueueHub/ReplyQuestion', //答复问题
  GetPeQuestionlocks: '/QueueHub/GetPeQuestionlocks', //获取医生已答复且主检未解锁（已锁）的问题列表
  UnlockQuestion: '/QueueHub/UnlockQuestion', //主检医生解锁问题
  //基础代码
  PageQuery_PeClsDoctor: '/CodeMapping/PageQuery_PeClsDoctor', //获取体检分类-主检审核医生对应信息
  CD_PeClsDoctor: '/CodeMapping/CD_PeClsDoctor', ///CD_PeClsDoctor/Create 新增体检分类-主检审核医生对应信息/CD_PeClsDoctor/Delete 删除体检分类-主检审核医生对应信息

  // 数据查询
  PeComprehensiveQuery: '/DataQuery/PeComprehensiveQuery', //体检综合查询
  GetPatientBasicInfo: '/DataQuery/GetPatientBasicInfo', //获取患者基本信息
  GetLogData: '/DataQuery/GetLogData', //获取日志数据
  GetCriticalException: '/DataQuery/GetCriticalException', //获取危急异常
  PeFollowUpQuery: '/FollowUp/PeFollowUpQuery', //体检随访查询
  SavePeFollowUp: '/FollowUp/SavePeFollowUp', //保存体检随访
  InsertPeFollowUp: '/FollowUp/InsertPeFollowUp', //新增体检随访
  DeletePeFollowUp: '/FollowUp/DeletePeFollowUp', //删除体检随访
  CriticalExceptionQuery: '/DataQuery/CriticalExceptionQuery', //获取危急异常列表
  DeleteCriticalException: '/DataQuery/DeleteCriticalException', //删除危急异常列表
  InspectionDataQuery: '/DataQuery/InspectionDataQuery', //检验数据查询
  PeMissingItemQuery: '/DataQuery/PeMissingItemQuery', //体检漏项查询
  GenerateHealthCardXML: '/DataQuery/GenerateHealthCardXML', //生成可上传的健康证XML (Auth)

  // 公用API
  GetUserRecentMssage: '/CommonApi/GetUserRecentMssage', //获取用户最近的消息
  GetItemsInfo: '/DataQuery/GetItemsInfo', //获取项目情况
  GetExpenseList: '/DataQuery/GetExpenseList', //获取费用清单
  GetSampleData: '/DataQuery/GetSampleData', //获取标本数据
  UserReadNotice: '/Notice/UserReadNotice', //公告已读
  GetAllUntreatedCriticalValue: '/CommonApi/GetAllUntreatedCriticalValue', //获取所有未处理的危急值

  //工作量统计
  GetBasicStatistical: '/WorkloadStatistics/GetBasicStatistical', //获取基础工作量统计(图表)
  GetDeptPressure: '/WorkloadStatistics/GetDeptPressure', //获取科室压力统计
  GetDeptWorkload: '/WorkloadStatistics/GetDeptWorkload', //获取科室统计
  GetDoctorWorkload: '/WorkloadStatistics/GetDoctorWorkload', //获取医生工作量
  GetRegistrarWorkload: '/WorkloadStatistics/GetRegistrarWorkload', //获取登记员工作量
  GetApplicantWorkload: '/WorkloadStatistics/GetApplicantWorkload', //获取开单医生工作量
  GetGatherOperatorWorkload: '/WorkloadStatistics/GetGatherOperatorWorkload', //获取采集员工作量
  AddItemWorkloadReport: '/WorkloadStatistics/AddItemWorkloadReport', //加项工作量报表
  ActivateWorkloadReport: '/WorkloadStatistics/ActivateWorkloadReport', //激活工作量报表
  InputDoctorWorkloadReport: '/WorkloadStatistics/InputDoctorWorkloadReport', //录入员工作量报表
  CheckDoctorWorkloadReport: '/WorkloadStatistics/CheckDoctorWorkloadReport', //主检员工作量报表
  AuditDoctorWorkloadReport: '/WorkloadStatistics/AuditDoctorWorkloadReport', //审核员工作量报表

  // Notice
  CreateNotice: '/Notice/CreateNotice', //发布公告
  ReadNotices: '/Notice/ReadNotices', //获取公告列表
  //首页
  GetIndexPageStatistics: '/IndexPageStatistics/GetIndexPageStatistics', //获取首页数据

  //套餐、单位统计报表
  PackageInfoReport: '/PackageStatistics/PackageInfoReport', //套餐登记信息统计
  PersonnelFeeList: '/PackageStatistics/PersonnelFeeList', //体检人员费用列表
  CompanyPersonnelSummaryReport:
    '/CompanyStatistics/CompanyPersonnelSummaryReport', //单位人员汇总报表
  CompanyPersonnelDetailReport:
    '/CompanyStatistics/CompanyPersonnelDetailReport', //单位人员明细报表
  CompanyPeReport: '/CompanyStatistics/CompanyPeReport', //单位体检报告
  GetCompanyPatientClusters: '/CompanyStatistics/GetCompanyPatientClusters', //获取单位体检人员项目明细统计(单位体检人员套餐)
  GetCompanyPatientCombs: '/CompanyStatistics/GetCompanyPatientCombs', //获取单位体检人员项目汇总(单位体检人员组合)
  GetCompanyCombAmount: '/CompanyStatistics/GetCompanyCombAmount', //获取单位项目工作量统计（单位组合费用/次数）
  LoginUsers: '/Test/LoginUsers', //获取登录的用户列表
  QueryCompanyDetialCombPrice: '/CompanyStatistics/QueryCompanyDetialCombPrice', // 获取单位人员组合费用明细报表（交叉报表）

  // 重大阳性
  RD_MajorPositive: '/MajorPositive/RD_MajorPositive', // /RD_MajorPositive/Read 获取重大阳性信息 /RD_MajorPositive/Delete 删除重大阳性信息
  CU_MajorPositive: '/MajorPositive/CU_MajorPositive', ///CU_MajorPositive/Create 新增重大阳性信息 /CU_MajorPositive/Update 更新重大阳性信息
  CU_MajorPositiveKeyword: '/MajorPositive/CU_MajorPositiveKeyword', // /CU_MajorPositiveKeyword/Create 新增重大阳性关键字 /CU_MajorPositiveKeyword/Update 更新重大阳性关键字
  RD_MajorPositiveKeyword: '/MajorPositive/RD_MajorPositiveKeyword', // /RD_MajorPositiveKeyword/Read   获取重大阳性关键字 /RD_MajorPositiveKeyword/Delete 删除重大阳性关键字
  UpdateKeywordMajorPositive: '/CodeMapping/UpdateKeywordMajorPositive/Update', //Update修改重大阳性关键字-重大阳性对应信息的排序(仅支持排序,勿修改数据)
  SaveMajorPositiveExpression: '/MajorPositive/SaveMajorPositiveExpression', ///保存重大阳性判断计算式
  RD_MajorPositiveExpression: '/MajorPositive/RD_MajorPositiveExpression', ///RD_MajorPositiveExpression/Read 获取重大阳性判断计算式 /RD_MajorPositiveExpression/Delete 删除重大阳性判断计算式
  PageQuery_KeywordPositiveKeyword:
    '/CodeMapping/PageQuery_KeywordPositiveKeyword', // 重大阳性关键字-补充对应信息
  CU_KeywordPositiveKeyword: '/CodeMapping/CU_KeywordPositiveKeyword', // /CU_KeywordPositiveKeyword/Create 新增重大阳性关键字-补充对应信息 /CU_KeywordPositiveKeyword/Update 修改重大阳性关键字-补充对应信息
  Delete_KeywordPositiveKeyword: '/CodeMapping/Delete_KeywordPositiveKeyword', //Delete_KeywordPositiveKeyword 删除重大阳性关键字-补充对应信息
  GetSimpleItemResult: '/Record/GetSimpleItemResult', //获取简易项目结果(主检用到)
  PositiveResultReview: '/ReportConclusion/PositiveResultReview', //重大阳性结果审核
  SavePositiveResult: '/ReportConclusion/SavePositiveResult', //保存重大阳性结果
  MajorPositiveQuery: '/MajorPositive/QueryMajorPositiveList', //重大阳性查询
  DeleteMajorPositive: '/MajorPositive/DeleteMajorPositive', //删除重大阳性
  BatchDeleteMajorPositive: '/MajorPositive/BatchDeleteMajorPositive', //批量删除重大阳性结果 (Auth)
  VerifyExpression: '/CommonApi/VerifyExpression', //校验疾病计算公式
  OperationAndFunction: '/CommonApi/OperationAndFunction', //运算符及函数列表
  ConfirmMajorPositive: '/MajorPositive/ConfirmMajorPositive', //确认重大阳性
  CancelConfirmMajorPositive: '/MajorPositive/CancelConfirmMajorPositive', //取消确认重大阳性
  RestoreMajorPositive: '/MajorPositive/RestoreMajorPositive', //恢复重大阳性结果
  QueryDeleteMajorPositiveList: '/MajorPositive/QueryDeleteMajorPositiveList', //重大阳性删除记录查询
  ManualSaveMajorPositive: '/MajorPositive/ManualSaveMajorPositive', //手动添加重大阳性结果
  GetMajorPositiveKeywords: '/MajorPositive/GetMajorPositiveKeywords', //获取重大阳性关键字列表 (Auth)
  SaveMajorPositiveKeyword: '/MajorPositive/SaveMajorPositiveKeyword', //保存重大阳性关键字 (Auth)"
  BatchRemoveMajorPositiveKeyword:
    '/MajorPositive/BatchRemoveMajorPositiveKeyword', //删除重大阳性关键字 (Auth)
  GetMajorPositiveParts: '/MajorPositive/GetMajorPositiveParts', //获取重大阳性部位字列表 (Auth)'
  SaveMajorPositivePart: '/MajorPositive/SaveMajorPositivePart', //保存重大阳性部位 (Auth)
  GetMapPartWithKeywordMajorPositives:
    '/MajorPositive/GetMapPartWithKeywordMajorPositives', //{positiveCode}根据重大阳性代码获取部位与关键字关系信息 (Auth)"
  SaveMapPartWithKeywordMajorPositive:
    '/MajorPositive/SaveMapPartWithKeywordMajorPositive', //保存重大阳性部位、关键字的对应关系信息 (Auth)"
  BatchRemoveMajorPositivePart: '/MajorPositive/BatchRemoveMajorPositivePart', //删除重大阳性部位 (Auth)
  GetMajorPositiveCriteria: '/MajorPositive/GetMajorPositiveCriteria', // /{positiveCode}获取重大阳性审核条件 (Auth)
  SaveMajorPositiveCriteria: '/MajorPositive/SaveMajorPositiveCriteria', //保存重大阳性审核条件 (Auth)
  GetMajorPositiveCriteriaItems: '/MajorPositive/GetMajorPositiveCriteriaItems', //获取重大阳性审核项目列表 (Auth)
  SaveMajorPositiveCriteriaItem: '/MajorPositive/SaveMajorPositiveCriteriaItem', //保存重大阳性审核项目 (Auth)
  BatchMajorPositiveCriteriaItem:
    '/MajorPositive/BatchMajorPositiveCriteriaItem', //删除重大阳性审核条件 (Auth)
  // 报告进程查询
  PeNoReportQuery: '/DataQuery/PeNoReportQuery', //未出报告列表查询
  PeReportProcessQuery: '/DataQuery/PeReportProcessQuery', //体检报告进程查询
  PeReportProcessAnalize: '/DataQuery/PeReportProcessAnalize', //体检报告进程分析

  // 疾病统计报表
  InitCompanyTemplate: '/DiseaseStatistics/InitCompanyTemplate', //初始化单位查询模板
  CD_MapCompanyDiseaseCls: 'DiseaseStatistics/CD_MapCompanyDiseaseCls', // /CD_MapCompanyDiseaseCls/Create 新增单位疾病分类对应疾病/CD_MapCompanyDiseaseCls/Delete 新增单位疾病分类对应疾病
  DiseaseStatisticsByDiseaseCls:
    'DiseaseStatistics/DiseaseStatisticsByDiseaseCls', // 疾病情况统计报表(按大类)
  Read_MapCompanyDiseaseClsDisease:
    '/DiseaseStatistics/Read_MapCompanyDiseaseClsDisease', //获取单位疾病分类对应疾病
  FeeClsAmountsStatistics: '/FinancialStatistics/FeeClsAmountsStatistics', //财务分类统计
  PaymentReportStatistics: '/FinancialStatistics/PaymentReportStatistics', //缴款报表统计

  // 财务统计报表
  CompanySettlementByCombReport:
    '/FinancialStatistics/CompanySettlementByCombReport', //团体结算(按实际项目)
  CompanySettlementByFeeClsReport:
    '/FinancialStatistics/CompanySettlementByFeeClsReport', //团体结算(按费用分类)
  CompanyPersonAccountingReport:
    '/FinancialStatistics/CompanyPersonAccountingReport', //单位个人记账报表
  CompanySettlementByPersonDetails:
    '/FinancialStatistics/CompanySettlementByPersonDetails', //团体个人明细结算
  CompanySettlementByClusterReport:
    '/FinancialStatistics/CompanySettlementByClusterReport', //团体套餐结算
  CompanySettlementByFeeDetailReport:
    '/FinancialStatistics/CompanySettlementByFeeDetailReport', //团体明细费用报表
  ResearchAnalysis: '/DataQuery/ResearchAnalysis', //   科研项目分析
  SyncPayStatus: '/Register/SyncPayStatus', //同步个人缴费状态
  CU_ResearchAnalysisTemplate: '/BasicCode/CU_ResearchAnalysisTemplate', ///CU_ResearchAnalysisTemplate/Create 新增科研分析模板 /CU_ResearchAnalysisTemplate/Update 更新科研分析模板
  DeleteResearchAnalysisTemplate: '/BasicCode/DeleteResearchAnalysisTemplate', //DeleteResearchAnalysisTemplate 删除科研分析模板
  ReadResearchAnalysisTemplate: '/BasicCode/ReadResearchAnalysisTemplate', //ReadResearchAnalysisTemplate 获取科研分析模板

  ManuallyAcceptResults: '/Record/ManuallyAcceptResults', //手动接受结果
  AddNormalSuggestions: '/ReportConclusion/AddNormalSuggestions', //手动生成正常建议
  ChangeUserPassword: '/UserManage/ChangeUserPassword', //修改用户密码

  // 分院区
  GetHospitalInfo: '/UserManage/GetHospitalInfo', //获取院区列表
  NewAuditReportConclusion: '/ReportConclusion/NewAuditReportConclusion', //(新)审核报告结论 (Auth)

  PeItemResultListQuery: '/PackageStatistics/PeItemResultListQuery', //体检项目结果清单
  PeItemResultDetailListReportQuery:
    '/CompanyStatistics/PeItemResultDetailListReportQuery', //单位体检人员项目明细报表 (Auth)

  // 深汕的接口
  ShenShanLogin: '/ShenShan/Permission/Login', // 前端登录（根据统一门户Token）
  HisRefund: '/CheckStand/HisRefund', // 个人登记退费
  CancelHisRefund: '/CheckStand/CancelHisRefund', //   撤销HIS预退费
  GetOperatorListByRoleCode: '/EnumData/GetOperatorListByRoleCode', // 按角色获取操作员列表
  GetSampleDataToMachine: '/ShenShan/Machine/GetSampleDataToMachine', // 获取采血机条码数据接口
  PrintSample: '/Sample/PrintSample', //标本打印时间更新 (Auth)
  GatherSample: '/Sample/GatherSample', //标本采集状态更新 (Auth)
  GetCodeHisChargeItems: '/ExtenseBasicCode/GetCodeHisChargeItems', // 获取His收费项目集合 (Auth)
  GetMapCodeItemCombHisChargeItems:
    '/ExtenseBasicCode/GetMapCodeItemCombHisChargeItems', // 获取体检组合下对应的His收费项目信息 (Auth)
  SaveMapCodeItemCombHisChargeItems:
    '/ExtenseBasicCode/SaveMapCodeItemCombHisChargeItems', // 保存体检组合与His收费项目信息 (Auth)
  RemoveCodeHisChargeItems: '/ExtenseBasicCode/RemoveCodeHisChargeItems', // 删除体检组合与His收费项目信息 (Auth)
  GetCodeHisOrderItems: '/ExtenseBasicCode/GetCodeHisOrderItems', //获取His医嘱项目集合 (Auth)
  GetAllRecordImage: '/Record/GetAllRecordImage', //获取所有结果图像 (Auth)
  SyncCodeItemCombPrice: '/ExtenseBasicCode/SyncCodeItemCombPrice', //同步更新体检组合价格 (Auth)
  ManualInsertPeFollowUp: '/FollowUp/ManualInsertPeFollowUp', //手动新增体检随访 (Auth)
  CancelGatherSample: '/Sample/CancelGatherSample', //  取消标本采集 (Auth)
  ReadGatherSampleData: '/Sample/ReadGatherSampleData', //标本采集的查询接口
  HisBillListQuery: '/ShenShan/Statistics/HisBillListQuery', //获取收费信息列表 (Auth)
  GetReportRecieveLog: '/Record/GetReportRecieveLog', //获取第三方接收日志
  RecieveExtReport: '/Record/RecieveExtReport', // 接收结果
  SaveShortMsgTemplate: '/ShortMessage/SaveShortMsgTemplate', //保存短信模板 (Auth)
  GetShortMsgTemplate: '/ShortMessage/GetShortMsgTemplate', //获取短信模板 (Auth)
  GetShortMsgTemplates: '/ShortMessage/GetShortMsgTemplates', //获取短信模板集合 (Auth)
  GetShortMsgTemplateTypes: '/ShortMessage/GetShortMsgTemplateTypes', //获取短信模板类型 (Auth)
  NewShortMsgTemplate: '/ShortMessage/NewShortMsgTemplate', //新建短信模板初始化内容 (Auth)
  NewPeSendShortMsgRecord: '/ShortMessage/NewPeSendShortMsgRecord', //新建短信记录信息 (Auth)
  SavePeSendShortMsgRecord: '/ShortMessage/SavePeSendShortMsgRecord', //保存短信记录信息 (Auth)
  GetPeSendShortMsgRecord: '/ShortMessage/GetPeSendShortMsgRecord', //获取短信记录信息 (Auth)
  GetPeSendShortMsgRecords: '/ShortMessage/GetPeSendShortMsgRecords', //获取短信记录信息集合 (Auth)
  BatchRemovePeSendShortMsgRecord:
    '/ShortMessage/BatchRemovePeSendShortMsgRecord', //批量删除短信记录（已发送短信不能删除） (Auth)
  BatchAuditPeSendShortMsgRecord:
    '/ShortMessage/BatchAuditPeSendShortMsgRecord', //批量审核短信记录 (Auth)
  BatchSendShortMsgByPeShortMsgRecord:
    '/ShortMessage/BatchSendShortMsgByPeShortMsgRecord', //批量根据短信内容发送短信 (Auth)
  GetMapCodeLisItems: '/ExtenseBasicCode/GetMapCodeLisItems', //获取体检项目对应Lis项目集合查询 (Auth)
  GetCodeLisItems: '/ExtenseBasicCode/GetCodeLisItems', //获取Lis体检项目集合查询 (Auth)
  SaveMapCodeLisItems: '/ExtenseBasicCode/SaveMapCodeLisItems', //保存体检项目对应信息Lis项目信息(Auth)
  RemoveMapCodeLisItems: '/ExtenseBasicCode/RemoveMapCodeLisItems', //移除体检项目对应Lis项目信息 (Auth)
  CheckDoctorWorkloadDetailReport:
    '/WorkloadStatistics/CheckDoctorWorkloadDetailReport', //主检员工作量报表(明细)
  GetCodeHisDrugItems: '/ExtenseBasicCode/GetCodeHisDrugItems', //获取His药品集合 (Auth)
  GetCodeHisItemTypes: '/ExtenseBasicCode/GetCodeHisItemTypes', //获取组合项目对应类型 (Auth)
  BatchNewPeSendShortMsgRecord: '/ShortMessage/BatchNewPeSendShortMsgRecord', //批量新建短信记录信息 (Auth)
  BatchSavePeSendShortMsgRecord: '/ShortMessage/BatchSavePeSendShortMsgRecord', //批量保存短信记录信息 (Auth)
  GetExamCombList: '/Record/GetExamCombList', //获取检查组合导航列表（医生工作站、结果录入） (Auth)
  GetPersonalSettlementCombReport:
    '/ShenShan/Statistics/GetPersonalSettlementCombReport', // 获取个人收费日结组合统计报表

  GetQuestionDataByRegNo: '/Register/GetQuestionDataByRegNo', //通过体检号获取问卷数据 (Auth)
  GetReportConclusion: '/ReportConclusionNew/GetReportConclusion', //获取报告结论及综述建议
  GetReportConclusionV2: '/ReportConclusionV2/GetReportConclusion', //获取报告结论及综述建议
  EditSummaryTag: '/ReportConclusionNew/EditSummaryTag', //编辑综述标签
  EditSummaryTagV2: '/ReportConclusionV2/EditSummaryTag', //编辑综述标签

  //疾病管理词条
  GetCodeDiseases: '/DiseaseMgt/GetCodeDiseases', //获取疾病集合 (Auth)
  BatchCodeDiseasesToEntries: '/DiseaseMgt/BatchCodeDiseasesToEntries', //批量疾病词条转所属疾病词条 (Auth)
  TempSaveReportConclusion: '/ReportConclusionNew/TempSaveReportConclusion', //暂存主检审核 (Auth)
  TempSaveReportConclusionV2: '/ReportConclusionV2/TempSaveReportConclusion', //暂存主检审核 (Auth)
  ConfirmReportConclusion: '/ReportConclusionNew/ConfirmReportConclusion', //确认主检审核 (Auth)
  ConfirmReportConclusionV2: '/ReportConclusionV2/ConfirmReportConclusion', //确认主检审核 (Auth)
  GenerateSummarySuggestionFromRecord:
    '/ReportConclusionNew/GenerateSummarySuggestionFromRecord', //根据结果记录生成综述建议 (Auth)
  GenerateSummarySuggestionFromRecordV2:
    '/ReportConclusionV2/GenerateSummarySuggestionFromRecord', //根据结果记录生成综述建议 (Auth)
  CancelReportConclusionNew: '/ReportConclusionNew/CancelReportConclusion', //取消主检审核 (Auth)
  CalculateTustTubeCombs: '/Company/CalculateTustTubeCombs', //计算套餐试管、采血费用 (Auth)
  GetHisPatientIndex: '/ShenShan/Order/GetHisPatientIndex', //获取HIS索引号 (Auth)
  GetHistoryArchivesByHisCard: '/ShenShan/Order/GetHistoryArchivesByHisCard', //根据门诊卡号获取病人信息 (Auth)
  ReadCandidateCombItems: '/Register/ReadCandidateCombItems', // 获取候选组合的项目明细 (Auth)
  GetCompanySettlement: '/CompanySettlement/GetCompanySettlement', //获取结算记录 (Auth)
  QueryPersonListForSettlement:
    '/CompanySettlement/QueryPersonListForSettlement', //获取待结算人员信息 (Auth)
  CreateCompanySettlement: '/CompanySettlement/CreateCompanySettlement', //生成结算数据 (Auth)
  DeleteCompanySettlement: '/CompanySettlement/DeleteCompanySettlement', //删除结算数据 (Auth)
  AuditCompanySettlement: '/CompanySettlement/AuditCompanySettlement', //审核结算信息 产生HIS结算数据 (Auth)
  QueryCompanySettlementDetail:
    '/CompanySettlement/QueryCompanySettlementDetail', //结算费用明细报表 (Auth)
  UpdateCompanySettlement: '/CompanySettlement/UpdateCompanySettlement', //更新结算记录的发票抬头 (Auth)
  RefundCompanySettlement: '/CompanySettlement/RefundCompanySettlement', //退费申请 (Auth)
  CancelRefundCompanySettlement:
    '/CompanySettlement/CancelRefundCompanySettlement', //撤销退费申请 (Auth)
  HasFollowUp: '/FollowUp/HasFollowUp', //是否已随访 (Auth)
  QueryCompanySettlementDetailByPerson:
    '/CompanySettlement/QueryCompanySettlementDetailByPerson', //结算费用明细报表(按人员) (Auth)
  CalculateDiscountPrice: '/CompanySettlement/CalculateDiscountPrice', //用HIS字典价计算折后价 (Auth)
  CancelAuditCompanySettlement:
    '/CompanySettlement/CancelAuditCompanySettlement', //取消审核结算记录
  GetCompaniesWithTimes: '/Company/GetCompaniesWithTimes', // 获取单位和单位次数
  QueryCompanySettlementByChargeTime:
    '/CompanySettlement/QueryCompanySettlementByChargeTime', // 按结算时间获取单位结算记录 (Auth)
  GetExtHisApplyCombs: '/ShenShan/Report/GetExtHisApplyCombs', // 获取体检申请单组合列表
  BatchModifyExtHisApplyComb: '/ShenShan/Report/BatchModifyExtHisApplyComb', // 修改体检申请单组合

  //导检
  Call: '/Guide/Call', //叫号
  Done: '/Guide/Done', //完成
  Back: '/Guide/Back', //置后

  //=========== 职业病基础代码维护 ===========
  // 证件类型
  CreateCodeOccupationalCardType:
    '/Occupation/BasicCode/CU_CodeOccupationalCardType/Create', // 新增
  UpdateCodeOccupationalCardType:
    '/Occupation/BasicCode/CU_CodeOccupationalCardType/Update', // 修改
  ReadCodeOccupationalCardType:
    '/Occupation/BasicCode/RD_CodeOccupationalCardType/Read', // 获取
  DeleteCodeOccupationalCardType:
    '/Occupation/BasicCode/RD_CodeOccupationalCardType/Delete', // 删除
  CreateMapCardType: '/Occupation/CodeMapping/CD_MapCardType/Create', // 新建证件类型映射
  DeleteMapCardType: '/Occupation/CodeMapping/CD_MapCardType/Delete', // 删除证件类型映射
  QueryMapCardType: '/Occupation/CodeMapping/Query_MapCardType', // 证件类型映射查询

  // 婚姻状况
  CreateCodeOccupationalMarryStatus:
    '/Occupation/BasicCode/CU_CodeOccupationalMarryStatus/Create', // 新增
  UpdateCodeOccupationalMarryStatus:
    '/Occupation/BasicCode/CU_CodeOccupationalMarryStatus/Update', // 修改
  ReadCodeOccupationalMarryStatus:
    '/Occupation/BasicCode/RD_CodeOccupationalMarryStatus/Read', // 获取
  DeleteCodeOccupationalMarryStatus:
    '/Occupation/BasicCode/RD_CodeOccupationalMarryStatus/Delete', // 删除
  CreateMapMarryStatus: '/Occupation/CodeMapping/CD_MapMarryStatus/Create', // 新建婚姻映射
  DeleteMapMarryStatus: '/Occupation/CodeMapping/CD_MapMarryStatus/Delete', // 删除婚姻映射
  QueryMapMarryStatus: '/Occupation/CodeMapping/Query_MapMarryStatus', // 婚姻映射查询

  // 岗位状态
  CreateCodeOccupationalPositionStatus:
    '/Occupation/BasicCode/CU_CodeOccupationalPositionStatus/Create', // 新增
  UpdateCodeOccupationalPositionStatus:
    '/Occupation/BasicCode/CU_CodeOccupationalPositionStatus/Update', // 修改
  ReadCodeOccupationalPositionStatus:
    '/Occupation/BasicCode/RD_CodeOccupationalPositionStatus/Read', // 获取
  DeleteCodeOccupationalPositionStatus:
    '/Occupation/BasicCode/RD_CodeOccupationalPositionStatus/Delete', // 删除

  // 经济类型
  CreateCodeOccupationalEconomicType:
    '/Occupation/BasicCode/CU_CodeOccupationalEconomicType/Create', // 新增
  UpdateCodeOccupationalEconomicType:
    '/Occupation/BasicCode/CU_CodeOccupationalEconomicType/Update', // 修改
  ReadCodeOccupationalEconomicType:
    '/Occupation/BasicCode/RD_CodeOccupationalEconomicType/Read', // 获取
  DeleteCodeOccupationalEconomicType:
    '/Occupation/BasicCode/RD_CodeOccupationalEconomicType/Delete', // 删除

  // 行业分类
  CreateCodeOccupationalIndustry:
    '/Occupation/BasicCode/CU_CodeOccupationalIndustry/Create', // 新增
  UpdateCodeOccupationalIndustry:
    '/Occupation/BasicCode/CU_CodeOccupationalIndustry/Update', // 修改
  ReadCodeOccupationalIndustryByPage:
    '/Occupation/BasicCode/PageQuery_CodeOccupationalIndustry', // 分页获取
  DeleteCodeOccupationalIndustry:
    '/Occupation/BasicCode/Delete_CodeOccupationalIndustry', // 删除

  // 职业禁忌证
  CreateCodeOccupationalContraindication:
    '/Occupation/BasicCode/CU_CodeOccupationalContraindication/Create', // 新增
  UpdateCodeOccupationalContraindication:
    '/Occupation/BasicCode/CU_CodeOccupationalContraindication/Update', // 修改
  ReadCodeOccupationalContraindicationByPage:
    '/Occupation/BasicCode/PageQuery_CodeOccupationalContraindication', // 分页获取
  DeleteCodeOccupationalContraindication:
    '/Occupation/BasicCode/Delete_CodeOccupationalContraindication', // 删除

  // 疑似职业病
  CreateCodeOccupationalDisease:
    '/Occupation/BasicCode/CU_CodeOccupationalDisease/Create', // 新增
  UpdateCodeOccupationalDisease:
    '/Occupation/BasicCode/CU_CodeOccupationalDisease/Update', // 修改
  ReadCodeOccupationalDiseaseByPage:
    '/Occupation/BasicCode/PageQuery_CodeOccupationalDisease', // 分页获取
  DeleteCodeOccupationalDisease:
    '/Occupation/BasicCode/Delete_CodeOccupationalDisease', // 删除

  // 项目计量单位
  CreateCodeOccupationalItemUnit:
    '/Occupation/BasicCode/CU_CodeOccupationalItemUnit/Create', // 新增
  UpdateCodeOccupationalItemUnit:
    '/Occupation/BasicCode/CU_CodeOccupationalItemUnit/Update', // 修改
  ReadCodeOccupationalItemUnitByPage:
    '/Occupation/BasicCode/PageQuery_CodeOccupationalItemUnit', // 分页获取
  DeleteCodeOccupationalItemUnit:
    '/Occupation/BasicCode/Delete_CodeOccupationalItemUnit', // 删除

  // 报告模块
  ReadReportModel: '/Occupation/BasicCode/ReadReportModel', // 获取
  CreateMapReportModelItem:
    '/Occupation/CodeMapping/CD_MapReportModelItem/Create', // 新建检项目对应报告模块
  DeleteMapReportModelItem:
    '/Occupation/CodeMapping/CD_MapReportModelItem/Delete', // 删除体检项目对应报告模块
  ReadMapReportModelItem: '/Occupation/CodeMapping/Query_MapReportModelItem', // 获取体检项目对应报告模块

  // 地区
  CreateCodeOccupationalAddress:
    '/Occupation/BasicCode/CU_CodeOccupationalAddress/Create', // 新增
  UpdateCodeOccupationalAddress:
    '/Occupation/BasicCode/CU_CodeOccupationalAddress/Update', // 修改
  DeleteCodeOccupationalAddress:
    '/Occupation/BasicCode/Delete_CodeOccupationalAddress', // 删除
  ReadCodeOccupationalAddressByPage:
    '/Occupation/BasicCode/PageQuery_CodeOccupationalAddress', //   分页获取

  // 危害因素
  CreateCodeOccupationalHazardous:
    '/Occupation/BasicCode/CU_CodeOccupationalHazardous/Create', // 新增
  UpdateCodeOccupationalHazardous:
    '/Occupation/BasicCode/CU_CodeOccupationalHazardous/Update', // 修改
  DeleteCodeOccupationalHazardous:
    '/Occupation/BasicCode/Delete_CodeOccupationalHazardous', // 删除
  ReadCodeOccupationalHazardousByPage:
    '/Occupation/BasicCode/PageQuery_CodeOccupationalHazardous', //   分页获取
  CreateMapHazardousComb:
    '/Occupation/CodeMapping/CD_MapOccupationHazardousComb/Create', //  创建危害因素组合对应
  DeleteMapHazardousComb:
    '/Occupation/CodeMapping/CD_MapOccupationHazardousComb/Delete', //   删除危害因素组合对应
  ReadMapHazardousComb: '/Occupation/CodeMapping/Query_MapHazardousComb', //   获取危害因素组合对应

  // 检查项目限定
  CreateCodeOccupationalItemLimit:
    '/Occupation/BasicCode/CU_CodeOccupationalItemLimit/Create', // 新增
  UpdateCodeOccupationalItemLimit:
    '/Occupation/BasicCode/CU_CodeOccupationalItemLimit/Update', // 修改
  DeleteCodeOccupationalItemLimit:
    '/Occupation/BasicCode/RD_CodeOccupationalItemLimit/Delete', // 删除
  ReadCodeOccupationalItemLimit:
    '/Occupation/BasicCode/RD_CodeOccupationalItemLimit/Read', // 获取

  // 职业病体检项目
  CreateCodeOccupationalItem:
    '/Occupation/BasicCode/CU_CodeOccupationalItem/Create', // 新增
  UpdateCodeOccupationalItem:
    '/Occupation/BasicCode/CU_CodeOccupationalItem/Update', // 修改
  DeleteCodeOccupationalItem:
    '/Occupation/BasicCode/Delete_CodeOccupationalItem', // 删除
  ReadCodeOccupationalItemByPage:
    '/Occupation/BasicCode/PageQuery_CodeOccupationalItem', //   分页获取
  CreateMapItemUnit: '/Occupation/CodeMapping/CD_MapItemUnit/Create', // 创建项目计量单位映射
  DeleteMapItemUnit: '/Occupation/CodeMapping/CD_MapItemUnit/Delete', // 删除项目计量单位映射
  ReadMapItemUnit: '/Occupation/CodeMapping/Query_MapItemUnit', // 获取项目计量单位映射
  CreateMapItemPeItem: '/Occupation/CodeMapping/CD_MapItemPeItem/Create', // 新建体检项目对应数据
  DeleteMapItemPeItem: '/Occupation/CodeMapping/CD_MapItemPeItem/Delete', // 删除体检项目对应数据
  ReadMapItemPeItem: '/Occupation/CodeMapping/Query_MapItemPeItem', // 获取体检项目对应数据

  //职业病套餐
  RD_CodeClusterOccupation: '/Occupation/BasicCode/RD_CodeCluster', //获取/删除职业病套餐信息
  CU_CodeClusterOccupation: '/Occupation/BasicCode/CU_CodeCluster', //新增/更新职业病套餐信息

  // 职业病体检结论
  CreateCodeOccupationalConclusion:
    '/Occupation/BasicCode/CU_CodeOccupationalConclusion/Create', // 新增
  UpdateCodeOccupationalConclusion:
    '/Occupation/BasicCode/CU_CodeOccupationalConclusion/Update', // 修改
  DeleteCodeOccupationalConclusion:
    '/Occupation/BasicCode/RD_CodeOccupationalConclusion/Delete', // 删除
  ReadCodeOccupationalConclusion:
    '/Occupation/BasicCode/RD_CodeOccupationalConclusion/Read', // 获取

  // 职业工病种
  CreateCodeOccupationalJob:
    '/Occupation/BasicCode/CU_CodeOccupationalJob/Create', // 新增
  UpdateCodeOccupationalJob:
    '/Occupation/BasicCode/CU_CodeOccupationalJob/Update', // 修改
  DeleteCodeOccupationalJob: '/Occupation/BasicCode/Delete_CodeOccupationalJob', // 删除
  ReadCodeOccupationalJobByPage:
    '/Occupation/BasicCode/PageQuery_CodeOccupationalJob', // 分页获取

  // 症状询问
  CreateCodeOccupationalSymptom:
    '/Occupation/BasicCode/CU_CodeOccupationalSymptom/Create', // 新增
  UpdateCodeOccupationalSymptom:
    '/Occupation/BasicCode/CU_CodeOccupationalSymptom/Update', // 修改
  DeleteCodeOccupationalSymptom:
    '/Occupation/BasicCode/RD_CodeOccupationalSymptom/Delete', // 删除
  ReadCodeOccupationalSymptom:
    '/Occupation/BasicCode/RD_CodeOccupationalSymptom/Read', // 获取
  CreateMapOccupationalSymptomPeItem:
    '/Occupation/CodeMapping/CD_MapOccupationalSymptomPeItem/Create', // 新建症状与体检项目映射
  DeleteMapOccupationalSymptomPeItem:
    '/Occupation/CodeMapping/CD_MapOccupationalSymptomPeItem/Delete', // 删除症状与体检项目映射
  QueryMapOccupationalSymptomPeItem:
    '/Occupation/CodeMapping/Query_MapSymptomPeItem', // 症状与体检项目映射查询

  //听力校正值字典维护
  CreateCodeAuditoryCorrectedValue:
    '/Occupation/BasicCode/CU_CodeOccupationalAuditoryCorrectedValue/Create', // 新增
  UpdateCodeAuditoryCorrectedValue:
    '/Occupation/BasicCode/CU_CodeOccupationalAuditoryCorrectedValue/Update', // 新增
  DeleteCodeAuditoryCorrectedValue:
    '/Occupation/BasicCode/RD_CodeOccupationalAuditoryCorrectedValue/Delete', // 删除
  ReadCodeAuditoryCorrectedValue:
    '/Occupation/BasicCode/RD_CodeOccupationalAuditoryCorrectedValue/Read', // 获取

  // 重点监测项目
  SaveCodeOccupationalImportantMonitorItem:
    '/Occupation/BasicCode/SaveCodeOccupationalImportantMonitorItem',
  DeleteCodeOccupationalImportantMonitorItem:
    '/Occupation/BasicCode/DeleteCodeOccupationalImportantMonitorItem',
  ReadCodeOccupationalImportantMonitorItem:
    '/Occupation/BasicCode/ReadCodeOccupationalImportantMonitorItem',
  // 新版登记
  GetRegisterOrderNew: '/RegisterNew/GetRegisterOrder', //获取订单的资料、套餐、组合 (Auth)
  ReadCandidateClusterNew: '/RegisterNew/ReadCandidateCluster', //获取候选套餐列表（套餐、绑定的组合、互斥的组合） (Auth)
  ReadCandidateCombNew: '/RegisterNew/ReadCandidateComb', //获取候选组合列表（组合、绑定的组合、互斥的组合） (Auth)
  RealtimeCalculateComb: '/RegisterNew/RealtimeCalculateComb', //实时计算增删组合 (Auth)
  RealtimeCalculateCluster: '/RegisterNew/RealtimeCalculateCluster', //实时计算增删套餐 (Auth)
  SaveRegisterOrderNew: '/RegisterNew/SaveRegisterOrder', //SaveRegisterOrder/Person 保存个人订单 SaveRegisterOrder/Company 保存单位订单 (Auth)
  GetHistoryArchivesNew: '/RegisterNew/GetHistoryArchives', //获取体检人的历史档案资料 (Auth)
  RealtimeCalculateHazardFacotr: '/RegisterNew/RealtimeCalculateHazardFacotr', //实时计算增删套餐
  GetRegistersByMultipleFilterNew: '/RegisterNew/GetRegistersByMultipleFilter', //获取登记资料列表（多筛查条件） (Auth)
  AlterRegisterPatientNew: '/RegisterNew/AlterRegisterPatient', //修改体检人信息 (Auth)
  GetRegisterByQueryTypeNew: '/RegisterNew/GetRegisterByQueryType', //获取登记资料列表 (Auth)
  RealtimeCalculateTestTubeNew: '/RegisterNew/RealtimeCalculateTestTube', //实时计算材料费 (Auth)
  ReadOccupationalCandidateCluster:
    '/RegisterNew/ReadOccupationalCandidateCluster', //获取候选危害因素套餐列表（套餐、绑定的组合、互斥的组合）
  ReadExamItemEntryReport: '/DataQuery/ReadExamItemEntryReport', //检查项目明细报表
  ReadRegisterClusterComb: '/RegisterNew/ReadRegisterClusterComb', //根据体检号获取套餐组合记录

  // 新版-医生工作站、结果录入
  GetPatientListByDoctorV2: '/RecordNew/GetPatientListByDoctor', // 录入结果人员列表(医生工作站) (Auth)
  GetExamCombListV2: '/RecordNew/GetExamCombList', // 获取检查组合导航列表（医生工作站、结果录入） (Auth)
  ReadOperDept: '/RecordNew/ReadOperDept', // 获取医生跨科科室 (Auth)
  SaveRecordCombV2: '/RecordNew/SaveRecordComb', // 保存组合项目结果(Auth)
  DeleteRecordCombV2: '/RecordNew/DeleteRecordComb', // 删除组合项目结果记录(Auth)
  CalculateAuditoryResult: '/Occupation/Record/CalculateAuditoryResult', // 计算纯音测听结论 (Auth)
  SaveAuditoryResult: '/Occupation/Record/SaveAuditoryResult', // 保存纯音测听结论 (Auth)
  ReadAuditoryResult: '/Occupation/Record/ReadAuditoryResult', // 读取听力测试结果 (Auth)
  DeleteAuditoryResult: '/Occupation/Record/DeleteAuditoryResult', // 删除听力测试结果 (Auth)
  SaveSmokingHistory: '/Occupation/Record/SaveSmokingHistory', // 保存吸烟史结果 (Auth)
  ReadSmokingHistory: '/Occupation/Record/ReadSmokingHistory', // 读取吸烟史结果 (Auth)
  DeleteSmokingHistory: '/Occupation/Record/DeleteSmokingHistory', // 删除吸烟史结果 (Auth)
  QueryPrintFileList: '/PrintFile/QueryPrintFileList', //获取可打印列表
  UpdatePrintStatus: '/PrintFile/UpdatePrintStatus', //打印状态更新
  ReadCopyPersonList: '/RegisterNew/ReadCopyPersonList', //查询人员列表用于复制组合 (Auth)
  GetGatheredRegisterList: '/Test/GetGatheredRegisterList', // 获取已采集的登记人员列表
  AbandonCombV2: '/RecordNew/AbandonComb', // 删除组合项目结果记录(Auth)
  GetSymptomResults: '/Occupation/Record/GetSymptomResults', // 获取症状结果 (Auth)
  SaveSymptomResults: '/Occupation/Record/SaveSymptomResults', // 保存症状结果 (Auth)

  // 新版主检
  GetSimpleItemResultNew: '/RecordNew/GetSimpleItemResult', //获取简易项目结果(主检用到) (Auth)
  GetPatientList4NotAllocateV2:
    '/ReportConclusionV2/GetPatientList4NotAllocate', //获取主检的人员列表（主检不分配）/审核的人员列表 (Auth)
  CancelReportConclusionNewV2: '/ReportConclusionV2/CancelReportConclusion', //取消主检审核v2
  AuditReportConclusionV2: '/ReportConclusionV2/AuditReportConclusion', //一键审核v2
  CancelAuditReportConclusionV2:
    '/ReportConclusionV2/CancelAuditReportConclusion', //取消审核v2

  ReturnCheckedNew: '/ReportConclusionV2/ReturnChecked', //撤回主检
  GetNotReplyReturnNew: '/ReportConclusionV2/GetNotReplyReturn', //获取未答复的撤回主检的信息
  ReplyReturnNew: '/ReportConclusionV2/ReplyReturn', //答复撤回主检
  GetReturnCheckedNew: '/ReportConclusionV2/GetReturnChecked', //获取撤回主检记录（主检回复）
  // 职业病报表
  GetExportPersonList: '/Occupation/OccupationReport/GetExportPersonList', //获取疑似职业病/职业禁忌证/复查导出人员列表 (Auth)
  GetOccupationalDiseaseNoticeTemplate:
    '/Occupation/OccupationReport/GetOccupationalDiseaseNoticeTemplate', //疑似职业病告知书 (Auth)
  GetOccupationalDiseaseCardTemplate:
    '/Occupation/OccupationReport/GetOccupationalDiseaseCardTemplate', //疑似职业病报告卡 (Auth)
  OccupationalContraindicationNoticeTemplate:
    '/Occupation/OccupationReport/OccupationalContraindicationNoticeTemplate', //职业禁忌证告知书 (Auth)
  GetOccupationalContraindicationCardTemplate:
    '/Occupation/OccupationReport/GetOccupationalContraindicationCardTemplate', //职业禁忌证报告卡 (Auth)
  GetOccupationalRecheckNoticeTemplate:
    '/Occupation/OccupationReport/GetOccupationalRecheckNoticeTemplate', //复查通知书 (Auth)
  GetImportantMonitorItemReport:
    '/Occupation/OccupationReport/GetImportantMonitorItemReport', //重点职业病监测报表 (Auth)
  UploadPhotoNew: '/CommonApi/UploadPhotoNew', //上传体检人图片新 (Auth)
  NewGetReportPrintList: '/ReportMailPrint/NewGetReportPrintList', // 获取报告打印列表 (Auth)
  ReadRecheckInfo: '/RegisterNew/ReadRecheckInfo', //获取复查信息 (Auth)
  CreateRecheckOrder: '/RegisterNew/CreateRecheckOrder', //生成复查订单 (Auth)
  CancelRecheckOrder: '/RegisterNew/CancelRecheckOrder', //撤销复查 (Auth)
  GetOccupationalFinalReport:
    '/Occupation/OccupationReport/GetOccupationalFinalReport', //职业病单位总结报告 (Auth)
  ReadOccupationUploadPersons: '/Upload/ReadOccupationUploadPersons', //获取职业病上传人员列表 (Auth)
  UploadPersonReport: '/Upload/UploadPersonReport', //上传人员信息 (Auth)
  QueryDeletePeFollowUp: '/FollowUp/QueryDeletePeFollowUp', //随访删除记录查询
  RestorePeFollowUp: '/FollowUp/RestorePeFollowUp', //随访录恢复
  CompletelyDeletePeFollowUp: '/FollowUp/CompletelyDeletePeFollowUp', //随访记录彻底删除
  // 自定义疾病管理
  GetCustomDiseaseSummaries: '/DiseaseMgt/GetCustomDiseaseSummaries', //汇总统计自定义疾病 (Auth)
  GetCustomDiseaseSuggestions: '/DiseaseMgt/GetCustomDiseaseSuggestions', //获取自定义疾病建议 (Auth)
  // 报告数据模型设计
  List: '/ReportDataModel/List', //获取数据模型列表
  ReportDataModelCreate: '/ReportDataModel/Create', //创建数据模型
  ReportDataModelUpdate: '/ReportDataModel/Update', //更新数据模型
  ReportDataModelDelete: '/ReportDataModel/Delete', //删除数据模型
  ResultSetList: '/ReportDataModel/ResultSet/List', //获取数据模型的结果集列表
  ResultSetCreate: '/ReportDataModel/ResultSet/Create', //创建数据模型的结果集
  ResultSetUpdate: '/ReportDataModel/ResultSet/Update', //更新数据模型的结果集
  ResultSetDelete: '/ReportDataModel/ResultSet/Delete', //删除数据模型的结果集
  UpdateClusterComb: '/CodeMapping/UpdateClusterComb', //更新体检套餐-组合对应信息
  GetDiseaseInfo: '/DiseaseMgt/GetDiseaseInfo', //通过关键字获取疾病新消息
  GetSplitPdfByPages: '/CommonApi/GetSplitPdfByPages', //获取切割的pdf文件
  RecordImages: '/RecordNew/RecordImages', //获取影像图例列表 (Auth)
  CancelReturnChecked: '/ReportConclusionV2/CancelReturnChecked', //取消撤回主检
  GettReturnCheckedList: '/ReportConclusionV2/GettReturnCheckedList', //获取撤回主检记录列表
  GetCodeFollowUpResultTemplates: '/BasicCode/GetCodeFollowUpResultTemplates', //获取随访处理结果模版内容
  SaveCodeFollowUpResultTemplate: '/BasicCode/SaveCodeFollowUpResultTemplate', //保存随访处理结果模版内容
  DeleteCodeFollowUpResultTemplate:
    '/BasicCode/DeleteCodeFollowUpResultTemplate', //删除随访处理结果模版内容
  HasNotHandledFollowUp: '/FollowUp/HasNotHandledFollowUp', //查询是否有未处理的随访
  GetDiseasesByName: '/DiseaseMgt/GetDiseasesByName', //根据疾病名获取疾病信息(Auth)
  EditSuggesstionDisease: '/ReportConclusionNew/EditSuggesstionDisease', //编辑疾病建议 (Auth)
  ExportPeItemResultListQuery: '/PackageStatistics/ExportPeItemResultListQuery', //体检项目结果清单导出 (Auth)
  SaveOccupationalConsultation:
    '/Occupation/Record/SaveOccupationalConsultation', //保存职业问诊结果
  GetOccupationalConsultation: '/Occupation/Record/GetOccupationalConsultation', //获取职业问诊结果
  DiseaseRateStatistic: '/DiseaseStatistics/DiseaseRateStatistic', //疾病患病率统计报表
  CreateCodeDiseaseStatAgeRange: '/DiseaseMgt/CreateCodeDiseaseStatAgeRange', //新增疾病统计年龄段 (Auth)
  UpdateCodeDiseaseStatAgeRange: '/DiseaseMgt/UpdateCodeDiseaseStatAgeRange', //更新疾病统计年龄段 (Auth)
  DeleteCodeDiseaseStatAgeRange: '/DiseaseMgt/DeleteCodeDiseaseStatAgeRange', //删除疾病统计年龄段 (Auth)
  ReadCodeDiseaseStatAgeRange: '/DiseaseMgt/ReadCodeDiseaseStatAgeRange', //获取疾病统计年龄段 (Auth)
  DiseaseRateDistributionByAge:
    '/DiseaseStatistics/DiseaseRateDistributionByAge', //疾病按年龄分布统计报表 (Auth)
  UpdateQuestionData: '/RegisterNew/UpdateQuestionData', //更新微信问卷数据 (Auth)
  // 短信发送
  QueryBatchSendShortMessage: '/ShortMessageBatch/QueryBatchSendShortMessage', //查询批量发送短信记录 (Auth)
  QueryPatientInfo: '/ShortMessageBatch/QueryPatientInfo', //查询人员信息 (Auth)
  CreateShortMsgAndSend: '/ShortMessageBatch/CreateShortMsgAndSend', //创建并发送短信 (Auth)
  BatchImportShortMsgAndSend: '/ShortMessageBatch/BatchImportShortMsgAndSend', //短信批量导入发送 (Auth)

  ReadHistorySummary: '/ReportConclusionV2/ReadHistorySummary', //获取历史综述 (Auth)
  ReadHistoryCombResult: '/ReportConclusionV2/ReadHistoryCombResult', //获取历史综述 (Auth)
  GetMajorPositiveDetailToDos: '/MajorPositive/GetMajorPositiveDetailToDos', //获取重大阳性待办列表 (Auth)
  UploadSignature: '/CommonApi/UploadSignature', //上传签名图片
  GetCombWithDiseCounts: '/DiseaseMgt/GetCombWithDiseCounts', //获取带有统计疾病数的组合信息 (Auth)
  GetMapDiseaseClsCombs: '/DiseaseMgt/GetMapDiseaseClsCombs', //查询疾病分类对应组合内容 (Auth)
  BatchSaveMapDiseaseClsComb: '/DiseaseMgt/BatchSaveMapDiseaseClsComb', //保存疾病分类对应组合内容 (Auth)
  BatchRemoveMapDiseaseClsComb: '/DiseaseMgt/BatchRemoveMapDiseaseClsComb', // /{diseaseClsCode},//删除疾病分类对应组合内容 (Auth)
  UploadReportGraphFile: '/CommonApi/UploadReportGraphFile', //上传检查图文报告
  DeleteReportGraphicText: '/RecordNew/DeleteReportGraphicText', //删除图文报告 (Auth)
  ReadMedicalAdvice: '/ReportConclusionV2/ReadMedicalAdvice', //获取医疗指导 (Auth)
  SaveMedicalAdvice: '/ReportConclusionV2/SaveMedicalAdvice', //保存医疗指导 (Auth)
  UploadCompany: '/Upload/UploadCompany', //上传人员信息 (Auth)
  GetCodeClusterWithDetails: '/BasicCode/GetCodeClusterWithDetails', //获取套餐及其明细内容 (Auth)
  OccGetCodeClusterWithDetails:
    '/Occupation/BasicCode/GetCodeClusterWithDetails', //获取套餐及其明细内容 (Auth)
  DownloadCompanyYearAnalyseFile:
    '/CompanyStatistics/DownloadCompanyYearAnalyseFile', //下载单位年度报告分析文件 (Auth)
  BatchSaveMutexCodeMapComb: '/BasicCode/BatchSaveMutexCodeMapComb' //批量保存互斥组合明细 (Auth)
};
