import { fetchEventSource } from '@microsoft/fetch-event-source';
import { Message } from 'element-ui';

export const sendChatMessage = async (url, settings, onMessage, onClose) => {
  const options = {
    method: settings.method || 'POST',
    headers: settings.headers || {
      Authorization: '',
      'Content-Type': 'application/json'
    },
    body: settings.body,
    signal: settings.signal,
    retry: 0,
    retryInterval: 0,
    openWhenHidden: true,
    async onmessage(ev) {
      onMessage(ev);
    },
    //会话发送完毕时触发
    onclose() {
      onClose();
    },
    onerror(err) {
      console.error('Error occurred:', err);
      Message.error('请求出错了，请稍后再试:' + err.message);
      onClose();
      throw err;
    }
  };

  fetchEventSource(url, options);
};
