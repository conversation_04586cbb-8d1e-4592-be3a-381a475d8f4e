/**
 * @FilePath: \KrPeis\src\common\zipUtil.js
 * @Description: 多个文件压缩为一个zip文件工具
 * @Author: justin
 * @Date: 2024-05-11 13:41:18
 * @Version: 0.0.1
 * @LastEditors: justin
 * @LastEditTime: 2024-05-11 16:27:31
 
 */
import JSZip from 'jszip';
import FileSaver from 'file-saver';
import { ajax } from '@/common';
import { message } from './resetMessage';
import { Loading } from 'element-ui';
const regexDomain =
  /^(?:https?:\/\/)?(?:[^@\n]+@)?(?:www\.)?([^:/\n]+)(:[0-9]+)?/gi;

/**
 * @author: justin
 * @description: 批量下载文件并打包成zip文件
 * @param {*} fileList 文件列表
 * @param {*} fileName 压缩包名称
 * @return {*}
 **/
export function batchDownloadFile(fileList, fileName = '未命名') {
  if (!fileList || fileList.length === 0) {
    return message.error('批量下载的文件不能为空！');
  }

  if (fileList.length <= 1) {
    return message.error('需要批量下载的文件数量不能少于2个！');
  }

  for (let item of fileList) {
    if (!item.url) {
      return message.error('[url]：下载文件链接不能为空！');
    }

    if (!item.name) {
      return message.error('[name]：下载文件名称不能为空！');
    }
  }

  let zip = new JSZip();
  let promises = [];
  for (let item of fileList) {
    const hostUrlList = item.url.match(regexDomain);
    let options = {
      responseType: 'arraybuffer',
      baseURL: hostUrlList && hostUrlList.length > 0 ? hostUrlList[0] : null
    };
    const promise = ajax.get(item.url, options).then((res) => {
      zip.file(item.name, res.data, { binary: true });
    });
    promises.push(promise);
  }

  Promise.all(promises)
    .then(() => {
      zip.generateAsync({ type: 'blob' }).then((content) => {
        FileSaver.saveAs(content, `${fileName}.zip`);
        setTimeout(() => {
          message.success('批量下载文件成功！');
        }, 100);
      });
    })
    .catch((res) => {
      console.error(res);
      setTimeout(() => {
        message.error('批量下载文件失败，请稍后重试！');
      }, 100);
    });
}

export async function newBatchDownloadFile(fileList, fileName = '未命名') {
  if (!fileList || fileList.length === 0) {
    return message.error('批量下载的文件不能为空！');
  }

  if (fileList.length <= 1) {
    return message.error('需要批量下载的文件数量不能少于2个！');
  }

  for (let item of fileList) {
    if (!item.url) {
      return message.error('[url]：下载文件链接不能为空！');
    }

    if (!item.name) {
      return message.error('[name]：下载文件名称不能为空！');
    }
  }

  let zip = new JSZip();
  let batchSize = 50; // 每次发起
  const loading = Loading.service({
    text: '正在下载文件...',
    fullscreen: true
  });
  const processBatch = async (batch) => {
    let batchPromises = batch.map((item) => {
      const hostUrlList = item.url.match(regexDomain);
      let options = {
        responseType: 'arraybuffer',
        baseURL: hostUrlList && hostUrlList.length > 0 ? hostUrlList[0] : null,
        timeout: 0
      };

      return ajax.get(item.url, options).then((res) => {
        zip.file(item.name, res.data, { binary: true });
      });
    });
    await Promise.all(batchPromises);
  };

  // 将文件列表分批处理
  for (let i = 0; i < fileList.length; i += batchSize) {
    const batch = fileList.slice(i, i + batchSize);
    await processBatch(batch); // 每次处理一个批次
  }

  // 所有请求完成后，生成 zip 文件
  zip
    .generateAsync({ type: 'blob' })
    .then((content) => {
      FileSaver.saveAs(content, `${fileName}.zip`);
      setTimeout(() => {
        message.success('批量下载文件成功！');
      }, 100);
    })
    .catch((err) => {
      console.error(err);
      setTimeout(() => {
        message.error('批量下载文件失败，请稍后重试！');
      }, 100);
    })
    .finally(() => {
      loading.close();
    });
}
