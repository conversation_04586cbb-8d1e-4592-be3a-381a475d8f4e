export default {
  data() {
    return {
      isOpenKeysFlag: false
    };
  },
  methods: {
    test1() {
      console.log(1111);
    },
    test2() {
      console.log(2222);
    },
    // 键盘按下事件
    keyDownFun(e) {
      let keyArr = Object.keys(this.shortcutList);
      if (keyArr.includes(e.keyCode + '')) {
        e.preventDefault();
      }
    },
    // 表格的键盘事件
    keyUpFun(e) {
      e.preventDefault();
      e.stopPropagation();
      let keyArr = Object.keys(this.shortcutList);
      // console.log(e);
      let code = e.keyCode.toString();
      // console.log(code);
      let idx = keyArr.indexOf(code);
      // console.log(idx);
      if (idx != -1) {
        this.shortcutList[code]();
      }
    }
  },
  mounted() {
    addEventListener('keyup', this.keyUpFun);
    addEventListener('keydown', this.keyDownFun);
  },
  activated() {
    if (this.isOpenKeysFlag) return;
    addEventListener('keyup', this.keyUpFun);
    addEventListener('keydown', this.keyDownFun);
  },
  beforeDestroy() {
    removeEventListener('keyup', this.keyUpFun);
    removeEventListener('keydown', this.keyDownFun);
  },
  deactivated() {
    console.log('988888877777');
    removeEventListener('keyup', this.keyUpFun);
    removeEventListener('keydown', this.keyDownFun);
  }
};
