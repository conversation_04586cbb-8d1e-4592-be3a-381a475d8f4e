import CookieFun from 'js-cookie';

export const Cookies = {
  get(key) {
    try {
      var val = JSON.parse(CookieFun.get(key));
      return val;
    } catch (e) {
      return CookieFun.get(key);
    }
  },
  set(key, value, expiresTime = 0) {
    let seconds = expiresTime;
    let expires = new Date(new Date() * 1 + seconds * 1000 * 60);
    console.log(expires);
    console.log(value);
    if (typeof value === 'string') {
      if (!expiresTime) {
        CookieFun.set(key, value);
      } else {
        CookieFun.set(key, value, { expires: expires });
      }
    } else {
      if (!expiresTime) {
        CookieFun.set(key, JSON.stringify(value));
      } else {
        CookieFun.set(key, JSON.stringify(value), { expires: expires });
      }
    }
  },
  remove(name) {
    if (name) {
      CookieFun.remove(key);
    } else {
      var keys = document.cookie.match(/[^ =;]+(?=\=)/g);
      if (keys) {
        for (var i = keys.length; i--; )
          document.cookie = keys[i] + '=0;expires=' + new Date(0).toUTCString();
      }
    }
  }
};
