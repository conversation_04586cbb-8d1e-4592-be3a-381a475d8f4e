//数据验证过滤
export const dataUtils = {
  //判断是否数字
  checkRate(nubmer) {
    var re = /^[0-9]*[1-9][0-9]*$/; //判断字符串是否为数字 /^[0-9]+.?[0-9]*/    //判断正整数/[1−9]+[0−9]∗]∗/
    if (!re.test(nubmer)) {
      return false;
    }
    return true;
  },

  //手机判断
  isTel(tel) {
    let reg = /^1[0-9]{10}$/;
    if (!reg.test(tel)) {
      return '填写完整的11位手机号码！';
    }
    return true;
  },

  //名称判断
  isPatName(tel) {
    let namereg = /^[\u4e00-\u9fa5]{2,4}$/;
    if (!namereg.test(tel)) {
      return '请输入两个到四个名字';
    }
    return true;
  },

  //截取带T的日期
  interceptDate(date) {
    return date.split('T')[0];
  },
  //截取空格的日期
  subBlankDate(date) {
    if (!date) return;
    return date.split(' ')[0];
  },
  // 日期格式化
  dateToString(date) {
    let _date = new Date(date);
    if (!Date.parse(_date)) return '';

    var year = _date.getFullYear();
    var month = (_date.getMonth() + 1).toString();
    var day = _date.getDate().toString();
    month = month.padStart(2, '0');
    day = day.padStart(2, '0');
    var dateTime = year + '-' + month + '-' + day;
    return dateTime;
  },

  /**
   * @author: justin
   * @description: 日期转开始时间字符串
   * @param {*} date
   * @return {*}
   **/
  dateToStrStart(date) {
    let dateTime = this.dateToString(date);
    if (!dateTime) return '';

    return dateTime + ' 00:00:00';
  },

  /**
   * @author: justin
   * @description: 日期转结束时间字符串
   * @param {*} date
   * @return {*}
   **/
  dateToStrEnd(date) {
    let dateTime = this.dateToString(date);
    if (!dateTime) return '';

    return dateTime + ' 23:59:59';
  },

  //获取当前时间戳
  getNowDateTiemNo() {
    var date = new Date();
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var strDate = date.getDate();
    var hour = date.getHours();
    var minute = date.getMinutes();
    var second = date.getSeconds();
    return (
      '' +
      year +
      this.checkTime(month) +
      this.checkTime(strDate) +
      this.checkTime(hour) +
      this.checkTime(minute) +
      this.checkTime(second)
    );
  },
  //获取当前年月日
  getDate() {
    const nowDate = new Date();
    const date = {
      year: nowDate.getFullYear(),
      month: nowDate.getMonth() + 1,
      date: nowDate.getDate()
    };
    const newmonth = date.month >= 10 ? date.month : '0' + date.month;
    const day = date.date >= 10 ? date.date : '0' + date.date;
    const updateTime = date.year + '-' + newmonth + '-' + day;
    return updateTime;
  },

  /**
   * @author: all
   * @description: 年月日时间补"00:00:00"
   * @param {*} date
   * @return {*}
   **/
  hourMinSeStart(date) {
    if (!date) return '';

    if (typeof date === 'object' && Date.parse(date))
      date = this.dateToString(date);

    return date + ' 00:00:00';
  },

  /**
   * @author: all
   * @description: 年月日时间补"23:59:59"
   * @param {*} date
   * @return {*}
   **/
  hourMinSeEnd(date) {
    if (!date) return '';

    if (typeof date === 'object' && Date.parse(date))
      date = this.dateToString(date);

    return date + ' 23:59:59';
  },
  // 计算年龄
  jsGetAge(strBirthday) {
    var returnAge;
    var strBirthdayArr = this.interceptDate(strBirthday).split('-');
    var birthYear = strBirthdayArr[0];
    var birthMonth = strBirthdayArr[1];
    var birthDay = strBirthdayArr[2];

    var d = new Date();
    var nowYear = d.getFullYear();
    var nowMonth = d.getMonth() + 1;
    var nowDay = d.getDate();

    if (nowYear == birthYear) {
      if (nowMonth - birthMonth == 0) {
        return (returnAge = '1');
      }
      returnAge = nowMonth - birthMonth; //同年 则为0岁
    } else {
      returnAge = nowYear - birthYear; //年之差
      // var ageDiff = nowYear - birthYear; //年之差
      // if (ageDiff > 0) {
      //     if (nowMonth == birthMonth) {
      //         var dayDiff = nowDay - birthDay;//日之差
      //         if (dayDiff < 0) {
      //             returnAge = ageDiff - 1;
      //         }
      //         else {
      //             returnAge = ageDiff;
      //         }
      //     }
      //     else {
      //         var monthDiff = nowMonth - birthMonth;//月之差
      //         if (monthDiff < 0) {
      //             returnAge = ageDiff - 1;
      //         }
      //         else {
      //             returnAge = ageDiff;
      //         }
      //     }
      // }
      // else {
      //     returnAge = -1;//返回-1 表示出生日期输入错误 晚于今天
      // }
    }
    console.log(returnAge);
    return returnAge; //返回周岁年龄
  },

  deepCopy(obj) {
    if (typeof obj == 'object') {
      return JSON.parse(JSON.stringify(obj));
    }
    return false;
  },

  /**
   * @author: all
   * @description: 小于10补0
   * @param {*} i
   * @return {*}
   **/
  checkTime(i) {
    return (i + '').padStart(2, '0');
  },

  // 处理价格保留两位小数
  handlePrice(price) {
    let priceStr = price.toString();
    let lengths = priceStr.split('.');

    if (lengths[1] == undefined) {
      return priceStr + '.00';
    }
    if (lengths[1].length < 2) {
      return priceStr + '0';
    }
    return priceStr;
  },

  //根据身份证获取出生年月、性别、年龄
  getByIdCard(idCard) {
    let sex = null;
    let birthday = null;
    let myDate = new Date();
    let monthNow = myDate.getMonth() + 1;
    let dayNow = myDate.getDate();
    let age = 0;
    let month = 0;
    let day = 0;
    if (idCard.length === 18) {
      age = myDate.getFullYear() - idCard.substring(6, 10);
      sex = idCard.substring(16, 17);
      month = idCard.substring(10, 12);
      day = idCard.substring(12, 14);
      birthday = idCard.substring(6, 10) + '-' + month + '-' + day;
    }
    if (idCard.length === 15) {
      age = myDate.getFullYear() - idCard.substring(6, 8) - 1901;
      sex = idCard.substring(13, 14);
      month = idCard.substring(8, 10);
      day = idCard.substring(10, 12);
      birthday = '19' + idCard.substring(6, 8) + '-' + month + '-' + day;
    }
    if (monthNow < month || (monthNow == month && dayNow < day)) {
      age--;
    }

    sex = sex % 2 === 1 ? 1 : 2; // 性别代码 1代表男，2代表女，暂时不涉及其他类型性别
    return { age, sex, birthday };
  },

  /**
   * 判断是否为一个整数
   * @param {object} obj 字符串
   * @return {boolean} true/false
   */
  isInteger(obj) {
    return Math.floor(obj) === obj;
  },

  /**
   * 将一个浮点数转成整数，返回整数和倍数。如 3.14 >> 314，倍数是 100
   * @param {object} floatNum 小数
   * @return {object} {times:100, num: 314}
   */
  toInteger(floatNum) {
    var ret = { times: 1, num: 0 };
    var isNegative = floatNum < 0;
    if (this.isInteger(floatNum)) {
      ret.num = floatNum;
      return ret;
    }
    var strfi = floatNum + '';
    var dotPos = strfi.indexOf('.');
    var len = strfi.substring(dotPos + 1).length;
    var times = Math.pow(10, len);
    var intNum = parseInt(Math.abs(floatNum) * times + 0.5, 10);
    ret.times = times;
    if (isNegative) {
      intNum = -intNum;
    }
    ret.num = intNum;
    return ret;
  },

  /**
   * 浮点型数字运算精度丢失修复
   * @param {object} num 小数
   * @param {number} s 小数位数
   * @return {object} s位小数
   */
  toFixed(num, s) {
    num = Number(num) || 0;
    var times = Math.pow(10, s);
    var des = (num * times + 0.5).toFixed(2);
    des = parseInt(des, 10) / times + '';

    var pos_decimal = des.indexOf('.');
    if (pos_decimal > 0) {
      while (des.length <= pos_decimal + s) {
        des += '0';
      }
    }

    return des;
  },

  /**
   * 浮点型数字运算精度丢失修复
   * 核心方法，实现加减乘除运算，确保不丢失精度
   * 思路：把小数放大为整数（乘），进行算术运算，再缩小为小数（除）
   * @param a {number} 运算数1
   * @param b {number} 运算数2
   * @param dig {number} 精度，保留的小数点数，比如 2, 即保留为两位小数
   * @param op {string} 运算类型，有加减乘除（add/subtract/multiply/divide）
   * @return {object} dig位小数
   */
  operation(a, b, dig, op) {
    var o1 = this.toInteger(a);
    var o2 = this.toInteger(b);
    var n1 = o1.num;
    var n2 = o2.num;
    var t1 = o1.times;
    var t2 = o2.times;
    var max = t1 > t2 ? t1 : t2;
    var result = null;
    switch (op) {
      case 'add':
        if (t1 === t2) {
          // 两个小数位数相同
          result = n1 + n2;
        } else if (t1 > t2) {
          // o1 小数位 大于 o2
          result = n1 + n2 * (t1 / t2);
        } else {
          // o1 小数位 小于 o2
          result = n1 * (t2 / t1) + n2;
        }
        return this.toFixed(result / max, dig);
      case 'subtract':
        if (t1 === t2) {
          result = n1 - n2;
        } else if (t1 > t2) {
          result = n1 - n2 * (t1 / t2);
        } else {
          result = n1 * (t2 / t1) - n2;
        }
        return this.toFixed(result / max, dig);
      case 'multiply':
        result = (n1 * n2) / (t1 * t2);
        return this.toFixed(result, dig);
      case 'divide':
        if (n2 === 0) result = 0;
        else result = (n1 / n2) * (t2 / t1);
        return this.toFixed(result, dig);
    }
  },

  /**
   * 加法
   * @param a {number} 运算数1
   * @param b {number} 运算数2
   * @return {object} s位小数
   */
  add(a, b, s) {
    return this.operation(a, b, s | 2, 'add');
  },

  /**
   * 减法
   * @param a {number} 运算数1
   * @param b {number} 运算数2
   * @return {object} s位小数
   */
  subtract(a, b, s) {
    return this.operation(a, b, s | 2, 'subtract');
  },

  /**
   * 乘法
   * @param a {number} 运算数1
   * @param b {number} 运算数2
   * @return {object} s位小数
   */
  multiply(a, b, s = 2) {
    return this.operation(a, b, s | 2, 'multiply');
  },

  /**
   * 除法
   * @param a {number} 运算数1
   * @param b {number} 运算数2
   * @return {object} s位小数
   */
  divide(a, b, s) {
    return this.operation(a, b, s | 2, 'divide');
  },

  /**
   * @author: justin
   * @description: 是否为体检号
   * @param {*} value 参数值
   * @return {boolean} true/false
   **/
  isRegNo(value) {
    if (!value || value.length !== 12) return false;

    return /\d{12}/.test(value);
  },

  /**
   * @author: justin
   * @description: 是否为空
   * @param {*} value 参数值
   * @return {*}
   **/
  isEmpty(value) {
    return !value || (value + '').trim().length === 0;
  },

  /**
   * @author: justin
   * @description: 获取性别尊称
   * @param {*} sex
   * @return {*}
   **/
  getSexNameRespect(sex) {
    if (!sex) return '';

    return Number(sex) === 1 ? '先生' : '女士';
  },

  /**
   * @author: justin
   * @description: base64转文件对象
   * @param {*} base64  base64字符串
   * @param {*} fileName 文件名
   * @return {*}
   **/
  base64ToFile(base64, fileName) {
    // 检查是否支持 atob
    if (typeof atob !== 'function') {
      throw new Error('atob is not supported in this environment');
    }

    let arr = base64.split(',');
    let mime = arr[0].match(/:(.*?);/)[1];
    let bstr = atob(arr[1]);
    let n = bstr.length;
    let u8arr = new Uint8Array(n);

    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], fileName, { type: mime });
  },

  /**
   * @author: justin
   * @description: 文件对象转base64
   * @param {*} file 文件对象
   * @return {*}
   **/
  fileToBase64(file) {
    return new Promise((resolve, reject) => {
      // 检查是否支持 FileReader
      if (typeof FileReader !== 'function') {
        reject(new Error('FileReader is not supported in this environment'));
        return;
      }

      // 创建一个新的 FileReader 对象
      const reader = new FileReader();
      // 读取 File 对象
      reader.readAsDataURL(file);
      // 加载完成后
      reader.onload = function () {
        // 将读取的数据转换为 base64 编码的字符串
        const base64String = reader.result.split(',')[1];
        // 解析为 Promise 对象，并返回 base64 编码的字符串
        resolve(base64String);
      };

      // 加载失败时
      reader.onerror = function () {
        reject(new Error('Failed to load file'));
      };
    });
  },
  // 获取光标的位置
  getCaretPosition(editableDiv) {
    let caretOffset = 0;
    if (window.getSelection) {
      const selection = window.getSelection();
      if (selection.rangeCount) {
        const range = selection.getRangeAt(0);
        const preCaretRange = range.cloneRange();
        preCaretRange.selectNodeContents(editableDiv);
        preCaretRange.setEnd(range.endContainer, range.endOffset);
        caretOffset = preCaretRange.toString().length;
      }
    } else if (document.selection && document.selection.createRange) {
      // IE < 9
      const range = document.selection.createRange();
      const workRange = range.duplicate();
      workRange.moveToElementText(editableDiv);
      workRange.setEndPoint('EndToEnd', range);
      caretOffset = workRange.text.length;
    }
    return caretOffset;
  },
  // 设置光标的位置
  setCursorPosition(element, index) {
    console.log(index);
    if (window.getSelection) {
      // 保存当前选区
      var selection = window.getSelection();
      var range = document.createRange();

      // 选中目标元素
      range.selectNodeContents(element);
      selection.removeAllRanges();
      selection.addRange(range);

      // 如果指定了索引，则移动光标位置
      if (index !== undefined) {
        var charIndex = 0,
          nodeStack = [element];

        // 遍历子节点直到索引位置
        while (nodeStack.length) {
          var node = nodeStack.pop();
          if (node.nodeType === 3) {
            var nextNode = node.nextSibling;
            var textNode = node;
            if (charIndex + textNode.length >= index) {
              // 将光标设置在节点的指定位置
              console.log(index - charIndex);
              range.setStart(textNode, index - charIndex);
              range.setEnd(textNode, index - charIndex);
              break;
            }
            charIndex += textNode.length;
          } else {
            var childNodes = node.childNodes;
            for (var i = childNodes.length - 1; i >= 0; i--) {
              nodeStack.push(childNodes[i]);
            }
          }
        }
      }

      // 移除当前选区
      selection.removeAllRanges();

      // 把新的range加入selection
      selection.addRange(range);
    } else if (document.selection && document.selection.createRange) {
      // 支持IE < 9
      var range = document.selection.createRange();
      range.moveToElementText(element);
      range.collapse(true);
      range.select();
    }
  }
};
