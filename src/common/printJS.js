export default function Print(id) {
  let thead = document
    .getElementsByClassName('print_table')[0]
    .getElementsByClassName('has-gutter')[0];
  console.log(document.getElementsByClassName('has-gutter'));

  let table = document
    .getElementsByClassName('print_table')[0]
    .getElementsByClassName('el-table__body')[0];
  let table_th = table.getElementsByClassName('has-gutter')[0];

  let tbody = table.getElementsByTagName('tbody')[0];
  console.log(table_th);
  if (!table_th) {
    table.insertBefore(thead, tbody);
  } else {
    console.log(thead.innerHTML);
    table_th.innerHTML = thead.innerHTML;
  }
  // table.insertBefore(thead,tbody);
  console.log(table);

  let Dom = document.getElementById(id).innerHTML;
  const style = getStyle();
  const container = getContainer(Dom);

  document.body.appendChild(style);
  document.body.appendChild(container);

  getLoadPromise(container).then(() => {
    window.print();
    document.body.removeChild(style);
    document.body.removeChild(container);
  });
}

// 设置打印样式
function getStyle() {
  const styleContent = `#print-container {
        display: none
    }
    @media print {
        body > :not(.print-container) {
            display: none
        }
        html,
        body {
            display: block !important;
        }
        .el-table{border:none;}
        table{border:1px solid #ddd;border-collapse:collapse;}
        .el-table__header,.el-table__body,.el-table__footer{width:100% !important;text-align:center;}
        table,table tr th, table tr td { word-wrap:break-word}
        .has-gutter th{
            width:auto !important;
        }
        .cell_blue {
            color: #1770df;
          }
        
          .cell_red {
            color: #d63031;
          }
          .el-table__header-wrapper{display:none !important;}
        .el-table th.gutter{display: none;}
        .el-table colgroup.gutter{display: none;}

        #print-container {
            display: block;
                width:100%;
            }
        @page {
          margin: 1mm;
        }
        
    }`;
  const style = document.createElement('style');
  style.innerHTML = styleContent;
  return style;
}

// 清空打印内容
function cleanPrint() {
  const div = document.getElementById('print-container');
  if (div) {
    document.querySelector('body').removeChild(div);
  }
}

// 新建DOM，将需要打印的内容填充到DOM
function getContainer(html) {
  cleanPrint();
  const container = document.createElement('div');
  container.setAttribute('id', 'print-container');

  container.innerHTML = html;

  return container;
}

// 图片完全加载后再调用打印方法
function getLoadPromise(dom) {
  let imgs = dom.querySelectorAll('img');
  imgs = [].slice.call(imgs);

  if (imgs.length === 0) {
    return Promise.resolve();
  }

  let finishedCount = 0;
  return new Promise((resolve) => {
    function check() {
      finishedCount++;
      if (finishedCount === imgs.length) {
        resolve();
      }
    }
    imgs.forEach((img) => {
      img.addEventListener('load', check);
      img.addEventListener('error', check);
    });
  });
}
