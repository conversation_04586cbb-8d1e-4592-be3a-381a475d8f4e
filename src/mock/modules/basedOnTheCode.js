const muenData = [
  {
    id: '1',
    title: '公共类代码',
    icon: require('@/assets/img/serviceDataIcon/classify_gray.png'),
    children: [
      {
        id: '1-1',
        title: '科室信息',
        icon: require('@/assets/img/serviceDataIcon/chart_gray.png'),
        children: []
      },
      {
        id: '1-2',
        title: '采集地点信息',
        icon: require('@/assets/img/serviceDataIcon/chart_gray.png'),
        children: []
      },
      {
        id: '1-3',
        title: '职业工种信息',
        icon: require('@/assets/img/serviceDataIcon/chart_gray.png'),
        children: []
      }
    ]
  },
  {
    id: '2',
    title: '体检类代码',
    icon: require('@/assets/img/serviceDataIcon/classify_gray.png'),
    children: [
      {
        id: '2-1',
        title: '项目分类信息',
        icon: require('@/assets/img/serviceDataIcon/chart_gray.png'),
        children: []
      },

      {
        id: '2-2',
        title: '条码分类信息',
        icon: require('@/assets/img/serviceDataIcon/chart_gray.png'),
        children: []
      },
      {
        id: '2-3',
        title: '参考范围信息',
        icon: require('@/assets/img/serviceDataIcon/chart_gray.png'),
        children: []
      },
      {
        id: '2-4',
        title: '体检项目信息',
        icon: require('@/assets/img/serviceDataIcon/file_open_gray.png'),
        children: [
          {
            id: '2-4-1',
            title: '一般检查',
            icon: require('@/assets/img/serviceDataIcon/chart_gray.png')
          },
          {
            id: '2-4-2',
            title: '内科',
            icon: require('@/assets/img/serviceDataIcon/chart_gray.png')
          },
          {
            id: '2-4-3',
            title: '外科',
            icon: require('@/assets/img/serviceDataIcon/chart_gray.png')
          }
        ]
      }
    ]
  },
  {
    id: '3',
    title: '收费类代码',
    icon: require('@/assets/img/serviceDataIcon/classify_gray.png'),
    children: [
      {
        id: '3-1',
        title: '收费项目信息',
        icon: require('@/assets/img/serviceDataIcon/chart_gray.png')
      },
      {
        id: '3-2',
        title: '基础分类信息',
        icon: require('@/assets/img/serviceDataIcon/chart_gray.png')
      },
      {
        id: '3-3',
        title: '项目分类信息',
        icon: require('@/assets/img/serviceDataIcon/chart_gray.png')
      }
    ]
  }
];

// 获取按钮列表
const getBaseCodeList = {
  url: '/serviceData/BaseCodeList',
  method: 'post',
  response: ({ body }) => {
    return {
      message: '',
      success: true,
      returnData: muenData
    };
  }
};

export default [getBaseCodeList];
