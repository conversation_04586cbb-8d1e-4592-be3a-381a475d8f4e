// modules/user.js
import { Rsa } from '@/common';
// 登录返回的菜单JSON
const users = [
  {
    userName: 'admin',
    password: '123456',
    other: [],
    menu: [
      {
        icon: 'el-icon-edit',
        title: '权限设置',
        path: '/authSet',
        children: [
          {
            title: '用户管理',
            name: 'userManage',
            path: '/authSet/userManage',
            del: true,
            edit: true,
            add: true,
            other: []
          },
          {
            title: '角色管理',
            name: 'roleManage',
            path: '/authSet/roleManage',
            del: true,
            edit: true,
            add: true,
            other: []
          },
          {
            title: '菜单管理',
            name: 'menuManage',
            path: '/authSet/menuManage',
            del: true,
            edit: true,
            add: true,
            other: []
          },
          {
            title: 'api地址维护',
            name: 'apiAddress',
            path: '/authSet/apiAddress',
            del: true,
            edit: true,
            add: true,
            other: []
          }
        ]
      },
      {
        icon: 'el-icon-edit',
        title: '登记模块',
        path: '/register',
        children: [
          {
            title: '个人登记',
            name: 'personRegister',
            path: '/register/personRegister',
            del: true,
            edit: true,
            add: true,
            other: []
          }
        ]
      }
    ]
  }
];
const userList = {
  url: '/user/login',
  method: 'post',
  response: ({ body }) => {
    return {
      message: '',
      success: true,
      returnData: {
        userName: '',
        menu: users[0].menu
      }
    };
  }
};

export default [userList];
