import { encrypt } from '@/common';
const tableList = [
  {
    id: '1',
    name: '测试人员1',
    code: '123456'
  },
  {
    id: '2',
    name: '测试人员2',
    code: '234567'
  },
  {
    id: '3',
    name: '测试人员3',
    code: '345678'
  },
  {
    id: '4',
    name: '测试人员4',
    code: '456789'
  },
  {
    id: '5',
    name: '测试人员5',
    code: '234567'
  },
  {
    id: '6',
    name: '测试人员6',
    code: '345678'
  },
  {
    id: '7',
    name: '测试人员7',
    code: '456789'
  },
  {
    id: '8',
    name: '测试人员8',
    code: '234567'
  },
  {
    id: '9',
    name: '测试人员9',
    code: '345678'
  },
  {
    id: '10',
    name: '测试人员10',
    code: '456789'
  },
  {
    id: '11',
    name: '测试人员11',
    code: '234567'
  },
  {
    id: '12',
    name: '测试人员12',
    code: '345678'
  },
  {
    id: '13',
    name: '测试人员13',
    code: '456789'
  },
  {
    id: '14',
    name: '测试人员14',
    code: '234567'
  },
  {
    id: '15',
    name: '测试人员n',
    code: '345678'
  }
];
const getTableList = {
  url: '/userMsg/getAllTableList',
  method: 'post',
  response: ({ body }) => {
    return {
      returnMsg: '查询成功',
      success: true,
      returnData: tableList
    };
  }
};

export default [getTableList];
