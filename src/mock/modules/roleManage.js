let publicBtn = [
  {
    label: '添加',
    code: 'add'
  },
  {
    label: '删除',
    code: 'del'
  },
  {
    label: '修改',
    code: 'edit'
  }
];
// 登录返回的菜单JSON
const users = [
  {
    icon: 'el-icon-edit',
    id: '1',
    title: '权限设置',
    name: '',
    path: '/authSet',
    children: [
      {
        id: '1-1',
        icon: '',
        title: '用户管理',
        name: 'userManage',
        path: '/authSet/userManage',
        other: [
          ...publicBtn,
          {
            label: '审核',
            code: 'audit'
          },
          {
            label: '打印',
            code: 'print'
          }
        ]
      },
      {
        id: '1-2',
        title: '角色管理',
        icon: '',
        name: 'roleManage',
        path: '/authSet/roleManage',
        other: [
          ...publicBtn,
          {
            label: '审核',
            code: 'audit'
          }
        ]
      },
      {
        id: '1-3',
        icon: '',
        title: '菜单管理',
        name: 'menuManage',
        path: '/authSet/menuManage',
        other: [...publicBtn]
      }
    ]
  }
];
const getRoleAuth = {
  url: '/role/getRoleAuth',
  method: 'post',
  response: ({ body }) => {
    return {
      message: '',
      success: true,
      returnData: users
    };
  }
};

export default [getRoleAuth];
