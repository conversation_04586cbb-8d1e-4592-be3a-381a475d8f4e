import { encrypt } from '@/common';
const apiList = [
  {
    id: '1',
    apiName: '测试api名称1',
    apiCode: '123456'
  },
  {
    id: '2',
    apiName: '测试api名称2',
    apiCode: '234567'
  },
  {
    id: '3',
    apiName: '测试api名称3',
    apiCode: '345678'
  },
  {
    id: '4',
    apiName: '测试api名称4',
    apiCode: '456789'
  },
  {
    id: '5',
    apiName: '测试api名称5',
    apiCode: '234567'
  },
  {
    id: '6',
    apiName: '测试api名称6',
    apiCode: '345678'
  },
  {
    id: '7',
    apiName: '测试api名称7',
    apiCode: '456789'
  },
  {
    id: '8',
    apiName: '测试api名称8',
    apiCode: '234567'
  },
  {
    id: '9',
    apiName: '测试api名称9',
    apiCode: '345678'
  },
  {
    id: '10',
    apiName: '测试api名称10',
    apiCode: '456789'
  },
  {
    id: '11',
    apiName: '测试api名称11',
    apiCode: '234567'
  },
  {
    id: '12',
    apiName: '测试api名称12',
    apiCode: '345678'
  },
  {
    id: '13',
    apiName: '测试api名称13',
    apiCode: '456789'
  },
  {
    id: '14',
    apiName: '测试api名称14',
    apiCode: '234567'
  },
  {
    id: '15',
    apiName: '测试api名称n',
    apiCode: '345678'
  }
];
const getApiList = {
  url: '/apiList/getAllApiList',
  method: 'post',
  response: ({ body }) => {
    return {
      returnMsg: '查询成功',
      success: true,
      returnData: apiList
    };
  }
};

export default [getApiList];
