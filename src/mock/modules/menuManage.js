// modules/user.js
import { encrypt } from '@/common';

let publicBtn = [
  {
    api: ['/audit/audit', '/add/add'],
    label: '添加',
    code: 'add'
  },
  {
    api: [],
    label: '删除',
    code: 'del'
  },
  {
    api: [],
    label: '修改',
    code: 'edit'
  }
];
// api列表
const apiList = [
  {
    label: '审核',
    api: '/audit/audit'
  },
  {
    label: '添加',
    api: '/add/add'
  },
  {
    label: '删除',
    api: '/del/del'
  }
];

// 获取apiList接口
const getApiList = {
  url: '/menu/getApiList',
  method: 'post',
  response: ({ body }) => {
    return {
      message: '',
      success: true,
      returnData: apiList
    };
  }
};

// 按钮列表
const btnList = [
  {
    label: '添加',
    code: 'add',
    api: []
  },
  {
    label: '修改',
    code: 'edit',
    api: []
  },
  {
    label: '删除',
    code: 'del',
    api: []
  },
  {
    label: '审核',
    code: 'audit',
    api: []
  },
  {
    label: '打印',
    code: 'print',
    api: []
  },
  {
    label: '签名',
    code: 'sign',
    api: []
  }
];
// 获取按钮列表
const getBtnList = {
  url: '/menu/getBtnList',
  method: 'post',
  response: ({ body }) => {
    return {
      message: '',
      success: true,
      returnData: btnList
    };
  }
};

// 登录返回的菜单JSON
const allMenuList = [
  {
    icon: 'el-icon-edit',
    id: '1',
    title: '权限设置',
    name: '',
    path: '/authSet',
    children: [
      {
        id: '1-1',
        icon: '',
        title: '用户管理',
        name: 'userManage',
        path: '/authSet/userManage',
        other: [
          ...publicBtn,
          {
            api: [],
            label: '审核',
            code: 'audit'
          },
          {
            api: [],
            label: '打印',
            code: 'print'
          }
        ],
        checkOther: []
      },
      {
        id: '1-2',
        title: '角色管理',
        icon: '',
        name: 'roleManage',
        path: '/authSet/roleManage',
        other: [
          ...publicBtn,
          {
            api: [],
            label: '审核',
            code: 'audit'
          }
        ],
        checkOther: []
      },
      {
        id: '1-3',
        icon: '',
        title: '菜单管理',
        name: 'menuManage',
        path: '/authSet/menuManage',
        other: [...publicBtn],
        checkOther: []
      }
    ]
  },
  {
    id: '2',
    icon: 'el-icon-edit',
    title: '登记模块',
    name: '',
    path: '/register',
    children: [
      {
        id: '2-1',
        icon: '',
        title: '个人登记',
        name: 'personRegister',
        path: '/register/personRegister',
        other: [...publicBtn],
        checkOther: []
      }
    ]
  }
];
const getAllMenuList = {
  url: '/menu/getAllMenuList',
  method: 'post',
  response: ({ body }) => {
    return {
      message: '',
      success: true,
      returnData: allMenuList
    };
  }
};

export default [getAllMenuList, getApiList, getBtnList];
