import Vue from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import { ajax, apiUrls, storage } from '@/common';

import 'xe-utils';
import VXETable from 'vxe-table';
import 'vxe-table/lib/style.css';
import elTableInfiniteScroll from 'el-table-infinite-scroll';
import directives from './directives';
import Print from 'vue-print-nb-jeecg';
import 'devextreme/dist/css/dx.light.css';
import '@devexpress/analytics-core/dist/css/dx-analytics.common.css';
import '@devexpress/analytics-core/dist/css/dx-analytics.light.css';
import 'devexpress-reporting/dist/css/dx-webdocumentviewer.css';
import '@/utils/directives.js';
import VueClipBoard from 'vue-clipboard2';
import './common/loadmore.js';
import 'github-markdown-css/github-markdown.css';
import 'highlight.js/styles/default.css';

// 扩展Vue类型，添加全局属性
declare module 'vue/types/vue' {
  interface Vue {
    $ajax: typeof ajax;
    $apiUrls: typeof apiUrls;
    $config: Record<string, any>;
    $bus: Vue;
  }
}

// 解决element-ui弹框遮挡问题
ElementUI.Dialog.props.modalAppendToBody.default = false;
ElementUI.Drawer.props.modalAppendToBody.default = false;
ElementUI.DatePicker.props.unlinkPanels = { type: Boolean, default: true };
Vue.use(VueClipBoard);
Vue.use(directives);
Vue.use(Print);

// 注册 vxe-table 及其组件
Vue.use(VXETable);

Vue.use(elTableInfiniteScroll);

const isProduction = process.env.NODE_ENV === 'production';
if (process.env.VUE_APP_MOCK === 'true' && !isProduction) {
  console.log('mock');
  require('./mock');
}
Vue.use(ElementUI);

Vue.config.productionTip = false;
Vue.prototype.$ajax = ajax;
Vue.prototype.$apiUrls = apiUrls;
Vue.prototype.$config = {};

// 页面刷新添加动态路由
const updata_page = storage.session.get('userInfo');
const menuList = storage.session.get('menuList') || [];
if (!!updata_page) {
  store.dispatch('A_getNavList', menuList);
}

async function init(): Promise<void> {
  let configUrl = isProduction
    ? './config/config.js'
    : './config/config.dev.js';
  configUrl += '?t=' + new Date().getTime();
  await ajax.get(configUrl).then((r) => {
    let configs = new Function(r.data)();
    console.log(configs);
    Vue.prototype.$config = configs;
    document.title = configs.documentName;
    store.commit('M_config', configs);
    ajax.setbaseURL(configs.baseUrl);
  });

  new Vue({
    router,
    store,
    render: (h) => h(App),
    beforeCreate() {
      Vue.prototype.$bus = this; //安装全局事件总线
    },
    mounted() {
      var newLink = document.createElement('link');
      newLink.rel = 'icon';
      newLink.type = 'image/x-icon';
      newLink.href = './document-logo.png';

      // 获取并移除原有的 favicon link（如果有多个，可能需要更精确的选择器）
      var oldLinks = document.querySelectorAll('link[rel="icon"]');
      for (var i = 0; i < oldLinks.length; i++) {
        oldLinks[i].parentNode?.removeChild(oldLinks[i]);
      }

      // 将新创建的 link 插入到 head 中
      document.head.appendChild(newLink);
    }
  }).$mount('#app');
}
setTimeout(() => {
  init();
});

// // 检查版本更新
// setInterval(() => {
//   upgradeVersion.checkVersion();
// }, 30 * 1000);
