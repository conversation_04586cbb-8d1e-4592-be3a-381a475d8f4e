/**
 * @FilePath: \KrPeis\src\router\index.js
 * @Description:  路由守卫配置
 * @Author:
 * @Date: 2024-03-19 17:23:58
 * @Version: 0.0.1
 * @LastEditors: justin
 * @LastEditTime: 2024-06-04 08:50:54
 
 */
import Vue from 'vue';
import Router from 'vue-router';
import { storage } from '../common';
import upgradeVersion from '@/apis/upgradeVersion';
const whiteList = ['login', 'auth'];

Vue.use(Router);
// 固定路由
const constantRoutes = [
  {
    path: '/auth',
    name: 'auth',
    component: () => import('@/views/auth')
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/login')
  },
  {
    path: '/test',
    name: 'test',
    component: () => import('@/views/test')
  },
  {
    path: '/testTinymce',
    name: 'testTinymce',
    component: () => import('@/views/testTinymce')
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/404')
  },
  {
    path: '/input',
    name: 'input',
    component: () => import('@/components/input')
  }
];

//这个是解决多次点击跳转同一个页面报错的BUG；
const originalPush = Router.prototype.push;
Router.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => err);
};

// 创建路由
const createRouter = () =>
  new Router({
    mode: 'hash', // require service support
    routes: constantRoutes
  });

const router = createRouter();

// 重置路由，退出登录的时候需要重置路由
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

// 路由守卫
router.beforeEach((to, from, next) => {
  const u = storage.session.get('userInfo');
  if (!u && !whiteList.includes(to.name)) {
    if (to.name) storage.session.set('redirect', to.path);
    next({
      path: '/login'
    });
  } else if (!to.name) {
    next({
      path: '/404'
    });
  } else {
    next();
  }
});

// 路由错误处理：页面内容未找到等
// router.onError((error) => {
//   const jsPattern = /Loading chunk (\S)+ failed/g;
//   const cssPattern = /Loading CSS chunk (\S)+ failed/g;
//   const isChunkLoadFailed =
//     error.message.match(jsPattern) || error.message.match(cssPattern);
//   if (isChunkLoadFailed) {
//     upgradeVersion.closeNotify();
//     upgradeVersion.upgradeNotice('跳转失败，页面内容已更新，请刷新页面！');
//   }
// });

export default router;
