/**
 
 * @Description: 创建发布版本号脚本
 
 * @Date: 2023-12-20 15:04:16
 * @Version: 0.0.1
 * @LastEditors: justin
 * @LastEditTime: 2024-06-03 18:09:01
 
 */

const fs = require('fs');

//返回package的json数据
function getPackageJson() {
  let data = fs.readFileSync('./package.json'); //fs读取文件
  return JSON.parse(data); //转换为json对象
}

let packageJson = getPackageJson(); //获取package的json
const appVersion = packageJson.version;

// 更新版本的函数
const incrementVersion = (version) => {
  // 主版本号.次版本号.修订号
  // 主版本号：当进行不兼容的 API 变动时增加。
  // 次版本号：当进行向下兼容的功能性新增时增加。
  // 修订号：当进行向下兼容的问题修正时增加
  const versionArray = version.split('.');
  const [major, minor, patch] = versionArray;

  // 增量更新次版本号
  const newPatch = parseInt(patch) + 1;
  return `${major}.${minor}.${newPatch}`;
};

// 计算新版本号
const newVersion = incrementVersion(appVersion);
console.log('newVersion', newVersion, 'oldVersion', appVersion);

// 基于新版本号更新 package.json
packageJson.version = newVersion;
fs.writeFileSync(
  './package.json',
  JSON.stringify(packageJson, null, 2),
  'utf-8'
);

// 基于新版本号创建 version.json
const metaJson = { version: newVersion, oldVersion: appVersion };
fs.writeFileSync(
  './public/version.json',
  JSON.stringify(metaJson, null, 2),
  'utf8'
);

console.log(
  '\x1b[32m%s\x1b[0m',
  'Version has been updated in package.json and version.json：' + newVersion
);
