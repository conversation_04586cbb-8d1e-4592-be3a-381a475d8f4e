<template>
  <div class="TCMManage_page">
    <div class="left_wrap wrap_style">
      <header>
        <h3>模板维护</h3>
      </header>
      <div class="left_content"></div>
    </div>
    <div class="right_wrap wrap_style">
      <header>
        <el-button class="blue_btn" size="small">编辑</el-button>
        <el-button class="blue_btn" size="small">保存</el-button>
      </header>
      <div class="right_content"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TCMManage',
  data() {
    return {};
  }
};
</script>
<style lang="less" scoped>
.TCMManage_page {
  display: flex;

  .wrap_style {
    background-color: #fff;
    border-radius: 5px;
    overflow: auto;
    position: relative;
    padding-top: 48px;
    header {
      height: 48px;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      display: flex;
      align-items: center;
      padding: 0 15px;
      h3 {
        font-size: 16px;
        color: #2d3436;
        font-weight: 600;
      }
    }
  }

  .left_wrap {
    width: 360px;
    margin-right: 20px;
  }

  .right_wrap {
    flex: 1;
    flex-shrink: 0;
    padding-top: 58px;
    header {
      top: 10px;
      justify-content: flex-end;
    }
  }
}
</style>
