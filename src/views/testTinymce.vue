<template>
  <div>
    <!-- <tinymce style="width: 100%;" id="myedit" ref="editor" :height="300"
            input="input" /> -->

    <component
      :is="tinymce"
      id="myedit"
      ref="editor"
      :height="300"
      @input="input"
      @changeView="changeView"
    ></component>
  </div>
</template>

<script>
import Tinymce from '@/components/tinymce';
export default {
  name: 'testTinymce',
  components: {
    Tinymce
  },
  data() {
    return {
      tinymce: Tinymce
    };
  },
  methods: {
    input() {
      console.log(this.$refs.editor.myValue); //获取富文本中的值
    },
    changeView(value) {
      console.log(value);
    }
  }
};
</script>
<style lang="less" scoped></style>
