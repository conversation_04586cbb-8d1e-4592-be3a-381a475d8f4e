<template>
  <div>
    <test-group message="撒大大方式方法" :obj="obj"></test-group>
  </div>
</template>

<script>
let str = `<div><p><img style="float: right; max-width: 40%;" src="static/img/qqgroup.jpg" /></p>
<p style="text-align: center;">强大的所见即所得的富文本编辑器。同类程序有：</p>
<table style="border-collapse: collapse; width: 100%;" border="1">
<tbody>
<tr style="height: 21px;">
<td style="width: 5.51876%; height: 21px;">姓名</td>
<td style="width: 20.7947%; height: 21px;">{{name}}</td>
<td style="width: 23.6865%; height: 21px;">信息</td>
<td style="width: 10%;" colspan="2">{{message}}</td>
</tr>
<tr style="height: 21px;">
<td style="width: 5.51876%; height: 21px;">年龄</td>
<td style="width: 20.7947%; height: 21px;">{{age}}</td>
<td style="width: 23.6865%; height: 21px;">性别</td>
<td style="width: 5%;">{{sex}}</td>
<td style="width: 5%;">&nbsp;</td>
</tr>
</tbody>
</table></div>`;
export default {
  name: 'test',
  components: {
    'test-group': {
      props: ['obj'],
      data() {
        return {
          ...this.obj
        };
      },
      template: str
    }
  },
  data() {
    return {
      obj: {
        age: '18',
        name: '测试',
        sex: '女'
      }
    };
  }
};
</script>
<style lang="less" scoped></style>
