<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { storage } from '@/common';
import store from '@/store';
import { apiUrls as $apiUrls } from '@/common/apiUrls';
import { ajax as $ajax } from '@/common';
import { Message } from 'element-ui';
import router from '@/router';

// 定义接口
interface RuleForm {
  operatorCode: string;
  password: string;
  hospCode: string;
}

interface HospitalOption {
  hospCode: string;
  hospName: string;
  hospShortName: string;
  [key: string]: any;
}

interface UserInfo {
  operatorCode: string;
  password?: string;
  hospCode?: string;
  hospName?: string;
  hospShortName?: string;
  [key: string]: any;
}

// 获取当前Vue实例 - 在 Vue 2.7 中不再需要访问全局属性

// 表单引用
const ruleFormRef = ref<any>(null);

// 响应式数据
const loading = ref(false);
const options = ref<HospitalOption[]>([]);

// 表单数据
const ruleForm = reactive<RuleForm>({
  operatorCode: '',
  password: '',
  hospCode: ''
});

// 从store获取配置
const G_config = computed(() => store.getters.G_config);

// 表单验证规则
const rules = reactive({
  operatorCode: [
    {
      validator: (_rule: any, value: string, callback: Function) => {
        if (value === '') {
          return callback(new Error('请输入账号'));
        }
        callback();
      },
      trigger: 'blur'
    }
  ],
  password: [
    {
      validator: (_rule: any, value: string, callback: Function) => {
        if (value === '') {
          return callback(new Error('请输入密码'));
        }
        callback();
      },
      trigger: 'blur'
    }
  ],
  hospCode: [
    {
      validator: (_rule: any, value: string, callback: Function) => {
        if (value === '' && G_config.value?.openBranch) {
          return callback(new Error('请选择院区'));
        }
        callback();
      },
      trigger: 'change'
    }
  ]
});

// 获取院区列表
const getCodeHospital = async () => {
  try {
    const response = await $ajax.post($apiUrls.GetHospitalInfo);
    const { success, returnData } = response.data;
    if (!success) return;

    options.value = returnData;

    const user = storage.local.get('user') as UserInfo | null;
    if (user?.hospCode) {
      ruleForm.hospCode = user.hospCode;
    }
  } catch (error) {
    console.error('获取院区列表失败:', error);
  }
};

// 登录方法
const login = async () => {
  if (!ruleFormRef.value) return;

  try {
    const valid = await new Promise<boolean>((resolve) => {
      ruleFormRef.value.validate((isValid: boolean) => {
        resolve(isValid);
      });
    });

    if (!valid) return;
    // 获取全局配置
    const $config = G_config.value || {};
    // 构建登录数据
    const data = {
      ...ruleForm,
      hospCode: ruleForm.hospCode || $config.branchCode
    };

    loading.value = true;

    // 发送登录请求
    const response = await $ajax.post($apiUrls.login, data);
    const { returnData } = response.data;
    const { codeOper } = returnData;

    // 更新用户信息和显示头部
    store.commit('M_userInfo', returnData);
    store.commit('M_headShow', true);

    // 获取菜单列表
    const menuResponse = await store.dispatch(
      'A_getMenuList',
      codeOper.operatorCode
    );
    const { userMenuTree = [] } = menuResponse.data?.returnData || {};
    const menu = userMenuTree || [];

    // 保存菜单列表
    storage.session.set('menuList', menu);

    // 查找当前选择的医院信息
    let hospName: string | undefined;
    let hospShortName: string | undefined;
    options.value.some((item) => {
      if (item.hospCode === ruleForm.hospCode) {
        hospName = item.hospName;
        hospShortName = item.hospShortName;
        return true;
      }
      return false;
    });

    // 保存用户信息到本地和会话存储
    const userInfo: UserInfo = {
      ...ruleForm,
      password: '',
      hospName,
      hospShortName
    };

    storage.local.set('user', userInfo);
    storage.session.set('user', userInfo);

    // 获取导航列表并跳转
    await store.dispatch('A_getNavList', menu);

    // 处理重定向
    const redirect = storage.session.get('redirect') as string | null;
    // 直接使用导入的 router
    if (redirect) {
      router.push(redirect);
    } else {
      router.replace('/');
    }

    // 显示成功消息
    // 使用 Element UI 的 Message 组件
    Message({
      message: '登录成功',
      type: 'success',
      duration: $config.messageTime,
      showClose: true
    });
  } catch (error) {
    console.error('登录失败:', error);
  } finally {
    loading.value = false;
  }
};

// 生命周期钩子
onMounted(() => {
  // 获取院区列表
  setTimeout(() => {
    getCodeHospital();
  }, 200);

  // 从本地存储获取用户信息
  const user = storage.local.get('user') as UserInfo | null;
  if (user) {
    ruleForm.operatorCode = user.operatorCode || '';
    ruleForm.password = user.password || '';
  }

  // 设置会话存储
  storage.session.set('isGateway', false);
});
</script>
<template>
  <div class="loginPage">
    <div class="form_wrap">
      <div class="left-side"></div>
      <div class="right-side">
        <div class="form-box">
          <div class="form-title">
            <h1>体检系统</h1>
          </div>
          <el-form
            :model="ruleForm"
            ref="ruleFormRef"
            @keyup.enter.native="login"
            :rules="rules"
            class="form"
          >
            <el-form-item prop="operatorCode" class="form-input">
              <el-input
                class="input"
                v-model.trim="ruleForm.operatorCode"
                placeholder="账号"
                autocomplete="off"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item prop="password" class="form-input">
              <el-input
                class="input"
                placeholder="密码"
                type="password"
                v-model.trim="ruleForm.password"
                autocomplete="off"
                show-password
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              prop="hospCode"
              class="form-input"
              v-if="G_config.openBranch"
            >
              <el-select
                v-model="ruleForm.hospCode"
                placeholder="院区"
                class="input"
                clearable
              >
                <el-option
                  v-for="item in options"
                  :key="item.hospCode"
                  :label="item.hospName"
                  :value="item.hospCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item class="form-button">
              <el-button
                type="primary"
                style="width: 100%"
                @click="login"
                :loading="loading"
                >登 录</el-button
              >
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.loginPage {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  width: 100%;
  background-color: #f5f7fa;
  background-size: cover;
  color: #2d3436;

  .form_wrap {
    background: #fff;
    width: 1100px;
    height: 600px;
    display: flex;
    justify-content: space-between;
    align-items: stretch;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    position: relative;

    .left-side {
      width: 55%;
      background: url('../assets/img/login_img.png') no-repeat center;
      background-size: cover;
    }

    .right-side {
      width: 45%;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #fff;
    }
  }

  .form-box {
    background: #fff;
    padding: 40px;
    width: 80%;
    max-width: 380px;
    position: relative;
    z-index: 1;

    .form-title {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 50px;
      .logo {
        width: 42px;
        display: block;
        margin-right: 10px;
      }
      h1 {
        font-size: 28px;
        text-align: center;
        color: #333;
        font-weight: 500;
        margin: 0;
      }
    }
    .form {
      width: 368px;
      margin: 0 auto;
    }
    .input {
      width: 100%;
    }
    .input,
    /deep/.el-input__clear,
    /deep/.el-select__caret {
      font-size: 16px;
    }
    /deep/.el-input__inner {
      font-size: 14px;
      height: 40px;
      line-height: 40px;
      border-radius: 4px;
      border: 1px solid #dcdfe6;
      background: #fff;
      color: #606266;
      padding: 0 15px;
      transition: all 0.2s ease;

      &:focus {
        border-color: #409eff;
        outline: 0;
      }

      &::placeholder {
        color: #c0c4cc;
      }
    }

    /deep/.el-input__prefix,
    /deep/.el-input__suffix {
      color: #c0c4cc;
    }
    .form-input {
      margin-bottom: 25px;
      &:last-child {
        margin-bottom: 0;
      }
    }
    /deep/.el-form-item__error {
      font-size: 12px;
      color: #f56c6c;
      padding-top: 4px;
      line-height: 1;
    }
    .form-button {
      margin-top: 30px;
      margin-bottom: 20px;
      .el-button--primary {
        background-color: #409eff;
        border-color: #409eff;
        font-size: 14px;
        height: 40px;
        border-radius: 4px;
        transition: all 0.3s;

        &:hover {
          background-color: #66b1ff;
          border-color: #66b1ff;
        }

        &:active {
          background-color: #3a8ee6;
          border-color: #3a8ee6;
        }
      }
    }
    .other-login {
      span {
        color: #9ca0a4;
        font-size: 18px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        &::after {
          content: '';
          display: block;
          width: 110px;
          height: 1px;
          background: #c0c2c2;
        }
        &::before {
          content: '';
          display: block;
          width: 110px;
          height: 1px;
          background: #c0c2c2;
        }
      }
    }
    .other-img {
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: 48px;
        margin-top: 18px;
        cursor: pointer;
      }
      .img-wx {
        margin-right: 68px;
      }
    }
  }
}
@media screen and (max-width: 1360px) {
  .loginPage {
    .form_wrap {
      width: 900px;
      height: 550px;
    }
  }
}
@media screen and (max-width: 1280px) {
  .loginPage {
    .form_wrap {
      width: 850px;
      height: 520px;
    }
  }
}
@media screen and (max-width: 768px) {
  .loginPage {
    .form_wrap {
      width: 90%;
      height: auto;
      flex-direction: column;

      .left-side {
        width: 100%;
        height: 200px;
      }

      .right-side {
        width: 100%;
        padding: 30px 0;
      }
    }
  }
}
</style>
