<template>
  <!-- 登录弹窗  -->
  <div>
    <el-dialog
      title="登录"
      :visible.sync="G_loginDialogShow"
      width="30%"
      @open="openDialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="cancel"
      :show-close="false"
    >
      <el-form
        :model="loginForm"
        label-width="50px"
        :rules="loginRules"
        ref="loginForm"
      >
        <el-form-item label="账号" prop="operatorCode">
          <div @dblclick="readonlyOperatorCode = false">
            <el-input
              v-model="loginForm.operatorCode"
              clearable
              placeholder="请输入帐号"
              :readonly="readonlyOperatorCode"
              @keyup.enter.native="confirm"
              @blur="readonlyOperatorCode = true"
              :class="readonlyOperatorCode ? 'readonly-input' : ''"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="loginForm.password"
            show-password
            clearable
            placeholder="请输入密码"
            @keyup.enter.native="confirm"
            autofocus
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="quit" :loading="loading">退 出</el-button>
        <el-button type="primary" @click="confirm" :loading="loading"
          >登 录</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex';
export default {
  name: 'LoginDialog',
  data() {
    return {
      loginForm: {
        operatorCode: '',
        password: '',
        hospCode: ''
      },
      loginRules: {
        operatorCode: [
          { required: true, message: '请输入帐号', trigger: 'blur' }
        ],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
      },
      loading: false,
      readonlyOperatorCode: true
    };
  },
  computed: {
    ...mapGetters(['G_loginDialogShow', 'G_userInfo'])
  },
  methods: {
    ...mapMutations(['M_loginDialogShow', 'M_userInfo', 'M_logout']),
    /**
     * @author: justin
     * @description:  打开登录弹窗回调函数
     * @return {*}
     */
    openDialog() {
      this.loginForm.password = '';
      const userInfo = this.G_userInfo;
      if (!userInfo || !userInfo.codeOper) {
        this.cancel();
        this.M_logout();
        return;
      }

      if (userInfo && userInfo.codeOper)
        this.loginForm.operatorCode = userInfo.codeOper.operatorCode;

      if (userInfo && userInfo.hospInfo)
        this.loginForm.hospCode = userInfo.hospInfo.hospCode;
    },

    /**
     * @author: justin
     * @description: 确认登录
     * @return {*}
     */
    confirm() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          let data = {
            ...this.loginForm,
            hospCode: this.loginForm.hospCode
              ? this.loginForm.hospCode
              : this.$config.branchCode
          };
          const loginFunc = () => {
            this.loading = true;
            this.$ajax
              .post(this.$apiUrls.login, data)
              .then((res) => {
                const { success, returnData } = res.data;
                if (!success || !returnData) {
                  return;
                }

                this.M_userInfo(returnData);
                this.cancel();
                this.$message.success('登录成功！');
              })
              .finally((_) => {
                this.loading = false;
              });
          };
          const userInfo = this.G_userInfo;
          if (
            !userInfo ||
            userInfo.codeOper.operatorCode !== this.loginForm.operatorCode
          ) {
            this.$confirm(
              '暂不支持切换账号操作，是否前往登录主页，进行账号切换？',
              '账号切换提示',
              {
                confirmButtonText: '前 往',
                cancelButtonText: '不 了',
                type: 'warning',
                showClose: false
              }
            ).then(() => {
              this.cancel();
              this.M_logout();
            });
          } else {
            loginFunc();
          }
        }
      });
    },
    /**
     * @author: justin
     * @description: 取消登录
     * @return {*}
     */
    cancel() {
      this.M_loginDialogShow(false);
    },

    /**
     * @author: justin
     * @description: 退出登录
     * @return {*}
     */
    quit() {
      this.$confirm('退出将清除当前登录信息，确认退出吗？', '退出提示', {
        confirmButtonText: '确 定',
        cancelButtonText: '不 了',
        type: 'warning',
        showClose: false
      }).then(() => {
        this.cancel();
        this.M_logout();
      });
    }
  }
};
</script>

<style lang="less" scoped>
.readonly-input {
  /deep/.el-input__inner {
    background: #f5f7fa;
  }
}
</style>
