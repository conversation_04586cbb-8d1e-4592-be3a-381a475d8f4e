<template>
  <PublicTable
    :theads="urgentTheads"
    :viewTableList="G_urgentList"
    :columnWidth="urgentColumnWidth"
  >
    <template #sex="{ scope }">
      {{ G_EnumList['Sex'][scope.row.sex] }}
    </template>
    <template #creator="{ scope }">
      {{ G_EnumList['SysOperator'][scope.row.creator] || scope.row.creator }}
    </template>
    <template #type="{ scope }">
      {{ urgentGrade[scope.row.type] }}
    </template>
    <template #columnRight>
      <el-table-column label="结果" width="90">
        <template slot-scope="scope">
          <el-popover
            placement="right"
            width="600"
            @show="popoverShow(scope.row)"
            trigger="click"
          >
            <div>
              <div style="max-height: 500px">
                <PublicTable
                  :theads="infoTheads"
                  :viewTableList="infoList"
                  :columnWidth="infoColumnWidth"
                >
                  <template #columnRight>
                    <el-table-column label="参考范围" width="100">
                      <template slot-scope="scope">
                        {{ scope.row.lowerLimit + '~' + scope.row.upperLimit }}
                      </template>
                    </el-table-column>
                  </template>
                </PublicTable>
              </div>
            </div>
            <el-button
              type="text"
              style="padding: 2px 5px"
              slot="reference"
              class="reply_btn"
              >查看结果</el-button
            >
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="65">
        <template slot-scope="scope">
          <el-popover
            placement="right"
            width="600"
            @show="popoverShow(scope.row)"
            v-model="scope.row.popoverVisible"
            trigger="click"
          >
            <div>
              <el-input
                type="textarea"
                :autosize="{ minRows: 4, maxRows: 4 }"
                placeholder="请输入内容"
                v-model.trim="replyVal"
              >
              </el-input>
              <div style="text-align: right; padding: 10px 0">
                <el-button
                  type="primary"
                  size="small"
                  @click="replyHandle(scope.row)"
                  >回复</el-button
                >
              </div>
              <div style="max-height: 500px">
                <PublicTable
                  :theads="infoTheads"
                  :viewTableList="infoList"
                  :columnWidth="infoColumnWidth"
                >
                  <template #columnRight>
                    <el-table-column label="参考范围" width="100">
                      <template slot-scope="scope">
                        {{ scope.row.lowerLimit + '~' + scope.row.upperLimit }}
                      </template>
                    </el-table-column>
                  </template>
                </PublicTable>
              </div>
            </div>
            <el-button
              type="text"
              style="padding: 2px 5px"
              slot="reference"
              class="reply_btn"
              >回复</el-button
            >
          </el-popover>
        </template>
      </el-table-column>
    </template>
  </PublicTable>
</template>

<script>
import PublicTable from '@/components/publicTable';
import { mapActions, mapGetters } from 'vuex';
export default {
  name: 'danger',
  components: {
    PublicTable
  },
  computed: {
    ...mapGetters(['G_urgentList', 'G_EnumList', 'G_userInfo'])
  },
  data() {
    return {
      urgentTheads: {
        regNo: '体检号',
        name: '姓名',
        age: '年龄',
        sex: '性别',
        createTime: '发起时间',
        creator: '发起人'
        // type: "异常等级",
      },
      urgentColumnWidth: {
        regNo: 150,
        name: 100,
        age: 60,
        sex: 60,
        // createTime:'发起时间',
        creator: 100
        // type:'异常等级'
      },
      urgentGrade: {
        1: '危急',
        2: '重大阳性'
      },
      infoColumnWidth: {},
      infoTheads: {
        itemName: '项目名称',
        itemResult: '结果'
      },
      infoList: [],
      replyVal: ''
    };
  },
  methods: {
    ...mapActions(['A_getUrgentList']),
    // 回复弹窗显示的回调
    popoverShow(row) {
      this.replyVal = '';
      this.infoList = row.items;
    },
    // 确认回复
    replyHandle(row) {
      if (this.replyVal == '') {
        this.$message({
          message: '请输入回复内容！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let datas = {
        id: row.id,
        regNo: row.regNo,
        replyContent: this.replyVal,
        replyPerson: this.G_userInfo.codeOper.operatorCode
      };
      this.$ajax.post(this.$apiUrls.ReplyCriticalValue, datas).then((r) => {
        let { success } = r.data;
        if (!success) return;
        this.$message({
          message: '回复成功！',
          type: 'success',
          showClose: true
        });
        let parameter = {
          ...row,
          notifierL: datas.replyContent,
          afterFollowUp: datas.replyPerson
        };
        this.$emit('jump', parameter);
        row.popoverVisible = false;
        this.A_getUrgentList();
      });
    }
  }
};
</script>

<style></style>
