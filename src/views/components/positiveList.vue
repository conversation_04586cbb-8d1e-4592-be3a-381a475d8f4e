<template>
  <PublicTable
    :theads="urgentTheads"
    :viewTableList="G_positiveList"
    :columnWidth="urgentColumnWidth"
  >
    <template #sex="{ scope }">
      {{ G_EnumList['Sex'][scope.row.regInfo.sex] }}
    </template>
    <template #name="{ scope }">
      {{ scope.row.regInfo.name }}
    </template>
    <template #age="{ scope }">
      {{ scope.row.regInfo.age }}
    </template>
    <template #columnRight>
      <el-table-column label="结果" width="90">
        <template slot-scope="scope">
          <el-popover
            placement="right"
            width="600"
            @show="popoverShow(scope.row)"
            trigger="click"
          >
            <div>
              <div style="max-height: 500px">
                <PublicTable
                  :theads="infoTheads"
                  :viewTableList="infoList"
                  :columnWidth="infoColumnWidth"
                >
                  <template #columnRight>
                    <el-table-column label="参考范围" width="100">
                      <template slot-scope="scope">
                        {{ scope.row.lowerLimit + '~' + scope.row.upperLimit }}
                      </template>
                    </el-table-column>
                  </template>
                </PublicTable>
              </div>
            </div>
            <el-button
              type="text"
              style="padding: 2px 5px"
              slot="reference"
              class="reply_btn"
              >查看结果</el-button
            >
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="65">
        <template slot-scope="scope">
          <el-button
            type="text"
            style="padding: 2px 5px"
            slot="reference"
            class="reply_btn"
            @click="confirmMajorPositive(scope.row)"
            >确认</el-button
          >
        </template>
      </el-table-column>
    </template>
  </PublicTable>
</template>

<script>
import PublicTable from '@/components/publicTable';
import { mapActions, mapGetters, mapMutations } from 'vuex';
export default {
  name: 'positiveList',
  components: {
    PublicTable
  },
  computed: {
    ...mapGetters(['G_positiveList', 'G_EnumList'])
  },
  data() {
    return {
      urgentTheads: {
        regNo: '体检号',
        name: '姓名',
        age: '年龄',
        sex: '性别',
        recordTime: '发起时间',
        positiveCode: '重大阳性代码',
        positiveName: '重大阳性名称',
        recordOperator: '发起人'
        // type: "异常等级",
      },
      urgentColumnWidth: {
        regNo: 150,
        name: 100,
        age: 60,
        sex: 60,
        recordTime: 150,
        recordOperator: 100,
        positiveCode: 105
        // type:'异常等级'
      },
      urgentGrade: {
        1: '危急',
        2: '重大阳性'
      },
      infoColumnWidth: {},
      infoTheads: {
        itemName: '项目名称',
        itemResult: '结果'
      },
      infoList: []
    };
  },
  methods: {
    ...mapMutations(['M_delPositiveList']),
    ...mapActions(['A_GetMajorPositiveDetailToDos']),
    // 回复弹窗显示的回调
    popoverShow(row) {
      this.infoList = row.items;
    },
    // 确认重大阳性
    confirmMajorPositive(row) {
      console.log(row);

      this.$confirm('是否确认此记录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax
            .paramsPost(this.$apiUrls.ConfirmMajorPositive, {
              majorPositiveId: row.id
            })
            .then((r) => {
              let { success } = r.data;
              if (!success) return;
              this.$message({
                message: '确认成功！',
                type: 'success'
              });
              this.$nextTick(() => {
                this.A_GetMajorPositiveDetailToDos();
              });
            });
        })
        .catch(() => {});
    }
  }
};
</script>

<style></style>
