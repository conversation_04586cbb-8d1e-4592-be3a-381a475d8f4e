<script>
import TagsLabel from '@/components/tagsLabel';
import { mapActions, mapGetters, mapMutations } from 'vuex';
import WS from '@/common/websocket';
import Vue from 'vue';
import moment from 'moment';
import PublicTable from '@/components/publicTable';
import { dataUtils, storage } from '../common';
import NavWaterFall from '@/components/navWaterFall.vue';
import NavDialog from '@/components/navDialog.vue';
import IndexMixins from './indexMixins.js';
import LoginDialog from './components/loginDialog';
import printMixins from '../components/printMixinsV2.js';
import Danger from './components/danger.vue';
import PositiveList from './components/positiveList';

export default {
  mixins: [IndexMixins, printMixins],
  components: {
    TagsLabel,
    PublicTable,
    NavWaterFall,
    NavDialog,
    LoginDialog,
    Danger,
    PositiveList
  },
  computed: {
    ...mapGetters([
      'G_keepAlive',
      'G_reloadFlag',
      'G_leftMenuList',
      'G_userInfo',
      'G_rowRes',
      'G_bellList',
      'G_noticeList',
      'G_urgentList',
      'G_EnumList',
      'getterNavList',
      'G_gatewayToken',
      'G_headShow',
      'G_printerList',
      'G_printerState',
      'G_datePickerShortcuts',
      'G_positiveList'
    ]),
    key() {
      this.M_btnAuthList(this.$route.meta.other);
      this.idxPatientList = [];
      this.currentRoute = this.$route;
      this.dateArr = [
        moment().startOf('day').format('YYYY-MM-DD'),
        moment().startOf('day').format('YYYY-MM-DD')
      ];
      return this.$route.path;
    },
    G_isGateway() {
      return !storage.session.get('isGateway');
    }
  },
  async created() {
    // this.openLongTimeExamine();
    this.GetEnumList();
    this.M_chatRoomList();
    await Promise.all([
      this.A_getUrgentList(),
      this.A_GetMajorPositiveDetailToDos()
    ]);
    this.urgentShow();

    if (this.G_urgentList.length != 0 || this.G_positiveList.length != 0) {
      let urgentSetInterval;
      urgentSetInterval && clearInterval(urgentSetInterval);
      urgentSetInterval = setInterval(() => {
        this.urgentShow();
      }, 60000);
    }
  },
  mounted() {
    this.M_getClinicList();
    // this.fullscreen();
    window.addEventListener('unload', (e) => this.unloadHandler(e));
    this.navList = this.getterNavList;
    this.initSideBarData();

    //获取打印机
    // this.getAListOfPrinters();
  },
  destroyed() {
    window.removeEventListener('unload', (e) => this.unloadHandler(e));
  },
  data() {
    return {
      printerData: {
        printer: '',
        barcodePrinters: '',
        printerList: []
      },
      setThePrinterDisplay: false,
      currentRoute: {},
      patId: '',
      openType: '',
      iframeShow: false,
      menuShow: true,
      defaultIdx: '/',
      navShow: false, //控制导航页显示隐藏
      sideBarShow: false,
      hospName: storage.session.get('user')?.hospName,
      modifyPasswordShow: false,
      modifyInfo: {
        name: '',
        password: ''
      },
      rules: {
        oldPassword: [
          { required: true, message: '请输入旧密码', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' }
        ]
      },
      menuSearchVal: '',
      navList: [],
      sideBarData: [],
      urgentHintTabName: 'positive',
      fixed_sideBarData: [
        {
          icon: require('@/assets/img/shortcut/gerendengji.png'),
          title: '个人登记',
          name: 'personRegisterV2',
          path: '/register/personRegisterV2'
        },
        {
          icon: require('@/assets/img/shortcut/tuantidengji.png'),
          title: '团体登记',
          name: 'groupRegisterV2',
          path: '/register/groupRegisterV2'
        },
        {
          icon: require('@/assets/img/shortcut/gerenjiaofei.png'),
          title: '个人缴费',
          name: 'personCharge',
          path: '/financialManagement/personCharge'
        },
        {
          icon: require('@/assets/img/shortcut/caijipeiguan.png'),
          title: '采集配管',
          name: 'collectPiping',
          path: '/inspectIn/collectPiping'
        },
        {
          icon: require('@/assets/img/shortcut/biaobenyunsong.png'),
          title: '标本采集',
          name: 'specimenCollection',
          path: '/inspectIn/specimenCollection'
        },
        {
          icon: require('@/assets/img/shortcut/yishenggongzuozhan.png'),
          title: '医生工作站',
          name: 'doctorWorkStationV2',
          path: '/inspectIn/doctorWorkStationV2'
        },
        {
          icon: require('@/assets/img/shortcut/jieguoluru.png'),
          title: '结果录入',
          name: 'resultEntryV2',
          path: '/inspectIn/resultEntryV2'
        },
        {
          icon: require('@/assets/img/shortcut/zhujian.png'),
          title: '主检',
          name: 'mainInspection',
          path: '/mainInspectionPage/mainInspection'
        },
        {
          icon: require('@/assets/img/shortcut/shenhe.png'),
          title: '审核',
          name: 'process',
          path: '/mainInspectionPage/process'
        },
        // {
        //   icon: require("@/assets/img/sideBar/print_guide.png"),
        //   title: "打印指引单",
        //   name:'',
        //   path: ""
        // },
        {
          icon: require('@/assets/img/shortcut/baogaodayin.png'),
          title: '报告打印',
          name: 'reportPrinting',
          path: '/reportManagement/reportPrinting'
        },
        {
          icon: require('@/assets/img/shortcut/shujuchaxun.png'),
          title: '综合查询',
          name: 'comprehensiveQuery',
          path: '/dataQuery/comprehensiveQuery'
        }
        // {
        //   icon: require("@/assets/img/sideBar/log.png"),
        //   title: "日志查询",
        //   name:'logQuery',
        //   path: "/dataQuery/logQuery"
        // }
      ],
      height: '0',
      noticeShow: false,
      checkRow: {},
      urgentHintShow: false,
      examineTime: '',
      thirdpartyList: [
        {
          icon: require('@/assets/img/thirdparty/zhenliaoquanlan.png'),
          name: '诊疗全览',
          path: 'medicalRecord'
        },
        {
          icon: require('@/assets/img/thirdparty/shoufeichaxun.png'),
          name: '收费查询',
          path: 'charge'
        },
        {
          icon: require('@/assets/img/thirdparty/binglichaxun.png'),
          name: '病历查询',
          path: 'medicalRecord'
        },
        {
          icon: require('@/assets/img/thirdparty/jianchabaogao.png'),
          name: '检查报告',
          path: 'exam'
        },
        {
          icon: require('@/assets/img/thirdparty/jianyanbaogao.png'),
          name: '检验报告',
          path: 'lab'
        },
        {
          icon: require('@/assets/img/thirdparty/binglibaogao.png'),
          name: '病理报告',
          path: 'pathology'
        },
        {
          icon: require('@/assets/img/thirdparty/yingxiangbaogao.png'),
          name: '影像报告',
          path: 'image'
        },
        {
          icon: require('@/assets/img/thirdparty/zhiliaobaogao.png'),
          name: '治疗报告',
          path: 'treatment'
        },
        {
          icon: require('@/assets/img/thirdparty/chuzhenchaxun.png'),
          name: '出诊查询',
          path: 'visitInquiry'
        }
        // {
        //   icon:require('@/assets/img/thirdparty/linchuangyaoshi.png'),
        //   name:'临床药师',
        //   path:'',
        // },
      ]
    };
  },
  methods: {
    ...mapMutations([
      'M_EnumList',
      'M_btnAuthList',
      'M_resData',
      'M_chatRoomList',
      'M_addChatRoomList',
      'M_getClinicList',
      'M_addUrgentList',
      'M_delUrgentList',
      'M_activeProgressShow',
      'M_CompanyImport',
      'M_BatchAddOrDeleteShow',
      'M_printerList',
      'M_addPositiveList',
      'M_delPositiveList'
    ]),
    ...mapActions([
      'A_logout',
      'A_getUrgentList',
      'A_GetMajorPositiveDetailToDos'
    ]),
    initSideBarData() {
      const menuList = storage.session.get('menuList');
      const nameSet = new Set();
      menuList?.forEach((item) => {
        item.children.forEach((child) => {
          nameSet.add(child.name);
        });
      });
      this.sideBarData = this.fixed_sideBarData.filter((item) =>
        nameSet.has(item.name)
      );
      // console.log('this.sideBarData', this.sideBarData);
    },

    /**
     * @author: justin
     * @description: 主服务端websocket初始化
     * @return {*}
     */
    webSocketMainInit() {
      Vue.prototype.$ws && Vue.prototype.$ws?.closeWebSocket(); //关闭之前的连接
      const { token } = this.G_userInfo || {};
      if (!token || !token.access_token) {
        return;
      }
      // 创建websocket连接
      Vue.prototype.$ws = new WS(
        `${this.$config.websocketUrl}/socket?access_token=${token.access_token}`
      );

      let sendData = {
        SendUser: this.G_userInfo.codeOper.operatorCode,
        ReceiveUser: 'Service',
        Content: '连接'
      };
      // 创建连接
      this.$ws.sendSock(JSON.stringify(sendData));
      // 接收消息的回调
      this.$ws.websocketonmessage = async (e) => {
        let chatInfo = JSON.parse(decodeUnicode(e.data));
        console.log(chatInfo);

        let nowTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
        // 踢出登录
        if (chatInfo.statusCode == '499') {
          this.$message({
            message: chatInfo.msgData.content,
            type: 'warning',
            duration: 5000,
            showClose: true
          });
          this.A_logout();
          return;
        }

        if (chatInfo.msgData?.chatId == this.G_rowRes?.chatId) {
          this.M_resData(chatInfo.msgData);
        }
        if (chatInfo.msgData?.statusCode == '201') {
          this.readLatestChat(chatInfo.msgData.chatId);
        }
        // 危急
        if (chatInfo.msgCode == 'WeiJiZhi-Notice') {
          // for(let keys in chatInfo.msgData){
          //   chatInfo.msgData[`${keys.toLowerCase()}`] = chatInfo.msgData[keys];
          //   delete chatInfo.msgData[keys];
          // }
          chatInfo.msgData.popoverVisible = false;
          // 未处理
          if (chatInfo.msgData.state == 1) {
            await this.M_addUrgentList(chatInfo.msgData);
          } else {
            await this.M_delUrgentList(chatInfo.msgData.id);
          }
          this.urgentShow();
        }
        // 重大阳性
        if (chatInfo.msgCode == 'MajorPositive-Notice') {
          chatInfo.msgData.popoverVisible = false;
          // 未处理
          if (chatInfo.msgData.state == 1) {
            await this.M_addPositiveList(chatInfo.msgData);
          } else {
            await this.M_delPositiveList(chatInfo.msgData.id);
          }
          this.urgentShow();
        }
        // 激活进度
        if (chatInfo.msgCode == 'ActivationOrCancelForBatch') {
          this.M_activeProgressShow({ flag: true, msgData: chatInfo.msgData });
          if (chatInfo.msgData.currentProgressPercentage >= 100) {
            setTimeout(() => {
              this.M_activeProgressShow({
                flag: false,
                msgData: chatInfo.msgData
              });
              this.$message({
                showClose: true,
                message: '操作完成!',
                type: 'success'
              });
            }, 500);
          }
        }
        // 导入进度
        if (chatInfo.msgCode == 'CompanyImport') {
          if (chatInfo.msgData.currentProgressPercentage < 100) {
            this.M_CompanyImport({ flag: true, msgData: chatInfo.msgData });
          } else {
            this.M_CompanyImport({ flag: false, msgData: chatInfo.msgData });
          }
        }
        // 团体批增加/删除
        if (chatInfo.msgCode == 'CompanyBatchAddOrDelete') {
          this.M_BatchAddOrDeleteShow({
            flag: true,
            msgData: chatInfo.msgData
          });
          if (chatInfo.msgData.currentProgressPercentage >= 100) {
            setTimeout(() => {
              this.M_BatchAddOrDeleteShow({
                flag: false,
                msgData: chatInfo.msgData
              });
              this.$message({
                showClose: true,
                message: '操作完成!',
                type: 'success'
              });
            }, 500);
          }
        }

        this.M_getClinicList();

        function decodeUnicode(str) {
          str = str.replace(/\\/g, '%');
          //转换中文
          str = unescape(str);
          //将其他受影响的转换回原来
          str = str.replace(/%/g, '\\');
          //对网址的链接进行处理
          str = str.replace(/\\/g, '');
          return str;
        }
      };
    },
    // 快捷菜单的点击回调
    shortcutClick(path) {
      this.$router.push(path);
      this.iframeShow = false;
      this.navShow = false;
    },
    // 获取登录的用户列表
    LoginUsers() {
      this.$ajax.get(this.$apiUrls.LoginUsers).then((r) => {});
    },
    // 监听页面刷新关闭全局的socket连接
    unloadHandler() {
      Vue.prototype.$ws && Vue.prototype.$ws.closeWebSocket();
    },
    fullscreen() {
      const element = document.documentElement;
      if (element.requestFullscreen) {
        element.requestFullscreen();
      } else if (element.webkitRequestFullScreen) {
        element.webkitRequestFullScreen();
      } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen();
      } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen();
      }
    },
    //显示导航弹出层
    showNavDig() {
      this.navShow = true;
    },
    navClick(twoItem, item) {
      this.$router.push({
        name: twoItem.name
      });
      this.iframeShow = false;
      // this.navShow = false;
      // this.menuSearchVal = "";
      // this.navList = dataUtils.deepCopy(this.getterNavList);
    },
    navDialogClick(twoItem) {
      this.$router.push({
        name: twoItem
      });
      this.iframeShow = false;
      this.navShow = false;
      this.menuSearchVal = '';
      this.navList = dataUtils.deepCopy(this.getterNavList);
    },
    navClose() {
      this.navShow = false;
      this.menuSearchVal = '';
      this.navList = dataUtils.deepCopy(this.getterNavList);
    },
    menuHover() {
      this.height = '300px';
    },
    menuLeave() {
      this.height = '0';
    },
    sideBarClick(path) {
      if (path === '') {
        this.$message.warning('对不起，您没有该页面的操作权限！');
      } else {
        this.$router.push({
          path: path
        });
      }
    },
    handleSelect(key) {
      this.$router.push({
        path: key
      });
    },
    //获取常用枚举列表
    GetEnumList() {
      this.$ajax.post(this.$apiUrls.GetEnumList).then((r) => {
        // console.log('枚举类型数据', r);
        this.M_EnumList(r.data.returnData.data);
      });
    },
    // 获取最新的会诊聊天室
    readLatestChat(chatId) {
      this.$ajax
        .paramsPost(this.$apiUrls.ReadLatestChat, { chatId })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.M_addChatRoomList(returnData);
        });
    },
    // 消息通知的点击回调
    dropdownClick(row) {
      if (row.msgCode == 'HuiZhen') {
        let datas = {
          chatId: row.subjectId,
          createTime: row.lastTime,
          receiveUser: this.G_userInfo.codeOper.operatorCode
        };

        this.$ajax.post(this.$apiUrls.UpdateChatMsgStauts, datas).then((r) => {
          let { success } = r.data;
          if (!success) return;
          this.M_getClinicList();
        });
        this.$router.push({
          name: 'doctorWorkStation',
          params: row
        });
      } else {
        this.noticeShow = true;
        this.checkRow = row;
        let datas = {
          noticeId: row.subjectId,
          operCode: this.G_userInfo.codeOper.operatorCode
        };
        this.$ajax.paramsPost(this.$apiUrls.UserReadNotice, datas).then((r) => {
          let { success } = r.data;
          if (!success) return;
          this.M_getClinicList();
        });
      }
    },
    // 危急点击回调
    urgentClick() {
      this.urgentHintShow = true;
    },
    jump(row) {
      let itemName = '';
      let itemResult = '';
      row.items.forEach((item) => {
        itemName += item.itemName || '';
        itemResult += item.itemResult || '';
      });
      let datas = {
        regNo: row.regNo,
        followContent: itemName + ':' + itemResult,
        deptCode: this.G_userInfo.codeOper.deptCode,
        recorder: this.G_userInfo.codeOper.name,
        abnormalType: 3,
        majorPositiveId: row.majorPositiveId,
        notifier: row.notifierL,
        afterFollowUp: row.afterFollowUp
      };
      this.$ajax.post(this.$apiUrls.InsertPeFollowUp, datas).then((r) => {
        let { success } = r.data;
        if (!success) return;
      });
    },
    // 显示危急弹窗
    urgentShow() {
      let str = JSON.stringify(this.getterNavList || {});
      if (
        // (str.includes("/mainInspectionPage/mainInspection") ||
        //   str.includes("/mainInspectionPage/mainInspectionList") ||
        //   str.includes("/inspectIn/process")) &&
        this.G_urgentList.length != 0 ||
        this.G_positiveList.length != 0
      ) {
        this.urgentHintShow = true;
      }
      if (this.G_urgentList.length != 0) {
        this.urgentHintTabName = 'danger';
        return;
      }
      this.urgentHintTabName = 'positive';
    },
    // 搜索菜单
    searchMenu() {
      let navList = dataUtils.deepCopy(this.getterNavList);
      navList.map((item) => {
        let itemArr = [];
        item.children.map((twoItem, twoIdx, arr) => {
          if (twoItem.title.includes(this.menuSearchVal)) {
            itemArr.push(twoItem);
          }
        });
        item.children = dataUtils.deepCopy(itemArr);
      });

      let arr = navList.filter((item) => {
        return item.children.length != 0;
      });
      this.navList = arr;
    },
    menuOpen() {
      this.menuSearchVal = '';
      this.navList = dataUtils.deepCopy(this.getterNavList);
    },
    // 显示修改密码弹窗
    modifyPassword() {
      this.modifyPasswordShow = true;
      this.modifyInfo.operatorCode = this.G_userInfo.codeOper.operatorCode;
    },
    // 修改密码弹窗关闭前的回调
    handleClose(done) {
      this.$refs.modify_Ref.resetFields();
      this.$nextTick(() => {
        done();
      });
    },
    // 确定修改密码
    saveModifyPassword() {
      this.$refs.modify_Ref.validate((valid) => {
        if (valid) {
          this.$ajax
            .paramsPost(this.$apiUrls.ChangeUserPassword, this.modifyInfo)
            .then((r) => {
              let { success, returnData } = r.data;
              if (!success) return;
              this.$message({
                message: '修改密码成功，请重新登录帐号',
                type: 'success',
                showClose: true
              });
              this.A_logout();
            });
        } else {
          return false;
        }
      });
    },
    // 显示左边菜单
    showMenu(flag) {
      this.sideBarShow = flag;
    },
    // 开启长时间未操作的检测
    openLongTimeExamine() {
      var lastTime = Date.now();
      var currentTime = Date.now();
      var timeOut = 120 * 60 * 1000; //设置超时时间： 30分
      storage.session.set('lastTime', lastTime);
      window.document.onmousedown = function () {
        storage.session.set('lastTime', Date.now());
      };
      let that = this;
      function checkTimeout() {
        currentTime = Date.now(); //更新当前时间
        lastTime = storage.session.get('lastTime');
        if (currentTime - lastTime > timeOut) {
          //判断是否超时
          that.examineOut();
          that.$alert('登录已超时，请重新登录！', '提示', {
            confirmButtonText: '确定',
            callback: async () => {
              await that.examineOut();
              that.A_logout();
            }
          });
        }
      }
      /* 定时器 间隔30秒检测是否长时间未操作页面 */
      this.examineTime = setInterval(checkTimeout, 3000);
    },
    // 退出登录清空检测
    examineOut() {
      clearInterval(this.examineTime);
      window.document.onmousedown = '';
    },
    // 显示/隐藏菜单
    menuClick() {
      this.menuShow = !this.menuShow;
    },
    // 第三方功能按钮的点击回调
    gatewayTokenClick(openType) {
      if (
        !this.$refs.routerView_Ref.HisPatInfo ||
        !this.$refs.routerView_Ref.HisPatInfo.regNo
      ) {
        this.patId = '';
        this.openType = openType;
        this.iframeShow = true;
        return;
      }
      this.$ajax
        .paramsPost(this.$apiUrls.GetHisPatientIndex, {
          regNo: this.$refs.routerView_Ref.HisPatInfo.regNo
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) {
            this.patId = '';
            this.openType = openType;
            this.iframeShow = true;
            return;
          }
          this.patId = returnData;
          this.openType = openType;
          this.iframeShow = true;
        });
    },
    // 关闭iframe弹窗
    iframeClose() {
      this.iframeShow = false;
    },
    /**
     * @description: 打印机弹窗确定
     * @return {*}
     * @author: key
     */
    setUpThePrinter() {
      storage.local.set('print', {
        printer: this.printerData.printer,
        barcodePrinters: this.printerData.barcodePrinters
      });
      this.setThePrinterDisplay = false;
    },

    /**
     * @description: 获取打印机列表并设置打印机默认值
     * @return {*}
     * @author: key
     */
    getAListOfPrinters() {
      let that = this;
      this.connectPrint((r) => {
        const dataInfo = JSON.parse(r.data);
        console.log(dataInfo);

        that.M_printerList(dataInfo.Data);
        const print = storage.local.get('print');

        that.printerData.printer = print?.printer || dataInfo.Data[0];
        that.printerData.barcodePrinters =
          print?.barcodePrinters || dataInfo.Data[0];
      });
    },
    /**
     * @description: 断开链接
     * @return {*}
     * @author: key
     */
    breakTheLink() {
      if (!this.$w_print) return;
      this.$w_print.closeWebSocket && this.$w_print.closeWebSocket();
      this.$w_print.reConnect = () => {};
      this.$w_print.createWebSocket = () => {};
      this.$w_print.heartCheck.stop && this.$w_print.heartCheck.stop();
    }
  },
  destroyed() {
    this.breakTheLink();
  },
  watch: {
    //el-tooltip菜单关闭,数据初始化
    navShow(val) {
      if (val == false) {
        this.menuSearchVal = '';
        this.navList = dataUtils.deepCopy(this.getterNavList);
      }
    },
    G_userInfo: {
      // 监听用户信息
      handler(newVal, oldVal) {
        this.webSocketMainInit();
      },
      deep: true,
      immediate: true
    },
    setThePrinterDisplay: {
      handler(newVal, oldVal) {
        if (!newVal) {
          this.printerData.printer = '';
          this.printerData.barcodePrinters = '';
        } else {
          this.getAListOfPrinters();
          console.log(this.printerData.printList);
          // const print = storage.local.get("print");

          // this.printerData.printer = print
          //   ? print.printer
          //   : this.printerData.printList[0];
          // this.printerData.barcodePrinters = print
          //   ? print.barcodePrinters
          //   : this.printerData.printList[0];
        }
      }
    }
  }
};
</script>

<template>
  <div
    class="indexPage"
    @click="showMenu(false)"
    :class="{ pagePaddingTop: G_headShow }"
  >
    <!-- 显示菜单 -->
    <div v-if="false" class="show_menu" @mouseenter="showMenu(true)"></div>
    <div class="headerWrapper" v-if="G_headShow">
      <header @mouseleave="menuLeave">
        <div class="container-box">
          <div class="container">
            <div class="logo">
              <!-- <img src="/logo.png" alt="" /> -->
              <h3>
                {{ $config.hospitalName }}
                <span v-if="hospName"> ({{ hospName }}) </span>
              </h3>
            </div>
            <!-- 页面快捷按钮 -->
            <div class="index_shortcut_wrap">
              <ul>
                <el-popover
                  placement="bottom-start"
                  width="500"
                  v-model="patientListShow"
                  @show="getPatientList"
                  trigger="click"
                >
                  <ul class="userList_ul">
                    <li>
                      <label>体检时间</label>
                      <p>
                        <el-date-picker
                          :picker-options="{ shortcuts: G_datePickerShortcuts }"
                          type="daterange"
                          format="yyyy-MM-dd"
                          value-format="yyyy-MM-dd"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          :clearable="false"
                          size="small"
                          v-model="dateArr"
                          @change="getPatientList"
                          style="width: 100%"
                        >
                        </el-date-picker>
                      </p>
                    </li>
                    <li>
                      <p>
                        <el-input
                          size="small"
                          placeholder="体检号/姓名"
                          clearable
                          v-model="searchIpt"
                          @keyup.enter.native="getPatientList"
                          @clear="getPatientList"
                        ></el-input>
                      </p>
                      <span>
                        <el-button
                          class="blue_btn btn"
                          size="mini"
                          icon="iconfont icon-search"
                          @click="getPatientList"
                          >查询</el-button
                        >
                      </span>
                    </li>
                  </ul>
                  <PublicTable
                    style="height: 450px"
                    :theads="patientListTheads"
                    :viewTableList="idxPatientList"
                    :columnWidth="idxPatientListCWidth"
                    @rowDblclick="patientListDbClick"
                  >
                    <template #sex="{ scope }">
                      <div class="sex_wrap">
                        <img
                          v-if="scope.row.sex == 1"
                          src="@/assets/img/nan.png"
                          alt=""
                        />
                        <img
                          v-if="scope.row.sex == 2"
                          src="@/assets/img/nv.png"
                          alt=""
                        />
                        {{ G_EnumList['Sex'][scope.row.sex] }}
                      </div>
                    </template>
                  </PublicTable>
                  <li slot="reference">
                    <img
                      src="@/assets/img/shortcut/yonghuduilie.png"
                      style="filter: hue-rotate(30deg)"
                    />
                    <span @click="iframeShow = false">用户队列</span>
                  </li>
                </el-popover>
                <li
                  v-for="(item, idx) in sideBarData"
                  :key="idx"
                  @click="shortcutClick(item.path)"
                >
                  <img :src="item.icon" style="filter: hue-rotate(30deg)" />
                  <span>{{ item.title }}</span>
                </li>
                <li @click="shortcutClick('/inspectIn/OPOrdering')">
                  <img
                    src="@/assets/img/shortcut/yishenggongzuozhan.png"
                    alt=""
                    style="filter: hue-rotate(30deg)"
                  />
                  <span>门诊医生工作站</span>
                </li>
              </ul>
              <div class="news_wrap" v-if="!G_headShow">
                <div class="navIconDiv" style="margin-right: 10px">
                  <el-popover
                    placement="bottom"
                    popper-class="menu_popover"
                    v-model="navShow"
                    trigger="click"
                  >
                    <div class="navDialogDiv">
                      <div class="navIpt">
                        <el-input
                          placeholder="请输入内容"
                          clearable
                          @input="searchMenu"
                          v-model="menuSearchVal"
                          size="small"
                          @clear="searchMenu"
                        ></el-input>
                      </div>
                      <NavDialog
                        :dataMenus="navList"
                        ref="navDialog"
                        :navTitleClick="navDialogClick"
                      />
                    </div>
                    <span
                      title="菜单"
                      slot="reference"
                      class="iconfont icon-daohangcaozuo"
                    ></span>
                  </el-popover>
                </div>
                <i
                  class="el-icon-printer"
                  @click="setThePrinterDisplay = true"
                  title="打印机设置"
                  style="
                    margin-right: 10px;
                    font-size: 20px;
                    display: flex;
                    align-items: center;
                    cursor: pointer;
                  "
                  :style="{ color: G_printerState === 1 ? '#079c66' : '' }"
                ></i>
                <div class="critical">
                  <img
                    src="../assets/img/critical_n.png"
                    alt=""
                    v-if="G_urgentList.length == 0"
                  />
                  <el-badge v-else :value="G_urgentList.length" class="item">
                    <img
                      @click="urgentClick"
                      src="../assets/img/critical_h.png"
                      alt=""
                    />
                  </el-badge>
                </div>
                <div class="news">
                  <el-badge
                    class="item"
                    :value="G_bellList.length == 0 ? '' : G_bellList.length"
                  >
                    <el-dropdown @command="dropdownClick">
                      <span
                        class="iconfont icon-tongzhixiaoxi news-icon"
                      ></span>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item
                          :command="item"
                          class="dropdown_item"
                          :icon="
                            item.msgCode == 'Notice'
                              ? 'iconfont icon-gonggao-tongzhi-09'
                              : 'iconfont icon-tuantixian'
                          "
                          v-for="item in G_bellList"
                          :key="item.subjectId"
                          >{{ item.subject }}</el-dropdown-item
                        >
                      </el-dropdown-menu>
                    </el-dropdown>
                  </el-badge>
                </div>
              </div>
            </div>
            <div class="userImgWrap">
              <div class="navIconDiv">
                <el-popover
                  placement="bottom"
                  popper-class="menu_popover"
                  v-model="navShow"
                  trigger="click"
                >
                  <div class="navDialogDiv">
                    <div class="navIpt">
                      <el-input
                        placeholder="请输入内容"
                        clearable
                        @input="searchMenu"
                        v-model="menuSearchVal"
                        size="small"
                        @clear="searchMenu"
                      ></el-input>
                    </div>
                    <NavDialog
                      :dataMenus="navList"
                      ref="navDialog"
                      :navTitleClick="navDialogClick"
                    />
                  </div>
                  <span
                    title="菜单"
                    slot="reference"
                    class="iconfont icon-daohangcaozuo"
                  ></span>
                </el-popover>
              </div>
              <div class="critical">
                <img
                  src="../assets/img/critical_n.png"
                  alt=""
                  v-if="G_urgentList.length == 0 && G_positiveList.length == 0"
                />
                <el-badge
                  v-else
                  :value="G_urgentList.length + G_positiveList.length"
                  class="item"
                >
                  <img
                    @click="urgentClick"
                    src="../assets/img/critical_h.png"
                    alt=""
                  />
                </el-badge>
              </div>
              <div class="news">
                <el-badge
                  class="item"
                  :value="G_bellList.length == 0 ? '' : G_bellList.length"
                >
                  <el-dropdown @command="dropdownClick">
                    <span class="iconfont icon-tongzhixiaoxi news-icon"></span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item
                        :command="item"
                        class="dropdown_item"
                        :icon="
                          item.msgCode == 'Notice'
                            ? 'iconfont icon-gonggao-tongzhi-09'
                            : 'iconfont icon-tuantixian'
                        "
                        v-for="item in G_bellList"
                        :key="item.subjectId"
                        >{{ item.subject }}</el-dropdown-item
                      >
                    </el-dropdown-menu>
                  </el-dropdown>
                </el-badge>
              </div>
              <!-- 用户名 -->
              <div>{{ G_userInfo.codeOper.name }}</div>
              <el-dropdown>
                <div class="el-dropdown-link">
                  <img src="../assets/img/login_user.png" />
                  <span class="iconfont icon-gengduo more"></span>
                </div>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item @click.native="setThePrinterDisplay = true"
                    >打印机设置</el-dropdown-item
                  >
                  <el-dropdown-item @click.native="modifyPassword()"
                    >修改密码</el-dropdown-item
                  >
                  <el-dropdown-item @click.native="A_logout()"
                    >退出登录</el-dropdown-item
                  >
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
        </div>
        <!-- 菜单下拉框 -->
        <!-- <div class="menuDropdown" :style="{ height: height }"></div> -->
        <!-- <div class="cagsLabel-box">
          <TagsLabel class="cagsLabel" />
        </div> -->
        <!-- 侧边栏 -->
        <div
          class="side-bar"
          :class="{ 'side-bar_show': sideBarShow }"
          @mouseleave="showMenu(false)"
          @click.stop
        >
          <ul class="sideBar-ul">
            <li
              class="sideBar-li"
              v-for="(item, index) in sideBarData"
              :key="index"
              @click="sideBarClick(item.path)"
            >
              <img :src="item.icon" alt="" />
              <div>{{ item.title }}</div>
            </li>
          </ul>
        </div>
      </header>
    </div>
    <el-menu
      :default-active="key"
      class="el-menu-vertical-demo"
      background-color="#216441"
      text-color="#fff"
      :class="{ menu_collapse: menuShow }"
      active-text-color="#ffd04b"
    >
      <el-submenu
        :index="item.menuCode"
        v-for="item in navList"
        :key="item.menuCode"
      >
        <template slot="title">
          <i :class="item.icon"></i>
          <span>{{ item.title }}</span>
        </template>
        <el-menu-item
          @click="navClick(twoItem, item)"
          :index="twoItem.path"
          v-for="twoItem in item.children"
          :key="twoItem.menuCode"
          >{{ twoItem.title }}</el-menu-item
        >
      </el-submenu>
    </el-menu>
    <div
      class="contentWrap"
      :style="{ overflow: iframeShow ? 'hidden' : 'auto' }"
    >
      <!-- 页面层级 -->
      <div class="breadcrumb_wrap">
        <i
          class="breadcrumb_rightArrow"
          :class="menuShow ? 'el-icon-d-arrow-right' : 'el-icon-d-arrow-left'"
          @click="menuClick"
        ></i>
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-breadcrumb-item :to="{ path: '' }"
            >体检中心工作站</el-breadcrumb-item
          >
          <el-breadcrumb-item
            :to="{ path: '' }"
            v-if="currentRoute.meta.parentName"
            >{{ currentRoute.meta.parentName }}</el-breadcrumb-item
          >
          <el-breadcrumb-item>{{ currentRoute.meta.title }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <!-- 第三方页面菜单按钮 -->
      <div class="thirdpartyBtn_wrap" v-if="!G_headShow">
        <ul>
          <!-- <li title="菜单" @click="menuClick">
            <i class="el-icon-menu"></i>
          </li> -->
          <li
            v-for="item in thirdpartyList"
            :key="item.name"
            @click="gatewayTokenClick(item.path)"
          >
            <img :src="item.icon" alt="" />
            <span>{{ item.name }}</span>
          </li>
        </ul>
        <div class="fullView_btn" @click="gatewayTokenClick('panorama')">
          <img src="@/assets/img/thirdparty/quanjingbingli.png" alt="" />
          <span>全景病历</span>
        </div>
        <div class="more_btn">
          <i class="el-icon-d-arrow-right"></i>
        </div>
      </div>

      <div class="cagsLabel-box">
        <TagsLabel class="cagsLabel" />
      </div>
      <transition name="fade-transform" mode="out-in">
        <!-- 多页面 -->
        <keep-alive :include="G_keepAlive" v-if="!$config.isSinglePage">
          <router-view
            ref="routerView_Ref"
            class="tabPage"
            :key="key"
            v-if="G_reloadFlag"
          />
        </keep-alive>
        <!-- 单页面 -->
        <keep-alive
          :include="['batchAddDel']"
          v-if="$config.isSinglePage && G_reloadFlag"
        >
          <router-view class="tabPage" :key="key" ref="routerView_Ref" />
        </keep-alive>
      </transition>
      <div class="iframe_wrap" :class="{ iframe_collapse: iframeShow }">
        <i class="el-icon-close close_i" @click="iframeClose"></i>
        <div
          style="
            overflow: auto;
            height: 100%;
            display: flex;
            flex-direction: column;
          "
        >
          <iframe
            class="iframe_dom"
            :src="
              'https://gdbyway-apps.sschospital.cn/app/emr-cpoe-fe/patientPeports?isClientGrant=true&authorization=MjAwMTY6QkxPT0RfVFJBTlNGVVNJT05fU1lTVEVN&openType=' +
              openType +
              '&patId=' +
              patId
            "
            frameborder="0"
          ></iframe>
        </div>
      </div>
    </div>
    <!-- 公告弹窗 -->
    <el-dialog title="公告通知" :visible.sync="noticeShow" width="50%" center>
      <span v-html="checkRow.lastContent"></span>
      <span
        slot="footer"
        class="dialog-footer"
        style="text-align: right; display: block"
      >
        <el-button type="primary" @click="noticeShow = false">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 危急列表弹窗 -->
    <el-dialog
      title="重大阳性/危急值确认表"
      :close-on-click-modal="false"
      :visible.sync="urgentHintShow"
      width="80%"
      center
      custom-class="urgentHint_dialog"
    >
      <div style="height: 500px; overflow: auto">
        <el-tabs v-model="urgentHintTabName">
          <el-tab-pane name="positive">
            <span slot="label"
              >重大阳性确认表<el-badge
                v-if="G_positiveList.length"
                :value="G_positiveList.length"
                class="item"
            /></span>
            <PositiveList />
          </el-tab-pane>
          <el-tab-pane name="danger">
            <span slot="label"
              >危急值确认表<el-badge
                v-if="G_urgentList.length"
                :value="G_urgentList.length"
                class="item"
            /></span>
            <Danger @jump="jump" />
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
    <!-- 修改密码弹窗 -->
    <el-dialog
      title="修改密码"
      :visible.sync="modifyPasswordShow"
      :before-close="handleClose"
    >
      <el-form :model="modifyInfo" :rules="rules" ref="modify_Ref">
        <el-form-item label="帐号" label-width="70px">
          <el-input
            v-model="modifyInfo.operatorCode"
            readonly
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="旧密码" label-width="70px" prop="oldPassword">
          <el-input
            v-model="modifyInfo.oldPassword"
            show-password
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="新密码" label-width="70px" prop="newPassword">
          <el-input
            v-model="modifyInfo.newPassword"
            show-password
            autocomplete="off"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="modifyPasswordShow = false">取 消</el-button>
        <el-button type="primary" @click="saveModifyPassword">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 打印机设置弹窗 -->
    <el-dialog
      title="设置全局打印机"
      width="500px"
      :visible.sync="setThePrinterDisplay"
      :close-on-click-modal="false"
    >
      <div style="display: flex">
        <div>
          <p>打印机</p>
          <el-select v-model="printerData.printer" placeholder="请选择">
            <el-option
              v-for="item in G_printerList"
              :key="item"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
        </div>
        <div style="margin-left: 20px">
          <p>条形打印机</p>
          <el-select v-model="printerData.barcodePrinters" placeholder="请选择">
            <el-option
              v-for="item in G_printerList"
              :key="item"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="setThePrinterDisplay = false">取 消</el-button>
        <el-button type="primary" @click="setUpThePrinter">确 定</el-button>
      </div>
    </el-dialog>
    <LoginDialog />
  </div>
</template>

<style lang="less" scoped>
// 变量定义
@primary-color: #079c66;
@menu-active-color: #1770df;
@border-color: #f5f5f5;
@text-color: #818586;
@header-height: 50px;
@header-height-small: 40px;

/* Vue过渡动画 - 保留在外部 */
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all 0.25s;
}

.fade-transform-enter {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

// 页面顶部填充
.pagePaddingTop {
  padding-top: @header-height;

  @media screen and (max-width: 1440px) {
    padding-top: @header-height-small;
  }
}

// 主页面容器
.indexPage {
  display: flex;
  height: 100%;

  // iframe相关样式
  .iframe_wrap {
    flex-shrink: 0;
    overflow: auto;
    position: absolute;
    bottom: 0;
    height: calc(100% - 76px);
    width: 80%;
    z-index: 55;
    transform: translateX(-120%);
    transition: all 0.3s ease-in-out;
    box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.5);

    .close_i {
      position: absolute;
      top: 5px;
      right: 15px;
      cursor: pointer;
    }

    &.iframe_collapse {
      transform: translateX(0);
    }
  }

  .iframe_dom {
    flex: 1;
    overflow: auto;
    background: #fff;
  }

  // 垂直菜单
  .el-menu-vertical-demo {
    width: 200px;
    overflow: auto;
    transition: all 0.3s ease-in-out;

    :deep(.el-menu-item) {
      background-color: #1b5136 !important;
    }

    :deep(.is-active) {
      background-color: #fff !important;
      color: #000 !important;
    }

    &.menu_collapse {
      width: 0;
    }
  }

  // 显示菜单按钮
  .show_menu {
    position: fixed;
    top: 60px;
    left: 0px;
    width: 20px;
    height: 100%;
  }

  .reply_btn {
    padding: 0;
  }

  // 头部区域
  .headerWrapper {
    position: fixed;
    width: 100%;
    left: 0;
    top: 0;
    z-index: 1500;
    background: #fff;
  }

  header {
    border-bottom: 1px solid @border-color;
    position: relative;

    h3 {
      margin-right: 10px;
    }

    .menuWrap {
      display: flex;
      flex: 1;
      flex-shrink: 0;

      .menuBtn {
        font-size: 20px;
        margin-left: 20px;
        cursor: pointer;
      }
    }

    .menuUl {
      display: flex;

      li {
        cursor: pointer;

        & + li {
          margin-left: 20px;
          font-size: 14px;
        }
      }
    }

    .el-dropdown-link {
      display: flex;
      align-items: center;

      img {
        cursor: pointer;
        margin-left: 10px;

        @media screen and (max-width: 1440px) {
          height: 38px;
        }
      }
    }

    .menuDropdown {
      position: absolute;
      top: 50px;
      left: 0;
      right: 0;
      z-index: 10;
      background: #f5f5f5;
      transition: height 0.5s ease-in-out;
    }
  }

  // 容器样式
  .container {
    height: @header-height - 1px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;

    @media screen and (max-width: 1440px) {
      height: @header-height-small;
    }
  }

  // Logo区域
  .logo {
    display: flex;
    align-items: center;

    img {
      height: 32px;
      margin-right: 12px;
    }
  }

  // 用户头像区域
  .userImgWrap {
    display: flex;
    align-items: center;
  }

  // 导航图标
  .navIconDiv {
    margin-right: 20px;
    cursor: pointer;

    span {
      font-size: 24px;
    }
  }

  // 水平菜单
  .el-menu-demo {
    :deep(.el-submenu__title) {
      height: 50px;
      line-height: 50px;
    }

    :deep(.el-submenu.is-active .el-submenu__title) {
      border-bottom-color: #fff;
    }

    :deep(.el-icon-arrow-down:before) {
      content: '';
    }
  }

  // 关键区域和新闻区域
  .critical {
    height: 24px;
    padding: 0 28px;
    border-left: 1px solid @text-color;
    cursor: pointer;
  }

  .news {
    height: 24px;
    margin-right: 28px;
    cursor: pointer;
  }
  // 内容区域
  .contentWrap {
    display: flex;
    flex-direction: column;
    flex: 1;
    flex-shrink: 0;
    padding-left: 5px;
    padding-right: 5px;
    overflow: auto;
    position: relative;

    .breadcrumb_wrap {
      display: flex;
      padding: 3px 0;

      .breadcrumb_rightArrow {
        color: @primary-color;
        margin-right: 5px;
        cursor: pointer;
      }
    }

    .tabPage {
      flex: 1;
      flex-shrink: 0;
      width: 100%;
      min-height: 550px;
      margin: 0 auto;
      border: 1px solid @border-color;
      padding: 5px 0;
      overflow: auto;
    }

    .thirdpartyBtn_wrap {
      display: flex;

      ul {
        flex: 1;
        flex-shrink: 0;
        background: #f5f5f5;
        display: flex;
        overflow: auto;
      }

      .fullView_btn,
      li {
        padding: 3px 5px;
        background: #fff;
        margin-bottom: 5px;
        border-radius: 2px;
        font-size: 16px;
        cursor: pointer;
        flex-shrink: 0;
        color: #17a370;
        display: flex;
        align-items: center;

        img {
          height: 20px;
        }

        & + li {
          margin-left: 5px;
        }
      }

      .more_btn {
        display: flex;
        align-items: center;
        margin-left: 5px;
        cursor: pointer;

        i {
          rotate: 90deg;
          color: @primary-color;
        }
      }
    }
  }
  // 容器盒子
  .container-box {
    border-bottom: 1px solid #e9e9e9;
    padding: 0 10px;
  }

  .cagsLabel-box {
    width: 100%;
    margin: 0 auto;
  }

  // 侧边栏
  .side-bar {
    position: fixed;
    left: -100px;
    top: 50px;
    bottom: 0;
    min-height: 600px;
    max-height: 100%;
    padding: 5px;
    overflow: auto;
    background: #fff;
    border: 1px solid #e8ebed;
    z-index: 2000;
    transition: 300ms ease-in-out;

    &.side-bar_show {
      left: 0;
    }

    @media screen and (max-width: 1280px) {
      padding-right: 5px;
      padding-left: 5px;
    }
  }

  // 侧边栏列表
  .sideBar-ul {
    text-align: center;
    font-size: 12px;
    color: #969a9a;
    display: flex;
    flex-direction: column;
  }

  .sideBar-li {
    margin-bottom: 18px;
    cursor: pointer;

    &:last-child {
      margin-bottom: 0px;
    }

    img {
      width: 28px;
      height: 28px;
      margin-bottom: 4px;
    }
  }

  // 图标样式
  .hide_nav {
    font-size: 24px;
    color: @text-color;
  }

  .more {
    font-size: 20px;
    color: @text-color;
  }

  .news-icon {
    color: @text-color;
    font-size: 24px;
  }
}

/* fade-transform */
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all 0.25s;
}

.fade-transform-enter {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
@media screen and (max-width: 1440px) {
  .pagePaddingTop {
    padding-top: 40px;
  }
  .indexPage {
    .container {
      height: 40px;
    }
    header .el-dropdown-link img {
      height: 38px;
    }
  }
}
@media screen and (max-width: 1280px) {
  .indexPage {
    // .container,
    // .cagsLabel {
    //   width: 1102px;
    // }
    .side-bar {
      padding-right: 5px;
      padding-left: 5px;
    }
    // .contentWrap {
    //   width: 1148px;
    // }
  }
}
</style>
<style lang="less">
.el-menu--horizontal .el-menu--popup {
  min-width: 160px !important;
  min-height: 236px !important;
  box-shadow: 0 2px 8px 0 rgba(45, 52, 54, 0.2) !important;
  overflow-y: auto !important;
}
.el-menu--horizontal .el-submenu .el-menu-item {
  min-width: 160px !important;
}
.el-menu--horizontal .el-menu-item:not(.is-disabled):hover {
  background: rgba(23, 112, 223, 0.1) !important;
  color: #1770df !important;
}
.el-menu--horizontal .el-submenu__title:hover,
.el-menu--horizontal .el-menu .el-menu-item.is-active,
.el-menu--horizontal .el-menu .el-submenu.is-active > .el-submenu__title {
  color: #1770df !important;
}
.dropdown_item {
  width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
//菜单路由弹出层导航样式
.navDialog {
  overflow: hidden;
}
.navDialog .el-dialog {
  width: 90%;
  max-width: 1200px;
  display: flex;
  flex-direction: column;
  margin-top: 40px !important;
  height: auto;
  min-height: 300px;
}
.navDialog .el-dialog__body {
  flex: 1;
  overflow: auto;
  padding: 0px 20px 12px 20px;
}
.navIpt {
  margin-bottom: 10px;
  width: 200px;
}
.navDialogDiv {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.navDialogDiv .nav {
  flex: 1;
  display: flex;
  overflow: auto;
  flex-direction: column;
}
.index_tooltip {
  width: 90%;
  max-width: 1200px;
  border: 1px solid #eee !important;
}
.userList_ul {
  li {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    label {
      margin-right: 5px;
    }
    p {
      flex: 1;
    }
    span {
      margin-left: 5px;
    }
  }
}
.sex_wrap {
  display: flex;
  align-items: center;
  img {
    margin-right: 5px;
    height: 24px;
  }
}
.menu_popover {
  width: 90%;
  max-width: 1200px;
}
.index_shortcut_wrap {
  display: flex;
  border-bottom: 1px solid #ccc;
  ul {
    flex: 1;
    flex-shrink: 0;
    background: #fff;
    display: flex;
    overflow: auto;
    & > span {
      flex-shrink: 0;
    }
    li {
      display: flex;
      align-items: center;
      font-size: 16px;
      // color: rgba(45, 52, 54, 0.6);
      color: #079c66;
      padding: 3px;
      flex-shrink: 0;
      cursor: pointer;
      & + li {
        margin-left: 10px;
      }
      img {
        height: 28px;
        margin-right: 5px;
      }
    }
  }
  .news_wrap {
    display: flex;
    background: #fff;
    padding: 4px 0;
    align-items: center;
    & > div {
      display: flex;
      align-items: center;
    }
    .critical {
      padding: 0 10px;
    }
    .news {
      margin-right: 10px;
    }
  }
}
.urgentHint_dialog {
  .el-dialog__body {
    padding: 0 5px;
  }
  .el-tabs {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .el-tabs__content {
    flex: 1 0 0;
  }
  .el-tab-pane {
    height: 100%;
    overflow: auto;
  }
  .el-tabs__header {
    margin-bottom: 5px;
  }
}
</style>
