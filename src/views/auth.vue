<template>
  <div
    class="auth_page"
    v-loading="loading"
    element-loading-text="正在进入体检系统"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
  ></div>
</template>

<script>
import { mapActions, mapGetters, mapMutations } from 'vuex';
import { storage } from '@/common';

export default {
  name: 'auth',
  data() {
    return {
      loading: true
    };
  },
  methods: {
    ...mapMutations(['M_userInfo', 'M_gatewayToken', 'M_headShow']),
    ...mapActions(['A_getNavList', 'A_getMenuList'])
  },
  mounted() {
    setTimeout(() => {
      storage.session.set('isGateway', true);
      let paramesStr = location.href.split('?');
      let paramsArr = paramesStr[1].split('&');
      console.log(paramsArr);
      let parames = {};
      paramsArr.map((item, idx) => {
        let itemParam = item.split('=');
        if (idx === 0) {
          parames.token = itemParam[1];
          return;
        }
        parames[itemParam[0]] = itemParam[1];
      });
      console.log(parames);
      this.$ajax.paramsPost(this.$apiUrls.ShenShanLogin, parames).then((r) => {
        this.M_headShow(false);
        const { codeOper } = r.data.returnData;
        this.M_userInfo(r.data.returnData);
        this.A_getMenuList(codeOper.operatorCode).then((twoR) => {
          console.log(twoR);
          let { userMenuTree } = twoR.data?.returnData || [];
          let menu = userMenuTree || [];
          storage.session.set('menuList', menu);
          // storage.local.set("operatorCode", this.ruleForm.operatorCode);
          storage.local.set('user', {
            ...this.ruleForm,
            password: ''
          });
          storage.session.set('user', {
            ...this.ruleForm,
            password: ''
          });
          this.M_gatewayToken(parames.token);
          this.A_getNavList(menu).then(() => {
            const redirect = storage.session.get('redirect');
            if (redirect) this.$router.push(redirect);
            else this.$router.replace('/');
            this.$message({
              message: '登录成功',
              type: 'success',
              duration: this.$config.messageTime,
              showClose: true
            });
          });
        });
      });
    }, 1000);
  }
};
</script>

<style lang="less" scoped></style>
