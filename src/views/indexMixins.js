import moment from 'moment';
export default {
  data() {
    return {
      patientListShow: false,
      patientListTheads: {
        sex: '性别',
        regNo: '体检号',
        name: '姓名',
        age: '年龄'
      },
      idxPatientList: [],
      idxPatientListCWidth: {
        regNo: 120,
        sex: 70,
        age: 60
      },
      searchIpt: '',
      dateArr: [
        moment().startOf('day').format('YYYY-MM-DD'),
        moment().startOf('day').format('YYYY-MM-DD')
      ],
      route: {
        personRegister: {
          path: '/Register/GetRegistersByMultipleFilter',
          data: {
            patCode: '',
            regNo: '',
            name: '',
            sex: -1,
            cardNo: '',
            peStatus: 0,
            queryType: 1,
            // startTime: this.dateArr[0],
            // endTime: this.dateArr[1],
            companyCode: '',
            companyTimes: -1,
            clusCode: '',
            companyDeptCode: '',
            isCompanyCheck: false,
            bookType: 0
          },
          dataModel: 'record'
        },
        groupRegister: {
          path: '/Register/GetRegistersByMultipleFilter',
          data: {
            patCode: '',
            regNo: '',
            name: '',
            sex: -1,
            cardNo: '',
            peStatus: 0,
            queryType: 2,
            // startTime: this.dateArr[0],
            // endTime: this.dateArr[1],
            companyCode: '',
            companyTimes: -1,
            clusCode: '',
            companyDeptCode: '',
            isCompanyCheck: true,
            bookType: 0
          },
          dataModel: 'record'
        },
        doctorWorkStation: {
          path: '/Record/GetPatientListByDoctor',
          data: {
            // operCode: this.G_userInfo.codeOper.operatorCode,
            companyCode: '',
            peCls: -1,
            // startTime: this.dateArr[0],
            // endTime: this.dateArr[1],
            keyWord: '',
            peStatus: 1
          },
          dataModel: ''
        },
        mainInspection: {
          path: '/ReportConclusion/GetPatientList4NotAllocate',
          data: {
            checkStatus: 1
            // operatorCode: this.G_userInfo.codeOper.operatorCode,
            // beginDate: this.dateArr[0],
            // endDate: this.dateArr[1]
          },
          dataModel: ''
        },
        process: {
          path: '/ReportConclusion/GetPatientList4NotAllocate',
          data: {
            checkStatus: 2
            // operatorCode: this.G_userInfo.codeOper.operatorCode,
            // beginDate: this.dateArr[0],
            // endDate: this.dateArr[1]
          },
          dataModel: ''
        },
        resultEntry: {
          path: '/Record/GetPatientList',
          data: {
            companyCode: '',
            peCls: -1,
            keyWord: '',
            peStatus: 1
          },
          dataModel: ''
        }
      }
    };
  },
  methods: {
    // 获取每个页面的用户队列
    getPatientList() {
      console.log(this.route, this.$route.name);
      let route = this.route[this.$route.name];
      console.log(route);
      this.idxPatientList = [];
      if (!route) {
        return;
      }
      let datas = {
        ...route.data,
        operCode: this.G_userInfo.codeOper.operatorCode,
        operatorCode: this.G_userInfo.codeOper.operatorCode,
        beginDate: this.dateArr[0],
        endDate: this.dateArr[1],
        startTime: this.dateArr[0],
        endTime: this.dateArr[1],
        keyWord: this.searchIpt
      };
      console.log(datas);
      this.$ajax
        .post(route.path, datas, {
          query: datas
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          if (!returnData) {
            this.idxPatientList = [];
            return;
          }
          this.idxPatientList = route.dataModel
            ? returnData[route.dataModel]
            : returnData;
        });
    },
    // 队列双击的回调
    patientListDbClick(row) {
      console.log(row);
      this.$refs.routerView_Ref.patientListDbClick(row);
      this.patientListShow = false;
    }
  }
};
