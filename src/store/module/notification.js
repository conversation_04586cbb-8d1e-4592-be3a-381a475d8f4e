import { ajax, apiUrls } from '../../common';
import loginStore from './login';

const state = {
  noticeList: [], //科内通知
  clinicList: [], //会诊记录
  bellList: [], //铃铛消息
  // 体检激活
  activeProgressShow: false,
  activeMsgData: {
    message: '',
    currentProgressPercentage: 0
  },
  // 导入
  CompanyImport: {
    message: '',
    currentProgressPercentage: 0
  },
  CompanyImportShow: false,
  // 团体批添加/删除
  BatchAddOrDeleteData: {
    message: '',
    currentProgressPercentage: 0
  },
  BatchAddOrDeleteShow: false
};

const getters = {
  G_noticeList(state) {
    return state.noticeList;
  },
  G_clinicList(state) {
    return state.clinicList;
  },
  G_bellList(state) {
    return state.bellList;
  },
  G_activeProgressShow(state) {
    return state.activeProgressShow;
  },
  G_activeMsgData(state) {
    return state.activeMsgData;
  },
  G_CompanyImport(state) {
    return state.CompanyImport;
  },
  G_CompanyImportShow(state) {
    return state.CompanyImportShow;
  },
  G_BatchAddOrDeleteData(state) {
    return state.BatchAddOrDeleteData;
  },
  G_BatchAddOrDeleteShow(state) {
    return state.BatchAddOrDeleteShow;
  }
};

const mutations = {
  // 获取公告
  M_getNoticeList() {},
  // 获取会诊记录
  M_getClinicList(state) {
    let datas = {
      operCode: loginStore.state.userInfo.codeOper.operatorCode,
      lastDays: 7
    };
    ajax.paramsPost(apiUrls.GetUserRecentMssage, datas).then((r) => {
      let { success, returnData } = r.data;
      if (!success) return;
      let noticeList = [];
      let clinicList = [];
      let bellList = [];
      returnData?.map((item) => {
        if (item.msgCode == 'Notice') {
          noticeList.push(item);
        } else {
          clinicList.push(item);
        }
        if (item.unReadMsgCount > 0) {
          bellList.push(item);
        }
      });
      state.noticeList = noticeList;
      state.clinicList = clinicList;
      state.bellList = bellList;
    });
  },
  M_activeProgressShow(state, obj) {
    state.activeMsgData = obj.msgData;
    state.activeProgressShow = obj.flag;
  },
  M_CompanyImport(state, obj) {
    state.CompanyImport = obj.msgData;
    state.CompanyImportShow = obj.flag;
  },
  M_BatchAddOrDeleteShow(state, obj) {
    state.BatchAddOrDeleteData = obj.msgData;
    state.BatchAddOrDeleteShow = obj.flag;
  }
};

const actions = {};

export default {
  state,
  getters,
  mutations,
  actions
};
