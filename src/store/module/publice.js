const state = {
  keepAlive: [],
  reloadFlag: true,
  config: {},
  otherBtn: [
    {
      label: '添加',
      code: 'add',
      api: []
    },
    {
      label: '修改',
      code: 'edit',
      api: []
    },
    {
      label: '删除',
      code: 'del',
      api: []
    },
    {
      label: '审核',
      code: 'audit',
      api: []
    },
    {
      label: '打印',
      code: 'print',
      api: []
    },
    {
      label: '签名',
      code: 'sign',
      api: []
    }
  ],
  sexList: [
    {
      value: 1,
      label: '男'
    },
    {
      value: 2,
      label: '女'
    }
  ],
  datePicker: {
    shortcuts: [
      {
        text: '今天',
        onClick(picker) {
          const end = new Date();
          end.setHours(23, 59, 59, 0);
          picker.$emit('pick', [new Date(), end]);
        }
      },
      {
        text: '昨天',
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24);
          end.setTime(end.getTime() - 3600 * 1000 * 24);
          end.setHours(23, 59, 59, 0);
          picker.$emit('pick', [start, end]);
        }
      },
      {
        text: '近3天',
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          end.setTime(start.getTime());
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 2);
          end.setHours(23, 59, 59, 0);
          picker.$emit('pick', [start, end]);
        }
      },
      {
        text: '近7天',
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          end.setTime(start.getTime());
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
          end.setHours(23, 59, 59, 0);
          picker.$emit('pick', [start, end]);
        }
      },
      {
        text: '近30天',
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          end.setTime(start.getTime());
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
          end.setHours(23, 59, 59, 0);
          picker.$emit('pick', [start, end]);
        }
      },
      {
        text: '近90天',
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          end.setTime(start.getTime());
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 89);
          end.setHours(23, 59, 59, 0);
          picker.$emit('pick', [start, end]);
        }
      },
      {
        text: '近180天',
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          end.setTime(start.getTime());
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 179);
          end.setHours(23, 59, 59, 0);
          picker.$emit('pick', [start, end]);
        }
      },
      {
        text: '近一年',
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          end.setTime(start.getTime());
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 364);
          end.setHours(23, 59, 59, 0);
          picker.$emit('pick', [start, end]);
        }
      },
      {
        text: '本周',
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          end.setTime(start.getTime());
          //现在星期几；0代表星期天，6代表星期六
          var thisDay = start.getDay();
          //现在是一个月的第几天
          var thisDate = start.getDate();
          if (thisDay != 0) {
            start.setDate(thisDate - thisDay + 1);
          }
          end.setHours(23, 59, 59, 0);
          picker.$emit('pick', [start, end]);
        }
      },
      {
        text: '本月',
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          end.setTime(start.getTime());
          start.setDate(1);
          end.setHours(23, 59, 59, 0);
          picker.$emit('pick', [start, end]);
        }
      },
      {
        text: '本季度',
        onClick(picker) {
          var oDate = new Date();
          var thisYear = oDate.getFullYear();
          var thisMonth = oDate.getMonth() + 1;
          var n = Math.ceil(thisMonth / 3); // 季度
          var Month = n * 3 - 1;
          var start = new Date(thisYear, Month - 2, 1);
          var end = new Date();
          end.setTime(end.getTime());
          end.setHours(23, 59, 59, 0);
          picker.$emit('pick', [start, end]);
        }
      },
      {
        text: '本年',
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setMonth(0);
          start.setDate(1);
          end.setTime(end.getTime());
          end.setHours(23, 59, 59, 0);
          picker.$emit('pick', [start, end]);
        }
      }
    ]
  }
};

const getters = {
  G_keepAlive(state) {
    let keepAlive = state.keepAlive.filter((item, idx) => {
      return item != 'home';
    });
    return keepAlive;
  },
  G_reloadFlag(state) {
    return state.reloadFlag;
  },
  G_otherBtn(state) {
    return state.otherBtn;
  },
  G_config(state) {
    return state.config;
  },
  G_datePickerShortcuts(state) {
    return state?.datePicker?.shortcuts || [];
  }
};

const mutations = {
  // 设置是否需要缓存页面
  M_setKeepAlive(state, keepAlive) {
    state.keepAlive = keepAlive;
  },
  M_reloadFlag(state, flag) {
    state.reloadFlag = flag;
  },
  M_config(state, config) {
    state.config = config;
    state.config.physicalGeneralMode =
      config?.physicalMode?.length == 1 &&
      config?.physicalMode?.includes('普检'); // 仅有普检模式
    state.config.physicalOccupationMode =
      config?.physicalMode?.length == 1 &&
      config?.physicalMode?.includes('职检'); // 仅有职检模式
  }
};

const actions = {};

export default {
  state,
  getters,
  mutations,
  actions
};
