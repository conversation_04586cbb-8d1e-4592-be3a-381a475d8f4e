import { storage } from '../../common';
//#region 解决http异步请求时，未能及时更新state数据，出现连续索引访问EnumList异常
// eg：dom中表格数据渲染时， G_EnumList["PeCls"][scope.row.peCls]
const _enumList = {};
// 自定义访问拦截器
var handler = {
  get: function (_enumList, prop) {
    let value = _enumList[prop];
    return value === undefined ? {} : value;
  }
};
// 创建代理
const enumList = new Proxy(_enumList, handler);
//#endregion

const state = {
  EnumList: {}, //常用枚举列表
  _enumList: enumList // 临时枚举列表
};

const getters = {
  G_EnumList(state) {
    return state._enumList;
  },
  // 无通用性别
  G_sexList(state) {
    return (state.EnumList['Sex'] || []).filter((x) => x.value != 0);
  },
  // 带通用性别
  G_shareSexList(state) {
    return state.EnumList['Sex'] || [];
  },
  //体检分类
  G_peClsList(state) {
    return state.EnumList['PeCls'] || [];
  },
  //婚姻状况
  G_marriageStatus(state) {
    return state.EnumList['MarryStatus'] || [];
  },
  //证件类型
  G_cardType() {
    return (state.EnumList['CardType'] || []).map((x) => {
      x.value = x.value.toString();
      return x;
    });
  },
  //预约类型
  G_bookType() {
    return state.EnumList['BookType'] || [];
  },
  //籍贯
  G_codeNativePlace() {
    return state.EnumList['CodeNativePlace'] || [];
  },
  //年龄单位
  G_ageUnit() {
    return state.EnumList['AgeUnit'] || [];
  },
  // 组合的检查分类
  G_checkCls() {
    return state.EnumList['CheckCls'] || [];
  },
  // 项目的结果类型
  G_valueType() {
    return state.EnumList['ValueType'] || [];
  },
  // 组合的检查限制
  G_checkLimit() {
    return state.EnumList['CheckLimit'] || [];
  },
  // 项目结果的异常类型
  G_abnormalType() {
    return state.EnumList['AbnormalType'] || [];
  },
  // 收费项目分类
  G_feeClsGroupType() {
    return state.EnumList['FeeClsGroupType'] || [];
  },
  // 指引单格式
  G_codeGuidanceType() {
    let list = [];
    let arr = state.EnumList['CodeGuidanceType'];
    if (!arr) return list;

    let userInfo = storage.session.get('userInfo');
    for (let i = 0; i < arr.length; i++) {
      let item = arr[i];
      let idx = item.label.indexOf('*') == -1;
      if (
        idx ||
        (item.label.indexOf('*') != -1 &&
          item.label.indexOf(userInfo.hospInfo.hospShortName) != -1)
      ) {
        list.push(item);
      }
    }
    return list;
  },
  // 报告单格式
  G_codeReportType() {
    let list = [];
    let arr = state.EnumList['CodeReportType'];
    if (!arr) return list;

    let userInfo = storage.session.get('userInfo');
    for (let i = 0; i < arr.length; i++) {
      let item = arr[i];
      let idx = item.label.indexOf('*') == -1;
      if (
        idx ||
        (item.label.indexOf('*') != -1 &&
          item.label.indexOf(userInfo.hospInfo.hospShortName) != -1)
      ) {
        list.push(item);
      }
    }
    return list;
  },
  // 职业病报告格式
  G_CodeOccupationalReportType() {
    let list = [];
    let arr = state.EnumList['CodeOccupationalReportType'];
    if (!arr) return list;

    let userInfo = storage.session.get('userInfo');
    for (let i = 0; i < arr.length; i++) {
      let item = arr[i];
      let idx = item.label.indexOf('*') == -1;
      if (
        idx ||
        (item.label.indexOf('*') != -1 &&
          item.label.indexOf(userInfo.hospInfo.hospShortName) != -1)
      ) {
        list.push(item);
      }
    }
    return list;
  },
  // 员工列表
  G_sysOperator() {
    return state.EnumList['SysOperator'] || [];
  },
  //科室列表
  G_codeDepartment() {
    return state.EnumList['CodeDepartment'] || [];
  },
  // 疾病等级
  G_diseaseGrade() {
    return state.EnumList['DiseaseGrade'] || [];
  },
  //体检状态
  G_peStatus() {
    return state.EnumList['PeStatus'] || [];
  },
  //项目分类
  G_codeItemCls() {
    return state.EnumList['CodeItemCls'] || [];
  },

  // 职业病-危害因素类别
  G_occupationalHazardousType() {
    return state.EnumList['HazardousType'] || [];
  },

  // 职业病-体检项目分类
  G_occupationalItemType() {
    return state.EnumList['OccupationalItemType'] || [];
  },

  // 职业病-企业规模
  G_occupationalEnterpriseSize() {
    return state.EnumList['OccupationalEnterpriseSize'] || [];
  },

  // 岗位状态
  G_CodeOccupationalPositionStatus() {
    return state.EnumList['CodeOccupationalPositionStatus'] || [];
  },
  // 危害因素分类
  G_HazardousTypeList() {
    return state.EnumList['HazardousType'] || [];
  },
  //起止进程
  G_ProcessType() {
    return state.EnumList['ProcessType'] || [];
  },
  // 固定常用角色类型：Doctor医生  Nurse护士  Audit审核医生
  G_constRoleType() {
    return state.EnumList['ConstRoleType'] || [];
  },

  // 组合扩展枚举：纯音测听、吸烟史等
  G_combExtension() {
    return state.EnumList['CombExtension'] || [];
  },

  // 吸烟史情况
  G_smokingStatus() {
    return state.EnumList['SmokingStatus'] || [];
  },
  // 回声情况
  G_EchoType() {
    return state.EnumList['EchoType'] || [];
  },
  // 检查情况
  G_MajorCheckType() {
    return state.EnumList['MajorCheckType'] || [];
  },
  // 随访来源
  G_FollowUpSource() {
    return state.EnumList['FollowUpSource'] || [];
  },
  // 重大阳性类型
  G_PositiveType() {
    return state.EnumList['PositiveType'] || [];
  }
};

const mutations = {
  M_EnumList(state, list) {
    state.EnumList = list;

    // 以下是常用枚举列表，可以根据实际情况进行扩展
    state.EnumList['ConstRoleType'] = [
      { label: 'Doctor', value: 'doctor' },
      { label: 'Nurse', value: 'nurse' },
      { label: 'Audit', value: 'audit' }
    ];

    if (typeof state.EnumList === 'object') {
      // 还原EnumList索引读取
      state._enumList = new Proxy(_enumList, handler);
      Object.keys(state.EnumList).forEach((key) => {
        let item = state.EnumList[key];
        if (Array.isArray(item)) {
          state._enumList[key] = state._enumList[key] || {};
          item.forEach((x) => {
            state._enumList[key][x.value] = x.label;
          });
        } else {
          state._enumList[key] = item;
        }
      });
    }
  }
};

const actions = {};

export default {
  state,
  getters,
  mutations,
  actions
};
