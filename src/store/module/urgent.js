import { ajax, apiUrls } from '../../common';

const state = {
  urgentList: [], //危急列表
  positiveList: [] //重大阳性列表
};

const getters = {
  G_urgentList(state) {
    return state.urgentList;
  },
  G_positiveList(state) {
    return state.positiveList;
  }
};

const mutations = {
  // 添加危急值
  M_addUrgentList(state, row) {
    state.urgentList.push(row);
  },
  // 删除危急值
  M_delUrgentList(state, id) {
    state.urgentList.some((item, idx, arr) => {
      if (item.id == id) {
        arr.splice(idx, 1);
      }
    });
  },
  // 添加重大阳性
  M_addPositiveList(state, row) {
    state.positiveList.push(row);
  },
  // 删除重大阳性
  M_delPositiveList(state, id) {
    state.positiveList.some((item, idx, arr) => {
      if (item.id == id) {
        arr.splice(idx, 1);
      }
    });
  }
};

const actions = {
  // 获取危急值列表
  A_getUrgentList(context) {
    return new Promise((resolve, reject) => {
      ajax.post(apiUrls.GetAllUntreatedCriticalValue).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        returnData?.map((item) => {
          item.popoverVisible = false;
        });
        context.state.urgentList = returnData;
        resolve(returnData || []);
      });
    });
  },
  // 获取重大阳性列表
  A_GetMajorPositiveDetailToDos(context) {
    return new Promise((resolve, reject) => {
      ajax
        .post(apiUrls.GetMajorPositiveDetailToDos, { keyword: '' })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          returnData?.map((item) => {
            item.popoverVisible = false;
          });
          context.state.positiveList = returnData;
          resolve(returnData || []);
        });
    });
  }
};

export default {
  state,
  getters,
  mutations,
  actions
};
