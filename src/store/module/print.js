/*
 * @FilePath: \KrPeis\src\store\module\print.js
 * @Description:
 * @Version: 2.0
 * @Author: key
 * @Date: 2024-07-25 13:29:26
 * @LastEditors: key
 * @LastEditTime: 2024-07-25 13:59:15
 */
const state = {
  printerList: [],
  printerState: 3 // 1表示已连接，3表示连接断开
};
const getters = {
  G_printerList(state) {
    return state.printerList;
  },
  G_printerState(state) {
    return state.printerState;
  }
};

const mutations = {
  M_printerList(state, data) {
    state.printerList = data;
  },
  M_printerState(state, num) {
    state.printerState = num;
  }
};

const actions = {};

export default {
  state,
  getters,
  mutations,
  actions
};
