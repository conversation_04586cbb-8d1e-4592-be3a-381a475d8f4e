import { ajax, apiUrls } from '../../common';
import loginStore from './login';

const state = {
  chatRoomList: [], //我发的会诊列表
  chatReceiveList: [], //我收到的会诊列表
  resData: [], //响应会诊聊天记录列表
  rowRes: {} //当前打开的聊天室
};

const getters = {
  G_resData(state) {
    return state.resData;
  },
  G_rowRes(state) {
    return state.rowRes;
  },
  G_chatRoomList(state) {
    return state.chatRoomList;
  },
  G_chatReceiveList(state) {
    return state.chatReceiveList;
  }
};

const mutations = {
  // 获取会诊列表
  M_chatRoomList(state) {
    // return;
    ajax
      .paramsPost(apiUrls.ReadDoctorChat, {
        operCode: loginStore.state.userInfo.codeOper.operatorCode
      })
      .then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        state.chatRoomList = returnData.myChatRecord;
        state.chatReceiveList = returnData.otherChatRecord;
      });
  },
  // 获取响应会诊聊天记录列表
  M_getReadDoctorChatReply(state, data) {
    state.resData = [];
    ajax.post(apiUrls.ReadDoctorChatReply, data).then((r) => {
      let { success, returnData } = r.data;
      if (!success) return;
      state.resData = returnData;
    });
  },
  // 前面插入响应会诊聊天记录
  M_resData(state, data) {
    state.resData.unshift(data);
  },
  // 获取当前打开的聊天室
  M_rowRes(state, data) {
    state.rowRes = data;
  },
  // 前面插入会诊
  M_addChatRoomList(state, data) {
    state.chatRoomList.unshift(data);
  },
  // 前面插入别人发的会诊
  M_chatReceiveList(state, data) {
    state.chatReceiveList.unshift(data);
  }
};

const actions = {};

export default {
  state,
  getters,
  mutations,
  actions
};
