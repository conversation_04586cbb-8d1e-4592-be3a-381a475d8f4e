import { storage, ajax, apiUrls } from '../../common';
import router, { resetRouter } from '../../router';
import HandleNav from '../../common/handleNav';
import Vue from 'vue';
//要设置的全局访问的state对象
const state = {
  navList: [],
  userInfo: {},
  leftMenuList: [], //左边菜单列表
  btnAuthList: [], //按钮权限列表
  gatewayToken: '', // 门户token
  headShow: true, //是否显示头部
  loginDialogShow: false,
  parentObj: {} //获取页面父级的对应信息
};
//实时监听state值的变化(最新状态)
const getters = {
  // 获取用户的基本信息
  getterNavList(state) {
    return state.navList;
  },
  G_userInfo(state) {
    return state.userInfo;
  },
  G_leftMenuList(state) {
    return state.leftMenuList;
  },
  G_btnAuthList(state) {
    return state.btnAuthList;
  },
  G_gatewayToken(state) {
    return state.gatewayToken;
  },
  G_headShow(state) {
    return state.headShow;
  },
  G_loginDialogShow(state) {
    return state.loginDialogShow;
  }
};
//同步
const mutations = {
  M_getNavList(state, list) {
    resetRouter(); //重置路由
    state.navList = list;
    state.parentObj = {};
    state.navList.forEach((item) => {
      state.parentObj[item.menuCode] = item.title;
    });
    var items = [];
    var defaultPage = '';
    HandleNav(state.navList, items);
    items = items.map((item, index) => {
      if (index == 0) {
        defaultPage = item.name;
      }
      return (item = {
        path: item.path,
        name: item.name,
        component: () => import(`../../pages${item.path}`),
        meta: {
          title: item.title,
          keepAlive: true,
          auth: true,
          other: item.other,
          parentCode: item.parentCode,
          parentName: state.parentObj[item.parentCode]
        }
      });
    });
    state.leftMenuList = items;
    var routesArrs = [
      {
        path: '/',
        name: 'index',
        redirect: `/${defaultPage}`,
        component: () => import('../../views/index'),
        children: [
          {
            path: '/',
            name: 'home',
            component: () => import(`../../pages/home/<USER>
            meta: {
              title: '首页',
              keepAlive: true,
              auth: true
            }
          },
          ...items,
          {
            path: '/inspectIn/OPOrdering',
            name: 'OPOrdering',
            component: () => import(`../../pages/inspectIn/OPOrdering`),
            meta: {
              title: '门诊医生工作站',
              keepAlive: true,
              auth: true
            }
          }
        ],
        meta: {
          Auth: true
        }
      }
    ];
    if ((!state.navList) instanceof Array) return;

    router.addRoute(...routesArrs);
  },
  //  退出登录重置路由，避免添加重复的路由
  M_logout() {
    storage.session.clear();
    resetRouter(); //重置路由
    router.push('/login');
    Vue.prototype.$ws && Vue.prototype.$ws.closeWebSocket();
  },
  // 登录后设置保存登录用户的基本信息
  M_userInfo(state, userInfo) {
    storage.session.set('userInfo', userInfo);
    state.userInfo = userInfo;
  },
  // 处理页面的按钮权限
  M_btnAuthList(state, btnList = []) {
    let btnArr = [];
    btnList.map((item) => {
      btnArr.push(item.code);
    });
    state.btnAuthList = btnArr;
  },
  // 设置门户的token
  M_gatewayToken(state, token) {
    state.gatewayToken = token;
  },
  // 设置门户的token
  M_headShow(state, flag) {
    storage.session.set('headShow', flag);
    state.headShow = flag;
  },
  // 原地登录的显示
  M_loginDialogShow(state, flag) {
    state.loginDialogShow = flag;
  }
};
// 异步
const actions = {
  A_getNavList(context, list) {
    return new Promise((resolve, reject) => {
      context.commit('M_getNavList', list);
      if (context.state.navList instanceof Array) {
        resolve(context.state.navList);
      } else {
        reject('动态路由必须是数组');
      }
    });
  },
  A_logout(context) {
    return new Promise((resolve, reject) => {
      context.commit('M_logout');
      resolve('重置路由成功');
    });
  },
  // 获取角色菜单列表
  A_getMenuList(context, operCode) {
    return ajax.post(apiUrls.GetUserMenus, '', {
      query: { operatorCode: operCode }
    });
  }
};
export default {
  state,
  getters,
  mutations,
  actions
};
