import Vue from 'vue';
import Vuex from 'vuex';
import { storage } from '@/common/storage';

Vue.use(Vuex);

const modulesFiles = require.context('./module', true, /\.js$/);

const modules = modulesFiles.keys().reduce((modules, modulePath) => {
  // set './app.js' => 'app'
  const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1');
  const value = modulesFiles(modulePath);
  modules[moduleName] = value.default;
  return modules;
}, {});

// 循环获取浏览器缓存赋值
for (let a in modules) {
  let oneModule = modules[a];
  for (let item in oneModule.state) {
    let oneItem = oneModule.state[item];
    oneModule.state[item] =
      storage.session.get(item) || storage.session.get(item) === false
        ? storage.session.get(item)
        : oneItem;
  }
}

export default new Vuex.Store({
  modules
});
