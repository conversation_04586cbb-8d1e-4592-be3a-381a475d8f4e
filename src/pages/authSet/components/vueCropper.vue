<template>
  <div class="vue_cropper" style="padding: 10px; overflow: auto">
    <div class="preview_img" style="display: flex; overflow: auto">
      <div
        class="preview"
        style="
          margin-right: 10px;
          overflow: hidden;
          width: 180px;
          height: 100px;
          border: 1px solid #ccc;
        "
      >
        <el-image style="width: 100%; height: 100%" :src="photoUrl"></el-image>
      </div>
    </div>
    <div class="preview_btn" style="margin-bottom: 6px">
      <el-button
        size="small"
        class="blue_btn"
        icon="iconfont icon-baocun"
        @click="confirmClip('blob')"
        >确定</el-button
      >
      <el-button size="small" @click="cancel">取消</el-button>
    </div>
    <div class="cropper">
      <vue-cropper
        ref="cropper"
        :img="option.img"
        :outputSize="option.outputSize"
        :outputType="option.outputType"
        :info="option.info"
        :canScale="option.canScale"
        :autoCrop="option.autoCrop"
        :autoCropWidth="option.autoCropWidth"
        :autoCropHeight="option.autoCropHeight"
        :fixed="option.fixed"
        :fixedNumber="option.fixedNumber"
        :full="option.full"
        :fixedBox="option.fixedBox"
        :canMove="option.canMove"
        :canMoveBox="option.canMoveBox"
        :original="option.original"
        :centerBox="option.centerBox"
        :height="option.height"
        :infoTrue="option.infoTrue"
        :maxImgSize="option.maxImgSize"
        :enlarge="option.enlarge"
        :mode="option.mode"
        @realTime="realTime"
        @imgLoad="imgLoad"
      >
      </vue-cropper>
    </div>
    <!--底部操作工具按钮-->
    <div class="footer-btn" style="margin-top: 10px">
      <div class="scope-btn">
        <label class="up_label blue_btn" for="uploads">
          <i class="el-icon-upload"></i> 上传本地图片
        </label>
        <input
          type="file"
          id="uploads"
          ref="img_file"
          style="position: absolute; clip: rect(0 0 0 0)"
          accept="image/png, image/jpeg, image/gif, image/jpg"
          @change="selectImg($event)"
        />
        <el-button
          size="mini"
          type="danger"
          plain
          icon="el-icon-zoom-in"
          @click="changeScale(1)"
          >放大</el-button
        >
        <el-button
          size="mini"
          type="danger"
          plain
          icon="el-icon-zoom-out"
          @click="changeScale(-1)"
          >缩小</el-button
        >
        <el-button size="mini" type="danger" plain @click="rotateLeft"
          >↺ 左旋转</el-button
        >
        <el-button size="mini" type="danger" plain @click="rotateRight"
          >↻ 右旋转</el-button
        >
      </div>
    </div>
  </div>
</template>

<script>
import { VueCropper } from 'vue-cropper';
export default {
  name: 'vueCroppers',
  components: {
    VueCropper
  },
  data() {
    return {
      previews: {},
      photoUrl: '',
      option: {
        img: '', //裁剪图片的地址
        outputSize: 1, //裁剪生成图片的质量(可选0.1 - 1)
        outputType: 'png', //裁剪生成图片的格式（jpeg || png || webp）
        info: true, //图片大小信息
        canScale: true, //图片是否允许滚轮缩放
        autoCrop: true, //是否默认生成截图框
        autoCropWidth: 180, //默认生成截图框宽度
        autoCropHeight: 100, //默认生成截图框高度
        fixed: true, //是否开启截图框宽高固定比例
        fixedNumber: [1.8, 1], //截图框的宽高比例
        full: false, //false按原比例裁切图片，不失真
        fixedBox: false, //固定截图框大小，不允许改变
        canMove: true, //上传图片是否可以移动
        canMoveBox: true, //截图框能否拖动
        original: true, //上传图片按照原始比例渲染
        centerBox: true, //截图框是否被限制在图片里面
        height: true, //是否按照设备的dpr 输出等比例图片
        infoTrue: true, //true为展示真实输出图片宽高，false展示看到的截图框宽高
        maxImgSize: 3000, //限制图片最大宽度和高度
        enlarge: 1, //图片根据截图框输出比例倍数
        mode: 'auto auto' //图片默认渲染方式
      },
      fileData: ''
    };
  },
  methods: {
    // 确定裁剪图片
    confirmClip(type) {
      this.clip(type).then((r) => {
        this.$refs.img_file.value = '';
        this.option.img = '';
        this.photoUrl = '';
        this.$emit('confirmClip', r);
      });
    },
    // 裁剪
    clip(type) {
      return new Promise((resolve, reject) => {
        this.$nextTick(() => {
          // 输出
          if (type === 'blob') {
            this.$refs.cropper.getCropBlob((data) => {
              this.fileData = data;
              var img = window.URL.createObjectURL(data);
              this.photoUrl = img;
              resolve({
                data,
                photoUrl: img
              });
            });
          } else {
            this.$refs.cropper.getCropData((data) => {
              console.log(data);
              this.photoUrl = data;
              resolve({
                data
              });
            });
          }
        });
      });
    },
    // 上传图片
    uploadImg() {
      if (this.fileData == '') return Promise.resolve(); // 如果没有文件，直接返回一个解决的 Promise
      let formData = new FormData();
      var file = new File([this.fileData], `${new Date()}.png`, {
        type: this.fileData.type,
        lastModified: Date.now()
      });
      console.log(file.name);
      formData.append('files', file);
      console.log(formData);

      return this.$ajax
        .post(this.$apiUrls.UploadPhotoNew, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: ''
          }
        })
        .then((r) => {
          this.headPortraitImg = r?.data?.returnData;
          this.fileData = '';
        });
    },
    //初始化函数
    imgLoad(msg) {
      console.log('工具初始化函数=====' + msg);
    },
    //图片缩放
    changeScale(num) {
      num = num || 1;
      this.$refs.cropper.changeScale(num);
    },
    //向左旋转
    rotateLeft() {
      this.$refs.cropper.rotateLeft();
    },
    //向右旋转
    rotateRight() {
      this.$refs.cropper.rotateRight();
    },
    //实时预览函数
    realTime(data) {
      // console.log(data);
      // let previews = {
      //   ...data,
      //   photoUrl: data.url,
      // };
      // delete previews.url;
      // this.previews = previews;
      this.clip('blob');
    },
    //选择图片
    selectImg(e) {
      let file = e.target.files[0];
      if (!/\.(jpg|jpeg|png|JPG|PNG)$/.test(e.target.value)) {
        this.$message({
          message: '图片类型要求：jpeg、jpg、png',
          type: 'error'
        });
        return false;
      }
      //转化为blob
      let reader = new FileReader();
      reader.onload = (e) => {
        let data;
        if (typeof e.target.result === 'object') {
          data = window.URL.createObjectURL(new Blob([e.target.result]));
        } else {
          data = e.target.result;
        }
        this.option.img = data;
      };
      //转化为base64
      reader.readAsDataURL(file);
    },
    // 取消
    cancel() {
      this.$emit('cancel');
    }
  }
};
</script>

<style lang="less" scoped>
.vue_cropper {
  .cropper {
    width: 100%;
    height: 300px;
  }
  .up_label {
    border: 1px solid #ccc;
    font-size: 12px;
    border-radius: 3px;
    line-height: 26px;
    padding: 0 15px;
    display: inline-block;
    vertical-align: middle;
    cursor: pointer;
    margin-right: 10px;
  }
}
</style>
