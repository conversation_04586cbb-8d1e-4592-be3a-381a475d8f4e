<template>
  <div class="roleManagePage">
    <header>
      <el-button type="primary" size="small" @click="addRoleBtn"
        >添加角色</el-button
      >
      <div class="searchWrap">
        <el-input
          v-model.trim="searchVal"
          style="width: 300px"
          placeholder="请输入内容"
          size="small"
        ></el-input>
        <el-button
          type="primary"
          size="small"
          style="margin-left: 10px"
          icon="el-icon-search"
          >查询</el-button
        >
      </div>
    </header>
    <!-- 角色列表 -->
    <div class="table-wrap">
      <el-table
        class="elTabel"
        :data="roleList"
        style="width: 100%"
        size="small"
        height="100%"
        border
      >
        <el-table-column type="index" label="序号" width="80"></el-table-column>
        <el-table-column prop="name" label="角色名称"></el-table-column>
        <el-table-column prop="note" label="备注"></el-table-column>
        <el-table-column label="操作" width="300">
          <template slot-scope="scope">
            <div>
              <el-button size="mini" plain @click="setRoleMenuBtn(scope.row)"
                >设置菜单权限</el-button
              >
              <el-button
                type="primary"
                size="mini"
                plain
                @click="editRoleBtn(scope.row)"
                >编辑</el-button
              >
              <el-button
                type="danger"
                size="mini"
                plain
                @click="deleteRoleBtn(scope.row)"
                >删除</el-button
              >
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 添加角色弹窗 -->
    <el-dialog
      :title="`${isAdd ? '添加' : '编辑'}角色`"
      :visible.sync="dialogFormVisible"
    >
      <el-form :model="form" :rules="rules" ref="ruleForm">
        <el-form-item
          v-if="!isAdd"
          label="角色代码"
          :label-width="'100px'"
          prop="roleCode"
        >
          <el-input
            v-model.trim="form.roleCode"
            :disabled="!isAdd"
            autocomplete="off"
            maxlength="20"
            size="small"
          ></el-input>
        </el-form-item>
        <el-form-item label="名称" :label-width="'100px'" prop="name">
          <el-input
            v-model.trim="form.name"
            maxlength="15"
            autocomplete="off"
            size="small"
          ></el-input>
        </el-form-item>
        <el-form-item label="备注" :label-width="'100px'" prop="note">
          <el-input
            v-model.trim="form.note"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
            autocomplete="off"
            size="small"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelSave" size="small">取 消</el-button>
        <el-button type="primary" @click="roleSave" size="small"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <!-- 设置菜单权限弹窗 -->
    <el-dialog title="设置角色菜单权限" :visible.sync="roleDialogShow">
      <div class="roleTbody">
        <el-tree
          style="width: 100%"
          ref="roleMenuTreeRef"
          :data="menuList"
          show-checkbox
          node-key="menuCode"
          default-expand-all
          :props="defaultProps"
          @check="treeBoxClick"
          @check-change="treeCheck"
        >
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <span>{{ node.label }}</span>
            <!-- <span v-if="!data.children" style="display: flex"> -->
            <!-- 其他功能按钮 -->
            <!-- <span style="display: flex"> -->
            <div>
              <el-checkbox-group
                v-model.trim="data.checkOther"
                size="mini"
                style="line-height: 45px"
                @change="checkBoxGroupChange(node, data)"
              >
                <el-checkbox
                  :label="item.code"
                  v-for="item in data.other"
                  :key="item.code"
                  border
                >
                  {{ item.label }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
            <!-- </span> -->
          </span>
        </el-tree>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="roleDialogShow = false" size="small"
          >取 消</el-button
        >
        <el-button type="primary" size="small" @click="confirmSubmit"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  computed: {
    ...mapGetters(['G_userInfo'])
  },
  data() {
    return {
      searchVal: '',
      roleList: [],
      form: {
        roleCode: '',
        name: '',
        note: ''
      },
      dialogFormVisible: false,
      rules: {
        roleCode: [
          { required: true, message: '请输入角色代码', trigger: 'blur' }
        ],
        name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }]
      },
      editRole: {},
      isAdd: true, //true表示添加，false表示修改；
      roleDialogShow: false,
      fixed_menuList: [],
      menuList: [],
      defaultProps: {
        children: 'children',
        label: 'title'
      },
      roleAuthList: [],
      roleCheckOther: {},
      rowMenuCode: '',
      isBtnClick: false //判断是否按钮选择
    };
  },
  methods: {
    treeCheck(data, thisFlag, thatFlag) {
      this.$nextTick(() => {
        // console.log(data);
        if (data.other.length == 0 || this.isBtnClick) return;
        if (thisFlag) {
          data.other.map((item) => {
            if (!data.checkOther.includes(item.code)) {
              data.checkOther.push(item.code);
            }
          });
        } else {
          data.checkOther = [];
        }
      });
    },
    treeBoxClick() {
      this.isBtnClick = false;
    },
    // 获取角色列表
    getAllRoleList() {
      this.$ajax.post(this.$apiUrls.GetAllRole).then((r) => {
        let { returnData, success } = r.data;
        if (!success) {
          return;
        }
        this.roleList = returnData || [];
      });
    },
    // 添加角色按钮点击事件
    addRoleBtn() {
      this.isAdd = true;
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs.ruleForm.resetFields();
      });
    },
    // 编辑按钮点击事件
    editRoleBtn(row) {
      console.log(row);

      this.isAdd = false;
      this.dialogFormVisible = true;
      this.editRole = row;
      this.$nextTick(() => {
        this.$refs.ruleForm.resetFields();
        this.form = row;
      });
    },
    // 添加角色的提交
    addSubmit() {
      //   this.roleList.push({ ...this.form });
      console.log(this.form);
      // this.editRole.name = this.form.name;
      // this.editRole.note = this.form.note;
      var editRole = {
        roleCode: this.form.roleCode,
        name: this.form.name,
        note: this.form.note,
        status: 0
      };
      this.$ajax.post(this.$apiUrls.AddRole, editRole).then((r) => {
        this.getAllRoleList();
        this.$message({
          message: '添加成功',
          type: 'success'
        });
      });
    },
    // 编辑角色的提交
    editSubmit() {
      this.editRole.name = this.form.name;
      this.editRole.note = this.form.note;
      console.log(this.editRole);
      this.$ajax.post(this.$apiUrls.UpdateRole, this.editRole).then((r) => {
        this.getAllRoleList();
        this.$message({
          message: '修改成功',
          type: 'success'
        });
      });
    },
    // 取消角色的保存
    cancelSave() {
      this.dialogFormVisible = false;
      this.getAllRoleList();
    },
    // 添加和编辑角色的保存
    roleSave() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.dialogFormVisible = false;
          if (this.isAdd) {
            this.addSubmit();
            return;
          }
          this.editSubmit();
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    // 删除角色信息
    deleteRoleBtn(row) {
      console.log(row.roleCode);
      this.$confirm(`是否删除${row.name}角色?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax
            .post(this.$apiUrls.DeleteRoles, '', {
              query: {
                roleCode: row.roleCode.toString()
              }
            })
            .then((r) => {
              this.getAllRoleList();
              this.$message({
                message: '删除成功',
                type: 'success'
              });
            });
        })
        .catch(() => {});

      // this.menuList = this.G_userInfo.menu;
      // this.$ajax.post(this.$apiUrls.GetAllMenu).then((r) => {
      //
      //     let { returnData } = r.data;
      //     this.fixed_menuList = returnData
      // })
    },
    // 设置菜单权限按钮
    setRoleMenuBtn(row) {
      console.log(row);
      this.rowMenuCode = row.roleCode;
      this.menuList = JSON.parse(JSON.stringify(this.fixed_menuList));
      this.roleCheckOther = {};
      let setCheckNodes = [];
      this.$ajax
        .post(this.$apiUrls.GetRoleMenu, '', {
          query: { roleCode: row.roleCode }
        })
        .then((r) => {
          console.log(r.data.returnData);
          let { returnData } = r.data;
          this.roleAuthList = returnData || [];
          this.roleAuthList.map((item) => {
            this.disposeRoleMenu(item);
            setCheckNodes.push(...item.children);
          });
          console.log(this.roleCheckOther);
          this.menuList.map((item) => {
            this.disposeMenu(item);
          });
          this.$nextTick(() => {
            this.$refs.roleMenuTreeRef.setCheckedNodes(setCheckNodes);
          });
        });
      // this.$ajax.post(this.$apiUrls.getRoleAuth).then((r) => {
      //
      //   let { returnData } = r.data;
      //   this.roleAuthList = returnData;
      //   this.roleAuthList.map((item) => {
      //     this.disposeRoleMenu(item);
      //   });
      //   this.menuList.map((item) => {
      //     this.disposeMenu(item);
      //   });
      //   console.log(this.menuList);
      //   this.$nextTick(() => {
      //     this.$refs.roleMenuTreeRef.setCheckedNodes(returnData);
      //   });
      // });

      this.roleDialogShow = true;
    },

    // 设置角色菜单权限的提交
    confirmSubmit() {
      console.log(this.$refs.roleMenuTreeRef.getCheckedNodes(true));
      let checkAuthList = JSON.parse(
        JSON.stringify(this.$refs.roleMenuTreeRef.getCheckedNodes(true))
      );
      checkAuthList.map((item) => {
        let otherArr = [];
        item.other.map((twoItem) => {
          if (item.checkOther.includes(twoItem.code)) {
            otherArr.push(twoItem);
          }
        });

        item.other = otherArr;
      });
      console.log(checkAuthList);
      var editRole = [];
      for (var i = 0; i < checkAuthList.length; i++) {
        for (var k = 0; k < checkAuthList[i].other.length; k++) {
          editRole.push({
            roleCode: this.rowMenuCode,
            menuCode: checkAuthList[i].menuCode,
            funcCode: checkAuthList[i].other[k].code
          });
        }
      }
      console.log(editRole);

      this.$ajax
        .post(this.$apiUrls.SetRolePermissions, editRole, {
          query: { roleCode: this.rowMenuCode }
        })
        .then((r) => {
          this.roleDialogShow = false;
          this.$message({
            message: '设置成功',
            type: 'success'
          });
        });
    },
    // 处理角色菜单的递归函数
    disposeRoleMenu(child) {
      if (!child.children) return;
      child.children.map((item) => {
        if (!item.children || item.children.length === 0) {
          let checkOther = [];
          item.other.map((twoItem) => {
            checkOther.push(twoItem.code);
          });
          item.checkOther = checkOther;
          this.roleCheckOther[item.menuCode] = checkOther;
          return;
        }
        this.disposeRoleMenu(item);
      });
    },
    // 处理菜单列表功能按钮选中的递归函数
    disposeMenu(child) {
      if (!child.children) return;
      child.children.map((item) => {
        // console.log(item);
        item.checkOther = [];
        if (!item.children || item.children.length === 0) {
          let checkOther = [];
          checkOther = this.roleCheckOther['' + item.menuCode];
          if (!checkOther) return;
          item.checkOther = [...this.roleCheckOther['' + item.menuCode]];
          return;
        }
        this.disposeMenu(item.children);
      });
    },
    // 选中功能按钮关联树形菜单的选中
    checkBoxGroupChange(node, data) {
      this.isBtnClick = true;
      // console.log(node, data);
      let checkLength = data.checkOther.length;
      let otherLength = data.other.length;
      if (checkLength == otherLength) {
        node.checked = true;
        node.indeterminate = false;
      }
      if (checkLength < otherLength && checkLength > 0) {
        node.checked = false;
        node.indeterminate = true;
      }
      if (checkLength == 0) {
        node.checked = false;
        node.indeterminate = false;
      }
      // 最外层的选中
      let checkedTimes = 0;
      let indeterminateTimes = 0;
      node.parent.childNodes.map((item) => {
        if (item.checked) {
          checkedTimes += 1;
        }
        if (item.indeterminate) {
          indeterminateTimes += 1;
        }
      });
      if (checkedTimes == node.parent.childNodes.length) {
        node.parent.checked = true;
        node.parent.indeterminate = false;
        return;
      }
      if (indeterminateTimes > 0 || checkedTimes > 0) {
        node.parent.checked = false;
        node.parent.indeterminate = true;
      }
    }
  },
  created() {
    this.getAllRoleList();
    this.menuList = this.G_userInfo.menu;
    // this.$ajax.post(this.$apiUrls.getAllMenuList).then((r) => {
    //
    //   let { returnData } = r.data;
    //   this.fixed_menuList = returnData;
    // });
    this.$ajax.post(this.$apiUrls.GetAllMenu).then((r) => {
      let { returnData } = r.data;
      this.fixed_menuList = returnData;
    });
  }
};
</script>
<style lang="less" scoped>
.roleManagePage {
  display: flex;
  flex-direction: column;
  header {
    display: flex;
    justify-content: space-between;
    height: 60px;
  }

  .roleTbody {
    max-height: 500px;
    overflow: scroll;
  }

  .custom-tree-node {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
  }
  .el-button--small {
    height: 36px;
  }
  // /deep/.el-dialog {
  //   margin-top: 20px !important;
  // }
  .table-wrap {
    flex: 1;
    overflow: auto;
  }
}
</style>
<style>
.el-tree-node__content {
  height: 100% !important;
  align-items: initial !important;
}
</style>
