<template>
  <div class="menuManagePage">
    <header>
      <el-button type="primary" size="small" @click="addMenuBtn"
        >添加根目录</el-button
      >
    </header>
    <div class="cont">
      <el-table
        class="elTabel"
        :data="menuList"
        style="width: 100%"
        row-key="menuCode"
        size="small"
        border
        default-expand-all
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column prop="title" label="菜单名称" sortable width="260">
        </el-table-column>
        <el-table-column prop="sortIndex" label="排序" sortable>
        </el-table-column>
        <el-table-column prop="other" label="功能按钮" sortable>
          <template slot-scope="scope">
            <span v-for="(item, i) in scope.row.other" :key="i">
              {{ item.label }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <div>
              <el-button
                size="mini"
                plain
                @click="addItemBtn(scope.row)"
                v-if="scope.row.parentCode == 0"
                >添加子菜单</el-button
              >
              <el-button
                type="primary"
                size="mini"
                v-if="scope.row.parentCode == 0"
                @click="editMenuBtn(2, scope.row)"
                plain
                >编辑</el-button
              >
              <el-button
                type="primary"
                size="mini"
                v-else
                @click="editMenuBtn(4, scope.row)"
                plain
                >编辑</el-button
              >
              <el-button
                type="danger"
                size="mini"
                plain
                @click="DeleteMenuBtn(scope.row)"
                >删除</el-button
              >
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 添加和修改根目录弹窗 -->
    <el-dialog :title="stateEnum[state]" :visible.sync="rootDialogShow">
      <el-form
        :model="rootForm"
        :rules="rootRules"
        ref="rootForm"
        :label-width="'100px'"
      >
        <el-form-item label="名称" prop="title">
          <el-input
            v-model.trim="rootForm.title"
            autocomplete="off"
            maxlength="50"
            size="small"
          ></el-input>
        </el-form-item>
        <el-form-item label="菜单编码" prop="menuCode">
          <el-input
            v-model.trim="rootForm.menuCode"
            maxlength="50"
            :disabled="state == 2 || state == 4"
            autocomplete="off"
            size="small"
          ></el-input>
        </el-form-item>
        <el-form-item label="name" prop="name">
          <el-input
            v-model.trim="rootForm.name"
            maxlength="50"
            autocomplete="off"
            size="small"
          ></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="sortIndex">
          <el-input
            v-model.trim="rootForm.sortIndex"
            maxlength="3"
            autocomplete="off"
            size="small"
          ></el-input>
        </el-form-item>
        <el-form-item label="路径" prop="path">
          <!-- v-if="!rootForm.children && state != 1" -->
          <el-input
            v-model.trim="rootForm.path"
            autocomplete="off"
            size="small"
          ></el-input>
        </el-form-item>
        <!-- <el-form-item label="图标" >
                    <el-input v-model.trim="rootForm.icon" autocomplete="off" size="small"></el-input>
                </el-form-item> -->
        <el-form-item label="功能" v-if="state == 4">
          <el-button
            type="success"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="innerVisible = true"
            >编辑功能</el-button
          >
          <div class="funBtn_wrap" v-for="(item, i) in btnList" :key="i">
            {{ item.data.funName }}

            <!-- <el-checkbox-group v-model.trim="rootForm.checkOther" size="mini">
            <el-checkbox
              :label="item.apiCode"
              v-for="item in btnList"
              :key="item.apiCode"
            >
              {{ item.funName }} -->
            <!-- <el-select v-model.trim="item.api" size="small" multiple filterable remote reserve-keyword
                placeholder="请输入关键词" :remote-method="remoteMethod" :loading="loading">
                <el-option 
                    v-for="apiOpt in apiOptions" 
                    :key="apiOpt.api" 
                    :label="apiOpt.label"
                    :value="apiOpt.api"
                    class="elOption"
                    @click.stop
                >
                  <span style="float: left">{{ apiOpt.label }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ apiOpt.api }}</span>
                </el-option>
            </el-select> -->
            <el-select
              v-model.trim="item.api"
              clearable
              filterable
              multiple
              placeholder="请选择接口"
            >
              <el-option
                v-for="apiOpt in apiList"
                :key="apiOpt.apiCode"
                :label="apiOpt.name"
                :value="apiOpt.apiCode"
                class="elOption"
                @click.stop
              >
                <span style="float: left">{{ apiOpt.name }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{
                  apiOpt.path
                }}</span>
              </el-option>
            </el-select>
            <i
              class="icon el-icon-delete del_funBtn"
              @click="delFunBtn(item)"
            ></i>
            <!-- </el-checkbox>
          </el-checkbox-group> -->
          </div>
        </el-form-item>
      </el-form>
      <el-dialog
        width="30%"
        title="添加功能"
        :visible.sync="innerVisible"
        append-to-body
      >
        <el-form
          :model="funForm"
          :rules="funFormRules"
          ref="rootForm"
          :label-width="'100px'"
        >
          <el-form-item label="功能名称" prop="name">
            <el-input
              v-model.trim="funForm.name"
              maxlength="15"
              autocomplete="off"
              size="small"
            ></el-input>
          </el-form-item>
          <el-form-item label="功能编码" prop="funcCode">
            <el-input
              v-model.trim="funForm.funcCode"
              maxlength="50"
              autocomplete="off"
              size="small"
            ></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="note">
            <el-input
              v-model.trim="funForm.note"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4 }"
              autocomplete="off"
              size="small"
            ></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="innerVisible = false" size="small"
            >取 消</el-button
          >
          <el-button type="primary" size="small" @click="addFunctionBtn"
            >确 定</el-button
          >
        </div>
      </el-dialog>
      <div slot="footer" class="dialog-footer">
        <el-button @click="rootDialogShow = false" size="small"
          >取 消</el-button
        >
        <el-button type="primary" size="small" @click="save">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex';
import { dataUtils } from '@/common';
export default {
  name: 'menuManage',
  computed: {
    ...mapGetters(['G_otherBtn'])
  },
  data() {
    return {
      state: 1, //1表示添加根目录，2表示编辑根目录，3表示添加子菜单,4表示编辑子菜单
      stateEnum: {
        1: '添加根目录',
        2: '编辑根目录',
        3: '添加子菜单',
        4: '编辑子菜单'
      },
      rootDialogShow: false,
      innerVisible: false,
      rootForm: {
        title: '',
        menuCode: '',
        name: '',
        path: '',
        icon: 'el-icon-edit',
        other: [],
        sortIndex: '',
        checkOther: []
      },
      editRoot: {},
      rootRules: {
        title: [
          { required: true, message: '请输入根目录名称', trigger: 'blur' }
        ],
        menuCode: [
          { required: true, message: '请输入菜单编码', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入页面的name', trigger: 'blur' }
        ],
        sortIndex: [
          { required: true, message: '请输入排序号', trigger: 'blur' }
        ]
        // path: [
        //     { required: true, message: '请输入路径', trigger: 'blur' },
        // ]
      },
      funForm: {
        funcCode: '',
        name: '',
        note: ''
      },
      funFormRules: {
        name: [{ required: true, message: '请输入功能名称', trigger: 'blur' }],
        funcCode: [
          { required: true, message: '请输入功能编码', trigger: 'blur' }
        ]
      },
      menuList: [],
      apiList: [],
      btnList: [],
      apiOptions: [],
      loading: false,
      rowmenuCode: null
    };
  },
  methods: {
    ...mapMutations(['M_getNavList']),
    // 添加根目录按钮点击事件
    addMenuBtn() {
      //   this.btnList = dataUtils.deepCopy(this.G_otherBtn);
      this.getApiList();
      this.state = 1;
      this.rootDialogShow = true;
      this.$nextTick(() => {
        this.$refs.rootForm.resetFields();
      });
    },
    // 添加子菜单按钮
    addItemBtn(row) {
      //   this.btnList = dataUtils.deepCopy(this.G_otherBtn);
      this.getApiList();
      console.log(row);
      this.state = 3;
      this.rootDialogShow = true;
      this.editRoot = row;
      this.rootForm.checkOther = [];

      this.$nextTick(() => {
        this.$refs.rootForm.resetFields();
      });
    },
    // 编辑根目录按钮点击事件
    editMenuBtn(state, row) {
      this.editRoot = {};
      this.rowmenuCode = row.menuCode;
      console.log(row);
      // this.btnList = dataUtils.deepCopy(this.G_otherBtn);
      this.getApiList();
      console.log(this.btnList);
      this.state = state;
      this.rootDialogShow = true;

      let checkOther = [];
      this.$nextTick(() => {
        this.editRoot = row;
        this.$refs.rootForm.resetFields();
        this.rootForm = { ...row };

        // if (!row.children) {
        //   row.other.map((item) => {
        //     checkOther.push(item.code);
        //     this.btnList.some((twoItem) => {
        //       if (item.label == twoItem.label) {
        //         twoItem.api = [...item.api];
        //       }
        //       return true;
        //     });
        //   });
        // }
        // console.log(this.btnList);
        // this.rootForm.checkOther = checkOther;
        // console.log(this.rootForm);
        this.GetUserMenus();
        this.GetFuncByMenuCode();
      });
    },
    // 根据menuCode获取当前目录的功能
    GetFuncByMenuCode() {
      this.$ajax
        .post(this.$apiUrls.GetFuncByMenuCode, '', {
          query: { menuCode: this.editRoot.menuCode }
        })
        .then((r) => {
          let { returnData } = r.data;
          var newArr = [];
          // for (var i = 0; i < NewArr.length; i++) {
          //   for (var k = 0; k < NewArr[i].apis.length; k++) {
          //     NewArr[i].api.push(NewArr[i].apis[k].apiCode);
          //   }
          // }
          newArr = returnData.map((item, i) => {
            return {
              api: [],
              data: item
            };
          });
          console.log(newArr);
          for (var i = 0; i < newArr.length; i++) {
            for (var k = 0; k < newArr[i].data.apis.length; k++) {
              if (newArr[i].data.apis[k].apiCode !== null) {
                newArr[i].api.push(newArr[i].data.apis[k].apiCode);
              }
            }
          }
          this.btnList = newArr;
          console.log(newArr);
          console.log(this.btnList);
          console.log(this.editRoot);
        });
    },

    // 编辑根目录和子菜单的提交与修改 status区分子菜单和根目录  type区分新增还是修改
    editSubmit(isEnabled, type) {
      //   let { checkOther, icon, menuCode, name, other, path, title } =
      //     this.rootForm;
      //   this.editRoot.checkOther = checkOther;
      //   this.editRoot.icon = icon;
      //   this.editRoot.menuCode = menuCode;
      //   this.editRoot.name = name;
      //   this.editRoot.other = other;
      //   this.editRoot.path = path;
      //   this.editRoot.title = title;

      var parentCode = '0';
      var sortIndex = this.menuList.length + 1;
      if (isEnabled == true) {
        parentCode = this.editRoot.menuCode;
        sortIndex = this.editRoot.children.length + 1;
        if (type == 'edit') {
          parentCode = this.editRoot.parentCode;
          sortIndex = this.editRoot.sortIndex;
        }
      }

      var editRole = {
        menuCode: this.rootForm.menuCode,
        icon: this.rootForm.icon,
        title: this.rootForm.title,
        name: this.rootForm.name,
        path: this.rootForm.path,
        parentCode: parentCode,
        sortIndex: this.rootForm.sortIndex,
        isEnabled: isEnabled,
        isView: parentCode == 0 ? false : true
      };
      console.log(editRole);
      if (type == 'add') {
        this.$ajax.post(this.$apiUrls.AddMenu, editRole).then((r) => {
          let { success } = r.data;
          if (!success) return;
          this.GetAllMenu();
          this.$message({
            message: '添加成功',
            type: 'success'
          });
        });
        return;
      } else {
        this.$ajax.post(this.$apiUrls.UpdateMenu, editRole).then((r) => {
          let { success } = r.data;
          if (!success) return;
          if (isEnabled == true) {
            this.addRelevanceApiSubmit();
          }
          this.GetAllMenu();
          this.$message({
            message: '修改成功',
            type: 'success'
          });
        });
      }
    },
    // 添加子菜单的功能api提交
    addRelevanceApiSubmit() {
      //   if (!this.editRoot.children) {
      //     this.editRoot.children = [{ ...this.rootForm }];
      //     console.log(this.menuList);
      //     return;
      //   }
      //   this.editRoot.children.push({ ...this.rootForm });
      console.log(this.btnList);
      var editRole = this.btnList.map((item, i) => {
        return {
          funcCode: item.data.funCode,
          apiArray: item.api
        };
      });
      // var editRole = newArr.filter((item) => item.apiArray.length > 0);
      console.log(editRole);

      this.$ajax.post(this.$apiUrls.RelevanceApi, editRole).then((r) => {
        this.GetUserMenus();
      });
    },

    // 删除根目录
    DeleteMenuBtn(row) {
      console.log(typeof row.menuCode);
      this.$confirm(`是否删除${row.title}菜单?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax
            .post(this.$apiUrls.DeleteMenu, '', {
              query: { menuCode: row.menuCode }
            })
            .then((r) => {
              let { success } = r.data;
              if (!success) return;
              this.GetAllMenu();
              this.$message({
                message: '删除成功',
                type: 'success'
              });
            });
        })
        .catch(() => {});
    },
    // 添加功能
    addFunctionBtn() {
      var editRole = {
        funcCode: this.funForm.funcCode,
        menuCode: this.editRoot.menuCode,
        name: this.funForm.name,
        isEnabled: true,
        note: this.funForm.name
      };
      console.log(editRole);
      this.$ajax.post(this.$apiUrls.AddOrUpdateFunction, editRole).then((r) => {
        let { success } = r.data;
        if (!success) return;
        this.innerVisible = false;
        this.GetFuncByMenuCode();
        this.$message({
          message: '添加成功',
          type: 'success'
        });
      });
    },

    // 添加和编辑角色的保存
    save() {
      let other = [];
      this.btnList.map((item) => {
        console.log(item);
        if (this.rootForm.checkOther.includes(item.code)) {
          other.push(item);
        }
      });
      this.rootForm.other = other;
      console.log(this.state);
      this.$refs.rootForm.validate((valid) => {
        if (valid) {
          this.rootDialogShow = false;
          switch (this.state) {
            case 1:
              this.editSubmit(false, 'add');
              break;
            case 2:
              this.editSubmit(false, 'edit');
              break;
            case 3:
              this.editSubmit(true, 'add');
              break;
            case 4:
              this.editSubmit(true, 'edit');

              break;
            default:
              break;
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    // 获取菜单列表
    GetAllMenu() {
      this.$ajax.post(this.$apiUrls.GetAllMenu).then((r) => {
        let { returnData } = r.data;
        console.log(returnData);
        this.menuList = returnData;
      });
    },
    // 获取菜单列表
    GetUserMenus() {
      this.$ajax
        .post(this.$apiUrls.GetUserMenus, '', {
          query: { operatorCode: this.editRoot.menuCode }
        })
        .then((r) => {});
    },
    // 获取api列表
    getApiList() {
      this.$ajax.post(this.$apiUrls.GetAllApiList).then((r) => {
        console.log(r.returnData);
        let { returnData } = r.data;
        this.apiList = returnData;
      });
    },
    // api接口列表的筛选
    remoteMethod(query) {
      if (query !== '') {
        this.loading = true;
        setTimeout(() => {
          this.loading = false;
          this.apiOptions = this.apiList.filter((item) => {
            return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1;
          });
        }, 200);
      } else {
        this.apiOptions = [];
      }
    },
    // 删除功能按钮
    delFunBtn(item) {
      console.log(item);
      this.$confirm(`是否删除${item.data.funName}功能?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          let datas = { funcCode: item.data.funCode };
          this.$ajax
            .paramsPost(this.$apiUrls.DeleteFunction, datas)
            .then((r) => {
              let { success } = r.data;
              if (!success) return;
              this.$message({
                message: '删除成功',
                type: 'success'
              });
              this.GetFuncByMenuCode();
            });
        })
        .catch(() => {});
    }
  },
  created() {
    this.GetAllMenu();
  }
};
</script>
<style lang="less" scoped>
.menuManagePage {
  display: flex;
  flex-direction: column;
  height: 100%;
  header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    height: 36px;
  }
  .cont {
    display: flex;
    flex-direction: column;
    flex: 1;
    height: calc(100% - 50px);
    /deep/.el-table__body-wrapper {
      height: 100%;
      overflow-y: scroll;
    }
    .el-table {
      display: flex;
      flex-direction: column;
    }
  }
  /deep/.el-dialog {
    margin-top: 12vh !important;
  }
  .funBtn_wrap {
    + .funBtn_wrap {
      margin-top: 10px;
    }
    .del_funBtn {
      color: #d63031;
      font-size: 18px;
      cursor: pointer;
    }
  }
}
</style>
<style lang="less">
// .elOption {
//   padding-right: 20px !important;
// }
</style>
