<template>
  <div class="userManage">
    <!-- 用户列表查询 -->
    <el-container class="container">
      <el-header>
        <div>
          <el-form class="demo-form-inline">
            <el-form-item>
              <el-button type="primary" size="small" @click="addAdmin"
                >添加用户</el-button
              >
              <el-button type="danger" size="small" @click="delMore"
                >批量删除</el-button
              >
            </el-form-item>
          </el-form>
        </div>
        <div class="searchWrap">
          <el-form :inline="true" class="demo-form-inline">
            <el-form-item label="" label-width="70px">
              <el-input
                clearable
                v-model.trim="formInline"
                placeholder="请输入"
                size="small"
              ></el-input>
            </el-form-item>
            <!-- <el-form-item label="账号" label-width="70px">
              <el-input
                clearable
                v-model.trim="formInline.operatorCode"
                placeholder="请输入账号"
                size="small"
              ></el-input>
            </el-form-item> -->
            <el-form-item>
              <el-button
                type="primary"
                size="small"
                icon="el-icon-search"
                @click="search"
                >查询</el-button
              >
            </el-form-item>
          </el-form>
        </div>
      </el-header>
      <el-main class="main-wrap">
        <el-table
          v-loading="loading"
          ref="multipleTable"
          :data="tableData"
          @selection-change="handleSelectChangeTable"
          border
          style="width: 100%"
          height="100%"
          row-key="operatorCode"
        >
          <el-table-column
            type="selection"
            width="55"
            :reserve-selection="true"
          >
          </el-table-column>
          <el-table-column
            type="index"
            :index="indexMethod"
            label="序号"
            width="180"
          >
          </el-table-column>
          <el-table-column prop="name" label="用户名"> </el-table-column>
          <el-table-column prop="operatorCode" label="账号"> </el-table-column>
          <el-table-column prop="deptName" label="科室"> </el-table-column>
          <el-table-column prop="role" label="角色"> </el-table-column>
          <el-table-column fixed="right" label="操作">
            <template slot-scope="scope">
              <el-button
                @click="handleClick(scope.row)"
                type="primary"
                size="mini"
                plain
                >编辑</el-button
              >
              <el-button
                @click="handleDelete(scope.row)"
                type="danger"
                size="mini"
                plain
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-main>
      <el-footer>
        <el-pagination
          style="padding-top: 15px"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="index"
          :page-sizes="pageSize"
          :page-size="size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableList.length"
        >
        </el-pagination>
      </el-footer>
    </el-container>
    <!-- 新增编辑弹出框 ,编辑密码非必填-->
    <el-dialog
      :title="`${isAdd ? '添加' : '编辑'}用户`"
      :visible.sync="dialogFormVisible"
    >
      <el-form :model="popupForm" :rules="rules" ref="ruleForm">
        <el-form-item label="用户名" :label-width="formLabelWidth" prop="name">
          <el-input
            v-model.trim="popupForm.name"
            autocomplete="off"
            size="small"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="账号"
          :label-width="formLabelWidth"
          prop="operatorCode"
        >
          <el-input
            v-model.trim="popupForm.operatorCode"
            autocomplete="off"
            size="small"
            :disabled="!isAdd"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="密码"
          :label-width="formLabelWidth"
          :prop="isAdd ? 'password' : ''"
        >
          <el-input
            v-model.trim="popupForm.password"
            autocomplete="off"
            size="small"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="角色"
          prop="roleCode"
          :label-width="formLabelWidth"
        >
          <!-- <el-select
            v-model.trim="popupForm.roleCode"
            multiple
            filterable
            placeholder="请选择"
            size="mini"
          >
            <el-option
              v-for="items in roleList"
              :key="items.roleCode"
              :label="items.name"
              :value="items.roleCode"
            >
            </el-option>
          </el-select> -->
          <el-cascader
            v-model="popupForm.roleCode"
            size="small"
            :options="roleList"
            :props="props"
            clearable
          >
          </el-cascader>
        </el-form-item>
        <el-form-item
          label="院区"
          prop="hospCode"
          :label-width="formLabelWidth"
        >
          <el-select
            v-model.trim="popupForm.hospCode"
            multiple
            filterable
            placeholder="请选择"
            @change="getDepartment(popupForm.hospCode)"
            size="mini"
          >
            <el-option
              v-for="items in brankList"
              :key="items.hospCode"
              :label="items.hospName"
              :value="items.hospCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" :label-width="formLabelWidth">
          <el-radio-group v-model.trim="popupForm.isEnabled">
            <el-radio
              :label="item.value"
              :key="item.value"
              v-for="item in options"
              >{{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="科室"
          prop="deptCode"
          :label-width="formLabelWidth"
        >
          <!-- <el-select
            v-model.trim="popupForm.deptCode"
            placeholder="请选择"
            filterable
            size="mini"
          >
            <el-option
              v-for="items in departList"
              :key="items.deptCode"
              :label="items.deptName"
              :value="items.deptCode"
            >
            </el-option>
          </el-select> -->
          <el-select
            size="mini"
            filterable
            v-model="popupForm.deptCode"
            placeholder="请选择"
          >
            <el-option-group
              v-for="group in departList"
              :key="group.key"
              :label="group.key"
            >
              <el-option
                v-for="item in group.value"
                :key="item.deptCode"
                :label="item.deptName"
                :value="item.deptCode"
              >
              </el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item
          label="最大折扣价"
          :label-width="formLabelWidth"
          prop="maxDiscountPrice"
        >
          <!-- ^[0-9]+(.[0-9]{2}) -->
          <el-input
            v-model.trim="popupForm.maxDiscountPrice"
            autocomplete="off"
            size="small"
            onkeyup="this.value=this.value.match(/\d+\.?\d{0,2}/)"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="最大抵扣金额"
          :label-width="formLabelWidth"
          prop="maxDeductionPrice"
        >
          <el-input
            v-model.trim="popupForm.maxDeductionPrice"
            autocomplete="off"
            size="small"
            onkeyup="this.value=this.value.match(/\d+\.?\d{0,2}/)"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="最大折扣"
          :label-width="formLabelWidth"
          prop="maxDeductionPrice"
        >
          <el-input-number
            size="small"
            v-model="popupForm.maxDiscount"
            :precision="2"
            :step="0.01"
            :min="0"
            :max="1"
          ></el-input-number>
        </el-form-item>
        <el-form-item
          label="电子签名"
          :label-width="formLabelWidth"
          prop="signaturePath"
          v-if="G_config.isSignatureShow"
        >
          <el-image
            style="
              width: 180px;
              height: 100px;
              cursor: pointer;
              border: 1px solid #ddd;
            "
            @click.native="signatureClick"
            :src="popupForm.signaturePath"
          >
          </el-image>
        </el-form-item>
      </el-form>

      <!--上传裁剪电子签名图片的抽屉-->
      <el-drawer
        title="电子签名"
        :modal="false"
        :visible.sync="signatureShow"
        :wrapperClosable="false"
      >
        <VueCropper
          @confirmClip="confirmClip"
          @cancel="vueCropperCancel"
          ref="VueCropper_Ref"
        ></VueCropper>
      </el-drawer>
      <!-- 编辑提示密码不填写默认不修改密码 -->
      <div slot="footer" class="dialog-footer">
        <h4 v-show="!isAdd">*密码不填写默认不修改密码</h4>
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import VueCropper from './components/vueCropper';
export default {
  name: 'userManage',
  components: {
    VueCropper
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_config'])
  },
  data() {
    return {
      props: {
        value: 'roleCode',
        label: 'name',
        multiple: true
      },
      formInline: '',
      codeArr: [],
      resizeFlag: '',
      loading: false,
      tableHeight: '',
      index: 1, //当前页数
      size: 10, //当前页条数
      pageSize: [10, 20, 30, 40, 50],
      tableList: [],
      tableData: [],
      departList: [],
      dialogFormVisible: false,
      dialogTitle: '',
      isAdd: true,
      formLabelWidth: '120px',
      options: [
        {
          value: false,
          label: '禁用'
        },
        {
          value: true,
          label: '启用'
        }
      ],
      brankList: [],
      roleList: [],
      popupForm: {
        name: '',
        operatorCode: '',
        password: '',
        isEnabled: true,
        roleCode: [],
        deptCode: '',
        maxDiscountPrice: 0,
        maxDeductionPrice: 0,
        maxDiscount: 1,
        loginTime: '',
        hospCode: [],
        signaturePath: ''
      },
      roleCode: [],
      rules: {
        name: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
        operatorCode: [
          { required: true, message: '请输入账号', trigger: 'blur' }
        ],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        roleCode: [{ required: true, message: '请选择角色', trigger: 'blur' }],
        deptCode: [{ required: true, message: '请选择科室', trigger: 'blur' }],
        hospCode: [{ required: true, message: '请选择院区', trigger: 'blur' }]
      },
      signatureShow: false,
      fileData: ''
    };
  },
  created: function () {
    this.search(); //默认查询

    // this.getDepartment();
    this.getCodeHospital();
  },
  mounted: function () {
    setTimeout(() => {
      this.getAllRoleList(); //默认查询角色列表
    }, 1000);
  },

  methods: {
    //查询
    search() {
      this.loading = true;
      var _this = this;
      this.$ajax.post(this.$apiUrls.GetAllOperator).then((r) => {
        //
        var r = r.data;
        if ((r ?? '') !== '') {
          if (!r.success) {
            alert(r.returnMsg);
            _this.tableList = [];
            _this.tableData = _this.paging(_this.size, _this.index);
          } else {
            let newListData = []; // 用于存放搜索出来数据的新数组
            JSON.parse(JSON.stringify(r.returnData)).filter((item) => {
              if (
                item.name.indexOf(_this.formInline) !== -1 ||
                item.operatorCode.indexOf(_this.formInline) !== -1
              ) {
                newListData.push(item);
              }
            });
            _this.tableList = newListData; //模糊查询后的数据
            // _this.tableList = JSON.parse(JSON.stringify(r.returnData));
            _this.tableData = _this.paging(_this.size, _this.index);
          }
        } else {
          _this.tableList = [];
          _this.tableData = _this.paging(_this.size, _this.index);
        }
        _this.loading = false;
      });
    },
    // 获取角色列表
    getAllRoleList() {
      this.$ajax.post(this.$apiUrls.GetAllRole).then((r) => {
        let { returnData, success } = r.data;
        if (!success) {
          return;
        }
        let hospCodeArr = [];
        let roleList = [];
        this.$nextTick(() => {
          returnData?.map((item) => {
            let idx = hospCodeArr.indexOf(item.hospCode);
            if (idx == -1) {
              hospCodeArr.push(item.hospCode);
              roleList.push({
                hospCode: item.hospCode,
                // isEnabled: false,
                name: this.G_EnumList['CodeHospital'][item.hospCode],
                // note: "具有最大权限",
                roleCode: idx,
                children: [item]
              });
              return;
            }
            roleList[idx].children.push(item);
          });
          console.log(roleList);
          this.roleList = roleList;
        });
      });
    },
    // 获取科室列表
    getDepartment(hospCode) {
      this.$ajax
        .post(this.$apiUrls.ReadCodeDepartmentByHosp, hospCode)
        .then((r) => {
          let { returnData, success } = r.data;
          if (!success) {
            return;
          }
          this.departList = returnData || [];
        });
    },
    //新增
    addAdmin() {
      this.isAdd = true;
      this.dialogFormVisible = true;
      // this.getAllRoleList();
      this.$nextTick(() => {
        this.$refs.ruleForm.resetFields();
        setTimeout(() => {
          console.log(this.popupForm);
        });
      });
    },
    //新增提交
    addSubmit() {
      // console.log("this.popupForm", this.popupForm);
      let roleCodeArr = [];
      this.popupForm.roleCode.map((item) => {
        roleCodeArr.push(item[1]);
      });
      console.log(roleCodeArr);
      let datas = {
        name: this.popupForm.name,
        operatorCode: this.popupForm.operatorCode,
        password: this.popupForm.password,
        isEnabled: this.popupForm.isEnabled,
        deptCode: this.popupForm.deptCode,
        maxDiscountPrice: parseInt(this.popupForm.maxDiscountPrice),
        maxDeductionPrice: parseInt(this.popupForm.maxDeductionPrice),
        maxDiscount: this.popupForm.maxDiscount,
        loginTime: '',
        hospCode: this.popupForm.hospCode
      };
      console.log(datas);
      this.$ajax
        .post(this.$apiUrls.AddOperator, datas, {
          query: { roleCode: roleCodeArr.toString() }
        })
        .then((r) => {
          //
          r = r.data;
          if (!r.success) {
            return;
          }
          this.$message({
            showClose: true,
            message: '新建成功!',
            type: 'success'
          });
          this.uploadSignature();
          this.search();
        });
    },
    // 根据操作员编码获取操作员角色
    GetUserRole(operatorCode) {
      this.roleCode = [];
      this.$ajax
        .post(this.$apiUrls.GetUserRole, '', {
          query: {
            operatorCode: operatorCode
          }
        })
        .then((r) => {
          let { returnData, success } = r.data;
          if (!success) {
            return;
          }
          returnData.map((item, i) => {
            if (item.roleCode != '') {
              this.roleCode.push(['-1', item.roleCode]);
            }
          });
          console.log(this.roleCode);
        });
    },
    //编辑
    async handleClick(row) {
      console.log('row', row);
      await this.GetUserRole(row.operatorCode); //查询角色
      await this.getDepartment(row.hospCode || []);
      this.isAdd = false;
      this.dialogFormVisible = true;
      this.editapi = row;
      this.$nextTick(() => {
        this.$refs.ruleForm.resetFields();
        setTimeout(() => {
          this.popupForm = {
            name: row.name,
            operatorCode: row.operatorCode,
            password: '',
            isEnabled: row.isEnabled,
            roleCode: this.roleCode,
            deptCode: row.deptCode,
            maxDiscountPrice: row.maxDiscountPrice,
            maxDeductionPrice: row.maxDeductionPrice,
            maxDiscount: row.maxDiscount,
            loginTime: '',
            hospCode: row.hospCode,
            signaturePath: row.signaturePath
          };
          console.log(this.popupForm);
        });
      });
    },
    //编辑提交
    editSubmit() {
      console.log(this.popupForm);
      let roleCodeArr = [];
      this.popupForm.roleCode.map((item) => {
        roleCodeArr.push(item[1]);
      });
      console.log(roleCodeArr);
      let datas = {
        name: this.popupForm.name,
        operatorCode: this.popupForm.operatorCode,
        password: this.popupForm.password,
        isEnabled: this.popupForm.isEnabled,
        deptCode: this.popupForm.deptCode,
        maxDiscountPrice: parseInt(this.popupForm.maxDiscountPrice),
        maxDeductionPrice: parseInt(this.popupForm.maxDeductionPrice),
        maxDiscount: this.popupForm.maxDiscount,
        loginTime: this.popupForm.loginTime,
        hospCode: this.popupForm.hospCode,
        signaturePath: this.popupForm.signaturePath
      };
      console.log(datas);
      this.$ajax
        .post(this.$apiUrls.UpdateOperator, datas, {
          query: { roleCode: roleCodeArr.toString() }
        })
        .then((r) => {
          //
          r = r.data;
          if (!r.success) {
            // this.$message.error(r.ResultMsg);
            return;
          }
          this.$message({
            showClose: true,
            message: '修改成功!',
            type: 'success'
          });
          this.uploadSignature();
          this.search();
        });
    },
    //提交
    submit() {
      //console.log(this.isAdd);
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.dialogFormVisible = false;
          if (this.isAdd) {
            this.addSubmit();
            return;
          }
          this.editSubmit();
        } else {
          //console.log("error submit!!");
          return false;
        }
      });
    },
    //复选框勾选操作
    handleSelectChangeTable(val) {
      //console.log(val);
      this.codeArr = [];
      val.map((item, i) => {
        if (item.operatorCode != '') {
          this.codeArr.push(item.operatorCode);
        }
      });
      //console.log(this.codeArr);
    },
    //单条删除
    handleDelete(row) {
      //console.log(row);
      this.codeArr = [];
      this.codeArr.push(row.operatorCode);
      this.$confirm('是否确认删除该文件数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.delete(this.codeArr);
        })
        .catch(() => {
          return;
        });
    },
    //多条删除
    delMore() {
      if (this.codeArr.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.delete(this.codeArr);
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    //确认删除
    delete(codeArr) {
      // console.log("确认删除"); //"123,1234"
      this.$ajax
        .post(this.$apiUrls.DeleteOperators, '', {
          query: { operatorCode: codeArr.toString() }
        })
        .then((r) => {
          r = r.data;
          if (!r.success) {
            // this.$message.error(r.ResultMsg);
            return;
          }
          this.$message({
            showClose: true,
            message: '删除成功!',
            type: 'success'
          });
          this.search();
        });
    },
    //分页序号
    indexMethod(indexs) {
      return (this.index - 1) * this.size + indexs + 1;
    },
    // 页数改变事件
    handleSizeChange(size) {
      this.size = size;
      this.tableData = this.paging(size, this.index);
    },
    // 页码改变事件
    handleCurrentChange(current) {
      this.index = current;
      //console.log(current)
      this.tableData = this.paging(this.size, current);
    },
    // 本地分页的方法
    paging(size, current) {
      //console.log(size, current)
      var tableLists = JSON.parse(JSON.stringify(this.tableList));
      var tablePush = [];
      tableLists.forEach(function (item, index) {
        if (size * (current - 1) <= index && index <= size * current - 1) {
          tablePush.push(item);
        }
      });
      return tablePush;
    },
    //获取院区列表
    getCodeHospital() {
      this.$ajax.post(this.$apiUrls.GetHospitalInfo).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.brankList = returnData;
      });
    },
    // 电子签名的点击回调
    signatureClick() {
      this.signatureShow = true;
      this.$nextTick(() => {
        this.$set(
          this.$refs.VueCropper_Ref,
          'photoUrl',
          this.popupForm.signaturePath
        );
        this.$refs.VueCropper_Ref.option.img = this.popupForm.signaturePath;
      });
    },
    // 确认裁剪图片的回调
    confirmClip(imgMsg) {
      this.$set(this.popupForm, 'signaturePath', imgMsg.photoUrl);
      this.fileData = imgMsg.data;
      this.signatureShow = false;
    },
    // 上传电子签名
    uploadSignature() {
      console.log(this.fileData);
      if (this.fileData == '') return;
      let formData = new FormData();
      var file = new File([this.fileData], `${new Date()}.png`, {
        type: this.fileData.type,
        lastModified: Date.now()
      });
      formData.append('files', file);

      this.$ajax
        .post(this.$apiUrls.UploadSignature, formData, {
          query: {
            operatorCode: this.popupForm.operatorCode
          },
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: ''
          }
        })
        .then((r) => {});
    },
    // 电子签名取消裁剪
    vueCropperCancel() {
      this.signatureShow = false;
    }
  }
};
</script>
<style lang="less" scoped>
.userManage {
  display: flex;
  flex-direction: column;
  font-weight: 600;
  .el-form-item {
    margin-bottom: 8px;
  }
  header {
    display: flex;
    justify-content: space-between;
    .el-form-item {
      margin-bottom: 0;
    }
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    h4 {
      line-height: 40px;
      margin-right: 20px;
      color: red;
    }
  }
  .el-header,
  .el-main {
    padding: 0;
  }

  .searchWrap .el-form-item:last-child {
    margin-right: 0;
    margin-left: 10px;
  }
  .indexPage .contentWrap .tabPage {
    margin-top: 0;
  }
  .el-pagination {
    text-align: center;
  }
  .el-select {
    width: 100%;
  }
  .container {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .main-wrap {
    flex: 1;
    overflow: auto;
  }
}
</style>
<style>
.el-dialog {
  width: 480px;
}
.el-table .el-table__cell {
  padding: 5px 0;
}
.el-main .el-checkbox__inner {
  width: 20px;
  height: 20px;
}
/* 表格内容颜色 */
/* .el-table__body tbody tr:nth-child(odd) {}
 .el-table__body tbody tr:nth-child(even) td {} */
</style>
