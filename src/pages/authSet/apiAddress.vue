<template>
  <div class="apiAddress">
    <el-container class="container">
      <el-header>
        <div>
          <el-form class="demo-form-inline">
            <el-form-item>
              <el-button type="primary" size="small" @click="addapiBtn"
                >添加api</el-button
              >
              <el-button type="danger" size="small" @click="delMore"
                >批量删除</el-button
              >
            </el-form-item>
          </el-form>
        </div>
        <div class="searchWrap">
          <el-form :inline="true" class="demo-form-inline">
            <el-form-item label="" label-width="70px">
              <el-input
                clearable
                v-model.trim="searchCode"
                placeholder="请输入"
                size="small"
              ></el-input>
            </el-form-item>
            <el-form-item style="margin-left: 10px">
              <el-button
                type="primary"
                size="small"
                icon="el-icon-search"
                @click="search"
                >查询</el-button
              >
            </el-form-item>
          </el-form>
        </div>
      </el-header>
      <el-main class="main-wrap">
        <el-table
          v-loading="loading"
          class="elTabel"
          :data="tableData"
          style="width: 100%"
          size="small"
          border
          height="100%"
          @selection-change="handleSelectChangeTable"
        >
          <el-table-column type="selection" width="55"> </el-table-column>
          <el-table-column
            type="index"
            :index="indexMethod"
            label="序号"
            width="80"
          ></el-table-column>
          <el-table-column prop="name" label="api名称"></el-table-column>
          <el-table-column prop="apiPath" label="api地址"></el-table-column>
          <el-table-column label="操作" width="300">
            <template slot-scope="scope">
              <div>
                <el-button
                  type="primary"
                  size="mini"
                  plain
                  @click="editapiBtn(scope.row)"
                  >编辑</el-button
                >
                <el-button
                  type="danger"
                  size="mini"
                  plain
                  @click="handleDelete(scope.row)"
                  >删除</el-button
                >
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-main>
      <!--   分页 -->
      <el-footer>
        <el-pagination
          style="padding-top: 15px"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="index"
          :page-sizes="pageSize"
          :page-size="size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="apiList.length"
        >
        </el-pagination>
      </el-footer>
    </el-container>
    <!-- 添加api弹窗 -->
    <el-dialog
      :title="`${isAdd ? '添加' : '编辑'}api`"
      :visible.sync="dialogFormVisible"
    >
      <el-form :model="popupForm" :rules="rules" ref="ruleForm">
        <el-form-item label="api名称" :label-width="'80px'" prop="name">
          <el-input
            v-model.trim="popupForm.name"
            autocomplete="off"
            size="small"
          ></el-input>
        </el-form-item>
        <el-form-item label="api地址" :label-width="'80px'" prop="apiPath">
          <el-input
            v-model.trim="popupForm.apiPath"
            autocomplete="off"
            size="small"
          ></el-input>
        </el-form-item>
        <el-form-item label="api代码" :label-width="'80px'" prop="apiCode">
          <el-input
            v-model.trim="popupForm.apiCode"
            autocomplete="off"
            size="small"
            :disabled="!isAdd"
          ></el-input>
        </el-form-item>
        <el-form-item label="组名" :label-width="'80px'">
          <el-input
            v-model.trim="popupForm.groupName"
            autocomplete="off"
            size="small"
          ></el-input>
        </el-form-item>
        <el-form-item label="备注" :label-width="'80px'">
          <el-input
            v-model.trim="popupForm.note"
            autocomplete="off"
            size="small"
          ></el-input>
        </el-form-item>
        <el-form-item label="状态" :label-width="'80px'">
          <el-radio-group v-model.trim="popupForm.isEnabled">
            <el-radio
              :label="item.value"
              :key="item.value"
              v-for="item in options"
              >{{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false" size="small"
          >取 消</el-button
        >
        <el-button type="primary" @click="apiSave" size="small"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'apiAddress',
  data() {
    return {
      codeArr: [],
      resizeFlag: '',
      loading: false,
      tableHeight: '',
      index: 1, //当前页数
      size: 10, //当前页条数
      pageSize: [10, 20, 30, 40, 50],
      apiList: [],
      tableData: [],
      searchCode: '',
      options: [
        {
          value: false,
          label: '禁用'
        },
        {
          value: true,
          label: '启用'
        }
      ],
      popupForm: {
        name: '',
        apiPath: '',
        apiCode: '',
        groupName: '',
        note: '',
        isEnabled: true
      },
      dialogFormVisible: false,
      rules: {
        name: [{ required: true, message: '请输入api名称', trigger: 'blur' }],
        apiPath: [
          { required: true, message: '请输入api代码', trigger: 'blur' }
        ],
        apiCode: [{ required: true, message: '请输入api地址', trigger: 'blur' }]
        // note: [{ required: true, message: "请输入备注", trigger: "blur" }],
        // groupName: [
        //   { required: true, message: "请输入组名", trigger: "blur" },
        // ],
      },
      editapi: {},
      isAdd: true, //true表示添加，false表示修改；
      apiDialogShow: false
    };
  },
  created: function () {
    this.search(); //默认查询
  },

  mounted: function () {},
  methods: {
    //查询
    search() {
      this.loading = true;
      var _this = this;
      this.$ajax.post(this.$apiUrls.GetAllApiList).then((r) => {
        //
        var r = r.data;
        if ((r ?? '') !== '') {
          if (!r.success) {
            alert(r.returnMsg);
            _this.apiList = [];
            _this.tableData = _this.paging(_this.size, _this.index);
          } else {
            let newListData = []; // 用于存放搜索出来数据的新数组
            JSON.parse(JSON.stringify(r.returnData)).filter((item) => {
              if (
                item.name.indexOf(_this.searchCode) !== -1 ||
                item.apiPath.indexOf(_this.searchCode) !== -1
              ) {
                newListData.push(item);
              }
            });
            _this.apiList = newListData; //模糊查询后的数据
            // _this.apiList = JSON.parse(JSON.stringify(r.returnData));
            _this.tableData = _this.paging(_this.size, _this.index);
            //console.log(" this.apiList", _this.apiList);
          }
        } else {
          _this.apiList = [];
          _this.tableData = _this.paging(_this.size, _this.index);
        }
        _this.loading = false;
      });
    },
    // 添加api按钮点击事件
    addapiBtn() {
      this.isAdd = true;
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs.ruleForm.resetFields();
      });
    },
    // 编辑按钮点击事件
    editapiBtn(row) {
      //console.log(row);
      this.isAdd = false;
      this.dialogFormVisible = true;
      this.editapi = row;
      this.$nextTick(() => {
        this.$refs.ruleForm.resetFields();
        this.popupForm = {
          name: row.name,
          apiPath: row.apiPath,
          apiCode: row.apiCode,
          groupName: row.groupName,
          note: row.note,
          isEnabled: row.isEnabled
        };
      });
    },

    // 添加api的提交
    addSubmit() {
      console.log(this.popupForm);
      this.$ajax
        .post(this.$apiUrls.AddApi, {
          name: this.popupForm.name,
          apiPath: this.popupForm.apiPath,
          apiCode: this.popupForm.apiCode,
          groupName: this.popupForm.groupName,
          note: this.popupForm.note,
          isEnabled: this.popupForm.isEnabled
        })
        .then((r) => {
          //
          r = r.data;
          if (!r.success) {
            //this.$message.error(r.ResultMsg);
            return;
          }
          // this.search();
          this.tableData.push({ ...this.popupForm });
        });
    },
    // 编辑api的提交
    editSubmit() {
      //console.log("this.popupForm", this.popupForm);
      this.$ajax
        .post(this.$apiUrls.UpdateApi, {
          name: this.popupForm.name,
          apiPath: this.popupForm.apiPath,
          apiCode: this.popupForm.apiCode,
          groupName: this.popupForm.groupName,
          note: this.popupForm.note,
          isEnabled: this.popupForm.isEnabled
        })
        .then((r) => {
          //
          r = r.data;
          if (!r.success) {
            return;
          }
          this.search();
        });
    },
    // 添加和编辑api的保存
    apiSave() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.dialogFormVisible = false;
          if (this.isAdd) {
            this.addSubmit();
            return;
          }
          this.editSubmit();
        } else {
          //console.log("error submit!!");
          return false;
        }
      });
    },
    //复选框勾选操作
    handleSelectChangeTable: function (val) {
      //console.log(val);
      this.codeArr = [];
      val.map((item, i) => {
        if (item.apiCode != '') {
          this.codeArr.push(item.apiCode);
        }
      });
      // console.log(this.codeArr);
    },

    //单条删除
    handleDelete(row) {
      // console.log(row);
      this.codeArr = [];
      this.codeArr.push(row.apiCode);
      this.$confirm('是否确认删除该文件数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.apiDelete(this.codeArr);
        })
        .catch(() => {
          return;
        });
    },
    //多条删除
    delMore() {
      if (this.codeArr.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.apiDelete(this.codeArr);
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    //确认删除
    apiDelete(codeArr) {
      // console.log("确认删除", codeArr); //["1234"]
      console.log(codeArr);
      this.$ajax.post(this.$apiUrls.DeleteApi, codeArr).then((r) => {
        //
        r = r.data;
        if (!r.success) {
          // this.$message.error(r.ResultMsg);
          return;
        }
        this.search();
      });
    },
    //分页序号
    indexMethod(indexs) {
      return (this.index - 1) * this.size + indexs + 1;
    },
    // 页数改变事件
    handleSizeChange(size) {
      this.size = size;
      this.tableData = this.paging(size, this.index);
    },
    // 页码改变事件
    handleCurrentChange(current) {
      this.index = current;
      //console.log(current)
      this.tableData = this.paging(this.size, current);
    },
    // 本地分页的方法
    paging(size, current) {
      //console.log(size, current)
      var apiLists = JSON.parse(JSON.stringify(this.apiList));
      var tablePush = [];
      apiLists.forEach(function (item, index) {
        if (size * (current - 1) <= index && index <= size * current - 1) {
          tablePush.push(item);
        }
      });
      return tablePush;
    }
  }
};
</script>
<style lang="less" scoped>
.apiAddress {
  display: flex;
  flex-direction: column;
  font-weight: 600;
  header {
    display: flex;
    justify-content: space-between;
    .el-form-item {
      margin-bottom: 0;
    }
  }

  .el-header,
  .el-main {
    padding: 0;
  }
  .searchWrap .el-form-item:last-child {
    margin-right: 0;
    margin-left: 10px;
  }
  .indexPage .contentWrap .tabPage {
    margin-top: 0;
  }
  .el-pagination {
    text-align: center;
  }
  .container {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .main-wrap {
    flex: 1;
    overflow: auto;
  }
}
</style>
<style>
.el-dialog {
  width: 480px;
}
.el-table .el-table__cell {
  padding: 5px 0;
}
.el-main .el-checkbox__inner {
  width: 20px;
  height: 20px;
}
</style>
