<template>
  <!-- 体检疾患名单汇总统计 -->
  <div class="examinePersonnelCost">
    <div class="main">
      <div class="searchBar">
        <div class="search-list">
          <div class="list-item" style="width: 350px">
            <span style="width: 36px">时间</span>
            <el-date-picker
              :picker-options="{ shortcuts: G_datePickerShortcuts }"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              v-model="date"
              :clearable="false"
              value-format="yyyy-MM-dd"
              @change="statistics"
            >
            </el-date-picker>
          </div>
          <div class="list-item">
            <span style="width: 72px">疾病名称</span>
            <vxe-select
              multiple
              :option-props="{ value: 'diseaseCode', label: 'diseaseName' }"
              v-model="searchInfo.diseaseCodes"
              :options="diseaseList"
              filterable
              clearable
            ></vxe-select>
          </div>
          <div class="list-item">
            <el-radio-group
              v-model="searchInfo.personCompany"
              @change="radioChange"
            >
              <el-radio :label="0">所有</el-radio>
              <el-radio :label="1">个人</el-radio>
              <el-radio :label="2">团体</el-radio>
            </el-radio-group>
          </div>
          <div class="list-item">
            <span style="width: 36px">单位</span>
            <el-cascader
              ref="company_cascader_ref"
              v-model="searchInfo.companyCode"
              :filter-method="filterMethod"
              :options="companyList"
              :props="{ multiple: false }"
              clearable
              filterable
              size="small"
              collapse-tags
              @change="companyChange"
            >
            </el-cascader>
          </div>
          <div class="list-item">
            <span style="width: 36px">部门</span>
            <el-select
              class="select"
              v-model.trim="searchInfo.companyDeptCode"
              placeholder="请选择"
              size="small"
              filterable
              clearable
              :disabled="isHave"
              @change="statistics"
            >
              <el-option
                v-for="(item, index) in companyDeptList"
                :key="index"
                :label="item.deptName"
                :value="item.deptCode"
              ></el-option>
            </el-select>
          </div>
          <ButtonGroup
            :btnList="['查询', '导出']"
            @search="statistics"
            @exports="exports"
          >
          </ButtonGroup>
        </div>
        <div class="title-wrap">
          <h3>体检病患名单汇总:</h3>
          <div>
            <span>合计人数：{{ totalInfo.totalCount }}</span>
            <span>男：{{ totalInfo.maleCount }}</span>
            <span>女：{{ totalInfo.femaleCount }}</span>
          </div>
        </div>
      </div>
      <div class="main-table" v-loading="loading">
        <PublicTable
          :isSortShow="false"
          :theads="theads"
          :viewTableList.sync="tableData"
          :columnWidth="columnWidth"
        >
          <template #sex="{ scope }">
            <div>
              {{ G_EnumList['Sex'][scope.row.sex] }}
            </div>
          </template>
        </PublicTable>
      </div>
    </div>
  </div>
</template>

<script>
import ButtonGroup from './components/buttonGroup.vue';
import PublicTable from '@/components/publicTable.vue';
import { mapGetters } from 'vuex';
import { dataUtils } from '@/common';
import { export2Excel } from '@/common/excelUtil';
import ExportExcel from '@/common/excel/exportExcel';
export default {
  name: 'summaryOfDiseaseList',
  mixins: [ExportExcel],
  components: {
    ButtonGroup,
    PublicTable
  },
  data() {
    return {
      diseaseList: [],
      loading: false,
      date: [new Date(), new Date()],
      searchInfo: {
        beginDate: '',
        endDate: '',
        companyCode: '',
        companyTimes: null,
        personCompany: 0,
        companyDeptCode: '',
        diseaseCodes: []
      },
      isHave: true,
      companyDeptList: [],
      theads: {
        diseaseName: '疾病名称',
        diseaseCount: '患病人数',
        diseaseRate: '患病率'
      },
      columnWidth: {},
      tableData: [],
      companyList: [],
      clusterList: [],
      fixed_clusterList: [],
      totalInfo: {
        totalCount: 0,
        maleCount: 0,
        femaleCount: 0
      }
    };
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_peClsList', 'G_datePickerShortcuts'])
  },
  created() {
    this.getCompany();
    this.getDiseaseList();
  },
  methods: {
    //获取疾病列表
    getDiseaseList() {
      this.$ajax.post(this.$apiUrls.GetDiseaseInDept).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.diseaseList = returnData
          .map((item) => {
            return item.diseases;
          })
          .flat();
        console.log(
          '🚀 ~ this.diseaseList=returnData.map ~ this.diseaseList:',
          this.diseaseList
        );
      });
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    // 获取单位
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) {
        this.date = [dataUtils.getDate(), dataUtils.getDate()];
        this.searchInfo.companyDeptCode = '';
        this.isHave = true;
        this.statistics();
        return;
      }
      this.getDepartList(data);
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.date = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.statistics();
    },
    //获取部门数据
    getDepartList(companyCode) {
      this.searchInfo.companyDeptCode = '';
      this.isHave = true;
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanyDepartment, {
          companyCode: companyCode?.[0] || '',
          deptCode: '',
          deptName: ''
        })
        .then((r) => {
          this.isHave = false;
          //console.log("r", r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.companyDeptList = returnData || [];
        });
    },
    // 查询
    statistics() {
      let [beginDate, endDate] = this.date;
      let data = {
        beginDate: dataUtils.dateToStrStart(beginDate) || '',
        endDate: dataUtils.dateToStrEnd(endDate) || '',
        companyCode: this.searchInfo.companyCode[0] || '',
        companyTimes: this.searchInfo.companyCode[1] || null,
        companyDeptCode: this.searchInfo.companyDeptCode,
        personCompany: this.searchInfo.personCompany,
        diseaseCodes: this.searchInfo.diseaseCodes
      };
      this.loading = true;
      console.log(data);
      this.$ajax
        .post(this.$apiUrls.DiseaseRateStatistic, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) {
            this.tableData = [];
            return;
          }
          this.tableData = returnData.list;
          this.totalInfo.totalCount = returnData.totalCount;
          this.totalInfo.maleCount = returnData.maleCount;
          this.totalInfo.femaleCount = returnData.femaleCount;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 导出
    exports() {
      if (this.tableData.length == 0) {
        this.$message({
          message: '体检人员费用列表不能为空！',
          type: 'warning',
          showClose: true
        });
        return;
      }

      this.$confirm(`体检疾患名单汇总列表?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let text = `合计人数：${this.totalInfo.totalCount}   男：${this.totalInfo.maleCount} 女：${this.totalInfo.femaleCount}`;
        this.exportExcel(
          '体检疾患名单汇总列表',
          '体检疾患名单汇总列表',
          this.theads,
          this.tableData,
          text
        );
      });
    },
    // 单选框切换
    radioChange() {
      if (this.searchInfo.peType === 1) {
        this.searchInfo.companyCode = '';
      }
      this.statistics();
    }
  }
};
</script>

<style lang="less" scoped>
.examinePersonnelCost {
  color: #2d3436;

  .main {
    height: 100%;
    background: #fff;
    padding: 18px 10px;
    display: flex;
    flex-direction: column;
  }

  .search-list {
    display: flex;
    align-items: center;
    margin-bottom: 18px;
  }

  .list-item {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 20px;
    width: 300px;

    &:last-child {
      margin-right: 0;
    }

    span {
      font-size: 14px;
      font-weight: 600;
      margin-right: 10px;
    }
  }

  .input {
    width: 100%;
  }

  .title-wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 14px;

    h3 {
      font-size: 18px;
    }

    span {
      font-size: 16px;

      & + span {
        margin-left: 20px;
      }

      i {
        color: red;
        font-style: normal;
      }
    }
  }

  .main-table {
    border: 1px solid rgba(178, 190, 195, 0.5);
    border-radius: 4px;
    flex: 1;
    overflow: auto;
  }

  /deep/ .el-radio {
    margin-right: 10px;
  }

  /deep/ .el-radio-group {
    display: flex;
  }

  /deep/.vxe-select--panel {
    max-width: 350px;
  }

  span {
    white-space: nowrap;
  }
}
</style>
