<template>
  <div class="payReport_page">
    <div class="centent_wrap">
      <ButtonGroup
        ref="buttonGroup"
        @search="getFeeClsAmountsStatistics"
        class="btn_group"
        :btnList="['查询', '日期范围']"
      >
        <template #footAdd>
          <el-button
            size="small"
            v-print="'#test'"
            :disabled="!feeList.length"
            class="green_btn btn"
            icon="iconfont icon-dayin-"
            >打印</el-button
          >
        </template>
      </ButtonGroup>
      <div class="report_wrap" id="test">
        <div class="every_table">
          <p class="title_p">体检收费日缴款报表</p>
          <el-table :data="feeList" style="width: 100%" show-summary>
            <el-table-column prop="startNo" label="开始发票号" width="100">
            </el-table-column>
            <el-table-column prop="endNo" label="结束发票号" width="100">
            </el-table-column>
            <el-table-column prop="useCount" label="张数" width="60">
            </el-table-column>
            <el-table-column prop="price" label="收入合计" width="85">
            </el-table-column>
            <el-table-column prop="cash" label="现金" width="75">
            </el-table-column>
            <el-table-column prop="unionPay" label="银联" width="75">
            </el-table-column>
            <el-table-column prop="weChat" label="微信"> </el-table-column>
            <el-table-column prop="alipay" label="支付宝" width="70">
            </el-table-column>
            <!-- <el-table-column prop="price" label="每日健康" width="78">
                        </el-table-column> -->
            <el-table-column prop="storedValueCard" label="储值卡" width="70">
            </el-table-column>
            <el-table-column prop="refund" label="其中退款" width="65">
            </el-table-column>
          </el-table>
          <div class="report_bottom">
            <span>共有发票{{ invoiceReport.totalUseCount }}张</span>
            <span>复核:</span>
            <span>出纳:</span>
            <span>操作员：{{ G_userInfo.codeOper.name }}</span>
          </div>
        </div>
        <div class="every_table">
          <p class="title_p">资金分类统计</p>
          <ul class="capital_count">
            <li>
              <span>分类名称</span>
              <span>金额</span>
            </li>
            <li>
              <span>分类名称</span>
              <span>金额</span>
            </li>
            <li>
              <span>分类名称</span>
              <span>金额</span>
            </li>
            <li>
              <span>分类名称</span>
              <span>金额</span>
            </li>
            <li v-for="item in capitalCountList" :key="item.index">
              <span>{{ item.feeClsName }}</span>
              <span>{{ item.price }}</span>
            </li>
          </ul>
          <div class="report_bottom">
            <span>统计时间：{{ C_statisticTime }}</span>
            <span>复核:</span>
            <span>出纳:</span>
          </div>
        </div>
        <div class="every_table">
          <p class="title_p">退明款细日报表</p>
          <el-table :data="refundReport" show-summary style="width: 100%">
            <el-table-column prop="refundNo" label="退款结算号" width="120">
            </el-table-column>
            <el-table-column prop="refundName" label="退款员" width="80">
            </el-table-column>
            <el-table-column prop="settleName" label="收款员">
            </el-table-column>
            <el-table-column prop="settlementNo" label="收款结算号">
            </el-table-column>
            <el-table-column prop="refundType" label="退款方式" width="80">
            </el-table-column>
            <el-table-column
              prop="refundPrice"
              label="退款金额"
              align="right"
              width="80"
            >
              <template #default="scope">
                {{ C_price(scope.row.refundPrice) }}
              </template>
            </el-table-column>
          </el-table>
          <div class="report_bottom">
            <span>统计时间：{{ C_statisticTime }}</span>
            <span>复核:</span>
            <span>出纳:</span>
            <span>操作员：{{ G_userInfo.codeOper.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ButtonGroup from './components/buttonGroup';
import PublicTable from '@/components/publicTable';
import { mapGetters } from 'vuex';
export default {
  name: 'payReport',
  components: {
    ButtonGroup,
    PublicTable
  },
  computed: {
    ...mapGetters(['G_userInfo']),
    C_statisticTime() {
      if (!this.startTime) return;
      return `${this.startTime}~${this.endTime}`;
    },
    C_price() {
      return function (price) {
        if (!price) return;
        let priceArr = price.toString().split('.');
        console.log(priceArr);
        let num = priceArr[1];
        console.log(num);
        if (num === undefined) return priceArr[0] + '.00';
        if (num.length == 1) return priceArr[0] + '.' + num + '0';
        return price;
      };
    }
  },
  data() {
    return {
      feeList: [], //收费日缴列表
      capitalCountList: [], //资金分类统计列表
      refundReport: [],
      startTime: '',
      endTime: '',
      invoiceReport: {}
    };
  },
  methods: {
    // 获取财务分类统计
    getFeeClsAmountsStatistics() {
      console.log(this.$refs.buttonGroup.daterangeVal);
      if (
        !this.$refs.buttonGroup.daterangeVal ||
        this.$refs.buttonGroup.daterangeVal.length == 0
      ) {
        this.$refs.buttonGroup.daterangeVal = [];
        this.$message({
          message: '请选择日期',
          type: 'warning'
        });
        return;
      }
      this.startTime = this.$refs.buttonGroup.daterangeVal[0] || '';
      this.endTime = this.$refs.buttonGroup.daterangeVal[1] || '';
      let datas = {
        startTime: this.startTime,
        endTime: this.endTime
      };
      console.log(datas);
      this.$ajax
        .post(this.$apiUrls.PaymentReportStatistics, datas)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.refundReport = returnData.refundReport.refundData;
          this.feeList = returnData.invoiceReport.invoiceData;
          this.capitalCountList = returnData.feeClsReport;
          this.invoiceReport = returnData.invoiceReport;
        });
    }
  },
  mounted() {}
};
</script>

<style lang="less" scoped>
.payReport_page {
  .centent_wrap {
    height: 100%;
    background: #fff;
    overflow: auto;
    padding: 10px;
    display: flex;
    flex-direction: column;
  }

  .btn_group {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 10px;
  }

  .report_wrap {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    width: 794px;
    margin: 0 auto;
    .every_table {
      margin-bottom: 10px;
      &:last-child {
        margin: 0;
      }
    }
    .title_p {
      text-align: center;
      font-size: 20px;
    }

    .report_bottom {
      display: flex;
      margin-top: 10px;

      span {
        font-size: 14px;

        &:first-child {
          flex: 1;
          flex-shrink: 0;
        }

        &:nth-child(2) {
          width: 120px;
        }

        &:nth-child(3) {
          width: 120px;
        }
      }
    }
    // 资金统计
    .capital_count {
      display: flex;
      border-top: 1px solid #000;
      border-left: 1px solid #000;
      flex-wrap: wrap;
      li {
        flex-basis: 25%;
        flex-shrink: 0;
        border-right: 1px solid #000;
        border-bottom: 1px solid #000;
        display: flex;
        span {
          padding: 5px;
          font-size: 14px;
          text-align: center;
        }
        span:first-child {
          flex: 1;
          flex-shrink: 0;
          border-right: 1px solid #000;
        }
        span:last-child {
          width: 85px;
        }
      }
    }
  }
}
</style>
<style lang="less" media="test" scoped>
@media print {
  @page {
    size: A4 portrait;
    /*  */
    margin: 5mm 5mm 5mm 5mm;
    background: #fff;
    /* 国家标准公文页边距 GB/T 9704-2012 */
  }

  #test {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    width: 794px;
    margin: 0 auto;
    height: 100%;
    background: #fff;
    .every_table {
      margin-bottom: 10px;
      &:last-child {
        margin: 0;
      }
    }
    .title_p {
      text-align: center;
      font-size: 20px;
    }

    .report_bottom {
      display: flex;
      margin-top: 10px;

      span {
        font-size: 14px;

        &:first-child {
          flex: 1;
          flex-shrink: 0;
        }

        &:nth-child(2) {
          width: 120px;
        }

        &:nth-child(3) {
          width: 120px;
        }
      }
    }
    // 资金统计
    .capital_count {
      display: flex;
      border-top: 1px solid #000;
      border-left: 1px solid #000;
      flex-wrap: wrap;
      li {
        flex-basis: 25%;
        flex-shrink: 0;
        border-right: 1px solid #000;
        border-bottom: 1px solid #000;
        display: flex;
        span {
          padding: 5px;
          font-size: 14px;
          text-align: center;
        }
        span:first-child {
          flex: 1;
          flex-shrink: 0;
          border-right: 1px solid #000;
        }
        span:last-child {
          width: 85px;
        }
      }
    }
  }
}
</style>
