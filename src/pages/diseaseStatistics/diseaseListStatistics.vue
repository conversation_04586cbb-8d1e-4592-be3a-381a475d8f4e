<template>
  <!-- 体检疾患名单汇总统计表 -->
  <div class="diseaseListStatistics">
    <div class="left-wrap">
      <div class="left-operate">
        <div class="search-operate">
          <el-input
            v-model="keyword"
            size="small"
            style="margin-right: 10px"
            placeholder="请输入关键字"
            @keypress.enter.native="searchKeyword"
            @clear="searchKeyword"
            clearable
          ></el-input>
          <ButtonGroup :btnList="['查询']" @search="searchKeyword" />
        </div>
        <el-button
          size="small"
          class="green_btn btn"
          icon="iconfont icon-huifuxitongmoren"
          @click="initializeTemplate"
          >初始化模板</el-button
        >
        <el-button
          @click="editClick"
          class="blue_btn btn"
          size="small"
          icon="iconfont icon-bianji"
          :disabled="
            infoData.isCompanyCheck === 0 || infoData.isCompanyCheck === 1
          "
          >编辑</el-button
        >
      </div>
      <div class="left-table">
        <PublicTable
          isCheck
          :isSortShow="false"
          :theads="theads"
          :viewTableList.sync="tableData"
          ref="view_Ref"
          @selectionChange="rowSelectionChange"
        >
        </PublicTable>
      </div>
    </div>
    <div class="right-wrap">
      <div class="right-operate">
        <div class="operate-item">
          <label style="width: 78px">统计时间</label>
          <el-date-picker
            :picker-options="{ shortcuts: G_datePickerShortcuts }"
            v-model="infoData.date"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
            class="width"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </div>
        <el-radio-group
          v-model="infoData.isCompanyCheck"
          class="radio-group"
          @change="radioGroupChange"
        >
          <el-radio :label="0">所有</el-radio>
          <el-radio :label="1">个人</el-radio>
          <el-radio :label="2">团体</el-radio>
        </el-radio-group>
        <div class="operate-item">
          <label style="width: 40px">性别</label>
          <el-select
            v-model="infoData.sex"
            placeholder="请选择"
            size="small"
            class="width"
            clearable
          >
            <el-option
              v-for="item in G_shareSexList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <div class="operate-item">
          <label style="width: 110px">年龄段</label>
          <el-input
            v-model="infoData.startAge"
            size="small"
            placeholder="请输入"
            class="width"
            clearable
          ></el-input>
          <span>-</span>
          <el-input
            v-model="infoData.endAge"
            size="small"
            placeholder="请输入"
            class="width"
            clearable
          ></el-input>
        </div>
      </div>
      <div class="right-operate">
        <div class="operate-item">
          <label style="width: 40px">单位</label>
          <el-cascader
            ref="company_cascader_ref"
            v-model="infoData.companyCode"
            :filter-method="filterMethod"
            :options="companyList"
            :props="{ multiple: false }"
            clearable
            filterable
            size="small"
            collapse-tags
            @change="companyChange"
          >
          </el-cascader>
        </div>
        <!-- <div class="operate-item">
          <label style="width: 88px">体检次数</label>
          <el-select
            v-model="infoData.companyTimes"
            placeholder="请选择"
            size="small"
            class="width"
            clearable
            filterable
            @focus="companyTimesFocus"
            :disabled="infoData.isCompanyCheck !== 2"
          >
            <el-option
              v-for="item in companyTimesList"
              :key="item.companyTimes"
              :label="item.companyTimes"
              :value="item.companyTimes"
            >
            </el-option>
          </el-select>
        </div> -->
        <div class="operate-item">
          <label style="width: 40px">部门</label>
          <el-select
            v-model="infoData.deptCode"
            placeholder="请选择"
            size="small"
            class="width"
            clearable
            filterable
            :disabled="!infoData.companyCode[0]"
          >
            <el-option
              v-for="item in companyDepartmentList"
              :key="item.deptCode"
              :label="item.deptName"
              :value="item.deptCode"
            >
            </el-option>
          </el-select>
        </div>
        <div class="operate-item">
          <label style="width: 40px">套餐</label>
          <el-select
            v-model="infoData.clusterCode"
            placeholder="请选择"
            size="small"
            class="width"
            clearable
            filterable
            @focus="clusterFocus"
          >
            <el-option
              v-for="item in clusterList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <!-- <ButtonGroup :btnList="['查询', '导出']" @search="search" @exports="exports" /> -->
        <div>
          <el-button
            @click="search"
            class="blue_btn btn"
            size="small"
            :loading="loadingFlag"
            icon="iconfont icon-search"
            >查询</el-button
          >
          <el-button
            @click="exports"
            size="small"
            class="yellow_btn btn"
            icon="iconfont icon-daochu"
            >导出</el-button
          >
        </div>
      </div>
      <div>
        <el-radio-group v-model="detailRadio">
          <el-radio :label="1">明细</el-radio>
          <el-radio :label="2">汇总</el-radio>
        </el-radio-group>
      </div>
      <div class="right-table">
        <div
          class="table-item"
          v-for="(item, index) in diseaseClsData"
          :key="index"
        >
          <ul class="table-info">
            <li class="cell_blue">{{ item.diseaseClsName }}</li>
            <li>患病人数：{{ item.sickCount }}</li>
            <li>患病率：{{ item.probability }}</li>
          </ul>
          <div class="table" v-show="detailRadio === 1">
            <PublicTable
              :theads="diseaseClsTheads"
              :columnWidth="diseaseColumnWidth"
              :viewTableList.sync="item.regData"
              ref="view_Ref"
            >
              <template #sex="{ scope }">
                {{ G_EnumList['Sex'][scope.row.sex] }}
              </template>
            </PublicTable>
          </div>
          <!-- <div class="table">
            <PublicTable
              :theads="diseaseClsTheads"
              :viewTableList.sync="item.regData"
              ref="view_Ref"
            >
              <template #sex="{ scope }">
                {{ G_EnumList["Sex"][scope.row.sex] }}
              </template>
            </PublicTable>
          </div> -->
        </div>
        <el-empty
          :image-size="200"
          v-if="diseaseClsData.length == 0"
        ></el-empty>
      </div>
    </div>
    <el-drawer
      title="疾病分类对应疾病属性"
      :visible.sync="drawer"
      :before-close="cancel"
      size="80%"
      :wrapperClosable="false"
    >
      <div class="drawer-wrap">
        <div class="drawer-left">
          <div class="drawer-table">
            <span>疾病分类列表</span>
            <div class="table-list">
              <PublicTable
                :isSortShow="false"
                is-check
                :theads="codeDiseaseClsThead"
                @currentChange="codeDiseaseClsRowClick"
                @selectionChange="codeDiseaseClsSelection"
                :viewTableList.sync="codeDiseaseCls"
              >
              </PublicTable>
            </div>
          </div>
          <div class="drawer-table">
            <span>科室包含疾病列表</span>
            <div class="table-list">
              <PublicTable
                :isSortShow="false"
                is-check
                :theads="diseaseInDeptThead"
                @selectionChange="diseaseInDeptSelection"
                :viewTableList.sync="diseaseInDept"
              >
              </PublicTable>
            </div>
          </div>
        </div>
        <div class="drawer-btn">
          <el-button
            size="small"
            class="blue_btn add-del"
            style="margin-bottom: 40px"
            @click="addClick"
            >添加
            <i class="iconfont icon-Rightxiangyou34"></i>
          </el-button>
          <el-button
            size="small"
            class="red_btn add-del"
            @click="delClick"
            icon="iconfont icon-Leftxiangzuo35"
          >
            删除</el-button
          >
        </div>
        <div class="drawer-right">
          <span>疾病分类对应疾病列表</span>
          <div class="table-list">
            <PublicTable
              is-check
              :isSortShow="false"
              :theads="companyDiseaseClsDiseaseThead"
              @selectionChange="companyDiseaseClsDiseaseSelection"
              :viewTableList.sync="companyDiseaseClsDisease"
            >
            </PublicTable>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import ButtonGroup from './components/buttonGroup.vue';
import PublicTable from '@/components/publicTable.vue';
import { mapGetters } from 'vuex';
import { dataUtils } from '@/common';
import groupDetailedExpenseReportExcelJs from '@/common/excel/groupDetailedExpenseReportExcel';
export default {
  name: 'diseaseListStatistics',
  mixins: [groupDetailedExpenseReportExcelJs],
  components: {
    PublicTable,
    ButtonGroup
  },
  data() {
    return {
      infoData: {
        date: [new Date(), new Date()],
        companyCode: '',
        companyTimes: '',
        isCompanyCheck: 0,
        clusterCode: '',
        deptCode: '',
        sex: '',
        startAge: '',
        endAge: '',
        diseaseClsArray: []
      },
      theads: {
        diseaseClsName: '疾病分类名称'
      },
      tableData: [],
      tableDataCopy: [],
      options: [],
      keyword: '',
      companyList: [],
      companyTimesList: [],
      companyDepartmentList: [],
      clusterList: [],
      diseaseClsData: [],
      drawer: false,
      codeDiseaseClsThead: {
        diseaseClsCode: '疾病分类代码',
        diseaseClsName: '疾病分类名称'
      },
      codeDiseaseCls: [],
      diseaseInDeptThead: {
        clsName: '科室',
        diseaseCode: '疾病代码',
        diseaseName: '疾病名称'
      },
      diseaseInDept: [],
      companyDiseaseClsDiseaseThead: {
        diseaseClsCode: '疾病分类代码',
        diseaseClsName: '疾病分类名称',
        diseaseCode: '疾病代码',
        diseaseName: '疾病名称'
      },
      companyDiseaseClsDisease: [],
      codeDiseaseRow: {},
      codeDiseaseSelect: [],
      diseaseInDeptSelect: [],
      companyDiseaseClsDiseaseSelect: [],
      detailRadio: 1,
      diseaseClsTheads: {
        regNo: '体检号',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        tel: '电话',
        cardNo: '证件号',
        registerTime: '登记时间',
        activeTime: '激活时间',
        diseaseTag: '疾病名称'
      },
      diseaseColumnWidth: {
        regNo: 120,
        name: 100,
        sex: 50,
        age: 60,
        tel: 120,
        cardNo: 165,
        registerTime: 150,
        activeTime: 150
      },
      loadingFlag: false
    };
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_shareSexList',
      'G_peClsList',
      'G_datePickerShortcuts'
    ])
  },
  created() {
    this.getCompany();
    this.getCodeDiseaseCls();
  },
  methods: {
    // 获取单位
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    //单位下拉变化
    companyChange(data) {
      this.infoData.deptCode = '';
      if (!data || data.length === 0) return;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.infoData.date = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.search();
      this.companyTimesFocus();
    },
    // 获取单位体检次数
    getCompanyTimes() {
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanyTimes, '', {
          query: {
            companyCode: this.infoData.companyCode
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.companyTimesList = returnData || [];
        });
    },
    // 获取单位部门
    getCompanyDepartment() {
      let data = {
        companyCode: this.infoData.companyCode[0]
      };
      this.$ajax.post(this.$apiUrls.R_CodeCompanyDepartment, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyDepartmentList = returnData || [];
      });
    },
    // 获取套餐
    getCluster() {
      if (!this.infoData.companyCode[0]) {
        this.$ajax.post(this.$apiUrls.Cluster).then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.clusterList =
            returnData.map((item) => {
              return {
                label: item.clusName,
                value: item.clusCode
              };
            }) || [];
        });
      } else {
        if (!this.infoData.companyCode && !this.infoData.companyTimes) return;
        this.$ajax
          .post(this.$apiUrls.GetCompanyCluster, '', {
            query: {
              companyCode: this.infoData.companyCode[0],
              companyTimes: this.infoData.companyCode[1]
            }
          })
          .then((r) => {
            let { success, returnData } = r.data;
            if (!success) return;
            this.clusterList =
              returnData.map((item) => {
                return {
                  label: item.clusterName,
                  value: item.clusterCode
                };
              }) || [];
          });
      }
    },
    // 体检次数获取焦点
    companyTimesFocus() {
      if (!this.infoData.companyCode) {
        this.$message({
          message: '请先选择单位!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.getCompanyDepartment();
    },
    // 套餐获取焦点
    clusterFocus() {
      this.getCluster();
      if (this.infoData.isCompanyCheck === 2) {
        if (!this.infoData.companyCode && !this.infoData.companyTimes) {
          this.$message({
            message: '请先选择单位和体检次数!',
            type: 'warning',
            showClose: true
          });
          return;
        }
      }
    },
    // 单选框切换
    radioGroupChange() {
      this.clusterList = [];
    },
    // 查询
    search() {
      if (this.infoData.isCompanyCheck == 2 && !this.infoData.companyCode[0]) {
        this.$message({
          message: '请选择单位!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.infoData.diseaseClsArray.length === 0) {
        this.$message({
          message: '请勾选分类!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      console.log(this.infoData);
      this.loadingFlag = true;
      let data = {
        startTime: this.infoData.date[0],
        endTime: this.infoData.date[1],
        companyCode: this.infoData.companyCode[0] || '',
        companyTimes: this.infoData.companyCode[1] || '',
        isCompanyCheck: this.infoData.isCompanyCheck,
        clusterCode: this.infoData.clusterCode,
        deptCode: this.infoData.deptCode,
        sex: this.infoData.sex == '' ? -1 : this.infoData.sex,
        startAge: this.infoData.startAge,
        endAge: this.infoData.endAge,
        diseaseClsArray: this.infoData.diseaseClsArray
      };
      this.$ajax
        .post(this.$apiUrls.DiseaseStatisticsByDiseaseCls, data)
        .then((r) => {
          console.log('r: ', r);
          this.loadingFlag = false;
          let { success, returnData } = r.data;
          if (!success) return;
          this.diseaseClsData = returnData.diseaseClsData || [];
        })
        .catch((e) => {
          this.loadingFlag = false;
        });
    },
    // 初始化模板
    initializeTemplate() {
      console.log(this.infoData.companyCode);
      if (!this.infoData.companyCode) {
        this.$message({
          message: '请先选择单位!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$ajax
        .post(this.$apiUrls.InitCompanyTemplate, '', {
          query: { companyCode: this.infoData.companyCode[0] }
        })
        .then((r) => {
          console.log('r: ', r);
          let { success, returnData } = r.data;
          if (!success) return;
          // this.tableData = returnData || [];
          // this.tableDataCopy = returnData || [];
        });
    },
    // 查询关键字
    searchKeyword() {
      if (this.keyword.trim() === '') {
        this.tableData = dataUtils.deepCopy(this.tableDataCopy);
        return;
      }
      this.tableData = this.tableDataCopy.filter((item) => {
        return (
          item.diseaseClsCode.indexOf(this.keyword) !== -1 ||
          item.diseaseClsName.indexOf(this.keyword) !== -1
        );
      });
    },
    // 表格勾选
    rowSelectionChange(row) {
      this.infoData.diseaseClsArray = row;
    },
    // 获取疾病分类列表
    getCodeDiseaseCls() {
      this.$ajax
        .post(this.$apiUrls.RD_CodeDiseaseCls + '/Read', [])
        .then((r) => {
          console.log('r: ', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.codeDiseaseCls = returnData || [];
          this.tableData = returnData || [];
          this.tableDataCopy = returnData || [];
        });
    },
    // 获取疾病分类列表
    getDiseaseInDept() {
      let newData = [];
      this.$ajax.post(this.$apiUrls.GetDiseaseInDept).then((r) => {
        console.log('r: ', r);
        let { success, returnData } = r.data;
        if (!success) return;
        returnData.map((item) => {
          item.diseases.map((i) => {
            newData.push({
              clsName: item.clsName,
              ...i
            });
          });
        });
        this.diseaseInDept = newData || [];
        console.log('this.diseaseInDept : ', this.diseaseInDept);
        // this.tableDataCopy = returnData || [];
      });
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    // 获取单位疾病分类对应疾病
    getMapCompanyDiseaseClsDisease() {
      let data = {
        companyCode: this.infoData.companyCode[0],
        diseaseClsCode: this.codeDiseaseRow.diseaseClsCode
      };
      this.$ajax
        .post(this.$apiUrls.Read_MapCompanyDiseaseClsDisease, data)
        .then((r) => {
          console.log('r: ', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.companyDiseaseClsDisease = returnData || [];
          // this.tableDataCopy = returnData || [];
        });
    },
    // 编辑按钮
    editClick() {
      if (!this.infoData.companyCode && this.infoData.isCompanyCheck === 2) {
        this.$message({
          message: '请选择单位!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.drawer = true;
      this.getCodeDiseaseCls();
      this.getDiseaseInDept();
      this.getMapCompanyDiseaseClsDisease();
    },
    // 疾病分类列表行点击
    codeDiseaseClsRowClick(row) {
      if (row) {
        this.codeDiseaseRow = row;
        this.getMapCompanyDiseaseClsDisease();
      }
    },
    // 疾病分类列表勾选
    codeDiseaseClsSelection(row) {
      this.codeDiseaseSelect = row;
    },
    // 科室包含疾病列表勾选
    diseaseInDeptSelection(row) {
      this.diseaseInDeptSelect = row;
    },
    // 疾病分类对应疾病列表勾选
    companyDiseaseClsDiseaseSelection(row) {
      this.companyDiseaseClsDiseaseSelect = row;
    },
    // 关闭
    cancel() {
      this.drawer = false;
    },
    // 添加按钮
    addClick() {
      if (
        this.codeDiseaseSelect.length === 0 &&
        this.diseaseInDeptSelect.length === 0
      ) {
        this.$message({
          message: '请勾选疾病分类列表和科室包含疾病列表内容!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let data = [];
      this.codeDiseaseSelect.map((item) => {
        this.diseaseInDeptSelect.map((i) => {
          data.push({
            companyCode: this.infoData.companyCode[0],
            diseaseClsCode: item.diseaseClsCode,
            diseaseCode: i.diseaseCode
          });
        });
      });
      this.$ajax
        .post(this.$apiUrls.CD_MapCompanyDiseaseCls + '/Create', data)
        .then((r) => {
          console.log('r: ', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.$message({
            message: '添加成功!',
            type: 'success',
            showClose: true
          });
          this.codeDiseaseSelection = [];
          this.diseaseInDeptSelect = [];
          this.getMapCompanyDiseaseClsDisease();
        });
    },
    // 删除按钮
    delClick() {
      if (this.companyDiseaseClsDiseaseSelect.length === 0) {
        this.$message({
          message: '请勾选疾病分类对应疾病列表内容!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let data = [];
      this.companyDiseaseClsDiseaseSelect.map((item) => {
        data.push({
          companyCode: this.infoData.companyCode[0],
          diseaseClsCode: item.diseaseClsCode,
          diseaseCode: item.diseaseCode
        });
      });
      console.log('data: ', data);
      this.$ajax
        .post(this.$apiUrls.CD_MapCompanyDiseaseCls + '/Delete', data)
        .then((r) => {
          console.log('r: ', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.$message({
            message: '删除成功!',
            type: 'success',
            showClose: true
          });
          this.getMapCompanyDiseaseClsDisease();
        });
    },
    // 导出
    exports() {
      let fileName =
        `体检疾患名单${this.detailRadio === 1 ? '明细' : '汇总'}统计表_` +
        dataUtils.getNowDateTiemNo();
      if (this.detailRadio === 2) {
        this.exportExcel(
          this.diseaseClsTheads,
          this.diseaseClsData,
          '',
          '`${item.diseaseClsName}   患病人数：${item.sickCount}   患病率：${item.probability}`',
          fileName,
          ''
        );
        return;
      }
      this.exportExcel(
        this.diseaseClsTheads,
        this.diseaseClsData,
        '',
        '`${item.diseaseClsName}   患病人数：${item.sickCount}   患病率：${item.probability}`',
        fileName,
        'regData'
      );
    }
  }
};
</script>

<style lang="less" scoped>
.diseaseListStatistics {
  display: flex;
  color: #2d3436;
  .left-wrap {
    width: 280px;
    background: #fff;
    margin-right: 10px;
    padding: 10px;
    display: flex;
    flex-direction: column;
    border-radius: 4px;
  }
  .left-operate {
    margin-bottom: 10px;
  }
  .btn {
    padding: 6.5px 10px;
  }
  .search-operate {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
  .left-table {
    flex: 1;
    overflow: auto;
    border: 1px solid #d9dfe2;
    border-radius: 4px;
  }
  .right-wrap {
    background: #fff;
    flex: 1;
    overflow: auto;
    border-radius: 4px;
    padding: 10px;
    display: flex;
    flex-direction: column;
  }
  .right-operate {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 10px;
  }
  .operate-item {
    display: flex;
    align-items: center;
    margin-right: 10px;
    &:last-child {
      margin-right: 0;
    }
  }
  .width {
    width: 100%;
  }
  .radio-group {
    display: flex;
    align-items: center;
    margin-right: 10px;
  }
  .right-table {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .table-item {
    display: flex;
    flex-direction: column;
    // flex: 1;
    // overflow: auto;
    margin-top: 10px;
  }
  .table-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: 600;
  }
  .table {
    flex: 1;
    overflow: auto;
    border: 1px solid #d9dfe2;
    border-radius: 4px;
    /deep/.el-table--scrollable-y .el-table__body-wrapper {
      height: auto !important;
    }
    /deep/.el-table__body-wrapper {
      height: auto !important;
    }
  }
  /deep/.el-drawer__header {
    background: #d1e2f9;
    margin-bottom: 0;
    padding: 18px;
    color: #2d3436;
    font-weight: 600;
  }
  .drawer-wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    height: 100%;
  }
  .add-del {
    padding: 9px 9px;
    margin-left: 0;
  }
  .drawer-btn {
    width: 100px;
    height: 100%;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
  }
  .drawer-left {
    flex: 1;
    overflow: auto;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .drawer-table {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    display: flex;
    flex-direction: column;
    span {
      font-weight: 600;
      margin-bottom: 12px;
    }
    &:last-child {
      margin-top: 12px;
    }
  }
  .table-list {
    flex: 1;
    overflow: auto;
    border: 1px solid #d9dfe2;
    border-radius: 4px;
  }
  .drawer-right {
    flex: 1;
    overflow: auto;
    height: 100%;
    display: flex;
    flex-direction: column;
    span {
      font-weight: 600;
      margin-bottom: 12px;
    }
  }
}
</style>
