<template>
  <div class="financeReport_page">
    <div class="centent_wrap">
      <ButtonGroup
        ref="buttonGroup"
        @search="getFeeClsAmountsStatistics"
        class="btn_group"
        :btnList="['查询', '日期范围']"
      >
        <template #footAdd>
          <el-button
            size="small"
            v-print="'#test'"
            :disabled="!viewTableList.length"
            class="green_btn btn"
            icon="iconfont icon-dayin-"
            >打印</el-button
          >
        </template>
      </ButtonGroup>
      <div class="report_wrap" id="test">
        <p class="title_p">体检收费分类汇总报表</p>
        <!-- <PublicTable :columnWidth="columnWidth" :isSortShow="false" :viewTableList="viewTableList" :theads="theads"></PublicTable> -->
        <el-table :data="viewTableList" show-summary style="width: 100%">
          <el-table-column prop="feeClsCode" label="分类代码" width="80">
          </el-table-column>
          <el-table-column prop="feeClsName" label="发票分类名称">
          </el-table-column>
          <el-table-column prop="price" label="金额" width="80">
          </el-table-column>
          <el-table-column prop="num" label="人数" width="80">
          </el-table-column>
        </el-table>
        <div class="report_bottom">
          <span>统计时间：{{ C_statisticTime }}</span>
          <span>复核:</span>
          <span>出纳:</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ButtonGroup from './components/buttonGroup';
import PublicTable from '@/components/publicTable';
export default {
  name: 'financeReport',
  components: {
    ButtonGroup,
    PublicTable
  },
  computed: {
    C_statisticTime() {
      if (!this.startTime) return;
      return `${this.startTime}~${this.endTime}`;
    }
  },
  data() {
    return {
      theads: {
        feeClsCode: '分类代码',
        feeClsName: '发票分类名称',
        price: '金额',
        num: '人数'
      },
      columnWidth: {
        feeClsCode: 80,
        price: 80,
        num: 80
      },
      viewTableList: [],
      startTime: '',
      endTime: ''
    };
  },
  methods: {
    // 获取财务分类统计
    getFeeClsAmountsStatistics() {
      console.log(this.$refs.buttonGroup.daterangeVal);
      if (
        !this.$refs.buttonGroup.daterangeVal ||
        this.$refs.buttonGroup.daterangeVal.length == 0
      ) {
        this.$refs.buttonGroup.daterangeVal = [];
        this.$message({
          message: '请选择日期',
          type: 'warning'
        });
        return;
      }
      this.startTime = this.$refs.buttonGroup.daterangeVal[0] || '';
      this.endTime = this.$refs.buttonGroup.daterangeVal[1] || '';
      let datas = {
        startTime: this.startTime,
        endTime: this.endTime
      };
      console.log(datas);
      this.$ajax
        .paramsPost(this.$apiUrls.FeeClsAmountsStatistics, datas)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.viewTableList = returnData;
        });
    }
  },
  mounted() {}
};
</script>

<style lang="less" scoped>
.financeReport_page {
  .centent_wrap {
    height: 100%;
    background: #fff;
    overflow: auto;
    padding: 10px;
    display: flex;
    flex-direction: column;
  }

  .btn_group {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 10px;
  }

  .report_wrap {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    width: 794px;
    margin: 0 auto;

    .title_p {
      text-align: center;
      font-size: 20px;
    }

    .report_bottom {
      display: flex;
      margin-top: 10px;

      span {
        font-size: 14px;

        &:first-child {
          flex: 1;
          flex-shrink: 0;
        }

        &:nth-child(2) {
          width: 120px;
        }

        &:nth-child(3) {
          width: 120px;
        }
      }
    }
  }
}
</style>
<style lang="less" media="test" scoped>
@media print {
  @page {
    size: A4 portrait;
    /*  */
    margin: 5mm 5mm 5mm 5mm;
    background: #fff;
    /* 国家标准公文页边距 GB/T 9704-2012 */
  }

  #test {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    width: 794px;
    margin: 0 auto;
    background: #fff;
    height: 100%;
    .title_p {
      text-align: center;
      font-size: 20px;
    }

    .report_bottom {
      display: flex;
      padding: 10px 0;
      span {
        font-size: 14px;

        &:first-child {
          flex: 1;
          flex-shrink: 0;
        }

        &:nth-child(2) {
          width: 120px;
        }

        &:nth-child(3) {
          width: 120px;
        }
      }
    }
  }
}
</style>
