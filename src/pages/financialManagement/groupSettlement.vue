<template>
  <div class="groupSettlement">
    <ul class="group_head">
      <li>
        <div class="every_inp" style="width: 25%">
          <label>单位名称</label>
          <!-- <el-select
            placeholder="请选择"
            size="small"
            filterable
            clearable
            v-model="searchForm.companyCode"
            @change="getTypeList"
            @clear="clearAll"
            class="select"
          >
            <el-option
              v-for="item in companyList"
              :key="item.companyCode"
              :label="item.companyName"
              :value="item.companyCode"
            >
            </el-option>
          </el-select> -->
          <el-cascader
            style="width: 100%"
            ref="company_cascader_ref"
            v-model="searchForm.companyCode"
            :options="companyList"
            :props="{ multiple: false }"
            clearable
            filterable
            size="small"
            collapse-tags
            @change="companyChange"
            :filter-method="filterMethod"
          >
          </el-cascader>
        </div>
        <div class="every_inp">
          <label>单位部门</label>
          <el-select
            class="select"
            v-model.trim="searchForm.companyDeptCode"
            placeholder="请选择"
            size="small"
            filterable
            clearable
            :disabled="
              !searchForm.companyCode || searchForm.companyCode.length == 0
            "
          >
            <el-option
              v-for="(item, index) in companyDeptList"
              :key="index"
              :label="item.deptName"
              :value="item.deptCode"
            ></el-option>
          </el-select>
        </div>
        <!-- <div class="every_inp">
          <label>体检次数</label>
          <el-select
            v-model.trim="searchForm.companyTimes"
            placeholder="请选择"
            size="small"
            clearable
            filterable
            class="select"
            @change="companyTimesChange"
            :disabled="!searchForm.companyCode"
          >
            <el-option
              v-for="(item, index) in comTimesList"
              :key="index"
              :value="item.value"
              :label="item.label"
            ></el-option>
          </el-select>
        </div> -->

        <div class="every_inp">
          <BtnCommon :btnList="['查询']" @search="search" />
        </div>
      </li>
      <li>
        <div class="every_inp" style="width: 25%">
          <label>体检周期</label>
          <el-date-picker
            :picker-options="{ shortcuts: G_datePickerShortcuts }"
            class="select"
            v-model="physicalTime"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </div>
        <div class="every_inp">
          <label>体检套餐</label>
          <el-select
            class="select"
            v-model.trim="searchForm.clusCode"
            placeholder="请选择"
            size="small"
            filterable
            clearable
            :disabled="!searchForm.companyTimes"
          >
            <el-option
              v-for="(item, index) in clusterList"
              :key="index"
              :value="item.clusterCode"
              :label="item.clusterName"
            ></el-option>
          </el-select>
        </div>
        <div class="every_inp">
          <label>性别</label>
          <el-select
            v-model.trim="searchForm.sex"
            placeholder="请选择"
            size="small"
            clearable
            filterable
            class="select"
          >
            <el-option
              v-for="(item, index) in G_shareSexList"
              :key="index"
              :value="item.value"
              :label="item.label"
            ></el-option>
          </el-select>
        </div>
      </li>
    </ul>

    <div class="contDiv">
      <div class="leftCont">
        <div class="searchHear">
          <span class="title"> 已体检人员列表：</span>
          <BtnCommon :btnList="['生成清单']" @createList="createList" />
        </div>
        <div class="tabTitle">
          <p class="firstP">
            <span
              >已选人数：<span class="colorBlue">{{ selNum }}</span></span
            >
          </p>
          <span
            >总人数：<span class="colorBlue">{{ tableData.length }}</span></span
          >
          <p></p>
        </div>
        <div class="tableCont">
          <PublicTable
            ref="multipleTable"
            :viewTableList.sync="tableData"
            :theads.sync="theads"
            :tableLoading.sync="loading"
            @handleSelectionChange="handleSelRow"
            isCheck
            :columnWidth="columnWidth"
          >
          </PublicTable>
        </div>
      </div>
      <div class="rightCont">
        <div class="headerTitle">
          <span class="title"> 费用结算清单：</span>
          <BtnCommon
            :btnList="['导出', '打印', '结算', '结算记录']"
            @settlement="settlement"
            @settlementRecords="settlementRecords"
          />
        </div>

        <div class="rightTableCont">
          <PublicTable
            :viewTableList.sync="rightTableData"
            :theads.sync="righttheads"
            :tableLoading.sync="loading"
            :columnWidth="columnWidth"
          >
            <template #discount="{ scope }">
              <div
                class="cell_blue discount_div"
                @click="batchDiscountBtn(scope.row)"
              >
                {{ scope.row.discount }}
              </div>
            </template>
            <template #allPrice="{ scope }">
              <div class="redColor">
                {{ scope.row.allPrice }}
              </div>
            </template>
          </PublicTable>
        </div>
        <div class="price_wrap">
          <div class="colorTitlte">汇总:</div>
          <div class="statistics">
            <p>已检人数总计：<span class="colorTitlte">123</span></p>
            <p>拒检人数总计：<span class="colorTitlte">4444</span></p>
            <p>实收金额总计: <span class="redColor">4444.02元</span></p>
          </div>
        </div>
      </div>
    </div>
    <!-- 打折的登录 -->
    <el-dialog
      title="打折管理"
      :visible.sync="loginPropShow"
      top="20%"
      width="360px"
      height="300px"
    >
      <el-form
        :model="loginInfo"
        :rules="rules"
        ref="ruleForm"
        label-width="60px"
      >
        <el-form-item label="账号" prop="operatorCode">
          <el-input
            size="small"
            @keyup.enter.native="login"
            v-focus
            v-model="loginInfo.operatorCode"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
            type="password"
            @keyup.enter.native="login"
            size="small"
            show-password
            v-model="loginInfo.password"
            autocomplete="off"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="loginPropShow = false">取 消</el-button>
        <el-button type="primary" @click="login">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 打折弹窗 -->
    <el-dialog
      title="打折管理"
      :close-on-click-modal="false"
      :visible.sync="discountShow"
      top="20%"
      width="360px"
      height="300px"
    >
      <el-form
        :model="fixedInfo"
        :rules="discountRules"
        ref="discount_Ref"
        label-width="60px"
      >
        <div class="discountTitle">
          {{ authInfo.name }}医生的打折范围为：
          <span class="cell_blue" style="font-weight: 600">{{
            C_fixedPrice
          }}</span>
        </div>
        <el-form-item label="折扣" prop="discount">
          <el-input
            size="small"
            v-focus
            @keyup.enter.native="discountConfirm"
            v-model.trim="fixedInfo.discount"
            autocomplete="off"
            class="cell_blue"
          ></el-input>
        </el-form-item>
        <el-form-item label="">
          * 打折组合：{{ discountRow.itemName }}
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="discountShow = false">取 消</el-button>
        <el-button type="primary" @click="discountConfirm">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 结算 -->
    <el-dialog
      title="结算"
      :visible.sync="settleDialog"
      :before-close="handleClose"
      :wrapperClosable="false"
      width="360px"
    >
      <el-form :model="settleInfo" label-width="80px">
        <el-form-item label="发票抬头">
          <el-input
            size="small"
            v-focus
            v-model.trim="settleInfo.invoiceTitle"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="征信代码">
          <el-input
            size="small"
            v-model.trim="settleInfo.adCode"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="税务号">
          <el-input
            size="small"
            v-model.trim="settleInfo.taxNo"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="总金额">
          <el-input
            size="small"
            v-model.trim="settleInfo.allPrice"
            autocomplete="off"
            disabled
            class="allPrice"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer setleFooter">
        <BtnCommon :btnList="['确认结算']" @submitSetle="submitSetle" />
      </div>
    </el-dialog>
    <!-- 结算记录 -->
    <el-dialog
      title="结算记录"
      :visible.sync="recordDialog"
      :before-close="recordDialogClose"
      :wrapperClosable="false"
      class="recordDialog"
    >
      <div class="dialogHear">
        <div class="title">
          <div class="titleTime">结算日期</div>
          <el-date-picker
            :picker-options="{ shortcuts: G_datePickerShortcuts }"
            v-model.trim="operateDate"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :clearable="false"
            size="small"
          >
          </el-date-picker>
        </div>
        <div>
          <BtnCommon :btnList="['查询']" @search="searchRecord" />
        </div>
      </div>
      <div class="dialogTable">
        <PublicTable
          :viewTableList.sync="dialogTableData"
          :theads.sync="dialogTheads"
          :tableLoading.sync="loading"
        >
          <template #price="{ scope }">
            <div class="redColor">
              {{ scope.row.settlePrice }}
            </div>
          </template>
        </PublicTable>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import BtnCommon from './components/btnCommon.vue';
import PublicTable from '@/components/publicTable.vue';
import { mapGetters } from 'vuex';
import { dataUtils } from '@/common';
export default {
  name: 'groupSettlement',
  components: { BtnCommon, PublicTable },
  data() {
    var fixedPriceRange = (rule, value, callback) => {
      let valArr = this.C_fixedPrice.split('-');
      let minVal = valArr[0];
      let maxVal = valArr[1];
      let valNum = Number(value);
      if (!valNum) {
        callback(new Error(`内容必须为数字`));
        return;
      }
      if (valNum >= minVal && valNum <= maxVal) {
        callback();
      } else {
        callback(new Error(`请输入${this.C_fixedPrice}范围内的值`));
      }
    };
    return {
      companyList: [], //单位
      clusterList: [],
      physicalTime: [dataUtils.getDate(), dataUtils.getDate()],
      companyDeptList: [], //部门
      comTimesList: [], //单位次数组
      searchForm: {
        companyCode: '',
        companyDeptCode: '',
        companyTimes: null,
        PhysicalStartTime: '',
        PhysicalEndTime: '',
        clusCode: '',
        sex: null
      },
      loading: false,
      theads: {
        serialNo: '流水号',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        companyCode: '单位名称'
      },
      tableData: [],
      newTableData: [],
      columnWidth: { itemName: 200, companyCode: 200, serialNo: 140 },
      serialNoArr: [],
      selNum: 0,
      righttheads: {
        itemName: '组合名称',
        originalPrice: '登记单价',
        palTimePrice: '实时单价',
        unitPrice: '实收单价',
        discount: '折扣',
        checkedNum: '已检数量',
        rejectsnUM: '拒检数量',
        allPrice: '总价'
      },
      rightTableData: [],
      //打折登录
      loginPropShow: false,
      loginInfo: {
        operatorCode: '',
        password: ''
      },
      fixedInfo: {
        discount: ''
      },
      authInfo: {},
      rules: {
        operatorCode: [
          { required: true, message: '请输入账号', trigger: 'blur' }
        ],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
      },
      // 打折
      discountShow: false,
      discountRow: {}, //打折行数据
      discountRules: {
        discount: [
          { required: true, message: '请输入内容', trigger: 'blur' },
          { validator: fixedPriceRange, trigger: 'blur' }
        ]
      },
      //结算
      settleDialog: false,
      settleInfo: {
        invoiceTitle: '',
        adCode: '',
        taxNo: '',
        allPrice: ''
      },
      //结算记录
      recordDialog: false,
      operateDate: [dataUtils.getDate(), dataUtils.getDate()],
      recordSearchInfo: {
        beginTime: '',
        endTime: ''
      },
      dialogTheads: {
        setleDate: '结算时间',
        setlePrice: '结算金额',
        setleNum: '结算人数',
        invoiceTitle: '发票抬头',
        adCode: '征信代码',
        taxNo: '税务号',
        operatorName: '收费员'
      },
      dialogTableData: []
    };
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_sysOperator',
      'G_shareSexList',
      'G_datePickerShortcuts'
    ]),
    C_fixedPrice() {
      let discount = this.authInfo.maxDeductionPrice
        ? this.authInfo.maxDeductionPrice.toString().split('.')[1]
        : '00';
      return `${
        discount.length < 2
          ? this.authInfo.maxDeductionPrice + '0'
          : this.authInfo.maxDeductionPrice
      }-1.00`;
    }
  },
  mounted() {
    this.getCompany();
    this.search();
  },

  methods: {
    //获取单位下拉
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) {
        this.clearAll();
        return;
      }
      this.getDepartList(data[0]);
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.physicalTime = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.search();
    },
    //查询公司部门信息
    getDepartList(companyCode) {
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanyDepartment, {
          companyCode: companyCode,
          deptCode: '',
          deptName: ''
        })
        .then((r) => {
          //console.log("r", r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.companyDeptList = returnData || [];
        });
    },
    //获取体检次数
    getSimpleCompanyTimes(companyCode) {
      this.comTimesList = [];
      this.$ajax
        .post(this.$apiUrls.GetSimpleCompanyTimes, '', {
          query: { companyCode: companyCode }
        })
        .then((r) => {
          console.log('rrrr', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.$nextTick(function () {
            this.comTimesList = [];
            if (returnData.length > 0) {
              returnData.map((item) => {
                this.comTimesList.push({
                  label: item.companyTimes,
                  value: item.companyTimes
                });
              });
            }
          });
        });
    },
    //清空单位及其联动
    clearAll() {
      this.$nextTick(() => {
        this.companyDeptList = [];
        this.comTimesList = [];
        this.searchForm.companyDeptCode = '';
        this.searchForm.companyTimes = null;
      });
    },
    //获取套餐：
    getCluster() {
      this.$ajax
        .post(this.$apiUrls.GetCompanyCluster, '', {
          query: {
            companyCode: this.searchForm.companyCode,
            companyTimes: this.searchForm.companyTimes
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.clusterList = returnData || [];
        });
    },
    // 体检次数选择切换
    companyTimesChange() {
      if (this.searchForm.companyTimes) {
        this.getCluster();
      }
    },
    //已体检人员列表勾选
    selectAll(row) {
      console.log('[ row ]-579', row);
      this.serialNoArr = [];
      if (row.length < 1) {
        this.selNum = 0;
        this.newTableData = [];
        return;
      }
      this.newTableData = this.tableData;
      this.newTableData.map((item, index) => {
        this.serialNoArr.push(item.serialNo);
      });
      this.selNum = this.serialNoArr.length;
      console.log('[ this.serialNoArr ]-583', this.serialNoArr.length);
    },
    //操作勾选行
    handleSelRow(selection, row) {
      console.log('[ this.newTableData ]-595', this.newTableData);
      let selected = selection.length && selection.indexOf(row) !== -1; //判断是否选中
      console.log('[ selected ]-599', selected);
      if (selected) {
        this.newTableData.push(row);
      } else {
        let arr = [row.serialNo];
        console.log('[ arr ]-604', arr);
        const results = this.newTableData.filter(
          (item) => !arr.includes(item.serialNo)
        );
        this.newTableData = results;
      }
      this.serialNoArr = [];
      this.newTableData.map((item, index) => {
        this.serialNoArr.push(item.serialNo);
      });
      this.selNum = this.serialNoArr.length;
      console.log('[  this.newTableData ]-608', this.newTableData);
    },

    //查询
    search() {
      this.tableData = [];
      //测试数据
      for (var i = 0; i < 100; i++) {
        this.tableData.push({
          serialNo: '123456' * 1 + i,
          name: '测试' + (i + 1) * 1,
          sex: i % 2 == '1' ? '男' : '女',
          age: '22' * 1 + i,
          companyCode: '单位名称' + (i + 1) * 1
        });
      }
      //默认全选
      // this.$nextTick(() => {
      //   this.$refs.multipleTable.$refs.tableCom_Ref.toggleAllSelection();
      // });
      this.newTableData = this.tableData;
      return;
      if (!this.physicalTime) {
        this.searchForm.PhysicalStartTime = '';
        this.searchForm.PhysicalEndTime = '';
      } else {
        this.searchForm.PhysicalStartTime = this.physicalTime[0];
        this.searchForm.PhysicalEndTime = this.physicalTime[1];
      }
      const parameter = JSON.parse(JSON.stringify(this.searchForm));
      parameter.companyCode = parameter.companyCode?.[0] || '';
      parameter.companyTimes = parameter.companyTimes?.[1] || -1;
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanyDepartment, parameter)
        .then((r) => {
          let { success, returnData } = r.data;
          console.log('🚀 ~ search ~ returnData:', returnData);
          if (!success) return;

          if (returnData.length >= 1) {
            this.tableData = returnData;
            this.newTableData = returnData;
          } else {
            this.tableData = [];
            this.newTableData = [];
            this.$message({
              message: '暂无数据',
              type: 'success',
              showClose: true
            });
          }
        });
    },
    //生成清单
    createList() {
      console.log('[ this.serialNoArr ]-1153', this.serialNoArr);
      if (this.serialNoArr.length < 1) {
        this.$message({
          message: '请先选择要生成清单的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.rightTableData = [
        {
          itemName: '身高、体重、血压',
          originalPrice: '5.00',
          palTimePrice: '5.00',
          unitPrice: '4.00',
          discount: '1.00',
          checkedNum: '100',
          rejectsnUM: '0',
          allPrice: '400.00'
        }
      ];
      return;
      this.$ajax
        .post(this.$apiUrls.DeleteSettlementDaily, '', {
          query: { serialNo: this.serialNoArr }
        })
        .then((r) => {
          let { success } = r.data;
          if (!success) return;
          if (returnData.length >= 1) {
            this.rightTableData = returnData;
            this.$message({
              message: '生产清单成功',
              type: 'success',
              showClose: true
            });
          } else {
            this.rightTableData = [];
            this.$message({
              message: '暂无数据!',
              type: 'success',
              showClose: true
            });
          }
        });
    },
    //打折
    batchDiscountBtn(row) {
      console.log('[ row ]-527', row);
      this.loginPropShow = true;
      this.discountRow = row;
    },
    // 登录
    login() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.$ajax
            .post(this.$apiUrls.GetOperatorDiscount, '', {
              query: this.loginInfo
            })
            .then((r) => {
              let { success, returnData } = r.data;
              if (!success) return;
              this.loginPropShow = false;
              this.authInfo = returnData;
              this.discountShow = true;
            });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    // 打折的确定按钮
    discountConfirm() {
      this.$refs['discount_Ref'].validate((valid) => {
        if (valid) {
          let datas = {
            serialNo: this.discountRow.serialNo,
            discountOperCode: this.authInfo.operatorCode,
            discountOperName: this.authInfo.name,
            discount: this.fixedInfo.discount,
            ids: this.discountRow.id
          };
          console.log(datas);
          this.$ajax
            .post(this.$apiUrls.ReductioProjectsPrice, datas)
            .then((r) => {
              let { success, returnData } = r.data;
              if (!success) return;
              this.$message({
                message: '优惠成功！',
                type: 'success',
                showClose: true
              });
              this.discountShow = false;
              this.authInfo = {};
              this.discountRow = {};
              this.createList(); //打折后重新生成清单
            });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    //关闭抽屉
    handleClose() {
      this.settleDialog = false;
    },
    //结算
    settlement() {
      if (this.rightTableData.length < 1) {
        this.$message({
          message: '暂无结算数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.settleDialog = true;
    },
    //确认结算
    submitSetle() {
      this.$ajax
        .post(this.$apiUrls.RD_CodeFeeCls, this.settleInfo)
        .then((r) => {
          const { success } = r.data;
          if (!success) {
            return;
          }
          this.$message({
            message: '结算成功!!',
            type: 'success',
            showClose: true
          });
        });
    },
    //结算记录
    settlementRecords() {
      this.recordDialog = true;
    },
    //关闭结算记录弹窗
    recordDialogClose() {
      this.recordDialog = false;
    },
    //查询结算记录
    searchRecord() {
      if (!this.operateDate) {
        this.recordSearchInfo.beginTime = dataUtils.getDate();
        this.recordSearchInfo.endTime = dataUtils.getDate();
      } else {
        this.recordSearchInfo.beginTime = this.operateDate[0];
        this.recordSearchInfo.endTime = this.operateDate[1];
      }
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanyDepartment, this.recordSearchInfo)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          if (returnData.length >= 1) {
            this.dialogTableData = returnData;
          } else {
            this.dialogTableData = [];
            this.$message({
              message: '暂无数据',
              type: 'success',
              showClose: true
            });
          }
        });
    }
  }
};
</script>

<style lang="less" scoped>
.groupSettlement {
  display: flex;
  width: 100%;
  height: 100%;
  border-radius: 4px;
  display: flex;
  color: #2d3436;
  flex-direction: column;

  .group_head {
    background: #fff;

    padding: 10px 10px 0 10px;
    margin-bottom: 10px;
    li {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
    }

    .every_inp {
      display: flex;
      align-items: center;
      margin-right: 10px;
      label {
        font-size: 14px;
        font-weight: 600;
        margin-right: 10px;
        width: 74px;
        text-align: right;
      }
    }
  }

  .contDiv {
    flex: 1;
    display: flex;
    overflow: auto;
    .leftCont {
      width: 45%;
      background-color: #fff;
      padding: 18px;
      display: flex;
      flex-direction: column;
      .tabTitle {
        display: flex;
        align-items: center;
        font-size: 14px;
        margin-bottom: 10px;
        .firstP {
          margin-right: 20px;
        }
        .colorBlue {
          color: #1770df;
        }
      }
      .searchHear {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 18px;
        color: #2d3436;
        font-weight: 600;
        margin-bottom: 12px;
      }
      .tableCont {
        flex: 1;
        flex-shrink: 0;
        overflow: auto;
        border: 1px solid #d8dee1;
        border-radius: 4px;
      }
    }
    .rightCont {
      flex: 1;
      margin-left: 18px;
      background-color: #fff;
      padding: 18px;
      display: flex;
      flex-direction: column;
      overflow: auto;
      .headerTitle {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #2d3436;
        margin-bottom: 10px;
        .title {
          font-size: 24px;
          color: #2d3436;
        }
      }
      .rightTableCont {
        flex: 1;
        overflow: auto;
        border: 1px solid #d8dee1;
        border-radius: 4px;
        margin-bottom: 10px;
      }
    }
  }

  .price_wrap {
    font-size: 18px;
    .colorTitlte {
      font-size: 18px;
      color: #2d3436;
      margin-bottom: 10px;
      font-weight: 600;
    }
    .statistics {
      display: flex;
      align-items: center;
      justify-content: space-between;
      p {
        font-size: 14px;
      }
      span {
        font-weight: 600;
      }
    }
    .redColor {
      font-family: PingFangSC-Medium;
      font-size: 18px;
      color: #d63031;
    }
  }
  .discountTitle {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #2d3436;
    margin-bottom: 10px;
  }
  .el-form-item {
    margin-bottom: 10px;
    /deep/.el-input__inner {
      font-family: PingFangSC-Regular;
      font-size: 18px;
      color: #2d3436;
    }
    .allPrice {
      /deep/.el-input__inner {
        color: #d63031;
      }
    }
    .cell_blue {
      /deep/.el-input__inner {
        color: #1770df;
      }
    }
  }
  .recordDialog {
    /deep/.el-dialog {
      height: 60%;
      width: 770px;
      display: flex;
      flex-direction: column;
      .dialogHear {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
        margin-bottom: 10px;
        .title {
          display: flex;
          align-items: center;
          .titleTime {
            margin-right: 10px;
            font-weight: 600;
          }
        }
      }
      .dialogTable {
        flex: 1;
        flex-shrink: 0;
        overflow: auto;
        border: 1px solid #d8dee1;
        border-radius: 4px;
      }
    }
  }
  .setleFooter {
    display: flex;
    justify-content: flex-end;
  }
  /deep/.el-dialog__header {
    height: 50px;
    line-height: 50px;
    background: rgba(23, 112, 223, 0.2);
    padding: 0 18px;
  }
  /deep/.el-dialog__headerbtn {
    top: 10px;
    font-size: 30px;
  }
  /deep/.el-dialog__body {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 10px 18px 18px 18px;
    flex-shrink: 0;
    overflow: auto;
  }
  .redColor {
    color: #d63031;
  }
  .blueColor {
    font-family: PingFangSC-Medium;
    font-size: 18px;
    color: #1770df;
  }
  .discount_div {
    cursor: pointer;
  }
  .select {
    width: 100%;
  }
}
</style>
