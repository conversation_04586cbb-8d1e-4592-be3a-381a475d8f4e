<template>
  <!-- 发票管理 -->
  <div class="invoiceManage">
    <div class="header">
      <div
        class="tabs-item"
        :class="active === index ? 'active' : ''"
        v-for="(item, index) in tabsData"
        :key="index"
        @click="handleClick(item.view, index)"
      >
        {{ item.title }}
      </div>
    </div>
    <div class="content">
      <keep-alive>
        <component :is="componentName"></component>
      </keep-alive>
    </div>
  </div>
</template>

<script>
import InvoiceBuy from './components/invoiceManage/invoiceBuy';
import InvoiceProvide from './components/invoiceManage/invoiceProvide';
import InvoiceDeliver from './components/invoiceManage/invoiceDeliver';
import InvoiceTakeBack from './components/invoiceManage/invoiceTakeBack';
import InvoiceWriteOff from './components/invoiceManage/invoiceWriteOff';
export default {
  name: 'invoiceManage',
  components: {
    InvoiceBuy,
    InvoiceProvide,
    InvoiceDeliver,
    InvoiceTakeBack,
    InvoiceWriteOff
  },
  data() {
    return {
      tabsData: [
        {
          title: '发票购入',
          view: 'InvoiceBuy'
        },
        {
          title: '发票发放',
          view: 'InvoiceProvide'
        },
        {
          title: '发票核销表',
          view: 'InvoiceWriteOff'
        },
        {
          title: '发票转交',
          view: 'InvoiceDeliver'
        },
        {
          title: '发票收回',
          view: 'InvoiceTakeBack'
        }
      ],
      componentName: 'InvoiceBuy',
      active: 0
    };
  },
  methods: {
    handleClick(view, index) {
      this.componentName = view;
      this.active = index;
    }
  }
};
</script>

<style lang="less" scoped>
.invoiceManage {
  display: flex;
  flex-direction: column;
  height: 100%;
  color: #2d3436;
  .header {
    display: flex;
    background: #fff;
    padding-left: 8px;
    border-radius: 4px;
    margin-bottom: 18px;
  }
  .tabs-item {
    width: 88px;
    margin: 0 34px;
    padding: 18px 0;
    text-align: center;
    font-size: 16px;
    cursor: pointer;
  }
  .active {
    color: #1770df;
    border-bottom: 2px solid #1770df;
  }
  .content {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
  }
}
</style>
