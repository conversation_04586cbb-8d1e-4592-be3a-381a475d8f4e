import { mapGetters } from 'vuex';
import moment from 'moment';
import { dataUtils } from '@/common';
export default {
  data() {
    const validateInvoice = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入发票号'));
      } else {
        let reg = /^[\da-zA-Z]{10}$/;
        if (!reg.test(value)) {
          callback(new Error('必须输入10位字符'));
        } else if (this.invoiceForm.checkNewInvoice !== '') {
          this.$refs.invoiceForm.validateField('checkNewInvoice');
        }
        if (this.invoiceCopy === null) {
          this.$message({
            message: '没有当前发票号，请先去设置发票!',
            type: 'warning',
            showClose: true
          });
          return;
        }
        if (this.invoiceForm.newInvoice === this.invoiceCheck) {
          this.showNote = false;
        } else if (this.invoiceForm.newInvoice !== this.invoiceCheck) {
          this.showNote = true;
        }
        callback();
      }
    };
    const checkInvoice = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入确认新发票号'));
      } else {
        let reg = /^[\da-zA-Z]{10}$/;
        if (!reg.test(value)) {
          callback(new Error('必须输入10位字符'));
        } else if (value !== this.invoiceForm.newInvoice) {
          callback(new Error('两次输入发票号不一致'));
        }
        if (this.invoiceCopy === null) {
          this.$message({
            message: '没有当前发票号，请先去设置发票!',
            type: 'warning',
            showClose: true
          });
          return;
        }
        if (this.invoiceForm.newInvoice === this.invoiceCheck) {
          this.showNote = false;
        } else if (this.invoiceForm.newInvoice !== this.invoiceCheck) {
          this.showNote = true;
        }
        callback();
      }
    };
    const checkNote = (rule, value, callback) => {
      if (this.invoiceForm.newInvoice !== this.invoiceCheck) {
        if (!value) {
          callback(new Error('请输入发票号跳段备注'));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    return {
      inputShow: false, //修改发票号显示输入框
      searchDate: new Date(), //未缴费列表日期
      editForm: {
        operatorCode: '',
        settlementTime: [new Date(), new Date()]
      },
      cardForm: {},
      columnWidth: {
        patCode: 100,
        regNo: 130,
        tel: 120,
        companyName: 210,
        registerTime: 110,
        editInvoiceNo: 130
      },
      columnAlign: {
        totalPrice: 'right',
        bookKeepingPrice: 'right',
        selfPaidPrice: 'right',
        price: 'right'
      },

      unpaidTheads: {
        regNo: '体检号',
        name: '姓名',
        tel: '联系电话',
        sex: '性别',
        age: '年龄',
        feeType: '费别',
        companyName: '工作单位',
        patCode: '档案号',
        registerTime: '登记时间'
      },
      infoTheads: {
        feeClsName: '分类名称',
        totalPrice: '总金额',
        bookKeepingPrice: '记账金额',
        selfPaidPrice: '自费金额'
      },
      editTheads: {
        settlementTime: '结算日期',
        operatorCode: '操作员',
        invoiceNo: '发票号码',
        name: '姓名',
        price: '发票金额',
        editInvoiceNo: '修改后发票号',
        status: '状态',
        operation: '操作'
      },
      searchTheads: {
        status: '状态',
        receiver: '领取人',
        prefix: '发票前缀',
        startNo: '开始发票号',
        endNo: '结束发票号',
        currentNo: '当前发票号',
        lastUseDate: '最后使用时间'
      },
      invoiceShow: false, //显示修改发票号弹窗
      invoiceSearch: false, //显示发票号查询弹窗
      payShow: false, //显示缴费弹窗
      invoiceEdit: false, //显示发票修改弹窗
      cardPayShow: false, //显示卡支付弹窗
      radio: 1, //缴费单选
      cardRadio: 1, //卡支付单选
      index: '', //上下键记录index
      payInfo: {
        price: '0.00'
      }, //缴费信息
      invoiceForm: {
        newInvoice: '',
        checkNewInvoice: '',
        note: ''
      },
      invoiceFormRules: {
        newInvoice: [{ validator: validateInvoice, trigger: 'blur' }],
        checkNewInvoice: [{ validator: checkInvoice, trigger: 'blur' }],
        note: [{ validator: checkNote, trigger: 'blur' }]
      },
      combList: [], //组合列表
      basicClsData: [], //分类列表
      invoiceNoData: [], //发票号查询列表
      editInvoiceData: [], //修改发票号列表
      unpaidData: [], //未缴费列表
      currentRow: {}, //单前行选中
      userInfo: {}, //右边用户信息
      receivableAmount: '0.00', //应收金额
      paidInAmount: '0.00', //实收
      payPaidInAmount: '0.00', //实收金额
      payChange: '0.00', //找零
      disabled: false, //禁用
      showSuccess: false, //显示支付成功
      successName: '', //支付成功名
      successPrice: '', //支付成功价格
      invoiceHeader: '', //发票抬头
      iconName: '',
      invoiceCheck: '', //记录发票号
      count: 0, //找零计算结果
      invoiceCopy: {}, //记录双击发票
      showNote: false, //显示备注
      cancelBtn: false, //取消按钮状态
      loading: false,
      keyCode: null, //按键值
      payCode: '', //支付码
      lastTime: '',
      nextTime: '',
      lastCode: '',
      nextCode: ''
    };
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_userInfo', 'G_sysOperator'])
  },
  created() {
    this.getInvoice();
    if (this.$parent.nextInvoice === '0') {
      this.invoiceShow = true;
    }
  },
  mounted() {
    addEventListener('keyup', this.keyUp);
    addEventListener('keydown', this.keyDown);
  },
  activated() {
    addEventListener('keyup', this.keyUp);
    addEventListener('keydown', this.keyDown);
    if (this.$parent.nextInvoice === '0') {
      this.invoiceShow = true;
      this.getInvoice();
    }
  },
  beforeDestroy() {
    removeEventListener('keyup', this.keyUp);
    removeEventListener('keydown', this.keyDown);
  },
  deactivated() {
    removeEventListener('keyup', this.keyUp);
    removeEventListener('keydown', this.keyDown);
  },
  methods: {
    // 获取发票号
    getInvoice() {
      this.$ajax
        .post(this.$apiUrls.QueryCurrentInvoice, '', {
          query: { operCode: this.G_userInfo.codeOper.operatorCode }
        })
        .then((r) => {
          console.log('QueryCurrentInvoice: ', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.invoiceCopy = returnData;
          if (this.invoiceCopy === null) {
            this.$message({
              message: '请先去设置发票!',
              type: 'warning',
              showClose: true
            });
            return;
          }
          let currentNo =
            returnData.currentNo === null
              ? returnData.startNo
              : returnData.currentNo + 1;
          this.invoiceForm = {
            newInvoice: returnData.prefix + currentNo,
            checkNewInvoice: returnData.prefix + currentNo
          };
          this.invoiceCheck = returnData.prefix + currentNo;
          // this.$parent.nextInvoice = returnData.prefix + currentNo;
        });
    },
    // 获取发票号查询表格数据
    getInvoiceSegment() {
      this.$ajax
        .post(this.$apiUrls.ReadInvoiceSegment, '', {
          query: { operCode: this.G_userInfo.codeOper.operatorCode }
        })
        .then((r) => {
          console.log('ReadInvoiceSegment: ', r);
          let { success, returnData } = r.data;
          if (!success) return;
          returnData.map((item) => {
            item.lastUseDate = moment(item.lastUseDate).format('YYYY-MM-DD');
          });
          this.$nextTick(() => {
            this.invoiceNoData = returnData || [];
          });
        });
    },
    // 获取未缴费列表
    getUnpaidList() {
      if (this.searchDate === null) return;
      this.$ajax
        .post(this.$apiUrls.ReadUnpaidList, '', {
          query: {
            registerTime: '',
            regNo: this.userInfo.regNo
          }
        })
        .then((r) => {
          console.log('ReadUnpaidList: ', r);
          let { success, returnData } = r.data;
          if (!success) return;
          // 查体检号
          if (returnData === null || returnData.length === 0) {
            this.userInfo = {
              regNo: this.userInfo.regNo
            };
            this.userInfo.sex = '';
            this.combList = [];
            this.basicClsData = [];
            this.receivableAmount = '0.00';
            this.paidInAmount = '0.00';
          } else {
            this.userInfo = returnData[0];
            this.userInfo.sex = this.G_EnumList['Sex'][this.userInfo.sex];
            this.getCombsAndBasicCls(this.userInfo.regNo);
          }
        });
    },
    // 获取个人信息、组合、基础分类
    getCombsAndBasicCls(regNo) {
      this.$ajax
        .post(this.$apiUrls.ReadUnpaidCombsAndBasicCls, '', {
          query: {
            regNo: regNo
          }
        })
        .then((r) => {
          console.log('ReadUnpaidCombsAndBasicCls: ', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.combList = returnData.combs;
          this.basicClsData = returnData.basicCls;
          this.receivableAmount = returnData.receivableAmount;
          this.paidInAmount = returnData.paidInAmount;
        });
    },
    // 获取修改发票列表
    getInvoiceNoInfo() {
      if (this.editForm.settlementTime === null) return;
      let beginTime = this.editForm.settlementTime[0];
      let endTime = this.editForm.settlementTime[1];
      let data = {
        operatorCode: this.editForm.operatorCode,
        beginTime: moment(beginTime).format('YYYY-MM-DD'),
        endTime: moment(endTime).format('YYYY-MM-DD')
      };
      console.log('data: ', data);
      this.$ajax.post(this.$apiUrls.GetInvoiceNoInfo, data).then((r) => {
        console.log('GetInvoiceNoInfo: ', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.editInvoiceData = returnData.map((item) => {
          return {
            ...item,
            settlementTime: moment(item.settlementTime).format('YYYY-MM-DD'),
            inputShow: false,
            editInvoiceNo: '',
            status: ''
          };
        });
      });
    },
    // 打开缴费窗口创建结算
    createSettlement() {
      this.$ajax
        .post(this.$apiUrls.CreateSettlement, '', {
          query: { regNo: this.userInfo.regNo }
        })
        .then((r) => {
          console.log('CreateSettlement: ', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.payInfo = {
            ...returnData,
            payPlans: returnData.payPlans.map((item) => {
              return {
                ...item,
                payPrice: this.handlePrice(item.payPrice)
              };
            })
          };
          this.countPayPrice();
        });
    },
    // 未缴费表格点击
    unpaidRowClick(row) {
      console.log('row: ', row);
      if (row === undefined) return;
      this.currentRow = row;
      this.userInfo = row;
      this.getCombsAndBasicCls(row.regNo);
    },
    // 发票号查询弹窗
    invoiceSearchClick() {
      this.invoiceSearch = true;
      this.getInvoiceSegment();
    },
    // 未缴费列表查询
    searchClick() {
      if (this.searchDate === null) return;
      this.$ajax
        .post(this.$apiUrls.ReadUnpaidList, '', {
          query: {
            registerTime: moment(this.searchDate).format('YYYY-MM-DD'),
            regNo: ''
          }
        })
        .then((r) => {
          console.log('ReadUnpaidList: ', r);
          let { success, returnData } = r.data;
          if (!success) return;
          // 查日期
          returnData.map((item) => {
            item.registerTime = moment(item.registerTime).format('YYYY-MM-DD');
            item.sex = this.G_EnumList['Sex'][item.sex];
          });
          this.$nextTick(() => {
            this.unpaidData = returnData || [];
          });
        });
    },
    // 发票查询表格双击
    invoiceSearchDblclick(row) {
      console.log('row: ', row);
      this.invoiceSearch = false;
      let currentNo = row.currentNo === null ? row.startNo : row.currentNo + 1;
      this.showNote = false;
      this.invoiceCopy = row;
      this.invoiceCheck = row.prefix + currentNo;
      this.invoiceForm = {
        newInvoice: row.prefix + currentNo,
        checkNewInvoice: row.prefix + currentNo
      };
    },
    // 发票号修改弹窗确定
    invoiceClick() {
      this.$refs.invoiceForm.validate((valid) => {
        if (valid) {
          if (this.invoiceForm.newInvoice !== this.invoiceCheck) {
            let data = {
              prefix: this.invoiceCopy.prefix,
              startNo: this.invoiceCopy.startNo,
              endNo: this.invoiceCopy.endNo,
              currentNo:
                this.invoiceCopy.currentNo === null
                  ? 0
                  : this.invoiceCopy.currentNo,
              newInvoiceNo: this.invoiceForm.newInvoice.slice(2),
              note: this.invoiceForm.note
            };
            console.log('data: ', data);
            this.$ajax
              .post(this.$apiUrls.ConfirmNewInvoice, data, { timeout: 120000 })
              .then((r) => {
                console.log('ConfirmNewInvoice: ', r);
                let { success, returnData } = r.data;
                if (!success) return;
                this.$message({
                  message: '修改发票号成功!',
                  type: 'success',
                  showClose: true
                });
                this.getInvoice();
                this.invoiceShow = false;
                this.showNote = false;
                this.$parent.nextInvoice = this.invoiceForm.newInvoice;
                this.$nextTick(() => {
                  this.resetForm('invoiceForm');
                });
              });
          } else {
            this.invoiceShow = false;
            this.$parent.nextInvoice = this.invoiceForm.newInvoice;
            this.searchClick();
            this.$nextTick(() => {
              this.resetForm('invoiceForm');
            });
          }
        }
      });
    },
    // 发票修改查询
    editSearchClick() {
      this.getInvoiceNoInfo();
    },
    // 发票修改保存
    saveClick() {
      let type = this.editInvoiceData.findIndex((item) => {
        return item.status === '已修改';
      });
      if (type === -1) {
        this.$message({
          message: '请先编辑需要修改的发票!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let invoiceNo = this.editInvoiceData
        .filter((item) => item.status == '已修改')
        .map((item) => {
          return {
            invoiceNoCurrent: item.invoiceNo,
            invoiceNoNew: item.editInvoiceNo
          };
        });
      let data = {
        operatorCode: this.G_userInfo.codeOper.operatorCode,
        invoiceNoCurrentNews: invoiceNo
      };
      console.log('data: ', data);
      this.$ajax.post(this.$apiUrls.UpdateInvoiceNo, data).then((r) => {
        console.log('UpdateInvoiceNo: ', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.$message({
          message: '修改发票成功!',
          type: 'success',
          showClose: true
        });
        this.invoiceEdit = false;
        this.invoiceShow = true;
        this.getInvoice();
        this.getInvoiceNoInfo();
      });
    },
    // 发票修改编辑按钮
    editRowClick(row) {
      console.log('row: ', row);
      row.editInvoiceNo = row.invoiceNo;
      row.inputShow = true;
    },
    // 发票修改输入框失去焦点
    editInputBlur(row) {
      if (row.editInvoiceNo) {
        let reg = /^[\da-zA-Z]{10}$/;
        if (!reg.test(row.editInvoiceNo)) {
          this.$message({
            message: '请输入10位字符的发票号!',
            type: 'warning',
            showClose: true
          });
          return;
        }
        if (row.invoiceNo !== row.editInvoiceNo && row.editInvoiceNo !== '') {
          row.status = '已修改';
        }
        row.inputShow = false;
      }
    },
    // 缴费弹窗
    payClick() {
      if (JSON.stringify(this.userInfo) === '{}') {
        this.$message({
          message: '请选择需要缴费的人!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.loading = false;
      this.payShow = true;
      this.showSuccess = false;
      this.iconName = '';
      this.createSettlement();
      this.payChange = '0.00';
    },
    // 计算找零
    inputChange(index, item) {
      // 现金支付
      if (item.payType === '1') {
        let change = 0;
        change = this.handlePrice(
          ~~(item.payPrice * 100 - this.payInfo.price * 100).toFixed() / 100
        );
        //其他支付方式有支付
        if (this.payInfo.payPlans.indexOf(item.hasPaid === 0)) {
          let allPrice = 0;
          this.payInfo.payPlans.map((pay) => {
            allPrice += pay.payPrice * 100;
          });
          allPrice = this.handlePrice((allPrice / 100).toFixed(2));
          if (allPrice > this.payInfo.price) {
            this.payChange = this.handlePrice(
              ~~(allPrice * 100 - this.payInfo.price * 100).toFixed() / 100
            );
          } else {
            this.payChange = '0.00';
          }
        } else {
          if (item.payPrice > this.payInfo.price) {
            this.payChange = change;
          } else {
            this.payChange = '0.00';
          }
        }
      }
    },
    // 计算实付金额
    countPayPrice() {
      let allPrice = 0;
      this.payInfo.payPlans.map((item, i) => {
        allPrice += item.payPrice * 100;
      });
      this.payPaidInAmount = this.handlePrice((allPrice / 100).toFixed(2));
      this.count =
        ~~(this.payInfo.price * 100 - this.payPaidInAmount * 100).toFixed() /
        100;
      this.$nextTick(() => {
        if (Number(this.payPaidInAmount) === this.payInfo.price) {
          this.disabled = false;
        } else {
          this.disabled = true;
        }
      });
    },
    // 支付输入框获取焦点
    payFocus(index, item) {
      this.index = index;
      this.showSuccess = false;
      this.payChange = '0.00';
      this.payInfo.payPlans.map((el, i) => {
        if (this.payPaidInAmount === '0.00') {
          if (i === index) {
            el.payPrice = this.handlePrice(this.payInfo.price);
          } else {
            el.payPrice = 0;
          }
        } else {
          if (el.hasPaid) {
            return;
          }
          if (i === index) {
            el.payPrice = this.count;
          } else {
            el.payPrice = 0;
          }
        }
      });
      if (item.payType === '1') {
        this.iconName = '';
      } else if (item.payType === '2') {
        this.iconName = 'icon-icon-test';
      } else if (item.payType === '3') {
        this.iconName = 'icon-saoma';
      } else if (item.payType === '4') {
        this.iconName = 'icon-saoma';
      } else if (item.payType === '5') {
        this.iconName = 'icon-icon-test';
      }
    },
    // 单个支付按钮
    singlePayClick(item) {
      if (this.loading && item.payPrice === 0) {
        return;
      }
      if (item.payPrice === 0) {
        this.$message({
          message: '请输入金额!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (Number(this.payPaidInAmount) === this.payInfo.price) {
        this.$message({
          message: '实收金额已达到应收金额，无需再支付!',
          type: 'warning',
          showClose: true
        });
        item.payPrice = 0;
        return;
      }
      let allPrice = 0;
      this.payInfo.payPlans.map((pay) => {
        allPrice += pay.payPrice * 100;
      });
      allPrice = this.handlePrice((allPrice / 100).toFixed(2));
      if (
        allPrice > this.payInfo.price &&
        this.payInfo.payPlans.indexOf(item.hasPaid === 0) &&
        item.payType !== '1'
      ) {
        this.$message({
          message: '当前支付金额和实收金额已大于应收金额!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let payPrice = item.payPrice;
      if (item.payType === '1') {
        payPrice = item.payPrice - this.payChange;
      }
      if (item.payType === '3' || item.payType === '4') {
        this.loading = true;
        this.$refs[`input_ref${this.index}`][0].blur();
        // 监听扫码枪事件
        window.document.onkeypress = (e) => {
          if (window.event) {
            // IE
            this.nextCode = e.keyCode;
          } else if (e.which) {
            // Netscape/Firefox/Opera
            this.nextCode = e.which;
          }
          if (e.which === 13) {
            // 键盘回车事件
            if (this.payCode.length < 3) return; // 扫码枪的速度很快，手动输入的时间不会让code的长度大于2，所以这里不会对扫码枪有效
            console.log('扫码结束。');
            console.log('条形码：', this.payCode);
            // 获取到扫码枪输入的内容，做别的操作
            if (this.payCode) {
              this.singlePay(item, payPrice);
              // 回车后输入清空
              this.payCode = '';
              this.lastTime = 0;
            }
            return;
          }
          this.nextTime = new Date().getTime();
          if (!this.lastTime && !this.lastCode) {
            this.payCode = ''; // 清空上次的条形码
            this.payCode += e.key;
            console.log('扫码开始---', this.payCode);
          }
          if (
            this.lastCode &&
            this.lastTime &&
            this.nextTime - this.lastTime > 500
          ) {
            // 当扫码前有keypress事件时,防止首字缺失
            this.payCode = e.key;
            console.log('防止首字缺失。。。', this.payCode);
          } else if (this.lastCode && this.lastTime) {
            this.payCode += e.key;
            console.log('扫码中。。。', this.payCode);
          }
          this.lastCode = this.nextCode;
          this.lastTime = this.nextTime;
        };
        return;
      }
      this.singlePay(item, payPrice);
      this.$refs[`input_ref${this.index}`][0].blur();
    },
    // 支付请求
    singlePay(item, payPrice) {
      let data = {
        settleNo: this.payInfo.settleNo,
        payCode: this.payCode,
        payType: item.payType,
        payName: item.payName,
        payPrice: payPrice
      };
      console.log('data: ', data);
      this.$ajax.post(this.$apiUrls.SinglePay, data).then((r) => {
        console.log('SinglePay: ', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.showSuccess = true;
        this.loading = false;
        this.successName = returnData.payName;
        this.successPrice = this.handlePrice(returnData.payPrice);
        this.createSettlement();
      });
    },
    // 取消扫码
    cancelCode() {
      this.loading = false;
      this.payCode = '';
      this.lastTime = 0;
    },
    // 单个退款
    refundClick(item) {
      this.$confirm(`是否确定退款?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$ajax
          .post(this.$apiUrls.CancelPaySingle, '', {
            query: { settleNo: this.payInfo.settleNo, payID: item.payID }
          })
          .then((r) => {
            console.log('CancelPaySingle: ', r);
            let { success, returnData } = r.data;
            if (!success) return;
            this.$message({
              message: '退款成功!',
              type: 'success',
              showClose: true
            });
            this.showSuccess = false;
            this.payChange = '0.00';
            this.count = 0;
            this.createSettlement();
          });
      });
    },
    // 全部退款
    refundAll() {
      let type = this.payInfo.payPlans.findIndex((item) => {
        return item.hasPaid;
      });
      if (type === -1) {
        this.$message({
          message: '当前缴费没有可退款!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm(`是否确定全部退款?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$ajax
          .post(this.$apiUrls.CancelPayAll, '', {
            query: { settleNo: this.payInfo.settleNo }
          })
          .then((r) => {
            console.log('CancelPayAll: ', r);
            let { success, returnData } = r.data;
            if (!success) return;
            this.$message({
              message: '全部退款成功!',
              type: 'success',
              showClose: true
            });
            this.showSuccess = false;
            this.payChange = '0.00';
            this.count = 0;
            this.createSettlement();
          });
      });
    },
    // 保存结算
    saveSettlement() {
      if (this.$parent.nextInvoice === '0') {
        this.$message({
          message: '发票号不能为空!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let prefix = this.$parent.nextInvoice.slice(0, -8);
      let number = this.$parent.nextInvoice.slice(2);
      let data = {
        settleNo: this.payInfo.settleNo,
        regNo: this.userInfo.regNo,
        price: this.payPaidInAmount,
        invoiceHeader: this.invoiceHeader,
        invPrefix: prefix,
        invNumber: number
      };
      console.log('data: ', data);
      this.$ajax.post(this.$apiUrls.FinishSettlement, data).then((r) => {
        console.log('FinishSettlement: ', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.$message({
          message: '结算成功!',
          type: 'success',
          showClose: true
        });
        this.payShow = false;
        this.searchClick();
        this.clearClick();
        this.$parent.nextInvoice = prefix + (~~number + 1);
      });
    },
    // 清空
    clearClick() {
      this.currentRow = {};
      this.userInfo = {};
      this.paidInAmount = '0.00';
      this.receivableAmount = '0.00';
      this.combList = [];
      this.basicClsData = [];
    },
    // 卡支付弹窗
    cardPayClick() {
      this.cardPayShow = true;
    },
    // 取消
    cancel() {
      if (this.cancelBtn === true) {
        this.invoiceShow = false;
        this.showNote = false;
        this.$nextTick(() => {
          this.resetForm('invoiceForm');
        });
      } else {
        if (this.invoiceCopy === null) {
          this.$router.push({ name: 'invoiceManage' });
        } else {
          this.$router.push('/');
        }
        this.invoiceShow = false;
        this.showNote = false;
        this.$nextTick(() => {
          this.resetForm('invoiceForm');
        });
      }
    },
    // 发票编辑弹窗
    invoiceEditClick() {
      this.invoiceEdit = true;
      this.editForm.operatorCode = this.G_userInfo.codeOper.operatorCode;
      this.getInvoiceNoInfo();
    },
    // 发票修改表格双击
    editRowDblclick(row) {
      this.inputShow = true;
      console.log('row: ', row);
    },

    // 键盘按下事件
    keyDown(e) {
      if (e.keyCode == 9) {
        e.preventDefault();
      }
      this.event = e;
    },
    // 键盘释放事件
    keyUp(e) {
      e.preventDefault();
      e.stopPropagation();
      console.log(e.keyCode);
      if (!this.payShow) return;
      this.nextFocus(e);
    },
    // 上下键获取下一输入框焦点
    nextFocus(e) {
      if (e.keyCode == 40) {
        // 下键
        if (this.index === '') {
          this.index = 0;
          this.$nextTick(() => {
            this.$refs[`input_ref${this.index}`][0].focus();
          });
          return;
        }
        if (this.index === 4) {
          // 判断是否是最后一个
          this.index = 0;
          this.$nextTick(() => {
            this.$refs[`input_ref${this.index}`][0].focus();
          });
          return;
        }
        this.index += 1;
        this.$nextTick(() => {
          this.$refs[`input_ref${this.index}`][0].focus();
        });
      } else if (e.keyCode == 38) {
        // 上键
        if (this.index === 0) {
          // 判断是否是第一个
          this.index = 4;
          this.$nextTick(() => {
            this.$refs[`input_ref${this.index}`][0].focus();
          });
          return;
        }
        this.index -= 1;
        this.$nextTick(() => {
          this.$refs[`input_ref${this.index}`][0].focus();
        });
      }
    },
    // 状态颜色处理
    statusColor(val) {
      let color = '';
      switch (val) {
        case 1:
          color = 'cell_blue';
          break;
        case 2:
          color = 'cell_green';
          break;
        case 3:
          color = '';
          break;
      }
      return color;
    },
    handlePrice: dataUtils.handlePrice,
    // 重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields();
    }
  }
};
