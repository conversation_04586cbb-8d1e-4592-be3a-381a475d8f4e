<template>
  <!-- 个人单位记账报表 -->
  <div class="groupPersonalAccountingReport">
    <div class="groupPersonalAccountingReport-wrap">
      <div class="search-operate">
        <div class="operate-item">
          <span style="width: 70px">登记时间</span>
          <el-date-picker
            :picker-options="{ shortcuts: G_datePickerShortcuts }"
            v-model.trim="searchInfo.date"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :clearable="false"
            size="small"
            class="select"
          >
          </el-date-picker>
        </div>
        <div class="operate-item">
          <span>体检单位</span>
          <el-select
            class="select"
            v-model.trim="searchInfo.companyCodes"
            placeholder="请选择"
            size="small"
            filterable
            clearable
            multiple
            collapse-tags
            :filter-method="filterMethod"
            @visible-change="companyVisibleChange"
          >
            <el-option
              v-for="item in companyList"
              :key="item.companyCode"
              :value="item.companyCode"
              :label="item.companyName"
            ></el-option>
          </el-select>
        </div>
        <div class="operate-item">
          <span>项目筛选</span>
          <el-select
            placeholder="请选择"
            size="small"
            clearable
            v-model="searchInfo.isItem"
            class="select"
          >
            <el-option label="全部" :value="0"></el-option>
            <el-option label="套餐内" :value="1"></el-option>
            <el-option label="加项" :value="2"></el-option>
          </el-select>
        </div>
        <div class="operate-item">
          <span>是否缴费</span>
          <el-select
            placeholder="请选择"
            size="small"
            clearable
            v-model="searchInfo.isPay"
            class="select"
          >
            <el-option label="全部" :value="0"></el-option>
            <el-option label="未缴费" :value="1"></el-option>
            <el-option label="已缴费" :value="2"></el-option>
          </el-select>
        </div>
      </div>
      <div class="search-operate">
        <div class="operate-item">
          <span>是否体检</span>
          <el-select
            class="select"
            v-model.trim="searchInfo.isActive"
            placeholder="请选择"
            size="small"
            clearable
          >
            <el-option label="全部" :value="0"></el-option>
            <el-option label="未体检" :value="1"></el-option>
            <el-option label="已体检" :value="2"></el-option>
          </el-select>
        </div>
        <div class="operate-item">
          <el-checkbox v-model="searchInfo.isShowMaterial"
            >显示材料费</el-checkbox
          >
        </div>
        <div class="operate-item">
          <BtnCommon :btnList="['查询', '打印']" @search="searchClick">
            <template slot="footAdd">
              <el-button
                size="small"
                class="yellow_btn btn"
                @click="exports"
                icon="iconfont icon-daochu"
                >导出</el-button
              >
            </template>
          </BtnCommon>
        </div>
      </div>
      <div class="legend">
        <span>总金额：{{ infoList.totalPrice }}元</span>
        <span>总加项金额：{{ infoList.totalAddItemPrice }}元</span>
        <span>总拒检金额：{{ infoList.totalRefuseCheckPrice }}元</span>
      </div>
      <div class="table-wrap">
        <div class="table-item" v-for="(item, index) in tableData" :key="index">
          <div class="table">
            <PublicTable
              :viewTableList.sync="item.personData"
              :theads.sync="theads"
              :isSortShow="false"
              :columnWidth="columnWidth"
            >
            </PublicTable>
          </div>
          <div class="table-legend">
            <span>个人总金额：{{ item.personTotalPrice }}元</span>
            <span>个人总加项金额：{{ item.personTotalAddItemPrice }}元</span>
            <span
              >个人总拒检金额：{{ item.personTotalRefuseCheckPrice }}元</span
            >
          </div>
        </div>
      </div>
      <!-- <ul class="info-wrap">
        <li>
          <span>制表时间：{{ infoList.tabulationDate }}</span>
          <span>制表：{{ G_userInfo.codeOper.name }}</span>
        </li>
        <li>
          <span>
            1.体检日期：{{ infoList.beinDate }}至{{ infoList.endDate }}
          </span>
        </li>
        <li>
          <span>2.单位体检次数：{{ infoList.companyTimes }}</span>
        </li>
        <li>
          <span>3.收款单位：{{ infoList.payeeCompany }}</span>
        </li>
        <li>
          <span>4.开户银行：{{ infoList.openBank }}</span>
          <span>账号：{{ infoList.bankAccount }}</span>
        </li>
        <li>
          <span>
            5.纳税人识别号(社会信用代码)：{{ infoList.socialCreditCode }}
          </span>
        </li>
        <li>
          <span>6.地址：{{ infoList.address }}</span>
        </li>
      </ul> -->
    </div>
  </div>
</template>

<script>
import BtnCommon from './components/btnCommon.vue';
import PublicTable from '@/components/publicTable.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
export default {
  name: 'groupPersonalAccountingReport',
  components: { BtnCommon, PublicTable },
  data() {
    return {
      searchInfo: {
        date: [dataUtils.getDate(), dataUtils.getDate()],
        companyCodes: [],
        isItem: 0,
        isPay: 0,
        isActive: 0,
        isShowMaterial: false
      },
      companyList: [],
      companyListCopy: [],
      options: [],
      casValue: [],
      shareScopeEnd: [],
      deptCodeList: [],
      tableData: [],
      theads: {
        operatorCode: '联系人工号',
        operatorName: '联系人',
        companyCode: '单位代码',
        companyName: '单位名称',
        activeTime: '体检日期',
        regNo: '体检号',
        name: '姓名',
        clusName: '套餐名称',
        combCode: '组合代码',
        combName: '组合名称',
        originalPrice: '价格',
        discount: '折扣',
        price: '金额',
        addItemPrice: '加项金额',
        refuseCheckPrice: '拒检金额'
      },
      infoList: {
        totalAddItemPrice: 0,
        totalPrice: 0,
        totalRefuseCheckPrice: 0
      },
      columnWidth: {
        operatorCode: 90,
        companyName: 200,
        activeTime: 100,
        regNo: 130,
        clusName: 130,
        combName: 180
      }
    };
  },
  created() {
    this.getCompany();
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_userInfo', 'G_datePickerShortcuts'])
  },
  methods: {
    //获取单位下拉
    getCompany() {
      this.$ajax.post(this.$apiUrls.Company).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData;
        this.companyListCopy = JSON.parse(JSON.stringify(this.companyList));
      });
    },
    filterMethod(val) {
      this.searchInfo.companyCode = val;
      if (val.trim() == '') {
        this.companyList = dataUtils.deepCopy(this.companyListCopy);
      } else {
        this.companyList = this.companyListCopy.filter(
          (item) =>
            item.companyName.includes(val) || item.companyCode.includes(val)
        );
      }
    },
    companyVisibleChange(data) {
      if (data)
        this.companyList = JSON.parse(JSON.stringify(this.companyListCopy));
    },
    // 查询
    searchClick() {
      if (this.searchInfo.companyCodes.length === 0) {
        this.$message({
          showClose: true,
          message: '请选择单位!',
          type: 'warning'
        });
        return;
      }
      let [beinDate, endDate] = this.searchInfo.date;
      let data = {
        beinDate: beinDate,
        endDate: endDate,
        companyCodes: this.searchInfo.companyCodes,
        isItem: this.searchInfo.isItem,
        isPay: this.searchInfo.isPay,
        isActive: this.searchInfo.isActive,
        isShowMaterial: this.searchInfo.isShowMaterial
      };
      this.$ajax
        .post(this.$apiUrls.CompanyPersonAccountingReport, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) {
            this.tableData = [];
            this.infoList = {
              totalAddItemPrice: 0,
              totalPrice: 0,
              totalRefuseCheckPrice: 0
            };
          } else {
            this.tableData = returnData.personAccount || [];
            this.infoList = {
              totalAddItemPrice: returnData.totalAddItemPrice,
              totalPrice: returnData.totalPrice,
              totalRefuseCheckPrice: returnData.totalRefuseCheckPrice
            };
          }
        });
    },
    // 单位勾选切换
    getChecked(val) {
      let changeFlag = false;
      let changeItem = [];
      if (this.shareScopeEnd.length == 0) {
        this.casValue = val;
      } else {
        // 与原数组比对
        this.casValue.forEach((item) => {
          if (item[0] !== this.shareScopeEnd[0][0]) {
            // 一级标签不同
            changeFlag = true;
            changeItem.push(item);
          }
        });
      }
      if (changeFlag) {
        this.casValue = [];
        this.casValue = changeItem;
      }
      this.shareScopeEnd = this.casValue;
      if (val.length !== 0) {
        this.searchInfo.companyCode = this.casValue[0][0];
        this.$nextTick(() => {
          this.searchInfo.companyName =
            this.$refs.cascader_ref.checkedNodes[0].parent.label;
        });
        let companyTimes = [];
        this.casValue.map((item) => {
          if (item) {
            companyTimes.push(item[1]);
          }
        });
        this.searchInfo.companyTimes = companyTimes;
      }
      this.getDeptCode();
    },
    // 导出
    exports() {}
  }
};
</script>

<style lang="less" scoped>
.groupPersonalAccountingReport {
  color: #2d3436;
  display: flex;
  flex-direction: column;
  .select {
    width: 100%;
  }
  .groupPersonalAccountingReport-wrap {
    background: #fff;
    flex: 1;
    overflow: auto;
    padding: 10px;
    display: flex;
    flex-direction: column;
  }
  .search-operate {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
  .operate-item {
    margin-right: 10px;
    display: flex;
    align-items: center;
    span {
      margin-right: 10px;
      font-weight: 600;
      font-size: 14px;
      width: 78px;
      &:last-child {
        margin-right: 0;
      }
    }
  }
  .legend {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 10px;
    span {
      margin-right: 80px;
    }
  }
  .table-wrap {
    flex: 1;
    overflow: auto;
  }
  .table-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 40px;
    border: 1px solid #d8dee1;
    border-radius: 4px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .table-legend {
    font-size: 14px;
    font-weight: 600;
    background: rgba(23, 112, 223, 0.2);
    padding: 10px 0;
    display: flex;
    justify-content: space-around;
    align-items: center;
    span {
      margin-right: 40px;
    }
  }
  .table {
    flex: 1;
    overflow: auto;
    /deep/.el-table--scrollable-y .el-table__body-wrapper {
      height: 600px !important;
    }
    /deep/.el-table__body-wrapper {
      height: 600px !important;
    }
  }
  .total {
    background: rgba(23, 112, 223, 0.2);
    padding: 10px;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
  }
  .info-wrap {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 10px;
    li {
      line-height: 26px;
      span {
        margin-right: 20px;
      }
    }
  }
}
</style>
