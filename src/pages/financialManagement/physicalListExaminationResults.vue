<template>
  <!-- 体检项目结果清单 -->
  <div class="physicalListExaminationResults">
    <div class="main">
      <div class="searchBar">
        <div class="search-list">
          <div class="list-item">
            <span style="width: 87px">审核时间</span>
            <el-date-picker
              :picker-options="{ shortcuts: G_datePickerShortcuts }"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              v-model="dateRange"
              :clearable="false"
              @change="search"
            >
            </el-date-picker>
          </div>
          <div class="list-item">
            <el-radio-group v-model="searchInfo.peType" @change="radioChange">
              <el-radio :label="0">所有</el-radio>
              <el-radio :label="1">个人</el-radio>
              <el-radio :label="2">单位</el-radio>
            </el-radio-group>
          </div>
          <div class="list-item">
            <span style="width: 36px">单位</span>
            <el-cascader
              ref="company_cascader_ref"
              v-model="searchInfo.companyCode"
              :filter-method="filterMethod"
              :options="companyList"
              :props="{ multiple: false }"
              clearable
              filterable
              size="small"
              collapse-tags
              @change="companyChange"
            >
            </el-cascader>
          </div>
          <div class="list-item">
            <span style="width: 36px">部门</span>
            <el-select
              class="select"
              v-model.trim="searchInfo.companyDeptCode"
              placeholder="请选择"
              size="small"
              filterable
              clearable
              :disabled="isHavue"
              @change="search"
            >
              <el-option
                v-for="(item, index) in companyDeptList"
                :key="index"
                :label="item.deptName"
                :value="item.deptCode"
              ></el-option>
            </el-select>
          </div>
          <BtnCommon
            :btnList="['查询', '导出']"
            @search="search"
            @exports="exportTable"
          />
        </div>
        <div class="search-list">
          <div class="list-item">
            <span style="width: 130px">小结过滤:</span>
            <el-input
              class="input-new-tag"
              v-model="searchInfo.resultKeyword"
              size="small"
              clearable
              placeholder="请输入"
              @keyup.enter.native="search"
            >
            </el-input>
          </div>
          <div class="list-item">
            <span style="width: 36px">科室</span>
            <el-select
              placeholder="请选择"
              size="small"
              filterable
              clearable
              v-model="searchInfo.departmentCode"
              class="input"
              @change="search"
            >
              <el-option
                v-for="item in G_codeDepartment"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
          <div class="list-item">
            <span style="width: 72px">组合</span>
            <el-select
              placeholder="请选择"
              size="small"
              filterable
              clearable
              v-model="searchInfo.combCode"
              class="input"
              @change="search"
            >
              <el-option
                v-for="item in codeItemCombList"
                :key="item.combCode"
                :label="item.combName"
                :value="item.combCode"
              >
              </el-option>
            </el-select>
          </div>
          <div class="list-item">
            <span style="width: 36px">项目</span>
            <el-select
              v-model="searchInfo.itemCode"
              placeholder="请选择"
              size="small"
              style="width: 100%"
              clearable
              filterable
              @change="search"
            >
              <el-option
                v-for="item in codeItemList"
                :key="item.itemCode"
                :label="item.itemName"
                :value="item.itemCode"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="search-list">
          <div class="list-item radioDiv">
            <el-radio-group v-model="searchInfo.checkCls" @change="search">
              <el-radio :label="-1">全部</el-radio>
              <el-radio :label="0">一般检查</el-radio>
              <el-radio :label="1">医生检查</el-radio>
              <el-radio :label="2">功能检查</el-radio>
              <el-radio :label="3">检验检查</el-radio>
            </el-radio-group>
          </div>
          <div style="font-weight: bold">记录数：{{ total }}</div>
        </div>
      </div>
      <div class="main-table" v-loading="loading">
        <PublicTable2
          :theads="thead"
          ref="table_ref"
          rowKey="rowKey"
          :pageSize="200"
          :url="$apiUrls.PeItemResultListQuery"
          :params="params"
          remoteByPage
          @request-finally="requestFinally"
          :elStyle="{
            'show-selection': false
          }"
        >
          <template #hint="{ scope }">
            <div class="redDiv" v-if="scope.row.hint">
              {{ scope.row.hint }}
            </div>
            <div v-else>
              {{ scope.row.hint }}
            </div>
          </template>
          <template #combResult="{ scope }">
            <div class="floatPopup_div">
              <el-popover
                popper-class="floatPopup_popover"
                width="500"
                trigger="hover"
              >
                <ul class="popover_ul">
                  <li>
                    <span>{{ scope.row.combResult }}</span>
                  </li>
                </ul>
                <div slot="reference" class="popover_div">
                  >{{ scope.row.combResult }}
                </div>
              </el-popover>
            </div>
          </template>
          <template #itemResult="{ scope }">
            <div class="floatPopup_div">
              <el-popover
                popper-class="floatPopup_popover"
                width="500"
                trigger="hover"
              >
                <ul class="popover_ul">
                  <li>
                    <span>{{ scope.row.itemResult }}</span>
                  </li>
                </ul>
                <div slot="reference" class="popover_div">
                  >{{ scope.row.itemResult }}
                </div>
              </el-popover>
            </div>
          </template>
          <template #limit="{ scope }">
            <div class="floatPopup_div">
              <el-popover
                popper-class="floatPopup_popover"
                width="500"
                trigger="hover"
              >
                <ul class="popover_ul">
                  <li>
                    <span>{{ scope.row.limit }}</span>
                  </li>
                </ul>
                <div slot="reference" class="popover_div">
                  >{{ scope.row.limit }}
                </div>
              </el-popover>
            </div>
          </template>
        </PublicTable2>
      </div>
      <div class="print_table" id="printHtml">
        <PublicTable :viewTableList.sync="printData" :theads.sync="theads">
        </PublicTable>
      </div>
    </div>
    <el-dialog
      title="导出文件"
      :visible.sync="exportLoading"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      width="30%"
    >
      <div v-loading="exportLoading" style="width: 100%; height: 120px"></div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="cancelDownload">取消导出</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import BtnCommon from './components/btnCommon.vue';
import PublicTable from '@/components/publicTable.vue';
import PublicTable2 from '@/components/publicTable2.vue';
import { mapGetters } from 'vuex';
import { dataUtils } from '@/common';
import { export2Excel } from '@/common/excelUtil';
import ExportExcel from '@/common/excel/exportExcel';
import moment from 'moment';
import printJS from '@/common/printJS';
import { storage } from '@/common/storage';
export default {
  name: 'physicalListExaminationResults',
  mixins: [ExportExcel],
  components: {
    BtnCommon,
    PublicTable,
    PublicTable2
  },
  data() {
    return {
      total: 0,
      dateRange: [new Date(), new Date()],
      searchInfo: {
        beginDate: '',
        endDate: '',
        companyCode: '',
        companyDeptCode: '',
        peType: 0,
        departmentCode: '',
        resultKeyword: '', //
        combCode: '',
        itemCode: '', //
        checkCls: -1
      },
      isHavue: true,
      loading: false,
      abortController: null,
      companyDeptList: [],
      params: {},
      thead: [
        {
          prop: 'regNo',
          label: '单号',
          align: '',
          width: '120px',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'activeDate',
          label: '体检日期',
          align: '',
          width: '160px',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'companyName',
          label: '单位名称',
          align: '',
          width: '190px',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'companyDeptName',
          label: '部门',
          align: '',
          width: '190px',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'name',
          label: '姓名',
          align: '',
          width: '100px',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'sex',
          label: '性别',
          align: '',
          width: '50px',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'age',
          label: '年龄',
          align: '',
          width: '60px',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'patCode',
          label: '档案号',
          align: '',
          width: '100px',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'tel',
          label: '电话',
          align: '',
          width: '110px',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'cardNo',
          label: '身份证号码',
          align: '',
          width: '180px',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'combCode',
          label: '组合编号',
          align: '',
          width: '80px',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'combName',
          label: '组合名称',
          align: '',
          width: '200px',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'combResult',
          label: '小结描述',
          align: '',
          width: '220px',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'examDate',
          label: '检查日期',
          align: '',
          width: '160px',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'examDoctor',
          label: '检查医生',
          align: '',
          width: '100px',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'examDepartment',
          label: '科室',
          align: '',
          width: '160px',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'itemCode',
          label: '项目编号',
          align: '',
          width: '100px',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'itemName',
          label: '明细项目名称',
          align: '',
          width: '220px',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'itemResult',
          label: '项目结果',
          align: '',
          width: '180px',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'itemUnit',
          label: '项目单位',
          align: '',
          width: '150px',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'hint',
          label: '提示',
          align: '',
          width: '150px',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'limit',
          label: '参考范围',
          align: '',
          width: '150px',
          sortable: false,
          showOverflowTooltip: true
        }
      ],
      theads: {
        regNo: '单号',
        activeDate: '体检日期',
        companyName: '单位名称',
        companyDeptName: '部门',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        patCode: ' 档案号',
        tel: '电话',
        cardNo: '身份证号码',
        combCode: '组合编号',
        combName: '组合名称',
        combResult: '小结描述',
        examDate: '检查日期',
        examDoctor: '检查医生',
        examDepartment: '科室',
        itemCode: '项目编号',
        itemName: '明细项目名称',
        itemResult: '项目结果',
        itemUnit: '项目单位',
        hint: '提示',
        limit: '参考范围'
      },
      exportLoading: false,
      tableData: [],
      columnWidth: {
        regNo: 120,
        activeDate: 160,
        companyName: 190,
        companyDeptName: 190,
        name: 100,
        sex: 50,
        age: 60,
        patCode: 100,
        tel: 110,
        cardNo: 180,
        combCode: 80,
        combName: 200,
        combResult: 220,
        examDate: 160,
        examDoctor: 100,
        examDepartment: 160,
        itemCode: 100,
        itemName: 220,
        itemResult: 180,
        limit: 150,
        itemUnit: 150,
        hint: 150
      },
      companyList: [],
      codeItemList: [],
      codeItemCombList: [],
      printData: []
    };
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_codeDepartment', 'G_datePickerShortcuts'])
  },
  created() {
    this.getCompany();
    this.getCodeItemComb();
    this.getCodeItem();
  },
  methods: {
    requestFinally() {
      this.total = this.$refs.table_ref.totalNumber;
    },
    // 查询
    search() {
      let [beginDate, endDate] = this.dateRange;
      this.searchInfo.beginDate = dataUtils.dateToString(beginDate);
      this.searchInfo.endDate = dataUtils.dateToString(endDate);
      this.tableData = [];
      this.printData = [];
      console.log('[ this.searchInfo ]-253', this.searchInfo);
      let { companyCode, ...surplus } = this.searchInfo;
      this.params = {
        companyCode: companyCode?.[0] || '',
        ...surplus
      };
      this.$refs.table_ref.loadData();
    },
    getPeItemResultListQuery(parameter) {
      return this.$ajax
        .post(this.$apiUrls.PeItemResultListQuery, parameter)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          return r.data || {};
        });
    },
    // 单选框切换
    radioChange() {
      if (this.searchInfo.peType == '1') {
        this.searchInfo.companyCode = '';
      }
      this.search();
    },
    //打印
    prints() {
      if (this.printData.length == 0) {
        return this.$message({
          message: '没有可以打印的数据！',
          type: 'warning',
          showClose: true
        });
      }
      setTimeout(() => {
        printJS('printHtml');
      }, 500);
    },
    //导出
    exportTable() {
      this.$confirm('确定导出列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.downloadExcelFile();
      });
    },
    getToken() {
      let { token } = storage.session.get('userInfo') || {};
      if (!token || !token.access_token) return '';

      return `${token.token_type} ${token.access_token}`;
    },
    async downloadExcelFile() {
      const data = dataUtils.deepCopy(this.searchInfo);
      const [beginDate, endDate] = this.dateRange;
      data.beginDate = dataUtils.dateToString(beginDate);
      data.endDate = dataUtils.dateToString(endDate);
      const { companyCode, ...surplus } = data;
      const params = {
        companyCode: companyCode?.[0] || '',
        ...surplus
      };
      try {
        this.abortController = new AbortController();
        const signal = this.abortController.signal;

        this.exportLoading = true;
        const res = await this.$ajax.post(
          this.$apiUrls.ExportPeItemResultListQuery,
          params,
          {
            responseType: 'arraybuffer',
            headers: {
              Accept:
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            },
            signal: signal, // 传递 signal
            timeout: 0 // 设置超时时间为 0，表示不设置超时时间
          }
        );
        if (res.status !== 200)
          return this.$message.error('导出失败:' + res.statusText);
        const blob = new Blob([res.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = '体检项目结果清单.xlsx';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        this.$message.success('导出成功');
      } catch (error) {
        if (error.message === 'canceled') {
          this.$message.warning('导出已取消');
        } else {
          this.$message.error('导出文件时出错:', error);
        }
      } finally {
        this.abortController = null;
        this.exportLoading = false;
      }
    },
    cancelDownload() {
      if (this.abortController) {
        this.abortController.abort();
        this.abortController = null;
        this.exportLoading = false;
      }
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    // 获取单位
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    //单位下拉变化
    companyChange(data) {
      this.getDepartList(data);
      if (!data || data.length === 0) return;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.dateRange = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.search();
    },
    //查询公司部门信息
    getDepartList(companyCode) {
      this.searchInfo.companyDeptCode = '';
      this.isHavue = true;
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanyDepartment, {
          companyCode: companyCode?.[0] || '',
          deptCode: '',
          deptName: ''
        })
        .then((r) => {
          this.isHavue = false;
          //console.log("r", r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.companyDeptList = returnData || [];
        });
    },
    // 获取组合
    getCodeItemComb() {
      this.$ajax
        .post(this.$apiUrls.PageQuery_CodeItemComb, '', {
          query: {
            clsCode: ''
          }
        })
        .then((r) => {
          this.codeItemCombList = r.data.returnData || [];
        });
    },
    //获取项目
    getCodeItem() {
      // this.loading = true;
      this.$ajax
        .post(this.$apiUrls.PageQuery_CodeItem, '', {
          query: {
            clsCode: ''
          }
        })
        .then((r) => {
          this.codeItemList = r.data.returnData || [];
        });
    }
  }
};
</script>

<style lang="less" scoped>
.physicalListExaminationResults {
  color: #2d3436;
  .main {
    height: 100%;
    background: #fff;
    padding: 18px 10px;
    display: flex;
    flex-direction: column;
  }
  .search-list {
    display: flex;
    align-items: center;
    margin-bottom: 18px;
  }
  .list-item {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 20px;
    width: 300px;
    &:last-child {
      margin-right: 0;
    }
    span {
      font-size: 14px;
      font-weight: 600;
      margin-right: 10px;
    }
    .el-radio {
      color: #000;
    }
    /deep/.el-radio__inner {
      width: 18px;
      height: 18px;
    }
  }
  .radioDiv {
    width: 550px;
  }
  .redDiv {
    color: red;
    font-weight: 600;
  }
  .input {
    width: 100%;
  }
  .title-wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 14px;
    h3 {
      font-size: 18px;
    }
    span {
      font-size: 14px;
    }
  }
  .main-table {
    border: 1px solid rgba(178, 190, 195, 0.5);
    border-radius: 4px;
    flex: 1;
    overflow: auto;
    .floatPopup_div .popover_div {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .print_table {
    display: none;
  }
}
</style>
<style lang="less">
.floatPopup_popover {
  background: #dae4f2;
  .popper__arrow::after {
    border-right-color: rgba(0, 0, 0, 0.6) !important;
  }
  li {
    color: #000;
  }
}
</style>
