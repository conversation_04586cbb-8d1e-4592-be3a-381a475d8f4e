<template>
  <div class="btn_group">
    <el-button
      v-if="btnList.includes('查询')"
      @click="search"
      class="blue_btn btn"
      size="small"
      icon="iconfont icon-search"
      >查询</el-button
    >

    <el-button
      v-if="btnList.includes('统计')"
      @click="statistics"
      class="blue_btn btn"
      size="small"
      icon="iconfont icon-tongji"
      :loading="loadingStatistics"
      >统计</el-button
    >

    <el-button
      v-if="btnList.includes('结算')"
      @click="settlement"
      class="blue_btn btn"
      size="small"
      icon="iconfont icon-shoufeiguanli"
      >结算</el-button
    >

    <el-button
      v-if="btnList.includes('结算记录')"
      size="small"
      icon="iconfont icon-yonghujiluchaxun"
      class="violet_btn btn"
      @click="settlementRecords"
      >结算记录</el-button
    >
    <!-- <el-button
      v-if="btnList.includes('导入')"
      size="small"
      icon="iconfont icon-daoru"
      class="violet_btn btn"
      @click="importFile"
      >导入</el-button
    > -->
    <a
      href="javascript:;"
      v-if="btnList.includes('导入')"
      class="file violet_btn btn"
      ><i class="iconfont icon-daoru"></i> 导入
      <input
        accept="image/*"
        type="file"
        name="image"
        size="small"
        @change="importFile($event)"
      />
    </a>
    <el-button
      size="small"
      class="yellow_btn btn"
      v-if="btnList.includes('导出')"
      @click="exports"
      icon="iconfont icon-daochu"
      >导出</el-button
    >
    <el-button
      size="small"
      class="green_btn btn"
      v-if="btnList.includes('打印')"
      @click="prints"
      icon="iconfont icon-dayin-"
      >打印</el-button
    >

    <el-button
      size="small"
      class="green_btn btn"
      v-if="btnList.includes('清空')"
      @click="emptys"
      icon="iconfont icon-qingkonghuancun"
      >清空</el-button
    >
    <el-button
      size="small"
      class="green_btn btn"
      v-if="btnList.includes('预览')"
      @click="previews"
      icon="iconfont icon-preview"
      >预览</el-button
    >
    <el-button
      v-if="btnList.includes('保存')"
      @click="saves"
      class="blue_btn btn"
      size="small"
      icon="iconfont icon-baocun"
      >保存</el-button
    >
    <el-button
      v-if="btnList.includes('采集')"
      @click="collection"
      class="violet_btn btn"
      size="small"
      icon="iconfont icon-screenshot-2-line"
      >采集</el-button
    >
    <el-button
      v-if="btnList.includes('删除')"
      @click="deletes"
      class="red_btn btn"
      size="small"
      icon="iconfont icon-shanchu"
      >删除</el-button
    >
    <el-button
      v-if="btnList.includes('选中')"
      @click="chooses"
      class="blue_btn btn"
      size="small"
      icon="iconfont icon-baocun"
      >选中</el-button
    >
    <el-button
      v-if="btnList.includes('取消')"
      @click="cancels"
      class="red_btn btn"
      size="small"
      icon="iconfont icon-shanchu"
      >取消</el-button
    >
    <el-button
      v-if="btnList.includes('记录查询')"
      size="small"
      icon="iconfont icon-yonghujiluchaxun"
      class="violet_btn btn"
      @click="recordSearchs"
      >记录查询</el-button
    >
    <el-button
      size="small"
      class="yellow_btn btn"
      v-if="btnList.includes('撤销')"
      @click="revocations"
      icon="iconfont icon-chexiao"
      >撤销</el-button
    >
    <el-button
      size="small"
      class="yellow_btn btn"
      v-if="btnList.includes('部分退款')"
      @click="rebates"
      icon="iconfont icon-tuikuan"
      >部分退款</el-button
    >
    <el-button
      size="small"
      class="red_btn btn"
      v-if="btnList.includes('退款')"
      @click="refunds"
      icon="iconfont icon-tuikuan"
      >退款</el-button
    >
    <el-button
      size="small"
      class="green_btn btn"
      v-if="btnList.includes('重打')"
      @click="reprints"
      icon="iconfont icon-zhongdayin"
      >重打</el-button
    >
    <el-button
      size="small"
      class="blue_btn btn"
      v-if="btnList.includes('生成清单')"
      @click="createList"
      icon="iconfont icon-shengchengbaobiao"
      >生成清单</el-button
    >
    <el-button
      size="small"
      class="violet_btn btn"
      v-if="btnList.includes('打包运送')"
      @click="packRansport"
      icon="iconfont icon-a-ziyuan702"
      >打包运送</el-button
    >
    <el-button
      size="small"
      class="blue_btn btn"
      v-if="btnList.includes('重新传输')"
      @click="retransmission"
      icon="iconfont icon-fenpei"
      >重新传输</el-button
    >
    <el-button
      size="small"
      class="blue_btn btn"
      v-if="btnList.includes('确认结算')"
      @click="submitSetle"
      icon="iconfont icon-shengchengbaobiao"
      >确认结算</el-button
    >
    <el-button
      size="small"
      class="yellow_btn btn"
      v-if="btnList.includes('身份证')"
      @click="idCardClick"
      icon="iconfont icon-shenfenzheng"
      >身份证</el-button
    >
    <el-button
      size="small"
      class="violet_btn btn"
      v-if="btnList.includes('配管')"
      @click="pipingClick"
      icon="iconfont icon-shiguanshiji"
      >配管</el-button
    >
    <el-button
      size="small"
      class="yellow_btn btn"
      v-if="btnList.includes('取消配管')"
      @click="cancelPipingClick"
      icon="iconfont icon-shiguanshiji"
      >取消配管</el-button
    >
    <el-button
      size="small"
      class="green_btn btn"
      v-if="btnList.includes('发起会诊')"
      @click="sendConsultation"
      icon="iconfont icon-fasong"
      >发起会诊</el-button
    >
    <el-button
      size="small"
      class="blue_btn btn"
      v-if="btnList.includes('响应会诊')"
      @click="responseConsultation"
      icon="iconfont icon-changyonghuifu"
      >响应会诊</el-button
    >
    <el-button
      size="small"
      class="blue_btn btn"
      v-if="btnList.includes('发送')"
      @click="sendInfo"
      icon="iconfont icon-fasong"
      >发送</el-button
    >
    <el-button
      v-if="btnList.includes('修改')"
      @click="amend"
      class="blue_btn btn"
      size="small"
      icon="iconfont icon-bianji"
      >{{ isModify ? '撤销编辑' : '编辑结果' }}</el-button
    >
    <el-button
      v-if="btnList.includes('弃检')"
      @click="refuseCheck"
      class="yellow_btn btn"
      size="small"
      icon="iconfont icon-wj-thwj"
      >弃检</el-button
    >
    <el-button
      v-if="btnList.includes('删除结果')"
      @click="delResult"
      class="red_btn btn"
      size="small"
      icon="iconfont icon-shanchu"
      >删除结果</el-button
    >
    <el-button
      v-if="btnList.includes('历史报告')"
      @click="historyReport"
      class="violet_btn btn"
      size="small"
      icon="iconfont icon-a-lishijilubiaodanjishi"
      >历史报告</el-button
    >
    <el-button
      v-if="btnList.includes('列表')"
      @click="listClick"
      class="yellow_btn btn"
      size="small"
      icon="iconfont icon-jinduchaxun"
      >列表</el-button
    >
    <slot name="footAdd"></slot>
  </div>
</template>

<script>
export default {
  name: 'btnGroup',
  props: {
    btnList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    isModify: {
      type: Boolean,
      default: false
    },
    loadingStatistics: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isPreview: false
    };
  },
  methods: {
    // 查询
    search() {
      this.$emit('search');
    },

    // 打印
    prints() {
      this.$emit('prints');
    },
    //导入
    importFile($event) {
      this.$emit('importFile', $event);
    },
    // 导出
    exports() {
      this.$emit('exports');
    },
    //清空
    emptys() {
      this.$emit('emptys');
    },
    //保存
    saves() {
      this.$emit('saves');
    },
    //记录查询
    recordSearchs() {
      this.$emit('recordSearchs');
    },
    //预览
    previews() {
      this.$emit('previews');
    },
    //撤销
    revocations() {
      this.$emit('revocations');
    },
    //部分退款
    rebates() {
      this.$emit('rebates');
    },
    //退款
    refunds() {
      this.$emit('refunds');
    },
    //重打
    reprints() {
      this.$emit('reprint');
    },
    //生成清单
    createList() {
      this.$emit('createList');
    },
    //打包运送
    packRansport() {
      this.$emit('packRansport');
    },
    //重新传输
    retransmission() {
      this.$emit('retransmission');
    },
    //结算
    settlement() {
      this.$emit('settlement');
    },
    //结算记录
    settlementRecords() {
      this.$emit('settlementRecords');
    },
    //确认结算
    submitSetle() {
      this.$emit('submitSetle');
    },
    //身份证
    idCardClick() {
      this.$emit('idCardClick');
    },
    //配管
    pipingClick() {
      this.$emit('pipingClick');
    },
    //取消配管
    cancelPipingClick() {
      this.$emit('cancelPipingClick');
    },
    //发起会诊
    sendConsultation() {
      this.$emit('sendConsultation');
    },
    //响应会诊
    responseConsultation() {
      this.$emit('responseConsultation');
    },
    //发送
    sendInfo() {
      this.$emit('sendInfo');
    },
    //修改
    amend() {
      this.$emit('amend');
    },
    //弃检
    refuseCheck() {
      this.$emit('refuseCheck');
    },
    //采集
    collection() {
      this.$emit('collection');
    },
    // 删除
    deletes() {
      this.$emit('deletes');
    },
    //删除结果
    delResult() {
      this.$emit('delResult');
    },
    //历史报告
    historyReport() {
      this.$emit('historyReport');
    },
    //选中
    chooses() {
      this.$emit('chooses');
    },
    //取消
    cancels() {
      this.$emit('cancels');
    },
    // 列表
    listClick() {
      this.$emit('listClick');
    },
    /**
     * @author: justin
     * @description: 统计触发
     * @return {*}
     */
    statistics() {
      this.$emit('statistics');
    }
  }
};
</script>
<style lang="less" scoped>
.btn_group {
  // height: 48px;
  display: flex;
  align-items: center;
  .btn {
    padding: 6.5px 10px;
  }
}
</style>
<style lang="less">
.file {
  position: relative;
  display: inline-block;
  border: 1px solid #99d3f5;
  border-radius: 4px;
  padding: 4px 12px;
  overflow: hidden;
  text-decoration: none;
  text-indent: 0;
  line-height: 16px;
  margin-right: 10px;
}
.file input {
  position: absolute;
  font-size: 100px;
  right: 0;
  top: 0;
  opacity: 0;
}
.file:hover {
  text-decoration: none;
}
</style>
