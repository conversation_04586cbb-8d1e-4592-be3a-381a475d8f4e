<template>
  <!-- 发票收回 -->
  <div class="invoiceTakeBack_page">
    <header>
      <h3>发票收回：</h3>
      <div class="search_wrap">
        有效日期
        <el-date-picker
          :picker-options="{ shortcuts: G_datePickerShortcuts }"
          v-model="searchDateVal"
          style="margin: 0 10px; width: 38%"
          size="small"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd"
          @change="getInvoiceList"
          value-format="yyyy-MM-dd HH:mm:ss"
        >
        </el-date-picker>
        <el-button
          class="blue_btn"
          size="small"
          icon="iconfont icon-search"
          @click="getInvoiceList"
          >查找</el-button
        >
        <el-button
          class="yellow_btn"
          size="small"
          style="margin-left: 18px"
          icon="iconfont icon-wj-thwj"
          @click="takebackClick"
          >收回</el-button
        >
      </div>
    </header>
    <!-- 发票列表 -->
    <div class="invoice_list">
      <PublicTable
        :viewTableList="invoiceList"
        :theads="theads"
        @rowClick="currentChange"
      >
        <template #status="{ scope }">
          <span :class="statusColor(scope.row.status)">
            {{ G_EnumList['InvoiceStatus'][scope.row.status] }}
          </span>
        </template>
        <template #effectiveDate="{ scope }">
          {{ subBlankDate(scope.row.effectiveDate) }}
        </template>
        <template #expiryDate="{ scope }">
          {{ subBlankDate(scope.row.expiryDate) }}
        </template>
      </PublicTable>
    </div>
  </div>
</template>

<script>
import PublicTable from '@/components/publicTable';
import { mapGetters } from 'vuex';
import { dataUtils } from '@/common';
export default {
  name: 'invoiceTakeBack',
  components: {
    PublicTable
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_datePickerShortcuts'])
  },
  data() {
    return {
      searchDateVal: [],
      invoiceList: [],
      theads: {
        prefix: '发票前缀',
        startNo: '开始发票号',
        endNo: '结束发票号',
        remainingNum: '余票',
        receiver: '领取人',
        currentNo: '当前发票号',
        status: '状态',
        effectiveDate: '生效日期',
        expiryDate: '失效日期'
      },
      rowId: ''
    };
  },
  created() {
    this.getInvoiceList();
  },
  methods: {
    // 获取发票列表
    getInvoiceList() {
      let effectiveDate = '';
      let expiryDate = '';
      if (this.searchDateVal !== null) {
        effectiveDate = this.searchDateVal[0];
        expiryDate = this.searchDateVal[1];
      }
      let data = {
        effectiveDate: effectiveDate ? effectiveDate : '',
        expiryDate: expiryDate ? expiryDate : '',
        status: 0
      };
      this.$ajax.post(this.$apiUrls.ReadInvoiceAllocation, data).then((r) => {
        console.log('ReadInvoiceAllocation: ', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.invoiceList = returnData || [];
        this.rowId = '';
      });
    },
    // 当前行点击
    currentChange(row) {
      if (row === null) return;
      this.rowId = row.id;
      console.log('this.rowId: ', this.rowId);
    },
    // 收回
    takebackClick() {
      if (!this.rowId) {
        this.$message({
          message: '请选择一条要收回发票的信息!',
          type: 'warning',
          showClose: true
        });
        return;
      }

      this.$confirm('是否确定收回发票?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax
            .post(this.$apiUrls.TakeBackInvoice, '', {
              query: { id: this.rowId }
            })
            .then((r) => {
              let { success } = r.data;
              if (!success) return;
              this.$message({
                message: '收回发票成功!',
                type: 'success',
                showClose: true
              });
              this.getInvoiceList();
            });
        })
        .catch(() => {});
    },
    // 状态颜色处理
    statusColor(val) {
      let color = '';
      switch (val) {
        case 1:
          color = 'cell_blue';
          break;
        case 2:
          color = 'cell_green';
          break;
        case 3:
          color = '';
          break;
      }
      return color;
    },
    // 截取空格的日期
    subBlankDate: dataUtils.subBlankDate
  }
};
</script>
<style lang="less" scoped>
.invoiceTakeBack_page {
  height: 100%;
  border-radius: 4px;
  overflow: auto;
  background: #fff;
  padding: 18px;
  display: flex;
  flex-direction: column;
  header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 18px;
    h3 {
      font-size: 18px;
    }
    .search_wrap {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      font-weight: 600;
      font-size: 14px;
      button {
        padding: 6.5px 10px;
      }
    }
  }
  .invoice_list {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
  }
}
</style>
