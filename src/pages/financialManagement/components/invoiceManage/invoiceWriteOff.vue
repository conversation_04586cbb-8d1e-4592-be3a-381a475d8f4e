<template>
  <div class="invoiceWriteOff">
    <header>
      <h3>票据核销表：</h3>
      <div class="search-btn">
        <div class="btn-item">
          <span>发票前缀</span>
          <el-input
            class="input"
            v-model.trim="form.prefix"
            size="small"
            placeholder="请输入发票前缀"
            :maxlength="2"
          ></el-input>
        </div>
        <div class="btn-item">
          <span>开始发票号</span>
          <el-input
            class="input"
            v-model.trim="form.startNo"
            size="small"
            placeholder="请输入开始发票号"
            :maxlength="8"
          ></el-input>
        </div>
        <div class="btn-item">
          <span>结束发票号</span>
          <el-input
            class="input"
            v-model.trim="form.endNo"
            size="small"
            placeholder="请输入结束发票号"
            :maxlength="8"
          ></el-input>
        </div>
        <div class="btn-item">
          <el-button
            size="small"
            class="blue_btn btn"
            icon="iconfont icon-shengchengbaobiao"
            @click="createClick"
            >生成</el-button
          >
        </div>
        <div class="btn-item">
          <el-button
            size="small"
            class="green_btn btn"
            icon="iconfont icon-dayin-"
            @click="printsClick"
            >打印</el-button
          >
        </div>
      </div>
    </header>
    <div class="writeOff-table">
      <h2>财政票据核销表</h2>
      <div class="table">
        <el-table
          v-show="tableData.length > 0"
          :data="tableData"
          border
          :summary-method="getSummaries"
          show-summary
          style="width: 100%"
          height="100%"
        >
          <el-table-column
            prop="beginDate"
            label="开始使用日期"
          ></el-table-column>
          <el-table-column prop="endDate" label="结束使用日期">
          </el-table-column>
          <el-table-column prop="startNo" label="起始号码"></el-table-column>
          <el-table-column prop="endNo" label="终止号码"></el-table-column>
          <el-table-column prop="usedCount" label="已用(份)"></el-table-column>
          <el-table-column prop="invalidCount" label="作废(份)">
          </el-table-column>
          <el-table-column prop="totalCount" label="总发票数合计(份)">
          </el-table-column>
          <el-table-column prop="totalChargeAmount" label="总收费金额">
          </el-table-column>
          <el-table-column prop="note" label="备注"></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment';
export default {
  name: 'invoiceWriteOff',
  data() {
    return {
      form: {
        // prefix: "",
        // startNo: "",
        // endNo: ""
      },
      theads: {
        beginDate: '开始使用日期',
        endDate: '结束使用日期',
        startNo: '起始号码',
        endNo: '终止号码',
        usedCount: '已用(份)',
        invalidCount: '作废(份)',
        totalCount: '总发票数合计(份)',
        totalChargeAmount: '总收费金额',
        note: '备注'
      },
      tableData: []
    };
  },
  methods: {
    createClick() {
      if (Object.keys(this.form).length === 0) {
        this.$message({
          message: '请输入发票号!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let data = {
        ...this.form
      };
      console.log('data: ', data);
      this.$ajax
        .post(this.$apiUrls.ReadInvoiceWriteOffReport, data)
        .then((r) => {
          console.log('ReadInvoiceWriteOffReport: ', r);
          let { success, returnData, returnMsg } = r.data;
          if (!success) return;
          if (returnData === null) {
            this.$message({
              message: returnMsg,
              type: 'warning',
              showClose: true
            });
            return;
          }
          returnData.map((item) => {
            item.beginDate = moment(item.beginDate).format('YYYY-MM-DD');
            item.endDate = moment(item.endDate).format('YYYY-MM-DD');
          });
          this.tableData = returnData || [];
        });
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          return;
        }
        const values = data.map((item) => Number(item[column.property]));
        if (
          column.property === 'usedCount' ||
          column.property === 'invalidCount' ||
          column.property === 'totalCount' ||
          column.property === 'totalChargeAmount'
        ) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          sums[index];
        } else {
          sums[index] = '';
        }
      });
      return sums;
    },
    printsClick() {}
  }
};
</script>

<style lang="less" scoped>
.invoiceWriteOff {
  height: 100%;
  background: #fff;
  padding: 18px;
  display: flex;
  flex-direction: column;
  color: #2d3436;
  border-radius: 4px;
  header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 18px;
  }
  .search-btn {
    display: flex;
  }
  .btn-item {
    display: flex;
    align-items: center;
    margin-right: 18px;
    span {
      font-size: 14px;
      font-weight: 600;
      margin-right: 10px;
    }
    &:last-child {
      margin-right: 0;
    }
  }
  .writeOff-table {
    background: #f7f8f9;
    flex: 1;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    padding: 20px;
    overflow: auto;
    h2 {
      text-align: center;
      font-size: 32px;
      // margin-top: 20px;
      margin-bottom: 20px;
    }
  }
  .table {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
  }
  .btn {
    padding: 6.5px 10px;
  }
  .input {
    width: 180px;
  }
}
</style>
