<template>
  <!-- 发票转交 -->
  <div class="invoiceDeliver">
    <div class="left-wrap">
      <div class="left-header">
        <h3>发票转交：</h3>
        <el-button
          size="small"
          icon="iconfont icon-zhuanhuan"
          class="blue_btn btn"
          @click="deliverClick"
          >转交</el-button
        >
      </div>
      <div>
        <el-form ref="form" :model="form" :rules="rules" label-width="95px">
          <el-form-item label="发票前缀" prop="prefix">
            <el-input
              :maxlength="2"
              v-model.trim="form.prefix"
              size="small"
              placeholder="请输入发票前缀"
            ></el-input>
          </el-form-item>
          <el-form-item label="开始发票号" prop="startNo">
            <el-input
              :maxlength="8"
              v-model.trim="form.startNo"
              size="small"
              placeholder="请输入开始发票号"
            ></el-input>
          </el-form-item>
          <el-form-item label="转交数量" prop="count">
            <el-input
              v-model.trim="count"
              size="small"
              placeholder="请输入转交数量"
              disabled
            ></el-input>
          </el-form-item>
          <el-form-item label="结束发票号" prop="endNo">
            <el-input
              :maxlength="8"
              v-model.trim="form.endNo"
              size="small"
              placeholder="请输入结束发票号"
            ></el-input>
          </el-form-item>
          <el-form-item label="转交人" prop="transferor">
            <el-select
              class="select"
              v-model.trim="form.transferor"
              placeholder="请选择转交人"
              size="small"
              filterable
            >
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in G_sysOperator"
                :key="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="接收人" prop="receiver">
            <el-select
              class="select"
              v-model.trim="form.receiver"
              placeholder="请选择接收人"
              size="small"
              filterable
            >
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in G_sysOperator"
                :key="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="content-wrap">
      <div class="content-header">
        <h3>可转交发票列表：</h3>
        <div class="header-btn">
          <span>有效日期</span>
          <el-date-picker
            :picker-options="{ shortcuts: G_datePickerShortcuts }"
            v-model.trim="searchDate"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
            style="width: 40%; margin: 0 10px"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
          <el-button
            size="small"
            class="blue_btn btn"
            icon="iconfont icon-search"
            @click="getInvoiceList"
            >查找</el-button
          >
        </div>
      </div>
      <div class="content-table">
        <PublicTable
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          @rowClick="currentChange"
          :columnWidth="columnWidth"
        >
          <template #status="{ scope }">
            <span :class="statusColor(scope.row.status)">
              {{ G_EnumList['InvoiceStatus'][scope.row.status] }}
            </span>
          </template>
        </PublicTable>
      </div>
    </div>
  </div>
</template>

<script>
import PublicTable from '@/components/publicTable';
import { mapGetters } from 'vuex';
import moment from 'moment';
export default {
  name: 'invoiceDeliver',
  components: {
    PublicTable
  },
  data() {
    // 开始发票号校验
    const checkStartNo = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入开始发票号'));
        return;
      }
      if (value) {
        let reg = /\d{8}/g;
        if (!reg.test(value)) {
          callback(new Error('必须输入8位数字'));
        } else {
          callback();
        }
      }
    };
    // 结束发票号校验
    const checkEndNo = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入结束发票号'));
        return;
      }
      if (value) {
        let reg = /\d{8}/g;
        if (!reg.test(value)) {
          callback(new Error('必须输入8位数字'));
        } else {
          if (value && this.form.startNo) {
            if (value < this.form.startNo) {
              callback(new Error('结束发票号必须大于开始发票号'));
            } else {
              callback();
            }
          }
        }
      }
    };
    return {
      form: {
        prefix: '',
        startNo: '',
        endNo: '',
        transferor: '',
        receiver: ''
      },
      theads: {
        prefix: '发票前缀',
        startNo: '开始发票号',
        endNo: '结束发票号',
        remainingNum: '余票',
        receiver: '领取人',
        currentNo: '当前发票号',
        status: '状态',
        effectiveDate: '生效日期',
        expiryDate: '失效日期'
      },
      tableData: [],
      columnWidth: {
        effectiveDate: 110,
        expiryDate: 110,
        startNo: 100,
        endNo: 100,
        currentNo: 100,
        receiver: 100,
        remainingNum: 100
      },
      rules: {
        prefix: [
          { required: true, message: '请输入发票前缀', trigger: 'blur' },
          { min: 2, message: '必须输入2个字符', trigger: 'blur' }
        ],
        startNo: [{ required: true, validator: checkStartNo, trigger: 'blur' }],
        endNo: [{ required: true, validator: checkEndNo, trigger: 'blur' }],
        transferor: [
          { required: true, message: '请选择转交人', trigger: 'change' }
        ],
        receiver: [
          { required: true, message: '请选择接收人', trigger: 'change' }
        ]
      },
      searchDate: null,
      rowId: ''
    };
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_userInfo',
      'G_sysOperator',
      'G_datePickerShortcuts'
    ]),
    // 数量计算
    count() {
      if (this.form.endNo && this.form.startNo) {
        if (Number(this.form.endNo) < Number(this.form.startNo)) return '';
        if (this.form.endNo === this.form.startNo) {
          return 1;
        }
        return this.form.endNo - this.form.startNo + 1;
      }
    }
  },
  created() {
    this.getInvoiceList();
  },
  methods: {
    // 获取或搜索发票列表
    getInvoiceList() {
      let effectiveDate = '';
      let expiryDate = '';
      if (this.searchDate !== null) {
        effectiveDate = this.searchDate[0];
        expiryDate = this.searchDate[1];
      }
      let data = {
        effectiveDate: effectiveDate ? effectiveDate : '',
        expiryDate: expiryDate ? expiryDate : '',
        status: 0
      };
      this.$ajax.post(this.$apiUrls.ReadInvoiceAllocation, data).then((r) => {
        console.log('ReadInvoiceAllocation: ', r);
        let { success, returnData } = r.data;
        if (!success) return;
        returnData.map((item) => {
          item.effectiveDate = moment(item.effectiveDate).format('YYYY-MM-DD');
          item.expiryDate = moment(item.expiryDate).format('YYYY-MM-DD');
        });
        this.tableData = returnData || [];
      });
    },
    deliverClick() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          let data = {
            ...this.form
          };
          console.log('data: ', data);
          this.$ajax.post(this.$apiUrls.TransferInvoice, data).then((r) => {
            console.log('TransferInvoice: ', r);
            let { success, returnData } = r.data;
            if (!success) return;
            this.$message({
              message: '发票转交成功!',
              type: 'success',
              showClose: true
            });
            this.getInvoiceList();
            this.$refs.form.resetFields();
          });
        }
      });
    },
    // 当前行点击
    currentChange(row) {
      if (row === null) return;
      this.rowId = row.id;
      console.log('this.rowId: ', this.rowId);
    },
    // 状态颜色处理
    statusColor(val) {
      let color = '';
      switch (val) {
        case 1:
          color = 'cell_blue';
          break;
        case 2:
          color = 'cell_green';
          break;
        case 3:
          color = '';
          break;
      }
      return color;
    }
  }
};
</script>

<style lang="less" scoped>
.invoiceDeliver {
  height: 100%;
  display: flex;
  .left-wrap {
    width: 360px;
    background: #fff;
    margin-right: 18px;
    padding: 18px;
    border-radius: 4px;
  }
  .left-header,
  .content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 18px;
    h3 {
      font-size: 18px;
    }
  }
  .content-wrap {
    flex: 1;
    background: #fff;
    padding: 18px;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    overflow: auto;
  }
  .header-btn {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    span {
      font-size: 14px;
      font-weight: 600;
    }
  }
  .content-table {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
  }
  .btn {
    padding: 6.5px 10px;
  }
  .select {
    width: 100%;
  }
  /deep/.el-form-item__label {
    color: #2d3436;
    font-weight: 600;
  }
  .el-form-item {
    margin-bottom: 10px;
  }
}
</style>
