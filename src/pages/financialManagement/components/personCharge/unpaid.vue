<template>
  <!-- 未缴费 -->
  <div class="unpaid">
    <!-- 未缴费列表 -->
    <div class="unpaid-list">
      <div class="search-wrap">
        <h3>未缴费列表:</h3>
        <div class="search-btn">
          <span>登记日期</span>
          <el-date-picker
            v-model.trim="searchDate"
            type="date"
            placeholder="请选择日期"
            size="small"
            class="date"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
          <el-button
            size="small"
            class="blue_btn btn"
            icon="iconfont icon-search"
            @click="searchClick"
            >查询</el-button
          >
        </div>
      </div>
      <div class="list-table">
        <PublicTable
          :isSortShow="false"
          :theads.sync="unpaidTheads"
          :viewTableList.sync="unpaidData"
          :columnWidth="columnWidth"
          @rowClick="unpaidRowClick"
        >
        </PublicTable>
      </div>
    </div>
    <!-- 信息列表 -->
    <div class="list-info">
      <div class="info-btn">
        <el-button
          size="small"
          class="green_btn btn"
          icon="iconfont icon-qingkonghuancun"
          @click="clearClick"
          >清空</el-button
        >
        <el-button
          size="small"
          class="yellow_btn btn"
          icon="iconfont icon-kapian"
          @click="cardPayClick"
          >卡支付</el-button
        >
        <el-button
          size="small"
          class="blue_btn btn"
          icon="iconfont icon-shoufeiguanli"
          @click="payClick"
          >缴费</el-button
        >
        <el-button
          size="small"
          class="violet_btn btn"
          icon="iconfont icon-caidanlan-caiwu-fapiaoguanli"
          @click="invoiceEditClick"
          >发票</el-button
        >
      </div>
      <div class="info-form">
        <el-form ref="ruleForm" :model="userInfo" label-width="70px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="体检号" prop="regNo">
                <el-input
                  v-model.trim="userInfo.regNo"
                  size="small"
                  placeholder=""
                  @keyup.enter.native="getUnpaidList"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="档案号" prop="patCode">
                <el-input
                  v-model.trim="userInfo.patCode"
                  size="small"
                  placeholder=""
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="姓名" prop="name">
                <el-input
                  v-model.trim="userInfo.name"
                  size="small"
                  placeholder=""
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="工作单位" prop="companyName">
                <el-input
                  v-model.trim="userInfo.companyName"
                  size="small"
                  placeholder=""
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="comb-list">
        <div class="comb-title">
          <h3>组合列表：</h3>
          <div class="comb-price">
            <div class="price-item">
              共 <span class="numb cell_blue">{{ combList.length }}</span> 项
            </div>
            <div class="price-item">
              应收：<span class="numb"
                >¥{{ handlePrice(receivableAmount) }}</span
              >
            </div>
            <div class="price-item">
              实收：<span class="numb cell_red"
                >¥{{ handlePrice(paidInAmount) }}</span
              >
            </div>
          </div>
        </div>
        <div v-if="combList.length === 0" class="empty">暂无数据</div>
        <div class="list-box">
          <div class="list-wrap">
            <div
              class="list-item"
              v-for="(item, index) in combList"
              :key="index"
            >
              <span>{{ item.combName }}</span>
              <span class="cell_red cell-price">
                ¥{{ handlePrice(item.price) }}
              </span>
            </div>
          </div>
        </div>
      </div>
      <div class="info-table">
        <PublicTable
          :isSortShow="false"
          :theads.sync="infoTheads"
          :viewTableList.sync="basicClsData"
          :cell_red="['totalPrice']"
          :columnAlign="columnAlign"
        >
          <template #totalPrice="{ scope }">
            <div class="cell-price">
              {{ handlePrice(scope.row.totalPrice) }}
            </div>
          </template>
          <template #bookKeepingPrice="{ scope }">
            <div class="cell-price">
              {{ handlePrice(scope.row.bookKeepingPrice) }}
            </div>
          </template>
          <template #selfPaidPrice="{ scope }">
            <div class="cell-price">
              {{ handlePrice(scope.row.selfPaidPrice) }}
            </div>
          </template>
        </PublicTable>
      </div>
    </div>
    <!-- 发票号修改弹窗 -->
    <el-dialog
      :visible.sync="invoiceShow"
      width="492px"
      top="20%"
      custom-class="invoiceShow"
      :close-on-click-modal="false"
      :before-close="cancel"
    >
      <div slot="title" class="dialog-title">体检发票号修改</div>
      <el-form
        ref="invoiceForm"
        :model="invoiceForm"
        label-width="100px"
        :rules="invoiceFormRules"
      >
        <el-form-item label="新发票号" prop="newInvoice">
          <el-input
            v-model.trim="invoiceForm.newInvoice"
            size="medium"
            :maxlength="10"
            class="medium-input"
          ></el-input>
        </el-form-item>
        <el-form-item label="确认新发票号" prop="checkNewInvoice">
          <el-input
            v-model.trim="invoiceForm.checkNewInvoice"
            size="medium"
            :maxlength="10"
            class="medium-input"
          ></el-input>
        </el-form-item>
        <el-form-item label="发票跳段备注" prop="note" v-show="showNote">
          <el-input
            v-model.trim="invoiceForm.note"
            size="medium"
            class="medium-input"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="blue_btn btn-footer"
          @click="invoiceSearchClick"
          size="small"
          >票号查询</el-button
        >
        <el-button @click="cancel" size="small" class="btn-footer"
          >取消</el-button
        >
        <el-button
          class="blue_btn btn-footer"
          @click="invoiceClick"
          size="small"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <!-- 发票号查询弹窗 -->
    <el-dialog
      :visible.sync="invoiceSearch"
      width="1016px"
      top="4%"
      custom-class="invoiceSearch"
      :close-on-click-modal="false"
    >
      <div slot="title" class="dialog-title">发票号查询</div>
      <div class="search-table">
        <PublicTable
          :theads.sync="searchTheads"
          :viewTableList.sync="invoiceNoData"
          @rowDblclick="invoiceSearchDblclick"
        >
          <template #status="{ scope }">
            <span :class="statusColor(scope.row.status)">
              {{ G_EnumList['InvoiceStatus'][scope.row.status] }}
            </span>
          </template>
        </PublicTable>
      </div>
    </el-dialog>
    <!-- 体检卡支付弹窗 -->
    <el-dialog
      :visible.sync="cardPayShow"
      width="440px"
      top="4%"
      custom-class="cardPayShow"
      :close-on-click-modal="false"
    >
      <div slot="title" class="dialog-title">体检卡支付窗口</div>
      <el-form
        ref="cardForm"
        :model="cardForm"
        label-width="90px"
        class="cardForm"
      >
        <el-form-item label="类型">
          <el-radio-group v-model.trim="cardRadio">
            <el-radio :label="1" class="radio">个人</el-radio>
            <el-radio :label="2" class="radio">单位</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="发票抬头">
          <el-input v-model.trim="cardForm.name" size="medium"></el-input>
        </el-form-item>
        <el-form-item label="姓名">
          <span class="name">杨勇</span>
        </el-form-item>
        <el-form-item label="体检卡号">
          <el-input v-model.trim="cardForm.name" size="medium"></el-input>
        </el-form-item>
        <el-form-item label="密码">
          <el-input
            type="password"
            v-model.trim="cardForm.name"
            size="medium"
          ></el-input>
        </el-form-item>
        <el-form-item label="总金额">
          <el-input
            v-model.trim="cardForm.name"
            size="medium"
            class="cell"
          ></el-input>
        </el-form-item>
        <el-form-item label="应扣金额">
          <el-input
            v-model.trim="cardForm.name"
            size="medium"
            class="cell"
          ></el-input>
        </el-form-item>
        <el-form-item label="折扣金额">
          <el-input
            v-model.trim="cardForm.name"
            size="medium"
            class="cell"
          ></el-input>
        </el-form-item>
        <el-form-item label="实扣金额">
          <el-input
            v-model.trim="cardForm.name"
            size="medium"
            class="cell"
          ></el-input>
        </el-form-item>
        <el-form-item label="剩余金额">
          <el-input v-model.trim="cardForm.name" size="medium"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cardPayShow = false" size="small" class="btn-footer"
          >取消</el-button
        >
        <el-button
          class="blue_btn btn-footer"
          @click="cardPayShow = false"
          size="small"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <!-- 缴费弹窗 -->
    <el-dialog
      :visible.sync="payShow"
      width="872px"
      top="8%"
      custom-class="payShow"
      :close-on-click-modal="false"
    >
      <div slot="title" class="dialog-title">缴费窗口</div>
      <div>
        <div class="pay-info">
          <ul>
            <li><span>体检号：</span>{{ userInfo.regNo }}</li>
            <li><span>姓名：</span>{{ userInfo.name }}</li>
            <li><span>性别：</span>{{ userInfo.sex }}</li>
            <li><span>年龄：</span>{{ userInfo.age }}岁</li>
          </ul>
          <ul>
            <li>
              <span>类型：</span>
              <el-radio-group v-model.trim="radio">
                <el-radio :label="1" class="radio">个人</el-radio>
                <el-radio :label="2" class="radio">单位</el-radio>
              </el-radio-group>
            </li>
            <li class="invoice-title">
              <span>发票抬头</span>
              <el-input
                v-model.trim="invoiceHeader"
                size="medium"
                style="width: 86%; font-size: 18px"
              ></el-input>
            </li>
          </ul>
        </div>
        <div>
          <ul class="price-wrap">
            <li>
              应收金额：<span class="cell_blue">{{
                handlePrice(payInfo.price)
              }}</span>
            </li>
            <li>
              实收金额：<span class="cell_red">{{ payPaidInAmount }}</span>
            </li>
            <li>
              找零：<span class="cell_red">{{ payChange }}</span>
            </li>
          </ul>
          <div class="price-btn">
            <ul>
              <li v-for="(item, index) in payInfo.payPlans" :key="index">
                <span>{{ item.payName }}</span>
                <el-input
                  :ref="`input_ref${index}`"
                  v-model.trim="item.payPrice"
                  size="medium"
                  style="width: 35%; margin-left: 10px"
                  @focus="payFocus(index, item)"
                  class="price-input"
                  :disabled="loading ? true : item.hasPaid"
                  oninput="value=value.replace(/[^\d]/g,'')"
                  @input="inputChange(index, item)"
                  @keyup.enter.native="singlePayClick(item)"
                ></el-input>
                <el-button
                  class="red_btn pay-btn"
                  size="small"
                  v-show="item.hasPaid"
                  @click="refundClick(item)"
                  >退款</el-button
                >
                <el-button
                  class="blue_btn pay-btn"
                  size="small"
                  v-show="!item.hasPaid"
                  @click="singlePayClick(item)"
                  >支付</el-button
                >
              </li>
            </ul>
            <div class="pay-type">
              <div v-show="showSuccess">
                <p class="iconfont icon-zhifuchenggong icon"></p>
                <p>{{ successName }}缴费成功</p>
                <p class="pay-success">{{ successPrice }}元</p>
              </div>
              <div>
                <p
                  v-show="!showSuccess && !loading"
                  :class="`iconfont ${iconName}`"
                  style="font-size: 120px"
                ></p>
                <div v-show="loading">
                  <p
                    class="loading"
                    v-loading="loading"
                    element-loading-text="扫码中..."
                    element-loading-spinner="el-icon-loading"
                  ></p>
                  <el-button
                    style="margin-top: 50px"
                    @click="cancelCode"
                    class="blue_btn btn"
                    size="small"
                    >取消扫码</el-button
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer" class="pay-footer">
        <el-button class="red_btn btn-footer" @click="refundAll" size="small"
          >全部退款</el-button
        >
        <div>
          <el-button @click="payShow = false" size="small" class="btn-footer"
            >取消</el-button
          >
          <el-button
            class="blue_btn btn-footer"
            @click="saveSettlement"
            size="small"
            :disabled="disabled"
            >确定</el-button
          >
        </div>
      </div>
    </el-dialog>
    <!-- 发票修改弹窗 -->
    <el-dialog
      :visible.sync="invoiceEdit"
      width="1016px"
      top="4%"
      custom-class="invoiceEdit"
      :close-on-click-modal="false"
    >
      <div slot="title" class="dialog-title">发票修改</div>
      <div class="edit-btn">
        <span>操作员</span>
        <el-select
          style="width: 18%; margin-right: 20px"
          v-model.trim="editForm.operatorCode"
          placeholder="请选择操作员"
          size="small"
          filterable
        >
          <el-option
            :label="item.label"
            :value="item.value"
            v-for="item in G_sysOperator"
            :key="item.value"
          ></el-option>
        </el-select>
        <span>结算日期</span>
        <el-date-picker
          :picker-options="{ shortcuts: G_datePickerShortcuts }"
          style="width: 30%; margin-right: 20px"
          v-model.trim="editForm.settlementTime"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          :clearable="false"
        >
        </el-date-picker>
        <el-button
          style="margin-right: 8px"
          size="small"
          class="blue_btn btn"
          icon="iconfont icon-search"
          @click="editSearchClick"
          >查询</el-button
        >
        <el-button
          size="small"
          icon="iconfont icon-baocun"
          class="blue_btn btn"
          @click="saveClick"
          >保存</el-button
        >
      </div>
      <div class="edit-table">
        <PublicTable
          ref="editTable"
          :viewTableList.sync="editInvoiceData"
          :theads.sync="editTheads"
          :columnWidth="columnWidth"
          :columnAlign="columnAlign"
        >
          <template #editInvoiceNo="{ scope }">
            <span v-show="!scope.row.inputShow">{{
              scope.row.editInvoiceNo
            }}</span>
            <el-input
              :maxlength="10"
              v-model="scope.row.editInvoiceNo"
              v-show="scope.row.inputShow"
              size="small"
              @blur="editInputBlur(scope.row)"
              @keyup.enter.native="editInputBlur(scope.row)"
            ></el-input>
          </template>
          <template #price="{ scope }">
            <div class="cell-price">
              {{ handlePrice(scope.row.price) }}
            </div>
          </template>
          <template #operation="{ scope }">
            <el-button type="text" @click="editRowClick(scope.row)"
              >编辑</el-button
            >
          </template>
        </PublicTable>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import PublicTable from '@/components/publicTable';
import Unpaid from '../../mixins/unpaid';
import { mapGetters } from 'vuex';
export default {
  name: 'unpaid',
  mixins: [Unpaid],
  components: {
    PublicTable
  },
  data() {
    return {};
  },
  computed: {
    ...mapGetters(['G_datePickerShortcuts'])
  },
  methods: {}
};
</script>

<style lang="less" scoped>
.unpaid {
  height: 100%;
  display: flex;
  justify-content: space-between;
  color: #2d3436;
  overflow: auto;
  .unpaid-list,
  .list-info {
    width: 50%;
    background: #fff;
    padding: 18px;
    border-radius: 4px;
  }
  .unpaid-list {
    margin-right: 18px;
    display: flex;
    flex-direction: column;
  }
  .list-info {
    display: flex;
    flex-direction: column;
    overflow: auto;
  }
  .search-wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 18px;
    h3 {
      font-size: 18px;
      flex: 1;
    }
  }
  .search-btn {
    flex: 2;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    span {
      font-weight: 600;
      font-size: 14px;
    }
  }
  .date {
    width: 40%;
    margin: 0 10px;
  }
  .list-table {
    flex: 1;
    border: 1px solid #d8dee1;
    border-radius: 4px;
    overflow: auto;
  }
  .btn {
    padding: 6.5px 10px;
  }
  .info-btn {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-bottom: 18px;
  }
  .info-form {
    margin-bottom: 8px;
    .el-form-item {
      margin-bottom: 10px;
    }
  }
  .comb-list {
    flex: 1;
    flex-shrink: 0;
    border-radius: 4px;
    // overflow: hidden;
    border: 1px solid #d8dee1;
    margin-bottom: 18px;
    display: flex;
    flex-direction: column;
  }
  .comb-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 18px;
    background: #cadcf4;
    border-bottom: 1px solid #d8dee1;
    h3 {
      font-size: 18px;
    }
  }
  .comb-price {
    font-weight: 600;
    display: flex;
    align-items: center;
    .price-item {
      font-size: 14px;
      margin-right: 20px;
    }
    .numb {
      font-size: 18px;
    }
  }
  .list-box {
    flex: 1;
    flex-shrink: 0;
    max-height: 230px;
    overflow: auto;
  }
  .list-wrap {
    display: grid;
    grid-template-columns: auto auto;
  }
  .empty {
    text-align: center;
    padding: 109px 0;
    font-size: 14px;
    color: #909399;
    font-weight: 600;
  }
  .list-item {
    padding: 8px 18px;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-right: 1px solid #d8dee1;
    border-bottom: 1px solid #d8dee1;
    &:nth-child(2n) {
      border-right: 0;
    }
    // &:nth-child(5n - 1) {
    //   background: #dfe9f5;
    // }
    // &:nth-child(4n - 1) {
    //   background: #dfe9f5;
    // }
    span:first-child {
      width: 182px;
    }
  }
  .cell-price {
    text-align: right;
  }
  .info-table {
    flex: 2;
    flex-shrink: 0;
    border: 1px solid #d8dee1;
    border-radius: 4px;
    overflow: auto;
  }
  .dialog-title {
    background: #d1e2f9;
    font-size: 18px;
    padding: 18px;
    font-weight: 600;
  }
  .btn-footer {
    padding: 9px 28px;
  }
  .search-table {
    border: 1px solid #d8dee1;
    border-radius: 4px;
    height: 100%;
    overflow: auto;
  }
  .cardForm {
    /deep/.el-form-item__label {
      font-size: 18px;
    }
    .name {
      font-size: 24px;
      color: #1770df;
      font-weight: 600;
    }
  }
  .cell {
    /deep/.el-input__inner {
      color: #d63031;
      font-size: 24px;
    }
  }
  .pay-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .pay-info {
    background: #f7f8f9;
    padding: 18px;
    font-size: 18px;
    color: #2d3436;
    ul {
      display: flex;
      align-items: center;
      margin-bottom: 24px;
      &:last-child {
        margin-bottom: 0;
      }
    }
    li {
      margin-right: 38px;
      span {
        font-weight: 600;
      }
    }
  }
  .invoice-title {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-right: 0;
  }
  .price-wrap {
    font-size: 18px;
    color: #2d3436;
    padding: 36px 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    li {
      // margin-right: 66px;
      font-weight: 600;
      display: flex;
      align-items: center;
      span {
        font-size: 32px;
      }
    }
  }
  .price-btn {
    color: #2d3436;
    background: #f7f8f9;
    margin: 0 18px;
    border-radius: 4px;
    padding: 26px 18px;
    font-size: 18px;
    display: flex;
    justify-content: space-between;
    ul {
      flex: 1;
      flex-shrink: 0;
      border-right: 1px solid #929697;
      height: 252px;
      overflow: auto;
    }
    li {
      margin-bottom: 18px;
      display: flex;
      align-items: center;
      &:last-child {
        margin-bottom: 0;
      }
    }
    span {
      display: inline-block;
      width: 56px;
      text-align: right;
    }
    .pay-btn {
      margin-left: 18px;
    }
  }
  .pay-type {
    flex: 1;
    flex-shrink: 0;
    text-align: center;
    // padding-top: 87px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .icon {
    font-size: 58px;
    color: #1770df;
    margin-bottom: 18px;
  }
  .pay-success {
    font-size: 24px;
    color: #d63031;
    margin-top: 10px;
  }
  .edit-btn {
    margin-bottom: 18px;
    color: #2d3436;
    display: flex;
    align-items: center;
    span {
      font-weight: 600;
      margin-right: 10px;
    }
  }
  .edit-table {
    height: calc(100% - 52px);
    border: 1px solid #d8dee1;
    border-radius: 4px;
    overflow: auto;
  }
  .loading {
    /deep/.el-loading-text,
    /deep/.el-loading-spinner i {
      font-size: 18px;
      color: #1770df;
    }
    /deep/.el-loading-spinner i {
      font-size: 34px;
    }
  }
  .price-input {
    /deep/.el-input__inner {
      color: #2d3436;
      font-size: 24px;
      text-align: right;
    }
  }
  .medium-input {
    /deep/.el-input__inner {
      color: #2d3436;
      font-size: 24px;
    }
  }
  .radio /deep/.el-radio__inner {
    width: 16px;
    height: 16px;
  }
  .radio /deep/.el-radio__label {
    font-size: 18px;
    color: #2d3436;
  }
  /deep/.el-form-item__label {
    color: #2d3436;
    font-weight: 600;
  }
  /deep/.el-dialog__header {
    padding: 0;
  }
}
</style>
<style lang="less">
.unpaid {
  .invoiceShow {
    .el-dialog__body {
      padding-right: 48px;
      padding-bottom: 8px;
    }
    .el-dialog__footer {
      padding-top: 0;
      padding-right: 48px;
    }
  }
  .invoiceSearch {
    height: 838px;
    .el-dialog__body {
      padding: 18px;
      height: calc(100% - 60px);
    }
  }
  .cardPayShow {
    .el-dialog__body {
      padding: 18px 28px;
    }
    .el-dialog__footer {
      padding: 6px 28px 48px 18px;
    }
  }
  .payShow {
    .el-dialog__body {
      padding: 0;
    }
    .el-dialog__footer {
      padding: 48px 18px;
    }
  }
  .invoiceEdit {
    height: 842px;
    .el-dialog__body {
      padding: 18px;
      height: calc(100% - 60px);
    }
  }
}
</style>
