<template>
  <div class="settlementRecord">
    <div class="leftCont">
      <el-header style="height: 48px; line-height: 48px">
        <div class="searchHear">
          <span class="title"> 结算记录列表：</span>
          <BtnCommon
            :btnList="['导出', '打印', '查询']"
            @exports="exports"
            @prints="prints"
            @search="search"
          />
        </div>
      </el-header>
      <div class="searchWrap">
        <el-form :model="searchForm" label-width="80px">
          <el-row>
            <el-col :span="12">
              <el-form-item label="发票号">
                <el-input
                  v-model.trim="searchForm.invoiceNo"
                  size="small"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="体检号">
                <el-input
                  v-model.trim="searchForm.regNo"
                  size="small"
                  placeholder="请输入"
                  clearable
                  @keyup.enter.native="search"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="档案卡号">
                <el-input
                  v-model.trim="searchForm.patCode"
                  size="small"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="操作员">
                <el-select
                  v-model.trim="searchForm.operatorCode"
                  placeholder="请选择"
                  size="small"
                  clearable
                >
                  <el-option
                    :label="item.label"
                    :value="item.value"
                    v-for="item in G_sysOperator"
                    :key="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="姓名">
                <el-input
                  v-model.trim="searchForm.name"
                  size="small"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="结算日期" class="settlementDate">
                <el-date-picker
                  :picker-options="{ shortcuts: G_datePickerShortcuts }"
                  v-model.trim="settlementDate"
                  type="daterange"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :clearable="false"
                  @change="search"
                  size="small"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="tableBody">
        <div class="titleCont">
          <div style="color: #2d3436">
            <span>已收费</span>
            <span class="colorDiv" style="background: #2d3436"></span>
          </div>
          <div style="color: #d63031">
            <span>已退费</span>
            <span class="colorDiv" style="background: #d63031"></span>
          </div>
          <div style="color: #7364f4">
            <span>被退费</span>
            <span class="colorDiv" style="background: #7364f4"></span>
          </div>
        </div>
        <div class="tableCont">
          <PublicTable
            :viewTableList.sync="tableData"
            :theads.sync="theads"
            :tableLoading.sync="loading"
            @rowClick="handleCurrentChange"
            :tableRowClassName="tableRowClassName"
            :isSortShow="false"
            :columnWidth="columnWidth"
            :columnAlign="columnAlign"
          >
            <template #settlementType="{ scope }">
              <div>
                {{ settlementType[scope.row.settlementType] }}
              </div>
            </template>
          </PublicTable>
        </div>
        <div class="price_wrap">
          <div class="totalPrice">
            <div>
              <p>合计</p>
              <p>
                现金: <span>{{ totalPrices.totalCash }}</span>
              </p>
              <p>
                银联: <span>{{ totalPrices.totalUnionPay }}</span>
              </p>
              <p>
                微信: <span>{{ totalPrices.totalWeChat }}</span>
              </p>
            </div>
            <div>
              <p>
                总金额： <span>{{ totalPrices.totalPrice }}</span>
              </p>
              <p>
                支付宝: <span>{{ totalPrices.totalAlipay }}</span>
              </p>
              <p>
                储值卡: <span>{{ totalPrices.totalStoredValueCard }}</span>
              </p>
              <p>
                其他: <span>{{ totalPrices.totalOther }}</span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="rightCont">
      <div class="headerTitle">
        <div class="header" ref="headerTitle">
          <el-descriptions class="margin-top" title="" :column="2">
            <template slot="extra">
              <BtnCommon
                :btnList="['清空', '部分退款', '退款', '重打']"
                @emptys="emptys"
                @rebates="rebates"
                @refunds="refunds"
                @reprints="reprints"
              />
            </template>
            <el-descriptions-item label="结算号"
              ><span class="blueColor">{{
                rightType.settlementNo
              }}</span></el-descriptions-item
            >
            <el-descriptions-item label="姓名"
              ><span class="blueColor">{{
                rightType.name
              }}</span></el-descriptions-item
            >
            <el-descriptions-item label="发票号">{{
              rightType.invoiceNo
            }}</el-descriptions-item>
            <el-descriptions-item label="档案卡号">{{
              rightType.patCode
            }}</el-descriptions-item>
            <el-descriptions-item label="体检号">{{
              rightType.regNo
            }}</el-descriptions-item>
            <el-descriptions-item label="发票抬头">{{
              rightType.invoiceHeader
            }}</el-descriptions-item>
            <el-descriptions-item label="工作单位">{{
              rightType.companyName
            }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      <div class="rightTableCont">
        <PublicTable
          :viewTableList.sync="rightTableData"
          :theads.sync="righttheads"
          :tableLoading.sync="loading"
          :columnWidth="columnWidths"
          :columnAlign="columnAlign"
        >
          <template #price="{ scope }">
            <div class="redColor">
              {{ scope.row.price }}
            </div>
          </template>
        </PublicTable>
      </div>
      <div class="price_wrap1">
        <p>合计</p>
        <p>
          实收金额： <span>{{ rightPrice }}</span>
        </p>
      </div>
    </div>
    <el-dialog
      title="部分退款"
      :visible.sync="dialog"
      :before-close="handleClose"
      :wrapperClosable="false"
    >
      <div class="dialogHear">
        <div class="title">选择要退款的项目：</div>
        <div style="display: flex">
          <div style="margin-right: 50px">
            <span style="margin-right: 10px">退款金额:</span
            ><span class="redColor"
              >¥<span>{{ refund }}</span></span
            >
          </div>
          <div>
            <el-button size="small" class="blue_btn" @click="confirmRefund"
              >确定</el-button
            >
          </div>
        </div>
      </div>
      <div class="dialogTable">
        <PublicTable
          :viewTableList.sync="dialogTableData"
          :theads.sync="dialogTheads"
          :tableLoading.sync="loading"
          @selectionChange="handleSelRow"
          isCheck
          :selectable="selectable"
          :tableRowClassName="dialogRowClassName"
          :isStripe="false"
          :columnWidth="dialogColumnWidth"
          :columnAlign="columnAlign"
        >
          <template #price="{ scope }">
            <div class="redColor">
              {{ scope.row.price }}
            </div>
          </template>
        </PublicTable>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import BtnCommon from '../btnCommon.vue';
import PublicTable from '@/components/publicTable.vue';
import { mapGetters } from 'vuex';
import { dataUtils } from '@/common';
export default {
  name: 'settlementRecord',
  components: { BtnCommon, PublicTable },
  data() {
    return {
      searchForm: {
        invoiceNo: '',
        regNo: '',
        patCode: '',
        operatorCode: '',
        name: '',
        startTime: '',
        endTime: ''
      },
      settlementDate: [dataUtils.getDate(), dataUtils.getDate()], //结算日期
      loading: false,
      theads: {
        settlementNo: '结算号',
        invoiceNo: '发票号',
        regNo: '体检号',
        patCode: '档案卡号',
        name: '姓名',
        settlementTime: '结算时间',
        price: '总金额',
        cash: '现金',
        unionPay: '银联',
        weChat: '微信',
        alipay: '支付宝',
        storedValueCard: '储值卡',
        other: '其他'
        // companyName: "工作单位",
        // invoiceHeader: "发票抬头",
        // settlementType: "结算类型",
        // paymentName: "支付类型",
      },
      tableData: [],
      columnWidth: {
        name: 75,
        settlementNo: 120,
        invoiceNo: 130,
        regNo: 140,
        patCode: 120,
        settlementTime: 170,
        price: 100,
        cash: 100,
        unionPay: 100,
        weChat: 100,
        alipay: 100,
        storedValueCard: 100,
        other: 100
      },
      settlementType: { 1: '结算', 2: '结算中', 3: '退款' },
      totalPrices: {
        totalPrice: '0.00',
        totalCash: '0.00',
        totalUnionPay: '0.00',
        totalWeChat: '0.00',
        totalAlipay: '0.00',
        totalStoredValueCard: '0.00',
        totalOther: '0.00'
      }, //左边合计
      rightType: {
        settlementNo: '',
        name: '',
        invoiceNo: '',
        patCode: '',
        regNo: '',
        invoiceHeader: '',
        companyName: ''
      },
      righttheads: {
        combName: '项目名称',
        originalPrice: '原价',
        price: '实收金额'
      },
      rightTableData: [],
      columnWidths: { originalPrice: 120, price: 120 },
      rightPrice: '0.00', //右边合计
      dialog: false,
      dialogTheads: {
        itemCode: '项目代码',
        itemName: '项目名称',
        originalPrice: '原价',
        price: '实收金额'
      },
      dialogColumnWidth: { itemName: 300 },
      dialogTableData: [],
      settlementType: '',
      settlementNo: '',
      refund: '0.00', //部分退款金额
      dialogSelArr: [],
      columnAlign: {
        price: 'right',
        cash: 'right',
        unionPay: 'right',
        weChat: 'right',
        alipay: 'right',
        storedValueCard: 'right',
        other: 'right',
        originalPrice: 'right'
      }
    };
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_sysOperator', 'G_datePickerShortcuts'])
  },
  mounted() {
    this.search();
  },

  methods: {
    //关闭抽屉
    handleClose() {
      this.dialog = false;
      this.search();
    },
    //给左边表格行文字加上动态颜色
    tableRowClassName({ row, rowIndex }) {
      if (row.settlementType == '2') {
        if (row.refunded) {
          //被退费
          return 'tr_violet';
        }
        return 'tr_black';
      } else if (row.settlementType == '3') {
        return 'tr_red';
      } else {
        return 'tr_black';
      }
    },
    // 弹出表格行是否可选
    selectable(row, index) {
      if (row.isCheck) {
        return false;
      } else {
        return true;
      }
    },
    //弹出表格行不可选时加灰背景颜色
    dialogRowClassName({ row, rowIndex }) {
      if (row.isCheck) {
        return 'tr_gray';
      } else {
        return '';
      }
    },
    regNoEnter() {
      this.searchForm.startTime = '';
      this.searchForm.endTime = '';
      this.$ajax
        .post(
          this.$apiUrls.ReadSettlementRecordsByMultipleFilter,
          this.searchForm
        )
        .then((r) => {
          console.log('r', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.tableData = returnData.records || [];
          let total = {};
          if (returnData.records.length >= 1) {
            total = {
              totalPrice: returnData.totalPrice,
              totalCash: returnData.totalCash,
              totalUnionPay: returnData.totalUnionPay,
              totalWeChat: returnData.totalWeChat,
              totalAlipay: returnData.totalAlipay,
              totalStoredValueCard: returnData.totalStoredValueCard,
              totalOther: returnData.totalOther
            };
            this.totalPrices = total;
          } else {
            this.totalPrices = {
              totalPrice: '0.00',
              totalCash: '0.00',
              totalUnionPay: '0.00',
              totalWeChat: '0.00',
              totalAlipay: '0.00',
              totalStoredValueCard: '0.00',
              totalOther: '0.00'
            };
            this.rightType = {
              settlementNo: '',
              name: '',
              invoiceNo: '',
              patCode: '',
              regNo: '',
              invoiceHeader: '',
              companyName: ''
            };
            this.rightTableData = [];
            this.rightPrice = '0.00';
            this.$message({
              message: '暂无数据!',
              type: 'success',
              showClose: true
            });
          }
        });
    },
    //查询
    search() {
      if (this.searchForm.regNo) {
        this.searchForm.startTime = '';
        this.searchForm.endTime = '';
      } else {
        this.searchForm.startTime = this.settlementDate
          ? this.settlementDate[0]
          : dataUtils.getDate();
        this.searchForm.endTime = this.settlementDate
          ? this.settlementDate[1]
          : dataUtils.getDate();
      }
      this.tableData = [];
      this.$ajax
        .post(
          this.$apiUrls.ReadSettlementRecordsByMultipleFilter,
          this.searchForm
        )
        .then((r) => {
          console.log('r', r);
          let { success, returnData } = r.data;
          if (!success) return;
          if (returnData === null) return;
          this.tableData = returnData.records || [];
          let total = {};
          if (returnData.records.length >= 1) {
            total = {
              totalPrice: returnData.totalPrice,
              totalCash: returnData.totalCash,
              totalUnionPay: returnData.totalUnionPay,
              totalWeChat: returnData.totalWeChat,
              totalAlipay: returnData.totalAlipay,
              totalStoredValueCard: returnData.totalStoredValueCard,
              totalOther: returnData.totalOther
            };
            this.totalPrices = total;
          } else {
            this.totalPrices = {
              totalPrice: '0.00',
              totalCash: '0.00',
              totalUnionPay: '0.00',
              totalWeChat: '0.00',
              totalAlipay: '0.00',
              totalStoredValueCard: '0.00',
              totalOther: '0.00'
            };
            this.rightType = {
              settlementNo: '',
              name: '',
              invoiceNo: '',
              patCode: '',
              regNo: '',
              invoiceHeader: '',
              companyName: ''
            };
            this.rightTableData = [];
            this.rightPrice = '0.00';
            this.$message({
              message: '暂无数据!',
              type: 'success',
              showClose: true
            });
          }
        });
    },
    //导出
    exports() {},
    //打印
    prints() {},
    //点击左边表格行
    handleCurrentChange(row) {
      this.settlementType = row.settlementType;
      this.settlementNo = row.settlementNo;
      this.$ajax
        .post(this.$apiUrls.ReadSettlementCombs, '', {
          query: { settlementNo: row.settlementNo }
        })
        .then((r) => {
          console.log('r', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.rightType = row;
          this.rightTableData = returnData.combs || [];
          this.rightPrice = returnData.total || '0.00';
        });
    },

    //清空
    emptys() {
      this.rightType = {};
      this.rightTableData = [];
      this.rightPrice = '0.00';
    },
    //部分退款
    rebates() {
      if (!this.settlementNo) {
        this.$message({
          message: '请先选择要部分退款的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      } else {
        this.dialog = true;
        this.$nextTick(() => {
          this.dialogTableData = [
            {
              itemCode: 'J1003',
              itemName: '血常规（四分类）',
              originalPrice: '502.00',
              price: '513.12',
              isCheck: false
            },
            {
              itemCode: 'J1003',
              itemName: '血常规（五分类）',
              originalPrice: '563.00',
              price: '563.12',
              isCheck: false
            },
            {
              itemCode: 'J1005',
              itemName: '血常规',
              originalPrice: '12.00',
              price: '12.23',
              isCheck: true
            },
            {
              itemCode: 'J1006',
              itemName: '甲胎蛋白性测定 (AFP) 各种免疫学方法 (0.9)',
              originalPrice: '22.22',
              price: '22.35',
              isCheck: true
            }
          ];
          return;
          this.getDialogData();
        });
      }
    },
    //获取弹出表格数据
    getDialogData() {
      this.$ajax
        .post(this.$apiUrls.Refund, '', {
          query: { settleNo: this.settlementNo }
        })
        .then((r) => {
          console.log('r', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.dialogTableData = returnData || [];
        });
    },
    //确认退款
    confirmRefund() {
      if (this.dialogSelArr.length < 1) {
        this.$message({
          message: '请先选择要退款的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm('是否确认要退款?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$ajax
          .post(this.$apiUrls.SaveRegisterOrder, this.dialogSelArr)
          .then((r) => {
            let { success } = r.data;
            if (!success) return;
            this.$message({
              message: '部分退款成功',
              type: 'success',
              showClose: true
            });
            this.getDialogData();
          });
      });
    },
    //部分退款弹出框勾选
    handleSelRow(row) {
      this.dialogSelArr = [];
      let totalSumAll = 0;
      row.map((item) => {
        this.dialogSelArr.push(item.itemCode);
        //计算勾选总金额
        if (!isNaN(item.price)) totalSumAll += item.price * 100;
      });
      if (isNaN(totalSumAll)) {
        return 0;
      }
      this.refund = (totalSumAll / 100).toFixed(2);
    },
    //退款
    refunds() {
      if (!this.settlementNo) {
        this.$message({
          message: '请先选择要退款的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.settlementType == '3') {
        this.$message({
          message: '已退款的数据不能再次退款!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm('是否确认要退款该条数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$ajax
          .post(this.$apiUrls.Refund, '', {
            query: { settleNo: this.settlementNo }
          })
          .then((r) => {
            let { success } = r.data;
            if (!success) return;
            this.$message({
              message: '退款成功',
              type: 'success',
              showClose: true
            });
            this.search();
          });
      });
    },
    //重打
    reprints() {}
  }
};
</script>

<style lang="less" scoped>
.settlementRecord {
  display: flex;
  width: 100%;
  height: 100%;
  border-radius: 4px;
  display: flex;
  color: #2d3436;
  font-weight: 600;
  .leftCont {
    width: 60%;
    background-color: #fff;
    padding: 18px;
    display: flex;
    flex-direction: column;
    .el-header {
      padding: 0;
      height: 42px;
    }
    .searchHear {
      display: flex;
      justify-content: space-between;
    }
    .searchWrap {
      /deep/.settlementDate .el-date-editor.el-input,
      .el-date-editor.el-input__inner {
        width: 100%;
      }
    }
    .tableBody {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: auto;
      flex-shrink: 0;
      .titleCont {
        display: flex;
        height: 32px;
        & > div {
          margin-right: 18px;
          & > span {
            display: inline-block;
          }
          .colorDiv {
            width: 30px;
            line-height: 30px;
            height: 16px;
            vertical-align: middle;
            margin-left: 10px;
          }
        }
      }
      .tableCont {
        flex: 1;
        flex-shrink: 0;
        overflow: auto;
        border: 1px solid #d8dee1;
      }
    }
  }
  .rightCont {
    flex: 1;
    margin-left: 18px;
    background-color: #fff;
    padding: 18px;
    display: flex;
    flex-direction: column;
    overflow: auto;
    .headerTitle {
      min-height: 147px;
      border-radius: 4px;
      background: #fff;
      padding-bottom: 0;
    }
    .rightTableCont {
      flex: 1;
      margin-top: 10px;
      overflow: auto;
      border: 1px solid #d8dee1;
    }
  }
  .price_wrap {
    // display: flex;
    // justify-content: space-between;
    font-size: 18px;
    line-height: 36px;
    background: rgba(23, 112, 223, 0.1);
    border-radius: 4;
    overflow: hidden;
    color: #d63031;
    font-weight: 600;
    padding: 0 15px;
    .totalPrice {
      & > div {
        display: flex;
        & > p {
          flex: 1;
        }
      }
    }
  }
  .price_wrap1 {
    display: flex;
    justify-content: space-between;
    font-size: 18px;
    line-height: 36px;
    background: rgba(23, 112, 223, 0.1);
    border-radius: 4;
    overflow: hidden;
    color: #d63031;
    font-weight: 600;
    padding: 0 15px;
  }
  /deep/.el-dialog {
    height: 80%;
    width: 700px;
    display: flex;
    flex-direction: column;
    .dialogHear {
      display: flex;
      justify-content: space-between;
      line-height: 32px;
      height: 42px;
      font-family: PingFangSC-Regular;
      font-size: 18px;
      color: #2d3436;
    }
    .dialogTable {
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
      border: 1px solid #d8dee1;
    }
  }
  .el-form-item {
    margin-bottom: 10px;
  }
  /deep/.el-select {
    width: 100%;
  }
  /deep/.el-input__inner,
  /deep/.el-range__icon {
    height: 32px;
    line-height: 32px;
  }
  /deep/.el-dialog__header {
    height: 50px;
    line-height: 50px;
    background: rgba(23, 112, 223, 0.2);
    padding: 0 18px;
  }
  /deep/.el-dialog__headerbtn {
    top: 10px;
    font-size: 30px;
  }
  /deep/.el-dialog__body {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 10px 18px 18px 18px;
    flex-shrink: 0;
    overflow: auto;
  }
  /deep/.el-form-item__label,
  /deep/.el-descriptions__body,
  /deep/.el-descriptions__body
    .el-descriptions__table
    .el-descriptions-item__cell {
    color: #2d3436;
    font-weight: 600 !important;
  }
  /deep/.el-form-item__label,
  /deep/.el-form-item__content {
    line-height: 32px;
  }
  /deep/.el-descriptions-item__container {
    align-items: center;
  }
  .redColor {
    color: #d63031;
    font-size: 14px;
    & > span {
      font-size: 24px;
      margin-left: 4px;
    }
  }
  .blueColor {
    font-family: PingFangSC-Medium;
    font-size: 18px;
    color: #1770df;
  }
}
</style>
