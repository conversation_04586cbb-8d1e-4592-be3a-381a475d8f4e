<template>
  <!-- 个人收费 -->
  <div class="personCharge">
    <div class="header">
      <div class="header-tabs">
        <div
          class="tabs-item"
          :class="active === index ? 'active' : ''"
          v-for="(item, index) in tabsData"
          :key="index"
          @click="handleClick(item.view, index)"
        >
          {{ item.title }}
        </div>
      </div>
      <div class="invoice-number" v-show="componentName === 'Unpaid'">
        下一发票号：<span class="cell_red">{{ nextInvoice }}</span>
        <span class="iconfont icon-bianji icon" @click="editClick"></span>
      </div>
    </div>
    <div class="content">
      <keep-alive>
        <component :is="componentName" ref="component_ref"></component>
      </keep-alive>
    </div>
  </div>
</template>

<script>
import Unpaid from './components/personCharge/unpaid';
import SettlementRecord from './components/personCharge/settlementRecord';
export default {
  name: 'personCharge',
  components: {
    Unpaid,
    SettlementRecord
  },
  data() {
    return {
      tabsData: [
        {
          title: '未缴费',
          view: 'Unpaid'
        },
        {
          title: '结算记录',
          view: 'SettlementRecord'
        }
      ],
      componentName: 'Unpaid',
      active: 0,
      show: false,
      nextInvoice: '0'
    };
  },
  methods: {
    // tab切换
    handleClick(view, index) {
      this.componentName = view;
      this.active = index;
    },
    // 发票修改
    editClick() {
      this.$refs.component_ref.invoiceShow = true;
      this.$refs.component_ref.getInvoice();
      this.$refs.component_ref.cancelBtn = true;
    }
  }
};
</script>

<style lang="less" scoped>
.personCharge {
  display: flex;
  flex-direction: column;
  height: 100%;
  color: #2d3436;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    padding-left: 8px;
    padding-right: 18px;
    border-radius: 4px;
    margin-bottom: 18px;
  }
  .header-tabs {
    display: flex;
  }
  .tabs-item {
    width: 88px;
    margin: 0 34px;
    padding: 18px 0;
    text-align: center;
    font-size: 16px;
    cursor: pointer;
  }
  .active {
    color: #1770df;
    border-bottom: 2px solid #1770df;
  }
  .invoice-number {
    font-weight: 600;
  }
  .icon {
    margin-left: 20px;
    cursor: pointer;
    color: #2d3436;
  }
  .content {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
  }
}
</style>
