<template>
  <!-- 团体结算(按医院分类) -->
  <div class="classifiedByHospital">
    <div class="classifiedByHospital-wrap">
      <div class="search-operate">
        <div class="operate-item">
          <span style="width: 70px">登记时间</span>
          <el-date-picker
            :picker-options="{ shortcuts: G_datePickerShortcuts }"
            v-model.trim="searchInfo.date"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :clearable="false"
            size="small"
            class="select"
          >
          </el-date-picker>
        </div>
        <div class="operate-item">
          <span>体检单位</span>
          <el-cascader
            ref="cascader_ref"
            v-model="casValue"
            :options="options"
            :props="{ multiple: true }"
            clearable
            filterable
            size="small"
            collapse-tags
            class="select"
            @change="getChecked"
            :filter-method="filterMethod"
          >
          </el-cascader>
        </div>
        <div class="operate-item">
          <span>单位部门</span>
          <el-select
            placeholder="请选择"
            size="small"
            filterable
            clearable
            v-model="searchInfo.deptCode"
            class="select"
            :disabled="!searchInfo.companyCode"
          >
            <el-option
              v-for="item in deptCodeList"
              :key="item.deptCode"
              :label="item.deptName"
              :value="item.deptCode"
            >
            </el-option>
          </el-select>
        </div>
        <div class="operate-item">
          <el-checkbox v-model="searchInfo.checked">已体检</el-checkbox>
        </div>
        <div class="operate-item">
          <BtnCommon :btnList="['查询', '打印']" @search="searchClick">
            <template slot="footAdd">
              <el-button
                size="small"
                class="yellow_btn btn"
                @click="exports"
                icon="iconfont icon-daochu"
                >导出</el-button
              >
            </template>
          </BtnCommon>
        </div>
      </div>
      <div class="legend">
        <span>实检人数：{{ infoList.checkCount }}人</span>
        <span>总金额：{{ infoList.totalPrice }}元</span>
      </div>
      <div class="table-wrap">
        <div class="table">
          <PublicTable
            :viewTableList.sync="tableData"
            :theads.sync="theads"
            :isSortShow="false"
          >
            <template #price="{ scope }">
              <div class="price">{{ scope.row.price }}</div>
            </template>
          </PublicTable>
        </div>
        <!-- <div class="total">
          <span>总计</span>
          <span>{{ infoList.totalPrice }}</span>
        </div> -->
      </div>
      <!-- <ul class="info-wrap">
        <li>
          <span>制表时间：{{ infoList.tabulationDate }}</span>
          <span>制表：{{ G_userInfo.codeOper.name }}</span>
        </li>
        <li>
          <span>
            1.体检日期：{{ infoList.beinDate }}至{{ infoList.endDate }}
          </span>
        </li>
        <li>
          <span>2.单位体检次数：{{ infoList.companyTimes }}</span>
        </li>
        <li>
          <span>3.收款单位：{{ infoList.payeeCompany }}</span>
        </li>
        <li>
          <span>4.开户银行：{{ infoList.openBank }}</span>
          <span>账号：{{ infoList.bankAccount }}</span>
        </li>
        <li>
          <span>
            5.纳税人识别号(社会信用代码)：{{ infoList.socialCreditCode }}
          </span>
        </li>
        <li>
          <span>6.地址：{{ infoList.address }}</span>
        </li>
      </ul> -->
    </div>
  </div>
</template>

<script>
import BtnCommon from './components/btnCommon.vue';
import PublicTable from '@/components/publicTable.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
export default {
  name: 'classifiedByHospital',
  components: { BtnCommon, PublicTable },
  data() {
    return {
      searchInfo: {
        date: [dataUtils.getDate(), dataUtils.getDate()],
        companyCode: '',
        companyTimes: [],
        deptCode: '',
        checked: false
      },
      companyList: [],
      options: [],
      casValue: [],
      shareScopeEnd: [],
      deptCodeList: [],
      tableData: [],
      theads: {
        feeClsName: '费用分类',
        price: '金额'
      },
      infoList: {
        checkCount: 0,
        totalPrice: 0
        // companyName: "",
        // tabulationDate: "",
        // beinDate: "",
        // endDate: "",
        // companyTimes: 0,
        // payeeCompany: "",
        // openBank: "",
        // bankAccount: "",
        // socialCreditCode: "",
        // address: ""
      }
    };
  },
  created() {
    this.getCompanyAndTimes();
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_userInfo', 'G_datePickerShortcuts'])
  },
  methods: {
    // 获取部门
    getDeptCode() {
      if (!this.searchInfo.companyCode) return;
      let data = {
        companyCode: this.searchInfo.companyCode
      };
      this.$ajax.post(this.$apiUrls.R_CodeCompanyDepartment, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.deptCodeList = returnData || [];
      });
    },
    // 获取单位和体检次数
    getCompanyAndTimes() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        let newData = [];
        newData = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${dataUtils.subBlankDate(
                  child.beginDate
                )}　${dataUtils.subBlankDate(child.endDate)}`
              };
            })
          };
        });
        this.options = newData;
      });
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    // 查询
    searchClick() {
      if (!this.searchInfo.companyCode) {
        this.$message({
          showClose: true,
          message: '请选择单位!',
          type: 'warning'
        });
        return;
      }
      let [beinDate, endDate] = this.searchInfo.date;
      let data = {
        beinDate: beinDate,
        endDate: endDate,
        companyCode: this.searchInfo.companyCode,
        companyTimes: this.searchInfo.companyTimes,
        deptCode: this.searchInfo.deptCode,
        checked: this.searchInfo.checked
      };
      this.$ajax
        .post(this.$apiUrls.CompanySettlementByFeeClsReport, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) {
            this.tableData = [];
            this.infoList = {
              checkCount: 0,
              totalPrice: 0
            };
          } else {
            this.tableData = returnData.combs || [];
            this.infoList = {
              checkCount: returnData.checkCount,
              totalPrice: returnData.totalPrice
            };
          }
        });
    },
    // 单位勾选切换
    getChecked(val) {
      if (val.length === 0) {
        this.searchInfo.companyCode = '';
        return;
      }
      let changeFlag = false;
      let changeItem = [];
      if (this.shareScopeEnd.length == 0) {
        this.casValue = val;
      } else {
        // 与原数组比对
        this.casValue.forEach((item) => {
          if (item[0] !== this.shareScopeEnd[0][0]) {
            // 一级标签不同
            changeFlag = true;
            changeItem.push(item);
          }
        });
      }
      if (changeFlag) {
        this.casValue = [];
        this.casValue = changeItem;
      }
      this.shareScopeEnd = this.casValue;
      if (val.length !== 0) {
        this.searchInfo.companyCode = this.casValue[0][0];
        let companyTimes = [];
        this.casValue.map((item) => {
          if (item) {
            companyTimes.push(item[1]);
          }
        });
        this.searchInfo.companyTimes = companyTimes;
      }
      this.getDeptCode();
    },
    // 导出
    exports() {}
  }
};
</script>

<style lang="less" scoped>
.classifiedByHospital {
  color: #2d3436;
  display: flex;
  flex-direction: column;
  .select {
    width: 100%;
  }
  .classifiedByHospital-wrap {
    background: #fff;
    flex: 1;
    overflow: auto;
    padding: 10px;
    display: flex;
    flex-direction: column;
  }
  .search-operate {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
  .operate-item {
    margin-right: 10px;
    display: flex;
    align-items: center;
    span {
      margin-right: 10px;
      font-weight: 600;
      font-size: 14px;
      width: 78px;
      &:last-child {
        margin-right: 0;
      }
    }
  }
  .legend {
    font-size: 14px;
    font-weight: 600;
    color: #1770df;
    margin-bottom: 10px;
    span {
      margin-right: 80px;
    }
  }
  .table-wrap {
    flex: 1;
    overflow: auto;
    border: 1px solid #d8dee1;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
  }
  .table {
    flex: 1;
    overflow: auto;
  }
  // .price {
  //   text-align: right;
  // }
  .total {
    background: rgba(23, 112, 223, 0.2);
    padding: 10px;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
  }
  .info-wrap {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 10px;
    li {
      line-height: 26px;
      span {
        margin-right: 20px;
      }
    }
  }
}
</style>
