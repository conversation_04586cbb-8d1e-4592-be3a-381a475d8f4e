<template>
  <div class="reSettleDaily">
    <div class="header">
      <div>日缴报表：</div>
      <div class="rightTitle">
        <span class="timeSpan"
          ><span class="timeTitle">时间段</span>
          <el-date-picker
            :picker-options="{ shortcuts: G_datePickerShortcuts }"
            v-model.trim="settlementDate"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :clearable="false"
            size="small"
          >
          </el-date-picker
        ></span>
        <BtnCommon
          :btnList="['预览', '保存', '记录查询']"
          @previews="previews"
          @saves="saves"
          @recordSearchs="recordSearchs"
        />
      </div>
    </div>
    <div class="bodyDiv"></div>
    <el-dialog
      title="日缴记录查询"
      :visible.sync="dialog"
      :before-close="handleClose"
      :wrapperClosable="false"
    >
      <div class="dialogHear">
        <div class="title">
          结算日期
          <el-date-picker
            :picker-options="{ shortcuts: G_datePickerShortcuts }"
            v-model.trim="operateDate"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :clearable="false"
            size="small"
          >
          </el-date-picker>
        </div>
        <div>
          <BtnCommon
            :btnList="['查询', '撤销']"
            @search="search"
            @revocations="revocations"
          />
        </div>
      </div>
      <div class="dialogTable">
        <PublicTable
          :viewTableList.sync="dialogTableData"
          :theads.sync="dialogTheads"
          :tableLoading.sync="loading"
          :isStripe="false"
          @rowClick="handleCurrentChange"
        >
          <!-- <template #price="{ scope }">
            <div class="redColor">
              {{ scope.row.price }}
            </div>
          </template> -->
        </PublicTable>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import BtnCommon from './components/btnCommon.vue';
import PublicTable from '@/components/publicTable.vue';
import { mapGetters } from 'vuex';
import { dataUtils } from '@/common';
export default {
  name: 'reSettleDaily',
  components: { BtnCommon, PublicTable },
  data() {
    return {
      saveInfo: {
        beginTime: '',
        endTime: '',
        operatorCode: ''
      },
      settlementDate: [dataUtils.getDate(), dataUtils.getDate()],
      reSettleDailyData: [], //获取日结报表
      operateDate: [dataUtils.getDate(), dataUtils.getDate()],
      searchInfo: {
        beginOperateDate: '',
        endOperateDate: '',
        operatorCode: ''
      },
      loading: false,
      dialog: false,
      dialogTheads: {
        beginTime: '开始时间',
        endTime: '结束时间',
        operateDate: '日结时间',
        // price: "总金额",
        operatorCode: '收费员'
      },
      dialogTableData: [],
      id: null
    };
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_userInfo', 'G_datePickerShortcuts'])
  },
  mounted() {},

  methods: {
    //关闭抽屉
    handleClose() {
      this.dialog = false;
    },
    //预览
    previews() {
      if (!this.settlementDate) {
        this.saveInfo.beginTime = dataUtils.getDate();
        this.saveInfo.endTime = dataUtils.getDate();
      } else {
        this.saveInfo.beginTime = this.settlementDate[0];
        this.saveInfo.endTime = this.settlementDate[1];
      }
      (this.saveInfo.operatorCode = this.G_userInfo.codeOper.operatorCode),
        this.$ajax
          .post(this.$apiUrls.GetReportSettlementDaily, this.saveInfo)
          .then((r) => {
            let { success, returnData } = r.data;
            if (!success) return;
            if (returnData.invoiceInfo.cancel.length >= 1) {
              this.reSettleDailyData = returnData.invoiceInfo.cancel;
            } else {
              this.reSettleDailyData = [];
              this.$message({
                message: '暂无数据!',
                type: 'success',
                showClose: true
              });
            }
          });
    },
    //保存
    saves() {
      if (!this.settlementDate) {
        this.saveInfo.beginTime = dataUtils.getDate();
        this.saveInfo.endTime = dataUtils.getDate();
      } else {
        this.saveInfo.beginTime = this.settlementDate[0];
        this.saveInfo.endTime = this.settlementDate[1];
      }
      (this.saveInfo.operatorCode = this.G_userInfo.codeOper.operatorCode),
        this.$ajax
          .post(this.$apiUrls.SaveSettlementDaily, this.saveInfo)
          .then((r) => {
            let { success } = r.data;
            if (!success) return;
            this.$message({
              message: '保存成功',
              type: 'success',
              showClose: true
            });
          });
    },
    //记录查询
    recordSearchs() {
      this.dialog = true;
    },
    //查询
    search() {
      if (!this.operateDate) {
        this.searchInfo.beginOperateDate = dataUtils.getDate();
        this.searchInfo.endOperateDate = dataUtils.getDate();
      } else {
        this.searchInfo.beginOperateDate = this.settlementDate[0];
        this.searchInfo.endOperateDate = this.settlementDate[1];
      }
      (this.searchInfo.operatorCode = this.G_userInfo.codeOper.operatorCode),
        this.$ajax
          .post(this.$apiUrls.GetSettlementDaily, '', {
            query: this.searchInfo
          })
          .then((r) => {
            console.log('r', r);
            let { success, returnData } = r.data;
            if (!success) return;

            if (returnData.length >= 1) {
              this.dialogTableData = returnData;
            } else {
              this.dialogTableData = [];
              this.$message({
                message: '暂无数据!',
                type: 'success',
                showClose: true
              });
            }
          });
    },
    handleCurrentChange(row) {
      this.id = row.id;
    },
    //撤销
    revocations() {
      if (!this.id) {
        this.$message({
          message: '请先选择要撤销的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$ajax
        .post(this.$apiUrls.DeleteSettlementDaily, '', {
          query: { id: this.id }
        })
        .then((r) => {
          let { success } = r.data;
          if (!success) return;
          this.$message({
            message: '撤销成功',
            type: 'success',
            showClose: true
          });
          this.search();
        });
    }
  }
};
</script>

<style lang="less" scoped>
.reSettleDaily {
  background: #ffffff;
  border-radius: 4px;
  border-radius: 4px;
  padding: 0 28px 18px 18px !important;
  display: flex;
  flex-direction: column;
  font-family: PingFangSC-Medium;
  font-size: 14px;
  color: #2d3436;
  .header {
    display: flex;
    justify-content: space-between;
    line-height: 68px;
    .btn_group {
      height: 68px;
    }
    .rightTitle {
      display: flex;
    }
    .timeSpan {
      margin-right: 20px;
    }
    .timeTitle {
      margin-right: 20px;
    }
  }
  .bodyDiv {
    flex: 1;
    background: rgba(178, 190, 195, 0.1);
    border-radius: 4px;
    border-radius: 4px;
  }

  /deep/.el-dialog {
    height: 60%;
    width: 770px;
    display: flex;
    flex-direction: column;
    .dialogHear {
      display: flex;
      justify-content: space-between;
      line-height: 32px;
      height: 54px;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #2d3436;
    }
    .dialogTable {
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
      border: 1px solid #d8dee1;
    }
  }
  /deep/.el-dialog__header {
    height: 50px;
    line-height: 50px;
    background: rgba(23, 112, 223, 0.2);
    padding: 0 18px;
  }
  /deep/.el-dialog__headerbtn {
    top: 10px;
    font-size: 30px;
  }
  /deep/.el-dialog__body {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 10px 18px 18px 18px;
    flex-shrink: 0;
    overflow: auto;
  }
}
</style>
