<!--
 * @FilePath: \KrPeis\src\pages\financialManagement\personalSettlementCombReport.vue
 * @Description:  个人收费日结组合统计报表
 * @Author: justin
 * @Date: 2024-04-03 11:47:17
 * @Version: 0.0.1
 * @LastEditors: justin
 * @LastEditTime: 2024-04-10 14:39:46
*
-->

<template>
  <div class="container">
    <div class="header-wrapper">
      <el-form
        :model="searchInfo"
        status-icon
        :rules="rules"
        :ref="refSearchForm"
        :inline="true"
        label-width="78px"
      >
        <el-row type="flex" align="middle">
          <el-col :span="24">
            <el-form-item label="统计时间" prop="dateRange">
              <el-date-picker
                :picker-options="{ shortcuts: G_datePickerShortcuts }"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                size="small"
                v-model="searchInfo.dateRange"
                class="input"
                style="width: 220px"
                @change="statistics"
              >
              </el-date-picker>
            </el-form-item>

            <el-form-item label="套餐" prop="clusterCode">
              <el-select
                placeholder="请选择"
                size="small"
                filterable
                clearable
                v-model="searchInfo.clusterCode"
                class="input"
                style="width: 180px"
                @change="statistics"
              >
                <el-option
                  v-for="item in clusterList"
                  :key="item.clusCode"
                  :label="item.clusName"
                  :value="item.clusCode"
                >
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="项目分类" prop="clsCode">
              <el-select
                placeholder="请选择"
                size="small"
                filterable
                clearable
                v-model="searchInfo.clsCode"
                class="input"
                style="width: 180px"
                @change="statistics"
              >
                <el-option
                  v-for="item in clsCodeList"
                  :key="item.clsCode"
                  :label="item.clsName"
                  :value="item.clsCode"
                >
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item
              label="体检分类"
              prop="peCls"
              v-if="G_config.physicalMode.includes('普检')"
            >
              <el-select
                placeholder="请选择"
                size="small"
                filterable
                clearable
                v-model="searchInfo.peCls"
                class="input"
                style="width: 180px"
                @change="statistics"
              >
                <el-option
                  v-for="item in G_peClsList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="预约类型" prop="bookType">
              <el-select
                placeholder="请选择"
                size="small"
                filterable
                clearable
                v-model="searchInfo.bookType"
                class="input"
                style="width: 180px"
                @change="statistics"
              >
                <el-option
                  :label="item.label"
                  :value="item.value"
                  v-for="item in G_bookType"
                  :key="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row type="flex" align="middle">
          <el-col :span="4">
            <h3>缴费组合列表：</h3>
          </el-col>

          <el-col :span="16">
            <div class="summary-box">
              <div class="summary-item">
                <span>总合计人数：</span>
                <span>{{ responseData.totalCount | toFixed(0) }}</span>
              </div>

              <div class="summary-item">
                <span>总合计金额：</span>
                <span>{{ responseData.totalAmount | toFixed(2) }}</span>
              </div>
            </div>
          </el-col>

          <el-col :span="4" :offset="6">
            <btnCommon
              :btnList="['统计', '导出']"
              @statistics="statistics"
              @exports="exports"
              :loadingStatistics="loadingStatistics"
            />
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="body-wrapper" v-loading="loadingStatistics">
      <template
        v-if="
          responseData.clsSummaryList && responseData.clsSummaryList.length > 0
        "
      >
        <div
          v-for="clsItem in responseData.clsSummaryList"
          :key="clsItem.clsCode"
          class="cls-box"
        >
          <el-row type="flex">
            <el-col :span="24" class="cls-header">
              <div class="cls-title">
                <span>项目分类：</span>
                <span>{{ clsItem.clsName }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row type="flex">
            <el-col :span="24" class="cls-body">
              <el-table
                ref="tableCom_Ref"
                style="
                  width: 100%;
                  color: #2d3436;
                  font-weight: 600;
                  font-size: 14px;
                "
                :header-cell-style="{
                  background: '#d1e2f9',
                  fontSize: '14px',
                  color: '#2d3436'
                }"
                border
                height="300"
                size="small"
                :data="clsItem.combItemList"
                highlight-current-row
                show-summary
                :summary-method="getSummaries"
              >
                <el-table-column
                  v-for="thItem of theadList"
                  :key="thItem.prop"
                  :prop="thItem.prop"
                  :label="thItem.label"
                  :min-width="thItem.width"
                >
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </div>
      </template>

      <template v-else>
        <el-empty :image-size="200"></el-empty>
      </template>
    </div>
  </div>
</template>

<script>
import btnCommon from './components/btnCommon.vue';
import { dataUtils } from '@/common/dataUtils';
import { mapGetters } from 'vuex';

export default {
  name: 'personalSettlementCombReport',
  components: {
    btnCommon
  },
  data() {
    return {
      searchInfo: {
        dateRange: [
          dataUtils.dateToString(new Date()),
          dataUtils.dateToString(new Date())
        ], // 统计时间
        clusterCode: '', // 套餐
        clsCode: '', // 项目分类
        peCls: null, // 体检分类
        bookType: null // 预约类型
      },
      rules: {
        dateRange: [
          {
            type: 'array',
            required: true,
            message: '请选择统计时间'
          }
        ]
      },
      clusterList: [],
      clsCodeList: [],
      theadList: [
        {
          prop: 'combName',
          label: '项目名称',
          width: 120
        },
        {
          prop: 'number',
          label: '人次',
          width: 60
        },
        {
          prop: 'price',
          label: '单价',
          width: 60
        },
        {
          prop: 'totalPrice',
          label: '金额',
          width: 60
        }
      ],
      refSearchForm: 'searchForm',
      checkTableList: [],
      loadingStatistics: false,
      responseData: {}
    };
  },
  computed: {
    ...mapGetters([
      'G_peClsList',
      'G_bookType',
      'G_datePickerShortcuts',
      'G_config'
    ])
  },
  created() {
    this.getClusterList();
    this.getClsCodeList();
  },
  beforeUpdate() {},
  methods: {
    /**
     * @description: 获取套餐下拉内容
     * @return {*}
     */
    getClusterList() {
      this.$ajax.post(this.$apiUrls.Cluster).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.clusterList = returnData;
      });
    },

    /**
     * @author: justin
     * @description: 项目分类下拉内容
     * @return {*}
     */
    getClsCodeList() {
      this.$ajax.post(this.$apiUrls.RD_CodeItemCls + '/Read', []).then((r) => {
        this.clsCodeList = r.data.returnData;
      });
    },

    /**
     * @author: justin
     * @description: 个人收费日结组合统计报表
     * @return {*}
     */
    statistics() {
      this.$refs[this.refSearchForm].validate((valid) => {
        if (!valid) return;

        this.loadingStatistics = true;
        let [startTime, endTime] = this.searchInfo.dateRange;
        let data = {
          chargeTimeStart: dataUtils.hourMinSeStart(startTime),
          chargeTimeEnd: dataUtils.hourMinSeEnd(endTime),
          clusterCode: this.searchInfo.clusterCode,
          clsCode: this.searchInfo.clsCode,
          peCls: parseInt(this.searchInfo.peCls),
          bookType: parseInt(this.searchInfo.bookType)
        };
        data.peCls = isNaN(data.peCls) ? null : data.peCls;
        data.bookType = isNaN(data.peCls) ? null : data.bookType;

        this.$ajax
          .post(this.$apiUrls.GetPersonalSettlementCombReport, data)
          .then((r) => {
            let { success, returnData } = r.data;
            if (!success || !returnData) return;

            this.responseData = returnData;
          })
          .finally(() => {
            this.loadingStatistics = false;
          });
      });
    },

    /**
     * @author: justin
     * @description: 表格汇总行调用
     * @param {*} param
     * @return {*}
     */
    getSummaries(param) {
      let { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (column.property === 'combName') {
          sums[index] = '合计';
          return;
        } else if (column.property === 'price') {
          sums[index] = 'N/A';
          return;
        }

        const values = data.map((item) => Number(item[column.property]));
        if (!values.every((value) => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return dataUtils.add(prev, curr);
            } else {
              return prev;
            }
          }, 0);
        } else {
          sums[index] = 'N/A';
        }
      });

      return sums;
    },

    /**
     * @description: 导出
     * @return {*}
     */
    exports() {
      const that = this;
      if (
        !that.responseData.clsSummaryList ||
        that.responseData.clsSummaryList.length == 0
      ) {
        that.$message.warning('暂无记录可导出。');
        return;
      }

      that
        .$confirm('确定下载列表文件?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        .then(() => {
          that.$notify({
            title: '成功',
            message: '正在导出中，请稍后...',
            type: 'success'
          });
          that.$nextTick(function () {
            that.export2Excel();
          });
        })
        .catch(() => {});
    },

    /**
     * @description: 数据写入excel
     * @return {*}
     */
    export2Excel() {
      const that = this;
      require.ensure([], () => {
        const { export_multi_table_to_excel } = require('@/utils/Export2Excel');
        const name = '单位人员组合费用明细报表';
        const tableNames = that.responseData.clsSummaryList.map(
          (x) => `项目分类：${x.clsName}`
        );
        export_multi_table_to_excel(
          that.$refs['tableCom_Ref'],
          tableNames,
          name
        );
      });
    }
  },
  filters: {
    toFixed: (value, num = 2) => {
      return (Number(value) || 0).toFixed(num);
    }
  }
};
</script>

<style lang="less" scoped>
.container {
  color: #2d3436;
  background: #fff;
  display: flex;
  flex-flow: column;
  padding: 0 10px !important;

  .header-wrapper {
    position: sticky;
    z-index: 2;
    top: 0;
    background: #fff;
    width: 100%;
    padding-bottom: 15px;

    /deep/ .el-form-item {
      margin-bottom: 10px !important;

      .el-form-item__label {
        font-size: 14px;
        font-weight: 600;
      }
    }

    .summary-box {
      display: flex;
      align-items: baseline;
      h3 {
        font-size: 18px;
        padding: 5px 0 10px 0;
      }

      .summary-item {
        margin-left: 100px;
        span:nth-child(1) {
          font-size: 14px;
          font-weight: 600;
        }
      }
    }
  }

  .body-wrapper {
    flex: 1;
    width: 100%;

    .cls-box {
      width: 100%;
      overflow: auto;
      margin-bottom: 20px;

      .cls-header {
        padding: 5px;
      }

      .cls-body {
        //设置表的外边框
        /deep/.el-table {
          border: 1px solid #d8dee1;
          border-radius: 5px;
        }
      }
    }
  }
}
</style>
