<template>
  <!-- 团体明细费用报表 -->
  <div class="groupDetailedExpenseReport">
    <div class="groupDetailedExpenseReport-wrap">
      <div class="search-operate">
        <div class="operate-item">
          <span style="width: 70px">登记时间</span>
          <el-date-picker
            :picker-options="{ shortcuts: G_datePickerShortcuts }"
            v-model.trim="searchInfo.date"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :clearable="false"
            size="small"
            class="select"
            style="width: 250px"
          >
          </el-date-picker>
        </div>
        <div class="operate-item">
          <span>体检单位</span>
          <el-cascader
            ref="cascader_ref"
            v-model="casValue"
            :filter-method="filterMethod"
            :options="options"
            :props="{ multiple: true }"
            clearable
            filterable
            size="small"
            collapse-tags
            class="select"
            @change="companyChange"
            style="width: 350px"
          >
          </el-cascader>
        </div>
        <div class="operate-item">
          <el-checkbox v-model="containsNoCheckItem" style="margin-right: 5px"
            >包含拒检</el-checkbox
          >
          <BtnCommon :btnList="['查询']" @search="searchClick">
            <template slot="footAdd">
              <el-button
                size="small"
                class="yellow_btn btn"
                @click="exports"
                icon="iconfont icon-daochu"
                >导出</el-button
              >
            </template>
          </BtnCommon>
        </div>
      </div>
      <div class="legend">
        <span>实检人数：{{ infoList.checkCount }}人</span>
      </div>
      <div class="table-wrap" v-loading="loading">
        <div class="table-item" v-for="(item, index) in tableData" :key="index">
          <div class="table-legend">
            <div class="legend-item">体检号：{{ item.regNo }}</div>
            <div class="legend-item">姓名：{{ item.name }}</div>
          </div>
          <div class="table">
            <PublicTable
              :viewTableList.sync="item.personCombs"
              :theads.sync="theads"
              :isSortShow="false"
              :columnWidth="columnWidth"
              :show-summary="true"
              :summary-method="getSummaries"
            >
            </PublicTable>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import BtnCommon from './components/btnCommon.vue';
import PublicTable from '@/components/publicTable.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
import { export2Excel } from '@/common/excelUtil';
import Exceljs from '@/common/excel/groupDetailedExpenseReportExcel';

export default {
  name: 'groupDetailedExpenseReport',
  mixins: [Exceljs],
  components: { BtnCommon, PublicTable },
  data() {
    return {
      containsNoCheckItem: true,
      searchInfo: {
        date: [dataUtils.getDate(), dataUtils.getDate()],
        companyCode: '',
        companyTimes: [],
        deptCode: '',
        checked: false
      },
      options: [],
      casValue: [],
      shareScopeEnd: [],
      deptCodeList: [],
      tableData: [],
      theads: {
        combCode: '组合代码',
        combName: '组合名称',
        count: '数量',
        price: '原始价格',
        actualPrice: '套餐价格',
        discount: '折扣'
      },
      columnWidth: {
        combName: 300
      },
      infoList: {
        checkCount: 0
      },
      loading: false
    };
  },
  created() {
    this.getCompanyAndTimes();
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_userInfo', 'G_datePickerShortcuts'])
  },
  methods: {
    // 获取部门
    getDeptCode() {
      if (!this.searchInfo.companyCode) return;
      let data = {
        companyCode: this.searchInfo.companyCode
      };
      this.$ajax.post(this.$apiUrls.R_CodeCompanyDepartment, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.deptCodeList = returnData || [];
      });
    },
    // 获取医院信息
    getHospitalInfo() {
      this.$ajax
        .post(this.$apiUrls.RD_CodeHospitalInfo + '/Read', '', {
          query: { hospCode: '01' }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.infoList = {
            ...this.infoList,
            ...returnData
          };
        });
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    // 获取单位和体检次数
    getCompanyAndTimes() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.options = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    // 查询
    searchClick() {
      if (!this.searchInfo.companyCode) {
        this.$message({
          showClose: true,
          message: '请选择单位!',
          type: 'warning'
        });
        return;
      }
      let [beinDate, endDate] = this.searchInfo.date;
      let data = {
        beinDate: dataUtils.dateToStrStart(beinDate),
        endDate: dataUtils.dateToStrEnd(endDate),
        companyCode: this.searchInfo.companyCode,
        companyTimes: this.searchInfo.companyTimes,
        deptCode: this.searchInfo.deptCode,
        checked: this.searchInfo.checked,
        containsNoCheckItem: this.containsNoCheckItem
      };
      this.loading = true;
      this.$ajax
        .post(this.$apiUrls.CompanySettlementByFeeDetailReport, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) {
            this.tableData = [];
            this.infoList = {
              checkCount: 0
            };
          } else {
            this.tableData = returnData.personGroups || [];
            this.infoList = {
              checkCount: returnData.checkCount
            };
          }
        })
        .finally((_) => {
          this.loading = false;
        });
    },
    /**
     * @author: justin
     * @description: 单位选择器内容改变回调事件
     * @param {*} val
     * @return {*}
     */
    companyChange(val) {
      if (val.length === 0) {
        this.searchInfo.companyCode = '';
        this.setDateRangeByCompanyTimes();
        return;
      }
      let changeFlag = false;
      let changeItem = [];
      if (this.shareScopeEnd.length == 0) {
        this.casValue = val;
      } else {
        // 与原数组比对
        this.casValue.forEach((item) => {
          if (item[0] !== this.shareScopeEnd[0][0]) {
            // 一级标签不同
            changeFlag = true;
            changeItem.push(item);
          }
        });
      }
      if (changeFlag) {
        this.casValue = [];
        this.casValue = changeItem;
      }
      this.shareScopeEnd = this.casValue;
      if (val.length !== 0) {
        this.searchInfo.companyCode = this.casValue[0][0];
        let companyTimes = [];
        this.casValue.map((item) => {
          if (item) {
            companyTimes.push(item[1]);
          }
        });
        this.searchInfo.companyTimes = companyTimes;
        this.setDateRangeByCompanyTimes();
        this.searchClick();
      }
    },

    /**
     * @author: justin
     * @description: 根据单位体检次数所在时间，设置日期范围
     * @return {*}
     */
    setDateRangeByCompanyTimes() {
      if (
        !this.searchInfo.companyCode ||
        !this.searchInfo.companyTimes ||
        this.searchInfo.companyTimes.length == 0
      ) {
        this.searchInfo.date = [dataUtils.getDate(), dataUtils.getDate()];
        return;
      }

      const company = this.options.find(
        (x) => x.value == this.searchInfo.companyCode
      );
      if (!company) return;

      let dateArr = [];
      company.children
        .filter((x) => this.searchInfo.companyTimes.includes(x.value))
        .forEach((x) => {
          dateArr.push(new Date(x.item.beginDate || Date())?.getTime());
          dateArr.push(new Date(x.item.endDate || Date())?.getTime());
        });
      if (dateArr.length == 0) return;

      const minDate = new Date(Math.min(...dateArr));
      const maxDate = new Date(Math.max(...dateArr));
      this.searchInfo.date = [minDate, maxDate];
    },

    // 导出
    exports() {
      if (this.tableData.length == 0) {
        this.$message.warning('报表不能为空!');
        return;
      }
      this.exportExcel(
        this.theads,
        this.tableData,
        `实检人数：${this.tableData.length} 人`
      );
      return;
      if (this.tableData.length == 0) {
        this.$message.warning('报表不能为空!');
        return;
      }
      this.$confirm('确定导出团体明细费用报表吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let columns = [];
        Object.keys(this.theads).map((item) => {
          columns.push({
            title: this.theads[item],
            key: item
          });
        });
        let data = [];
        this.tableData.map((item) => {
          let pushJson = {
            combCode: `体检号：${item.regNo}`,
            combName: `姓名：${item.name}`,
            count: '',
            price: '',
            actualPrice: '',
            discount: ''
          };
          let footRow = {
            combCode: ``,
            combName: ``,
            count: '',
            price: '',
            actualPrice: '',
            discount: ''
          };
          data.push(pushJson, ...item.personCombs, footRow);
        });

        const title = '团体明细费用报表';
        this.$nextTick(() => {
          export2Excel(columns, data, title);
        });
      });
    },

    /**
     * @author: justin
     * @description: 自定义汇总行数据
     * @param {*} columns
     * @param {*} data
     * @return {*}
     */
    getSummaries({ columns, data }) {
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          return;
        }
        const values = data.map((item) => Number(item[column.property]));
        if (column.property === 'actualPrice') {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return dataUtils.add(prev, curr);
            } else {
              return prev;
            }
          }, 0);
          sums[index];
        } else {
          sums[index] = '/';
        }
      });
      return sums;
    }
  }
};
</script>

<style lang="less" scoped>
.groupDetailedExpenseReport {
  color: #2d3436;
  display: flex;
  flex-direction: column;
  .select {
    width: 100%;
  }
  .groupDetailedExpenseReport-wrap {
    background: #fff;
    flex: 1;
    overflow: auto;
    padding: 10px;
    display: flex;
    flex-direction: column;
  }
  .search-operate {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
  .operate-item {
    margin-right: 10px;
    display: flex;
    align-items: center;
    span {
      margin-right: 10px;
      font-weight: 600;
      font-size: 14px;
      width: 78px;
      &:last-child {
        margin-right: 0;
      }
    }
  }
  .legend {
    font-size: 14px;
    font-weight: 600;
    color: #1770df;
    margin-bottom: 10px;
    span {
      margin-right: 80px;
    }
  }
  .table-wrap {
    flex: 1;
    overflow: auto;
  }
  .table-item {
    border: 1px solid #d8dee1;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    margin-bottom: 40px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .table-legend {
    display: flex;
    align-items: center;
  }
  .legend-item {
    font-size: 14px;
    font-weight: 600;
    padding: 10px 0;
    margin-right: 40px;
  }
  .table {
    flex: 1;
    overflow: auto;
    /deep/.el-table--scrollable-y .el-table__body-wrapper {
      height: 600px !important;
    }
    /deep/.el-table__body-wrapper {
      height: 600px !important;
    }
  }
  .total {
    background: rgba(23, 112, 223, 0.2);
    padding: 10px;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
  }
  .info-wrap {
    font-size: 14px;
    font-weight: 600;
    li {
      line-height: 26px;
      span {
        margin-right: 20px;
      }
    }
  }
}
</style>
