<!-- 职业病报告上传 -->
<template>
  <div class="uploadOccupationReport_page">
    <div class="header-wrapper">
      <el-form
        :model="searchInfo"
        status-icon
        ref="refSearchForm"
        :inline="true"
      >
        <el-row type="flex" align="middle">
          <el-col :span="24">
            <el-form-item prop="dateRange">
              <el-input
                size="small"
                style="width: 200px; margin-right: 5px"
                v-model="searchInfo.keyword"
                placeholder="请输入姓名/体检号搜索"
              ></el-input>
            </el-form-item>
            <el-form-item prop="dateRange">
              <el-date-picker
                :picker-options="{ shortcuts: G_datePickerShortcuts }"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                size="small"
                v-model="searchInfo.dateRange"
                class="input"
                style="width: 250px"
                @change="getTableData"
              >
              </el-date-picker>
            </el-form-item>

            <el-form-item label="单位" prop="companyCode">
              <el-cascader
                ref="cascader_ref"
                v-model="companyCasList"
                :filter-method="filterMethod"
                :options="companyList"
                :props="{ multiple: false }"
                clearable
                filterable
                size="small"
                collapse-tags
                @change="companyChange"
                style="width: 400px"
              >
              </el-cascader>
            </el-form-item>
            <el-form-item label="上传状态">
              <el-select
                class="select"
                style="width: 100px"
                @change="getTableData"
                v-model.trim="searchInfo.isUploaded"
                placeholder="请选择"
                size="small"
                filterable
              >
                <el-option
                  v-for="(item, index) in uploadStateList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              ><el-button
                size="mini"
                icon="iconfont icon-search"
                @click="getTableData"
                >查询</el-button
              ></el-form-item
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="main">
      <div class="main-left">
        <div
          style="display: flex; justify-content: flex-end; padding-bottom: 5px"
        >
          <el-select
            size="small"
            style="margin-right: 5px"
            v-model="uploadTypeVal"
            placeholder="请选择"
          >
            <el-option
              v-for="item in uploadTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-button size="mini" @click="uploadBtn" v-loading="uploadLoading"
            >上传</el-button
          >
        </div>
        <div class="left-table">
          <PublicTable
            ref="view_Ref"
            isCheck
            :isSortShow="false"
            :viewTableList.sync="tableData"
            :theads.sync="theads"
            :columnWidth="columnWidth"
            @selectionChange="selectionChange"
            @currentChange="currentChange"
            v-model="selection"
            :isStripe="false"
            :tableLoading="tableLoading"
            showOverflowTooltip
            border
          >
            <template #sex="{ scope }">
              {{ G_EnumList['Sex'][scope.row.sex] }}
            </template>
            <template #jobStatus="{ scope }">
              {{
                G_EnumList['CodeOccupationalPositionStatus'][
                  scope.row.jobStatus
                ]
              }}
            </template>
            <template #isUploaded="{ scope }">
              {{ scope.row.isUploaded ? '已上传' : '未上传' }}
            </template>
            <template #log="{ scope }">
              <div class="log_column" :title="scope.row.log">
                {{ scope.row.log }}
              </div>
            </template>
          </PublicTable>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { dataUtils } from '@/common';
import { renderAsync } from '@/assets/docx-preview/dist/docx-preview.min.js';
import { mapGetters } from 'vuex';
import PublicTable from '@/components/publicTable';

export default {
  name: 'uploadOccupationReport',
  components: {
    PublicTable
  },
  computed: {
    ...mapGetters(['G_printerList', 'G_EnumList', 'G_datePickerShortcuts'])
  },
  data() {
    return {
      tableLoading: false,
      uploadLoading: false,
      searchInfo: {
        dateRange: [dataUtils.getDate(), dataUtils.getDate()], // 体检时间
        companyCode: '', // 单位代码
        companyTimes: 0, // 单位次数
        keyword: '',
        isUploaded: null
      },
      companyList: [],
      companyDeptList: [],
      companyCasList: [],
      tableData: [],
      theads: {
        regNo: '体检号',
        companyName: '体检单位',
        name: '姓名',
        age: '年龄',
        sex: '性别',
        zkCode: '质控编号',
        hazards: '危害因素',
        activiteTime: '激活时间',

        jobStatus: '在岗状态',
        conclusion: '总检结论',
        log: '上传错误日志',
        isUploaded: '上传状态'
      },
      columnWidth: {
        regNo: 120,
        companyName: 180,
        name: 100,
        age: 50,
        sex: 50,
        zkCode: 150,
        activiteTime: 155,
        jobStatus: 80,
        conclusion: 80,
        isUploaded: 80
      },
      selection: [],
      blobObj: null,
      reportPrintVal: '',
      currentRow: null, //当前行
      uploadTypeList: [
        {
          label: '新增',
          value: 1
        },
        {
          label: '修改',
          value: 2
        },
        {
          label: '删除',
          value: 3
        }
      ],
      uploadTypeVal: 1,
      uploadStateList: [
        {
          label: '全部',
          value: null
        },
        {
          label: '未上传',
          value: false
        },
        {
          label: '已上传',
          value: true
        }
      ]
    };
  },
  methods: {
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    companyChange() {},
    /**
     * @description: 获取单位及次数
     * @return {*}
     */
    getCompanyWithTimesList() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    /**
     * @description: 单位选择器内容改变回调事件
     * @param {*} val
     * @return {*}
     */
    companyChange(val) {
      const that = this;
      // this.getDepartList();
      // this.searchInfo.companyDeptCode = "";
      if (!val || val.length === 0) {
        that.searchInfo.companyCode = '';
        that.searchInfo.companyTimes = 0;
        this.companyDeptList = [];
        that.setDateRangeByCompanyTimes();
        return;
      }

      that.searchInfo.companyCode = that.companyCasList[0];
      that.searchInfo.companyTimes = that.companyCasList[1];
      that.setDateRangeByCompanyTimes();
      this.getTableData();
    },
    /**
     * @author: justin
     * @description: 根据单位体检次数所在时间，设置日期范围
     * @return {*}
     */
    setDateRangeByCompanyTimes() {
      console.log(dataUtils.getDate());
      if (
        !this.searchInfo.companyCode ||
        !this.searchInfo.companyTimes ||
        this.searchInfo.companyTimes.length == 0
      ) {
        this.searchInfo.dateRange = [dataUtils.getDate(), dataUtils.getDate()];
        return;
      }

      const company = this.companyList.find(
        (x) => x.value == this.searchInfo.companyCode
      );
      if (!company) return;

      let dateArr = [];
      console.log(company);
      company.children
        .filter((x) => this.searchInfo.companyTimes == x.value)
        .forEach((x) => {
          dateArr.push(x.item.beginDate.split(' ')[0]);
          dateArr.push(x.item.endDate.split(' ')[0]);
        });
      if (dateArr.length == 0) return;

      // const minDate = new Date(Math.min(...dateArr));
      // const maxDate = new Date(Math.max(...dateArr));
      this.searchInfo.dateRange = [dateArr[0], dateArr[1]];
    },
    //查询公司部门信息
    getDepartList() {
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanyDepartment, {
          companyCode: this.companyCasList[0],
          deptCode: '',
          deptName: ''
        })
        .then((r) => {
          //console.log("r", r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.companyDeptList = returnData || [];
        });
    },
    // 获取表格数据
    getTableData() {
      this.tableLoading = true;
      this.getTableDataFun()
        .then((r) => {
          this.tableData = r || [];
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    // 获取表格数据的通用函数
    getTableDataFun() {
      return new Promise((resolve, reject) => {
        this.searchInfo.beginTime = this.searchInfo.dateRange[0];
        this.searchInfo.endTime = this.searchInfo.dateRange[1];
        console.log(this.searchInfo);
        this.$ajax
          .post(this.$apiUrls.ReadOccupationUploadPersons, this.searchInfo)
          .then((r) => {
            let { success, returnData } = r.data;
            if (!success) return;
            resolve(returnData);
          });
      });
    },
    // 选择人员列表改变的回调
    selectionChange(row, checkList) {
      this.selection = checkList;
    },
    // 当前行改变的回调
    currentChange(row) {
      this.currentRow = row;
    },
    // 上传
    async uploadBtn() {
      if (this.selection.length === 0) {
        this.$message({
          message: `请先选择需要上传的人员！`,
          type: 'warning'
        });
        return;
      }
      try {
        this.uploadLoading = true;

        for (const item of this.selection) {
          try {
            await this.uploadFun(item.regNo);
          } catch (e) {
            const { returnData } = e.data;
            this.$message.error(
              `体检号 ${returnData.regNo} ${returnData.errorString}`
            );
          }
        }

        this.$message.success('上传完成！');
      } finally {
        this.uploadLoading = false;
      }
      this.$nextTick(() => {
        this.getTableDataFun().then((r) => {
          r?.forEach((item, idx) => {
            this.tableData[idx].log = item.log;
            this.tableData[idx].isUploaded = item.isUploaded;
          });
        });
      });
    },
    // 上传的封装
    uploadFun(regNo) {
      return new Promise((resolve, reject) => {
        let datas = {
          regNo,
          uploadType: this.uploadTypeVal
        };
        this.$ajax
          .paramsPost(this.$apiUrls.UploadPersonReport, datas, { timeout: 0 })
          .then((r) => {
            let { success, returnData } = r.data;
            if (!success) {
              reject(r);
              return;
            }
            resolve(r);
          });
      });
    }
  },
  created() {
    this.getCompanyWithTimesList();
  },
  mounted() {
    console.log(renderAsync);
  }
};
</script>

<style lang="less" scoped>
.uploadOccupationReport_page {
  display: flex;
  flex-direction: column;
  .header-wrapper {
    background: #fff;
    padding: 0 5px 5px;
    .el-form-item {
      margin-bottom: 0 !important;
    }
  }
  .title_wrap {
    display: flex;
    justify-content: space-between;
  }
  .main {
    flex: 1;
    display: flex;
    overflow: auto;
    padding-top: 5px;
  }
  .main-left,
  .main-right {
    background: #fff;
    border-radius: 4px;
    padding: 5px;
    overflow: auto;
  }
  .main-left {
    flex: 1;
    margin-right: 5px;
    display: flex;
    flex-direction: column;
  }
  .main-right {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .left-table {
    height: 100%;
    border: 1px solid rgba(178, 190, 195, 0.5);
    border-radius: 4px;
    flex: 1;
    flex-shrink: 0;
  }
  .right-btn {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    justify-content: space-between;
    // padding: 0 20px;
  }
  .btn-item {
    display: flex;
    align-items: center;
    margin-right: 10px;
    &:nth-child(1) {
      flex: 1;
    }
    &:nth-child(2) {
      flex: 2;
    }
    &:nth-child(3) {
      flex: 2;
    }
    &:nth-child(4) {
      flex: 3;
    }
    label {
      width: 82px;
      text-align: right;
      margin-right: 10px;
      font-size: 14px;
      font-weight: 600;
    }
  }
  .right-content {
    flex: 1;
    flex-shrink: 0;
    background: rgba(23, 112, 223, 0.1);
    border-radius: 4px;
    display: flex;
    overflow: auto;
    justify-content: center;
    .docx_dom {
      width: 100%;
    }
  }
  .log_column {
    white-space: pre-wrap;
  }
}
</style>
