<!-- 疑似职业病告知书 -->
<template>
  <div class="dubiousNotification_page">
    <div class="header-wrapper">
      <el-form
        :model="searchInfo"
        status-icon
        ref="refSearchForm"
        :inline="true"
      >
        <el-row type="flex" align="middle">
          <el-col :span="24">
            <el-form-item prop="dateRange">
              <el-date-picker
                :picker-options="{ shortcuts: G_datePickerShortcuts }"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                size="small"
                v-model="searchInfo.dateRange"
                class="input"
                style="width: 250px"
                @change="getTableData"
              >
              </el-date-picker>
            </el-form-item>

            <el-form-item label="单位" prop="companyCode">
              <el-cascader
                ref="cascader_ref"
                v-model="companyCasList"
                :filter-method="filterMethod"
                :options="companyList"
                :props="{ multiple: false }"
                clearable
                filterable
                size="small"
                collapse-tags
                @change="companyChange"
                style="width: 400px"
              >
              </el-cascader>
            </el-form-item>
            <el-form-item label="部门" prop="companyDeptCode">
              <el-select
                class="select"
                v-model.trim="searchInfo.companyDeptCode"
                placeholder="请选择"
                size="small"
                filterable
                clearable
              >
                <el-option
                  v-for="(item, index) in companyDeptList"
                  :key="index"
                  :label="item.deptName"
                  :value="item.deptCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="main">
      <div class="main-left">
        <div class="left-table">
          <PublicTable
            ref="view_Ref"
            isCheck
            :isSortShow="false"
            :viewTableList.sync="tableData"
            :theads.sync="theads"
            :columnWidth="{
              regNo: 124
            }"
            @selectionChange="selectionChange"
            @currentChange="currentChange"
            @rowClick="rowClick"
            v-model="selection"
            :isStripe="false"
          >
          </PublicTable>
        </div>
      </div>
      <div class="main-right">
        <div class="right-btn">
          <div>
            <el-button
              size="small"
              v-loading="isGenerating"
              @click="notificationBtn"
              >生成告知书</el-button
            >
          </div>
          <div style="margin-left: 10px">
            <!-- <el-select
              size="small"
              v-model="reportPrintVal"
              placeholder="请选择打印机"
            >
              <el-option
                v-for="item in G_printerList"
                :key="item"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
            <el-button size="small" style="margin-left: 10px">打印</el-button> -->
            <el-button
              size="small"
              :disabled="isGenerating"
              @click="downLoadWord(false)"
              >下载pdf</el-button
            >
            <el-button
              size="small"
              :disabled="isGenerating"
              @click="downLoadWord(true)"
              >下载docx</el-button
            >
          </div>
        </div>
        <div class="right-content">
          <!-- <div class="docx_dom" ref="docx_Ref" v-show="blobObj"></div> -->
          <embed
            type="application/pdf"
            width="100%"
            :src="blobObj + '#toolbar=0'"
            height="100%"
            v-show="blobObj"
          />
          <el-empty :image-size="200" v-if="!blobObj"></el-empty>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { dataUtils } from '@/common';
import { renderAsync } from '@/assets/docx-preview/dist/docx-preview.min.js';
import { mapGetters } from 'vuex';
import PublicTable from '@/components/publicTable';
import { saveAs } from 'file-saver';

export default {
  name: 'dubiousNotification',
  components: {
    PublicTable
  },
  computed: {
    ...mapGetters(['G_printerList', 'G_datePickerShortcuts'])
  },
  data() {
    return {
      searchInfo: {
        dateRange: [dataUtils.getDate(), dataUtils.getDate()], // 体检时间
        companyCode: '', // 单位代码
        companyTimes: 0, // 单位次数
        type: 1,
        companyDeptCode: '',
        regNo: ''
      },
      companyList: [],
      companyDeptList: [],
      companyCasList: [],
      tableData: [],
      theads: {
        regNo: '体检号',
        name: '姓名'
      },
      selection: [],
      blobObj: null,
      reportPrintVal: '',
      currentRow: null, //当前行
      isGenerating: false,
      regNoArr: []
    };
  },
  methods: {
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    companyChange() {},
    /**
     * @description: 获取单位及次数
     * @return {*}
     */
    getCompanyWithTimesList() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    /**
     * @description: 单位选择器内容改变回调事件
     * @param {*} val
     * @return {*}
     */
    companyChange(val) {
      const that = this;
      this.getDepartList();
      this.searchInfo.companyDeptCode = '';
      if (!val || val.length === 0) {
        that.searchInfo.companyCode = '';
        that.searchInfo.companyTimes = null;
        this.companyDeptList = [];
        that.setDateRangeByCompanyTimes();
        return;
      }

      that.searchInfo.companyCode = that.companyCasList[0];
      that.searchInfo.companyTimes = that.companyCasList[1];
      that.setDateRangeByCompanyTimes();
      this.getTableData();
    },
    /**
     * @author: justin
     * @description: 根据单位体检次数所在时间，设置日期范围
     * @return {*}
     */
    setDateRangeByCompanyTimes() {
      if (
        !this.searchInfo.companyCode ||
        !this.searchInfo.companyTimes ||
        this.searchInfo.companyTimes.length == 0
      ) {
        this.searchInfo.dateRange = [dataUtils.getDate(), dataUtils.getDate()];
        return;
      }

      const company = this.companyList.find(
        (x) => x.value == this.searchInfo.companyCode
      );
      if (!company) return;

      let dateArr = [];
      company.children
        .filter((x) => this.searchInfo.companyTimes == x.value)
        .forEach((x) => {
          dateArr.push(new Date(x.item.beginDate || Date())?.getTime());
          dateArr.push(new Date(x.item.endDate || Date())?.getTime());
        });
      if (dateArr.length == 0) return;

      const minDate = new Date(Math.min(...dateArr));
      const maxDate = new Date(Math.max(...dateArr));
      this.searchInfo.dateRange = [minDate, maxDate];
    },
    //查询公司部门信息
    getDepartList() {
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanyDepartment, {
          companyCode: this.companyCasList[0],
          deptCode: '',
          deptName: ''
        })
        .then((r) => {
          //console.log("r", r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.companyDeptList = returnData || [];
        });
    },
    // 获取表格数据
    getTableData() {
      this.searchInfo.beginDate = this.searchInfo.dateRange[0];
      this.searchInfo.endDate = this.searchInfo.dateRange[1];
      this.$ajax
        .post(this.$apiUrls.GetExportPersonList, this.searchInfo)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.tableData = returnData || [];
        });
    },
    // 告知书按钮的点击回调
    notificationBtn() {
      console.log(this.selection, this.currentRow);
      if (!this.selection.length && !this.currentRow) {
        this.$message({
          message: `请先选择人员！`,
          type: 'warning'
        });
        return;
      }
      let selectRecords = [];
      if (!this.selection.length) {
        selectRecords.push(this.currentRow.regNo);
      } else {
        selectRecords = this.selection.map((item) => item.regNo);
      }

      console.log(selectRecords);
      this.generateNotice(selectRecords);
    },
    /**
     * @description: 生成告知书
     * @param {*} regNoArr
     * @return {*}
     */
    generateNotice(regNoArr) {
      if (this.isGenerating)
        return this.$message.warning('正在生成，请稍后再试！');
      this.isGenerating = true;
      this.blobObj = '';
      this.$ajax
        .post(
          this.$apiUrls.GetOccupationalDiseaseNoticeTemplate + '/pdf',
          regNoArr,
          {
            responseType: 'arraybuffer'
          }
        )
        .then((r) => {
          let blob = new Blob([r.data], { type: 'application/pdf' });
          this.blobObj = URL.createObjectURL(blob);
        })
        .finally(() => {
          this.isGenerating = false;
        });
    },
    /**
     * @description: 表格行点击事件
     * @param {*} row
     */
    rowClick(row) {
      this.regNoArr = [row.regNo];
      this.generateNotice([row.regNo]);
    },
    hexStringToArrayBuffer(hexString) {
      if (hexString.length % 2 !== 0) {
        throw new Error('Invalid hex string');
      }
      var byteArray = new Uint8Array(hexString.length / 2);
      for (var i = 0; i < hexString.length; i += 2) {
        var byte = parseInt(hexString.substr(i, 2), 16);
        if (isNaN(byte)) {
          throw new Error('Invalid hex string');
        }
        byteArray[i / 2] = byte;
      }
      return byteArray.buffer;
    },
    // 选择人员列表改变的回调
    selectionChange(row, checkList) {
      this.selection = checkList;
    },
    // 当前行改变的回调
    currentChange(row) {
      this.currentRow = row;
    },
    // 下载word
    downLoadWord(isWord) {
      if (!this.blobObj) {
        this.$message({
          message: `请先选择人员！`,
          type: 'warning'
        });
        return;
      }
      if (isWord) {
        let downloadLink;

        this.$ajax
          .post(
            this.$apiUrls.GetOccupationalDiseaseNoticeTemplate + '/docx',
            this.regNoArr,
            {
              responseType: 'arraybuffer'
            }
          )
          .then((r) => {
            downloadLink = new Blob([r.data], {
              type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            });
            saveAs(downloadLink, '疑似职业病告知书');
          });
      } else {
        saveAs(this.blobObj, '疑似职业病告知书');
      }
    }
  },
  created() {
    this.getCompanyWithTimesList();
  },
  mounted() {
    console.log(renderAsync);
  }
};
</script>

<style lang="less" scoped>
.dubiousNotification_page {
  display: flex;
  flex-direction: column;
  .header-wrapper {
    background: #fff;
    padding: 0 5px 5px;
    .el-form-item {
      margin-bottom: 0 !important;
    }
  }
  .title_wrap {
    display: flex;
    justify-content: space-between;
  }
  .main {
    flex: 1;
    display: flex;
    overflow: auto;
    padding-top: 5px;
  }
  .main-left,
  .main-right {
    background: #fff;
    border-radius: 4px;
    padding: 5px;
    overflow: auto;
  }
  .main-left {
    width: 352px;
    margin-right: 5px;
  }
  .main-right {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .left-table {
    height: 100%;
    border: 1px solid rgba(178, 190, 195, 0.5);
    border-radius: 4px;
  }
  .right-btn {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    // justify-content: space-between;
    // padding: 0 20px;
  }
  .btn-item {
    display: flex;
    align-items: center;
    margin-right: 10px;
    &:nth-child(1) {
      flex: 1;
    }
    &:nth-child(2) {
      flex: 2;
    }
    &:nth-child(3) {
      flex: 2;
    }
    &:nth-child(4) {
      flex: 3;
    }
    label {
      width: 82px;
      text-align: right;
      margin-right: 10px;
      font-size: 14px;
      font-weight: 600;
    }
  }
  .right-content {
    flex: 1;
    flex-shrink: 0;
    background: rgba(23, 112, 223, 0.1);
    border-radius: 4px;
    display: flex;
    overflow: auto;
    justify-content: center;
    .docx_dom {
      width: 100%;
    }
  }
}
</style>
