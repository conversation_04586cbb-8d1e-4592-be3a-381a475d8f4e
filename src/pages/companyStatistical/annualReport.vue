<!-- 单位体检年度综合报告 -->
<template>
  <div class="annualReport_page">
    <div class="left_wrap">
      <!-- 分类 -->
      <div class="every_wrap" style="margin-bottom: 10px">
        <header>
          <div class="search_wrap">
            <el-input placeholder="按关键字查找" size="small"></el-input>
            <el-button
              class="search_btn blue_btn"
              icon="iconfont icon-search"
              size="mini"
              >查询</el-button
            >
          </div>
          <div style="margin-top: 10px">
            <el-button class="blue_btn" icon="iconfont icon-bianji" size="mini"
              >编辑</el-button
            >
            <el-button
              class="search_btn green_btn"
              icon="iconfont icon-huifuxitongmoren"
              size="mini"
              >刷新</el-button
            >
            <el-button
              class="search_btn violet_btn"
              type="primary"
              icon="iconfont icon-moban"
              size="mini"
              >模板</el-button
            >
          </div>
        </header>
        <div class="table_div">
          <PublicTable
            :theads="typeThead"
            :viewTableList="typeList"
            isCheck
            :isSortShow="false"
          >
          </PublicTable>
        </div>
      </div>
      <!-- 套餐 -->
      <div class="every_wrap">
        <header>
          <div class="search_wrap">
            <el-input placeholder="按关键字查找" size="small"></el-input>
            <el-button
              class="search_btn blue_btn"
              icon="iconfont icon-search"
              size="mini"
              >查询</el-button
            >
          </div>
        </header>
        <div class="table_div">
          <PublicTable
            :theads="mealThead"
            :viewTableList="mealList"
            isCheck
            :isSortShow="false"
            :columnWidth="mealColumnWidth"
          >
          </PublicTable>
        </div>
      </div>
    </div>
    <div class="right_wrap">
      <header>
        <el-form :model="searchForm" label-width="60px" class="form_dom">
          <el-form-item
            label="登记日期"
            prop="statisticalTime"
            label-width="70px"
          >
            <el-date-picker
              :picker-options="{ shortcuts: G_datePickerShortcuts }"
              style="width: 210px"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              v-model="statisticalTime"
              :clearable="false"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="单位" prop="companyCode">
            <el-cascader
              ref="company_cascader_ref"
              v-model="searchForm.companyCode"
              :options="companyList"
              :filter-method="filterMethod"
              :props="{ multiple: false }"
              clearable
              filterable
              size="small"
              collapse-tags
              @change="companyChange"
            >
            </el-cascader>
          </el-form-item>
          <el-form-item label="部门" prop="department">
            <el-select
              placeholder="请选择"
              :disabled="isHave"
              v-model="searchForm.department"
              size="small"
              filterable
              clearable
            >
              <el-option
                v-for="item in companyDeptList"
                :key="item.companyCode"
                :label="item.deptName"
                :value="item.deptCode"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <ButtonGroup :btnList="['统计', '导出']">
          <template #addBtn>
            <el-button
              size="small"
              class="green_btn btn"
              icon="iconfont icon-dayin-"
              v-print="'#test'"
              >打印</el-button
            >
          </template>
        </ButtonGroup>
      </header>
      <h3>
        <span>2022年广东亿讯科技有限公司员工健康体检情况分析：</span>
        <!-- <p>
                    <i class="el-icon-location-outline"></i>
                    定位导航
                </p> -->
        <el-popover
          placement="top-start"
          popper-class="annualReport_page"
          trigger="click"
        >
          <div class="popper_head">定位导航</div>
          <ul class="nav_ul">
            <li
              :class="li_activeId == oneItem.id ? 'li_active' : ''"
              v-for="(oneItem, oneIdx) in navList"
              :key="oneIdx"
            >
              <p @click="toTopic(oneItem.id)">{{ oneItem.label }}</p>
              <ul>
                <li
                  :class="li_activeId == twoItem.id ? 'li_active' : ''"
                  v-for="(twoItem, twoIdx) in oneItem.children"
                  :key="twoIdx"
                >
                  <p @click="toTopic(twoItem.id)">{{ twoItem.label }}</p>
                </li>
              </ul>
            </li>
          </ul>
          <el-button
            style="font-size: 18px"
            type="text"
            slot="reference"
            icon="el-icon-location-outline"
            >定位导航</el-button
          >
        </el-popover>
      </h3>
      <!-- 报告内容 -->
      <div class="report_content" id="test">
        <div class="unit_cover" id="Cover_anchor">封面</div>
        <div class="every_report">
          <p id="Situation_anchor">A、参检人员构成情况：</p>
          <ReportTable
            :theads="theads_1"
            :tableData="reportData.joinPersonnel"
          ></ReportTable>
        </div>
        <div class="every_report">
          <p id="Join_anchor">B、体检项目的参检情况：</p>
          <ReportTable
            :columnWidth="columnWidth_2"
            isSerial
            :theads="theads_2"
            :tableData="reportData.healthExaminationItem"
          >
          </ReportTable>
        </div>
        <div class="every_report">
          <p id="Suggest_anchor">一、常见疾病名单及医学建议</p>
          <div
            class="suggest_group"
            v-for="(item, idx) in reportData.illnessList"
            :key="idx"
          >
            <ul class="illness_title">
              <li>
                <span>1</span>
                <p>{{ item.illnessName }}</p>
              </li>
              <li>人数：{{ item.peopleNum }}人</li>
            </ul>
            <div class="suggest_div">
              <label>建议：</label>
              <p>
                (1)考虑血脂异常，建议低动物脂肪、低糖、低盐饮食，控制食量，加强体育锻炼；并定期复查血脂。(2)若经饮食控制及加强体育锻炼1～2月后，复查血脂仍高，建议心内科诊治，应用降脂药物。
              </p>
            </div>
            <ReportTable
              isSerial
              :theads="theads_3"
              :tableData="item.personnelList"
            >
            </ReportTable>
          </div>
        </div>
        <div class="every_report">
          <p id="ChartStatistics_anchor">二、常见疾病图表统计</p>
          <div class="suggest_group">
            <a id="PeopleRatio_anchor"></a>
            <ul class="illness_title">
              <li>
                <span>1</span>
                <p>疾病人数与比例</p>
              </li>
            </ul>
            <ReportTable
              :theads="theads_4"
              :tableData="reportData.numberDiseases"
            >
            </ReportTable>
            <div
              class="echart_dom"
              id="NumDiseasesEchart"
              style="height: 270px"
            ></div>
            <div
              class="echart_dom"
              id="FamiliarIllnessPercentage"
              style="height: 270px"
            ></div>
          </div>
          <div class="suggest_group">
            <ul class="illness_title">
              <li id="AgeDistribution_anchor">
                <span>2</span>
                <p>疾病人员年龄分布</p>
              </li>
            </ul>
            <ReportTable
              :theads="theads_5"
              :tableData="reportData.numberDiseases"
            >
            </ReportTable>
          </div>
          <!-- 男女病患比例 -->
          <div class="suggest_group">
            <ul class="illness_title">
              <li id="SickenRatio_anchor">
                <span>3</span>
                <p>男女病患比例</p>
              </li>
            </ul>
            <ReportTable
              isShowSummary
              :theads="theads_6"
              :tableData="reportData.sickenRatio"
            >
            </ReportTable>
            <div
              class="echart_dom"
              id="SickenRatio"
              style="height: 270px"
            ></div>
          </div>
        </div>
        <div class="every_report">
          <p id="StatisticsAnalyse_anchor">三、单位综合统计分析</p>
          <div class="suggest_group">
            <div
              class="echart_dom"
              id="MorbidityAge"
              style="height: 270px"
            ></div>
            <div
              class="echart_dom"
              id="ContrastAnalyse"
              style="height: 270px"
            ></div>
          </div>
          <div
            class="suggest_group"
            v-for="(item, idx) in reportData.suggestList"
            :key="idx"
          >
            <ul class="illness_title">
              <li>
                <span>{{ idx + 1 }}</span>
                <p>{{ item.illnessName }}</p>
              </li>
            </ul>
            <div class="suggest_div">
              <label>建议：</label>
              <p>
                {{ item.suggest }}
              </p>
            </div>
          </div>
        </div>
        <div class="every_report">
          <p id="Annex_anchor">四、附录：体检结果一栏表</p>
          <div
            class="review_wrap"
            v-for="(oneItem, oneIdx) in reportData.resultList"
            :key="oneIdx"
          >
            <div class="patientInfo_wrap">
              <span>体检号：{{ oneItem.regNo }}</span>
              <span>姓名：{{ oneItem.name }}</span>
              <span>性别：{{ oneItem.sex }}</span>
              <span>年龄：{{ oneItem.age }}</span>
              <span>部门：{{ oneItem.department }}</span>
              <span>工号：{{ oneItem.jobnum }}</span>
            </div>
            <div class="review_thead review_row">
              <div>综述</div>
              <div>建议</div>
            </div>
            <div class="review_body review_row">
              <div>
                <ul>
                  <li v-for="(item, index) in oneItem.overview" :key="index">
                    <span>{{ index + 1 }}、</span>
                    <div>
                      <label>{{ item.title }}</label>
                      <p>{{ item.val }}</p>
                    </div>
                  </li>
                </ul>
              </div>
              <div>
                <ul>
                  <li v-for="(item, index) in oneItem.suggest" :key="index">
                    <span>{{ index + 1 }}、</span>
                    <div>
                      <label>{{ item.title }}</label>
                      <p>{{ item.val }}</p>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import PublicTable from '@/components/publicTable';
import ButtonGroup from './components/buttonGroup.vue';
import ReportTable from './components/reportTable';
import tableConfig from './mixins/tableConfig';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
export default {
  name: 'annualReport',
  mixins: [tableConfig],
  components: {
    PublicTable,
    ButtonGroup,
    ReportTable
  },
  data() {
    return {
      typeThead: {
        typeName: '分类名称'
      },
      typeList: [
        {
          typeName: '贫血'
        },
        {
          typeName: '贫血'
        },
        {
          typeName: '贫血'
        },
        {
          typeName: '贫血'
        },
        {
          typeName: '贫血'
        }
      ],

      mealThead: {
        times: '次数',
        mealCode: '套餐代码',
        mealName: '套餐名称'
      },
      mealList: [
        {
          times: '1',
          mealCode: '0101',
          mealName: '3号尊贵版套餐'
        }
      ],
      mealColumnWidth: {
        times: 50
      },
      searchForm: {
        beginDate: '',
        endDate: '',
        companyCode: '',
        itemClsCode: '',
        doctorName: '',
        deptCode: '',
        department: ''
      },
      statisticalTime: [],
      companyList: [],
      companyDeptList: [],
      isHave: true,
      navList: [
        {
          id: 'Cover_anchor',
          label: '封面',
          children: []
        },
        {
          id: '',
          label: '目录',
          children: []
        },
        {
          id: 'Situation_anchor',
          label: 'A、参检人员构成情况',
          children: []
        },
        {
          id: 'Join_anchor',
          label: 'B、体检项目的参检情况',
          children: []
        },
        {
          id: 'Suggest_anchor',
          label: '一、常见疾病名单及医学建议',
          children: []
        },
        {
          id: 'ChartStatistics_anchor',
          label: '二、常见疾病图表统计',
          children: [
            {
              id: 'PeopleRatio_anchor',
              label: '1、疾病人数与比例'
            },
            {
              id: 'AgeDistribution_anchor',
              label: '2、疾病人员年龄分布'
            },
            {
              id: 'SickenRatio_anchor',
              label: '3、男女患病比例'
            }
          ]
        },
        {
          id: 'StatisticsAnalyse_anchor',
          label: '三、单位综合统计分析',
          children: []
        },
        {
          id: 'Annex_anchor',
          label: '四、附录：体检结果一栏表',
          children: []
        }
      ],
      li_activeId: 'null'
    };
  },
  computed: {
    ...mapGetters(['G_datePickerShortcuts'])
  },
  methods: {
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    // 锚点跳转
    toTopic(idName) {
      if (!idName) return;
      this.li_activeId = idName;
      document.querySelector('#' + idName).scrollIntoView(true);
    },
    // 获取单位
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    //单位下拉变化
    companyChange(data) {
      this.getDepartList(data);
      if (!data || data.length === 0) return;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.statisticalTime = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
    },
    //获取部门数据
    getDepartList(companyCode) {
      this.searchForm.department = '';
      this.isHave = true;
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanyDepartment, {
          companyCode: companyCode?.[0] || '',
          deptCode: '',
          deptName: ''
        })
        .then((r) => {
          this.isHave = false;
          //console.log("r", r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.companyDeptList = returnData || [];
        });
    }
  },
  mounted() {
    this.getCompany();
  }
};
</script>

<style lang="less" scoped>
.annualReport_page {
  display: flex;

  .left_wrap {
    width: 280px;
    margin-right: 10px;
    border-radius: 4px;
    background: #fff;
    padding: 10px;
    display: flex;
    flex-direction: column;
    overflow: auto;

    .every_wrap {
      display: flex;
      flex-direction: column;
      overflow: auto;

      &:first-child {
        height: 450px;
      }

      &:last-child {
        flex: 1;
      }
    }

    header {
      margin-bottom: 10px;
    }

    .search_wrap {
      display: flex;

      .search_btn {
        margin-left: 5px;
      }
    }

    .table_div {
      flex: 1;
    }
  }

  .right_wrap {
    border-radius: 4px;
    background: #fff;
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    padding: 10px;
    display: flex;
    flex-direction: column;

    header {
      display: flex;
      margin-bottom: 15px;

      .form_dom {
        display: flex;
        justify-content: center;
        margin-right: 10px;

        /deep/ .el-form-item {
          margin-bottom: 0;
        }
      }

      .radioForm {
        /deep/ .el-form-item__content {
          margin-left: 20px !important;
        }
      }
    }

    > h3 {
      display: flex;
      font-size: 18px;
      justify-content: space-between;

      span {
        font-weight: 600;
        color: #2d3436;
        line-height: 45px;
      }

      p {
        color: #1770df;
        cursor: pointer;
      }
    }

    .report_content {
      flex: 1;
      flex-shrink: 0;
      overflow: auto;

      .unit_cover {
        height: 30px;
        text-align: center;
        margin-bottom: 10px;
      }

      .every_report {
        margin-bottom: 10px;

        > p {
          height: 38px;
          line-height: 38px;
          background: #1770df;
          color: #fff;
          padding: 0 15px;
        }

        .suggest_div {
          padding: 10px 20px;
          font-size: 14px;
          color: #2d3436;

          label {
            display: block;
            font-weight: 600;
            margin-bottom: 10px;
          }
        }

        .illness_title {
          display: flex;
          margin-top: 10px;
          background: rgba(23, 112, 223, 0.2);
          align-items: center;

          li {
            height: 38px;
            font-size: 18px;
            color: #2d3436;
            line-height: 38px;

            &:nth-child(1) {
              display: flex;
              flex-basis: 30%;
              position: relative;
              padding-left: 60px;

              span {
                width: 50px;
                background: #1770df;
                color: #fff;
                position: absolute;
                top: 0;
                bottom: 0;
                left: 0;
                display: flex;
                align-items: center;
                justify-content: center;
              }

              p {
                flex: 1;
                flex-shrink: 0;
              }
            }

            &:nth-child(2) {
              flex: 1;
            }
          }
        }

        .echart_dom {
          margin-top: 10px;
        }

        .patientInfo_wrap {
          display: flex;
          padding: 7px 15px;
          background: rgba(23, 112, 223, 0.2);
          margin-top: 10px;
          font-size: 18px;
          color: #2d3436;

          span {
            & + span {
              margin-left: 10px;
            }

            &:nth-child(5) {
              flex: 1;
              flex-shrink: 0;
            }

            &:last-child {
              width: 120px;
            }
          }
        }

        .review_row {
          display: flex;
          overflow: auto;

          > div {
            flex: 1;
            flex-shrink: 0;
            border-bottom: 1px solid rgba(45, 52, 54, 0.6);
            padding: 10px 15px;

            & + div {
              border-left: 1px solid rgba(45, 52, 54, 0.6);
            }
          }
        }

        .review_thead {
          font-size: 14px;
          color: #2d3436;
        }

        .review_body {
          li {
            display: flex;

            & + li {
              margin-bottom: 10px;
            }

            div {
              flex: 1;
              flex-shrink: 0;
            }

            label {
              color: #1770df;
              margin-bottom: 4px;
            }
          }
        }
      }
    }
  }

  .popper_head {
    height: 48px;
    line-height: 48px;
    background: rgba(23, 112, 223, 0.2);
    font-size: 18px;
    color: #2d3436;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    padding: 0 15px;
  }

  .nav_ul {
    padding-top: 48px;

    li {
      p {
        height: 38px;
        line-height: 38px;
        position: relative;
        padding-left: 20px;
        cursor: pointer;

        &::before {
          display: block;
          content: '';
          position: absolute;
          top: 0;
          width: 1px;
          height: 100%;
          background: #b2bec3;
          left: 0;
        }
      }

      li {
        p {
          padding-left: 50px;
        }
      }
    }

    .li_active {
      > p {
        color: #1770df;

        &::before {
          background: #1770df;
        }

        &::after {
          display: block;
          content: '';
          position: absolute;
          width: 14px;
          height: 14px;
          background: #b2bec3;
          top: 50%;
          transform: translateY(-50%);
          left: -6px;
          background: url('~@/assets/img/doctorWorkStation/position.png')
            no-repeat center;
          background-size: 100% 100%;
        }
      }
    }
  }
}
</style>
<style lang="less" media="test" scoped>
#test {
  flex: 1;
  flex-shrink: 0;
  overflow: auto;

  .unit_cover {
    height: 30px;
    text-align: center;
    margin-bottom: 10px;
  }

  .every_report {
    margin-bottom: 10px;

    > p {
      height: 38px;
      line-height: 38px;
      background: #1770df;
      color: #fff;
      padding: 0 15px;
    }

    .suggest_div {
      padding: 10px 20px;
      font-size: 14px;
      color: #2d3436;

      label {
        display: block;
        font-weight: 600;
        margin-bottom: 10px;
      }
    }

    .illness_title {
      display: flex;
      margin-top: 10px;
      background: rgba(23, 112, 223, 0.2);
      align-items: center;

      li {
        height: 38px;
        font-size: 18px;
        color: #2d3436;
        line-height: 38px;

        &:nth-child(1) {
          display: flex;
          flex-basis: 30%;
          position: relative;
          padding-left: 60px;

          span {
            width: 50px;
            background: #1770df;
            color: #fff;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          p {
            flex: 1;
            flex-shrink: 0;
          }
        }

        &:nth-child(2) {
          flex: 1;
        }
      }
    }

    .echart_dom {
      margin-top: 10px;
    }

    .patientInfo_wrap {
      display: flex;
      padding: 7px 15px;
      background: rgba(23, 112, 223, 0.2);
      margin-top: 10px;
      font-size: 14px;
      color: #2d3436;

      span {
        & + span {
          margin-left: 10px;
        }

        &:nth-child(5) {
          flex: 1;
          flex-shrink: 0;
        }

        &:last-child {
          width: 120px;
        }
      }
    }

    .review_row {
      display: flex;
      overflow: auto;

      > div {
        flex: 1;
        flex-shrink: 0;
        border-bottom: 1px solid rgba(45, 52, 54, 0.6);
        padding: 10px 15px;

        & + div {
          border-left: 1px solid rgba(45, 52, 54, 0.6);
        }
      }
    }

    .review_thead {
      font-size: 14px;
      color: #2d3436;
    }

    .review_body {
      li {
        display: flex;

        & + li {
          margin-bottom: 10px;
        }

        div {
          flex: 1;
          flex-shrink: 0;
        }

        label {
          color: #1770df;
          margin-bottom: 4px;
        }
      }
    }
  }
}
</style>
