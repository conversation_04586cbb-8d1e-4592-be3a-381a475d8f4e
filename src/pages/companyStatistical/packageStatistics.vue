<template>
  <!-- 体检单位套餐统计表 -->
  <div class="packageStatistics" id="packageStatistics">
    <div class="packageStatistics-wrap">
      <div class="header">
        <div class="header-item">
          <span style="width: 35px">单位</span>
          <el-cascader
            ref="company_cascader_ref"
            v-model="searchForm.companyCode"
            :filter-method="filterMethod"
            :options="companyList"
            :props="{ multiple: false }"
            clearable
            filterable
            size="small"
            collapse-tags
            @change="companyChange"
          >
          </el-cascader>
        </div>
        <div class="header-item">
          <span>部门</span>
          <el-select
            class="select"
            v-model.trim="searchForm.companyDeptCode"
            placeholder="请选择"
            size="mini"
            filterable
            clearable
            :disabled="isHavue"
            @change="search"
          >
            <el-option
              v-for="(item, index) in companyDeptList"
              :key="index"
              :label="item.deptName"
              :value="item.deptCode"
            ></el-option>
          </el-select>
        </div>
        <div class="header-item">
          <span>套餐</span>
          <el-select
            placeholder="请选择"
            size="mini"
            filterable
            clearable
            v-model="searchForm.clusterCode"
            class="input"
            @change="search"
          >
            <el-option
              v-for="item in clusterList"
              :key="item.clusCode"
              :label="item.clusName"
              :value="item.clusCode"
            >
            </el-option>
          </el-select>
        </div>
        <div class="header-item">
          <span>状态</span>
          <el-select
            style="width: 100px"
            placeholder="请选择"
            size="mini"
            clearable
            v-model="searchForm.peStatus"
            class="input"
            @change="search"
          >
            <el-option
              v-for="item in G_peStatus"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <div class="header-item">
          <span>年龄段</span>
          <div style="display: flex; align-items: center">
            <el-input
              style="width: 60px"
              size="mini"
              onkeyup="this.value=this.value.replace(/\D|^/g,'')"
              v-model="searchForm.lowerAgeLimit"
              @input="inputChange"
            ></el-input>
            -
            <el-input
              style="width: 60px"
              onkeyup="this.value=this.value.replace(/\D|^/g,'')"
              @input="inputChange"
              v-model="searchForm.upperAgeLimit"
              size="mini"
            ></el-input>
          </div>
        </div>
        <div class="header-item">
          <span>统计时间</span>
          <el-date-picker
            :picker-options="{ shortcuts: G_datePickerShortcuts }"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
            v-model="statisticalTime"
            @change="search"
            :clearable="false"
            class="select"
          >
          </el-date-picker>
        </div>
        <div class="header-item">
          <ButtonGroup
            :btnList="['统计', '导出']"
            @statistics="search"
          ></ButtonGroup>
        </div>
      </div>
      <div id="printDiv">
        <div class="titlewrap">体检单位套餐统计表:</div>
        <div class="contDiv" id="contDiv">
          <p class="titleP">
            <span>体检单位：{{ companyName ? companyName : '无单位' }}</span>
            <span style="margin-left: 20px"
              >总人数：{{ tableData.length }}</span
            >
          </p>
          <div class="tableDiv">
            <PublicTable
              :isSortShow="false"
              :viewTableList.sync="tableData"
              :theads.sync="theads"
              :columnWidth="columnWidth"
              :columnSort="['peStatus', 'activeTime']"
            >
              <template #sex="{ scope }">
                <div>
                  {{ G_EnumList['Sex'][scope.row.sex] }}
                </div>
              </template>
              <template #peStatus="{ scope }">
                <div>
                  {{ G_EnumList['PeStatus'][scope.row.peStatus] }}
                </div>
              </template>
            </PublicTable>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import PublicTable from '@/components/publicTable.vue';
import ButtonGroup from './components/buttonGroup.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
export default {
  name: 'packageStatistics',
  components: { PublicTable, ButtonGroup },
  data() {
    return {
      searchForm: {
        beginDate: '',
        endDate: '',
        companyCode: '',
        companyDeptCode: '',
        clusterCode: '',
        peStatus: '',
        upperAgeLimit: 0,
        lowerAgeLimit: 0
      },
      isHavue: true,
      companyDeptList: [],
      clusterList: [],
      companyName: '',
      statisticalTime: [dataUtils.getDate(), dataUtils.getDate()],
      companyList: [],
      itemClsList: [],
      optionsExamRoom: [],
      theads: {
        regNo: '体检号',
        // jobNo: "工号",
        name: '姓名',
        sex: '性别',
        age: '年龄',
        companyDeptName: '部门',
        activeTime: '体检日期',
        peStatus: '体检状态',
        tel: '电话',
        cardNo: '身份证',
        clusterName: '套餐'
      },
      columnWidth: {
        regNo: 130,
        name: 70,
        sex: 60,
        age: 60,
        activeTime: 180,
        peStatus: 100,
        cardNo: 180,
        clusterName: 200
      },
      tableData: [],
      summation: {
        combName: '合计',
        amount: 0,
        times: 0
      }
    };
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_sysOperator',
      'G_datePickerShortcuts',
      'G_peStatus'
    ])
  },
  mounted() {
    this.getCompany();
  },
  created() {
    this.getClusterList();
  },
  methods: {
    //统计查询
    search() {
      // if (this.searchForm.companyCode === "") {
      //   this.$message({
      //     message: "请选择单位！",
      //     type: "warning",
      //     showClose: true
      //   });
      //   return;
      // }
      if (!this.statisticalTime) {
        this.searchForm.beginDate = '';
        this.searchForm.endDate = '';
      } else {
        this.searchForm.beginDate = this.statisticalTime[0];
        this.searchForm.endDate = this.statisticalTime[1];
      }
      let { companyCode, peStatus, upperAgeLimit, lowerAgeLimit, ...surplus } =
        this.searchForm;
      let parameter = {
        companyCode: companyCode[0],
        peStatus: peStatus || 0,
        upperAgeLimit: upperAgeLimit || 0,
        lowerAgeLimit: lowerAgeLimit || 0,
        ...surplus
      };
      this.$ajax
        .post(this.$apiUrls.GetCompanyPatientClusters, parameter)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.tableData = returnData || [];
          let temp = this.companyList.find(
            (item) => item.value === this.searchForm.companyCode[0]
          )?.label;
          this.$nextTick(() => {
            this.companyName = temp;
          });
        });
    },
    inputChange(value) {
      if (this.searchForm.upperAgeLimit < this.searchForm.lowerAgeLimit) {
        this.$message.error('年龄上限不能小于年龄下限');
        if (value === this.searchForm.upperAgeLimit)
          this.searchForm.upperAgeLimit = this.searchForm.lowerAgeLimit;
        else this.searchForm.lowerAgeLimit = this.searchForm.upperAgeLimit;
      }
    },
    // 获取单位
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    //单位下拉变化
    companyChange(data) {
      this.companyTimesChange();
      this.getClusterList();
      if (!data || data.length === 0) {
        this.isHavue = true;
        this.statisticalTime = [dataUtils.getDate(), dataUtils.getDate()];
        return;
      }
      this.getDepartList(data);
      this.isHavue = false;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.statisticalTime = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.search();
    },
    //查询公司部门信息
    getDepartList(companyCode) {
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanyDepartment, {
          companyCode: companyCode?.[0] || '',
          deptCode: '',
          deptName: ''
        })
        .then((r) => {
          //console.log("r", r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.companyDeptList = returnData || [];
        });
    },
    //次数改变时
    companyTimesChange() {
      this.searchForm.clusterCode = '';
      console.log(this.searchForm.companyCode[1]);

      if (!this.searchForm.companyCode[1]) {
        this.clusterList = this.fixed_clusterList;
        return;
      }
      this.$ajax
        .post(this.$apiUrls.ReadCompanyCandidateCluster, '', {
          query: {
            companyCode: this.searchForm.companyCode[0],
            companyTimes: this.searchForm.companyCode[1]
          }
        })
        .then(async (r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.clusterList = returnData || [];
        });
    },
    // 获取套餐下拉
    getClusterList() {
      this.$ajax.post(this.$apiUrls.Cluster).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.clusterList = returnData;
        this.fixed_clusterList = returnData;
      });
    }
  }
};
</script>
<style lang="less" scoped>
/deep/ .el-input-number__decrease,
.el-input-number__number {
  display: none;
}
.packageStatistics {
  color: #2d3436;
  display: flex;
  flex-direction: column;
  .packageStatistics-wrap {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    background: #fff;
    padding: 10px;
    font-size: 14px;
  }
  .header {
    display: flex;
    align-items: center;
    font-size: 14px;
    margin-bottom: 10px;
  }
  .select {
    width: 100%;
  }
  .header-item {
    display: flex;
    align-items: center;
    margin-right: 10px;
    span {
      font-weight: 600;
      // width: 66px;
      white-space: nowrap;
      margin-right: 10px;
    }
  }
  #printDiv {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    .titlewrap {
      font-size: 18px;
      font-weight: 600;
    }
    .contDiv {
      flex: 1;
      overflow: auto;
      display: flex;
      flex-direction: column;
      .tableDiv {
        flex: 1;
        overflow: auto;
        border: 1px solid #ccc;
        border-radius: 4px;
      }
      .titleP {
        color: #1770df;
        font-weight: 600;
        margin: 10px 0;
      }
    }
  }
}
</style>
