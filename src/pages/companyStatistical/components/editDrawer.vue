<template>
  <div class="editDrawer">
    <div class="stepDiv">
      <el-steps :active="active">
        <el-step title="步骤1:" description="选择疾病分类"></el-step>
        <el-step title="步骤2:" description="选择疾病"></el-step>
      </el-steps>
      <div class="btnDiv">
        <el-button
          style="margin-top: 12px"
          @click="last"
          v-show="active == 2"
          :disabled="isEdit"
          >上一步</el-button
        >
        <el-button style="margin-top: 12px" @click="next" v-show="active == 1"
          >下一步</el-button
        >
        <!-- <el-button
          style="margin-top: 12px"
          size="small"
          class="blue_btn"
          icon="iconfont icon-baocun"
          v-show="active == 2 && !isEdit"
          >保存</el-button
        > -->
      </div>
    </div>
    <!-- 第一页 -->
    <div class="bodyDiv1" v-show="active == 1">
      <ul class="record_head head1">
        <li>
          <div class="every_inp">
            <label>疾病分类列表</label>
          </div>
        </li>
        <li>
          <div class="every_inp1">
            <el-input
              v-model.trim="iptVal"
              @keyup.enter.native="getviewList"
              @clear="getviewList"
              size="small"
              style="margin: 0 20px"
              clearable
              placeholder="代码/名称"
            ></el-input>
            <el-button
              size="small"
              class="blue_btn btn"
              icon="iconfont icon-search"
              @click="getTable1"
              >搜索</el-button
            >
            <el-button
              size="small"
              class="blue_btn btn"
              icon="iconfont icon-xinjian"
              style="margin-left: 20px"
              @click="addInfo"
              >增加</el-button
            >
          </div>
        </li>
      </ul>
      <div class="record_table">
        <PublicTable
          :viewTableList="firstList"
          :theads="drawerThead"
          @currentChange="handleClick"
          :isSortShow="false"
        >
          <template #columnRight>
            <el-table-column prop="" label="操作" width="200" fixed="right">
              <template slot-scope="scope">
                <el-button
                  type="primary"
                  plain
                  size="mini"
                  class="blue_btn"
                  @click="viewDetails(scope.row)"
                  >修改</el-button
                >
                <el-button
                  type="primary"
                  plain
                  size="mini"
                  class="red_btn"
                  @click="delDetails(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </template>
        </PublicTable>
      </div>
    </div>

    <!-- 第二页 -->
    <div class="fullbodyDiv" v-show="active == 2">
      <div class="leftBody">
        <ul class="record_head">
          <li>
            <div class="every_inp">
              <label> 疾病列表 </label>
              <p>
                <el-select
                  v-model.trim="leftSel"
                  placeholder="请选择"
                  size="small"
                  clearable
                  filterable
                  @change="getLeftList"
                  @clear="getLeftList"
                >
                  <el-option
                    v-for="item in G_codeItemCls"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </p>
            </div>
            <div class="every_inp1">
              <p>
                <el-input
                  v-model.trim="leftIpt"
                  @keyup.enter.native="getLeftList"
                  @clear="getLeftList"
                  size="small"
                  placeholder="代码/名称"
                  clearable
                ></el-input>
              </p>
            </div>
            <el-button
              size="small"
              class="blue_btn btn"
              icon="iconfont icon-search"
              @click="getLeftList"
              >搜索</el-button
            >
          </li>
        </ul>
        <div class="record_table">
          <PublicTable
            :viewTableList="leftList"
            :theads="comThead"
            isCheck
            :isSortShow="false"
            @selectionChange="selectionChange"
            ref="leftTable"
          >
          </PublicTable>
        </div>
      </div>
      <div class="centerBody">
        <el-button
          size="small"
          class="blue_btn add-del"
          style="margin-bottom: 40px"
          @click="addComb"
          >添加
          <i class="iconfont icon-Rightxiangyou34"></i>
        </el-button>
        <el-button
          size="small"
          class="red_btn add-del"
          @click="delComb"
          icon="iconfont icon-Leftxiangzuo35"
        >
          删除</el-button
        >
      </div>
      <div class="rightBody">
        <ul class="record_head">
          <li>
            <div class="every_inp">
              <label :title="title2">{{ title2 }}</label>
            </div>
            <p class="p">
              <el-input
                v-model.trim="rightIpt"
                @keyup.enter.native="getRightData"
                @clear="getRightData"
                placeholder="代码/名称"
                size="small"
                clearable
              >
              </el-input>
              <el-button
                size="small"
                class="blue_btn btn"
                icon="iconfont icon-search"
                @click="getRightData"
                >搜索</el-button
              >
            </p>
          </li>
        </ul>
        <div class="record_table">
          <div class="tableDiv">
            <PublicTable
              :viewTableList="rightList"
              :theads="comThead"
              isCheck
              :isSortShow="false"
              :highlightCurrentRow="false"
              @selectionChange="rightSelectionChange"
              ref="rightTable"
            >
            </PublicTable>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      title="新增疾病分类"
      :visible.sync="dialogVisible"
      append-to-body
      custom-class="dialogCss"
      :close-on-click-modal="false"
      v-if="dialogVisible"
      :before-close="close"
    >
      <el-form
        :model="popupForm"
        ref="ruleForm"
        :rules="rules"
        label-width="140px"
        class="headForm"
      >
        <el-row>
          <el-form-item label=" 代码：" prop="diseaseClsCode">
            <el-input
              v-model.trim="popupForm.diseaseClsCode"
              size="mini"
              placeholder="请输入"
              clearable
            ></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="疾病分类名称：" prop="diseaseClsName">
            <el-input
              v-model.trim="popupForm.diseaseClsName"
              size="mini"
              placeholder="请输入"
              clearable
            ></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="说明建议：" prop="suggestion">
            <el-input
              v-model.trim="popupForm.suggestion"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4 }"
              autocomplete="off"
              size="small"
              clearable
            ></el-input>
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="close" size="small">取 消</el-button>
        <el-button type="primary" size="small" @click="submit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import PublicTable from '@/components/publicTable.vue';
import { mapGetters } from 'vuex';
export default {
  name: 'editDrawer',
  components: {
    PublicTable
  },
  props: {
    rowInfo: {
      type: Object,
      default: () => {
        return {};
      }
    },
    actives: {
      type: Number,
      default: () => {
        return 1;
      }
    },
    isEdit: {
      type: Boolean,
      default: () => {
        return false;
      }
    }
  },
  data() {
    return {
      active: this.actives,
      loading: false,
      iptVal: '', //
      selList: [],
      dialogVisible: false,
      drawerThead: {
        diseaseClsCode: '代码',
        diseaseClsName: '疾病分类名称',
        suggestion: '说明建议'
      },
      firstList: [],
      firstListCopy: [],
      leftIpt: '',
      leftSel: '',
      comThead: {
        diseaseCode: '疾病代码',
        diseaseName: '疾病名称',
        deptName: '分类名称'
      },
      leftList: [],
      rowInfos: [], //点击第一行数据
      leftSelection: [],
      rightIpt: '',
      // rightSel: "",
      rightSelection: [],
      rightList: [],
      rightListCopy: [],
      columnWidth: {},
      title2: '',
      popupForm: {
        diseaseClsCode: '',
        diseaseClsName: '',
        suggestion: '',
        sortIndex: 0
      },
      rules: {
        diseaseClsCode: [
          { required: true, message: '请输入代码', trigger: 'blur' }
        ],
        diseaseClsName: [
          { required: true, message: '请输入疾病分类名称', trigger: 'blur' }
        ]
      },
      funTpye: '/Create',
      diseaseClsCode: ''
    };
  },
  computed: { ...mapGetters(['G_EnumList', 'G_codeItemCls']) },
  created() {},
  mounted() {
    this.getTable1();
  },
  methods: {
    //获取页面一表格数据
    getTable1() {
      this.$ajax
        .post(this.$apiUrls.RD_CodeDiseaseCls + '/Read', [])
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.firstList = returnData || [];
          this.firstListCopy = returnData || [];
          this.loading = false;
        });
    },
    // 筛选表一数据
    getviewList() {
      if (!this.iptVal) {
        return (this.firstList = this.firstListCopy);
      }
      this.firstList = this.firstListCopy.filter((item) => {
        return (
          item.diseaseClsCode.indexOf(this.iptVal) !== -1 ||
          item.diseaseClsName.indexOf(this.iptVal) !== -1
        );
      });
    },

    //页面一增加
    addInfo() {
      this.dialogVisible = true;
      this.funTpye = '/Create';
    },
    //页面一修改
    viewDetails(row) {
      this.dialogVisible = true;
      this.popupForm = {
        diseaseClsCode: row.diseaseClsCode,
        diseaseClsName: row.diseaseClsName,
        suggestion: row.suggestion,
        sortIndex: row.sortIndex
      };
      this.funTpye = '/Update';
    },
    //页面一删除
    delDetails(row) {
      this.$confirm('是否确认删除该条文件数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax
            .post(this.$apiUrls.RD_CodeDiseaseCls + '/Delete ', [
              row.diseaseClsCode
            ])
            .then((r) => {
              let { success } = r.data;
              if (!success) return;
              this.getTable1();
              this.$message({
                message: '删除成功',
                type: 'success',
                showClose: true
              });
            });
        })
        .catch(() => {
          return;
        });
    },
    close() {
      this.dialogVisible = false;
      this.$refs.ruleForm.resetFields();
    },
    //提交
    submit() {
      console.log('[ this.funTpye ]-427', this.funTpye);
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$ajax
            .post(
              this.$apiUrls.CU_CodeDiseaseCls + this.funTpye,
              this.popupForm
            )
            .then((r) => {
              r = r.data;
              if (!r.success) {
                return;
              }
              this.dialogVisible = false;
              this.$nextTick(() => {
                this.getTable1();
              });
              if (this.funTpye == '/Create') {
                this.$message({
                  message: '新建成功',
                  type: 'success',
                  showClose: true
                });
              } else {
                this.$message({
                  message: '修改成功',
                  type: 'success',
                  showClose: true
                });
              }
            });
        } else {
          return false;
        }
      });
    },

    // 上一步
    last() {
      if (this.active-- <= 1) this.active = 1;
    },
    // 下一步
    next() {
      console.log(this.rowInfos.length);
      if (this.rowInfos.length < 1) {
        this.$message({
          message: '请先选择一个体检项目！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.getAllData();
      if (this.active++ >= 2) this.active = 2;
    },

    //页面一点击事件
    handleClick(row) {
      console.log('[ row ]-389', row);
      if (!row) {
        return;
      }
      this.title2 = row.diseaseClsName + '-对应疾病列表';
      this.rowInfos = row;
      this.diseaseClsCode = row.diseaseClsCode;
    },

    //获取页面二表格数据
    //左边
    getLeftSearch() {
      this.$ajax
        .post(this.$apiUrls.GetDeptDisease4DiseaseCls, '', {
          query: {
            diseaseClsCode: this.diseaseClsCode
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.leftList = returnData || [];
          this.leftListCopy = returnData || [];
          this.loading = false;
        });
    },
    //右边
    getRightSearch() {
      this.$ajax
        .post(this.$apiUrls.Query_MapDiseaseClsDisease, '', {
          query: { diseaseClsCode: this.diseaseClsCode }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.rightList = returnData || [];
          this.rightListCopy = returnData || [];
          this.loading = false;
        });
    },
    // 左边表格单选
    leftHandleClick(row) {},
    // 左边表格多选
    selectionChange(val) {
      console.log('val: ', val);
      this.leftSelection = [];
      val.map((item, index) => {
        this.leftSelection.push({
          diseaseClsCode: this.diseaseClsCode,
          diseaseCode: item.diseaseCode
        });
      });
    },
    // 右边表格多选
    rightSelectionChange(val) {
      this.rightSelection = [];
      val.map((item, index) => {
        this.rightSelection.push({
          diseaseClsCode: this.diseaseClsCode,
          diseaseCode: item.diseaseCode
        });
      });
      console.log('this.rightSelection: ', this.rightSelection);
    },

    // 筛选左边数据
    getLeftList() {
      if (!this.leftSel && !this.leftIpt) {
        return (this.leftList = this.leftListCopy);
      }
      var obj = {};
      obj = this.G_codeItemCls.find((item) => {
        return item.value === this.leftSel;
      });
      let newData = [];
      if (obj) {
        newData = this.leftListCopy.filter((item) => {
          return item.deptName.indexOf(obj.label) !== -1;
        });
      } else {
        newData = this.leftListCopy;
      }
      this.leftList = newData.filter((item) => {
        return (
          item.diseaseCode.indexOf(this.leftIpt) !== -1 ||
          item.diseaseName.indexOf(this.leftIpt) !== -1
        );
      });
    },
    //筛选右边表格数据
    getRightData() {
      if (!this.rightIpt) {
        return (this.rightList = this.rightListCopy);
      } else {
        this.rightList = this.rightListCopy.filter((item) => {
          return (
            item.diseaseCode.indexOf(this.rightIpt) !== -1 ||
            item.diseaseName.indexOf(this.rightIpt) !== -1
          );
        });
      }
    },
    getAllData() {
      this.getLeftSearch();
      this.getRightSearch();
    },
    //添加
    addComb() {
      if (this.leftSelection.length < 1) {
        return this.$message({
          message: '请勾选至少一条左边表格数据再进行操作！',
          type: 'warning',
          showClose: true
        });
      }
      this.$ajax
        .post(
          this.$apiUrls.CD_MapDiseaseClsDisease + '/Create',
          this.leftSelection
        )
        .then((r) => {
          if (!r.data.success) {
            return;
          }
          this.getAllData();
          this.leftSelection = [];
          this.$message({
            showClose: true,
            message: '添加成功!',
            type: 'success'
          });
        });
    },
    //删除
    delComb() {
      if (this.rightSelection.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            console.log(this.selArr);
            this.$ajax
              .post(
                this.$apiUrls.CD_MapDiseaseClsDisease + '/Delete',
                this.rightSelection
              )
              .then((r) => {
                let { success } = r.data;
                if (!success) return;
                this.$message({
                  showClose: true,
                  message: '删除成功',
                  type: 'success'
                });
                this.getAllData();
              });
          })
          .catch(() => {
            return;
          });
      } else {
        return this.$message({
          message: '请勾选至少一条数据再进行操作！',
          type: 'warning',
          showClose: true
        });
      }
    }
  }
};
</script>
<style lang="less" scoped>
.editDrawer {
  height: 100%;
  width: 100%;
  overflow: auto;
  border-radius: 4px;
  padding: 18px;
  display: flex;
  flex-direction: column;
  .btn {
    padding: 6px 9px;
  }
  .add-del {
    padding: 9px 9px;
  }
  .stepDiv {
    display: flex;
    padding: 15px;
    .el-steps {
      width: 50%;
    }
    .btnDiv {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      height: 42px;
      .el-button {
        line-height: 0;
      }
    }
  }
  .bodyDiv {
    flex: 1;
    flex-shrink: 0;
    width: 100%;
    height: 100%;
    display: flex;
  }
  .fullbodyDiv {
    flex: 1;
    flex-shrink: 0;
    width: 100%;
    height: calc(100% - 108px);
    display: flex;
  }
  .bodyDiv1 {
    height: calc(100% - 108px);
    // padding: 15px;
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .bodyDiv2 {
    height: calc(100% - 108px);
    flex: 1;
    flex-shrink: 0;
    width: 100%;
    // height: 100%;
    display: flex;
  }
  .leftBody {
    background-color: #fff;
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: auto;
    // padding: 15px;
    .btn {
      margin-left: 10px;
    }
  }
  .centerBody {
    width: 100px;
    height: 100%;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    .el-button + .el-button {
      margin-left: 0;
    }
  }
  .rightBody {
    background-color: #fff;
    // padding: 15px;
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    .combo_group {
      flex: 1;
    }
  }
  .head1 {
    display: flex;
    justify-content: space-between;
  }
  .record_head {
    li {
      display: flex;
      margin-bottom: 18px;
    }

    .every_inp,
    .every_inp1 {
      display: flex;
      align-items: center;
      margin-right: 10px;

      label {
        margin-right: 10px;
        width: auto;
        text-align: right;
        overflow: hidden; /*溢出的部分隐藏*/
        white-space: nowrap; /*文本不换行*/
        text-overflow: ellipsis;
      }

      p {
        flex: 1;
      }

      &:nth-child(4) {
        flex: 1;
      }
    }
    .every_inp {
      width: 250px;
    }
    .every_inp1 {
      flex: 1;
    }
  }
  .p {
    flex: 1;
    display: flex;
    .el-input {
      margin: 0 5px;
    }
  }
  .record_table {
    height: calc(100% - 50px);
    flex: 1;
    flex-shrink: 0;
    border-radius: 4px;
    border: 1px solid #dbe1e4;
    overflow: auto;
    .tableDiv {
      height: 100%;
    }
    .tableDiv1 {
      height: 100%;
    }
    .all_price {
      height: 32px;
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      line-height: 32px;
      background: rgba(214, 48, 49, 0.1);
      border-radius: 4;
      overflow: hidden;
      color: #d63031;
      font-weight: 600;
      padding: 0 15px;
    }
  }
}
</style>
<style lang="less">
.dialogCss {
  height: 520px !important;
  width: 600px;
  display: flex;
  flex-direction: column;
  padding: 15px;
}
</style>
