<template>
  <el-table
    :show-summary="isShowSummary"
    :data="tableData"
    size="small"
    style="width: 100%"
  >
    <el-table-column type="index" label="序号" width="50" v-if="isSerial">
    </el-table-column>
    <el-table-column
      :width="columnWidth[th]"
      :prop="th"
      :label="theads[th]"
      v-for="th in Object.keys(theads)"
      :key="th"
    >
      <template slot-scope="scope">
        <slot :name="th" :scope="scope">{{ scope.row[th] }}</slot>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'reportTable',
  props: {
    // 是否显示序号
    isSerial: {
      type: Boolean,
      default: false
    },
    // 是否显示合计
    isShowSummary: {
      type: Boolean,
      default: false
    },
    tableData: {
      type: Array,
      default: []
    },
    theads: {
      type: Object,
      default: {}
    },
    // 列宽
    columnWidth: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {};
  },
  methods: {},
  created() {}
};
</script>

<style></style>
