import * as echarts from 'echarts';

export default {
  data() {
    // 参检人员构成情况
    return {
      reportData: {
        cover: '', //封面
        joinPersonnel: [
          {
            type: '计划',
            boys: 3,
            girls: 6,
            total: 9,
            boysPercentum: '33%',
            girlsPercentum: '67%'
          }
        ],
        healthExaminationItem: [
          {
            type: 'B超室',
            projectName: '妇科腹部彩超常规检查',
            boys: 5,
            girls: 8,
            total: 14,
            boysPercentum: '50%',
            girlsPercentum: '50%'
          }
        ],
        // 疾病名单
        illnessList: [
          {
            illnessName: '血尿酸偏高',
            peopleNum: 50,
            percentage: '22.22%',
            suggest:
              '血尿酸是检查痛风的一种方式，痛风是体内代谢异常的疾病，使血液里尿酸的浓度过高，过多的尿酸与钠结合形成尿酸钠盐结晶。(2)尿酸钠盐结晶沉积在关节、肌腱、肾脏等处。一旦发病即会表现红、肿、发热、极端疼痛，冬天是痛风的好发季节。',
            personnelList: [
              {
                regNo: '201113010013',
                name: '梁曼华',
                sex: '女',
                age: '20',
                department: '南沙支行'
              },
              {
                regNo: '201113010013',
                name: '梁曼华',
                sex: '女',
                age: '20',
                department: '南沙支行'
              }
            ]
          },
          {
            illnessName: '血尿酸偏高',
            peopleNum: 50,
            percentage: '22.22%',
            suggest:
              '血尿酸是检查痛风的一种方式，痛风是体内代谢异常的疾病，使血液里尿酸的浓度过高，过多的尿酸与钠结合形成尿酸钠盐结晶。(2)尿酸钠盐结晶沉积在关节、肌腱、肾脏等处。一旦发病即会表现红、肿、发热、极端疼痛，冬天是痛风的好发季节。',
            personnelList: [
              {
                regNo: '201113010013',
                name: '梁曼华',
                sex: '女',
                age: '20',
                department: '南沙支行'
              },
              {
                regNo: '201113010013',
                name: '梁曼华',
                sex: '女',
                age: '20',
                department: '南沙支行'
              }
            ]
          }
        ],
        // 疾病人数与比例
        numberDiseases: [
          {
            illnessName: '血尿酸偏高',
            abnormalNum: 2,
            totalNum: 9,
            percentum: '22.22%'
          }
        ],
        NumDiseasesEchart: {
          xAxisData: [
            '血尿酸偏高',
            '血脂异常',
            '血糖偏高',
            '肝囊肿',
            '子宫肌瘤',
            '超重',
            '前列腺钙化',
            '阴道炎'
          ],
          seriesData: [120, 132, 101, 134, 90, 230, 210, 100]
        },
        familiarIllnessPercentage: {
          xAxisData: [
            '血尿酸偏高',
            '血脂异常',
            '血糖偏高',
            '肝囊肿',
            '子宫肌瘤',
            '超重',
            '前列腺钙化',
            '阴道炎'
          ],
          seriesData: [120, 132, 101, 134, 90, 230, 210, 100]
        },
        ageDistribution: [
          {
            illnessName: '血尿酸偏高',
            age_1: 2,
            age_2: 0,
            age_3: 5,
            age_4: 8,
            age_5: 0,
            age_6: 0,
            age_7: 8,
            total: 10,
            percentum: '22%'
          }
        ],
        sickenRatio: [
          {
            sex: '男',
            sickenNum: 2,
            finishNum: 3,
            percentum: '33%'
          },
          {
            sex: '女',
            sickenNum: 4,
            finishNum: 3,
            percentum: '33%'
          }
        ],
        morbidityAge: {
          xAxisData: [
            '0-24',
            '25-34',
            '35-44',
            '45-54',
            '55-64',
            '65-100',
            '其他'
          ],
          seriesData: [
            [1, 2, 3, 11, 5, 5, 7],
            [1, 8, 10, 2, 5, 7, 4],
            [1, 6, 2, 12, 5, 2, 3],
            [1, 3, 5, 3, 5, 1, 9],
            [1, 9, 7, 6, 5, 3, 2],
            [1, 20, 5, 8, 5, 6, 8],
            [1, 11, 7, 15, 5, 6, 1]
          ]
        },
        contrastAnalyse: {
          xAxisData: [
            '血尿酸偏高',
            '血脂异常',
            '血糖偏高',
            '肝囊肿',
            '子宫肌瘤',
            '超重',
            '前列腺钙化',
            '阴道炎'
          ],
          seriesData: [120, 132, 101, 134, 90, 230, 210, 100]
        },
        suggestList: [
          {
            illnessName: '血脂异常',
            suggest: '(1)考虑血脂异常，建议低动物脂肪、低糖、低盐饮食，'
          },
          {
            illnessName: '血尿酸偏高',
            suggest: '(1)考虑血脂异常，建议低动物脂'
          }
        ],
        // 综述和建议
        resultList: [
          {
            regNo: '201113010001',
            name: '邓丽章',
            sex: '女',
            age: 20,
            department: '南沙支行',
            jobnum: '123456',
            // 综述
            overview: [
              {
                title: '一般健康体检(通用)（内科）:',
                val: '身高体重指数20.74：正常;内科检查:未见明显异常。'
              }
            ],
            // 建议
            suggest: [
              {
                title: '一般健康体检(通用)（内科）:',
                val: '身高体重指数20.74：正常;内科检查:未见明显异常。'
              }
            ]
          }
        ]
      },
      theads_1: {
        type: '类型',
        boys: '男孩',
        girls: '女孩',
        total: '总数',
        boysPercentum: '男百分比',
        girlsPercentum: '女百分比'
      },
      theads_2: {
        type: '检查科室',
        projectName: '项目名称',
        boys: '男',
        girls: '女',
        total: '合计',
        boysPercentum: '男百分比',
        girlsPercentum: '女百分比'
      },
      columnWidth_2: {
        type: 100,
        boys: 60,
        girls: 60,
        total: 70,
        boysPercentum: 70,
        girlsPercentum: 70
      },
      theads_3: {
        regNo: '体检单号',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        department: '部门'
      },
      theads_4: {
        illnessName: '疾病名称',
        abnormalNum: '异常人数',
        totalNum: '总人数',
        percentum: '年龄'
      },
      theads_5: {
        illnessName: '疾病名称',
        age_1: '0-24',
        age_2: '25-34',
        age_3: '35-44',
        age_4: '45-54',
        age_5: '55-64',
        age_6: '65-74',
        age_7: '>=75',
        total: '合计',
        percentum: '百分比'
      },
      theads_6: {
        sex: '性别',
        sickenNum: '患病人数',
        finishNum: '体检完成人数',
        percentum: '疾患百分比'
      }
    };
  },
  methods: {
    echartsFun(id, title, xAxisData, seriesData, pie = false) {
      var chartDom = document.getElementById(id);
      var myChart = echarts.init(chartDom);
      var option;

      option = {
        title: {
          text: title
        },
        tooltip: {
          trigger: pie ? 'item' : 'axis'
        },
        legend: {
          left: 'right'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        toolbox: {},
        xAxis: pie || {
          type: 'category',
          // boundaryGap: false,
          axisTick: {
            alignWithLabel: true
          },
          data: xAxisData
        },
        yAxis: pie || {
          type: 'value'
        },
        series: seriesData
      };

      option && myChart.setOption(option);
    }
  },
  mounted() {
    let seriesData_1 = [
      {
        name: '异常人数',
        type: 'line',
        stack: 'Total',
        data: this.reportData.NumDiseasesEchart.seriesData
      }
    ];
    this.echartsFun(
      'NumDiseasesEchart',
      '疾病患病人数',
      this.reportData.NumDiseasesEchart.xAxisData,
      seriesData_1
    );

    let seriesData_2 = [
      {
        name: '异常人数',
        type: 'bar',
        stack: 'Total',
        barWidth: 20,
        label: {
          show: true,
          position: 'top',
          formatter: '{c}%'
        },
        itemStyle: {
          color: '#D63031'
        },
        data: this.reportData.familiarIllnessPercentage.seriesData
      }
    ];
    this.echartsFun(
      'FamiliarIllnessPercentage',
      '单位高发病对比分析图',
      this.reportData.familiarIllnessPercentage.xAxisData,
      seriesData_2
    );

    // 男女患病比例
    let seriesData_3 = [
      {
        name: '',
        type: 'pie',
        radius: '70%',
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 4
        },
        data: [
          {
            value: this.reportData.sickenRatio[0].sickenNum,
            name: '男',
            itemStyle: {
              color: '#1770DF'
            }
          },
          {
            value: this.reportData.sickenRatio[1].sickenNum,
            name: '女',
            itemStyle: {
              color: '#FF8384'
            }
          }
        ],
        label: {
          position: 'inner',
          formatter: '{b}\n\n{d}%'
        }
      }
    ];
    this.echartsFun(
      'SickenRatio',
      '男女患病比例',
      this.reportData.familiarIllnessPercentage.xAxisData,
      seriesData_3,
      true
    );

    // 高发病年龄分布图
    let seriesData_4 = [];
    this.reportData.morbidityAge.xAxisData.map((item, idx) => {
      seriesData_4.push({
        name: item,
        type: 'line',
        data: this.reportData.morbidityAge.seriesData[idx]
      });
    });
    this.echartsFun(
      'MorbidityAge',
      '高发病年龄分布图',
      this.reportData.morbidityAge.xAxisData,
      seriesData_4
    );

    // 单位高发病对比分析图
    let seriesData_5 = [
      {
        name: '异常人数',
        type: 'bar',
        stack: 'Total',
        barWidth: 20,
        label: {
          show: true,
          position: 'top',
          formatter: '{c}人'
        },
        // itemStyle: {
        //     color: '#D63031'
        // },
        data: this.reportData.contrastAnalyse.seriesData
      }
    ];
    this.echartsFun(
      'ContrastAnalyse',
      '单位高发病对比分析图',
      this.reportData.contrastAnalyse.xAxisData,
      seriesData_5
    );
  }
};
