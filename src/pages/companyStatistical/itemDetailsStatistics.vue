<template>
  <!-- 体检单位项目工作量统计表 -->
  <div class="itemDetailsStatistics" id="itemDetailsStatistics">
    <div class="itemDetailsStatistics-wrap">
      <div class="header">
        <div class="header-item">
          <span>统计时间</span>
          <el-date-picker
            :picker-options="{ shortcuts: G_datePickerShortcuts }"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
            v-model="statisticalTime"
            @change="search"
            :clearable="false"
          >
          </el-date-picker>
        </div>
        <div class="header-item">
          <span style="width: 30px">单位</span>
          <el-cascader
            ref="company_cascader_ref"
            v-model="searchForm.companyCode"
            :filter-method="filterMethod"
            :options="companyList"
            :props="{ multiple: false }"
            clearable
            filterable
            size="small"
            collapse-tags
            @change="companyChange"
          >
          </el-cascader>
        </div>
        <div class="header-item">
          <ButtonGroup
            :btnList="['统计', '打印', '导出']"
            @statistics="search"
            @prints="print"
            @exports="exportTable"
          ></ButtonGroup>
        </div>
      </div>
      <div id="printDiv">
        <div class="titlewrap">体检单位项目工作量统计表:</div>
        <div class="contDiv" id="contDiv">
          <p class="titleP">
            <span>体检单位:{{ companyName ? companyName : '无单位' }}</span>
          </p>
          <div class="tableDiv">
            <div class="table">
              <PublicTable
                :isSortShow="false"
                :viewTableList.sync="tableData"
                :theads.sync="theads"
              >
              </PublicTable>
            </div>
            <div class="spanDiv footerr">
              <span>{{ summation.combName }}:</span>
              <span>{{ summation.amount }}元</span>
              <span>{{ summation.checkedTimes }}人</span>
              <span>{{ summation.noCheckedTimes }}人</span>
              <span>{{ summation.totalTimes }}次</span>
            </div>
          </div>
        </div>
      </div>
      <!-- 打印的表格 -->
      <div class="print_table" id="printHtml">
        <p style="text-align: center; padding: 20px 0; font-size: 18px">
          {{ companyName ? companyName : '无单位' }}体检项目工作量统计表
        </p>
        <PublicTable :viewTableList.sync="printData" :theads.sync="theads">
        </PublicTable>
      </div>
    </div>
  </div>
</template>
<script>
import PublicTable from '@/components/publicTable.vue';
import ButtonGroup from './components/buttonGroup.vue';
import { dataUtils } from '@/common';
import { export2Excel } from '@/common/excelUtil';
import moment from 'moment';
import printJS from '@/common/printJS';
import { mapGetters } from 'vuex';
export default {
  name: 'itemDetailsStatistics',
  components: { PublicTable, ButtonGroup },
  data() {
    return {
      searchForm: {
        beginDate: '',
        endDate: '',
        companyCode: ''
      },
      companyName: '',
      statisticalTime: [dataUtils.getDate(), dataUtils.getDate()],
      companyList: [],
      theads: {
        combName: '体检项目组合',
        amount: '体检费用',
        checkedTimes: '已检查',
        noCheckedTimes: '未检查',
        totalTimes: '开单数'
      },
      tableData: [],
      summation: {
        combName: '合计',
        amount: 0,
        checkedTimes: 0,
        noCheckedTimes: 0,
        totalTimes: 0
      },
      printData: []
    };
  },
  computed: {
    ...mapGetters(['G_datePickerShortcuts'])
  },
  mounted() {
    this.getCompany();
  },
  methods: {
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    //获取单位下拉
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) return;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.statisticalTime = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.search();
    },
    //统计查询
    search() {
      if (this.searchForm.companyCode === '') {
        this.$message({
          message: '请选择单位！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (!this.statisticalTime) {
        this.searchForm.beginDate = '';
        this.searchForm.endDate = '';
      } else {
        this.searchForm.beginDate = this.statisticalTime[0];
        this.searchForm.endDate = this.statisticalTime[1];
      }
      let { companyCode, ...surplus } = this.searchForm;
      let parameter = {
        companyCode: companyCode[0],
        ...surplus
      };
      let temp = this.companyList.find(
        (item) => item.value === this.searchForm.companyCode[0]
      )?.label;
      this.companyName = temp;
      this.$ajax
        .post(this.$apiUrls.GetCompanyCombAmount, parameter)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.printData = returnData?.companyCombAmount || [];
          if (returnData?.companyCombAmount?.length >= 1) {
            this.summation =
              returnData?.companyCombAmount[
                returnData?.companyCombAmount?.length - 1
              ];
            let newData = returnData.companyCombAmount.slice(0, -1);
            console.log('🚀 ~ search ~ newData:', newData);

            this.tableData = newData || [];
            console.log('[  this.summation ]-144', this.summation);
            this.$message({
              message: '成功!',
              type: 'success',
              showClose: true
            });
          } else {
            this.tableData = [];
            this.summation = {
              combName: '合计',
              amount: 0,
              totalTimes: 0
            };
            this.$message({
              message: '暂无数据!',
              type: 'success',
              showClose: true
            });
          }
        });
    },
    // 导出excel
    exportTable() {
      if (this.printData.length == 0) {
        this.$message.warning('请选择至少一条数据进行操作!');
        return;
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const columns = [
          {
            title: '序号',
            key: 'index'
          },
          {
            title: '体检项目组合',
            key: 'combName'
          },
          {
            title: '已检查',
            key: 'checkedTimes'
          },
          {
            title: '未检查',
            key: 'noCheckedTimes'
          },
          {
            title: '开单数',
            key: 'totalTimes'
          }
        ];
        this.printData.map((item, i) => {
          item.index = i + 1;
        });
        const title =
          this.companyName + '项目工作量统计表' + moment().format('YYYY-MM-DD');
        this.$nextTick(() => {
          export2Excel(columns, this.printData, title);
        });
      });
    },
    //打印
    print() {
      if (this.printData.length == 0) {
        return this.$message({
          message: '请至少选择一条数据进行打印！',
          type: 'warning',
          showClose: true
        });
      }

      setTimeout(() => {
        printJS('printHtml');
      }, 500);
    }
  }
};
</script>
<style lang="less" scoped>
.itemDetailsStatistics {
  color: #2d3436;
  display: flex;
  flex-direction: column;
  .itemDetailsStatistics-wrap {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    background: #fff;
    padding: 10px;
    font-size: 14px;
  }
  .header {
    display: flex;
    align-items: center;
    font-size: 14px;
    margin-bottom: 10px;
  }
  .header-item {
    display: flex;
    align-items: center;
    margin-right: 10px;
    span {
      font-weight: 600;
      width: 56px;
      margin-right: 10px;
    }
  }
  #printDiv {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    .titlewrap {
      font-size: 18px;
      font-weight: 600;
    }
    .contDiv {
      flex: 1;
      overflow: auto;
      display: flex;
      flex-direction: column;
      .titleP {
        color: #1770df;
        font-weight: 600;
        margin: 10px 0;
      }
      .tableDiv {
        flex: 1;
        overflow: auto;
        border: 1px solid #ccc;
        border-radius: 4px;
        display: flex;
        flex-direction: column;
      }
    }
    .spanDiv {
      width: 100%;
      display: flex;
      padding: 10px;
      & > span {
        flex: 1;
      }
    }
    .footerr {
      background: #cbddf5;
      font-weight: 600;
      // color: #1770df;
    }
  }
  .table {
    flex: 1;
    overflow: auto;
  }
  .print_table {
    display: none;
  }
}
</style>
