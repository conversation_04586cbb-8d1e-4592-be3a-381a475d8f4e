<template>
  <!-- 套餐登记信息统计表 -->
  <div class="packageRegister">
    <div class="main">
      <div class="searchBar">
        <h3>套餐登记信息统计表:</h3>
        <div class="search-list">
          <div class="list-item">
            <span style="width: 56px">统计时间</span>
            <el-date-picker
              :picker-options="{ shortcuts: G_datePickerShortcuts }"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              v-model="searchInfo.date"
              @change="statistics"
              :clearable="false"
            >
            </el-date-picker>
          </div>
          <div class="list-item">
            <span style="width: 36px">套餐</span>
            <el-select
              placeholder="请选择"
              size="small"
              filterable
              clearable
              v-model="searchInfo.clusCode"
              class="input"
              @change="statistics"
            >
              <el-option
                v-for="item in clusterList"
                :key="item.clusCode"
                :label="item.clusName"
                :value="item.clusCode"
              >
              </el-option>
            </el-select>
          </div>
          <ButtonGroup
            :btnList="['统计', '打印', '导出']"
            @statistics="statistics"
            @exports="exports"
          />
        </div>
      </div>
      <div class="main-table">
        <div class="table">
          <PublicTable
            :isSortShow="false"
            :theads="theads"
            :viewTableList.sync="tableData"
          ></PublicTable>
        </div>
        <div class="total">
          <span>合计</span>
          <span>&nbsp;</span>
          <span>&nbsp;</span>
          <span>{{ totalInfo.totalPersonCount }}</span>
          <span>{{ totalInfo.totalAmount }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ButtonGroup from './components/buttonGroup.vue';
import PublicTable from '@/components/publicTable.vue';
import { dataUtils } from '@/common';
import ExportExcel from '@/common/excel/exportExcel';
import { mapGetters } from 'vuex';
export default {
  name: 'packageRegister',
  mixins: [ExportExcel],
  components: {
    ButtonGroup,
    PublicTable
  },
  data() {
    return {
      searchInfo: {
        date: [new Date(), new Date()],
        clusCode: ''
      },
      theads: {
        clusCode: '套餐代码',
        clusName: '套餐名称',
        price: '单价',
        personCount: '人次',
        totalAmount: '总金额'
      },
      tableData: [],
      totalInfo: {
        totalPersonCount: 0,
        totalAmount: 0
      },
      clusterList: []
    };
  },
  computed: {
    ...mapGetters(['G_datePickerShortcuts'])
  },
  created() {
    this.getCluster();
  },
  methods: {
    // 获取套餐
    getCluster() {
      this.$ajax.post(this.$apiUrls.Cluster).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.clusterList = returnData;
      });
    },
    // 查询
    statistics() {
      let [beginDate, endDate] = this.searchInfo.date;
      let data = {
        beginDate: dataUtils.dateToString(beginDate),
        endDate: dataUtils.dateToString(endDate),
        clusCode: this.searchInfo.clusCode
      };
      this.$ajax.post(this.$apiUrls.PackageInfoReport, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.tableData = returnData.packageInfos;
        this.totalInfo.totalPersonCount = returnData.totalPersonCount;
        this.totalInfo.totalAmount = returnData.totalAmount;
      });
    },
    // 导出
    exports() {
      if (this.tableData.length === 0) {
        this.$message({
          message: '套餐登记信息统计表不能为空！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let text = `人次：${this.totalInfo.totalPersonCount}    总金额：${this.totalInfo.totalAmount}`;
      this.exportExcel(
        '套餐登记信息统计表',
        '套餐登记信息统计表',
        this.theads,
        this.tableData,
        text
      );
    }
  }
};
</script>

<style lang="less" scoped>
.packageRegister {
  color: #2d3436;
  .main {
    height: 100%;
    background: #fff;
    padding: 18px 10px;
    display: flex;
    flex-direction: column;
  }
  .searchBar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    h3 {
      font-size: 18px;
    }
  }
  .search-list {
    display: flex;
    align-items: center;
  }
  .list-item {
    display: flex;
    align-items: center;
    margin-right: 10px;
    &:last-child {
      margin-right: 0;
    }
    span {
      font-size: 14px;
      font-weight: 600;
      margin-right: 10px;
    }
  }
  .input {
    width: 100%;
  }
  .main-table {
    border: 1px solid rgba(178, 190, 195, 0.5);
    border-radius: 4px;
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .table {
    flex: 1;
    overflow: auto;
  }
  .total {
    background: #cbddf5;
    font-size: 14px;
    font-weight: 600;
    padding: 10px;
    display: flex;
    align-items: center;
    span {
      flex: 1;
      &:nth-child(4) {
        padding-left: 30px;
      }
      &:nth-child(5) {
        padding-left: 8px;
      }
    }
  }
}
</style>
