<!--
 * @FilePath: \KrPeis\src\pages\companyStatistical\companyDetialCombPriceReport.vue
 * @Description: 单位人员组合费用明细报表
 * @Author: justin
 * @Date: 2024-03-22 09:17:40
 * @Version: 0.0.1
 * @LastEditors: key
 * @LastEditTime: 2024-08-19 16:55:52
*
-->

<template>
  <div class="container">
    <div class="header-wrapper">
      <el-form
        :model="searchInfo"
        status-icon
        :rules="rules"
        :ref="refSearchForm"
        :inline="true"
      >
        <el-row type="flex" align="middle">
          <el-col :span="24">
            <el-form-item prop="timeType">
              <el-select
                placeholder="请选择"
                size="small"
                v-model="searchInfo.timeType"
                class="input"
                @change="statistics"
                style="width: 110px"
              >
                <el-option label="体检时间" :value="1"></el-option>
                <el-option label="登记时间" :value="2"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item prop="dateRange">
              <el-date-picker
                :picker-options="{ shortcuts: G_datePickerShortcuts }"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                size="small"
                v-model="searchInfo.dateRange"
                class="input"
                style="width: 250px"
              >
              </el-date-picker>
            </el-form-item>

            <el-form-item label="单位" prop="companyCode">
              <el-cascader
                ref="cascader_ref"
                v-model="companyCasList"
                :filter-method="filterMethod"
                :options="companyList"
                :props="{ multiple: false }"
                clearable
                filterable
                size="small"
                collapse-tags
                @change="companyChange"
                style="width: 400px"
              >
              </el-cascader>
            </el-form-item>
            <el-form-item label="部门" prop="companyDeptCode">
              <el-select
                class="select"
                v-model.trim="searchInfo.companyDeptCode"
                placeholder="请选择"
                size="small"
                filterable
                clearable
                @change="statistics"
              >
                <el-option
                  v-for="(item, index) in companyDeptList"
                  :key="index"
                  :label="item.deptName"
                  :value="item.deptCode"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="套餐" prop="clusterCode">
              <el-select
                placeholder="请选择"
                size="small"
                filterable
                clearable
                v-model="searchInfo.clusterCode"
                class="input"
                @change="statistics"
              >
                <el-option
                  v-for="item in clusterList"
                  :key="item.clusCode"
                  :label="item.clusName"
                  :value="item.clusCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row type="flex" align="middle">
          <el-col :span="20">
            <el-form-item label="" prop="type">
              <el-radio v-model="searchInfo.type" :label="0">全部组合</el-radio>
              <el-radio v-model="searchInfo.type" :label="1">实际组合</el-radio>
            </el-form-item>
          </el-col>

          <el-col :span="4" :offset="6">
            <ButtonGroup
              :btnList="['统计', '导出']"
              @statistics="statistics"
              @prints="prints"
              @exports="exports"
              :loadingStatistics="loadingStatistics"
            />
          </el-col>
        </el-row>
      </el-form>

      <h3>单位人员组合费用明细报表：</h3>
    </div>

    <div class="body-wrapper">
      <!-- <el-table
        ref="tableCom_Ref"
        style="width: 100%; color: #2d3436; font-weight: 600; font-size: 14px"
        size="small"
        :data="tableData"
        v-el-table-infinite-scroll="load"
        :header-cell-style="{
          background: '#d1e2f9',
          fontSize: '14px',
          color: '#2d3436',
        }"
        height="100%"
        highlight-current-row
      >
        <el-table-column type="selection" width="45" fixed></el-table-column>
        <el-table-column
          :prop="key"
          :label="value"
          v-for="([key, value], index) of thead"
          :key="key"
          :fixed="index <= 4"
          :min-width="$options.filters.setTableColWidth(index)"
        >
        </el-table-column>
      </el-table> -->
      <vxe-table
        show-overflow
        highlight-hover-ro
        ref="tableCom_Ref"
        show-footer
        :height="'100%'"
        style="width: 100%; color: #2d3436; font-weight: 600; font-size: 14px"
        :sort-config="{ trigger: 'cell' }"
        :data="tableData"
        :header-cell-class-name="headerCellClassName"
        :footer-method="footerMethod"
        :row-config="{ isHover: true }"
        :scroll-y="{ enabled: true }"
      >
        >
        <vxe-table-column
          type="checkbox"
          fixed="left"
          width="45"
        ></vxe-table-column>
        <vxe-table-column
          :field="key"
          :title="value"
          v-for="([key, value], index) of thead"
          :key="key"
          :fixed="index <= 4 ? 'left' : ''"
          :min-width="$options.filters.setTableColWidth(index)"
        >
        </vxe-table-column>
      </vxe-table>
    </div>
  </div>
</template>

<script>
import ButtonGroup from './components/buttonGroup.vue';
import { dataUtils } from '@/common/dataUtils';
import { mapGetters } from 'vuex';
export default {
  name: 'companyDetialCombPriceReport',
  components: {
    ButtonGroup
  },
  data() {
    return {
      lastItem: [],
      searchInfo: {
        timeType: 1,
        dateRange: [dataUtils.getDate(), dataUtils.getDate()], // 体检时间
        companyCode: '', // 单位代码
        companyTimes: '', // 单位次数
        type: 0, // 0 按全部组合 1 按实际组合
        clusterCode: '', // 套餐代码
        companyDeptCode: ''
      },
      companyDeptList: [],
      rules: {
        dateRange: [
          {
            type: 'array',
            required: true,
            message: '请选择体检日期'
          }
        ],
        companyCode: [
          {
            required: true,
            message: '请选择一个单位及其次数',
            trigger: 'change'
          }
        ]
      },
      thead: {},
      tableData: [],
      excelData: [],
      pageSize: 50,
      currentPage: 1,
      clusterList: [],
      fixed_clusterList: [],
      refSearchForm: 'searchForm',
      checkTableList: [],
      loadingStatistics: false,
      companyList: [],
      companyCasList: []
    };
  },
  computed: {
    ...mapGetters(['G_datePickerShortcuts'])
  },
  created() {
    this.getCompanyWithTimesList();
    this.getClusterList();
    this.thead = new Map();
    this.thead.set('regNo', '体检号');
    this.thead.set('name', '姓名');
    this.thead.set('sex', '性别');
    this.thead.set('age', '年龄');
    this.thead.set('clusterName', '体检套餐');
  },
  beforeUpdate() {
    // this.$nextTick(() => {
    //   // 在数据加载完，重新渲染表格，解决表格抖动
    //   this.$refs.tableCom_Ref.doLayout();
    // });
  },
  methods: {
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    /**
     * @description: 获取单位及次数
     * @return {*}
     */
    getCompanyWithTimesList() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },

    /**
     * @author: justin
     * @description: 单位选择器内容改变回调事件
     * @param {*} val
     * @return {*}
     */
    companyChange(val) {
      const that = this;
      this.getDepartList();
      this.companyTimesChange();
      this.searchInfo.clusterCode = '';
      this.searchInfo.companyDeptCode = '';
      if (!val || val.length === 0) {
        that.searchInfo.companyCode = '';
        that.searchInfo.companyTimes = null;
        this.companyDeptList = [];
        that.setDateRangeByCompanyTimes();
        return;
      }

      that.searchInfo.companyCode = that.companyCasList[0];
      that.searchInfo.companyTimes = that.companyCasList[1];
      that.setDateRangeByCompanyTimes();
      that.statistics();
    },

    /**
     * @author: justin
     * @description: 根据单位体检次数所在时间，设置日期范围
     * @return {*}
     */
    setDateRangeByCompanyTimes() {
      if (
        !this.searchInfo.companyCode ||
        !this.searchInfo.companyTimes ||
        this.searchInfo.companyTimes.length == 0
      ) {
        this.searchInfo.dateRange = [dataUtils.getDate(), dataUtils.getDate()];
        return;
      }

      const company = this.companyList.find(
        (x) => x.value == this.searchInfo.companyCode
      );
      if (!company) return;

      let dateArr = [];
      company.children
        .filter((x) => this.searchInfo.companyTimes == x.value)
        .forEach((x) => {
          dateArr.push(new Date(x.item.beginDate || Date())?.getTime());
          dateArr.push(new Date(x.item.endDate || Date())?.getTime());
        });
      if (dateArr.length == 0) return;

      const minDate = new Date(Math.min(...dateArr));
      const maxDate = new Date(Math.max(...dateArr));
      this.searchInfo.dateRange = [minDate, maxDate];
    },

    /**
     * @description: 获取套餐下拉内容
     * @return {*}
     */
    getClusterList() {
      this.$ajax.post(this.$apiUrls.Cluster).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.clusterList = returnData;
        this.fixed_clusterList = dataUtils.deepCopy(returnData);
      });
    },

    /**
     * @description: 获取单位人员组合费用明细报表（交叉报表）
     * @return {*}
     */
    statistics() {
      this.$refs[this.refSearchForm].validate((valid) => {
        if (!valid) return;

        this.loadingStatistics = true;
        let [startTime, endTime] = this.searchInfo.dateRange;
        let data = {
          beginDate: dataUtils.dateToStrStart(startTime),
          endDate: dataUtils.dateToStrEnd(endTime),
          companyCode: this.searchInfo.companyCode,
          companyTimes: this.searchInfo.companyTimes,
          type: this.searchInfo.type,
          clusterCode: this.searchInfo.clusterCode,
          timeType: this.searchInfo.timeType,
          companyDeptCode: this.searchInfo.companyDeptCode
        };
        this.$ajax
          .post(this.$apiUrls.QueryCompanyDetialCombPrice, data)
          .then((r) => {
            let { success, returnData } = r.data;
            if (!success) return;
            if (!returnData) return;
            for (const key in returnData.header || {}) {
              this.thead.set(key, returnData.header[key]);
            }
            let { bodyData } = returnData;
            if (!bodyData || bodyData?.length === 0)
              this.tableData = bodyData || [];
            else {
              this.lastItem = bodyData[bodyData.length - 1];
              this.tableData = bodyData.slice(0, bodyData.length - 1);
            }
          })
          .finally(() => {
            this.loadingStatistics = false;
          });
      });
    },

    /**
     * @description: 打印
     * @return {*}
     */
    prints() {
      //todo
    },

    /**
     * @description: 导出
     * @return {*}
     */
    exports() {
      let tableComRef = this.$refs.tableCom_Ref.getCheckboxRecords();

      if (tableComRef.length == 0) {
        return this.$message({
          message: '请选择至少一条数据进行操作！',
          type: 'warning',
          showClose: true
        });
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.excelData = [...tableComRef, this.lastItem]; // multipleSelection是一个数组，存储表格中选择的行的数据。
          this.excelData.map((item, i) => {
            item.index = i + 1;
          });
          this.$nextTick(function () {
            this.export2Excel();
          });
        })
        .catch(() => {});
    },

    /**
     * @description: 数据写入excel
     * @return {*}
     */
    export2Excel() {
      var that = this;
      require.ensure([], () => {
        const { export_json_to_excel } = require('@/utils/Export2Excel'); // 这里必须使用绝对路径，使用@/+存放export2Excel的路径
        let tHeader = []; // 导出的表头名信息
        let filterVal = []; // 导出的表头字段名
        for (let [key, value] of that.thead) {
          tHeader.push(value);
          filterVal.push(key);
        }
        const list = that.excelData;
        const data = that.formatJson(filterVal, list);
        const name = '单位人员组合费用明细报表';
        export_json_to_excel(tHeader, data, name);
      });
    },

    /**
     * @description: 格式转换
     * @param {*} filterVal
     * @param {*} jsonData
     * @return {*}
     */
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) => filterVal.map((j) => v[j]));
    },

    /**
     * @author: justin
     * @description: 滚动加载
     * @return {*}
     */
    // load() {
    //   console.log("加载表格分页");
    //   this.currentPage++;
    //   // if (this.currentPage * this.pageSize >= this.viewTableList.length) return;
    //   let loadSize = this.currentPage * this.pageSize;
    //   if (loadSize - this.tableData.length >= this.pageSize) return;
    //   let start = (this.currentPage - 1) * this.pageSize;
    //   let end = this.currentPage * this.pageSize;
    //   let pageData = this.tableData.slice(start, end);
    //   console.log(pageData);
    //   this.tableData.push(...pageData);
    //   console.log(this.tableData);

    //   let tableRef = this.$refs.tableCom_Ref;
    //   console.log(tableRef.store.states.isAllSelected);

    //   this.defaultCheckRow(pageData);
    //   console.log(tableRef.selection);
    //   if (tableRef.store.states.isAllSelected) {
    //     tableRef.toggleAllSelection();
    //   }
    // },
    //次数改变时
    companyTimesChange() {
      if (!this.companyCasList[1]) {
        this.clusterList = this.fixed_clusterList;
        return;
      }
      this.$ajax
        .post(this.$apiUrls.ReadCompanyCandidateCluster, '', {
          query: {
            companyCode: this.companyCasList[0],
            companyTimes: this.companyCasList[1]
          }
        })
        .then(async (r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.clusterList = returnData || [];
        });
    },
    //查询公司部门信息
    getDepartList() {
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanyDepartment, {
          companyCode: this.companyCasList[0],
          deptCode: '',
          deptName: ''
        })
        .then((r) => {
          //console.log("r", r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.companyDeptList = returnData || [];
        });
    },
    headerCellClassName({ $columnIndex }) {
      if ($columnIndex === 0) {
        return 'col-zero';
      }
      return 'col-box';
    },
    footerMethod({ columns, data }) {
      // 返回一个二维数组的表尾合计
      if (data.length === 0) return [];
      return [
        columns.map((column, columnIndex) => {
          if (columnIndex === 1) {
            return '总价';
          } else if (columnIndex <= 5) {
            return '';
          } else {
            return this.lastItem[column.field];
          }
        })
      ];
    }
  },
  watch: {
    /**
     * @author: justin
     * @description: viewTableList
     * @param {*} n
     * @param {*} o
     * @return {*}
     */
    viewTableList(n, o) {
      this.tableData = [];
      this.currentPage = 1;
      this.$nextTick(() => {
        if (!this.isOpenPage) {
          this.tableData = n;
          return;
        }
        if (this.currentPage * this.pageSize >= n.length) {
          this.tableData.push(...n);
          return;
        }
        let start = (this.currentPage - 1) * this.pageSize;
        let end = this.currentPage * this.pageSize;
        let pageData = n.slice(start, end);
        console.log(pageData);
        this.tableData.push(...pageData);
      });
    }
  },
  filters: {
    /**
     * @author: justin
     * @description: 设置表格列宽
     * @return {*}
     */
    setTableColWidth: (index) => {
      switch (index) {
        case 0:
        case 1:
        case 4:
          return 130;

        case 2:
        case 3:
          return 80;

        default:
          return 250;
      }
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .col-box {
  background-color: #d1e2f9;
  font-size: 14px;
  color: #2d3436;
}
/deep/ .col-zero {
  background-color: #d1e2f9;
  .vxe-cell--title {
    background: #f5f5f5;
  }
  font-size: 14px;
  color: #2d3436;
}
.container {
  color: #2d3436;
  background: #fff;
  display: flex;
  flex-flow: column;
  padding: 0 10px !important;

  .header-wrapper {
    width: 100%;
    h3 {
      font-size: 18px;
      padding: 5px 0 10px 0;
    }
    /deep/ .el-form-item {
      margin-bottom: unset !important;

      .el-form-item__label {
        font-size: 14px;
        font-weight: 600;
      }
    }
  }

  .body-wrapper {
    width: 100%;
    display: flex;
    flex: 1;
    border: 1px solid rgba(178, 190, 195, 0.5);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 20px;
  }
}
</style>
