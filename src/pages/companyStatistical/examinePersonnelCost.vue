<template>
  <!-- 体检人员费用列表 -->
  <div class="examinePersonnelCost">
    <div class="main">
      <div class="searchBar">
        <div class="search-list">
          <div class="list-item" style="width: 350px">
            <div style="width: 160px; margin-right: 10px">
              <el-select
                placeholder="请选择"
                size="small"
                v-model="searchInfo.timeType"
                class="input"
              >
                <el-option label="体检时间" :value="1"></el-option>
                <el-option label="登记时间" :value="2"></el-option>
              </el-select>
            </div>
            <el-date-picker
              :picker-options="{ shortcuts: G_datePickerShortcuts }"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              v-model="searchInfo.date"
              :clearable="false"
              value-format="yyyy-MM-dd"
              @change="statistics"
            >
            </el-date-picker>
          </div>
          <div class="list-item">
            <el-radio-group v-model="searchInfo.peType" @change="radioChange">
              <el-radio :label="0">所有</el-radio>
              <el-radio :label="1">个人</el-radio>
              <el-radio :label="2">团体</el-radio>
            </el-radio-group>
          </div>
          <div class="list-item">
            <span style="width: 36px">单位</span>
            <el-cascader
              ref="company_cascader_ref"
              v-model="searchInfo.companyCode"
              :filter-method="filterMethod"
              :options="companyList"
              :props="{ multiple: false }"
              clearable
              filterable
              size="small"
              collapse-tags
              @change="companyChange"
            >
            </el-cascader>
          </div>
          <div class="list-item">
            <span style="width: 36px">部门</span>
            <el-select
              class="select"
              v-model.trim="searchInfo.companyDeptCode"
              placeholder="请选择"
              size="small"
              filterable
              clearable
              :disabled="isHave"
              @change="statistics"
            >
              <el-option
                v-for="(item, index) in companyDeptList"
                :key="index"
                :label="item.deptName"
                :value="item.deptCode"
              ></el-option>
            </el-select>
          </div>
          <div class="list-item">
            <span style="width: 36px">套餐</span>
            <el-select
              placeholder="请选择"
              size="small"
              filterable
              clearable
              v-model="searchInfo.clusCode"
              class="input"
              @change="statistics"
            >
              <el-option
                v-for="item in clusterList"
                :key="item.clusCode"
                :label="item.clusName"
                :value="item.clusCode"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="search-list">
          <div class="list-item" v-if="G_config.physicalMode.includes('普检')">
            <span style="width: 72px">体检分类</span>
            <el-select
              placeholder="请选择"
              size="small"
              clearable
              v-model="searchInfo.peCls"
              class="input"
            >
              <el-option
                v-for="item in G_peClsList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
          <div class="list-item">
            <span style="width: 36px">状态</span>
            <el-select
              placeholder="请选择"
              size="small"
              v-model="searchInfo.vipNormal"
              class="input"
            >
              <el-option label="全部" :value="0"></el-option>
              <el-option label="VIP" :value="1"></el-option>
              <el-option label="普通" :value="2"></el-option>
            </el-select>
          </div>
          <div class="list-item">
            <el-radio-group
              v-model="searchInfo.isActuallyExam"
              @change="statistics"
            >
              <el-radio :label="false">全部项目</el-radio>
              <el-radio :label="true">实际项目</el-radio>
            </el-radio-group>
          </div>
          <ButtonGroup
            :btnList="['统计', '导出']"
            @statistics="statistics"
            @exports="exports"
          />
        </div>
        <div class="title-wrap">
          <h3>体检人员费用列表:</h3>
          <div>
            <span>合计人数：{{ totalInfo.totalCount }}</span>
            <span
              >总金额：<i>{{ totalInfo.totalPrice }}</i> 元</span
            >
          </div>
        </div>
      </div>
      <div class="main-table">
        <PublicTable
          :isSortShow="false"
          :theads="theads"
          :viewTableList.sync="tableData"
          :columnWidth="columnWidth"
        >
          <template #sex="{ scope }">
            <div>
              {{ G_EnumList['Sex'][scope.row.sex] }}
            </div>
          </template>
        </PublicTable>
      </div>
    </div>
  </div>
</template>

<script>
import ButtonGroup from './components/buttonGroup.vue';
import PublicTable from '@/components/publicTable.vue';
import { mapGetters } from 'vuex';
import { dataUtils } from '@/common';
import { export2Excel } from '@/common/excelUtil';
import ExportExcel from '@/common/excel/exportExcel';
export default {
  name: 'examinePersonnelCost',
  mixins: [ExportExcel],
  components: {
    ButtonGroup,
    PublicTable
  },
  data() {
    return {
      searchInfo: {
        timeType: 1,
        date: [new Date(), new Date()],
        companyCode: '',
        companyDeptCode: '',
        peType: 0,
        clusCode: '',
        peCls: '',
        vipNormal: 0,
        isActuallyExam: false
      },
      isHave: true,
      companyDeptList: [],
      theads: {
        regNo: '体检号',
        companyName: '单位',
        companyDeptName: '部门',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        tel: '电话',
        activeTime: '体检日期',
        clusterName: '套餐名称',
        price: '总金额'
      },
      columnWidth: {
        activeTime: 180,
        regNo: 130,
        name: 80,
        sex: 80,
        age: 80,
        clusterName: 160,
        price: 100,
        tel: 120
      },
      tableData: [],
      companyList: [],
      clusterList: [],
      fixed_clusterList: [],
      totalInfo: {
        totalCount: 0,
        totalPrice: 0
      }
    };
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_peClsList',
      'G_datePickerShortcuts',
      'G_config'
    ])
  },
  created() {
    this.getCompany();
    this.getCluster();
  },
  methods: {
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    // 获取单位
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    //单位下拉变化
    companyChange(data) {
      this.companyTimesChange(data);
      if (!data || data.length === 0) {
        this.searchInfo.date = [dataUtils.getDate(), dataUtils.getDate()];
        this.searchInfo.companyDeptCode = '';
        this.isHave = true;
        this.statistics();
        return;
      }
      this.getDepartList(data);
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.searchInfo.date = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.statistics();
    },
    // 获取套餐
    getCluster() {
      this.$ajax.post(this.$apiUrls.Cluster).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.clusterList = returnData;
        this.fixed_clusterList = returnData;
      });
    },
    // 单位改变更新套餐
    companyTimesChange(data) {
      this.searchInfo.clusCode = '';
      if (!data || data.length === 0) {
        this.clusterList = this.fixed_clusterList;
        return;
      }
      this.$ajax
        .post(this.$apiUrls.ReadCompanyCandidateCluster, '', {
          query: {
            companyCode: data[0],
            companyTimes: data[1]
          }
        })
        .then(async (r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.clusterList = returnData || [];
        });
    },
    //获取部门数据
    getDepartList(companyCode) {
      this.searchInfo.companyDeptCode = '';
      this.isHave = true;
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanyDepartment, {
          companyCode: companyCode?.[0] || '',
          deptCode: '',
          deptName: ''
        })
        .then((r) => {
          this.isHave = false;
          //console.log("r", r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.companyDeptList = returnData || [];
        });
    },
    // 查询
    statistics() {
      let [beginDate, endDate] = this.searchInfo.date;
      let data = {
        beginDate: dataUtils.dateToStrStart(beginDate),
        endDate: dataUtils.dateToStrEnd(endDate),
        companyCode: this.searchInfo.companyCode[0],
        companyDeptCode: this.searchInfo.companyDeptCode,
        peType: this.searchInfo.peType,
        clusCode: this.searchInfo.clusCode,
        peCls: this.searchInfo.peCls === '' ? -1 : this.searchInfo.peCls,
        vipNormal: this.searchInfo.vipNormal,
        isActuallyExam: this.searchInfo.isActuallyExam,
        timeType: this.searchInfo.timeType
      };
      console.log(data);
      this.$ajax.post(this.$apiUrls.PersonnelFeeList, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.tableData = returnData.feeList;
        this.totalInfo.totalCount = returnData.totalCount;
        this.totalInfo.totalPrice = returnData.totalPrice;
      });
    },
    // 导出
    exports() {
      if (this.tableData.length == 0) {
        this.$message({
          message: '体检人员费用列表不能为空！',
          type: 'warning',
          showClose: true
        });
        return;
      }

      this.$confirm(`确定导出体检人员费用列表?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let thead = {
          regNo: '体检号',
          companyName: '单位',
          companyDeptName: '部门',
          name: '姓名',
          sex: '性别',
          age: '年龄',
          tel: '电话',
          activeTime: '体检日期',
          clusterName: '套餐名称',
          price: '总金额'
        };
        let text = `合计人数：${this.totalInfo.totalCount}   总金额：${this.totalInfo.totalPrice} 元`;
        this.exportExcel(
          '体检人员费用列表',
          '体检人员费用列表',
          thead,
          this.tableData,
          text
        );
        return;
        let columns = [];
        Object.keys(this.theads).map((item) => {
          columns.push({
            title: this.theads[item],
            key: item
          });
        });
        let data = dataUtils.deepCopy(this.tableData);
        let pushJson = {
          regNo: '',
          companyName: '',
          name: '',
          sex: '',
          age: '',
          tel: '',
          activeTime: `合计人数：${this.totalInfo.totalCount}`,
          price: `总金额：${this.totalInfo.totalPrice} 元`,
          chargeTime: ''
        };
        data.unshift(pushJson);
        const title = `${this.searchInfo.date[0]}-${this.searchInfo.date[1]}体检人员费用`;
        this.$nextTick(() => {
          export2Excel(columns, data, title);
        });
      });
    },
    // 单选框切换
    radioChange() {
      if (this.searchInfo.peType === 1) {
        this.searchInfo.companyCode = '';
      }
      this.statistics();
    }
  }
};
</script>

<style lang="less" scoped>
.examinePersonnelCost {
  color: #2d3436;
  .main {
    height: 100%;
    background: #fff;
    padding: 18px 10px;
    display: flex;
    flex-direction: column;
  }
  .search-list {
    display: flex;
    align-items: center;
    margin-bottom: 18px;
  }
  .list-item {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 20px;
    width: 300px;
    &:last-child {
      margin-right: 0;
    }
    span {
      font-size: 14px;
      font-weight: 600;
      margin-right: 10px;
    }
  }
  .input {
    width: 100%;
  }
  .title-wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 14px;
    h3 {
      font-size: 18px;
    }
    span {
      font-size: 16px;
      & + span {
        margin-left: 20px;
      }
      i {
        color: red;
        font-style: normal;
      }
    }
  }
  .main-table {
    border: 1px solid rgba(178, 190, 195, 0.5);
    border-radius: 4px;
    flex: 1;
    overflow: auto;
  }
}
</style>
