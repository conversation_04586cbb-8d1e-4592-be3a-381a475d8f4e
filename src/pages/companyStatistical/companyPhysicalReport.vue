<template>
  <!-- 单位体检报告 -->
  <div class="companyPhysicalReport">
    <div class="main">
      <div class="searchBar">
        <div class="search-list">
          <div class="list-item">
            <span style="width: 96px">统计时间</span>
            <el-date-picker
              :picker-options="{ shortcuts: G_datePickerShortcuts }"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              v-model="searchInfo.date"
              :clearable="false"
              readonly
            >
            </el-date-picker>
          </div>
          <div class="list-item">
            <span style="width: 36px">单位</span>
            <el-cascader
              ref="company_cascader_ref"
              v-model="searchInfo.companyCode"
              :filter-method="filterMethod"
              :options="companyList"
              :props="{ multiple: false }"
              clearable
              filterable
              size="small"
              collapse-tags
              @change="companyChange"
            >
            </el-cascader>
          </div>
          <div class="list-item">
            <span style="width: 36px">类型</span>
            <el-select
              placeholder="请选择"
              size="small"
              v-model="searchInfo.resultType"
              class="input"
              @change="statistics"
            >
              <el-option
                v-for="item in resultTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
          <div class="list-item">
            <span style="width: 72px">检查状态</span>
            <vxe-select
              @change="statistics"
              placeholder="请选择"
              size="small"
              clearable
              multiple
              :popper-append-to-body="true"
              v-model="searchInfo.peStatuses"
              :options="G_peStatus"
            >
            </vxe-select>
          </div>
          <div class="list-item" v-if="C_mixedInspection">
            <el-checkbox @change="statistics" v-model="searchInfo.isOccupation"
              >职</el-checkbox
            >
          </div>
          <ButtonGroup
            :btnList="['统计', '导出']"
            @statistics="statistics"
            @exports="exports"
          />
        </div>
        <div class="title-wrap">
          <h3>体检情况登记表:</h3>
        </div>
      </div>
      <div class="main-table" v-for="(item, index) in dataInfo" :key="index">
        <div class="table-title">
          <span>受检单位：{{ item.companyName }}</span>
          <span>总人数：{{ item.totalNum }}</span>
          <span>男：{{ item.maleNum }}</span>
          <span>女：{{ item.femaleNum }}</span>
          <span>未完成：{{ item.unfinishNum }}</span>
        </div>
        <div class="table">
          <PublicTable
            :theads="theads"
            :viewTableList.sync="item.report"
            :columnWidth="columnWidth"
            :tableLoading="tableLoading"
            showOverflowTooltip
            border
          >
            <template #sex="{ scope }">
              {{ G_EnumList['Sex'][scope.row.sex] }}
            </template>
          </PublicTable>
        </div>
      </div>
      <div class="main-table" v-if="dataInfo.length === 0">
        <PublicTable
          :theads="theads"
          showOverflowTooltip
          :tableLoading="tableLoading"
          border
        >
        </PublicTable>
      </div>
    </div>
  </div>
</template>

<script>
import ButtonGroup from './components/buttonGroup.vue';
import PublicTable from '@/components/publicTable.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
import exportExcelJs from '@/common/excel/exportExcel';
export default {
  name: 'companyPhysicalReport',
  components: {
    ButtonGroup,
    PublicTable
  },
  mixins: [exportExcelJs],
  data() {
    return {
      tableLoading: false,
      searchInfo: {
        date: [new Date(), new Date()],
        companyCode: '',
        resultType: 0,
        isOccupation: false,
        peStatuses: []
      },
      theads: {
        regNo: '体检号',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        peStatusName: '检查状态',
        conclusion: '体检结果',
        summary: '体检综述'
      },
      theads1: {
        regNo: '体检号',
        activeTime: '体检日期',
        peStatusName: '检查状态',
        auditTime: '总检日期',
        name: '姓名',
        sex: '性别',
        tel: '手机号',
        cardNo: '身份证号',
        companyName: '单位名称',
        hazardNames: '危害因素',
        jobStatusName: '体检类别',
        jobId: '工号',
        jobName: '工种',
        summary: '体检综述',
        maxConclusion: '总检结论',
        conclusion: '体检结果',
        occupationalAdvice: '处理意见',
        suggestion: '建议',
        totalPrice: '金额'
      },
      tableData: [],
      companyList: [],
      resultTypeList: [
        {
          value: 0,
          label: '全部'
        },
        {
          value: 1,
          label: '体检结果'
        },
        {
          value: 2,
          label: '体检综述'
        }
      ],
      dataInfo: [],
      columnWidth: {
        regNo: 130,
        name: 80,
        sex: 60,
        age: 60,
        cardNo: 160,
        tel: 130,
        activeTime: 160,
        auditTime: 160,
        peStatusName: 80,
        companyName: 180,
        conclusion: 700,
        hazardNames: 500,
        summary: 700
      }
    };
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_datePickerShortcuts',
      'G_config',
      'G_peStatus'
    ]),
    C_mixedInspection() {
      return this.G_config?.physicalMode.length > 1;
    }
  },
  created() {
    this.searchInfo.isOccupation = this.G_config?.physicalOccupationMode;
    this.processingHeader(
      this.searchInfo.isOccupation,
      this.searchInfo.resultType
    );
    this.getCompany();
  },
  methods: {
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    // 获取单位
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) return;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.searchInfo.date = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.statistics();
    },
    statistics() {
      if (!this.searchInfo.companyCode) {
        this.$message({
          message: '请选择单位!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let [beginDate, endDate] = this.searchInfo.date;
      let data = {
        beginDate: dataUtils.dateToStrStart(dataUtils.dateToString(beginDate)),
        endDate: dataUtils.dateToStrEnd(dataUtils.dateToString(endDate)),
        companyCode: this.searchInfo?.companyCode[0] || '',
        companyTimes: this.searchInfo?.companyCode[1] || '',
        resultType: this.searchInfo.resultType,
        isOccupation: this.searchInfo.isOccupation,
        peStatuses: this.searchInfo.peStatuses || []
      };
      this.tableLoading = true;
      this.$ajax
        .post(this.$apiUrls.CompanyPeReport, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.processingHeader(
            this.searchInfo.isOccupation,
            this.searchInfo.resultType
          );
          if (this.searchInfo.resultType === 0) {
            // this.theads = {
            //   regNo: "体检号",
            //   name: "姓名",
            //   sex: "性别",
            //   age: "年龄",
            //   conclusion: "体检结果",
            //   summary: "体检综述"
            // };
            this.columnWidth.conclusion = 700;
            this.columnWidth.summary = 700;
          } else if (this.searchInfo.resultType === 1) {
            // this.theads = {
            //   regNo: "体检号",
            //   name: "姓名",
            //   sex: "性别",
            //   age: "年龄",
            //   conclusion: "体检结果"
            // };
            this.columnWidth.conclusion = '';
          } else {
            // this.theads = {
            //   regNo: "体检号",
            //   name: "姓名",
            //   sex: "性别",
            //   age: "年龄",
            //   summary: "体检综述"
            // };
            this.columnWidth.summary = '';
          }
          this.dataInfo = returnData;
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    processingHeader(isOccupation, ind) {
      const headerMapping = {
        1: 'summary',
        2: 'conclusion'
      };
      if (isOccupation) {
        this.theads = {
          regNo: '体检号',
          activeTime: '体检日期',
          peStatusName: '检查状态',
          auditTime: '总检日期',
          name: '姓名',
          sex: '性别',
          age: '年龄',
          tel: '手机号',
          cardNo: '身份证号',
          companyName: '单位名称',
          hazardNames: '危害因素',
          jobStatusName: '体检类别',
          jobId: '工号',
          jobName: '工种',
          summary: '体检综述',
          maxConclusion: '总检结论',
          conclusion: '体检结果',
          occupationalAdvice: '处理意见',
          suggestion: '建议',
          totalPrice: '金额'
        };
      } else {
        this.theads = {
          regNo: '体检号',
          name: '姓名',
          sex: '性别',
          age: '年龄',
          peStatusName: '检查状态',
          conclusion: '体检结果',
          summary: '体检综述'
        };
      }
      if (ind !== 0) delete this.theads[headerMapping[ind]];
    },
    exports() {
      if (this.dataInfo.length === 0) {
        this.$message({
          message: '暂无数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm('确定导出文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.exportExcel(
          '单位体检报告_' + this.dataInfo[0]?.companyName || '',
          this.dataInfo[0]?.companyName || '单位体检报告',
          this.theads,
          this.dataInfo[0].report
        );
      });
    }
  }
};
</script>

<style lang="less" scoped>
.companyPhysicalReport {
  color: #2d3436;
  .main {
    height: 100%;
    background: #fff;
    padding: 18px 10px;
    display: flex;
    flex-direction: column;
  }
  .search-list {
    display: flex;
    align-items: center;
    margin-bottom: 18px;
  }
  .list-item {
    display: flex;
    align-items: center;
    margin-right: 20px;
    width: 300px;
    &:last-child {
      margin-right: 0;
    }
    span {
      font-size: 14px;
      font-weight: 600;
      margin-right: 10px;
    }
  }
  .input {
    width: 100%;
  }
  .title-wrap {
    margin-bottom: 18px;
    h3 {
      font-size: 18px;
    }
  }
  .main-table {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .table-title {
    font-size: 14px;
    color: #1770df;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    span {
      flex: 1;
      &:first-child {
        flex: 2;
      }
    }
  }
  .table {
    border: 1px solid rgba(178, 190, 195, 0.5);
    border-radius: 4px;
    flex: 1;
    overflow: auto;
  }
}
</style>
