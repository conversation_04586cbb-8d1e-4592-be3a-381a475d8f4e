<!-- 单位体检综合报告 -->
<template>
  <div class="synthesis_page">
    <div class="synthesis_wrap">
      <header>
        <el-form :model="searchForm" label-width="60px" class="form_dom">
          <el-form-item
            label="统计时间"
            prop="statisticalTime"
            label-width="70px"
          >
            <el-date-picker
              :picker-options="{ shortcuts: G_datePickerShortcuts }"
              style="width: 210px"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              v-model="statisticalTime"
              :clearable="false"
            >
            </el-date-picker>
          </el-form-item>
          <!-- <el-form-item label="" prop="" class="radioForm">
            <div class="titleRadio">
              <el-radio-group v-model.trim="searchForm.personGroup">
                <el-radio :label="0">所有</el-radio>
                <el-radio :label="1">个人</el-radio>
                <el-radio :label="2">团体</el-radio>
              </el-radio-group>
            </div>
          </el-form-item> -->
          <el-form-item label="单位" prop="companyCode">
            <el-cascader
              ref="company_cascader_ref"
              v-model="searchForm.companyCode"
              :filter-method="filterMethod"
              :options="companyList"
              :props="{ multiple: false }"
              clearable
              filterable
              size="small"
              collapse-tags
              @change="companyChange"
            >
            </el-cascader>
          </el-form-item>
        </el-form>
        <ButtonGroup :btnList="['统计', '导出']">
          <template #addBtn>
            <el-button
              size="small"
              class="green_btn btn"
              icon="iconfont icon-dayin-"
              v-print="'#test'"
              >打印</el-button
            >
            <!-- <el-button
              size="small"
              class="green_btn btn"
              icon="iconfont icon-dayin-"
              @click="showReport"
              >显示报告</el-button
            > -->
          </template>
        </ButtonGroup>
      </header>
      <div class="content_wrap">
        <h3>体检疾患名单汇总统计表：</h3>
        <div class="info_wrap" id="test">
          <WebDocumentViewer ref="report_ref" />
          <!-- <div class="unit_cover">封面</div>
          <ul class="unit_head">
            <li>体检单位：广东亿讯科技有限公司</li>
            <li>总人数：98人</li>
            <li>男：58人</li>
            <li>女：40人</li>
            <li>统计时间：2022.01.01-2022.12.20</li>
          </ul>
          <div class="every_illness">
            <ul class="illness_title">
              <li>
                <span>1</span>
                <p>13碳呼气试验强阳性</p>
              </li>
              <li>人数：3人</li>
            </ul>
            <div class="person_list">
              <p>人员名单：</p>
              <span>翁少敏 、 王晓鸥 、 莫建超</span>
            </div>
            <div class="suggest_wrap">
              <p>建议：</p>
              <span>消化科门诊诊治。</span>
            </div>
          </div>
          <div class="every_illness">
            <ul class="illness_title">
              <li>
                <span>1</span>
                <p>13碳呼气试验强阳性</p>
              </li>
              <li>人数：3人</li>
            </ul>
            <div class="person_list">
              <p>人员名单：</p>
              <span>翁少敏 、 王晓鸥 、 莫建超</span>
            </div>
            <div class="suggest_wrap">
              <p>建议：</p>
              <span>消化科门诊诊治。</span>
            </div>
          </div>
          <div class="every_illness">
            <ul class="illness_title">
              <li>
                <span>1</span>
                <p>13碳呼气试验强阳性</p>
              </li>
              <li>人数：3人</li>
            </ul>
            <div class="person_list">
              <p>人员名单：</p>
              <span>翁少敏 、 王晓鸥 、 莫建超</span>
            </div>
            <div class="suggest_wrap">
              <p>建议：</p>
              <span>消化科门诊诊治。</span>
            </div>
          </div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ButtonGroup from './components/buttonGroup.vue';
import WebDocumentViewer from '@/components/reportViewer';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
export default {
  name: 'synthesis',
  components: {
    ButtonGroup,
    WebDocumentViewer
  },
  data() {
    return {
      searchForm: {
        beginDate: '',
        endDate: '',
        personGroup: 0,
        companyCode: '',
        itemClsCode: '',
        doctorName: '',
        deptCode: ''
      },
      statisticalTime: [],
      companyList: []
    };
  },
  computed: {
    ...mapGetters(['G_datePickerShortcuts'])
  },
  methods: {
    showReport() {
      this.$refs.report_ref.openReport();
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    // 获取单位
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) return;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.statisticalTime = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
    }
  }
};
</script>

<style lang="less" scoped>
.synthesis_page {
  .synthesis_wrap {
    padding: 10px;
    height: 100%;
    background: #fff;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    overflow: auto;
  }

  header {
    display: flex;
    margin-bottom: 15px;

    .form_dom {
      display: flex;
      justify-content: center;
      margin-right: 10px;

      /deep/ .el-form-item {
        margin-bottom: 0;
      }
    }

    .radioForm {
      /deep/ .el-form-item__content {
        margin-left: 20px !important;
      }
    }
  }

  .content_wrap {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    display: flex;
    flex-direction: column;
    h3 {
      font-family: PingFangSC-Medium;
      font-size: 18px;
      color: #2d3436;
    }
    .info_wrap {
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
    }
    .unit_cover {
      height: 29.7px;
      text-align: center;
    }

    .unit_head {
      display: flex;
      padding: 6px 15px;
      // width: 21cm;
      background: #1770df;
      color: #fff;

      li {
        flex-shrink: 0;
        font-size: 18px;

        &:nth-child(1) {
          flex-basis: 30%;
        }

        &:nth-child(2) {
          width: 150px;
        }

        &:nth-child(3) {
          width: 100px;
        }

        &:nth-child(4) {
          width: 100px;
        }

        &:nth-child(5) {
          flex: 1;
        }
      }
    }

    // .every_illness {
    // }

    .illness_title {
      display: flex;
      margin-top: 10px;
      background: rgba(23, 112, 223, 0.2);

      li {
        padding: 6px 0;
        font-size: 18px;
        color: #2d3436;

        &:nth-child(1) {
          display: flex;
          flex-basis: 30%;
          position: relative;
          padding-left: 60px;

          span {
            width: 50px;
            background: #1770df;
            color: #fff;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          p {
            flex: 1;
            flex-shrink: 0;
          }
        }

        &:nth-child(2) {
          width: 115px;
          text-align: right;
        }
      }
    }

    .person_list,
    .suggest_wrap {
      padding: 10px 20px;

      p {
        font-size: 14px;
        color: #2d3436;
        font-weight: 600;
        margin-bottom: 8px;
      }

      span {
        font-size: 14px;
        color: #2d3436;
      }
    }

    .person_list {
      background-image: linear-gradient(
        to right,
        rgba(45, 52, 54, 0.6) 20%,
        rgba(255, 255, 255, 0) 0%
      );
      /* 35%置虚线点x热上的长质*/
      background-position: bottom;
      /* top配置上边强位置的虚线 */
      background-size: 5px 1px;
      /* 第一个参数置线点的间距，第二个参置虚线点y上的长度*/
      background-repeat: repeat-x;
    }

    .suggest_wrap {
      border-bottom: 1px solid rgba(45, 52, 54, 0.6);
    }
  }
}
</style>
<style media="test" lang="less" scoped>
#test {
  .unit_cover {
    height: 29.7px;
    text-align: center;
  }
  .unit_head {
    li {
      font-size: 18px;
      padding: 0 5px;
      &:nth-child(1) {
        flex-basis: 30%;
      }

      &:nth-child(2) {
        width: 150px;
      }

      &:nth-child(3) {
        width: 100px;
      }

      &:nth-child(4) {
        width: 100px;
      }

      &:nth-child(5) {
        flex: 1;
      }
    }
  }

  //   .every_illness {
  //   }

  .illness_title {
    li {
      &:nth-child(1) {
        display: flex;
        flex-basis: 30%;
        position: relative;
        padding-left: 60px;

        span {
          width: 50px;
          background: #1770df;
          color: #fff;
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        p {
          flex: 1;
          flex-shrink: 0;
        }
      }

      &:nth-child(2) {
        width: 115px;
        text-align: right;
      }
    }
  }
}
</style>
