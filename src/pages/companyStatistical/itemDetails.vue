<template>
  <!-- 单位体检人员项目明细报表 -->
  <div class="itemDetails" id="itemDetails">
    <div class="itemDetails-wrap">
      <div class="header">
        <div class="header-item">
          <span>统计时间</span>
          <el-date-picker
            :picker-options="{ shortcuts: G_datePickerShortcuts }"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
            v-model="statisticalTime"
            @change="search"
            :clearable="false"
          >
          </el-date-picker>
        </div>
        <div class="header-item">
          <span style="width: 30px">单位</span>
          <el-cascader
            ref="company_cascader_ref"
            v-model="searchForm.companyCode"
            :filter-method="filterMethod"
            :options="companyList"
            :props="{ multiple: false }"
            clearable
            filterable
            size="small"
            collapse-tags
            @change="companyChange"
          >
          </el-cascader>
        </div>
        <div class="header-item">
          <el-radio-group v-model.trim="radioGroup" @change="search">
            <el-radio :label="0">明细</el-radio>
            <el-radio :label="1">汇总</el-radio>
          </el-radio-group>
        </div>
        <div class="header-item">
          <ButtonGroup
            :btnList="['统计', '导出']"
            @statistics="search"
            @exports="exportTable"
          ></ButtonGroup>
        </div>
      </div>
      <div id="printDiv">
        <div class="titlewrap">
          <p class="titleP">{{ headTitle }}:</p>
          <p class="contP" v-show="radioGroup === 1">
            <span class="otherSpsn">总人数：{{ totalCount }}</span>
            <span class="otherSpsn">到检人数：{{ checkCount }}</span>
            <span class="otherSpsn">未检人数:{{ noCheckCount }}</span>
          </p>
        </div>
        <div class="contDiv" id="contDiv">
          <p class="titleP">
            <span>体检单位:{{ companyName ? companyName : '无单位' }}</span>
            <!-- <span class="otherSpsn">体检日期:2022.12.17</span> -->
          </p>
          <div class="tableDiv">
            <PublicTable
              :isSortShow="false"
              :viewTableList.sync="tableData"
              :theads.sync="theads"
              :columnWidth="columnWidth"
              v-if="radioGroup == 1"
            >
              <template #sex="{ scope }">
                <div>
                  {{ G_EnumList['Sex'][scope.row.sex] }}
                </div>
              </template>
              <template #age="{ scope }">
                <div>
                  {{ scope.row.age + enum_ageUnit[scope.row.ageUnit] }}
                </div>
              </template>
            </PublicTable>
            <PublicTable
              :isSortShow="false"
              :viewTableList.sync="sumtableData"
              :theads.sync="sumtheads"
              :columnWidth="columnWidths"
              v-if="radioGroup == 0"
              :tableLoading="tableLoading"
            >
            </PublicTable>
          </div>
        </div>
      </div>
      <div class="print_table" id="printHtml">
        <p style="text-align: center; padding: 20px 0; font-size: 18px">
          {{ companyName ? companyName : '无单位' }}{{ headTitle }}
        </p>
        <PublicTable :viewTableList.sync="printData" :theads.sync="printTheads">
        </PublicTable>
      </div>
    </div>
  </div>
</template>
<script>
import PublicTable from '@/components/publicTable.vue';
import ButtonGroup from './components/buttonGroup.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
import { export2Excel } from '@/common/excelUtil';
import moment from 'moment';
import printJS from '@/common/printJS';
import exportExcelJs from '@/common/excel/exportExcel';
export default {
  name: 'itemDetails',
  mixins: [exportExcelJs],
  components: { PublicTable, ButtonGroup },
  data() {
    return {
      searchForm: {
        beginDate: '',
        endDate: '',
        companyCode: ''
      },
      radioGroup: 0,
      companyName: '',
      statisticalTime: [dataUtils.getDate(), dataUtils.getDate()],
      companyList: [],
      columnWidth: {
        regNo: 130,
        // name: 100,
        // sex: 60,
        // age: 80,
        cardNo: 180,
        activeTime: 180,
        tel: 150,
        companyDeptName: 180,
        clusterName: 400
      },
      columnWidths: {},
      enum_ageUnit: {
        null: '',
        0: '岁',
        1: '月'
      },
      theads: {
        combName: '体检项目组合',
        price: '单价',
        amount: '体检费用',
        totalTimes: '总检查人次',
        checkedTimes: '已检查人次',
        noCheckedTimes: '未检查人次'
      },
      tableData: [],
      totalCount: 0,
      checkCount: 0,
      noCheckCount: 0,
      thead: {
        regNo: '体检号',
        name: '姓名',
        cardNo: '身份证',
        sex: '性别',
        age: '年龄',
        activeTime: '体检日期'
      },
      sumtheads: {
        regNo: '体检号',
        name: '姓名',
        cardNo: '身份证',
        sex: '性别',
        age: '年龄',
        activeTime: '体检日期'
      },
      sumtableData: [],
      headTitle: '单位体检人员项目明细报表',
      printData: [],
      printTheads: {},
      tableLoading: false
    };
  },
  mounted() {
    this.getCompany();
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_sexList', 'G_datePickerShortcuts'])
  },
  methods: {
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    //统计查询
    search() {
      this.printData = [];
      if (this.radioGroup == 0) {
        this.headTitle = '单位体检人员项目明细报表';
        this.detailSearch(); //明细表
      } else {
        this.headTitle = '单位体检人员项目汇总报表';
        this.sumSearch(); //汇总表
      }
    },
    //汇总
    sumSearch() {
      if (this.searchForm.companyCode === '') {
        this.$message({
          message: '请选择单位！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (!this.statisticalTime) {
        this.searchForm.beginDate = '';
        this.searchForm.endDate = '';
      } else {
        this.searchForm.beginDate = this.statisticalTime[0];
        this.searchForm.endDate = this.statisticalTime[1];
      }
      let { companyCode, ...surplus } = this.searchForm;
      let parameter = {
        companyCode: companyCode[0],
        ...surplus
      };
      let temp = this.companyList.find(
        (item) => item.value === this.searchForm.companyCode[0]
      )?.label;
      this.companyName = temp;
      this.$ajax
        .post(this.$apiUrls.GetCompanyCombAmount, parameter)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.tableData = returnData.companyCombAmount || [];
          this.totalCount = returnData.totalCount;
          this.checkCount = returnData.checkCount;
          this.noCheckCount = returnData.noCheckCount;
          this.printData = returnData.companyCombAmount || [];
          this.printTheads = this.theads;
          if (this.tableData.length >= 1) {
            this.$message({
              message: '成功!',
              type: 'success',
              showClose: true
            });
          } else {
            this.$message({
              message: '暂无数据!',
              type: 'success',
              showClose: true
            });
          }
        });
    },
    //明细
    detailSearch() {
      if (this.searchForm.companyCode === '') {
        this.$message({
          message: '请选择单位！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (!this.statisticalTime) {
        this.searchForm.beginDate = '';
        this.searchForm.endDate = '';
      } else {
        this.searchForm.beginDate = this.statisticalTime[0];
        this.searchForm.endDate = this.statisticalTime[1];
      }
      this.sumtableData = [];
      this.sumtheads = this.thead;
      this.tableLoading = true;
      let { companyCode, ...surplus } = this.searchForm;
      let parameter = {
        companyCode: companyCode[0],
        ...surplus
      };
      let temp = this.companyList.find(
        (item) => item.value === this.searchForm.companyCode[0]
      )?.label;
      this.companyName = temp;
      this.$ajax
        .post(this.$apiUrls.GetCompanyPatientCombs, parameter)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) {
            this.tableLoading = false;
            return;
          }
          console.log('[ returnData ]-234', returnData);
          if (returnData.patientCombResults.length >= 1) {
            // 获取动态表头并拼接
            const zcombHead = {};
            const columnWidth = {};
            returnData.combNames.forEach((item, key) => {
              zcombHead[`zcomb${key}`] = item;
              columnWidth[`zcomb${key}`] = 240;
            });
            this.sumtheads = { ...this.thead, ...zcombHead };
            this.columnWidths = { ...this.columnWidth, ...columnWidth };
            console.log(zcombHead);

            // 获取动态数据并拼接
            const newData = returnData.patientCombResults.map((item) => {
              const dataObj = {
                regNo: item.regNo,
                name: item.name,
                cardNo: item.cardNo,
                sex: this.G_EnumList['Sex'][item.sex],
                age: item.ageUnit === 1 ? `${item.age}月` : `${item.age}岁`,
                activeTime: item.activeDate
              };

              const dataObj2 = item.combResults.reduce((obj, items, idx) => {
                obj[`zcomb${idx}`] = items;
                return obj;
              }, {});

              return { ...dataObj, ...dataObj2 };
            });

            console.log(newData);
            this.sumtableData = newData;
            this.printData = newData;
            this.printTheads = this.sumtheads;
            this.tableLoading = false;
            this.$message({
              message: '成功!',
              type: 'success',
              showClose: true
            });
          } else {
            this.sumtableData = [];
            this.tableLoading = false;
            this.$message({
              message: '暂无数据!',
              type: 'success',
              showClose: true
            });
            return;
          }
        });
    },
    //获取单位下拉
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) return;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.statisticalTime = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.search();
    },
    // 导出excel
    exportTable() {
      if (this.printData.length == 0) {
        this.$message.warning('请选择至少一条数据进行操作!');
        return;
      }
      this.$confirm('确定导出列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (this.radioGroup == 1) {
          this.exportExcel(
            '单位体检人员项目汇总报表_' + dataUtils.getNowDateTiemNo(),
            '单位体检人员项目汇总报表',
            this.printTheads,
            this.tableData
          );
        } else {
          this.exportExcel(
            '单位体检人员项目明细报表_' + dataUtils.getNowDateTiemNo(),
            '单位体检人员项目明细报表',
            this.printTheads,
            this.sumtableData
          );
        }

        // let columns = [];
        // let theads = { index: "序号", ...this.printTheads };
        // Object.keys(theads).map((item) => {
        //   columns.push({
        //     title: theads[item],
        //     key: item,
        //   });
        // });
        // let data = [];
        // data = dataUtils.deepCopy(this.printData);
        // data.map((item, i) => {
        //   item.index = i + 1;
        // });
        // if (this.radioGroup == 0) {
        // let pushJson = {
        //     index: "",
        //     regNo: "",
        //     name: "",
        //     cardNo: "",
        //     sex: `总人数：${this.totalCount}`,
        //     age: `到检人数：${this.checkCount}`,
        //     activeTime: `未检人数：${this.noCheckCount}`,
        //   };
        //    data.push(pushJson);
        // }
        // const title =
        //   this.companyName + this.headTitle + moment().format("YYYY-MM-DD");
        // this.$nextTick(() => {
        //   export2Excel(columns, data, title);
        // });
      });
    },
    //打印
    print() {
      if (this.printData.length == 0) {
        return this.$message({
          message: '请至少选择一条数据进行打印！',
          type: 'warning',
          showClose: true
        });
      }

      setTimeout(() => {
        printJS('printHtml');
      }, 500);
    }
  }
};
</script>
<style lang="less" scoped>
.itemDetails {
  color: #2d3436;
  display: flex;
  flex-direction: column;
  .itemDetails-wrap {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    padding: 10px;
    background: #fff;
    font-size: 14px;
  }
  .header {
    display: flex;
    align-items: center;
    font-size: 14px;
    margin-bottom: 10px;
  }
  .header-item {
    display: flex;
    align-items: center;
    margin-right: 10px;
    span {
      font-weight: 600;
      width: 56px;
      margin-right: 10px;
    }
  }
  #printDiv {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    .titlewrap {
      height: 60px;
      line-height: 60px;
      display: flex;
      flex-direction: row;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #2d3436;
      .titleP {
        font-family: PingFangSC-Medium;
        font-size: 18px;
        width: 224px;
        font-weight: 700;
      }
      .contP {
        flex: 1;
        display: flex;
        flex-direction: row;
        .otherSpsn {
          flex: 1;
          margin-left: 100px;
          font-size: 15px;
        }
      }
    }
    //.otherSpsn {
    // flex: 1;
    //  color: #1770df;
    //  margin-left: 100px;
    // }
    .contDiv {
      flex: 1;
      overflow: auto;
      display: flex;
      flex-direction: column;
      .titleP {
        color: #1770df;
        font-weight: 600;
        margin: 10px 0;
      }
      .tableDiv {
        flex: 1;
        overflow: auto;
      }
    }
    .publicTable_com {
      border: 1px solid #ccc;
      border-radius: 4px;
    }
  }
  .print_table {
    display: none;
  }
}
</style>
