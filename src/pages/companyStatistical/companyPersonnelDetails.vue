<template>
  <!-- 单位人员明细报表 -->
  <div class="companyPersonnelDetails">
    <div class="main">
      <div class="searchBar">
        <div class="search-list date_search">
          <div class="list-item">
            <div class="select">
              <el-select
                placeholder="请选择"
                size="small"
                v-model="searchInfo.timeType"
                class="input"
              >
                <el-option label="体检时间" :value="1"></el-option>
                <el-option label="登记时间" :value="2"></el-option>
                <el-option label="采集时间" :value="3"></el-option>
              </el-select>
            </div>
            <el-date-picker
              :picker-options="{ shortcuts: G_datePickerShortcuts }"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              v-model="searchInfo.date"
              :clearable="false"
              @change="statistics"
            >
            </el-date-picker>
          </div>
          <div class="list-item">
            <span style="width: 36px">单位</span>
            <el-cascader
              style="width: 100%"
              ref="company_cascader_ref"
              v-model="searchInfo.companyCode"
              :filter-method="filterMethod"
              :options="companyList"
              :props="{ multiple: false }"
              clearable
              filterable
              size="small"
              collapse-tags
              @change="companyChange"
            >
            </el-cascader>
          </div>
          <div class="list-item">
            <span style="width: 36px">部门</span>
            <el-select
              style="width: 250px"
              placeholder="请选择"
              v-model.trim="searchInfo.companyDeptCode"
              size="small"
              filterable
              clearable
              :disabled="isHavue"
              class="input"
              @change="statistics"
            >
              <el-option
                v-for="(item, index) in companyDeptList"
                :key="index"
                :label="item.deptName"
                :value="item.deptCode"
              ></el-option>
            </el-select>
          </div>
          <!-- <div class="list-item">
            <span style="width: 36px">套餐</span>
            <el-select
              placeholder="请选择"
              size="small"
              filterable
              clearable
              v-model="searchInfo.clusCode"
              class="input"
            >
              <el-option
                v-for="item in clusterList"
                :key="item.clusCode"
                :label="item.clusName"
                :value="item.clusCode"
              >
              </el-option>
            </el-select>
          </div>
          <div class="list-item">
            <span style="width: 76px">体检分类</span>
            <el-select
              placeholder="请选择"
              size="small"
              clearable
              v-model="searchInfo.peCls"
              class="input"
            >
              <el-option
                v-for="item in G_peClsList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div> -->
        </div>
        <div class="search-list">
          <div class="search-radio">
            <el-radio-group v-model="searchInfo.status" @change="statistics">
              <el-radio :label="0">全部</el-radio>
              <el-radio :label="1">登记未录结果</el-radio>
              <el-radio :label="2">录部分结果</el-radio>
              <el-radio :label="3">录完结果</el-radio>
              <el-radio :label="4">审核未打印</el-radio>
              <el-radio :label="5">已打印</el-radio>
            </el-radio-group>
          </div>
          <ButtonGroup
            :btnList="['统计', '导出']"
            @statistics="statistics"
            @exports="exports"
          />
        </div>
        <div class="title-wrap">
          <h3>单位人员明细报表:</h3>
          <div>
            <span class="company"
              >体检单位：{{ companyEnum[searchInfo.companyCode[0]] }}</span
            >
            <span>合计人数：{{ tableData.length }}</span>
          </div>
        </div>
      </div>
      <div class="main-table">
        <PublicTable
          :isSortShow="false"
          :theads="theads"
          :viewTableList.sync="tableData"
          :columnWidth="{
            cardNo: 180
          }"
        >
          <template #sex="{ scope }">
            {{ G_EnumList['Sex'][scope.row.sex] }}
          </template>
        </PublicTable>
      </div>
    </div>
  </div>
</template>

<script>
import ButtonGroup from './components/buttonGroup.vue';
import PublicTable from '@/components/publicTable.vue';
import { mapGetters } from 'vuex';
import { dataUtils } from '@/common';
import ExportExcel from '@/common/excel/exportExcel';
export default {
  name: 'companyPersonnelDetails',
  mixins: [ExportExcel],
  components: {
    ButtonGroup,
    PublicTable
  },
  data() {
    return {
      searchInfo: {
        date: [new Date(), new Date()],
        companyCode: '',
        timeType: 1,
        companyDeptCode: '',
        peCls: '',
        status: 0
      },
      theads: {
        regNo: '体检号',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        cardNo: '身份证号码',
        hisCard: '门诊卡号',
        activeTime: '体检日期',
        signer: '签收人',
        signTime: '签收日期'
      },
      tableData: [],
      companyList: [],
      companyEnum: {},
      clusterList: [],
      companyDeptList: [],
      isHavue: true
    };
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_peClsList', 'G_datePickerShortcuts'])
  },
  created() {
    this.getCompany();
    this.getCluster();
  },
  methods: {
    //获取部门数据
    getDepartList(companyCode) {
      this.searchInfo.companyDeptCode = '';
      this.isHavue = true;
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanyDepartment, {
          companyCode: companyCode || '',
          deptCode: '',
          deptName: ''
        })
        .then((r) => {
          this.isHavue = false;
          //console.log("r", r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.companyDeptList = returnData || [];
        });
    },
    // 获取单位
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        let newObj = {};
        returnData.map((item) => {
          newObj[item.companyCode] = item.companyName;
        });
        this.companyEnum = newObj;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) {
        this.isHavue = true;
        this.searchInfo.date = [dataUtils.getDate(), dataUtils.getDate()];
        this.statistics();
        return;
      }
      this.getDepartList(data[0]);
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.searchInfo.date = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.statistics();
    },
    // 获取套餐
    getCluster() {
      this.$ajax.post(this.$apiUrls.Cluster).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.clusterList = returnData;
      });
    },
    // 查询
    statistics() {
      if (!this.searchInfo.companyCode) {
        this.$message({
          message: '请选择单位!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let [beginDate, endDate] = this.searchInfo.date;
      let data = {
        beginDate: dataUtils.dateToStrStart(beginDate),
        endDate: dataUtils.dateToStrEnd(endDate),
        companyCode: this.searchInfo.companyCode[0],
        timeType: this.searchInfo.timeType,
        companyDeptCode: this.searchInfo.companyDeptCode,
        peCls: this.searchInfo.peCls === '' ? -1 : this.searchInfo.peCls,
        status: this.searchInfo.status
      };
      this.$ajax
        .post(this.$apiUrls.CompanyPersonnelDetailReport, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.tableData = returnData;
        });
    },
    // 导出
    exports() {
      if (this.tableData.length == 0) {
        this.$message({
          message: '单位人员明细报表不能为空！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm(`确定导出单位人员明细报表?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          let text = `体检单位：${this.companyEnum[this.searchInfo.companyCode[0]]}   合计人数：${this.tableData.length} 人`;
          this.exportExcel(
            '单位人员明细报表',
            '单位人员明细报表',
            this.theads,
            this.tableData,
            text
          );
        })
        .catch(() => {});
    }
  }
};
</script>

<style lang="less" scoped>
.companyPersonnelDetails {
  color: #2d3436;
  .main {
    height: 100%;
    background: #fff;
    padding: 18px 10px;
    display: flex;
    flex-direction: column;
  }
  .search-list {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 18px;
  }
  .date_search {
    justify-content: flex-start;
  }
  .list-item {
    display: flex;
    align-items: center;
    margin-right: 20px;
    width: 400px;
    &:last-child {
      margin-right: 0;
    }
    span {
      font-size: 14px;
      font-weight: 600;
      margin-right: 10px;
    }
  }
  .search-radio {
    background: rgba(23, 112, 223, 0.1);
    border-radius: 2px;
    padding: 6px 18px;
  }
  .select {
    width: 130px;
    margin-right: 10px;
  }
  .input {
    width: 100%;
  }
  .title-wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 14px;
    h3 {
      font-size: 18px;
    }
    span {
      font-size: 14px;
    }
  }
  .company {
    margin-right: 70px;
  }
  .main-table {
    border: 1px solid rgba(178, 190, 195, 0.5);
    border-radius: 4px;
    flex: 1;
    overflow: auto;
  }
}
</style>
