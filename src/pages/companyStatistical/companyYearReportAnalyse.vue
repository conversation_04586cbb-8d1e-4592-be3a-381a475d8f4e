<!-- 单位年度报告分析 -->
<template>
  <div class="companyYearReportAnalyse_page">
    <div class="header-wrapper">
      <el-form
        :model="searchInfo"
        status-icon
        ref="refSearchForm"
        :inline="true"
      >
        <el-row type="flex" align="middle">
          <el-col :span="24">
            <el-form-item prop="dateRange">
              <el-date-picker
                :picker-options="{ shortcuts: G_datePickerShortcuts }"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                size="small"
                v-model="searchInfo.dateRange"
                class="input"
                style="width: 250px"
                readonly
                @change="notificationBtn"
              >
              </el-date-picker>
            </el-form-item>

            <el-form-item label="单位" prop="companyCode">
              <el-cascader
                ref="cascader_ref"
                v-model="companyCasList"
                :filter-method="filterMethod"
                :options="companyList"
                :props="{ multiple: false }"
                clearable
                filterable
                size="small"
                collapse-tags
                @change="companyChange"
                style="width: 400px"
              >
              </el-cascader>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="main">
      <div class="main-right">
        <div class="right-btn">
          <div>
            <!-- <el-button size="small" @click="notificationBtn">告知书</el-button> -->
            单位年度报告分析：
          </div>
          <div>
            <el-button
              size="small"
              @click="downLoadWord(false)"
              :loading="downloadLoading"
              >下载pdf</el-button
            >
            <el-button
              size="small"
              @click="downLoadWord(true)"
              :loading="downloadLoading"
              >下载docx</el-button
            >
          </div>
        </div>
        <div class="right-content" v-loading="loadingShow">
          <embed
            type="application/pdf"
            width="100%"
            :src="blobObj + '#toolbar=0'"
            height="100%"
            v-show="blobObj"
          />
          <el-empty :image-size="200" v-if="!blobObj"></el-empty>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { dataUtils } from '@/common';
import { renderAsync } from '@/assets/docx-preview/dist/docx-preview.min.js';
import { mapGetters } from 'vuex';
import PublicTable from '@/components/publicTable';
import { saveAs } from 'file-saver';

export default {
  name: 'companyYearReportAnalyse',
  components: {
    PublicTable
  },
  computed: {
    ...mapGetters(['G_printerList', 'G_datePickerShortcuts'])
  },
  data() {
    return {
      searchInfo: {
        dateRange: [dataUtils.getDate(), dataUtils.getDate()], // 体检时间
        companyCode: '', // 单位代码
        companyTimes: 0 // 单位次数
      },
      companyList: [],
      companyCasList: [],
      blobObj: null,
      companyName: '',
      loadingShow: false,
      downloadLoading: false
    };
  },
  methods: {
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    /**
     * @description: 获取单位及次数
     * @return {*}
     */
    getCompanyWithTimesList() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    /**
     * @description: 单位选择器内容改变回调事件
     * @param {*} val
     * @return {*}
     */
    companyChange(val) {
      const that = this;
      if (!val || val.length === 0) {
        that.searchInfo.companyCode = '';
        that.searchInfo.companyTimes = null;
        that.setDateRangeByCompanyTimes();
        return;
      }
      this.loadingShow = true;
      that.searchInfo.companyCode = that.companyCasList[0];
      that.searchInfo.companyTimes = that.companyCasList[1];
      that.setDateRangeByCompanyTimes();
      this.notificationBtn();
    },
    /**
     * @author: justin
     * @description: 根据单位体检次数所在时间，设置日期范围
     * @return {*}
     */
    setDateRangeByCompanyTimes() {
      if (
        !this.searchInfo.companyCode ||
        !this.searchInfo.companyTimes ||
        this.searchInfo.companyTimes.length == 0
      ) {
        this.searchInfo.dateRange = [dataUtils.getDate(), dataUtils.getDate()];
        return;
      }

      const company = this.companyList.find(
        (x) => x.value == this.searchInfo.companyCode
      );
      if (!company) return;

      let dateArr = [];
      company.children
        .filter((x) => this.searchInfo.companyTimes == x.value)
        .forEach((x) => {
          dateArr.push(new Date(x.item.beginDate || Date())?.getTime());
          dateArr.push(new Date(x.item.endDate || Date())?.getTime());
        });
      if (dateArr.length == 0) return;

      const minDate = new Date(Math.min(...dateArr));
      const maxDate = new Date(Math.max(...dateArr));
      this.searchInfo.dateRange = [minDate, maxDate];
    },
    // 获取职业病年度总结报告
    notificationBtn() {
      this.blobObj = '';
      this.getFileFun('pdf').then((r) => {
        let blob = new Blob([r.data], { type: 'application/pdf' });
        this.blobObj = URL.createObjectURL(blob);
      });
    },
    // 下载word
    downLoadWord(isWord) {
      if (!this.blobObj) {
        this.$message({
          message: `请先选择单位和体检次数！`,
          type: 'warning'
        });
        return;
      }
      this.downloadLoading = true;
      if (isWord) {
        let downloadLink;
        this.getFileFun('docx').then((r) => {
          downloadLink = new Blob([r.data], {
            type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
          });
          saveAs(downloadLink, `${this.companyName} 年度报告分析`);
        });
      } else {
        saveAs(this.blobObj, `${this.companyName} 年度报告分析`);
        this.downloadLoading = false;
      }
    },
    // 获取文件的通用函数
    getFileFun(repFileType) {
      return new Promise((resolve, reject) => {
        this.searchInfo.beginDate = this.searchInfo.dateRange[0];
        this.searchInfo.endDate = this.searchInfo.dateRange[1];
        this.companyName = '';
        this.companyList.some((item) => {
          if (item.value === this.searchInfo.companyCode) {
            this.companyName = item.label;
            return true;
          }
        });
        let datas = {
          startTime: this.searchInfo.dateRange[0],
          endTime: this.searchInfo.dateRange[1],
          companyName: this.companyName,
          companyCode: this.searchInfo.companyCode,
          companyTimes: this.searchInfo.companyTimes,
          repFileType
        };
        this.$ajax
          .post(this.$apiUrls.DownloadCompanyYearAnalyseFile, datas, {
            responseType: 'arraybuffer'
          })
          .then((r) => {
            if (r.data.byteLength === 0) {
              this.$message({
                message: `该单位次数暂无年度报告分析 ！`,
                type: 'warning'
              });
              this.blobObj = '';
              reject('该单位次数暂无年度报告分析 ！');
              return;
            }
            resolve(r);
          })
          .finally((r) => {
            this.loadingShow = false;
            this.downloadLoading = false;
          });
      });
    }
  },
  created() {
    this.getCompanyWithTimesList();
  },
  mounted() {
    console.log(renderAsync);
  }
};
</script>

<style lang="less" scoped>
.companyYearReportAnalyse_page {
  display: flex;
  flex-direction: column;
  .header-wrapper {
    background: #fff;
    padding: 0 5px 5px;
    .el-form-item {
      margin-bottom: 0 !important;
    }
  }
  .title_wrap {
    display: flex;
    justify-content: space-between;
  }
  .main {
    flex: 1;
    display: flex;
    overflow: auto;
    padding-top: 5px;
  }
  .main-left,
  .main-right {
    background: #fff;
    border-radius: 4px;
    padding: 5px;
    overflow: auto;
  }
  .main-left {
    width: 352px;
    margin-right: 5px;
  }
  .main-right {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .left-table {
    height: 100%;
    border: 1px solid rgba(178, 190, 195, 0.5);
    border-radius: 4px;
  }
  .right-btn {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    justify-content: space-between;
    // padding: 0 20px;
  }
  .btn-item {
    display: flex;
    align-items: center;
    margin-right: 10px;
    &:nth-child(1) {
      flex: 1;
    }
    &:nth-child(2) {
      flex: 2;
    }
    &:nth-child(3) {
      flex: 2;
    }
    &:nth-child(4) {
      flex: 3;
    }
    label {
      width: 82px;
      text-align: right;
      margin-right: 10px;
      font-size: 14px;
      font-weight: 600;
    }
  }
  .right-content {
    flex: 1;
    flex-shrink: 0;
    background: rgba(23, 112, 223, 0.1);
    border-radius: 4px;
    display: flex;
    overflow: auto;
    justify-content: center;
    .docx_dom {
      width: 100%;
    }
  }
}
</style>
