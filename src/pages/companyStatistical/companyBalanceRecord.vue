<template>
  <div class="companyBalanceRecord_page">
    <header>
      <el-row :gutter="10" type="flex" justify="space-between" align="middle">
        <el-col :span="10">
          <h4>结算记录列表</h4>
        </el-col>

        <el-col :span="10">
          <label>结算日期：</label>
          <el-date-picker
            :picker-options="{ shortcuts: G_datePickerShortcuts }"
            size="small"
            v-model="dateVal"
            type="daterange"
            range-separator="-"
            :clearable="false"
            value-format="yyyy-MM-dd"
            @change="GetCompanySettlement"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-col>

        <el-col :span="4">
          <ButtonGroup
            :btnList="['统计', '导出']"
            @statistics="GetCompanySettlement"
            @exports="exportTable"
          >
          </ButtonGroup>
        </el-col>
      </el-row>
    </header>
    <div class="table_wrap">
      <PublicTable
        :theads="theads"
        :viewTableList.sync="balanceRecordList"
        :columnWidth="columnWidth"
        :cell_red="['originalPrice', 'actuallyPrice']"
        :tableCellClassName="tableCellClassName"
        :tableLoading="tableLoading"
      >
        <template #payStatus="{ scope }">
          <div>
            {{ G_EnumList['PayStatus'][scope.row.payStatus] }}
          </div>
        </template>
        <template #feeType="{ scope }">
          <div>
            {{ feeType_enum[scope.row.feeType] }}
          </div>
        </template>
        <template #caluateType="{ scope }">
          <div>
            {{ caluateType_enum[scope.row.caluateType] }}
          </div>
        </template>
      </PublicTable>
    </div>
  </div>
</template>
<script>
import ButtonGroup from './components/buttonGroup.vue';
import PublicTable from '@/components/publicTable';
import { mapGetters } from 'vuex';
import { dataUtils } from '@/common';
import { export2Excel } from '@/common/excelUtil';
import moment from 'moment';
export default {
  name: 'companyBalanceRecord',
  components: {
    ButtonGroup,
    PublicTable
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_datePickerShortcuts'])
  },
  data() {
    return {
      dateVal: [
        moment().add(-7, 'd').format('YYYY-MM-DD'),
        moment().startOf('day').format('YYYY-MM-DD')
      ],
      theads: {
        billSeqNo: '当次结算流水号',
        companyCode: '单位代码',
        companyTimes: '单位次数',
        billTimes: '结算次数',
        billPersonCount: '体检人数',
        taxID: '社会信用代码',
        invoiceName: '发票名称',
        // invoiceId:'发票ID',
        // invoiceNo:'发票编号',
        electronicInvoiceNo: '电子发票号',
        sendOperator: '姓名（工号）',
        createTime: '创建时间',
        payStatus: '支付状态',
        chargeTime: '结算时间',
        feeType: '订单状态',
        originalPrice: '原始金额',
        actuallyPrice: '实收金额',
        caluateType: '结算方式',
        auditOperator: '审核人',
        auditTime: '审核时间',
        chargeDateScope: '统计时间段',
        note: '备注'
      },
      feeType_enum: {
        0: '正常',
        1: '待退费',
        2: '已退费'
      },
      caluateType_enum: {
        0: '按登记时间',
        1: '按审核时间'
      },
      balanceRecordList: [],
      columnWidth: {
        createTime: 155,
        chargeTime: 155,
        auditTime: 155,
        invoiceName: 150,
        caluateType: 90,
        sendOperator: 120,
        taxID: 150,
        chargeDateScope: 155,
        note: 150,
        electronicInvoiceNo: 100
      },
      tableLoading: false
    };
  },
  methods: {
    // 获取结算记录
    GetCompanySettlement() {
      let datas = {
        beginTime: this.dateVal[0],
        endTime: this.dateVal[1]
      };
      this.tableLoading = true;
      this.$ajax
        .paramsPost(this.$apiUrls.QueryCompanySettlementByChargeTime, datas)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.balanceRecordList = returnData;
        })
        .finally((_) => {
          this.tableLoading = false;
        });
    },
    tableCellClassName({ row, column, rowIndex, columnIndex }) {
      if (column.property == 'invoiceName') {
        return {
          cursor: 'pointer'
        };
      }
    },
    //导出
    exportTable() {
      if (this.balanceRecordList.length == 0) {
        this.$message({
          message: '费用明细列表不能为空！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm(
        `确定导出${this.dateVal[0]} - ${this.dateVal[1]}结算记录吗?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        let columns = [];
        Object.keys(this.theads).map((item) => {
          columns.push({
            title: this.theads[item],
            key: item
          });
        });
        let data = dataUtils.deepCopy(this.balanceRecordList);
        data.map((item) => {
          item.payStatus = this.G_EnumList['PayStatus'][item.payStatus];
          item.feeType = this.feeType_enum[item.feeType];
          item.caluateType = this.caluateType_enum[item.caluateType];
        });
        const title = `${this.dateVal[0]} - ${this.dateVal[1]}结算记录`;
        this.$nextTick(() => {
          export2Excel(columns, data, title);
        });
      });
    }
  },
  mounted() {
    this.GetCompanySettlement();
  }
};
</script>
<style lang="less" scoped>
.companyBalanceRecord_page {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
  header {
    padding-bottom: 5px;
  }
  .table_wrap {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
  }
  /deep/.dialog_content {
    height: 500px;
    display: flex;
    flex-direction: column;
    overflow: auto;
    .dialog_search {
      display: flex;
      justify-content: flex-end;
      margin-bottom: 5px;
    }
    .dialog_table {
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
    }
    .feeDetail_bottom {
      display: flex;
      justify-content: flex-end;
      li {
        font-size: 20px;
        & + li {
          margin-left: 20px;
        }
        i {
          font-style: normal;
          color: #d63031;
        }
      }
    }
  }
  .btn_wrap {
    .el-button + .el-button {
      margin-left: 3px;
    }
  }
  /deep/.search_class {
    td {
      background: #49abf3 !important;
    }
  }
}
</style>
