<template>
  <!-- 单位人员汇总报表 -->
  <div class="companyPersonnelSummary">
    <div class="main">
      <div class="searchBar">
        <div class="search-list">
          <div class="list-item">
            <span style="width: 87px">统计时间</span>
            <el-date-picker
              :picker-options="{ shortcuts: G_datePickerShortcuts }"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              v-model="searchInfo.date"
              :clearable="false"
              @change="statistics"
            >
            </el-date-picker>
          </div>
          <div class="list-item">
            <span style="width: 36px">单位</span>
            <el-cascader
              ref="company_cascader_ref"
              v-model="searchInfo.companyCode"
              :filter-method="filterMethod"
              :options="companyList"
              :props="{ multiple: false }"
              clearable
              filterable
              size="small"
              collapse-tags
              @change="companyChange"
            >
            </el-cascader>
          </div>
          <!-- <div class="list-item">
            <span style="width: 36px">套餐</span>
            <el-select
              placeholder="请选择"
              size="small"
              filterable
              clearable
              v-model="searchInfo.clusCode"
              class="input"
            >
              <el-option
                v-for="item in clusterList"
                :key="item.clusCode"
                :label="item.clusName"
                :value="item.clusCode"
              >
              </el-option>
            </el-select>
          </div> -->
          <div class="list-item">
            <span style="width: 36px">院区</span>
            <el-select
              placeholder="请选择"
              size="small"
              clearable
              v-model="searchInfo.hospCode"
              class="input"
            >
              <!-- <el-option
                v-for="item in companyList"
                :key="item.companyCode"
                :label="item.companyName"
                :value="item.companyCode"
              >
              </el-option> -->
            </el-select>
          </div>
          <div class="list-item">
            <span style="width: 35px">外检</span>
            <el-select
              placeholder="请选择"
              size="small"
              clearable
              v-model="searchInfo.out"
              class="input"
            >
              <!-- <el-option
                v-for="item in companyList"
                :key="item.companyCode"
                :label="item.companyName"
                :value="item.companyCode"
              >
              </el-option> -->
            </el-select>
          </div>
          <div class="list-item">
            <el-checkbox v-model="searchInfo.dateShow">分日期显示</el-checkbox>
          </div>
        </div>
        <div class="search-list">
          <div class="list-item">
            <span style="width: 72px; text-align: right">状态</span>
            <el-select
              placeholder="请选择"
              size="small"
              v-model="searchInfo.vipNormal"
              class="input"
            >
              <el-option label="全部" :value="0"></el-option>
              <el-option label="VIP" :value="1"></el-option>
              <el-option label="普通" :value="2"></el-option>
            </el-select>
          </div>
          <ButtonGroup :btnList="['统计', '导出']" @statistics="statistics" />
        </div>
        <div class="title-wrap">
          <h3>单位人员汇总报表:</h3>
        </div>
      </div>
      <div class="main-table">
        <div class="table">
          <PublicTable
            :theads="theads"
            :viewTableList.sync="dataInfo.report"
          ></PublicTable>
        </div>
        <div class="total">
          <span>人数合计：</span>
          <span>&nbsp;</span>
          <span>&nbsp;</span>
          <span>{{ dataInfo.totalPeCount }}</span>
          <span>{{ dataInfo.totalBookCount }}</span>
          <span>{{ dataInfo.totalBookActiveCount }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ButtonGroup from './components/buttonGroup.vue';
import PublicTable from '@/components/publicTable.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
export default {
  name: 'companyPersonnelSummary',
  components: {
    ButtonGroup,
    PublicTable
  },
  data() {
    return {
      searchInfo: {
        date: [new Date(), new Date()],
        companyCode: '',
        hospCode: '',
        vipNormal: 0
      },
      theads: {
        companyCode: '单位编码',
        companyName: '单位',
        peCount: '体检人数',
        bookCount: '预约人数',
        bookActiveCount: '预约到检'
      },
      dataInfo: {
        totalPeCount: 0,
        totalBookCount: 0,
        totalBookActiveCount: 0,
        report: []
      },
      companyList: [],
      clusterList: []
    };
  },
  computed: {
    ...mapGetters(['G_datePickerShortcuts'])
  },
  created() {
    this.getCompany();
    this.getCluster();
  },
  methods: {
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    /// 获取单位
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) return;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.searchInfo.date = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.statistics();
    },
    // 获取套餐
    getCluster() {
      this.$ajax.post(this.$apiUrls.Cluster).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.clusterList = returnData;
      });
    },
    // 查询
    statistics() {
      let [beginDate, endDate] = this.searchInfo.date;
      let data = {
        beginDate: dataUtils.dateToString(beginDate),
        endDate: dataUtils.dateToString(endDate),
        companyCode: this.searchInfo.companyCode[0],
        hospCode: this.searchInfo.hospCode,
        vipNormal: this.searchInfo.vipNormal
      };
      this.$ajax
        .post(this.$apiUrls.CompanyPersonnelSummaryReport, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.dataInfo = returnData;
        });
    }
  }
};
</script>

<style lang="less" scoped>
.companyPersonnelSummary {
  color: #2d3436;
  .main {
    height: 100%;
    background: #fff;
    padding: 18px 10px;
    display: flex;
    flex-direction: column;
  }
  .search-list {
    display: flex;
    align-items: center;
    margin-bottom: 18px;
  }
  .list-item {
    display: flex;
    align-items: center;
    margin-right: 20px;
    width: 300px;
    &:last-child {
      margin-right: 0;
    }
    span {
      font-size: 14px;
      font-weight: 600;
      margin-right: 10px;
    }
  }
  .input {
    width: 100%;
  }
  .title-wrap {
    margin-bottom: 18px;
    h3 {
      font-size: 18px;
    }
  }
  .main-table {
    border: 1px solid rgba(178, 190, 195, 0.5);
    border-radius: 4px;
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .table {
    flex: 1;
    overflow: auto;
  }
  .total {
    background: #cbddf5;
    font-size: 14px;
    font-weight: 600;
    padding: 10px;
    display: flex;
    align-items: center;
    span {
      flex: 1;
      &:nth-child(4) {
        padding-right: 60px;
      }
      &:nth-child(5) {
        padding-right: 60px;
      }
      &:nth-child(6) {
        padding-right: 40px;
      }
    }
  }
}
</style>
