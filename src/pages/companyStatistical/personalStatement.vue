<template>
  <!-- 个人报表（日结、月结）：按缴费时间显示流水 -->
  <div class="personalStatement">
    <div class="main">
      <div class="searchBar">
        <div class="search-list">
          <div class="list-item">
            <span style="width: 87px">统计时间</span>
            <el-date-picker
              :picker-options="{ shortcuts: G_datePickerShortcuts }"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              v-model="searchInfo.date"
              :clearable="false"
              @change="statistics"
            >
            </el-date-picker>
          </div>
          <div class="list-item">
            <span style="width: 36px">套餐</span>
            <el-select
              placeholder="请选择"
              size="small"
              filterable
              clearable
              v-model="searchInfo.clusCode"
              class="input"
              @change="statistics"
            >
              <el-option
                v-for="item in clusterList"
                :key="item.clusCode"
                :label="item.clusName"
                :value="item.clusCode"
              >
              </el-option>
            </el-select>
          </div>
          <div class="list-item" v-if="G_config.physicalMode.includes('普检')">
            <span style="width: 72px">体检分类</span>
            <el-select
              placeholder="请选择"
              size="small"
              clearable
              v-model="searchInfo.peCls"
              class="input"
              @change="statistics"
            >
              <el-option
                v-for="item in G_peClsList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="search-list">
          <div class="list-item">
            <span style="width: 72px">是否VIP</span>
            <el-select
              placeholder="请选择"
              size="small"
              v-model="searchInfo.vipNormal"
              class="input"
              @change="statistics"
            >
              <el-option label="全部" :value="0"></el-option>
              <el-option label="是" :value="1"></el-option>
              <el-option label="否" :value="2"></el-option>
            </el-select>
          </div>
          <div class="list-item">
            <span style="width: 72px">预约类型</span>
            <el-select
              placeholder="请选择"
              size="small"
              v-model="searchInfo.bookType"
              class="input"
              @change="statistics"
            >
              <el-option label="全部" :value="-1"></el-option>
              <el-option label="现场登记" :value="0"></el-option>
              <el-option label="电话预约" :value="1"></el-option>
              <el-option label="团体导入" :value="2"></el-option>
              <el-option label="微信预约" :value="3"></el-option>
            </el-select>
          </div>
          <ButtonGroup
            :btnList="['统计', '导出']"
            @statistics="statistics"
            @exports="exportTable"
          />
        </div>
        <div class="title-wrap">
          <h3>体检人员缴费列表:</h3>
          <span>合计人数：{{ totalInfo.totalCount }}</span>
          <span>合计金额：{{ totalInfo.totalPrice }}</span>
        </div>
      </div>
      <div class="main-table">
        <PublicTable
          :isSortShow="false"
          :theads="theads"
          :viewTableList.sync="tableData"
          :columnWidth="{ companyName: 220, activeTime: 180 }"
        >
          <template #sex="{ scope }">
            <div>
              {{ G_EnumList['Sex'][scope.row.sex] }}
            </div>
          </template>
        </PublicTable>
      </div>
    </div>
  </div>
</template>

<script>
import ButtonGroup from './components/buttonGroup.vue';
import PublicTable from '@/components/publicTable.vue';
import { mapGetters } from 'vuex';
import { dataUtils } from '@/common';
import { export2Excel } from '@/common/excelUtil';
import moment from 'moment';
export default {
  name: 'personalStatement',
  components: {
    ButtonGroup,
    PublicTable
  },
  data() {
    return {
      searchInfo: {
        date: [new Date(), new Date()],
        clusCode: '',
        peCls: '',
        vipNormal: 0,
        bookType: -1
      },
      theads: {
        regNo: '体检号',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        tel: '电话',
        activeTime: '体检日期',
        chargeTime: '支付时间',
        price: '总金额'
      },
      tableData: [],
      companyList: [],
      clusterList: [],
      totalInfo: {
        totalCount: 0,
        totalPrice: 0
      },
      printData: []
    };
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_peClsList',
      'G_datePickerShortcuts',
      'G_config'
    ])
  },
  created() {
    this.getCluster();
  },
  methods: {
    // 获取套餐
    getCluster() {
      this.$ajax.post(this.$apiUrls.Cluster).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.clusterList = returnData;
      });
    },
    // 查询
    statistics() {
      let [beginDate, endDate] = this.searchInfo.date;
      let data = {
        beginDate: dataUtils.dateToString(beginDate),
        endDate: dataUtils.dateToString(endDate),
        peType: this.searchInfo.peType,
        clusCode: this.searchInfo.clusCode,
        peCls: this.searchInfo.peCls === '' ? -1 : this.searchInfo.peCls,
        vipNormal: this.searchInfo.vipNormal,
        bookType: this.searchInfo.bookType
      };
      this.tableData = [];
      this.printData = [];
      this.$ajax.post(this.$apiUrls.HisBillListQuery, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.tableData = returnData.feeList || [];
        this.printData = returnData.feeList || [];
        this.totalInfo.totalCount = returnData.totalCount;
        this.totalInfo.totalPrice = returnData.totalPrice;
      });
    },
    //导出
    exportTable() {
      if (this.printData.length == 0) {
        this.$message.warning('请选择至少一条数据进行操作!');
        return;
      }
      this.$confirm('确定导出列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let columns = [];
        let theads = { index: '序号', ...this.theads };
        Object.keys(theads).map((item) => {
          columns.push({
            title: theads[item],
            key: item
          });
        });
        let data = dataUtils.deepCopy(this.printData);
        data.map((item, index) => {
          return (item.index = index + 1);
        });
        let pushJson = {
          regNo: '',
          name: '',
          sex: '',
          age: '',
          tel: '',
          activeTime: '合计人数：' + this.totalInfo.totalCount,
          price: '合计金额：' + this.totalInfo.totalPrice
        };
        data.push(pushJson);
        const title = '体检人员缴费列表' + moment().format('YYYY-MM-DD');
        this.$nextTick(() => {
          export2Excel(columns, data, title);
        });
      });
    }
  }
};
</script>

<style lang="less" scoped>
.personalStatement {
  color: #2d3436;
  .main {
    height: 100%;
    background: #fff;
    padding: 18px 10px;
    display: flex;
    flex-direction: column;
  }
  .search-list {
    display: flex;
    align-items: center;
    margin-bottom: 18px;
  }
  .list-item {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 20px;
    width: 300px;
    &:last-child {
      margin-right: 0;
    }
    span {
      font-size: 14px;
      font-weight: 600;
      margin-right: 10px;
    }
  }
  .input {
    width: 100%;
  }
  .title-wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 14px;
    h3 {
      font-size: 18px;
    }
    span {
      font-size: 14px;
    }
  }
  .main-table {
    border: 1px solid rgba(178, 190, 195, 0.5);
    border-radius: 4px;
    flex: 1;
    overflow: auto;
  }
}
</style>
