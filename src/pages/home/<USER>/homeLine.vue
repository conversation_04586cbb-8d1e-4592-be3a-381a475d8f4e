<template>
  <div class="homeLine">
    <!-- 折线图 -->
    <div class="lineLeft">
      <p>近期已报到人数：</p>
      <div id="drawChart" class="chart"></div>
    </div>
    <div class="lineRight">
      <p>近期未完成人数：</p>
      <div id="drawChart2" class="chart2"></div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
require('echarts/theme/macarons');
export default {
  name: 'homeLine',
  props: {
    // xAxisData: {
    //   type: Array,
    //   default: [],
    // },
    // seriesData: {
    //   type: Array,
    //   default: [],
    // },
    // activedDayNums: {
    //   type: Object,
    //   default: { xAxisData: [], seriesData: [] },
    // },
    // undoneDayNums: {
    //   type: Object,
    //   default: { xAxisData: [], seriesData: [] },
    // },
    loading: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    //获取折线图数据
    getLineDta(lineData1, lineData2) {
      this.$nextTick(() => {
        this.drawChart(lineData1);
        this.drawChart2(lineData2);
      });
    },
    // 近期未完成人数
    drawChart(lineData1) {
      let myChart = echarts.getInstanceByDom(
        document.getElementById('drawChart')
      );
      //如果为空 则正常进行渲染 反之 不再进行初始化
      if (myChart == null) {
        myChart = echarts.init(document.getElementById('drawChart'));
      }
      myChart.setOption({
        color: ['#1770DF'],
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '42',
          bottom: '30',
          top: '32',
          right: '22'
        },
        xAxis: {
          data: lineData1.xAxisData,
          axisTick: {
            //y轴刻度线
            show: false
          },
          axisLabel: {
            color: '#2D3436'
          },
          axisLine: {
            //x轴
            lineStyle: {
              color: '#B2BEC3'
            }
          }
        },
        yAxis: {
          type: 'value',
          axisTick: {
            //y轴刻度线
            show: false
          },
          axisLine: {
            //y轴
            show: true
          },
          splitLine: {
            // y轴虚线
            show: true,
            lineStyle: {
              type: 'dashed',
              color: '#B2BEC3'
            }
          }
        },
        series: [
          {
            data: lineData1.seriesData,
            type: 'line',
            smooth: true,
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  // 1代表上面
                  offset: 1,
                  color: 'rgba(18,81,162,0)'
                },
                {
                  offset: 0,
                  color: 'rgba(18,81,162,0.4)'
                }
              ]),
              opacity: 1 // 填充区域透明度
            }
          }
        ]
      });
      // 浏览器大小调整echarts自适应
      window.addEventListener('resize', () => {
        if (myChart) {
          myChart.resize();
        }
      });
    },
    // 近期已报到人数
    drawChart2(lineData2) {
      let myChart = echarts.getInstanceByDom(
        document.getElementById('drawChart2')
      );
      //如果为空 则正常进行渲染 反之 不再进行初始化
      if (myChart == null) {
        myChart = echarts.init(document.getElementById('drawChart2'));
      }
      myChart.setOption({
        color: ['#D63031'],
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '42',
          bottom: '30',
          top: '32',
          right: '22'
        },
        xAxis: {
          data: lineData2.xAxisData,
          axisTick: {
            //y轴刻度线
            show: false
          },
          axisLabel: {
            color: '#2D3436',

            rich: {
              a: {
                height: 14 // 设置字体行高
              }
            }
          },
          axisLine: {
            //x轴
            lineStyle: {
              color: '#B2BEC3'
            }
          }
        },
        yAxis: {
          type: 'value',
          axisTick: {
            //y轴刻度线
            show: false
          },
          axisLine: {
            //y轴
            show: true
          },
          splitLine: {
            // y轴虚线
            show: true,
            lineStyle: {
              type: 'dashed',
              color: '#B2BEC3'
            }
          }
        },
        series: [
          {
            data: lineData2.seriesData,
            type: 'line',
            smooth: true,
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  // 1代表上面
                  offset: 1,
                  color: 'rgba(214,48,49,0)'
                },
                {
                  offset: 0,
                  color: 'rgba(150,30,31,0.4)'
                }
              ]),
              opacity: 1 // 填充区域透明度
            }
          }
        ]
      });
      // 浏览器大小调整echarts自适应
      window.addEventListener('resize', () => {
        if (myChart) {
          myChart.resize();
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.homeLine {
  display: flex;
  flex-direction: row;
  .lineLeft {
    flex: 1;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    height: 280px;
    margin-right: 10px;
    background: #fff;
    padding: 10px;
    border-radius: 4px;
    box-shadow:
      0 2px 4px rgba(0, 0, 0, 0.12),
      0 0 6px rgba(0, 0, 0, 0.04);
  }
  .lineRight {
    flex: 1;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    height: 280px;
    background: #fff;
    padding: 10px;
    border-radius: 4px;
    box-shadow:
      0 2px 4px rgba(0, 0, 0, 0.12),
      0 0 6px rgba(0, 0, 0, 0.04);
  }
  #drawChart,
  #drawChart2 {
    flex: 1;
  }
}
</style>
