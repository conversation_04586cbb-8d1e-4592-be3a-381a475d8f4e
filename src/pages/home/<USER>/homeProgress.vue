<template>
  <div class="homeProgress">
    <!-- 进度条组件 -->
    <div class="pie-wrap">
      <div class="pie-item">
        <p>已预约/预约报到人数：</p>
        <div class="progressDiv">
          <el-progress
            type="circle"
            :percentage="
              bookedNumInfo.bookedActivedPercent
                ? formatData(bookedNumInfo.bookedActivedPercent)
                : 0
            "
            :width="120"
            color="#1770DF"
            :stroke-width="12.0"
          ></el-progress>
          <div>
            <div class="male">
              <span class="gray">已预约人数：</span>
              <span class="blackText">{{ bookedNumInfo.bookedNum }}人</span>
            </div>
            <div class="female">
              <span class="gray">预约报到人数：</span>
              <span class="blueText"
                >{{ bookedNumInfo.bookedActivedNum }}人</span
              >
            </div>
          </div>
        </div>
      </div>
      <div class="pie-item">
        <p>报到男女比例：</p>
        <div class="progressDiv">
          <div id="sexRatio" class="sexRatio-chart"></div>
          <div>
            <div class="male">
              <span class="iconfont icon-nan icon-text"> 男性：</span>
              <span class="numb">{{ activedSexInfo.maleNum }}人</span>
            </div>
            <div class="female">
              <span class="iconfont icon-nv icon-text"> 女性：</span>
              <span class="numb">{{ activedSexInfo.femaleNum }}人</span>
            </div>
          </div>
        </div>
      </div>
      <div class="pie-item">
        <p>个人/团体报到人数对比：</p>
        <div class="progressDiv">
          <div id="numRatio" class="sexRatio-chart"></div>
          <div>
            <div class="male">
              <span class="gray"> 个人报到人数：</span>
              <span class="yellowText"
                >{{ activedPersonCompanyInfo.persionNum }}人</span
              >
            </div>
            <div class="female">
              <span class="gray"> 团体报到人数：</span>
              <span class="greenText"
                >{{ activedPersonCompanyInfo.companyNum }}人</span
              >
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
require('echarts/theme/macarons');
export default {
  name: 'homeProgress',
  props: {
    loading: {
      type: Boolean,
      default: true
    },
    bookedNumInfo: {
      type: Object,
      default: { bookedActivedNum: 0, bookedNum: 0 }
    },
    activedSexInfo: {
      type: Object,
      default: {
        maleNum: 0,
        femaleNum: 0
      }
    },
    activedPersonCompanyInfo: {
      type: Object,
      default: {
        persionNum: 0,
        companyNum: 0,
        persionPercent: 0,
        companyPercent: 0
      }
    }
  },

  methods: {
    //获取性别比,个人/团体报到人数对比数据
    getProgressData(proData1, proData2) {
      this.$nextTick(() => {
        this.progressChart(proData1);
        this.progressChart2(proData2);
      });
    },
    // 报道男女比例
    progressChart(proData1) {
      let myChart = echarts.getInstanceByDom(
        document.getElementById('sexRatio')
      );
      //如果为空 则正常进行渲染 反之 不再进行初始化
      if (myChart == null) {
        myChart = echarts.init(document.getElementById('sexRatio'));
      }
      myChart.setOption({
        tooltip: {
          trigger: 'item'
        },
        color: ['#1770DF', '#FF6D6E'],
        series: [
          {
            name: '报道男女比例',
            type: 'pie',
            radius: 60,
            label: {
              position: 'inner',
              fontSize: 14,
              color: '#fff',
              formatter: '{a|{b}}{a|\n{d}%}',
              rich: {
                a: {
                  fontSize: 14,
                  lineHeight: 20
                }
              }
            },
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 4
            },
            data: [
              { value: proData1.maleNum, name: '男' },
              { value: proData1.femaleNum, name: '女' }
            ],
            labelLine: {
              show: false
            }
          }
        ]
      });
      // 浏览器大小调整echarts自适应
      window.addEventListener('resize', () => {
        if (myChart) {
          myChart.resize();
        }
      });
    },
    //个人/团体报到人数对比
    progressChart2(proData2) {
      let myChart = echarts.getInstanceByDom(
        document.getElementById('numRatio')
      );
      //如果为空 则正常进行渲染 反之 不再进行初始化
      if (myChart == null) {
        myChart = echarts.init(document.getElementById('numRatio'));
      }
      myChart.setOption({
        tooltip: {
          trigger: 'item',
          confine: true
        },
        color: ['#FAB63B', '#3CB34F'],
        series: [
          {
            name: '报道男女比例',
            type: 'pie',
            radius: 60,
            label: {
              position: 'inner',
              fontSize: 14,
              color: '#fff'
            },
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 4
            },
            data: [
              { value: proData2.persionNum, name: '个人' },
              { value: proData2.companyNum, name: '团体' }
            ],
            labelLine: {
              show: false
            }
          }
        ]
      });
      // 浏览器大小调整echarts自适应
      window.addEventListener('resize', () => {
        if (myChart) {
          myChart.resize();
        }
      });
    },
    // 格式化数据
    formatData(value) {
      return Number(value.slice(0, -1));
    }
  }
};
</script>

<style lang="less" scoped>
.homeProgress {
  display: inline-block;
  width: 100%;
  display: flex;
  flex-direction: row;
  .progress-box {
    flex: 1;
    display: flex;
    align-items: flex-end;
    padding: 18px;
    color: #2d3436;
    font-family: PingFangSC-Regular;
    background: #fff;
    border-radius: 4px;
    display: flex;
    flex-direction: row;
  }
  .progress-info {
    flex: 1;
  }
  .title {
    opacity: 0.6;
    font-size: 14px;
    margin-bottom: 10px;
  }
  .number {
    font-size: 42px;
    span {
      font-size: 14px;
      margin-left: 10px;
    }
  }
  .pie-wrap {
    display: flex;
    flex-direction: row;
    width: 100%;
  }
  .pie-item {
    background: #fff;
    border-radius: 4px;
    padding: 10px;
    margin-right: 10px;
    flex: 1;
    display: flex;
    flex-direction: column;
    box-shadow:
      0 2px 4px rgba(0, 0, 0, 0.12),
      0 0 6px rgba(0, 0, 0, 0.04);

    &:last-child {
      margin-right: 0;
    }

    .progressDiv {
      flex: 1;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      margin: 36px 10px;
    }
  }
  .sexRatio-chart {
    width: 160px;
    height: 126px;
  }
  .pie-text {
    display: flex;
    justify-content: center;
    align-items: center;
    span {
      font-weight: normal;
    }
  }
  .male {
    color: #1770df;
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
  }
  .female {
    color: #ff6d6e;
    display: flex;
    flex-direction: column;
  }
  .numb {
    font-size: 18px;
  }
  .icon-text {
    margin-right: 20px;
    padding-bottom: 8px;
    font-size: 14px;
  }
  /deep/.el-skeleton {
    display: flex;
  }
  .classify-chart {
    width: 100%;
    // height: 200px;
  }
  .gray {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: rgba(45, 52, 54, 0.6);
    padding-bottom: 8px;
  }
  .yellowText {
    color: #fab63b;
    font-size: 18px;
  }
  .greenText {
    color: #3cb34f;
    font-size: 18px;
  }
  .blueText {
    color: #4a95f4;
    font-size: 18px;
  }
  .blackText {
    color: #2d3436;
    font-size: 18px;
  }
}
</style>
