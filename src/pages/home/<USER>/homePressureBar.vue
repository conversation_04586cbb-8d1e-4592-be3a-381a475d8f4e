<template>
  <div class="homePressureBar">
    <!-- 压力图 -->
    <p>各个科室接待压力：</p>
    <div id="pressure" class="chart"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
require('echarts/theme/macarons');
export default {
  name: 'homePressureBar',
  props: {
    loading: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    //获取柱形数据
    getBarDta(barData) {
      this.$nextTick(() => {
        this.barChart(barData);
      });
    },
    barChart(barData) {
      let myChart = echarts.getInstanceByDom(
        document.getElementById('pressure')
      );
      //如果为空 则正常进行渲染 反之 不再进行初始化
      if (myChart == null) {
        myChart = echarts.init(document.getElementById('pressure'));
      }
      myChart.setOption({
        // title: {
        //   text: "各科室接待压力",
        // },
        // dataset: barData.dataset,
        dataset: [
          {
            dimensions: ['value', 'deptName'],
            source: barData
            // [
            //   [12, "口腔科"],
            //   [11, "耳鼻喉科"],
            //   [10, "彩超一室(男)"],
            //   [2, "核磁共振检查室"],
            //   [9, "CT检查室"],
            //   [8, "骨密度检查室"],
            //   [26, "外科"],
            //   [34, "抽血室"],
            //   [18, "彩超一室(女)"],
            //   [13, "眼科"],
            //   [11, "彩超二室(男)"],
            //   [29, "内科"],
            //   [17, "彩超二室(女)"],
            //   [16, "彩超三室(女)"],
            // ]
          },
          {
            transform: {
              type: 'sort',
              // 排序
              config: { dimension: 'value', order: 'desc' }
            }
          }
        ],
        grid: {
          left: '0',
          bottom: '160',
          top: '60',
          right: '0'
        },
        tooltip: {},
        xAxis: {
          type: 'category',
          axisLabel: {
            formatter: function (value) {
              //x轴的文字改为竖版显示
              return `{a|${value
                .replace('(', '')
                .replace(')', '')
                .split('')
                .join('\n')}}`;
            },
            rich: {
              a: {
                height: 14 // 设置字体行高
              }
            },
            color: '#2D3436'
          },
          axisTick: {
            //y轴刻度线
            show: false
          },
          axisLine: {
            //x轴
            lineStyle: {
              color: '#B2BEC3'
            }
          }
        },
        yAxis: {
          type: 'value',
          axisTick: {
            //y轴刻度线
            show: false
          },
          axisLine: {
            //y轴
            show: false
          },
          splitLine: {
            // y轴虚线
            show: false
          },
          axisLabel: {
            show: false
          }
        },
        visualMap: {
          orient: 'horizontal',
          inverse: true,
          top: 'top',
          left: 'right',
          min: 0,
          max: 29,
          text: ['大', '小'],
          dimension: 0,
          inRange: {
            color: ['#1770DF', '#3CB34F', '#FAB63B', '#D63031']
          }
        },
        series: [
          {
            type: 'bar',
            barWidth: 14,
            datasetIndex: 1,
            encode: {
              x: 'deptName',
              y: 'value'
            },
            label: {
              show: true,
              position: 'top',
              valueAnimation: true
            }
          }
        ]
      });
      // 浏览器大小调整echarts自适应
      window.addEventListener('resize', () => {
        if (myChart) {
          myChart.resize();
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.homePressureBar {
  background: #fff;
  border-radius: 4px;
  padding: 10px;
  // margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  height: 410px;
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.12),
    0 0 6px rgba(0, 0, 0, 0.04);
  .chart {
    flex: 1;
  }
}
</style>
