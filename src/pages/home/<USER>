<script setup lang="ts">
import HomeLine from './components/homeLine.vue';
import HomeProgress from './components/homeProgress.vue';
import HomePieDiagram from './components/homePieDiagram.vue';
import {
  ref,
  computed,
  onMounted,
  onActivated,
  onDeactivated,
  onUnmounted,
  nextTick,
  getCurrentInstance
} from 'vue';
import { apiUrls as $apiUrls } from '@/common/apiUrls';
import { ajax as $ajax } from '@/common';

import store from '@/store';

// 定义接口
interface BookedNumInfo {
  bookedActivedPercent: number;
  bookedNum: number;
  bookedActivedNum: number;
}

interface SexInfo {
  maleNum: number;
  femaleNum: number;
}

interface PersonCompanyInfo {
  persionNum: number;
  companyNum: number;
}

interface BookedLocalInfo {
  bookedActivedNum: number;
  localActivedNum: number;
  activedNum: number;
}

interface PeClsInfo {
  activedPeClses: Array<{ value?: number; name?: string }>;
  totalActived: number;
}

interface StatusNumInfo {
  unCheckedNum: number;
  checkingNum: number;
  todayReportedNum: number;
}

interface DayNumsInfo {
  xAxisData: string[];
  seriesData: number[];
}

interface TabItem {
  title: string;
  badge: string;
}

interface InfoItem {
  news: Array<{ isRead?: boolean; [key: string]: any }>;
}

interface DeptActived {
  actvicedNum: number;
  deptName: string;
  [key: string]: any;
}

// 组件引用
const progress = ref(null);
const pieDiagram = ref(null);
const lineChart = ref(null);
const pressureBar = ref(null);

// 响应式数据
const loading = ref(true);
const bookedNumInfo = ref<BookedNumInfo>({
  bookedActivedPercent: 0,
  bookedNum: 0,
  bookedActivedNum: 0
}); // 进度条
const activedSexInfo = ref<SexInfo>({
  maleNum: 0,
  femaleNum: 0
}); // 男女比例
const activedPersonCompanyInfo = ref<PersonCompanyInfo>({
  persionNum: 0,
  companyNum: 0
}); // 个人/团体比
const activedBookedLocalInfo = ref<BookedLocalInfo>({
  bookedActivedNum: 0,
  localActivedNum: 0,
  activedNum: 0
}); // 预约报到/现场报到
const activedPeClsInfo = ref<PeClsInfo>({
  activedPeClses: [],
  totalActived: 0
}); // 各个分类的人数对比
const statusNumInfo = ref<StatusNumInfo>({
  unCheckedNum: 0,
  checkingNum: 0,
  todayReportedNum: 0
}); // 状态人数信息
const activedDayNums = ref<DayNumsInfo>({
  xAxisData: [],
  seriesData: []
}); // 近期已报到信息
const undoneDayNums = ref<DayNumsInfo>({
  xAxisData: [],
  seriesData: []
}); // 近期未完成信息
const deptActiveds = ref<DeptActived[]>([]); // 科室报到信息（科室接待压力）

const tabsData = ref<TabItem[]>([
  // { title: "全部", badge: "" },
  { title: '科内通知', badge: '' },
  { title: '会诊记录', badge: '' }
]);
const infoData = ref<InfoItem[]>([{ news: [] }, { news: [] }]);
const pressure = ref<Array<[number, string]>>([]);
const timerName = ref<number | null>(null);

// 方法
// 获取首页数据
async function getHomePageData() {
  activedDayNums.value.xAxisData = [];
  activedDayNums.value.seriesData = [];
  undoneDayNums.value.xAxisData = [];
  undoneDayNums.value.seriesData = [];
  pressure.value = [];
  activedPeClsInfo.value.activedPeClses = [];

  try {
    const response = await $ajax.post($apiUrls.GetIndexPageStatistics);
    const { success, returnData } = response.data;

    if (!success) return;

    bookedNumInfo.value = returnData.bookedNumInfo;
    activedSexInfo.value = returnData.activedSexInfo;
    activedPersonCompanyInfo.value = returnData.activedPersonCompanyInfo;
    activedBookedLocalInfo.value = returnData.activedBookedLocalInfo;
    statusNumInfo.value = returnData.statusNumInfo;

    returnData.activedDayNums.forEach((item: any) => {
      activedDayNums.value.xAxisData.push(item.activedDate);
      activedDayNums.value.seriesData.push(item.activedNum);
    });

    returnData.undoneDayNums.forEach((item: any) => {
      undoneDayNums.value.xAxisData.push(item.activedDate);
      undoneDayNums.value.seriesData.push(item.undoneNum);
    });

    deptActiveds.value = returnData.deptActiveds;

    returnData.deptActiveds.forEach((items: DeptActived) => {
      pressure.value.push([items.actvicedNum, items.deptName]);
    });

    activedPeClsInfo.value.totalActived =
      returnData.activedPeClsInfo.totalActived;

    returnData.activedPeClsInfo.activedPeClses.forEach((item: any) => {
      activedPeClsInfo.value.activedPeClses.push({
        value: item.activedNum,
        name: item.peCls
      });
    });

    nextTick(() => {
      if (progress.value) {
        (progress.value as any).getProgressData(
          returnData.activedSexInfo,
          returnData.activedPersonCompanyInfo
        );
      }

      if (pieDiagram.value) {
        (pieDiagram.value as any).getPieDta(
          returnData.activedBookedLocalInfo,
          activedPeClsInfo.value
        );
      }

      if (lineChart.value) {
        (lineChart.value as any).getLineDta(
          activedDayNums.value,
          undoneDayNums.value
        );
      }

      if (pressureBar.value) {
        (pressureBar.value as any).getBarDta(pressure.value);
      }
    });

    loading.value = false;
  } catch (error) {
    console.error('获取首页数据失败:', error);
  }
}

// 获取徽章值
function getBadgeVale() {
  let badgeVale = infoData.value.map((item) => {
    return item.news.filter((i) => i.isRead === false).length;
  });

  tabsData.value.forEach((val, index) => {
    val.badge = badgeVale[index].toString();
  });
}

// 生命周期钩子
onMounted(() => {
  getBadgeVale();
  getHomePageData();
  timerName.value = window.setInterval(getHomePageData, 180000);
});

// 组件激活
onActivated(() => {
  if (timerName.value) return;
  getHomePageData();
  timerName.value = window.setInterval(getHomePageData, 180000);
});

// 组件失活
onDeactivated(() => {
  if (timerName.value) {
    clearInterval(timerName.value);
    timerName.value = null;
  }
});

// 组件销毁
onUnmounted(() => {
  if (timerName.value) {
    clearInterval(timerName.value);
    timerName.value = null;
  }
});
</script>
<template>
  <div class="home">
    <!-- 主要内容垂直布局 -->
    <div class="dashboard-container">
      <!-- 顶部：统计卡片区域 -->
      <div class="stats-section">
        <!-- 累计待总检人数统计卡片 -->
        <div class="stat-card stat-card--pending">
          <div class="stat-card__header">
            <h3 class="stat-card__title">累计待总检人数</h3>
          </div>
          <div class="stat-card__content">
            <span class="stat-card__number"
              >{{ statusNumInfo.unCheckedNum }}人</span
            >
            <span class="stat-card__icon stat-card__icon--pending">
              <i class="iconfont icon-dengdai"></i>
            </span>
          </div>
        </div>

        <!-- 累计检中总人数统计卡片 -->
        <div class="stat-card stat-card--checking">
          <div class="stat-card__header">
            <h3 class="stat-card__title">累计检中总人数</h3>
          </div>
          <div class="stat-card__content">
            <span class="stat-card__number"
              >{{ statusNumInfo.checkingNum }}人</span
            >
            <span class="stat-card__icon stat-card__icon--checking">
              <i class="iconfont icon-jiankangtijian"></i>
            </span>
          </div>
        </div>

        <!-- 今天已发报告人数统计卡片 -->
        <div class="stat-card stat-card--completed">
          <div class="stat-card__header">
            <h3 class="stat-card__title">今天已发报告人数</h3>
          </div>
          <div class="stat-card__content">
            <span class="stat-card__number"
              >{{ statusNumInfo.todayReportedNum }}人</span
            >
            <span class="stat-card__icon stat-card__icon--completed">
              <i class="iconfont icon-tijianbaogao"></i>
            </span>
          </div>
        </div>
      </div>

      <!-- 中部：体检进度统计图表 -->
      <div class="chart-card chart-card--progress">
        <div class="chart-card__header">
          <h3 class="chart-card__title">体检进度统计</h3>
        </div>
        <div class="chart-card__content">
          <HomeProgress
            ref="progress"
            :bookedNumInfo="bookedNumInfo"
            :activedSexInfo="activedSexInfo"
            :activedPersonCompanyInfo="activedPersonCompanyInfo"
            :loading="loading"
          />
        </div>
      </div>

      <!-- 底部：报到方式分布饼图 -->
      <div class="chart-card chart-card--pie">
        <div class="chart-card__header">
          <h3 class="chart-card__title">报到方式分布</h3>
        </div>
        <div class="chart-card__content">
          <HomePieDiagram
            ref="pieDiagram"
            :activedBookedLocalInfo="activedBookedLocalInfo"
            :loading="loading"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
/* ===== 主容器样式 ===== */
.home {
  color: #2d3436;
  padding: 20px;
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* ===== 主要仪表板容器布局 ===== */
.dashboard-container {
  /* 单列垂直布局配置 */
  display: grid;
  grid-template-rows: auto auto auto; /* 顶部统计卡片、中部图表、底部图表 */
  gap: 32px;
  max-width: 1400px;
  margin: 0 auto;

  /* 响应式断点 */
  @media (max-width: 768px) {
    gap: 24px;
    padding: 0 10px;
  }

  @media (min-width: 769px) and (max-width: 1024px) {
    gap: 28px;
  }
}

/* ===== 统计卡片区域布局 ===== */
.stats-section {
  /* 统计卡片水平排列布局 */
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* 三个统计卡片等宽水平排列 */
  gap: 20px;

  /* 响应式断点 */
  @media (max-width: 768px) {
    grid-template-columns: 1fr; /* 移动端改为单列垂直排列 */
    gap: 16px;
  }

  @media (min-width: 769px) and (max-width: 1024px) {
    gap: 18px;
  }

  @media (min-width: 1025px) {
    gap: 24px;
  }
}

/* ===== 图表卡片样式 ===== */
.chart-card {
  /* 图表卡片容器 */
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  box-shadow:
    0 4px 6px rgba(0, 0, 0, 0.07),
    0 1px 3px rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(0, 0, 0, 0.05);
  min-height: 320px; /* 增加高度以适应单列布局 */
  width: 100%; /* 确保占满容器宽度 */

  /* 悬停效果 */
  &:hover {
    transform: translateY(-1px);
    box-shadow:
      0 6px 20px rgba(0, 0, 0, 0.08),
      0 2px 6px rgba(0, 0, 0, 0.04);
  }

  /* 图表卡片头部 */
  .chart-card__header {
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f2f5;
  }

  /* 图表卡片标题 */
  .chart-card__title {
    font-family:
      'PingFangSC-Medium',
      -apple-system,
      BlinkMacSystemFont,
      sans-serif;
    font-size: 20px; /* 增大标题字体 */
    font-weight: 600;
    color: #2d3436;
    margin: 0;
    line-height: 1.4;
  }

  /* 图表内容区域 */
  .chart-card__content {
    height: calc(100% - 60px); /* 减去头部高度 */
    min-height: 240px; /* 增加最小高度 */
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* 响应式优化 */
  @media (max-width: 768px) {
    padding: 20px;
    min-height: 280px;

    .chart-card__title {
      font-size: 18px;
    }

    .chart-card__content {
      min-height: 200px;
    }
  }

  @media (min-width: 769px) and (max-width: 1024px) {
    min-height: 300px;

    .chart-card__title {
      font-size: 19px;
    }

    .chart-card__content {
      min-height: 220px;
    }
  }
}

/* 图表卡片主题变体 */
.chart-card--progress {
  border-left: 4px solid #409eff;
}

.chart-card--pie {
  border-left: 4px solid #67c23a;
}

/* ===== 统计卡片样式 ===== */
.stat-card {
  /* 统计卡片容器 */
  background: #ffffff;
  border-radius: 12px;
  padding: 20px;
  box-shadow:
    0 4px 6px rgba(0, 0, 0, 0.07),
    0 1px 3px rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(0, 0, 0, 0.05);
  min-height: 140px;
  display: flex;
  flex-direction: column;

  /* 悬停效果 */
  &:hover {
    transform: translateY(-2px);
    box-shadow:
      0 8px 25px rgba(0, 0, 0, 0.1),
      0 4px 10px rgba(0, 0, 0, 0.05);
  }

  /* 统计卡片头部 */
  .stat-card__header {
    margin-bottom: 16px;
  }

  /* 统计卡片标题 */
  .stat-card__title {
    font-family:
      'PingFangSC-Regular',
      -apple-system,
      BlinkMacSystemFont,
      sans-serif;
    font-size: 15px;
    font-weight: 500;
    color: #2d3436;
    margin: 0;
    line-height: 1.4;
  }

  /* 统计卡片内容区域 */
  .stat-card__content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
    flex: 1;
  }

  /* 数字显示 */
  .stat-card__number {
    font-family:
      'PingFangSC-Medium',
      -apple-system,
      BlinkMacSystemFont,
      sans-serif;
    font-size: 28px;
    font-weight: 600;
    color: #2d3436;
    line-height: 1.2;
    flex: 1;
  }

  /* 图标容器基础样式 */
  .stat-card__icon {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 24px;
    flex-shrink: 0;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.05);
    }
  }
}

/* 统计卡片图标主题变体 */
.stat-card__icon--pending {
  background: linear-gradient(135deg, #fab63b 0%, #f39c12 100%);
  box-shadow: 0 4px 12px rgba(250, 182, 59, 0.3);
}

.stat-card__icon--checking {
  background: linear-gradient(135deg, #1770df 0%, #0056b3 100%);
  box-shadow: 0 4px 12px rgba(23, 112, 223, 0.3);

  i {
    font-size: 28px;
  }
}

.stat-card__icon--completed {
  background: linear-gradient(135deg, #3cb34f 0%, #27ae60 100%);
  box-shadow: 0 4px 12px rgba(60, 179, 79, 0.3);

  i {
    font-size: 26px;
  }
}

/* ===== 统计卡片主题变体 ===== */
.stat-card--pending {
  border-left: 4px solid #fab63b;
}

.stat-card--checking {
  border-left: 4px solid #1770df;
}

.stat-card--completed {
  border-left: 4px solid #3cb34f;
}

/* ===== 响应式优化 ===== */
@media (max-width: 480px) {
  .home {
    padding: 16px 12px;
  }

  .dashboard-container {
    gap: 20px;
  }

  .stats-section {
    gap: 12px;
  }

  .chart-card {
    padding: 16px;
    min-height: 260px;

    .chart-card__title {
      font-size: 16px;
    }

    .chart-card__content {
      min-height: 180px;
    }
  }

  .stat-card {
    padding: 16px;
    min-height: 120px;

    .stat-card__title {
      font-size: 14px;
    }

    .stat-card__number {
      font-size: 24px;
    }

    .stat-card__icon {
      width: 48px;
      height: 48px;
      font-size: 20px;
    }
  }

  .stat-card__icon--checking i {
    font-size: 24px;
  }

  .stat-card__icon--completed i {
    font-size: 22px;
  }
}

/* ===== 中等屏幕优化 ===== */
@media (min-width: 481px) and (max-width: 768px) {
  .dashboard-container {
    gap: 22px;
  }

  .stats-section {
    gap: 14px;
  }

  .chart-card .chart-card__title {
    font-size: 17px;
  }

  .stat-card .stat-card__number {
    font-size: 26px;
  }

  .stat-card .stat-card__icon {
    width: 52px;
    height: 52px;
    font-size: 22px;
  }

  .stat-card__icon--checking i {
    font-size: 26px;
  }

  .stat-card__icon--completed i {
    font-size: 24px;
  }
}

/* ===== 打印样式优化 ===== */
@media print {
  .home {
    background-color: transparent;
    padding: 0;
  }

  .dashboard-container {
    gap: 16px;
    max-width: none;
  }

  .stats-section {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }

  .chart-card,
  .stat-card {
    box-shadow: none;
    border: 1px solid #ddd;
    break-inside: avoid;

    &:hover {
      transform: none;
    }
  }

  .chart-card {
    min-height: 200px;

    .chart-card__title {
      font-size: 16px;
    }
  }

  .stat-card {
    min-height: 100px;

    .stat-card__title {
      font-size: 13px;
    }

    .stat-card__number {
      font-size: 20px;
    }

    .stat-card__icon {
      width: 40px;
      height: 40px;
      font-size: 18px;
    }
  }

  .stat-card__icon--checking i {
    font-size: 20px;
  }

  .stat-card__icon--completed i {
    font-size: 19px;
  }
}

/* ===== 加载状态优化 ===== */
.chart-card__content,
.stat-card__content {
  position: relative;

  /* 为图表组件提供加载状态支持 */
  &.loading {
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 32px;
      height: 32px;
      margin: -16px 0 0 -16px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #409eff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
