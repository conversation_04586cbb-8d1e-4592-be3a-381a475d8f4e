<template>
  <div class="importSMS_page">
    <el-form ref="form" :model="form" label-width="120px">
      <el-row :gutter="18" class="row">
        <el-col :span="18">
          <el-form-item label="文件路径">
            <el-input v-model.trim="form.file" size="small" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-upload
            class="upload-demo"
            action=""
            :on-change="handleChange"
            ref="upload"
            accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
            :auto-upload="false"
            :show-file-list="false"
          >
            <el-button
              size="small"
              class="blue_btn btn"
              icon="iconfont icon-juxingxuanze"
              style="margin-bottom: 5px"
              >选择文件</el-button
            >
          </el-upload>
        </el-col>
      </el-row>
      <el-row :gutter="18" class="row">
        <el-col :span="18">
          <el-form-item label="文件名称">
            <el-input
              v-model.trim="form.fileName"
              size="small"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-button
            class="violet_btn btn"
            size="small"
            icon="iconfont icon-daoru"
            @click="importData"
            style="margin-bottom: 5px"
            :loading="sendLoading"
            >确认导入</el-button
          >
        </el-col>
      </el-row>
    </el-form>
    <div class="import-table">
      <div class="error-table">
        <div class="table-title">
          <h4>信息有误人数：{{ errorTableData.length }}人</h4>
        </div>
        <div class="table">
          <PublicTable
            :isSortShow="false"
            :theads="theads"
            :viewTableList.sync="errorTableData"
            :tableLoading.sync="tableLoading"
            :columnWidth="columnWidth"
          >
            <template #patCode="{ scope }">
              <el-input
                size="small"
                :class="{ red_color: scope.row.patCode === '' }"
                v-model.trim="scope.row.patCode"
                placeholder="请输入档案卡号"
              ></el-input>
            </template>
            <template #name="{ scope }">
              <el-input
                size="small"
                :class="{ red_color: scope.row.name === '' }"
                v-model.trim="scope.row.name"
                placeholder="请输入姓名"
              ></el-input>
            </template>
            <template #tel="{ scope }">
              <el-input
                size="small"
                :class="{ red_color: C_isTel(scope.row) }"
                v-model.trim="scope.row.tel"
                placeholder="请输入手机号"
              ></el-input>
            </template>
            <template #shortMsgContent="{ scope }">
              <el-input
                type="textarea"
                size="small"
                :autosize="{ minRows: 1 }"
                :class="{ red_color: scope.row.shortMsgContent === '' }"
                v-model.trim="scope.row.shortMsgContent"
                placeholder="请输入短信内容"
              ></el-input>
            </template>
            <template #columnRight>
              <el-table-column label="操作" width="70">
                <template slot-scope="scope">
                  <el-button size="small" @click="delErrTable(scope.$index)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </template>
          </PublicTable>
        </div>
      </div>
      <div class="correct-table">
        <div class="table-title">
          <h4>可导入人数：{{ tableData.length }}人</h4>
        </div>
        <div class="table">
          <PublicTable
            :isSortShow="false"
            :theads="theads"
            :viewTableList.sync="tableData"
            :tableLoading.sync="tableLoading"
            :columnWidth="columnWidth"
          ></PublicTable>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import XLSX from 'xlsx';
import PublicTable from '@/components/publicTable';
import { dataUtils } from '../../../common';
export default {
  components: {
    PublicTable
  },
  computed: {
    C_isTel() {
      return function (row) {
        console.log(dataUtils.isTel(row.tel));
        return row.tel === '' || dataUtils.isTel(row.tel) !== true;
      };
    }
  },
  data() {
    return {
      form: {
        fileName: '',
        file: ''
      },
      tableData: [],
      errorTableData: [],
      theads: {
        patCode: '档案卡号',
        name: '姓名',
        tel: '手机号码',
        shortMsgContent: '短信内容'
      },
      columnWidth: {
        patCode: 140,
        name: 150,
        tel: 140
      },
      tableLoading: false,
      checked: false,
      sendLoading: false
    };
  },
  methods: {
    // 确认导入
    importData() {
      let tableData = [];
      let telFlag = false;
      let errorTableData = this.errorTableData.filter((item) => {
        if (this.C_isTel(item)) {
          telFlag = true;
        }
        if (
          item.patCode !== '' &&
          item.name !== '' &&
          !this.C_isTel(item) &&
          item.shortMsgContent !== ''
        ) {
          tableData.push(item);
        }
        return (
          item.patCode === '' ||
          item.name === '' ||
          this.C_isTel(item) ||
          item.shortMsgContent === ''
        );
      });
      this.errorTableData = errorTableData;
      this.tableData.push(...tableData);
      if (telFlag) {
        this.$message({
          message: '请输入正确的手机号码！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.errorTableData.length > 0) {
        this.$message({
          message: '请先修改错误信息再确认导入！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.tableData.length === 0) {
        this.$message({
          message: '请先导入短信发送的数据！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.sendLoading = true;
      this.$ajax
        .post(this.$apiUrls.BatchImportShortMsgAndSend, this.tableData)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$message({
            message: '导入并且短信发送成功！',
            type: 'success',
            showClose: true
          });
          this.$emit('importSuccessHandle');
          this.sendLoading = false;
        })
        .catch((e) => {
          this.sendLoading = false;
        });
    },
    //  选择文件
    handleChange(file, fileList) {
      this.errorTableData = [];
      this.form.file =
        document.getElementsByClassName('el-upload__input')[0].value;
      let reg = /\.\w+$/;
      this.form.fileName = file.name.replace(reg, '');
      this.fileTemp = file.raw;
      if (this.fileTemp) {
        if (
          this.fileTemp.type ==
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
          this.fileTemp.type == 'application/vnd.ms-excel'
        ) {
          this.fileUpload();
        } else {
          this.$message({
            type: 'warning',
            message: '文件格式错误，请重新选择！',
            showClose: true
          });
        }
      } else {
        this.$message({
          type: 'warning',
          message: '请选择文件！',
          showClose: true
        });
      }
    },
    // 文件导入解析
    fileUpload() {
      this.file = event.currentTarget.files[0];
      let f = this.file;
      let reader = new FileReader();
      let rABS = false; // 是否将文件读取为二进制字符串

      FileReader.prototype.readAsBinaryString = (f) => {
        let binary = '';
        let wb; // 读取完成的数据
        let outData;

        reader.onload = (e) => {
          let bytes = new Uint8Array(reader.result);
          let length = bytes.byteLength;
          for (var i = 0; i < length; i++) {
            binary += String.fromCharCode(bytes[i]);
          }

          if (rABS) {
            wb = XLSX.read(btoa(fixdata(binary)), {
              //手动转化
              type: 'base64'
            });
          } else {
            wb = XLSX.read(binary, {
              type: 'binary'
            });
          }

          // 跳过表格前三行
          outData = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]], {
            range: 2
          });

          // 表格数据
          console.log('outData：', outData);

          //去除RFC2822/ISO date formats的警告
          //   moment.suppressDeprecationWarnings = true;
          let tableData = [];
          outData.map((item) => {
            let obj = {
              patCode: item['档案卡号']
                ? item['档案卡号'].toString()?.trim()
                : '',
              name: item['姓名']?.trim() || '',
              tel: item['手机号码'] ? item['手机号码'].toString()?.trim() : '',
              shortMsgContent: item['短信内容']?.trim() || ''
            };
            if (
              !item['档案卡号'] ||
              !item['姓名'] ||
              this.C_isTel(obj) ||
              !item['短信内容']
            ) {
              this.errorTableData.push(obj);
              return;
            }
            tableData.push(obj);
          });
          this.tableData = tableData;
          console.log('this.tableData: ', this.tableData, this.errorTableData);
        };
        reader.readAsArrayBuffer(f);
      };
      if (rABS) {
        reader.readAsArrayBuffer(f);
      } else {
        reader.readAsBinaryString(f);
      }
    },
    // 删除错误信息
    delErrTable(idx) {
      console.log(idx);
      this.errorTableData.splice(idx, 1);
    }
  }
};
</script>

<style lang="less" scoped>
.importSMS_page {
  height: 100%;
  overflow: auto;
  display: flex;
  flex-direction: column;
  .import-table {
    flex: 1;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    .table-title {
      display: flex;
      justify-content: space-between;
      padding: 5px 0;
    }
    .error-table {
      .table {
        height: 200px;
      }
      .red_color {
        /deep/input,
        /deep/ textarea,
        /deep/ select {
          border-color: red;
        }
      }
    }
    .correct-table {
      flex: 1;
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      .table {
        flex: 1;
        flex-shrink: 0;
        overflow: auto;
      }
    }
  }
}
</style>
