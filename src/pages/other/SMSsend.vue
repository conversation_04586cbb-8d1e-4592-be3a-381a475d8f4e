<template>
  <div class="SMSsend_page">
    <header>
      <el-select
        placeholder="请选择"
        size="mini"
        v-model="searchInfo.queryType"
        style="width: 100px; margin-right: 10px"
        @change="QueryBatchSendShortMessage"
      >
        <el-option
          v-for="item in queryTypeList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
      <el-date-picker
        :picker-options="{ shortcuts: G_datePickerShortcuts }"
        :clearable="false"
        type="daterange"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        size="mini"
        v-model="dates"
        style="width: 260px; margin-right: 10px"
        value-format="yyyy-MM-dd"
        @change="QueryBatchSendShortMessage"
      >
      </el-date-picker>
      <el-input
        size="mini"
        placeholder="档案卡号/姓名/手机号"
        clearable
        v-model.trim="searchInfo.keyWord"
        class="input"
        style="width: 200px; margin-right: 5px"
        @change="QueryBatchSendShortMessage"
      ></el-input>
      <el-radio-group
        v-model="searchInfo.isSuccess"
        @change="QueryBatchSendShortMessage"
        style="margin-right: 10px"
      >
        <el-radio :label="undefined">全部</el-radio>
        <el-radio :label="true">发送成功</el-radio>
        <el-radio :label="false">发送失败</el-radio>
      </el-radio-group>
      <el-button size="mini" class="violet_btn btn" @click="createBtnClick"
        >新建</el-button
      >
      <el-button size="mini" class="violet_btn btn" @click="importBtn"
        >excel批量导入</el-button
      >
    </header>
    <div class="table_wrap">
      <PublicTable ref="publicTable_Ref" url="" :theads="theads">
        <template #success="{ scope }">
          <span
            class="success_type"
            :class="{ success_fail: !scope.row.success }"
            >{{ scope.row.success ? '发送成功' : '发送失败' }}</span
          >
        </template>
      </PublicTable>
    </div>
    <!-- 新建短信发送的抽屉 -->
    <el-drawer
      title="新建短信发送"
      :visible.sync="drawerShow"
      direction="rtl"
      :wrapperClosable="false"
    >
      <el-form
        :model="popupForm"
        style="padding: 0 5px"
        ref="ruleForm_Ref"
        label-width="80px"
        :rules="rules"
      >
        <el-form-item label="档案卡号" prop="patCode">
          <el-input
            v-model.trim="popupForm.patCode"
            autocomplete="off"
            disabled
            size="small"
          ></el-input>
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-autocomplete
            v-model.trim="popupForm.name"
            style="width: 100%"
            size="small"
            :fetch-suggestions="querySearch"
            placeholder="请输入姓名搜索"
            :trigger-on-focus="false"
            popper-class="SMSsend_autocomplete"
            @select="handleSelect"
            @blur="autocompleteBlur"
          >
            <template #default="{ item }">
              <div class="item_wrap">
                <p>{{ item.name }}</p>
                <span>{{ item.tel }}</span>
              </div>
            </template>
          </el-autocomplete>
        </el-form-item>
        <el-form-item label="手机号码" prop="tel">
          <el-input
            v-model.trim="popupForm.tel"
            autocomplete="off"
            size="small"
            placeholder="请输入手机号码"
          ></el-input>
        </el-form-item>
        <el-form-item label="短信内容" prop="shortMsgContent">
          <el-input
            v-model.trim="popupForm.shortMsgContent"
            type="textarea"
            :autosize="{ minRows: 2 }"
            autocomplete="off"
            size="small"
            placeholder="请输入短信内容"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <div style="text-align: right">
            <el-button size="small" @click="drawerShow = false">取消</el-button>
            <el-button
              size="small"
              type="primary"
              @click="onSubmit"
              :loading="sendLoading"
              >发送</el-button
            >
          </div>
        </el-form-item>
      </el-form>
    </el-drawer>
    <!-- 导入 -->
    <el-drawer
      title=""
      :visible.sync="importDrawerShow"
      :wrapperClosable="false"
      size="60%"
      class="import-drawer"
      destroy-on-close
      custom-class="SMS_drawer"
    >
      <div slot="title">
        导入文件
        <el-button
          size="small"
          class="green_btn btn"
          icon="el-icon-download"
          @click="importTemplate"
          >下载模板</el-button
        >
      </div>
      <ImportSMS @importSuccessHandle="importSuccessHandle" />
      <a href="./普通团体导入模板.xlsx" download="普通团体导入模板.xlsx"></a>
    </el-drawer>
  </div>
</template>

<script>
import PublicTable from '@/components/publicTable2.vue';
import moment from 'moment';
import { mapGetters } from 'vuex/dist/vuex.common.js';
import ImportSMS from './components/importSMS.vue';
export default {
  name: 'SMSsend',
  components: {
    PublicTable,
    ImportSMS
  },
  computed: {
    ...mapGetters(['G_datePickerShortcuts'])
  },
  data() {
    const checkCardTel = (rule, value, callback) => {
      if (!value) {
        callback();
        return;
      } else {
        let telReg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/;
        if (!telReg.test(value)) {
          callback(new Error('手机号格式不正确！'));
        } else {
          callback();
          return;
        }
      }
    };
    return {
      queryTypeList: [
        {
          value: 1,
          label: '创建时间'
        },
        {
          value: 2,
          label: '发送时间'
        }
      ],
      keyWord: '',
      drawerShow: false,
      theads: [
        {
          prop: 'patCode',
          label: '档案卡号',
          align: '',
          width: '',
          sortable: false,
          width: 100
        },
        {
          prop: 'name',
          label: '姓名',
          align: '',
          width: '',
          sortable: false,
          width: 150
        },
        {
          prop: 'tel',
          label: '手机号码',
          align: '',
          width: '',
          sortable: false,
          width: 100
        },
        {
          prop: 'success',
          label: '发送状态',
          align: '',
          width: '',
          sortable: true,
          width: 100
        },
        {
          prop: 'createdTime',
          label: '创建时间',
          align: '',
          width: '',
          sortable: false,
          width: 150
        },
        {
          prop: 'sentTime',
          label: '发送时间',
          align: '',
          width: '',
          sortable: false,
          width: 150
        },
        {
          prop: 'shortMsgContent',
          label: '短信内容',
          align: '',
          width: '',
          sortable: false,
          minWidth: 500
        },
        {
          prop: 'errorMsg',
          label: '错误信息',
          align: '',
          width: '',
          sortable: false,
          minWidth: 500
        }
      ],
      popupForm: {
        name: '',
        patCode: '',
        tel: '',
        shortMsgContent: ''
      },
      checkPatient: {
        name: ''
      },
      rules: {
        name: [{ required: true, message: '请选择接收人', trigger: 'blur' }],
        patCode: [{ required: true, message: '请选择接收人', trigger: 'blur' }],
        tel: [
          { required: true, message: '请输入手机号码', trigger: 'blur' },
          { required: true, validator: checkCardTel, trigger: 'blur' }
        ],
        shortMsgContent: [
          { required: true, message: '请输入短信内容', trigger: 'blur' }
        ]
      },
      searchInfo: {
        beginDate: '',
        endDate: '',
        queryType: 1,
        isSuccess: undefined,
        keyWord: ''
      },
      dates: [
        moment().add(-7, 'd').format('YYYY-MM-DD'),
        moment().startOf('day').format('YYYY-MM-DD')
      ],
      importDrawerShow: false,
      sendLoading: false
    };
  },
  methods: {
    // 查询批量发送短信记录
    QueryBatchSendShortMessage() {
      this.searchInfo.beginDate = this.dates[0];
      this.searchInfo.endDate = this.dates[1];
      this.$ajax
        .post(this.$apiUrls.QueryBatchSendShortMessage, this.searchInfo)
        .then((r) => {
          this.$refs.publicTable_Ref.staticLoad(r.data.returnData || []);
        });
    },
    // 新建按钮点击回调
    createBtnClick() {
      this.drawerShow = true;
      this.sendLoading = false;
      this.$nextTick(() => {
        this.resetForm();
      });
    },
    // 重置新建表单
    resetForm() {
      this.$refs.ruleForm_Ref.resetFields();
    },
    // 病人的搜索回调
    querySearch(queryString, cb) {
      console.log(queryString);
      if (queryString.trim().length >= 2) {
        this.getPatientList().then((r) => {
          cb(r);
        });
        return;
      }
      cb([]);
    },
    // 获取病人列表
    getPatientList() {
      return new Promise((resolve, reject) => {
        this.$ajax
          .paramsPost(this.$apiUrls.QueryPatientInfo, {
            keyWord: this.popupForm.name
          })
          .then((r) => {
            resolve(r.data.returnData);
          });
      });
    },
    // 选择病人的回调
    handleSelect(row) {
      this.popupForm = {
        ...this.popupForm,
        ...row
      };
      this.checkPatient.name = row.name;
      this.$nextTick(() => {
        this.$refs.ruleForm_Ref.validateField('name');
      });
    },
    // 疾病列表搜索输入框的失焦回调
    autocompleteBlur() {
      if (this.popupForm.name === this.checkPatient.name) return;
      this.popupForm.name = this.checkPatient.name;
    },
    // 新建的保存并且发送短信
    onSubmit() {
      this.$refs.ruleForm_Ref.validate((valid) => {
        if (valid) {
          this.sendLoading = true;
          this.$ajax
            .post(this.$apiUrls.CreateShortMsgAndSend, this.popupForm)
            .then((r) => {
              let { success, returnData } = r.data;
              if (!success) return;
              this.$message({
                message: '新建短信并且发送成功！',
                type: 'success',
                showClose: true
              });
              this.drawerShow = false;
              this.sendLoading = false;
            })
            .catch((e) => {
              this.sendLoading = false;
            });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    //下载导入模板
    importTemplate() {
      let a = document.createElement('a');
      a.href = './短信发送导入模板.xlsx';
      a.download = '短信发送导入模板.xlsx';
      a.style.display = 'none';
      document.body.appendChild(a);
      a.click();
      a.remove();
    },
    // 批量导入按钮的点击回调
    importBtn() {
      this.importDrawerShow = true;
    },
    // 导入成功的回调
    importSuccessHandle() {
      this.importDrawerShow = false;
      this.QueryBatchSendShortMessage();
    }
  },
  mounted() {
    this.QueryBatchSendShortMessage();
  }
};
</script>

<style lang="less" scoped>
.SMSsend_page {
  display: flex;
  flex-direction: column;
  overflow: auto;
  header {
    justify-content: flex-end;
    display: flex;
    background: #fff;
    padding: 5px 0;
    display: flex;
    align-items: center;
  }
  .table_wrap {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    background: #fff;
  }
  .success_type {
    color: #1770df;
  }
  .success_fail {
    color: #d63031;
  }
}
</style>
<style lang="less">
.SMSsend_autocomplete {
  .item_wrap {
    display: flex;
    justify-content: space-between;
  }
}
.SMS_drawer {
  .el-drawer__header {
    margin-bottom: 5px;
  }
  .el-form-item {
    margin-bottom: 5px;
  }
  .el-row {
    display: flex;
    align-items: center;
  }
}
</style>
