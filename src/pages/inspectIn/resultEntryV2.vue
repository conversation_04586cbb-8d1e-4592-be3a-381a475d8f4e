<script>
import BtnCommon from './components/btnCommon.vue';
import PublicTable from '../../components/publicTable.vue';
import PhysicalSummary from './components/doctorWorkStation/physicalSummaryV2.vue';
import SideTabs from './components/doctorWorkStation/sideTabsV2.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
import Nomal from './components/doctorWorkStation/nomal.vue'; // 表格页面
import CheckboxGroup from './components/doctorWorkStation/checkboxGroup.vue';
import Response from './components/doctorWorkStation/response.vue';
import ReConsultation from './components/doctorWorkStation/reConsultation.vue';
import HistoryReport from './components/doctorWorkStation/historyReport.vue';
import CollectPictures from './components/doctorWorkStation/collectPictures.vue';
import ExamineV3 from './components/doctorWorkStation/examineV3.vue';
import urgent from './mixins/urgent';
import shortcut from '@/common/shortcut';
import consultMsgTips from './mixins/consultMsgTips';
import consultList from './mixins/consultList';
import Consult from '../mainInspectionPage/consult.vue';
import ImgText from './components/resultEntry/imgText.vue';
import imgTextJs from './mixins/imgText';
import FollowUp from '@/components/followUp';
import RegisterRecordTable from './components/registerRecordTable.vue'; // 左侧体检人信息列表
import PureToneAudiometry from './components/pureToneAudiometry.vue'; // 纯音测听
import SmokingHistory from './components/smokingHistory.vue'; // 吸烟史
import ReceiveReportResult from './components/resultEntry/receiveReportResult'; // 接收报告结果
import printMixins from '@/components/printMixins';
import ImageLegeng from './components/resultEntry/imageLegeng.vue';
import ImageLegengJS from './mixins/ImageLegeng';
import SignificantPositive from '@/components/significantPositive.vue';
import SymptomResult from './components/symptomResult.vue'; //   症状询问
import InformationInvestigation from './components/informationInvestigation.vue';
import Questionnaire from '@/pages/register/components/questionnaire.vue';
import UploadImgText from './components/resultEntry/uploadImgText.vue';
// 结果录入
export default {
  name: 'resultEntryV2',
  mixins: [
    printMixins,
    urgent,
    shortcut,
    consultMsgTips,
    consultList,
    imgTextJs,
    ImageLegengJS
  ],
  components: {
    BtnCommon,
    PublicTable,
    Nomal,
    PhysicalSummary,
    SideTabs,
    CheckboxGroup,
    ReConsultation,
    Response,
    HistoryReport,
    CollectPictures,
    ExamineV3,
    Consult,
    ImgText,
    FollowUp,
    RegisterRecordTable,
    PureToneAudiometry,
    SmokingHistory,
    SymptomResult,
    ReceiveReportResult,
    ImageLegeng,
    SignificantPositive,
    InformationInvestigation,
    Questionnaire,
    UploadImgText
  },
  props: {
    // 是否主检
    isMain: {
      type: Number,
      default: 2
    },
    // 是否审核页面
    processFlag: {
      type: Boolean,
      default: false
    },
    mainInspectionFlag: {
      type: Boolean,
      default: true
    },
    dataQueryFlag: {
      type: Boolean,
      default: true
    },
    // 是否需要显示咨询弹窗
    isConsultationReplyFlag: {
      type: Boolean,
      default: true
    },
    // 是否开启编辑模式
    P_isModify: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      questionnaireDisabled: true,
      showPatientInfoDialog: false,
      significantVisible: false,
      significantPositiveStatus: false,
      userListIsGet: false,
      checkedScrollTop: 0,
      notInspectedScrollTop: 0,
      HisPatInfo: {},
      searchRadioVal: 0,
      resultState: 1, //获取关联结果的请求状态，1 表示获取完成，2表示正在获取中；
      shortcutList: {
        112: this.savesBtn,
        113: this.amend,
        114: this.waiveBtn,
        115: this.deleteRecordComb
      },
      sendDrawer: false,
      resDrawer: false,
      hisDrawer: false,
      pictureDrawer: false,

      headerInfo: {
        regNo: '',
        name: '',
        sex: null,
        age: null,
        companyName: ''
      },
      titleName: '血常规（五分类）',
      typeInfo: {
        isAbnormal: false,
        operatorCode: '系统员',
        checkDate: dataUtils.getDate(),
        doctor: '系统员'
      },
      pageStatus: '',
      btnList: [],
      dynamicTags: [],
      isModify: true, //是否修改
      navList: [],
      infoData: {},
      backoutInitData: {}, //撤销时使用的初始化数据
      recordComb: {},
      major: {},
      isEnabled: false,
      combCode: '',
      clsCode: '',
      examDeptCode: '',
      id: null,
      errorDisabled: true,
      regCombs: [],
      deptCode: '',
      timerName: '',
      routeRow: {}, //路由参数
      isSave: false,
      searchRegNoIpt: '',
      followUpShow: false,
      showGuidance: false,
      showPureToneAudiometry: false, // 是否纯音测听
      showSmokingHistory: false, // 是否显示吸烟史
      showSymptom: false, // 是否显示症状按钮
      doctorList: [], // 检查医生列表
      printImgShow: false,
      phoneShow: false,
      phoneTheads: {
        createTime: '联系时间',
        contactPerson: '联系人',
        content: '联系内容'
      },
      phoneColumn: {},
      phoneList: [],
      innerVisible: false,
      phoneContent: '',
      checkPhone: {},
      printImgList: [],
      printList: [], //打印机列表
      consultingShow: false,
      consultingInput: '',
      showRecReportResult: false, // 是否显示接收结果
      isShowdialogTable: false,
      formInfo: {
        regNo: '', //体检号
        name: '', //姓名
        sex: '', //性别（0通用1男2女）
        age: '', //年龄
        tel: '', //手机号
        familyMedicalHistory: '', //家族史
        pastMedicalHistory: '', //既往病史
        operationStatus: '', //手术状况
        smokingHabit: '', //吸烟习惯
        drinkingHabit: '', //喝酒习惯
        livingHabit: '', //生活习惯
        currentCondition: '', //现在病况
        questionnaireAnswer: '' //问卷答案
      }
    };
  },
  mounted() {
    //记录左侧表格的滚动条
    this?.$refs.registerRecordTable_ref?.$refs.publicTable_Ref1?.$refs.tableCom_Ref.bodyWrapper.addEventListener(
      'scroll',
      (res) => {
        this.checkedScrollTop = res.target.scrollTop;
      },
      true
    );
    this?.$refs.registerRecordTable_ref?.$refs.publicTable_Ref2?.$refs.tableCom_Ref.bodyWrapper.addEventListener(
      'scroll',
      (res) => {
        this.notInspectedScrollTop = res.target.scrollTop;
      },
      true
    );
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_peClsList',
      'G_userInfo',
      'G_codeDepartment',
      'G_config',
      'G_combExtension',
      'G_sysOperator'
    ]),
    /**
     * @author: justin
     * @description: 是否显示纯音测听按钮
     * @return {*}
     */
    showPureToneAudiometryBtn() {
      if (!this.infoData?.recordComb || !this.infoData.recordComb?.regCombId)
        return false;
      const currComb = this.navList.find(
        (item) => item.regCombId === this.infoData.recordComb.regCombId
      );
      if (!currComb?.combExtensions) return false;
      return currComb.combExtensions.includes(
        Number(
          this.G_combExtension.find((item) => item.label === '纯音测听')?.value
        )
      );
    },
    /**
     * @author: justin
     * @description: 是否显示吸烟史按钮
     * @return {*}
     */
    showSmokingHistoryBtn() {
      if (!this.infoData?.recordComb || !this.infoData.recordComb?.regCombId)
        return false;
      const currComb = this.navList.find(
        (item) => item.regCombId === this.infoData.recordComb.regCombId
      );
      if (!currComb?.combExtensions) return false;
      return currComb.combExtensions.includes(
        Number(
          this.G_combExtension.find((item) => item.label === '职业问诊')?.value
        )
      );
    },
    showSymptomBtn() {
      if (!this.infoData?.recordComb || !this.infoData.recordComb?.regCombId)
        return false;
      const currComb = this.navList.find(
        (item) => item.regCombId === this.infoData.recordComb.regCombId
      );
      if (!currComb?.combExtensions) return false;
      return currComb.combExtensions.includes(
        Number(
          this.G_combExtension.find((item) => item.label === '症状询问')?.value
        )
      );
    },
    // 健康检/职业检的标记
    C_isOccupation() {
      return this.headerInfo.isOccupation && this.headerInfo.isOrdinary;
    },
    btnListState() {
      return this.headerInfo?.peStatus > 2
        ? ['保存', '撤销编辑', '弃检', '删除结果']
        : [];
    },
    C_significant() {
      return this.checkComb?.combStatus > 1;
    }
  },
  methods: {
    submitSuccess() {
      this.isShowdialogTable = false;
      this.questionnaireDisabled = false;
    },
    //问卷调查
    questionnaire() {
      if (!this.headerInfo.regNo) {
        this.$message({
          message: '请先选择需要问卷调查的体检信息！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.isShowdialogTable = true;
      // this.GetQuestionDataByRegNo(this.headerInfo);
    },
    //通过体检号获取问卷数据
    GetQuestionDataByRegNo(userInfo) {
      this.formInfo = {
        regNo: userInfo.regNo, //体检号
        name: userInfo.name, //姓名
        sex: this.G_EnumList['Sex'][userInfo.sex], //性别（0通用1男2女）
        age: userInfo.age == '0' ? userInfo.age + '月' : userInfo.age + '岁', //年龄
        tel: userInfo.tel //手机号
      };
      this.$ajax
        .post(this.$apiUrls.GetQuestionDataByRegNo, '', {
          query: {
            regNo: userInfo.regNo
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          if (!returnData) {
            this.questionnaireDisabled = true;
            return;
          }
          this.questionnaireDisabled = false;
          let temp = Object.assign(this.formInfo, returnData);
          this.formInfo = { ...temp };
        });
    },
    informatorySuccess() {
      this.showPatientInfoDialog = false;
    },
    informatoryCancel() {
      this.showPatientInfoDialog = false;
    },
    informatoryOpen() {
      this.$nextTick(() => {
        this.$refs.informationInvestigation.getFormData();
      });
    },
    sideTabsNavClickFun() {
      if (this.$refs.physicalSummary?.$refs.tagInput_ref) {
        this.$refs.physicalSummary.$refs.tagInput_ref.inputValue = '';
      }
    },
    pageClick() {
      if (this.$refs.examine_Ref) this.$refs.examine_Ref.resultLeave();
    },

    /**
     * @author: justin
     * @description: 是否显示检查医生列表
     * @param {*} flag
     * @return {*}
     */
    showDoctorList(flag) {
      if (flag) {
        this.doctorList = this.G_sysOperator;
      }
    },

    /**
     * @author: justin
     * @description: 检查医生列表过滤
     * @param {*} query
     * @return {*}
     */
    doctorfilter(query) {
      if (query) {
        setTimeout(() => {
          this.doctorList = this.G_sysOperator.filter((item) => {
            return (
              item.label.indexOf(query) != -1 || item.value.indexOf(query) != -1
            );
          });
        }, 200);
      } else {
        this.doctorList = this.G_sysOperator;
      }
    },

    /**
     * @author: justin
     * @description: 左侧列表查询调用
     * @param {*} searchData  查询条件
     * @return {*}
     */
    registerRecordTableSearch(searchData) {
      this.deptCode = searchData?.deptCode || this.G_userInfo.codeOper.deptCode;
    },

    /**
     * @author: justin
     * @description: 左侧登记记录查询双击回调
     * @param {*} row
     * @param {*} column
     * @param {*} event
     * @return {*}
     */
    rowDblClickRegRecord(row, column, event) {
      if (
        this.isModify &&
        JSON.stringify(this.fixed_infoData) != '{}' &&
        JSON.stringify(this.infoData?.recordItems) !=
          JSON.stringify(this.fixed_infoData?.recordItems)
      ) {
        this.$confirm('结果有修改，是否保存?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        })
          .then(() => {
            this.saves(false).then((r) => {
              this.rowClickFun(row);
            });
          })
          .catch(() => {
            this.rowClickFun(row);
          });
        return;
      }
      this.rowClickFun(row);
    },
    /**
     * @author: justin
     * @description:
     * @param {*} row
     * @return {*}
     */
    rowClickFun(row) {
      this.headerInfo = row;
      this.searchRegNoIpt = '';
      this.infoData = {};
      this.pageStatus = '';
      this.recordComb = {};
      this.dynamicTags = [];
      this.getNavList().then(() => {
        this.autoCheckObj();
      });
      // this.GetQuestionDataByRegNo(this.headerInfo)
      this.getImgTextList(row.regNo);
      this.$refs.sideTabs.template = '';
      this.$refs.sideTabs.liIndex = null;
      this.$refs.sideTabs.activeName = '';
      this.$refs.sideTabs.imgList = [];
      this.isSave = false;
    },

    /**
     * @author: justin
     * @description: 自动选中未检的项目
     * @return {*}
     */
    autoCheckObj() {
      this.navList.some((item, idx, arr) => {
        if (item.combStatus == 1) {
          this.$refs.sideTabs.liIndex = item.regCombId;
          this.$refs.sideTabs.navClickFun(item);
          return true;
        }
        if (item.combStatus > 1 && idx === arr.length - 1) {
          this.$refs.sideTabs.liIndex = arr[0].regCombId;
          this.$refs.sideTabs.navClickFun(arr[0]);
          return true;
        }
      });
    },

    /**
     * @author: justin
     * @description: 体检号回车查询
     * @return {*}
     */
    searchRegNo() {
      if (
        this.isModify &&
        JSON.stringify(this.fixed_infoData) != '{}' &&
        JSON.stringify(this.infoData?.recordItems) !=
          JSON.stringify(this.fixed_infoData?.recordItems)
      ) {
        this.$confirm('结果有修改，是否保存?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        })
          .then(() => {
            this.saves(false).then((r) => {
              this.infoData = {};
              this.regNoGetInfo(this.searchRegNoIpt);
            });
          })
          .catch(() => {
            this.infoData = {};
            this.regNoGetInfo(this.searchRegNoIpt);
          });
        return;
      }
      this.infoData = {};
      this.regNoGetInfo(this.searchRegNoIpt);
    },

    /**
     * @author: justin
     * @description: 清空
     * @return {*}
     */
    clearRegNo() {
      this.btnList = [];
      this.headerInfo = {};
      this.infoData = {};
      this.pageStatus = '';
      this.recordComb = {};
      this.navList = [];
    },

    /**
     * @author: justin
     * @description: 获取内容信息
     * @param {*} searchRegNoIpt
     * @return {*}
     */
    regNoGetInfo(searchRegNoIpt) {
      this.infoData = {};
      this.pageStatus = '';
      this.recordComb = {};
      this.getNavList(searchRegNoIpt).then(() => {
        this.navList.some((item, idx, arr) => {
          if (item.combStatus == 1) {
            this.$refs.sideTabs.liIndex = item.regCombId;
            this.$refs.sideTabs.navClickFun(item);
            return true;
          }
          if (item.combStatus > 1 && idx === arr.length - 1) {
            this.$refs.sideTabs.liIndex = arr[0].regCombId;
            this.$refs.sideTabs.navClickFun(arr[0]);
            return true;
          }
        });
      });
      this.getImgTextList(searchRegNoIpt);
      this.$refs.sideTabs.template = '';
      this.$refs.sideTabs.liIndex = null;
      this.$refs.sideTabs.activeName = '';
      this.$refs.sideTabs.imgList = [];
      this.isSave = false;
    },

    /**
     * @author: justin
     * @description: 获取项目导航及其登记信息
     * @param {*} regNo
     * @return {*}
     */
    getNavList(regNo) {
      return new Promise((resolve, reject) => {
        let data = {
          regNo: regNo || this.headerInfo.regNo,
          filterDoctor: false,
          doctorCode: this.G_userInfo.codeOper.operatorCode,
          deptCode: this.deptCode || this.G_userInfo.codeOper.deptCode
        };
        this.headerInfo = {};
        this.$ajax.post(this.$apiUrls.GetExamCombListV2, data).then((r) => {
          this.searchRegNoIpt = '';
          let { success, returnData, returnMsg } = r.data;
          if (!success) return;
          if (!returnData) {
            this.$message({
              message: returnMsg,
              type: 'success',
              showClose: true
            });
            this.btnList = [];
            this.headerInfo = {};
            this.infoData = {};
            this.pageStatus = '';
            this.recordComb = {};
            this.navList = [];
            return;
          }
          this.headerInfo = returnData.person;
          this.navList = returnData.examCombs;
          this.checkComb = returnData.examCombs.find(
            (item) => item.combCode === this.checkComb.combCode
          );
          this.getFollowUpState();
          this.GetQuestionDataByRegNo(this.headerInfo);
          resolve();
        });
      });
    },

    /**
     * @author: justin
     * @description: 获取组合内容
     * @param {*} data
     * @return {*}
     */
    getCombInfo(data) {
      this.pageStatus = data.template;
      this.infoData = {};
      this.fixed_infoData = {};
      this.$ajax.post(this.$apiUrls.ReadRecordCombNew, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.infoData = dataUtils.deepCopy(returnData || {});
        setTimeout(() => {
          this.fixed_infoData = dataUtils.deepCopy(this.infoData || {});
        }, 200);
        this.backoutInitData = JSON.parse(JSON.stringify(returnData));
        this.recordComb = returnData.recordComb;
        this.major = returnData.major || {};
        this.dynamicTags = returnData.combTags;
        if (this.pageStatus === 3) {
          this.$refs.checkboxGroup.getCheckboxList(
            returnData.recordComb.combCode
          );
        }
        this.$nextTick(() => {
          if (
            this.headerInfo.peStatus > 2 ||
            returnData.recordComb.doctorName === '弃检'
          ) {
            this.isModify = false;
          } else {
            this.isModify = true;
            this.$refs.examine_Ref.nextRowCurrent({ rowIndex: null });
          }
        });
      });
    },

    /**
     * @author: justin
     * @description: 关闭抽屉
     * @return {*}
     */
    sendDrawerHandleClose() {
      this.sendDrawer = false;
    },

    /**
     * @author: justin
     * @description: 关闭抽屉
     * @return {*}
     */
    handleClose() {
      this.resDrawer = false;
      this.routeRow = {};
    },

    /**
     * @author: justin
     * @description: 关闭历史报告弹出
     * @return {*}
     */
    hisDrawerClose() {
      this.hisDrawer = false;
    },
    /**
     * @author: justin
     * @description:
     * @return {*}
     */
    historyReport() {
      if (!this.headerInfo.regNo) {
        this.$message({
          message: '请先点击要查看历史报告的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.hisDrawer = true;
    },
    /**
     * @author: justin
     * @description: 图例关闭回调
     * @return {*}
     */
    pictureDrawerClose() {
      this.pictureDrawer = false;
      this.$refs.sideTabs.getImage(
        this.regCombId,
        this.$refs.sideTabs.deptCode
      );
    },

    /**
     * @author: justin
     * @description: 采集图片
     * @return {*}
     */
    collection() {
      if (!this.headerInfo.regNo) {
        this.$message({
          message: '请先点击要添加采集图片的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.pictureDrawer = true;
    },
    /**
     * @author: justin
     * @description:  发起会诊
     * @return {*}
     */
    sendConsultation() {
      if (!this.headerInfo.regNo) {
        this.$message({
          message: '请先点击要查看发起会诊的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.sendDrawer = true;
    },
    /**
     * @author: justin
     * @description: 响应会诊
     * @param {*} id
     * @return {*}
     */
    responseConsultation(id) {
      if (id) {
        this.id = id;
      } else {
        this.id = null;
      }
      this.sendDrawer = false;
      this.resDrawer = true;
    },

    /**
     * @author: justin
     * @description: 撤销按钮的点击回调
     * @return {*}
     */
    amend() {
      if (this.isModify) {
        this.$confirm('是否撤销所有编辑？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        })
          .then(() => {
            this.infoData = JSON.parse(JSON.stringify(this.backoutInitData));
            this.dynamicTags = this.infoData?.combTags || [];
            this.isModify = !this.isModify;
            this.$message({
              type: 'success',
              message: '撤销成功!'
            });
          })
          .catch(() => {});
      } else {
        this.isModify = !this.isModify;
      }
    },

    /**
     * @author: justin
     * @description: 保存按钮的点击回调
     * @return {*}
     */
    savesBtn() {
      if (
        this.$refs.examine_Ref.checkRowIndex !== null &&
        this.pageStatus !== 3
      ) {
        if (
          this.$refs.examine_Ref.$refs[
            'inpFocus_' + this.$refs.examine_Ref.checkRowIndex
          ]?.focus
        ) {
          this.$refs.examine_Ref.$refs[
            'inpFocus_' + this.$refs.examine_Ref.checkRowIndex
          ].blur();
        } else {
          this.$refs.examine_Ref.$refs[
            'inpFocus_' + this.$refs.examine_Ref.checkRowIndex
          ].inputHandle();
          let itemComb =
            this.infoData.recordItems[this.$refs.examine_Ref.checkRowIndex];
          this.$nextTick(() => {
            itemComb.itemTags.map((item, idx) => {
              this.$refs.examine_Ref.$refs[
                'inpFocus_' + this.$refs.examine_Ref.checkRowIndex
              ].$refs[
                'inputSpan_Ref_' + itemComb.itemCode + '_' + idx
              ][0].blur();
            });
          });
        }
      }
      setTimeout(() => {
        this.saves(false).then((r) => {
          this.$refs.examine_Ref.abnormalPopup = false;
          this.$refs.examine_Ref.resultPopup = false;
          this.$refs.examine_Ref.editPopup = false;
          this.getNavList().then(() => {
            let datas = {
              regCombId: this.infoData.recordComb.regCombId,
              regNo: this.infoData.recordComb.regNo,
              template: this.$refs.sideTabs.checkGroup.template
            };
            this.getCombInfo(datas);
            this.$refs.registerRecordTable_ref.search();
          });
        });
      }, 300);
    },

    /**
     * @author: justin
     * @description: 保存的通用函数
     * @param {*} flag
     * @return {*}
     */
    saves(flag = true) {
      return new Promise((resolve, reject) => {
        if (this.resultState == 2) {
          setTimeout(() => {
            this.saves();
          }, 200);
          return;
        }

        if (!this.recordComb.doctorName) {
          this.$message({
            message: '请选择体检医师再保存!',
            type: 'warning',
            showClose: true
          });
          return;
        }

        let datas = {
          recordComb: {
            regNo: this.headerInfo.regNo,
            regCombId: this.regCombId,
            combCode: this.combCode,
            combName: this.recordComb.combName,
            clsCode: this.clsCode,
            operName: this.recordComb.operName || this.G_userInfo.codeOper.name,
            doctorName: this.recordComb.doctorName,
            isError: this.recordComb.isError,
            examTime: this.recordComb.examTime
          },
          // 项目
          recordItems:
            this.pageStatus === 3
              ? this.$refs.checkboxGroup.listData
              : this.infoData.recordItems,
          // 小结
          combTags: this.dynamicTags
        };
        this.$ajax.post(this.$apiUrls.SaveRecordCombV2, datas).then((r) => {
          let { success } = r.data;
          if (!success) return;
          this.$message({
            message: '保存成功!',
            type: 'success',
            showClose: true
          });
          this.isSave = true;
          this.isSendNotice(this.infoData.recordItems, this.headerInfo.regNo);
          if (flag) {
            this.initNavList();
          }
          this.recordComb.doctorName = this.G_userInfo.codeOper.name;
          this.errorDisabled = true;
          resolve();
          this.btnList.push('删除结果');
        });
      });
    },

    /**
     * @author: justin
     * @description: 删除结果
     * @return {*}
     */
    deleteRecordComb() {
      let data = {
        regNo: this.headerInfo.regNo,
        regCombId: this.regCombId
      };
      if (!this.regCombId) {
        this.$message({
          message: '请先选择要组合项目结果记录!',
          type: 'warning'
        });
        return false;
      }
      this.$confirm(`是否确定删除?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      })
        .then(() => {
          this.$ajax.post(this.$apiUrls.DeleteRecordCombV2, data).then((r) => {
            let { success } = r.data;
            if (!success) return;
            this.$message({
              message: '删除结果成功!',
              type: 'success',
              showClose: true
            });
            this.initNavList();

            this.getCombInfo({
              regCombId: this.infoData.recordComb.regCombId,
              regNo: this.infoData.recordComb.regNo,
              template: this.$refs.sideTabs.checkGroup.template
            });
            this.errorDisabled = true;
            let idx = this.btnList.indexOf('删除结果');
            if (idx != -1) {
              this.btnList.splice(idx, 1);
            }
          });
        })
        .catch(() => {});
    },

    /**
     * @author: justin
     * @description: 弃检
     * @return {*}
     */
    waiveBtn() {
      if (this.infoData.recordComb.doctorName == '弃检') {
        this.$message({
          message: '该组合已经弃检！',
          type: 'warning',
          showClose: true
        });
        return;
      }

      this.$confirm(`是否要弃检${this.infoData.recordComb.combName}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      })
        .then(() => {
          this.$ajax
            .post(this.$apiUrls.AbandonCombV2, {
              regNo: this.infoData.recordComb.regNo,
              regCombId: this.infoData.recordComb.regCombId,
              operName: this.G_userInfo.codeOper.name
            })
            .then((r) => {
              let { success, returnData } = r.data;
              if (!success) return;
              this.$message({
                message: '弃检成功!',
                type: 'success',
                showClose: true
              });

              this.getCombInfo({
                regCombId: this.infoData.recordComb.regCombId,
                regNo: this.infoData.recordComb.regNo,
                template: this.$refs.sideTabs.checkGroup.template
              });
              this.initNavList();
            });
        })
        .catch(() => {});
    },

    /**
     * @author: justin
     * @description: 初始化右边导航菜单
     * @return {*}
     */
    initNavList() {
      let examDeptCode = this.$refs.sideTabs.activeName;
      let liIndex = this.$refs.sideTabs.liIndex;
      this.getNavList().then((r) => {
        this.$refs.sideTabs.activeName = examDeptCode;
        this.$refs.sideTabs.liIndex = liIndex;
        this.$refs.registerRecordTable_ref.search();
      });
    },

    /**
     * @author: justin
     * @description: 关闭随访
     * @param {*} flag
     * @return {*}
     */
    followUpClose(flag) {
      this.followUpShow = flag;
    },

    /**
     * @author: justin
     * @description: 添加随访的弹窗显示
     * @return {*}
     */
    followUpClick() {
      if (!this.headerInfo.regNo) {
        this.$message({
          message: '请先选择需要随访的人！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$ajax
        .post(
          this.$apiUrls.HasNotHandledFollowUp +
            `?regNo=${this.headerInfo.regNo}`
        )
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          if (returnData) {
            this.$confirm('存在未处理随访,是否跳转随访记录', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
              .then(() => {
                this.followUpRecodeClick();
              })
              .catch(() => {
                return;
              });
          } else {
            this.followUpShow = true;
          }
        });
    },

    /**
     * @author: justin
     * @description: 纯音测听确认回调
     * @param {*} data 表单数据
     * @return {*}
     */
    pureToneAudiometryConfirm(data) {
      const that = this;
      if (!data || !that.infoData?.recordItems) return;

      // 结果
      that.infoData.recordItems.forEach((item, index) => {
        item.itemTags = [
          {
            id: 0,
            itemCode: item.itemCode,
            tag: data.examResult,
            abnormalType: 0,
            resultId: 0,
            isCalcResul: false,
            calcItemTagIds: []
          }
        ];
      });

      // 小结
      that.dynamicTags = [
        {
          bindItemTags: [],
          id: 0,
          isCustom: true,
          tag: data.examResult
        }
      ];
    },

    /**
     * @author: justin
     * @description: 打印图例
     * @return {*}
     */
    printImg() {
      if (!this.headerInfo.regNo) {
        this.$message({
          message: '请选择体检人',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.printImgList = [];
      this.printImgShow = true;
      this.$nextTick(() => {
        this.$refs.printImg_Ref.$refs.printImgMenu_Ref.activeIndex = null;
        let datas = {
          regNo: this.headerInfo.regNo
        };
        this.$ajax.paramsPost(this.$apiUrls.ReadPrintImage, datas).then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.printImgList = returnData || [];
        });
      });
    },

    /**
     * @author: justin
     * @description: 电话
     * @return {*}
     */
    phoneBtn() {
      if (!this.headerInfo.regNo) {
        this.$message({
          message: '请选择体检人',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.phoneShow = true;
      this.getPhoneList();
    },

    /**
     * @author: justin
     * @description: 获取电话联系列表
     * @return {*}
     */
    getPhoneList() {
      let datas = {
        regNo: this.headerInfo.regNo
      };
      this.$ajax
        .paramsPost(this.$apiUrls.ReadContactRecord, datas)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.phoneList = returnData || [];
        });
    },

    /**
     * @author: justin
     * @description: 添加咨询
     * @return {*}
     */
    addPhone() {
      this.innerVisible = true;
    },

    /**
     * @author: justin
     * @description: 确认联系
     * @return {*}
     */
    phoneConfirm() {
      if (!this.phoneContent) {
        this.$message({
          message: '请输入联系内容！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.innerVisible = false;
      let datas = {
        id: 0,
        content: this.phoneContent,
        createTime: '2022-11-29T06:40:13.143Z',
        regNo: this.headerInfo.regNo,
        contactPerson: this.G_userInfo.codeOper.name
      };
      this.$ajax.post(this.$apiUrls.CreateContactRecord, datas).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.getPhoneList();
      });
    },

    /**
     * @author: justin
     * @description: 联系列表的点击回调
     * @param {*} row
     * @return {*}
     */
    phoneRowClick(row) {
      this.checkPhone = row;
    },

    /**
     * @author: justin
     * @description: 删除联系信息
     * @return {*}
     */
    delPhone() {
      if (!this.checkPhone.regNo) {
        this.$message({
          message: '请选择要删除的联系！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      })
        .then(() => {
          let datas = {
            id: this.checkPhone.id
          };
          this.$ajax
            .paramsPost(this.$apiUrls.DeleteContactRecord, datas)
            .then((r) => {
              let { success, returnData } = r.data;
              if (!success) return;
              this.getPhoneList();
              this.$message({
                type: 'success',
                message: '删除成功!',
                showClose: true
              });
            });
        })
        .catch(() => {});
    },

    /**
     * @author: justin
     * @description: 电话弹窗关闭的回调
     * @return {*}
     */
    phoneClosed() {
      this.checkPhone = {};
    },

    /**
     * @author: justin
     * @description: 打印图例关闭的回调
     * @return {*}
     */
    printImgClosed() {
      this.$refs.printImg_Ref.imgSrc = '';
    },

    /**
     * @author: justin
     * @description: 咨询按钮点击
     * @return {*}
     */
    consultingClick() {
      if (!this.combCode) {
        this.$message({
          message: '请选择要咨询的项目！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$ajax
        .post(this.$apiUrls.IsExistQueue, '', {
          query: {
            questionerCode: this.G_userInfo.codeOper.operatorCode,
            combCode: this.combCode
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          if (returnData) {
            this.$message({
              message: '已咨询过该项目！',
              type: 'warning',
              showClose: true
            });
          } else {
            this.consultingShow = true;
          }
        });
    },

    /**
     * @author: justin
     * @description: 咨询发送
     * @return {*}
     */
    consultingSubmit() {
      if (!this.consultingInput) {
        this.$message({
          message: '请输入咨询内容！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let data = {
        regNo: this.$parent.$parent.$parent.regNo,
        combCode: this.combCode,
        combName: this.recordComb.combName,
        questionerCode: this.G_userInfo.codeOper.operatorCode,
        questionerName: this.G_userInfo.codeOper.name,
        questionContent: this.consultingInput,
        // replyerCode: "",
        replyerName: this.recordComb.operName || this.G_userInfo.codeOper.name
      };
      this.$ajax.post(this.$apiUrls.Question, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.$message({
          type: 'success',
          message: '咨询问题发送成功!',
          showClose: true
        });
        this.consultingShow = false;
      });
    },

    /**
     * @author: justin
     * @description: 跳转主检
     * @return {*}
     */
    navToMainInspection(pageName) {
      console.log(this.headerInfo);
      // return
      if (!this.headerInfo.regNo) {
        this.$message({
          type: 'warning',
          message: '请先选择体检人!',
          showClose: true
        });
        return;
      }
      this.$router.push({
        name: pageName,
        params: {
          regNo: this.headerInfo.regNo
        }
      });
    },

    /**
     * @author: justin
     * @description: 接收结果
     * @return {*}
     */
    acceptResult() {
      if (!this.headerInfo.regNo) {
        this.$message({
          type: 'warning',
          message: '请先选择体检人!',
          showClose: true
        });
        return;
      }
      this.$ajax
        .paramsPost(this.$apiUrls.ManuallyAcceptResults, {
          regNo: this.headerInfo.regNo
        })
        .then((r) => {
          let { success } = r.data;
          if (!success) return;
          this.$message({
            type: 'success',
            message: '接收结果成功！',
            showClose: true
          });
        });
    },

    /**
     * @author: justin
     * @description: 显示接收结果弹窗
     * @return {*}
     */
    resultBtnClick() {
      if (!this.headerInfo.regNo) {
        this.$message({
          type: 'warning',
          message: '请先选择体检人!',
          showClose: true
        });
        return;
      }
      this.showRecReportResult = true;
    },

    /**
     * @author: justin
     * @description: 关闭接收结果弹窗回调
     * @return {*}
     */
    closedRecReportResult() {
      this.showRecReportResult = false;
    },
    //人员列表
    userListShow() {
      if (this.userListIsGet) {
        this.getUserList();
        this.userListIsGet = false;
      }
      this.$refs.registerRecordTable_ref.$refs.publicTable_Ref1.$refs.tableCom_Ref.bodyWrapper.scrollTop =
        this.checkedScrollTop;
      this.$refs.registerRecordTable_ref.$refs.publicTable_Ref2.$refs.tableCom_Ref.bodyWrapper.scrollTop =
        this.notInspectedScrollTop;
    },
    //添加重大阳性
    significantPositiveResults() {
      if (this.significantPositiveStatus) {
        this.$router.push({
          path: '/dataQuery/majorAnomalyList',
          name: 'majorAnomalyList',
          query: {
            regNo: this.headerInfo.regNo
          }
        });
      } else {
        this.significantVisible = true;
      }
    },
    successCallback() {
      this.significantPositiveStatus = true;
    }
  },
  watch: {
    printImgShow: {
      handler(n, o) {
        if (n) {
          this.connectPrint((r) => {
            let dataInfo = JSON.parse(r.data);

            this.printList = dataInfo.Data;
          });
        } else {
          if (JSON.stringify(this.ws) == '{}') return;
          this.ws.closeWebSocket && this.ws.closeWebSocket();
          this.ws.reConnect = () => {};
          this.ws.createWebSocket = () => {};
          this.ws.heartCheck.stop();
        }
      }
    },
    infoData: {
      handler(n, o) {
        this.isOpenKeysFlag = false;
        if (this.infoData?.recordComb?.doctorName == '弃检') {
          delete this.shortcutList[113];
          this.shortcutList[114] = this.deleteRecordComb;
        } else {
          this.shortcutList[113] = this.amend;
          this.shortcutList[114] = this.waiveBtn;
        }
        if (JSON.stringify(n) !== '{}') {
          addEventListener('keyup', this.keyUpFun);
          addEventListener('keydown', this.keyDownFun);
        } else {
          this.isOpenKeysFlag = true;
          this.$nextTick(() => {
            removeEventListener('keyup', this.keyUpFun);
            removeEventListener('keydown', this.keyDownFun);
          });
        }
      },
      immediate: true,
      deep: true
    },
    G_sysOperator: {
      handler(n, o) {
        this.doctorList = n;
      },
      immediate: true,
      deep: true
    },
    headerInfo: {
      handler: function (n, o) {
        this.HisPatInfo = n;
        if (n.regNo) {
          this.getImgList();
        }
      },
      deep: true
    },
    major: {
      handler(val, oldVal) {
        this.significantPositiveStatus = val?.id ? true : false;
      },
      deep: true,
      immediate: true
    }
  },
  // 组件激活
  activated() {
    let obj = this.$route.params;
    this.routeRow = obj;
    if (JSON.stringify(obj) != '{}') {
      this.resDrawer = true;
    }
  }
};
</script>
<template>
  <div class="result-entrt-container doctor_media" @click="pageClick">
    <!-- 左侧登记记录筛选列表 -->
    <div class="left-wrapper left_box" v-if="mainInspectionFlag">
      <RegisterRecordTable
        ref="registerRecordTable_ref"
        type="resultEntry"
        @rowDblClick="rowDblClickRegRecord"
        @search="registerRecordTableSearch"
        @clearResultFocus="pageClick"
      />
    </div>

    <!-- 右侧结果录入内容 -->
    <div class="right-wrapper">
      <!-- 头部 -->
      <el-row type="flex" align="center" class="header">
        <el-col :span="24">
          <!-- 顶部搜索栏内容 -->
          <el-row
            type="flex"
            justify="space-between"
            align="center"
            class="search"
          >
            <el-col :span="6" style="display: flex" v-if="mainInspectionFlag">
              <el-popover
                placement="bottom"
                popper-class="register_popverClass"
                width="400"
                trigger="click"
                @after-enter="userListShow"
              >
                <div class="left-wrapper" @click="pageClick">
                  <RegisterRecordTable
                    ref="registerRecordTable_ref"
                    type="resultEntry"
                    @rowDblClick="rowDblClickRegRecord"
                    @search="registerRecordTableSearch"
                    @clearResultFocus="pageClick"
                  />
                </div>
                <el-button
                  class="blue_btn btn query_btn"
                  size="small"
                  icon="iconfont icon-liebiao"
                  style="margin-right: 10px"
                  slot="reference"
                  >人员列表</el-button
                >
              </el-popover>
              <el-input
                size="small"
                placeholder="体检号"
                clearable
                v-model="searchRegNoIpt"
                @keyup.enter.native="searchRegNo"
                class="searchIpt"
                v-focus
              >
              </el-input>
            </el-col>
            <el-col :span="2"> </el-col>
            <el-col :span="16" class="btn-box">
              <BtnCommon
                :btnList="[]"
                :isModify.sync="isModify"
                @historyReport="historyReport"
                @collection="collection"
                @amend="amend"
                @saves="savesBtn"
                @delResult="deleteRecordComb"
                @refuseCheck="waiveBtn"
                @sendConsultation="sendConsultation"
                @responseConsultation="responseConsultation"
                :infoData="infoData"
                :headerInfo="headerInfo"
              >
                <template #footAdd>
                  <el-button
                    class="green_btn btn"
                    :class="{ 'questionnaire-disabled': questionnaireDisabled }"
                    size="small"
                    icon="el-icon-tickets"
                    @click="questionnaire"
                    >问卷调查</el-button
                  >
                  <span v-if="mainInspectionFlag">
                    <el-button
                      class="blue_btn btn"
                      size="small"
                      icon="iconfont icon-shenhe1"
                      v-if="!C_isOccupation"
                      @click="
                        navToMainInspection(
                          headerInfo.isOccupation
                            ? 'mainInspectionV2'
                            : 'mainInspection'
                        )
                      "
                    >
                      跳转主检
                    </el-button>
                    <el-dropdown v-else>
                      <el-button
                        class="blue_btn btn"
                        icon="iconfont icon-shenhe1"
                      >
                        跳转主检<i
                          class="el-icon-arrow-down el-icon--right"
                        ></i>
                      </el-button>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item
                          @click.native="navToMainInspection('mainInspection')"
                          >跳转健康检</el-dropdown-item
                        >
                        <el-dropdown-item
                          @click.native="
                            navToMainInspection('mainInspectionV2')
                          "
                          >跳转职业检</el-dropdown-item
                        >
                      </el-dropdown-menu>
                    </el-dropdown>
                  </span>

                  <el-button
                    class="yellow_btn btn"
                    size="small"
                    icon="iconfont icon-dianhuazixun-dianhua"
                    @click="phoneBtn"
                    v-if="mainInspectionFlag"
                  >
                    电话
                  </el-button>

                  <el-button
                    class="violet_btn btn"
                    size="small"
                    icon="iconfont icon-jieshou"
                    @click="resultBtnClick"
                    v-if="mainInspectionFlag"
                  >
                    接收结果
                  </el-button>

                  <el-button
                    class="violet_btn btn"
                    size="small"
                    icon="iconfont icon-zaixianzixun"
                    @click="consultingClick"
                    v-if="!mainInspectionFlag && !processFlag && isMain == 2"
                  >
                    咨询
                  </el-button>

                  <el-button
                    class="yellow_btn btn"
                    size="small"
                    @click="consultList"
                    icon="iconfont icon-changyonghuifu"
                    >咨询列表</el-button
                  >
                  <el-button
                    @click="historyReport"
                    class="violet_btn btn"
                    size="small"
                    icon="iconfont icon-a-lishijilubiaodanjishi"
                    v-if="mainInspectionFlag"
                    >历史报告</el-button
                  >
                  <!-- <el-button
                    size="small"
                    class="green_btn btn"
                    @click="imageLegengBtnClick"
                    :disabled="!isHaveImgListFlag"
                    >影像图例</el-button
                  > -->
                  <el-button
                    size="small"
                    class="green_btn btn"
                    @click="imgTextClick"
                    v-if="mainInspectionFlag && headerInfo.regNo"
                    >图文报告</el-button
                  >

                  <!-- <el-button
                    size="small"
                    class="green_btn btn"
                    @click="followUpClick"
                    v-if="mainInspectionFlag"
                    >添加随访</el-button
                  > -->

                  <el-button
                    size="small"
                    class="green_btn btn"
                    @click="followUpRecodeClick"
                    v-if="isHasFollowUp && mainInspectionFlag"
                    >随访记录</el-button
                  >
                </template>
              </BtnCommon>
            </el-col>
          </el-row>

          <!-- 体检人信息 -->
          <el-row
            type="flex"
            justify="space-between"
            align="center"
            class="patient"
          >
            <el-col
              :span="G_config.physicalMode.includes('职检') ? 2 : 0"
              v-if="G_config.physicalMode.includes('职检')"
            >
              <el-image
                fit="contain"
                :src="headerInfo.photoUrl"
                style="width: 5.5rem; height: 6.5rem; border-radius: 5px"
                alt="头像"
              ></el-image>
            </el-col>
            <el-col :span="G_config.physicalMode.includes('职检') ? 22 : 24">
              <!-- 基本信息 -->
              <div
                class="info-box"
                :style="{
                  'margin-top': G_config.physicalMode.includes('职检')
                    ? '10px'
                    : '0px'
                }"
              >
                <div class="row-box">
                  <div class="item-box" style="width: 15%">
                    <label>体检号:</label>
                    <p>{{ headerInfo.regNo }}</p>
                  </div>
                  <div class="item-box" style="width: 15%">
                    <label>姓名:</label>
                    <p>{{ headerInfo.name }}</p>
                  </div>
                  <div class="item-box" style="width: 10%">
                    <label>性别:</label>
                    <p>{{ G_EnumList['Sex'][headerInfo.sex] }}</p>
                  </div>
                  <div class="item-box" style="width: 10%">
                    <label>年龄:</label>
                    <p>{{ headerInfo.age }}</p>
                  </div>
                  <div class="item-box" style="max-width: 30%">
                    <label>套餐:</label>
                    <p :title="headerInfo.clusterName">
                      {{ headerInfo.clusterName }}
                    </p>
                  </div>
                </div>

                <div
                  class="row-box"
                  v-if="G_config.physicalMode.includes('职检')"
                >
                  <div class="item-box" style="width: 15%">
                    <label>岗位:</label>
                    <p>
                      {{
                        G_EnumList['CodeOccupationalPositionStatus'][
                          headerInfo.jobstatus
                        ]
                      }}
                    </p>
                  </div>

                  <div class="item-box" style="width: 50%">
                    <label>危害因素:</label>
                    <p :title="headerInfo.hazardousFators">
                      {{ headerInfo.hazardousFators }}
                    </p>
                  </div>
                </div>
              </div>

              <!-- 项目操作按钮 -->
              <div class="btn-box">
                <BtnCommon
                  :btnList="btnList"
                  :btnListState="btnListState"
                  @collection="collection"
                  @amend="amend"
                  :isModify.sync="isModify"
                  @saves="savesBtn"
                  @delResult="deleteRecordComb"
                  @refuseCheck="waiveBtn"
                  :infoData="infoData"
                  :headerInfo="headerInfo"
                  v-if="mainInspectionFlag"
                >
                  <template #headAdd>
                    <el-button
                      v-if="showPureToneAudiometryBtn"
                      class="yellow_btn btn"
                      size="small"
                      @click.native="showPureToneAudiometry = true"
                      icon="el-icon-headset"
                      >纯音测听</el-button
                    >
                    <el-button
                      v-if="showSmokingHistoryBtn"
                      class="yellow_btn btn"
                      size="small"
                      @click.native="showPatientInfoDialog = true"
                      icon="el-icon-s-order"
                      :disabled="!HisPatInfo.isOccupation"
                      >职业问诊</el-button
                    >
                    <el-button
                      v-if="showSymptomBtn"
                      class="yellow_btn btn"
                      size="small"
                      @click.native="showSymptom = true"
                      icon="el-icon-smoking"
                      >症状询问</el-button
                    >
                  </template>
                  <template #footAdd>
                    <el-button
                      size="small"
                      class="green_btn btn"
                      @click="significantPositiveResults"
                      v-if="C_significant"
                      >{{
                        `${significantPositiveStatus ? '查看' : '添加'}重大阳性`
                      }}</el-button
                    >
                  </template>
                </BtnCommon>
              </div>
            </el-col>
          </el-row>
        </el-col>
      </el-row>

      <!-- 内容 -->
      <div class="body">
        <div class="contLeft">
          <!-- 项目头部栏 -->
          <div class="header-box">
            <div class="left">
              <span class="title" :title="recordComb.combName">
                {{ recordComb.combName }}</span
              >
            </div>

            <div class="right">
              <div class="every_inp">
                <el-checkbox
                  v-model="recordComb.isError"
                  :disabled="errorDisabled"
                  >异常</el-checkbox
                >
              </div>
              <div class="every_inp">
                <label>操作员:</label>
                <p>
                  {{
                    recordComb.operName === null
                      ? G_userInfo.codeOper.name
                      : recordComb.operName
                  }}
                </p>
              </div>
              <div class="every_inp">
                <label>检查日期</label>
                <p>
                  <el-date-picker
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    :clearable="false"
                    size="mini"
                    v-model="recordComb.examTime"
                    style="width: 120px"
                  >
                  </el-date-picker>
                </p>
              </div>
              <div class="every_inp">
                <label title="第三方传入的数据显示第三方医生名字"
                  >体检医师</label
                >
                <p>
                  <el-select
                    v-model="recordComb.doctorName"
                    :disabled="!isModify"
                    size="mini"
                    clearable
                    filterable
                    placeholder="请选择"
                    allow-create
                    default-first-option
                    remote
                    @visible-change="showDoctorList"
                    @focus="pageClick"
                    :remote-method="doctorfilter"
                  >
                    <el-option
                      v-for="item in doctorList"
                      :key="item.index"
                      :label="item.label"
                      :value="item.label"
                    >
                    </el-option>
                  </el-select>
                </p>
              </div>
            </div>
          </div>

          <!-- 项目内容 -->
          <div class="rightTableDiv">
            <CheckboxGroup
              :infoData="infoData"
              v-show="pageStatus == 3"
              ref="checkboxGroup"
              :isModify.sync="isModify"
            />

            <!-- 表格内容 -->
            <div
              class="column-right"
              style="flex: 1; flex-shrink: 0; overflow: auto"
              v-show="pageStatus !== 3"
            >
              <ExamineV3
                :isModify.sync="isModify"
                :combData.sync="infoData"
                :mode="pageStatus"
                :headerInfo="headerInfo"
                :checkComb.sync="checkComb"
                :dynamicTags="dynamicTags"
                @dynamicTagsChange="(dynamic) => (dynamicTags = dynamic)"
                ref="examine_Ref"
                v-show="pageStatus == 2 || pageStatus == 1"
              />
            </div>

            <!-- 小结 -->
            <div class="nodulus">
              <PhysicalSummary
                ref="physicalSummary"
                :isModify.sync="isModify"
                :combData.sync="infoData"
                :dynamicTags.sync="dynamicTags"
              />
            </div>
          </div>
        </div>

        <!-- 右侧项目导航栏 -->
        <div class="contRight">
          <SideTabs
            :headerInfo="headerInfo"
            :navList="navList"
            @navClickFun="sideTabsNavClickFun"
            ref="sideTabs"
          />
        </div>
      </div>
    </div>

    <!-- 弹窗内容 -->
    <template>
      <el-drawer title="影像图例" size="90%" :visible.sync="ImageLegengShow">
        <ImageLegeng
          ref="ImgText_Ref"
          :headerInfo="headerInfo"
          :checkComb.sync="checkComb"
          v-if="ImageLegengShow"
        />
      </el-drawer>

      <el-drawer title="图文报告" size="90%" :visible.sync="ImgTextShow">
        <ImgText
          ref="ImgText_Ref"
          :printImgList.sync="ImgTextList"
          :headerInfo="headerInfo"
          :checkComb.sync="checkComb"
          @uploadBtnClick="uploadBtnClick"
          v-if="ImgTextShow"
        />
        <el-drawer
          title="上传图文报告"
          :append-to-body="true"
          size="80%"
          custom-class="upload_drawer"
          destroy-on-close
          @close="$refs.UploadImgText_Ref.closeCamer('video')"
          :visible.sync="innerDrawer"
        >
          <UploadImgText
            ref="UploadImgText_Ref"
            @close="innerDrawer = false"
            @update="getImgTextList(headerInfo.regNo)"
            :showFlag="innerDrawer"
            :headerInfo="headerInfo"
            :combsList.sync="ImgTextList"
            :navList.sync="navList"
          />
        </el-drawer>
      </el-drawer>

      <el-drawer
        title="请求会诊"
        :visible.sync="sendDrawer"
        :before-close="sendDrawerHandleClose"
        :wrapperClosable="false"
        size="90%"
        v-if="sendDrawer"
      >
        <ReConsultation
          :responseConsultation="responseConsultation"
          :headerInfo="headerInfo"
        />
      </el-drawer>

      <el-drawer
        title="响应会诊"
        :visible.sync="resDrawer"
        :before-close="handleClose"
        :wrapperClosable="false"
        size="90%"
        v-if="resDrawer"
      >
        <Response :routeRow="routeRow" :headerInfo="headerInfo" :id="id" />
      </el-drawer>

      <el-drawer
        title="历史报告"
        :visible.sync="hisDrawer"
        :before-close="hisDrawerClose"
        :wrapperClosable="false"
        size="90%"
        v-if="hisDrawer"
      >
        <HistoryReport :regNo="headerInfo.regNo" />
      </el-drawer>

      <el-drawer
        title="采集图片"
        :visible.sync="pictureDrawer"
        :before-close="pictureDrawerClose"
        :wrapperClosable="false"
        size="90%"
        v-if="pictureDrawer"
      >
        <CollectPictures
          :headerInfo="headerInfo"
          :propRegCombId="regCombId"
          :regCombs="regCombs"
          :deptCode="deptCode"
        />
      </el-drawer>

      <!-- 电话 -->
      <el-dialog
        title="电话联系记录"
        :visible.sync="phoneShow"
        width="60%"
        @closed="phoneClosed"
      >
        <el-dialog
          width="30%"
          title="联系内容"
          :visible.sync="innerVisible"
          append-to-body
        >
          <el-input
            type="textarea"
            :autosize="{ minRows: 4, maxRows: 4 }"
            placeholder="请输入内容"
            @keypress.enter.native="phoneConfirm"
            v-model.trim="phoneContent"
          >
          </el-input>
          <div style="text-align: right; padding-top: 10px">
            <el-button class="blue_btn btn" size="small" @click="phoneConfirm">
              确定
            </el-button>
          </div>
        </el-dialog>
        <div class="">
          <div class="phone_btn">
            <el-button
              class="blue_btn btn"
              size="small"
              icon="iconfont icon-xinjian"
              @click="addPhone"
            >
              添加
            </el-button>
            <el-button
              class="red_btn btn"
              size="small"
              icon="iconfont icon-shanchu"
              @click="delPhone"
            >
              删除
            </el-button>
          </div>
          <div style="height: 500px">
            <PublicTable
              :theads="phoneTheads"
              :columnWidth="phoneColumn"
              :viewTableList="phoneList"
              @rowClick="phoneRowClick"
              :isSortShow="false"
            />
          </div>
        </div>
      </el-dialog>

      <!-- 咨询弹窗 -->
      <el-dialog
        :visible.sync="consultingShow"
        width="800px"
        top="12%"
        custom-class="consultingShow"
        :close-on-click-modal="false"
        @closed="consultingShow = false"
      >
        <div slot="title" class="dialog-title">咨询录入窗口</div>
        <el-input
          type="textarea"
          :rows="20"
          placeholder="请输入咨询内容"
          v-model="consultingInput"
          style="margin-top: 20px"
        >
        </el-input>
        <div slot="footer" class="dialog-footer">
          <el-button @click="consultingShow = false" size="small"
            >取消</el-button
          >
          <el-button class="blue_btn" @click="consultingSubmit" size="small"
            >确定</el-button
          >
        </div>
      </el-dialog>

      <!-- 咨询消息列表 -->
      <el-dialog
        :visible.sync="consultationReply"
        width="1260px"
        top="6%"
        custom-class="consultationReply"
        :close-on-click-modal="false"
        @close="consultationReply = false"
      >
        <div slot="title" class="dialog-title">咨询回复</div>
        <h3 class="replyTitle">医生咨询列表：</h3>
        <div class="replyTable">
          <PublicTable
            :viewTableList.sync="replyData"
            :theads.sync="theads"
            :isSortShow="false"
            :columnWidth="{
              regNo: 124,
              combName: 120,
              questionTime: 166,
              questionContent: 200,
              replyTime: 166,
              replyContent: 200
            }"
          >
            <template #columnRight>
              <el-table-column width="60" label="操作">
                <template slot-scope="scope">
                  <el-popover
                    placement="bottom-end"
                    width="380"
                    v-model="scope.row.backoutPopoverShow"
                    trigger="click"
                  >
                    <el-input
                      type="textarea"
                      :autosize="{ minRows: 6, maxRows: 6 }"
                      placeholder="请输入内容"
                      v-model.trim="scope.row.replyContent"
                    >
                    </el-input>
                    <div class="reply-footer">
                      <el-button
                        @click="scope.row.backoutPopoverShow = false"
                        size="small"
                        >取消</el-button
                      >
                      <el-button
                        class="blue_btn"
                        @click="replySubmit(scope.row)"
                        size="small"
                        >确定</el-button
                      >
                    </div>
                    <div slot="reference" class="replyText">回复</div>
                  </el-popover>
                </template>
              </el-table-column>
            </template>
          </PublicTable>
        </div>
      </el-dialog>

      <!-- 医生咨询回复一览表 -->
      <el-drawer
        title="医生咨询回复一览表"
        :visible.sync="consultDialog"
        :before-close="consultDialogClose"
        :wrapperClosable="false"
        size="90%"
        v-if="consultDialog"
      >
        <Consult :conclusionList="conclusionList" />
      </el-drawer>

      <!-- 添加随访 -->
      <FollowUp
        :regNo="headerInfo.regNo"
        :followUpShow.sync="followUpShow"
        @followUpClose="followUpClose"
      />

      <!-- 纯音测听组件 -->
      <el-dialog
        title="纯音测听"
        width="100%"
        top="0px"
        :visible.sync="showPureToneAudiometry"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        class="pure-tone-audiometry-dialog"
        :key="'pureToneAudiometryDialog_' + headerInfo.regNo"
      >
        <div style="transform: scale(0.8); transform-origin: top left">
          <div class="pureToneAudiometry_wrap">
            <div class="pureToneAudiometry_dom">
              <PureToneAudiometry
                ref="pureToneAudiometry_Ref"
                :patientInfo="headerInfo"
                :combCode="combCode"
                :inputInfo="recordComb"
                @cancel="
                  (data) => {
                    this.showPureToneAudiometry = false;
                  }
                "
                @calc="pureToneAudiometryConfirm"
                @confirm="savesBtn"
              />
            </div>

            <div class="result_wrap">
              <div style="margin-bottom: 10px; min-height: 500px">
                <ExamineV3
                  :isModify.sync="isModify"
                  :combData.sync="infoData"
                  :mode="pageStatus"
                  :headerInfo="headerInfo"
                  :checkComb.sync="checkComb"
                  :dynamicTags="dynamicTags"
                  @dynamicTagsChange="(dynamic) => (dynamicTags = dynamic)"
                  :showPureToneAudiometry="showPureToneAudiometry"
                  ref="dialog_examine_Ref"
                  v-show="pageStatus == 2 || pageStatus == 1"
                />
              </div>
              <div style="height: 100px; overflow: auto">
                <PhysicalSummary
                  ref="physicalSummary"
                  :isModify.sync="isModify"
                  :combData.sync="infoData"
                  :dynamicTags.sync="dynamicTags"
                  :showPureToneAudiometry="showPureToneAudiometry"
                />
              </div>
            </div>
          </div>
        </div>
      </el-dialog>

      <!-- 吸烟史组件 -->
      <el-dialog
        title="吸烟史"
        width="380px"
        top="20%"
        :visible.sync="showSmokingHistory"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        class="smoking-history-dialog"
        :key="'smokingHistoryDialog_' + headerInfo.regNo"
      >
        <SmokingHistory
          ref="smokingHistory_Ref"
          :regNo="headerInfo.regNo"
          @cancel="
            () => {
              this.showSmokingHistory = false;
            }
          "
        />
      </el-dialog>

      <!-- 症状询问组件 -->
      <el-dialog
        title="症状询问"
        width="80%"
        top="10%"
        :visible.sync="showSymptom"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        class="smoking-history-dialog"
        :key="'symptomResultDialog_' + headerInfo.regNo"
      >
        <SymptomResult
          ref="symptomResult_Ref"
          :regNo="headerInfo.regNo"
          @cancel="
            () => {
              this.showSymptom = false;
            }
          "
        />
      </el-dialog>

      <!-- 接收结果 -->
      <ReceiveReportResult
        :visible="showRecReportResult"
        :regNo="headerInfo.regNo"
        @closed="closedRecReportResult"
      />
      <SignificantPositive
        :dialogVisible.sync="significantVisible"
        :recordComb="recordComb"
        :major="major"
        @successCallback="successCallback"
      />

      <el-dialog
        @open="informatoryOpen"
        :close-on-press-escape="false"
        title="职业问诊"
        fullscreen
        :visible.sync="showPatientInfoDialog"
        class="questionnaire_dig"
      >
        <InformationInvestigation
          @cancel="informatoryCancel"
          @success="informatorySuccess"
          ref="informationInvestigation"
          :regNo="headerInfo.regNo"
          :sex="headerInfo.sex"
        />
      </el-dialog>
      <el-dialog
        title="问卷调查表"
        :visible.sync="isShowdialogTable"
        class="questionnaire_dig"
      >
        <Questionnaire
          :formInfo="formInfo"
          @submitSuccess="submitSuccess"
          :questionnaireDisabled="questionnaireDisabled"
        ></Questionnaire>
      </el-dialog>
    </template>
  </div>
</template>

<style lang="less" scoped>
// 局部组件样式
.result-entrt-container {
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  .pure-tone-audiometry-dialog {
    /deep/.el-dialog {
      height: 100%;
      margin: 0 auto;
    }
  }
  // 右侧内容
  .right-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #fff;
    padding: 0 10px;
    min-width: 300px;
    max-width: 100%;
    // 头部信息
    .header {
      display: flex;
      flex-flow: column;
      align-items: center;
      justify-content: center;
      flex: 0;
      label,
      p {
        min-width: fit-content;
      }
      .el-row {
        margin: 10px 0;
      }

      // 搜索栏
      .search {
        align-items: baseline;

        .btn-box {
          display: flex;
          justify-content: end;
        }
      }

      // 体检人信息
      .patient {
        .info-box {
          display: flex;
          flex-flow: column;

          .row-box {
            display: flex;
            align-content: center;
            padding-bottom: 10px;
          }

          .item-box {
            display: flex;
            align-items: center;
            margin-right: 20px;

            label {
              font-family: PingFangSC-Semibold;
              font-size: 16px;
              color: #2b3436;
              text-align: right;
              font-weight: 600;
              margin-right: 10px;
            }

            p {
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
              font-family: PingFangSC-Regular;
              font-size: 16px;
              color: #2b3436;
              font-weight: 400;
            }
          }
        }

        .btn-box {
          display: flex;
          align-items: center;
          justify-content: end;
        }
      }
    }

    // 内容
    .body {
      display: flex;
      flex: 1;
      overflow: auto;
      will-change: width, height;
      .contLeft {
        flex: 1;
        flex-shrink: 0;
        will-change: width, height;
        display: flex;
        flex-direction: column;
        border: 1px solid #eee;
        border-radius: 4px;
        overflow: auto;

        .header-box {
          height: 52px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          line-height: 52px;
          border-bottom: 1px solid #d8dee1;
          padding: 0 10px;

          .left {
            font-size: 18px;
            color: #1770df;
            font-weight: 600;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            font-family: PingFangSC-Medium;
            margin-right: 10px;
          }
          .right {
            display: flex;
            justify-content: flex-end;
            align-items: center;

            .every_inp {
              display: flex;
              align-items: center;
              justify-content: end;
              margin-right: 10px;

              /deep/.el-checkbox__input.is-checked .el-checkbox__inner {
                background: #d63031;
                border: 1px solid #d63031;
                border-radius: 2px;
                border-radius: 2px;
              }
              /deep/.el-checkbox__input.is-checked + .el-checkbox__label {
                font-family: PingFangSC-Regular;
                font-size: 14px;
                color: #d63031;
              }
              /deep/.el-checkbox__input.is-disabled.is-checked
                .el-checkbox__inner::after {
                border-color: #fff;
              }
              /deep/.el-checkbox__inner::after {
                height: 10px;
                left: 8px;
                position: absolute;
                top: 2px;
              }
              /deep/ .el-input--suffix .el-input__inner {
                padding-right: 0 !important;
              }
              & > label {
                font-family: PingFangSC-Medium;
                font-size: 14px;
                color: #2b3436;
                font-weight: 550;
              }
              & > p {
                font-family: PingFangSC-Regular;
                color: #2d3436;
              }
              &:nth-child(1) {
                & > label {
                  margin-right: 10px;
                }

                /deep/.el-checkbox__inner {
                  width: 21px;
                  height: 21px;
                }
              }
              &:nth-child(2) {
                & > label {
                  width: 60px;
                }
                & > p {
                  width: 80px;
                }
              }
              &:nth-child(3) {
                & > label {
                  width: 65px;
                }
                & > p {
                  width: 130px;
                }
              }
              &:nth-child(4) {
                & > label {
                  width: 65px;
                }
                & > p {
                  width: 100px;
                }
              }
            }
          }
        }

        // 表格内容
        .rightTableDiv {
          flex: 1;
          flex-shrink: 0;
          overflow: auto;
          display: flex;
          flex-direction: column;
          will-change: width, height;
        }

        // 小结
        .nodulus {
          position: relative;
          height: 100px;
          border: 1px solid #eee;
          overflow: auto;
          border-radius: 4px;
          border-right: 0;
        }
      }

      // 右侧项目导航栏
      .contRight {
        width: 220px;
        height: 100%;
        overflow: auto;
        border: 1px solid #eee;
        border-radius: 4px;
        will-change: width;
      }
    }
  }

  // 杂项
  /deep/.el-date-editor.el-input,
  /deep/.el-date-editor.el-input__inner {
    width: 100%;
  }
  /deep/.el-drawer__header {
    align-items: center;
    display: flex;
    background: rgba(23, 112, 223, 0.2);
    border-radius: 4px 4px 0 0;
    line-height: 42px;
    font-family: PingFangSC-Medium;
    font-size: 18px;
    padding: 0;
    padding-left: 20px;
    color: #2d3436;
    margin-bottom: 0;
  }
  /deep/.el-drawer__close-btn {
    font-size: 30px;
  }
  /deep/.el-dialog__header {
    height: 50px;
    line-height: 50px;
    background: rgba(23, 112, 223, 0.2);
    padding: 0 18px;
  }
  /deep/.el-dialog__headerbtn {
    top: 10px;
    font-size: 30px;
  }
  /deep/.el-dialog__body {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 5px;
    flex-shrink: 0;
    overflow: auto;
    height: calc(100% - 50px);
  }
  .replyTable {
    height: calc(100% - 50px);
    border: 1px solid rgba(178, 190, 195, 0.5);
    border-radius: 4px;
  }
  .replyTitle {
    font-size: 18px;
    color: #2d3436;
    margin-top: 8px;
    margin-bottom: 18px;
  }
  .replyText {
    color: #1770df;
    cursor: pointer;
  }
  .reply-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
  }
  .btn {
    padding: 6.5px 10px;
  }
  .phone_btn {
    text-align: right;
    margin-bottom: 10px;
  }
}
// 左侧导航栏
.left-wrapper {
  width: 400px;
  margin-right: 7px;
}
// 左侧导航栏
.left-wrapper {
  width: 400px;
  height: 100%;
  margin-right: 7px;
}
.query_btn {
  display: none;
}
@media screen and (max-width: 1500px) {
  .query_btn {
    display: inline-block;
  }
  .left_box {
    display: none;
  }
}
@import url(./css/pureToneAudiometry.less);
@import url(./css/doctorMedia.less);
</style>

<style lang="less">
// 弹窗样式
.container {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.popverClass {
  height: calc(100% - 200px);
  .leftBody {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .group_head {
    li {
      display: flex;
      margin-bottom: 12px;
      &:last-child {
        margin-bottom: 0;
      }
    }

    .every_inp {
      display: flex;
      align-items: center;
      margin-right: 10px;
      width: 100%;
      font-family: PingFangSC-Medium;
      font-size: 14px;
      color: #2d3436;
      label {
        width: 70px;
      }
      .btnLabel {
        margin-left: 10px;
        margin-right: 0;
      }
      p {
        flex: 1;
      }

      &:nth-child(4) {
        flex: 1;
      }
    }
    .times {
      width: 100%;
      .el-date-editor--daterange.el-input__inner {
        width: 100%;
      }
    }
  }
  .el-tab-pane {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: auto;
  }

  .tableDiv {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
  }
  .tabs_wrap {
    height: 100%;
    display: flex;
    flex-direction: column;

    .el-tabs__content {
      padding: 0;
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
    }

    .el-tab-pane {
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: auto;
    }

    .table_wrap {
      flex: 1;
      overflow: auto;
    }
  }
}
/deep/.hisDialogClass {
  height: 800px !important;
  display: flex;
  flex-direction: column;
  padding: 15px;
}
.consultationReply {
  height: 700px;
  overflow: auto;
}
.reply-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}
.receiveResult_content {
  header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
  }
  .result_list {
    height: 500px;
  }
}
.questionnaire-disabled {
  opacity: 0.5;
}
.upload_drawer {
  .el-drawer__header {
    align-items: center;
    display: flex;
    background: rgba(23, 112, 223, 0.2);
    border-radius: 4px 4px 0 0;
    line-height: 42px;
    font-family: PingFangSC-Medium;
    font-size: 18px;
    padding: 0;
    padding-left: 20px;
    color: #2d3436;
    margin-bottom: 0;
  }
}
</style>
