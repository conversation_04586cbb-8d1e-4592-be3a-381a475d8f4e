<template>
  <!-- 采集配管 -->
  <div class="collectPiping">
    <div class="header-wrap">
      <ul class="search-wrap">
        <li>
          <div class="search-item">
            <label class="search-label">体检号</label>
            <span>
              <el-input
                v-model.trim="searchInfo.regNo"
                size="mini"
                placeholder="请输入"
                clearable
                class="input"
                @keyup.enter.native="regNoEnter"
                v-focus
              ></el-input>
            </span>
          </div>
          <div class="search-item">
            <label class="search-label">试管号</label>
            <span>
              <el-input
                v-model.trim="searchInfo.sampleNo"
                @keyup.enter.native="searchClick"
                size="mini"
                placeholder="请输入"
                clearable
                class="input"
              ></el-input>
            </span>
          </div>
          <div class="search-item">
            <label class="search-label">姓名</label>
            <span>
              <el-input
                v-model.trim="searchInfo.name"
                size="mini"
                placeholder="请输入"
                clearable
                class="input"
              ></el-input>
            </span>
          </div>
          <div class="search-item" style="width: 30%">
            <label class="search-label">身份证</label>
            <span class="idCard">
              <el-input
                v-model.trim="searchInfo.cardNo"
                size="mini"
                placeholder="请输入"
                style="margin-right: 10px"
                clearable
                class="input"
              ></el-input>
              <BtnCommon :btnList="['身份证']"></BtnCommon>
            </span>
          </div>
          <div class="search-item" style="width: 10%">
            <BtnCommon :btnList="['查询']" @search="searchClick"></BtnCommon>
          </div>
        </li>
        <li>
          <div class="search-item" style="justify-content: center">
            <el-radio-group
              v-model.trim="searchInfo.type"
              @change="searchClick"
            >
              <el-radio :label="0">全部</el-radio>
              <el-radio :label="1">个人</el-radio>
              <el-radio :label="2">团体</el-radio>
            </el-radio-group>
          </div>
          <div class="search-item">
            <label class="search-label">单位</label>
            <el-cascader
              ref="company_cascader_ref"
              placeholder="请选择单位"
              v-model="searchInfo.companyCode"
              :options="companyList"
              :props="{ multiple: false }"
              clearable
              filterable
              size="mini"
              collapse-tags
              :filter-method="filterMethod"
              @change="companyChange"
            >
            </el-cascader>
          </div>
          <div class="search-item">
            <label class="search-label">条码分类</label>
            <span>
              <el-select
                v-model.trim="searchInfo.barcode"
                placeholder="请选择条码分类"
                size="mini"
                class="input"
                filterable
                clearable
              >
                <el-option
                  :label="item.barcodeName"
                  :value="item.barcode"
                  v-for="item in barcodeType"
                  :key="item.barcode"
                >
                  <span style="margin-right: 30px">{{ item.barcode }}</span>
                  <span>{{ item.barcodeName }}</span>
                </el-option>
              </el-select>
            </span>
          </div>
          <div class="search-item" style="width: 30%; margin-right: 12%">
            <label class="search-label">体检日期</label>
            <span style="width: 100%">
              <el-date-picker
                :picker-options="{ shortcuts: G_datePickerShortcuts }"
                class="input"
                v-model.trim="searchInfo.date"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="mini"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </span>
          </div>
        </li>
        <li style="display: flex; flex-direction: column">
          <div class="specimen">
            <label class="specimen-label">标本：</label>
            <el-checkbox-group
              style="max-height: 100px; overflow: auto"
              v-model="searchInfo.sampCode"
              @change="searchClick"
            >
              <el-checkbox
                class="checkbox"
                :label="item.sampCode"
                v-for="item in codeSample"
                :key="item.sampCode"
                >{{ item.sampName }}</el-checkbox
              >
            </el-checkbox-group>
          </div>
          <div class="screen">
            <label class="screen-label">筛选：</label>
            <el-radio-group
              v-model.trim="searchInfo.status"
              @change="searchClick"
            >
              <el-radio :label="0">全部</el-radio>
              <el-radio :label="1">{{
                systemParams.testTubeMode === '系统生成' ? '未打印' : ' 未配管'
              }}</el-radio>
              <el-radio :label="2">{{
                systemParams.testTubeMode === '系统生成' ? '已打印' : ' 已配管'
              }}</el-radio>
              <el-radio :label="3">已采集</el-radio>
              <el-radio :label="4">已运送</el-radio>
            </el-radio-group>
          </div>
        </li>
      </ul>
    </div>
    <div class="inspection-list">
      <div class="list-title">
        <h3>待处理检验列表：</h3>
        <div>
          <div
            class="title-btnBox"
            v-show="systemParams.testTubeMode === '系统生成'"
          >
            <el-checkbox v-model="autoPrints">自动打印</el-checkbox>
            <el-checkbox v-model="collectFlag" style="margin-right: 5px"
              >采集</el-checkbox
            >
            <el-select
              size="mini"
              v-model="printSetInfo.barcodeName"
              placeholder="请选择打印机"
            >
              <el-option
                v-for="item in G_printerList"
                :key="item"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
            <BtnCommon
              :btnList="['打印']"
              style="margin-left: 18px"
              @prints="print"
            ></BtnCommon>
          </div>
          <!-- <div class="title-btnBox" v-show="systemParams.testTubeMode === '预制条码'">
            <el-checkbox v-model="autoPiping">自动配管</el-checkbox>
            <el-checkbox v-model="pipingPrint">配管即打印</el-checkbox>
            <BtnCommon
              :btnList="['配管']"
              class="title-btn"
              @pipingClick="pipingClick"
            ></BtnCommon>
            <BtnCommon :btnList="['打印']"></BtnCommon>
          </div> -->
        </div>
      </div>
      <div class="list-table">
        <PublicTable
          ref="table_ref"
          isCheck
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          :columnWidth="columnWidth"
          :columnMinWidth="{ combName: 300 }"
          @selectionChange="selectionChange"
          v-model="selectionData"
          :selectable="selectable"
          onlyId="sampleNo"
        >
          <template #sex="{ scope }">
            <span>{{ G_EnumList['Sex'][scope.row.sex] }}</span>
          </template>
          <template #status="{ scope }">
            <span :class="statusColor(scope.row.status)">{{
              textFormat(scope.row.status)
            }}</span>
          </template>
        </PublicTable>
      </div>
    </div>
    <!-- 配管弹窗 -->
    <el-dialog
      :visible.sync="pipingShow"
      width="872px"
      top="4%"
      custom-class="pipingShow"
      :close-on-click-modal="false"
      @close="cancel"
    >
      <div slot="title" class="dialog-title">体检配管</div>
      <div class="dialog-info">
        <div class="info-item">
          <label>
            姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名：
          </label>
          <span>{{ pipingInfo.name }}</span>
        </div>
        <div class="info-item">
          <label>体&nbsp;&nbsp;&nbsp;检&nbsp;&nbsp;&nbsp;号：</label>
          <span>{{ pipingInfo.regNo }}</span>
        </div>
        <div class="info-item" style="display: flex">
          <div style="margin-right: 100px">
            <label>试&nbsp;管&nbsp;容&nbsp;器：</label>
            <span>{{ pipingInfo.note }}</span>
          </div>
          <div class="padding-color">
            <label>试管颜色：</label>
            <span
              class="color-box"
              :style="`background-color: ${pipingInfo.color}`"
            ></span>
          </div>
        </div>
        <div class="info-item">
          <label>注&nbsp;意&nbsp;事&nbsp;项：</label>
          <span>{{ pipingInfo.attention }}</span>
        </div>
        <div class="info-item btn-box">
          <div>
            <label>条码起始号：</label>
            <span>{{ pipingInfo.preNum }}</span>
          </div>
          <div>
            <el-button size="mini" class="blue_btn btn" @click="nextClick"
              >跳过</el-button
            >
            <el-button
              size="mini"
              class="blue_btn btn"
              @click="sampleBindBarCode"
              >确定</el-button
            >
          </div>
        </div>
        <el-input
          :maxlength="10"
          v-model.trim="pipingInfo.sampleNo"
          placeholder="请输入10位数的试管号"
          clearable
          class="input"
          @keyup.enter.native="sampleBindBarCode"
          v-focus
          ref="input_ref"
        ></el-input>
      </div>
      <div class="tabs-box">
        <div class="tabs">
          <div
            class="tab-pane"
            :class="active === 1 ? 'active' : ''"
            @click="tabsClick(1)"
          >
            项目
          </div>
          <div
            class="tab-pane"
            :class="active === 2 ? 'active' : ''"
            @click="tabsClick(2)"
          >
            试管类别列表
          </div>
        </div>
        <div v-show="active === 1" class="tab-table">
          <PublicTable
            isCheck
            :viewTableList.sync="pipingInfo.combs"
            :theads.sync="combsTheads"
            ref="multipleTable"
            @selectAll="selectAll"
            @selectionChange="pipingSelectChange"
            :columnWidth="columnWidth"
          >
          </PublicTable>
        </div>
        <div v-show="active === 2" class="tabs-table">
          <PublicTable
            :isStripe="false"
            :viewTableList.sync="testTubeData"
            :theads.sync="testTubeTheads"
            :columnWidth="columnWidth"
          >
            <template #color="{ scope }">
              <div
                class="testTube-colorBox"
                :style="`background-color: ${scope.row.color}`"
              ></div>
            </template>
          </PublicTable>
        </div>
      </div>
    </el-dialog>
    <!-- 取消配管弹窗 -->
    <el-dialog
      :visible.sync="cancelPipingShow"
      width="1016px"
      top="4%"
      custom-class="cancelPipingShow"
      :close-on-click-modal="false"
      @close="cancel"
    >
      <div slot="title" class="dialog-title">取消配管</div>
      <div class="cancelPiping">
        <BtnCommon
          :btnList="['取消配管']"
          @cancelPipingClick="cancelPipingClick"
        ></BtnCommon>
      </div>
      <div class="cancelPiping-table">
        <PublicTable
          isCheck
          :isStripe="false"
          :viewTableList.sync="cancelPipingData"
          :theads.sync="cancelPipingTheads"
          @selectionChange="cancelPipingSelect"
          :columnWidth="{
            barcodeName: 100,
            color: 100,
            attention: 250
          }"
          :expandAll="true"
        >
          <template #color="{ scope }">
            <div
              class="testTube-colorBox"
              :style="`background-color: ${scope.row.color}`"
            ></div>
          </template>
          <template #columnRight>
            <el-table-column type="expand">
              <template slot-scope="scope">
                <div style="padding-left: 60px; display: flex">
                  <span>组合名称：</span>
                  <div>
                    <p
                      class="content cell_blue"
                      v-for="(item, index) in scope.row.combNameDetails"
                      :key="index"
                    >
                      {{ item }}
                    </p>
                  </div>
                </div>
              </template>
            </el-table-column>
          </template>
        </PublicTable>
      </div>
    </el-dialog>
    <div class="my_mask" v-if="maskShow">
      <el-progress
        type="circle"
        :percentage="G_message.percentage"
      ></el-progress>
      <p class="tips_p">{{ G_message.message }}</p>
    </div>
  </div>
</template>

<script>
import BtnCommon from './components/btnCommon.vue';
import PublicTable from '@/components/publicTable';
import moment from 'moment';
import { mapGetters, mapMutations } from 'vuex';
import PrintMixins from '@/components/printMixinsV2';
import { storage } from '@/common';
import Print from './mixins/print';
import { dataUtils } from '@/common';

export default {
  name: 'collectPiping',
  mixins: [PrintMixins, Print],
  components: {
    BtnCommon,
    PublicTable
  },
  data() {
    return {
      searchInfo: {
        regNo: '',
        sampleNo: '',
        name: '',
        cardNo: '',
        type: 0,
        companyCode: '',
        barcode: '',
        // startDate: "",
        // endDate: "",
        date: [new Date(), new Date()],
        sampCode: [],
        status: 0
      },
      autoPiping: false, //自动配管
      pipingPrint: false, //打印即配管
      tableData: [], //检验列表
      testTubeData: [], //试管类别列表
      columnWidth: {
        barcodeSN: 110,
        sampleNo: 130,
        regNo: 130,
        combName: 300,
        preNum: 80,
        note: 190,
        color: 100,
        gatherTime: 153,
        status: 65
      },
      theads: {
        barcodeSN: '条码分类序号',
        status: '状态',
        sampleNo: '试管号',
        regNo: '体检号',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        deptName: '科室',
        combName: '组合名称',
        gatherTime: '采集时间'
      },
      combsTheads: {
        sampleNo: '条码号',
        combCode: '代码',
        combName: '组合'
      },
      testTubeTheads: {
        preNum: '条码前缀',
        note: '试管',
        color: '颜色',
        attention: '注意事项'
      },
      cancelPipingTheads: {
        regNo: '体检号',
        sampleNo: '试管条码',
        barcodeName: '条码类型',
        // combName: "组合名称",
        note: '试管',
        color: '颜色',
        attention: '注意事项'
      },
      pipingShow: false, //配管显示
      cancelPipingShow: false, //取消配管显示
      active: 1, //激活状态
      companyList: [], //公司列表下拉
      companyListCopy: [],
      barcodeType: [], //条码分类下拉
      codeSample: [], //标本项
      pipingData: [], //配管详情总数据
      pipingInfo: {}, //记录配管详情
      pipingInfoIndex: 0, //配管详情index
      checkCombs: [], //组合全勾选数据
      cancelPipingData: [], //取消配管数据
      selectionData: [], //检验列表勾选的数据
      cancelPipingSelectData: [], //取消配管勾选的数据
      systemParams: {},
      autoPrints: false,
      collectFlag: false,
      currentNum: 0,
      maskShow: false,
      // printList:[],
      printSetInfo: {
        barcodeName: ''
      }
    };
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_userInfo',
      'G_printerList',
      'G_datePickerShortcuts'
    ]),
    G_message() {
      let percentage =
        (this.currentNum / this.selectionData.length).toFixed(2) * 100;
      let message = '正在打印中';
      if (percentage == 100) {
        message = '打印完成';
      }
      return {
        percentage,
        message
      };
    }
  },
  created() {
    this.getCompany();
    this.getBarcodeType();
    this.getCodeSample();
    this.searchClick();
    // this.getSystemParameters();

    this.systemParams = this.G_userInfo.systemParams;

    this.connectPrint((r) => {
      const dataInfo = JSON.parse(r.data);
      console.log(dataInfo);

      this.M_printerList(dataInfo.Data);
      const print = storage.local.get('print');

      this.printSetInfo.PrinterName = print?.printer || dataInfo.Data[0];
      this.printSetInfo.barcodeName =
        print?.barcodePrinters || dataInfo.Data[0];
      // let dataInfo = JSON.parse(r.data);
      // console.log(dataInfo);
      // let printDefault = storage.local.get('printDefault')
      // this.printList = dataInfo.Data;
      // this.printSetInfo.PrinterName = printDefault? (printDefault.general?printDefault.general:dataInfo.Data[0]) : dataInfo.Data[0];
      // this.printSetInfo.barcodeName = printDefault? (printDefault.barcodeName?printDefault.barcodeName:dataInfo.Data[0]) : dataInfo.Data[0];
    });
  },
  methods: {
    ...mapMutations(['M_printerList']),
    // 获取系统参数
    // getSystemParameters() {
    //   this.$ajax
    //     .post(this.$apiUrls.ReadSystemParameters, "", {
    //       query: { paramCode: 4 }
    //     })
    //     .then(r => {
    //       console.log("ReadSystemParameters: ", r);
    //       let { success, returnData } = r.data;
    //       if (!success) return;
    //       this.systemParams = returnData;
    //       // 1:"系统生成" 2:"预制条码"
    //     });
    // },
    // 标签页点击
    tabsClick(val) {
      this.active = val;
      if (this.active === 2) {
        this.getTestTubeCategoryList();
      }
    },
    //获取单位下拉
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) return;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.searchInfo.date = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.searchClick();
    },
    // 获取条码分类下拉
    getBarcodeType() {
      this.$ajax.post(this.$apiUrls.BarcodeType).then((r) => {
        console.log('BarcodeType: ', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.barcodeType = returnData || [];
      });
    },
    // 获取标本项
    getCodeSample() {
      this.$ajax.post(`${this.$apiUrls.RD_CodeSample}/Read `, []).then((r) => {
        console.log('RD_CodeSample: ', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.codeSample = returnData || [];
      });
    },
    // 获取试管类别列表
    getTestTubeCategoryList() {
      this.$ajax.post(this.$apiUrls.TestTubeCategoryList).then((r) => {
        console.log('TestTubeCategoryList: ', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.testTubeData = returnData || [];
      });
    },
    // 获取配管详情
    getSampleDetail() {
      let data = [];
      if (this.searchInfo.regNo !== '') {
        let obj = {
          regNo: this.searchInfo.regNo,
          barcodeTypes: []
        };
        data.push(obj);
      }
      if (this.selectionData.length !== 0) {
        let oldData = [];
        this.selectionData.forEach((el) => {
          let oldObj = {
            regNo: el.regNo,
            barcodeTypes: []
          };
          oldObj.barcodeTypes.push(el.barCodeType);
          oldData.push(oldObj);
        });
        let newData = [];
        let newObj = {};
        oldData.forEach((el, i) => {
          if (!newObj[el.regNo]) {
            newData.push(el);
            newObj[el.regNo] = true;
          } else {
            newData.forEach((el) => {
              if (el.regNo === oldData[i].regNo) {
                el.barcodeTypes = [
                  ...el.barcodeTypes,
                  ...oldData[i].barcodeTypes
                ];
              }
            });
          }
        });
        data = newData;
      }
      console.log('data: ', data);
      this.$ajax.post(this.$apiUrls.ReadSampleDetail, data).then((r) => {
        console.log('ReadSampleDetail: ', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.pipingData = returnData || [];
        if (this.pipingData.length === 0) return;
        this.pipingInfo = this.pipingData[0];
        this.$nextTick(() => {
          this.$refs.multipleTable.$refs.tableCom_Ref.toggleAllSelection();
        });
      });
    },
    // 获取条码详情
    getSampleBarcodeDetails() {
      let data = this.selectionData
        .filter((item) => {
          return item.status === 2;
        })
        .map((item) => {
          return item.sampleNo;
        });
      console.log('data: ', data);
      this.$ajax
        .post(this.$apiUrls.ReadSampleBarcodeDetails, data)
        .then((r) => {
          console.log('ReadSampleBarcodeDetails: ', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.cancelPipingData = returnData.map((item) => {
            let combNameDetails = item.barcodeDetails.map((item) => {
              return item.combName;
            });
            return {
              barcodeName: item.barcodeName,
              regNo: item.regNo,
              sampleNo: item.sampleNo,
              attention: item.barcodeDetails[0].attention,
              color: item.barcodeDetails[0].color,
              note: item.barcodeDetails[0].note,
              combNameDetails: combNameDetails
            };
          });
        });
    },
    // 搜索
    searchClick() {
      return new Promise((resolve, reject) => {
        let startDate = null;
        let endDate = null;
        if (this.searchInfo.date != null) {
          startDate = moment(this.searchInfo.date[0]).format('YYYY-MM-DD');
          endDate = moment(this.searchInfo.date[1]).format('YYYY-MM-DD');
        }
        let data = {
          regNo: this.searchInfo.regNo,
          sampleNo: this.searchInfo.sampleNo,
          name: this.searchInfo.name,
          cardNo: this.searchInfo.cardNo,
          type: this.searchInfo.type,
          companyCode: this.searchInfo.companyCode?.[0] || '',
          barcode: this.searchInfo.barcode,
          startDate: startDate,
          endDate: endDate,
          sampCode: this.searchInfo.sampCode,
          status: this.searchInfo.status
        };
        console.log('data: ', data);
        this.$ajax.post(this.$apiUrls.ReadSampleData, data).then((r) => {
          console.log('ReadSampleData: ', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.tableData = returnData || [];
          resolve();
        });
      });
    },
    // 自动打印
    autoPrint() {
      this.$refs.table_ref.$refs.tableCom_Ref.toggleAllSelection();
      let selectionData = this.tableData.filter((item) => {
        return item.status < 4;
      });
      this.selectionData = selectionData;
      this.print();
    },
    // 配管按钮
    pipingClick() {
      if (this.selectionData.length === 0) {
        this.$message({
          message: '请选择需要配管的项目!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let status = this.selectionData.map((item) => {
        return item.status;
      });
      if (status.includes(1) && status.includes(2)) {
        this.$message({
          message: '选择的项目有误!',
          type: 'warning',
          showClose: true
        });
        this.$nextTick(() => {
          this.$refs.table_ref.$refs.tableCom_Ref.clearSelection();
        });
        return;
      } else if (status.includes(1)) {
        this.pipingShow = true;
        this.getSampleDetail();
      } else if (status.includes(2)) {
        this.cancelPipingShow = true;
        this.getSampleBarcodeDetails();
      } else {
        return false;
      }
    },
    // 检验列表勾选
    selectionChange(rows, checkList) {
      console.log('row: ', checkList);
      this.selectionData = checkList;
    },
    // 检验列表是否可以勾选
    selectable(row) {
      if (row.status === 4) {
        return false;
      } else {
        return true;
      }
    },
    // 绑定试管条码、确定按钮
    sampleBindBarCode() {
      let data = {
        regNo: this.pipingInfo.regNo,
        preNum: this.pipingInfo.preNum,
        sampleNo: this.pipingInfo.sampleNo,
        combs: this.checkCombs,
        gatherOperator: this.G_userInfo.codeOper.operatorCode
      };
      console.log('data: ', data);
      this.$ajax.post(this.$apiUrls.PrefabricatedBarcode, data).then((r) => {
        console.log('PrefabricatedBarcode: ', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.$message({
          message: '配管成功!',
          type: 'success',
          showClose: true
        });
        this.nextClick();
      });
    },
    // 跳过按钮
    nextClick() {
      if (this.pipingData.length === 0) return;
      this.pipingInfoIndex += 1;
      if (this.pipingInfoIndex === this.pipingData.length) {
        this.pipingShow = false;
        this.searchClick();
        this.pipingInfoIndex = 0;
        return;
      }
      this.$nextTick(() => {
        this.pipingInfo = this.pipingData[this.pipingInfoIndex];
        this.$refs.input_ref.focus();
        this.$refs.multipleTable.$refs.tableCom_Ref.toggleAllSelection();
      });
    },
    // 项目列表全选
    selectAll(rows) {
      this.checkCombs = rows.map((item) => {
        return item.combCode;
      });
      console.log('this.checkCombs: ', this.checkCombs);
    },
    // 项目列表勾选
    pipingSelectChange(rows) {
      this.checkCombs = rows.map((item) => {
        return item.combCode;
      });
      console.log('this.checkCombs: ', this.checkCombs);
    },
    // 取消配管勾选
    cancelPipingSelect(rows) {
      this.cancelPipingSelectData = rows;
    },
    // 取消配管
    cancelPipingClick() {
      if (this.cancelPipingSelectData.length === 0) {
        this.$message({
          message: '请选择需要取消配管的项目!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm(`是否确定取消配管?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let data = this.cancelPipingSelectData.map((item) => {
          return item.sampleNo;
        });
        this.$ajax.post(this.$apiUrls.CancelSampleBarCode, data).then((r) => {
          console.log('CancelSampleBarCode: ', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.$message({
            message: '取消配管成功!',
            type: 'success',
            showClose: true
          });
          this.cancelPipingShow = false;
          this.searchClick();
        });
      });
    },
    // 体检号回车
    // regNoEnter() {
    //   if (this.systemParams.testTubeMode === "系统生成") {
    //     if (this.searchInfo.regNo && this.autoPrints) {
    //       let tableData = this.tableData.filter(i => i.status === 1);
    //       console.log("tableData: ", tableData);
    //       tableData.map(item => {
    //         let data = {
    //           regNo: item.regNo,
    //           combs: item.combs,
    //           gatherOperator: this.G_userInfo.codeOper.operatorCode
    //         };
    //         console.log("data: ", data);
    //         this.$ajax
    //           .post(this.$apiUrls.GeneratBarcodeBySystem, data)
    //           .then(r => {
    //             console.log("GeneratBarcodeBySystem: ", r);
    //             let { success, returnData } = r.data;
    //             if (!success) return;
    //             console.log("returnData: ", returnData);
    //             this.$message({
    //               message: "配管成功!",
    //               type: "success",
    //               showClose: true
    //             });
    //             console.log("打印");
    //             this.searchClick();
    //           });
    //       });
    //     } else {
    //       this.searchClick();
    //     }
    //   } else {
    //     if (this.searchInfo.regNo && this.autoPiping) {
    //       this.pipingShow = true;
    //       this.getSampleDetail();
    //     } else {
    //       this.searchClick();
    //     }
    //   }
    // },
    regNoEnter() {
      if (this.systemParams.testTubeMode === '系统生成') {
        if (this.searchInfo.regNo && this.autoPrints) {
          this.searchClick().then((r) => {
            this.autoPrint();
          });
        } else {
          this.searchClick();
        }
      } else {
        if (this.searchInfo.regNo && this.autoPiping) {
          this.pipingShow = true;
          this.getSampleDetail();
        } else {
          this.searchClick();
        }
      }
    },
    // // 系统打印（匹配）条码
    // async systemPrints() {
    //   if (this.selectionData.length === 0) {
    //     this.$message({
    //       message: "请选择需要打印条码的项目!",
    //       type: "warning",
    //       showClose: true
    //     });
    //     return;
    //   }
    //   await this.selectionData.map(item => {
    //     if (item.status === 1) {
    //       let data = {
    //         regNo: item.regNo,
    //         combs: item.combs,
    //         gatherOperator: this.G_userInfo.codeOper.operatorCode
    //       };
    //       console.log("data: ", data);
    //       this.$ajax
    //         .post(this.$apiUrls.GeneratBarcodeBySystem, data)
    //         .then(r => {
    //           console.log("GeneratBarcodeBySystem: ", r);
    //           let { success, returnData } = r.data;
    //           if (!success) return;
    //           console.log("returnData: ", returnData);
    //           this.$message({
    //             message: "配管成功!",
    //             type: "success",
    //             showClose: true
    //           });
    //           console.log("打印");
    //           this.searchClick();
    //         });
    //     }
    //   });
    // },
    // 系统打印（匹配）条码
    async systemPrints() {
      if (this.selectionData.length === 0) {
        this.$message({
          message: '请选择需要打印条码的项目!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      await this.selectionData.map((item) => {
        if (item.status === 1) {
          let data = {
            regNo: item.regNo,
            combs: item.combs,
            gatherOperator: this.G_userInfo.codeOper.operatorCode
          };
          console.log('data: ', data);
          this.$ajax
            .post(this.$apiUrls.GeneratBarcodeBySystem, data)
            .then((r) => {
              console.log('GeneratBarcodeBySystem: ', r);
              let { success, returnData } = r.data;
              if (!success) return;
              console.log('returnData: ', returnData);
              this.$message({
                message: '配管成功!',
                type: 'success',
                showClose: true
              });
              console.log('打印');
              this.searchClick();
            });
        }
      });
    },
    // 关闭
    cancel() {
      this.searchClick();
      this.pipingInfoIndex = 0;
    },
    // 状态格式化
    textFormat(val) {
      let text = '';
      if (this.systemParams.testTubeMode === '系统生成') {
        switch (val) {
          case 1:
            text = '未打印';
            break;
          case 2:
            text = '已打印';
            break;
          case 3:
            text = '已采集';
            break;
          case 4:
            text = '已运送';
            break;
        }
        return text;
      } else {
        switch (val) {
          case 1:
            text = '未配管';
            break;
          case 2:
            text = '已配管';
            break;
          case 3:
            text = '已运送';
            break;
        }
        return text;
      }
    },
    // 状态颜色处理
    statusColor(val) {
      let color = '';
      switch (val) {
        case 1:
          color = 'cell_red';
          break;
        case 2:
          color = 'cell_blue';
          break;
        case 3:
          color = 'cell_green';
          break;
        case 4:
          color = '';
          break;
      }
      return color;
    }
  }
};
</script>

<style lang="less" scoped>
.collectPiping {
  color: #2d3436;
  display: flex;
  flex-direction: column;
  h3 {
    font-size: 18px;
  }
  .header-wrap {
    background: #fff;
    padding: 5px;
    border-radius: 4px;
    margin-bottom: 5px;
  }
  .search-wrap {
    li {
      display: flex;
      margin-bottom: 5px;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  .search-item {
    display: flex;
    align-items: center;
    margin-right: 5px;
    width: 20%;
    span {
      flex: 1;
    }
  }
  .search-label {
    width: 60px;
    text-align: right;
    font-size: 14px;
    font-weight: 600;
    margin-right: 10px;
  }
  .idCard {
    display: flex;
    justify-content: space-between;
    width: 100%;
  }
  .input {
    width: 100%;
  }
  .specimen,
  .screen {
    display: flex;
    align-items: center;
    background: rgba(23, 112, 223, 0.1);
    border-radius: 2px;
    padding: 0 0 0px 5px;
    // width: 46%;
    flex: 1;
    flex-shrink: 0;
  }
  .specimen-label {
    width: 62px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 6px;
  }
  .checkbox {
    margin-bottom: 6px;
    margin-right: 15px;
  }
  .screen {
    // width: 40.6%;
    // padding: 6px 28px;
    margin-top: 5px;
  }
  .screen-label {
    width: 62px;
    font-size: 14px;
    font-weight: 600;
  }
  .inspection-list {
    padding: 5px;
    background: #fff;
    border-radius: 4px;
    flex: 1;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    overflow: auto;
  }
  .list-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
  }
  .title-btnBox {
    display: flex;
    align-items: center;
  }
  .title-btn {
    margin: 0 18px;
  }
  .list-table {
    flex: 1;
    overflow: auto;
    border: 1px solid #d8dee1;
    border-radius: 4px;
  }
  .dialog-title {
    background: #d1e2f9;
    font-size: 18px;
    padding: 18px;
    font-weight: 600;
  }
  .info-item {
    margin-bottom: 18px;
    label {
      width: 74px;
    }
    span {
      color: #1770df;
      font-weight: 600;
    }
  }
  .color-box {
    display: inline-block;
    width: 78px;
    height: 20px;
    border-radius: 2px;
  }
  .padding-color {
    display: flex;
  }
  .btn {
    padding: 7px 28px;
  }
  .btn-box {
    display: flex;
    justify-content: space-between;
  }
  /deep/.el-dialog__header {
    padding: 0;
  }
  .tabs-box {
    height: calc(100% - 239px);
    overflow: auto;
  }
  .tabs {
    display: flex;
    margin-bottom: 10px;
  }
  .tab-pane {
    width: 120px;
    text-align: center;
    padding-top: 18px;
    padding-bottom: 18px;
    margin-right: 20px;
    font-size: 16px;
    cursor: pointer;
    &.active {
      color: #1770df;
      border-bottom: 2px solid #1770df;
    }
  }
  .tab-table {
    height: calc(100% - 69px);
    border: 1px solid #d8dee1;
    border-radius: 4px;
    overflow: auto;
  }
  .tabs-table {
    height: calc(100% - 69px);
    border: 1px solid #d8dee1;
    border-radius: 4px;
    overflow: auto;
  }
  .cancelPiping {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 18px;
  }
  .cancelPiping-table {
    height: calc(100% - 50px);
    border: 1px solid #d8dee1;
    border-radius: 4px;
    overflow: auto;
  }
  .testTube-colorBox {
    // background: #1770df;
    width: 72px;
    height: 20px;
    border-radius: 2px;
  }
}
</style>
<style lang="less">
.collectPiping {
  .pipingShow {
    height: 832px;
    .el-dialog__body {
      padding: 18px;
      color: #2d3436;
      height: calc(100% - 60px);
    }
  }
  .cancelPipingShow {
    height: 832px;
    .el-dialog__body {
      padding: 18px;
      color: #2d3436;
      height: calc(100% - 60px);
    }
  }
}
</style>
