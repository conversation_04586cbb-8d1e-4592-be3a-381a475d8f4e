<template>
  <!-- 症状询问组件 -->
  <div class="smoking-symptom-container" v-loading="loadingMask">
    <div class="form-box">
      <div class="contDiv">
        <el-input
          style="width: 60%; margin-bottom: 5px"
          size="mini"
          placeholder="请输入关键字搜索"
          @input="filter"
          v-model.trim="keyword"
          clearable
        ></el-input>
        <PublicTable
          ref="publicTable_Ref1"
          :theads="tableTheads"
          :viewTableList.sync="tableData"
          remoteByPage
          :columnWidth="columnWidth"
          style="height: calc(100% - 30px)"
          @rowDblclick="leftRowDblclick"
          :tableRowClassName="tableRowClassName"
          :isStripe="false"
        >
          <!-- <template #check="{ scope }">
                <el-checkbox v-model="scope.row.check"></el-checkbox>
              </template> -->
        </PublicTable>
      </div>
      <div class="centerBody">
        <!-- <el-button
                size="mini"
                class="blue_btn add-del"
                style="margin-bottom: 40px"
                @click="addComb"
                >添加
                <i class="iconfont icon-Rightxiangyou34"></i>
                </el-button>
                <el-button
                size="mini"
                class="red_btn add-del"
                @click="delComb"
                icon="iconfont icon-Leftxiangzuo35"
                >
                删除</el-button
                > -->
      </div>
      <div class="contDiv">
        <p style="margin: 5.5px 0">已选列表：</p>
        <PublicTable
          style="height: calc(100% - 30px)"
          ref="publicTable_Ref1"
          :theads="tableTheads2"
          :viewTableList.sync="symptomResult.symptomResults"
          remoteByPage
          :params="params"
          :columnWidth="columnWidth"
          @rowDblclick="rightRowDblclick"
        >
          <!-- <template #check="{ scope }">
                    <el-checkbox v-model="scope.row.check"></el-checkbox>
                  </template> -->
          <template #symptomResult="{ scope }">
            <el-input
              v-model="scope.row.symptomResult"
              placeholder="请输入症状结果"
              v-if="scope.row.symptomCode == '10086'"
            ></el-input>
          </template>
        </PublicTable>
      </div>
    </div>
    <!-- 检查日期、医生 -->
    <el-form
      :model="symptomResult"
      ref="form_ref"
      :show-message="false"
      :inline="true"
      style="width: 100%"
    >
      <el-row
        align="center"
        :gutter="20"
        justify="space-between"
        type="flex"
        style="margin-bottom: 10px"
      >
        <el-col :span="7" style="display: flex; align-items: center">
          <label>检查日期：</label>
          <el-form-item style="margin: 0" prop="examTime" required>
            <el-date-picker
              v-model="symptomResult.examTime"
              type="date"
              placeholder="请选择日期"
              size="mini"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              style="width: 10rem"
              :picker-options="pickerOptions"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="7" style="display: flex; align-items: center">
          <label>检查医生：</label>
          <el-form-item style="margin: 0" prop="doctorName" required>
            <el-select
              v-model="symptomResult.doctorName"
              placeholder="请选择"
              size="mini"
              style="width: 10rem"
              filterable
            >
              <el-option
                v-for="item in doctorList"
                :key="item.operatorCode"
                :label="item.operatorName"
                :value="item.operatorName"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="10"> </el-col>
      </el-row>
    </el-form>
    <el-row
      align="center"
      :gutter="10"
      justify="space-between"
      type="flex"
      class="footer-box"
    >
      <el-col :span="24" class="btn-box">
        <el-button type="default" size="mini" @click="cancel" :loading="loading"
          >关闭</el-button
        >
        <el-button
          type="primary"
          size="mini"
          @click="saveResult"
          :loading="loading"
          >保存</el-button
        >
      </el-col>
    </el-row>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import PublicTable from '@/components/publicTable.vue';

import { dataUtils } from '@/common';

export default {
  name: 'symptomResult',
  props: {
    regNo: {
      type: String,
      required: true
    }
  },
  components: {
    PublicTable
  },
  data() {
    return {
      loadingMask: false, // 内容loading
      loading: false, // 按钮loading
      doctorList: [], // 医生列表
      tableData: [], // 表格数据
      tableDataBak: [], // 表格数据备份
      symptomResult: {
        examTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
        doctorName: '',
        symptomResults: [],
        regNo: ''
      }, // 表格数据
      keyword: '',
      params: {},
      tableTheads: {
        // check:"选中",
        symptomCode: '症状代码',
        symptomName: '症状名称'
      },
      tableTheads2: {
        // check:"选中",
        symptomCode: '症状代码',
        symptomName: '症状名称',
        symptomResult: '症状结果'
      },
      columnWidth: {
        check: 50
      },
      pickerOptions: {
        // 日期范围，不能超过今天
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      }
    };
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_userInfo', 'G_sysOperator'])
  },
  methods: {
    leftRowDblclick(row) {
      let idx = this.symptomResult.symptomResults.findIndex(
        (x) => x.symptomCode == row.symptomCode
      );
      if (idx != -1) {
        this.symptomResult.symptomResults.splice(idx, 1);
      } else {
        this.symptomResult.symptomResults.push(row);
      }
    },
    rightRowDblclick(row) {
      let idx = this.symptomResult.symptomResults.findIndex(
        (x) => x.symptomCode == row.symptomCode
      );
      if (idx != -1) this.symptomResult.symptomResults.splice(idx, 1);
    },
    // 表格行的样式回调
    tableRowClassName({ row }) {
      const checkListRegNos = new Set(
        this.symptomResult.symptomResults.map((item) => item.symptomCode)
      );
      if (checkListRegNos.has(row.symptomCode)) {
        return 'search_class';
      }
    },
    async getSymptomResult() {
      if (!this.regNo) return;

      this.loadingMask = true;
      await this.$ajax
        .post(this.$apiUrls.ReadCodeOccupationalSymptom, [])
        .then((res) => {
          const { success, returnData } = res.data;
          if (!success) return;
          this.tableData = returnData;
          this.tableDataBak = dataUtils.deepCopy(this.tableData);
        });
      await this.$ajax
        .paramsPost(this.$apiUrls.GetSymptomResults, { regNo: this.regNo })
        .then((res) => {
          const { success, returnData } = res.data;
          if (!success) return;
          this.symptomResult = returnData;
          this.symptomResult.symptomResults = returnData.symptomResults;
          (this.symptomResult.examTime = new Date()
            .toISOString()
            .slice(0, 19)
            .replace('T', ' ')),
            this.setDoctorName();
        })
        .finally((_) => {
          this.loadingMask = false;
        });
    },
    getDoctorList() {
      const that = this;
      that.$ajax
        .paramsPost(that.$apiUrls.GetOperatorListByRoleCode, {
          roleCode: that.G_EnumList['ConstRoleType'].doctor
        })
        .then((res) => {
          let { success, returnData } = res.data;
          if (!success) return;

          that.doctorList = returnData;
          that.setDoctorName();
        })
        .finally((_) => {});
    },
    setDoctorName() {
      const that = this;
      const item = that.doctorList.find(
        (item) => item.operatorCode == that.G_userInfo.codeOper?.operatorCode
      );

      if (item) that.symptomResult.doctorName = item.operatorName;
      else that.symptomResult.doctorName = that.doctorList[0].operatorName;
    },
    saveResult() {
      let valid = true;
      this.$refs.form_ref.validate((_valid) => {
        if (!_valid) valid = false;
      });
      if (!valid) {
        this.$message.warning('请填写完整数据！');
        return null;
      }
      this.$ajax
        .post(this.$apiUrls.SaveSymptomResults, this.symptomResult)
        .then((res) => {
          const { success, message } = res.data;
          if (!success) {
            this.$message.error(message);
            return;
          }
          this.$message.success('保存成功');
          this.cancel();
        });
    },
    cancel() {
      this.$emit('cancel');
    },
    filter() {
      console.log(this.tableDataBak);
      if (this.keyword)
        this.tableData = this.tableDataBak.filter((x) =>
          x.symptomName.includes(this.keyword)
        );
      else this.tableData = dataUtils.deepCopy(this.tableDataBak);
    }
  },
  watch: {
    regNo: {
      handler(newVal, oldVal) {
        this.getSymptomResult();
        this.getDoctorList();
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
.smoking-symptom-container {
  .form-box {
    display: flex;
    flex-wrap: wrap;
  }
  .footer-box {
    .btn-box {
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }
  .contDiv {
    // padding: 0 18px 18px 18px;
    flex: 1;
    height: 500px;
    width: 40%;
    border-radius: 4px;
  }
  .centerBody {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    .el-button + .el-button {
      margin-left: 0;
    }
  }
}
/deep/.search_class {
  td {
    background: #6cbcf5c4 !important;
  }
}
</style>
