<template>
  <div
    class="subject"
    style="width: 100%; height: 100%"
    v-loading.fullscreen.lock="loading"
  >
    <el-form ref="form" :model="form" class="subject-form" :rules="rules">
      <div class="subject-item">
        <h3>烟酒史：</h3>
        <div class="smoke">
          <div class="smoke-head">
            <span
              ><span class="required" style="color: #f56c6c">*</span
              >吸烟情况：</span
            >
            <el-form-item
              prop="generalConsultation.smokingHistory.smokingStatus"
            >
              <el-radio-group
                @change="handleSmokingChange"
                v-model="form.generalConsultation.smokingHistory.smokingStatus"
              >
                <el-radio :label="1">现在每天吸</el-radio>
                <el-radio :label="2">现在吸但不是每天吸</el-radio>
                <el-radio :label="3">过去吸现在不吸</el-radio>
                <el-radio :label="4">从不吸</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div
            style="display: flex; margin-left: 20px"
            v-if="form.generalConsultation.smokingHistory.smokingStatus !== 4"
          >
            <el-form-item
              prop="generalConsultation.smokingHistory.smokingCountPerDays"
              :rules="[
                {
                  required: true,
                  message: '请输入每日吸烟次数',
                  trigger: 'blur'
                }
              ]"
            >
              <label
                ><span class="required" style="color: #f56c6c">*</span
                >平均每日</label
              >
              <el-input-number
                v-model="
                  form.generalConsultation.smokingHistory.smokingCountPerDays
                "
                :controls="false"
                :step="1"
                :min="0"
                :precision="0"
                size="mini"
                placeholder="几"
              ></el-input-number>
              <label>支</label>
            </el-form-item>

            <el-form-item
              prop="generalConsultation.smokingHistory.smokingYears"
              :rules="[
                { required: true, message: '请输入吸烟年限', trigger: 'blur' }
              ]"
            >
              <label>，约</label>
              <el-input-number
                v-model="form.generalConsultation.smokingHistory.smokingYears"
                :controls="false"
                :step="1"
                :min="0"
                :max="150"
                :precision="0"
                size="mini"
                placeholder="几"
              ></el-input-number>
              <label>年</label>
            </el-form-item>

            <el-form-item
              prop="generalConsultation.smokingHistory.smokingMonths"
              :rules="[
                { required: true, message: '请输入吸烟月数', trigger: 'blur' }
              ]"
            >
              <el-input-number
                v-model="form.generalConsultation.smokingHistory.smokingMonths"
                :controls="false"
                :step="1"
                :min="0"
                :max="1500"
                :precision="0"
                size="mini"
                placeholder="几"
              ></el-input-number>
              <label>月</label>
            </el-form-item>
          </div>
        </div>
        <div class="smoke">
          <div class="smoke-head">
            <span>饮酒情况：</span>
            <el-form-item
              prop="generalConsultation.drinkingHistory.drinkingStatus"
            >
              <el-radio-group
                @change="handleDrinkingChange"
                v-model="
                  form.generalConsultation.drinkingHistory.drinkingStatus
                "
              >
                <el-radio :label="0">不饮酒</el-radio>
                <el-radio :label="1">偶尔饮</el-radio>
                <el-radio :label="2">经常饮</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div
            style="display: flex; margin-left: 20px"
            v-if="form.generalConsultation.drinkingHistory.drinkingStatus !== 0"
          >
            <el-form-item
              prop="generalConsultation.drinkingHistory.drinkingMillilitersPerDays"
            >
              <!-- :rules="[{ required: true, message: '请输入每日饮酒量', trigger: 'blur' }]" -->
              <label>平均每日</label>
              <el-input-number
                v-model="
                  form.generalConsultation.drinkingHistory
                    .drinkingMillilitersPerDays
                "
                :controls="false"
                :step="1"
                :min="0"
                :precision="0"
                size="mini"
                placeholder="几"
              ></el-input-number>
              <label>毫升</label>
            </el-form-item>

            <el-form-item
              prop="generalConsultation.drinkingHistory.drinkingHistoryYear"
            >
              <!-- :rules="[{ required: true, message: '请输入饮酒年限', trigger: 'blur' }]" -->
              <label>，约</label>
              <el-input-number
                v-model="
                  form.generalConsultation.drinkingHistory.drinkingHistoryYear
                "
                :controls="false"
                :step="1"
                :min="0"
                :max="150"
                :precision="0"
                size="mini"
                placeholder="几"
              ></el-input-number>
              <label>年</label>
            </el-form-item>
          </div>
        </div>
      </div>
      <div class="subject-item" v-if="sex === 2">
        <h3>月经史：</h3>
        <div class="menstruation">
          <el-form-item prop="generalConsultation.menarcheHistory.menarcheAge">
            <label>初潮年龄</label>
            <el-input
              v-model="form.generalConsultation.menarcheHistory.menarcheAge"
              size="mini"
              placeholder="几"
            ></el-input>
            <label>岁</label>
          </el-form-item>
          <el-form-item
            prop="generalConsultation.menarcheHistory.menstrualPeriod"
            class="last"
          >
            <label>经期</label>
            <el-input
              style="width: 5rem"
              v-model="form.generalConsultation.menarcheHistory.menstrualPeriod"
              size="mini"
              placeholder="几"
            ></el-input>
            <label>天</label>
          </el-form-item>
          <el-form-item
            prop="generalConsultation.menarcheHistory.menstrualCycle"
            class="last"
          >
            <label>周期</label>
            <el-input
              style="width: 5rem"
              v-model="form.generalConsultation.menarcheHistory.menstrualCycle"
              size="mini"
              placeholder="几"
            ></el-input>
            <label>天</label>
          </el-form-item>
          <el-form-item prop="generalConsultation.menarcheHistory.menopauseAge">
            <label>停经年龄</label>
            <el-input
              v-model="form.generalConsultation.menarcheHistory.menopauseAge"
              size="mini"
              placeholder="几"
            ></el-input>
            <label>岁</label>
          </el-form-item>
          <el-form-item
            prop="generalConsultation.menarcheHistory.isMenstrualPeriod"
            class="last"
          >
            <label>是否经期</label>
            <el-select
              size="mini"
              v-model="
                form.generalConsultation.menarcheHistory.isMenstrualPeriod
              "
            >
              <el-option label="是" :value="1"></el-option>
              <el-option label="否" :value="0"></el-option>
            </el-select>
          </el-form-item>
        </div>
      </div>
      <div class="subject-item">
        <h3>生育史：</h3>
        <div class="birth">
          <el-form-item>
            <label>子女人数</label>
            <el-input
              v-model="form.generalConsultation.procreateHistory.childQuantity"
              size="mini"
              placeholder="几"
            ></el-input>
            <label>个</label>
          </el-form-item>
          <el-form-item>
            <label>流产次数</label>
            <el-input
              v-model="form.generalConsultation.procreateHistory.abortiveTimes"
              size="mini"
              placeholder="几"
            ></el-input>
            <label>次</label>
          </el-form-item>
          <el-form-item>
            <label>早产次数</label>
            <el-input
              v-model="form.generalConsultation.procreateHistory.prematureTimes"
              size="mini"
              placeholder="几"
            ></el-input>
            <label>次</label>
          </el-form-item>
          <el-form-item class="last">
            <label>死产次数</label>
            <el-input
              style="width: 5rem"
              v-model="
                form.generalConsultation.procreateHistory.stillbirthTimes
              "
              size="mini"
              placeholder="几"
            ></el-input>
            <label>次</label>
          </el-form-item>
          <el-form-item>
            <label>畸胎次数</label>
            <el-input
              v-model="form.generalConsultation.procreateHistory.teratomaTimes"
              size="mini"
              placeholder="几"
            ></el-input>
            <label>次</label>
          </el-form-item>
          <el-form-item class="last">
            <label>子女健康状况：</label>
            <el-input
              v-model="
                form.generalConsultation.procreateHistory.childrenHealthStatus
              "
              type="textarea"
              size="mini"
              placeholder="请输入..."
            ></el-input>
          </el-form-item>
        </div>
      </div>
      <div class="subject-item">
        <h3>婚姻史：</h3>
        <div class="birth">
          <el-form-item class="last">
            <label>婚姻情况：</label>
            <el-select
              clearable
              @change="handleMarryChange"
              placeholder="请选择"
              size="mini"
              v-model="form.generalConsultation.marrageHistory.marryStatus"
            >
              <el-option label="未婚" :value="1"></el-option>
              <el-option label="已婚" :value="2"></el-option>
              <el-option label="离异" :value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            class="last"
            v-show="form.generalConsultation.marrageHistory.marryStatus > 1"
          >
            <label>结婚日期：</label>
            <el-date-picker
              style="width: 10rem"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              v-model="form.generalConsultation.marrageHistory.marrageDate"
              type="date"
              placeholder="选择日期"
              size="mini"
            ></el-date-picker>
          </el-form-item>
          <el-form-item
            class="last"
            v-show="form.generalConsultation.marrageHistory.marryStatus > 1"
          >
            <label>配偶接触放射线情况：</label>
            <el-input
              v-model="form.generalConsultation.marrageHistory.spouse"
              type="textarea"
              size="mini"
              placeholder="请输入..."
            ></el-input>
          </el-form-item>
          <el-form-item
            class="last"
            v-show="form.generalConsultation.marrageHistory.marryStatus > 1"
          >
            <label>配偶职业及健康状况：</label>
            <el-input
              v-model="
                form.generalConsultation.marrageHistory.spouseHealthStatus
              "
              type="textarea"
              size="mini"
              placeholder="请输入..."
            ></el-input>
          </el-form-item>
        </div>
      </div>
      <div class="subject-item">
        <h3>个人状况及家族史：</h3>
        <div class="birth">
          <el-form-item class="last" prop="generalConsultation.familyHistory">
            <label>家族史：</label>
            <el-input
              type="textarea"
              v-model="form.generalConsultation.familyHistory"
              style="width: 15rem"
              size="mini"
              placeholder="请输入..."
            ></el-input>
          </el-form-item>
          <el-form-item class="last" prop="generalConsultation.personalHistory">
            <label>个人史：</label>
            <el-input
              type="textarea"
              v-model="form.generalConsultation.personalHistory"
              style="width: 15rem"
              size="mini"
              placeholder="请输入..."
            ></el-input>
          </el-form-item>
          <el-form-item class="last" prop="generalConsultation.otherSituations">
            <label>其他情况：</label>
            <el-input
              type="textarea"
              v-model="form.generalConsultation.otherSituations"
              style="width: 15rem"
              size="mini"
              placeholder="请输入..."
            ></el-input>
          </el-form-item>
        </div>
      </div>
      <div class="subject-item">
        <div class="subject-item-head">
          <h3 style="white-space: nowrap">
            既往病史：<span style="color: grey; font-size: 14px"
              >(注:如果没有请删掉)</span
            >
          </h3>
          <el-input
            v-model="form.defaultAnamneses"
            style="margin: 0 20px"
            size="mini"
            placeholder="请输入..."
          ></el-input>
          <el-button
            icon="el-icon-circle-plus-outline"
            type="primary"
            size="mini"
            @click="addAnamnesis"
            >增加一行</el-button
          >
        </div>
        <div
          class="birth box-border"
          v-for="(item, index) in form.anamneses"
          :key="index"
        >
          <el-form-item
            class="last"
            :prop="'anamneses.' + index + '.diseaseName'"
            :rules="[
              { required: true, message: '请输入疾病名称', trigger: 'change' }
            ]"
          >
            <label><span class="required">*</span>疾病名称：</label>
            <el-input
              style="width: 10rem"
              v-model="item.diseaseName"
              size="mini"
              placeholder="请输入..."
            ></el-input>
          </el-form-item>
          <el-form-item
            class="last"
            :prop="'anamneses.' + index + '.diagnosisDate'"
            :rules="[
              { required: true, message: '请选择日期', trigger: 'change' }
            ]"
          >
            <label><span class="required">*</span>诊断日期：</label>
            <el-date-picker
              style="width: 10rem"
              v-model="item.diagnosisDate"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="date"
              placeholder="选择日期"
              size="mini"
            ></el-date-picker>
          </el-form-item>
          <el-form-item
            class="last"
            :prop="'anamneses.' + index + '.diagnosticUnit'"
            :rules="[
              { required: true, message: '请输入诊断单位', trigger: 'change' }
            ]"
          >
            <label><span class="required">*</span>诊断单位:</label>
            <el-input
              v-model="item.diagnosticUnit"
              size="mini"
              style="width: 10rem"
              placeholder="请输入..."
            ></el-input>
          </el-form-item>
          <el-form-item
            class="last"
            :prop="'anamneses.' + index + '.treatmentProcess'"
            :rules="[
              { required: true, message: '请输入治疗经过', trigger: 'change' }
            ]"
          >
            <label><span class="required">*</span>治疗经过:</label>
            <el-input
              v-model="item.treatmentProcess"
              type="textarea"
              style="width: 15rem"
              size="mini"
              placeholder="请输入..."
            ></el-input>
          </el-form-item>
          <el-form-item
            class="last"
            :prop="'anamneses.' + index + '.conversion'"
            :rules="[
              { required: true, message: '请输入转归', trigger: 'change' }
            ]"
          >
            <label><span class="required">*</span>转归:</label>
            <el-input
              v-model="item.conversion"
              size="mini"
              placeholder="请输入..."
            ></el-input>
          </el-form-item>
          <el-button
            style="height: 30px"
            icon="el-icon-delete"
            type="primary"
            size="mini"
            @click="form.anamneses.splice(index, 1)"
          ></el-button>
        </div>
      </div>
      <div class="subject-item">
        <div class="subject-item-head">
          <h3>职业史：</h3>
          <el-button
            icon="el-icon-circle-plus-outline"
            type="primary"
            size="mini"
            @click="addOccupation"
            >增加一行</el-button
          >
        </div>
        <div
          class="birth occupation box-border"
          v-for="(item, index) in form.occupationHistory"
          :key="index"
        >
          <!-- <div class="occupation-item"> -->
          <el-form-item
            class="last"
            :prop="'occupationHistory.' + index + '.hazardFactorType'"
            :rules="[
              { required: true, message: '请选择类型', trigger: 'change' }
            ]"
          >
            <label><span class="required">*</span>类型编码：</label>
            <el-select
              @change="handleOccupationTypeChange($event, item)"
              size="mini"
              v-model="item.hazardFactorType"
              :rules="{
                required: true,
                message: '请选择类型',
                trigger: 'change'
              }"
            >
              <el-option label="放射" :value="'1'"></el-option>
              <el-option label="非放射" :value="'2'"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            class="last"
            :prop="'occupationHistory.' + index + '.dataTime'"
            :rules="{
              required: true,
              message: '请选择日期',
              trigger: 'change'
            }"
          >
            <label><span class="required">*</span>时间：</label>
            <el-date-picker
              style="width: 20rem"
              type="daterange"
              v-model="item.dataTime"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              size="mini"
            ></el-date-picker>
          </el-form-item>
          <el-form-item
            class="last"
            :prop="'occupationHistory.' + index + '.companyName'"
            :rules="{
              required: true,
              message: '请输入公司名称',
              trigger: 'change'
            }"
          >
            <label><span class="required">*</span>公司名称：</label>
            <el-input
              style="width: 12rem"
              v-model="item.companyName"
              size="mini"
              placeholder="请输入..."
            ></el-input>
          </el-form-item>
          <el-form-item
            class="last"
            :prop="'occupationHistory.' + index + '.workshop'"
            :rules="{
              required: true,
              message: '请输入车间',
              trigger: 'change'
            }"
          >
            <label><span class="required">*</span>车间：</label>
            <el-input
              v-model="item.workshop"
              size="mini"
              placeholder="请输入..."
            ></el-input>
          </el-form-item>
          <el-form-item
            class="last"
            :prop="'occupationHistory.' + index + '.job'"
            :rules="{
              required: true,
              message: '请选择工种',
              trigger: 'change'
            }"
          >
            <label><span class="required">*</span>工种：</label>
            <vxe-select
              :option-props="{ value: 'jobName', label: 'jobName' }"
              v-model="item.job"
              :options="jobList"
              filterable
              clearable
            ></vxe-select>
          </el-form-item>
          <el-form-item
            class="last"
            :prop="'occupationHistory.' + index + '.contactHazardousElements'"
            :rules="{
              required: true,
              message: '请选择危害因素',
              trigger: 'change'
            }"
          >
            <label><span class="required">*</span>危害因素：</label>
            <vxe-select
              style="width: 30rem"
              clearable
              multiple
              :option-props="{ value: 'hazardousName', label: 'hazardousName' }"
              v-model="item.contactHazardousElements"
              :options="harmList"
              filterable
            ></vxe-select>
          </el-form-item>
          <!-- </div>
                                  <div class="occupation-item"> -->
          <el-form-item
            :prop="'occupationHistory.' + index + '.workloadPerDay'"
            :rules="{
              required: true,
              message: '请输入每日工作量',
              trigger: 'change'
            }"
            class="last"
            v-if="item.hazardFactorType == 1"
          >
            <label><span class="required">*</span>每日工作数或工作量：</label>
            <el-input
              v-model="item.workloadPerDay"
              size="mini"
              placeholder="请输入..."
            ></el-input>
          </el-form-item>
          <el-form-item
            :prop="'occupationHistory.' + index + '.exposureDose'"
            :rules="{
              required: true,
              message: '请输入职业史累积受照剂量',
              trigger: 'change'
            }"
            class="last"
            v-if="item.hazardFactorType == 1"
          >
            <label><span class="required">*</span>职业史累积受照剂量：</label>
            <el-input
              v-model="item.exposureDose"
              size="mini"
              placeholder="请输入..."
            ></el-input>
          </el-form-item>
          <el-form-item
            :prop="'occupationHistory.' + index + '.overExposureStatus'"
            :rules="{
              required: true,
              message: '请输入职业史过量照射史',
              trigger: 'change'
            }"
            class="last"
            v-if="item.hazardFactorType == 1"
          >
            <label><span class="required">*</span>职业史过量照射史：</label>
            <el-input
              v-model="item.overExposureStatus"
              size="mini"
              placeholder="请输入..."
            ></el-input>
          </el-form-item>
          <el-form-item
            :prop="'occupationHistory.' + index + '.irradiationType'"
            :rules="{
              required: true,
              message: '请输入职业照射种类',
              trigger: 'change'
            }"
            class="last"
            v-if="item.hazardFactorType == 1"
          >
            <label><span class="required">*</span>职业照射种类：</label>
            <el-input
              v-model="item.irradiationType"
              size="mini"
              placeholder="请输入..."
            ></el-input>
          </el-form-item>
          <el-form-item
            :prop="'occupationHistory.' + index + '.irradiationTypeCode'"
            :rules="{
              required: true,
              message: '请输入职业照射种类代码',
              trigger: 'change'
            }"
            class="last"
            v-if="item.hazardFactorType == 1"
          >
            <label><span class="required">*</span>职业照射种类代码：</label>
            <el-input
              v-model="item.irradiationTypeCode"
              size="mini"
              placeholder="请输入..."
            ></el-input>
          </el-form-item>
          <el-form-item
            :prop="'occupationHistory.' + index + '.radiationType'"
            :rules="{
              required: true,
              message: '请输入放射线种类',
              trigger: 'change'
            }"
            class="last"
            v-if="item.hazardFactorType == 1"
          >
            <label><span class="required">*</span>放射线种类：</label>
            <el-input
              v-model="item.radiationType"
              size="mini"
              placeholder="请输入..."
            ></el-input>
          </el-form-item>
          <el-form-item
            :prop="'occupationHistory.' + index + '.protectiveMeasures'"
            :rules="{
              required: true,
              message: '请输入职业防护措施',
              trigger: 'change'
            }"
            class="last"
            v-if="item.hazardFactorType == 2"
          >
            <label><span class="required">*</span>防护措施：</label>
            <el-input
              v-model="item.protectiveMeasures"
              size="mini"
              placeholder="请输入..."
            ></el-input>
          </el-form-item>
          <!-- </div>
                                  <div class="occupation-item"> -->
          <el-form-item
            :prop="'occupationHistory.' + index + '.examTime'"
            :rules="{
              required: true,
              message: '请选择检查日期',
              trigger: 'change'
            }"
            class="last"
          >
            <label><span class="required">*</span>检查日期：</label>
            <el-date-picker
              style="width: 10rem"
              v-model="item.examTime"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="date"
              placeholder="选择日期"
              size="mini"
            ></el-date-picker>
          </el-form-item>
          <el-form-item
            :prop="'occupationHistory.' + index + '.doctorName'"
            :rules="{
              required: true,
              message: '请输入检查医生',
              trigger: 'change'
            }"
            class="last"
          >
            <label><span class="required">*</span>检查医生：</label>
            <el-input
              v-model="item.doctorName"
              size="mini"
              placeholder="请输入..."
            ></el-input>
          </el-form-item>
          <el-button
            v-show="form.occupationHistory.length > 1"
            style="height: 30px"
            icon="el-icon-delete"
            type="primary"
            size="mini"
            @click="form.occupationHistory.splice(index, 1)"
          ></el-button>
          <!-- </div> -->
        </div>
      </div>
      <div class="subject-item item-bottom">
        <div>
          <el-form-item class="last" prop="examTime">
            <label><span class="required">*</span>检查日期：</label>
            <el-date-picker
              v-model="form.examTime"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="date"
              placeholder="选择日期"
              size="mini"
            ></el-date-picker>
          </el-form-item>
          <el-form-item class="last" prop="doctorName">
            <label><span class="required">*</span>检查医生：</label>
            <el-select
              v-model="form.doctorName"
              size="mini"
              placeholder="选择医生"
            >
              <el-option
                v-for="item in doctorList"
                :key="item.operatorName"
                :label="item.operatorName"
                :value="item.operatorCode"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </div>
        <div>
          <el-button type="primary" size="small" @click="cancelForm"
            >取消</el-button
          >
          <el-button type="primary" size="small" @click="saveForm"
            >保存</el-button
          >
        </div>
      </div>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { dataUtils } from '@/common';
export default {
  name: 'informationInvestigation',
  components: {},
  props: {
    regNo: {
      type: Number | String,
      default: '',
      required: true
    },
    sex: {
      type: Number | Number,
      default: 0,
      required: true
    }
  },
  data() {
    return {
      jobList: [], //工种列表
      harmList: [], //危害因素列表
      doctorList: [], //危害因素列表
      loading: false,
      form: {
        generalConsultation: {
          menarcheHistory: {
            menarcheAge: undefined,
            menstrualPeriod: undefined,
            menstrualCycle: undefined,
            menopauseAge: undefined,
            isMenstrualPeriod: undefined
          },
          procreateHistory: {
            childQuantity: undefined,
            abortiveTimes: undefined,
            prematureTimes: undefined,
            stillbirthTimes: undefined,
            teratomaTimes: undefined,
            childrenHealthStatus: ''
          },
          marrageHistory: {
            marrageDate: '',
            spouse: '',
            spouseHealthStatus: '',
            marryStatus: ''
          },
          smokingHistory: {
            smokingStatus: undefined,
            smokingCountPerDays: undefined,
            smokingYears: undefined,
            smokingMonths: undefined
          },
          drinkingHistory: {
            drinkingStatus: undefined,
            drinkingMillilitersPerDays: undefined,
            drinkingHistoryYear: undefined
          },
          familyHistory: '无特殊',
          personalHistory: '',
          otherSituations: ''
        },
        anamneses: [],
        occupationHistory: [
          {
            sortIndex: 0,
            hazardFactorType: '',
            dataTime: [],
            beginDate: '',
            endDate: '',
            companyName: '',
            workshop: '',
            job: '',
            contactHazardousElements: '',
            workloadPerDay: '',
            exposureDose: '',
            overExposureStatus: '',
            irradiationType: '',
            irradiationTypeCode: '',
            radiationType: '',
            protectiveMeasures: '',
            examTime: '',
            doctorName: ''
          }
        ],
        examTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
        doctorName: '',
        defaultAnamneses: ''
      },
      rules: {
        'generalConsultation.smokingHistory.smokingStatus': [
          { required: true, message: '请选择吸烟情况', trigger: 'change' }
        ],
        // 'generalConsultation.drinkingHistory.drinkingStatus': [{ required: true, message: "请选择饮酒情况", trigger: "change" }],
        // 'generalConsultation.menarcheHistory.menarcheAge': [{ required: true, message: "请选择月经初潮年龄", trigger: "change" }],
        // 'generalConsultation.menarcheHistory.menstrualPeriod': [{ required: true, message: "请输入月经持续天数", trigger: "change" }],
        // 'generalConsultation.menarcheHistory.menstrualCycle': [{ required: true, message: "请输入月经周期", trigger: "change" }],
        // 'generalConsultation.menarcheHistory.menopauseAge': [{ required: true, message: "请输入停经龄", trigger: "change" }],
        // 'generalConsultation.menarcheHistory.isMenstrualPeriod': [{ required: true, message: "请选择是否月经", trigger: "change" }],
        'generalConsultation.procreateHistory.childQuantity': [
          { required: true, message: '请选择子女数量', trigger: 'change' }
        ],
        'generalConsultation.procreateHistory.abortiveTimes': [
          { required: true, message: '请选择早产次数', trigger: 'change' }
        ],
        'generalConsultation.procreateHistory.prematureTimes': [
          { required: true, message: '请选择早产次数', trigger: 'change' }
        ],
        'generalConsultation.procreateHistory.stillbirthTimes': [
          { required: true, message: '请选择产次', trigger: 'change' }
        ],
        'generalConsultation.procreateHistory.teratomaTimes': [
          { required: true, message: '请选择产次', trigger: 'change' }
        ],
        'generalConsultation.procreateHistory.childrenHealthStatus': [
          { required: true, message: '请选择子宫状态', trigger: 'change' }
        ],
        'generalConsultation.marrageHistory.marrageDate': [
          { required: true, message: '请选择怀孕日期', trigger: 'change' }
        ],
        'generalConsultation.marrageHistory.spouse': [
          { required: true, message: '请选择配偶情况', trigger: 'change' }
        ],
        'generalConsultation.marrageHistory.spouseHealthStatus': [
          { required: true, message: '请选择配偶健康情况', trigger: 'change' }
        ],
        // 'generalConsultation.familyHistory': [{ required: true, message: "请填写家族史", trigger: "change" }],
        // 'generalConsultation.personalHistory': [{ required: true, message: "请填写个人史", trigger: "change" }],
        // 'generalConsultation.otherSituations': [{ required: true, message: "请填写其他情况", trigger: "change" }],
        examTime: [
          { required: true, message: '请选择检查日期', trigger: 'change' }
        ],
        doctorName: [
          { required: true, message: '请选择检查医生', trigger: 'change' }
        ]
      }
    };
  },
  watch: {},
  computed: {
    ...mapGetters(['G_sysOperator', 'G_userInfo', 'G_EnumList'])
  },
  methods: {
    getDoctorList() {
      const that = this;
      that.$ajax
        .paramsPost(that.$apiUrls.GetOperatorListByRoleCode, {
          roleCode: that.G_EnumList['ConstRoleType'].doctor
        })
        .then((res) => {
          let { success, returnData } = res.data;
          if (!success) return;

          that.doctorList = returnData;
          this.setDoctorName();
        })
        .finally((_) => {});
    },
    /**
     * @author: key
     * @description: 设置医生默认值 (医生列表没有就设置成第一个)
     * @return {*}
     */
    setDoctorName() {
      const that = this;
      const item = that.doctorList.find(
        (item) => item.operatorCode == that.G_userInfo.codeOper?.operatorCode
      );

      if (item) that.form.doctorName = item.operatorName;
      else that.form.doctorName = that.doctorList[0].operatorName;
    },
    //重置表单
    resetForm() {
      this.form = {
        generalConsultation: {
          menarcheHistory: {
            menarcheAge: undefined,
            menstrualPeriod: undefined,
            menstrualCycle: undefined,
            menopauseAge: undefined,
            isMenstrualPeriod: undefined
          },
          procreateHistory: {
            childQuantity: undefined,
            abortiveTimes: undefined,
            prematureTimes: undefined,
            stillbirthTimes: undefined,
            teratomaTimes: undefined,
            childrenHealthStatus: ''
          },
          marrageHistory: {
            marrageDate: '',
            spouse: '',
            spouseHealthStatus: '',
            marryStatus: ''
          },
          smokingHistory: {
            smokingStatus: undefined,
            smokingCountPerDays: undefined,
            smokingYears: undefined,
            smokingMonths: undefined
          },
          drinkingHistory: {
            drinkingStatus: undefined,
            drinkingMillilitersPerDays: undefined,
            drinkingHistoryYear: undefined
          },
          familyHistory: '无特殊',
          personalHistory: '',
          otherSituations: ''
        },
        anamneses: [],
        occupationHistory: [
          {
            sortIndex: 0,
            hazardFactorType: '',
            dataTime: [],
            beginDate: '',
            endDate: '',
            companyName: '',
            workshop: '',
            job: '',
            contactHazardousElements: '',
            workloadPerDay: '',
            exposureDose: '',
            overExposureStatus: '',
            irradiationType: '',
            irradiationTypeCode: '',
            radiationType: '',
            protectiveMeasures: '',
            examTime: '',
            doctorName: ''
          }
        ],
        examTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
        doctorName: '',
        defaultAnamneses: ''
      };
      this.getDoctorList();
    },
    //获取表单数据
    getFormData() {
      this.loading = true;
      try {
        this.resetForm(); // 重置表单
        this.$ajax
          .paramsPost(this.$apiUrls.GetOccupationalConsultation, {
            regNo: this.regNo
          })
          .then((r) => {
            let { success, returnData } = r.data;
            if (!success) return this.$message.error('获取数据失败');
            this.loading = false;
            // 数据回显
            if (returnData) {
              // 处理 generalConsultation
              if (returnData.generalConsultation) {
                for (let key in returnData.generalConsultation) {
                  if (
                    !!returnData.generalConsultation[key] &&
                    typeof returnData.generalConsultation[key] === 'object'
                  ) {
                    for (let k in returnData.generalConsultation[key]) {
                      if (key === 'marrageHistory')
                        this.form.generalConsultation[key][k] =
                          returnData.generalConsultation[key][k] ?? undefined;
                      else
                        this.form.generalConsultation[key][k] =
                          returnData.generalConsultation[key][k] ?? '/';
                    }
                  } else if (!!returnData.generalConsultation[key]) {
                    this.form.generalConsultation[key] =
                      returnData.generalConsultation[key] ?? undefined;
                  }
                }
              }
              console.log(this.form);
              // 处理 occupationHistory
              if (
                returnData.occupationHistory &&
                returnData.occupationHistory.length > 0
              ) {
                this.form.occupationHistory = returnData.occupationHistory.map(
                  (item) => {
                    // 将 beginDate 和 endDate 转换为 dataTime
                    if (item.beginDate && item.endDate) {
                      item.dataTime = [item.beginDate, item.endDate];
                    }
                    // 处理 contactHazardousElements
                    item.contactHazardousElements =
                      item.contactHazardousElements?.split(',') || '';
                    return item;
                  }
                );
              }

              // 处理 anamneses
              if (returnData.anamneses && returnData.anamneses.length > 0) {
                this.form.anamneses = returnData.anamneses.map((item) => {
                  return item;
                });
              }

              this.form.examTime = returnData.examTime || this.form.examTime;
              this.form.doctorName =
                returnData.doctorName || this.form.doctorName;
              this.form.defaultAnamneses =
                returnData.defaultAnamneses || this.form.defaultAnamneses;
            }
          });
      } finally {
        this.loading = false;
      }
    },
    //吸烟选择变化
    handleSmokingChange(e) {
      if (e === 4) {
        const { smokingStatus } = this.form.generalConsultation.smokingHistory;
        this.form.generalConsultation.smokingHistory = {
          smokingStatus,
          smokingCountPerDays: undefined,
          smokingYears: undefined,
          smokingMonths: undefined
        };
        this.form.generalConsultation.smokingCountPerDays = undefined;
        this.form.generalConsultation.smokingYears = undefined;
        this.form.generalConsultation.smokingMonths = undefined;
      }
    },
    //饮酒选择变化
    handleDrinkingChange(e) {
      if (e === 0) {
        const { drinkingStatus } =
          this.form.generalConsultation.drinkingHistory;
        this.form.generalConsultation.drinkingHistory = {
          drinkingStatus,
          drinkingMillilitersPerDays: undefined,
          drinkingHistoryYear: undefined
        };
      }
    },
    handleMarryChange(e) {
      if (e === 1) {
        this.form.generalConsultation.marrageHistory = {
          marrageDate: '',
          spouse: '',
          spouseHealthStatus: '',
          marryStatus: this.form.generalConsultation.marrageHistory.marryStatus
        };
      }
    },
    //取消
    cancelForm() {
      // this.$refs.form.resetFields();
      this.resetForm();
      this.$emit('cancel');
    },
    //保存
    saveForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          let params = dataUtils.deepCopy(this.form);
          // 递归处理所有属性
          const replaceSlashToNull = (obj) => {
            if (obj instanceof Object) {
              for (let key in obj) {
                if (obj[key] === '/' || obj[key] === '') {
                  obj[key] = null; // 替换为 null
                } else if (typeof obj[key] === 'object') {
                  replaceSlashToNull(obj[key]); // 递归处理嵌套对象
                }
              }
            } else if (Array.isArray(obj)) {
              obj.forEach((item) => replaceSlashToNull(item)); // 处理数组元素
            }
          };

          // 执行替换逻辑
          replaceSlashToNull(params);
          params.regNo = this.regNo;
          params.occupationHistory.forEach((item) => {
            item.beginDate = item.dataTime?.[0] || '';
            item.endDate = item.dataTime?.[1] || '';
            item.contactHazardousElements =
              item.contactHazardousElements?.length > 0
                ? item.contactHazardousElements?.join(',')
                : '';
          });
          this.$ajax
            .post(this.$apiUrls.SaveOccupationalConsultation, params)
            .then((r) => {
              let { success, returnData } = r.data;
              if (!success) return;
              this.$message.success('保存成功');
              this.$emit('success');
            })
            .catch((e) => {
              this.$message.error('保存失败:' + e);
            });
        } else {
          return false;
        }
      });
    },
    //获取危害因素列表
    getHarmList() {
      let datas = {
        pageSize: 0,
        pageNumber: 0
      };
      this.$ajax
        .post(this.$apiUrls.ReadCodeOccupationalHazardousByPage, datas)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.harmList = returnData;
        });
    },
    //获取工种下拉
    getItems() {
      this.$ajax
        .post(this.$apiUrls.ReadCodeOccupationalJobByPage, {
          pageNumber: 0,
          pageSize: 0,
          keyword: ''
        })
        .then((res) => {
          let { success, returnData, totalPage } = res.data;
          if (!success) return;
          this.jobList = returnData;
        });
    },
    //增加一行既往病史
    addAnamnesis() {
      let item = {
        sortIndex: this.form.anamneses?.length || 0,
        diseaseName: '',
        diagnosisDate: '',
        diagnosticUnit: '',
        treatmentProcess: '',
        conversion: ''
      };
      this.form.anamneses.push(item);
    },
    //增加一行职业史
    addOccupation() {
      let item = {
        sortIndex: this.form.occupationHistory.length,
        hazardFactorType: '',
        dataTime: [],
        beginDate: '',
        endDate: '',
        companyName: '',
        workshop: '',
        job: '',
        contactHazardousElements: '',
        workloadPerDay: '',
        exposureDose: '',
        overExposureStatus: '',
        irradiationType: '',
        irradiationTypeCode: '',
        radiationType: '',
        protectiveMeasures: '',
        examTime: '',
        doctorName: ''
      };
      this.form.occupationHistory.push(item);
    },
    //职业类型编码变化
    handleOccupationTypeChange(e, item) {
      item.workloadPerDay = '';
      item.exposureDose = '';
      item.overExposureStatus = '';
      item.irradiationType = '';
      item.irradiationTypeCode = '';
      item.radiationType = '';
      item.protectiveMeasures = '';
    }
  },
  created() {},
  mounted() {
    this.getItems();
    this.getHarmList();
  }
};
</script>
<style lang="less" scoped>
// .el-form-item {
//   margin-bottom: 0px;
// }

// .subject {
//   label {
//     white-space: nowrap;
//   }

//   .box-border {
//     padding: 10px 5px;
//     // border: #a3a4ab solid 1px;
//     border-radius: 5px;
//     box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)
//   }

//   .subject-form {
//     display: flex;
//     flex-direction: column;
//     gap: 10px;

//     .smoke,
//     .menstruation,
//     .birth {
//       display: flex;
//       align-content: center;
//       margin-top: 15px;
//       flex-wrap: wrap;

//       .el-input {
//         width: 4rem;
//       }

//       margin-left: 20px;

//       /deep/ .el-input__inner {
//         padding: 0 0 !important;
//         border: unset;
//         border-bottom: 1px solid #a3a4ab;
//         border-bottom-left-radius: 0;
//         border-bottom-right-radius: 0;
//         text-align: center;
//       }
//     }

//     .occupation {

//       // flex-direction: column;
//       .occupation-item {
//         display: flex;
//         gap: 35px;
//       }
//     }

//     .item-bottom {
//       display: flex;
//       align-items: center;
//       justify-content: space-between;

//       &>div {
//         display: flex;
//         align-items: center;
//         gap: 10px;
//       }
//     }

//     .subject-item {
//       .subject-item-head {
//         display: flex;
//         justify-content: space-between;
//         align-items: center;
//       }

//       .last {
//         /deep/ .el-form-item__content {
//           display: flex;
//         }
//       }

//       padding: 10px 0px;
//       border-bottom: 1px #DCDFE6 solid;

//       .smoke {
//         margin-top: 15px;
//         margin-left: 20px;

//         span {
//           align-content: center;
//           font-size: 14px;
//           font-weight: bold;
//           margin-right: 10px;

//         }

//         display: flex;
//         // flex-direction: column;
//         align-content: center;

//         .smoke-head {
//           display: flex;
//         }
//       }

//       .menstruation,
//       .birth {
//         display: flex;
//         gap: 35px;
//       }
//     }
//   }
// }
// .required{
//   color:red;
// }

@primary-color: #409eff;
@border-color: #ebeef5;
@background-color: #f5f7fa;
@text-color: #606266;
@title-color: #303133;

.subject {
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  // box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  label {
    white-space: nowrap;
    color: @text-color;
    font-size: 14px;
  }

  .box-border {
    padding: 15px;
    margin-bottom: 15px;
    background-color: @background-color;
    border-radius: 6px;
    border-left: 4px solid @primary-color;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
    }
  }

  .subject-form {
    .el-form-item {
      margin-bottom: 0px;

      /deep/ .el-form-item__label {
        color: @text-color;
        font-weight: normal;
      }
    }

    .smoke,
    .menstruation,
    .birth {
      display: flex;
      align-items: center;
      margin-top: 15px;
      flex-wrap: wrap;
      gap: 20px;

      .el-input {
        width: 6rem;
      }

      margin-left: 20px;

      // /deep/ .el-input__inner {
      //   padding: 0 10px !important;
      //   border: 1px solid @border-color;
      //   border-radius: 4px;
      //   text-align: center;
      //   height: 32px;
      //   line-height: 32px;
      // }
    }

    .occupation {
      .occupation-item {
        display: flex;
        gap: 20px;
      }
    }

    .item-bottom {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 20px;
      padding-top: 20px;
      border-top: 1px dashed @border-color;

      & > div {
        display: flex;
        align-items: center;
        gap: 15px;
      }
    }

    .subject-item {
      padding: 20px;
      margin-bottom: 20px;
      background-color: white;
      border-radius: 6px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.12);
      }

      h3 {
        color: @title-color;
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid @border-color;
      }

      .subject-item-head {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        h3 {
          border-bottom: none;
          margin-bottom: 0;
        }
      }

      .last {
        /deep/ .el-form-item__content {
          display: flex;
          align-items: center;
        }
      }

      .smoke {
        margin-top: 15px;
        margin-left: 20px;

        span {
          align-content: center;
          font-size: 14px;
          font-weight: 500;
          margin-right: 10px;
          color: @title-color;
        }

        .smoke-head {
          display: flex;
          align-items: center;
          width: 100%;
          margin-bottom: 10px;
        }
      }

      .menstruation,
      .birth {
        display: flex;
        gap: 20px;
        flex-wrap: wrap;
      }
    }
  }
}

.required {
  color: #f56c6c;
  margin-right: 4px;
}

/deep/ .el-radio {
  margin-right: 15px;
}

/deep/ .el-button {
  transition: all 0.3s;
}

/deep/ .el-textarea__inner {
  min-height: 60px !important;
}

/deep/ .el-date-editor.el-input {
  width: 180px;
}

/deep/ .el-select {
  width: 100%;
}
/deep/ .el-input__inner {
  padding: 0 0 !important;
  border: unset;
  border-bottom: 1px solid #a3a4ab;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  text-align: center;
}
</style>
