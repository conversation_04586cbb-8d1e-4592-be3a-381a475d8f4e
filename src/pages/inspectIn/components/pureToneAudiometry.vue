<!--
 * @FilePath: \shenshan\KrPeis\src\pages\inspectIn\components\pureToneAudiometry.vue
 * @Description:  纯音测听组件
 * @Author: justin
 * @Date: 2024-06-27 11:16:29
 * @Version: 0.0.1
 * @LastEditors: key
 * @LastEditTime: 2025-04-16 09:20:46
*
-->
<template>
  <div class="pure-ton-audiometry-container" v-loading="loadingMask">
    <div class="header-wrapper">
      <el-row
        align="center"
        :gutter="20"
        justify="space-between"
        type="flex"
        class="info-box"
      >
        <!-- 患者信息 -->
        <el-col :span="14" class="patient-info-box">
          <label>姓名：{{ patientInfo.name }}</label>
          <label>性别：{{ G_EnumList['Sex'][patientInfo.sex] }}</label>
          <label>年龄：{{ patientInfo.age }}</label>
        </el-col>
      </el-row>
    </div>

    <!-- 内容 -->
    <div class="body-wrapper">
      <el-button size="mini" @click="switchLegeng">{{
        rectifyShow ? '切换实测图例' : '切换校正图例'
      }}</el-button>
      <!-- 测听折线图 -->
      <el-row
        align="center"
        :gutter="20"
        justify="space-between"
        type="flex"
        class="chart-box"
        v-show="!rectifyShow"
      >
        <el-col :span="12">
          <div
            id="audiometry-chart-right"
            ref="chartRight_Ref"
            :style="myChartStyle"
          ></div>
        </el-col>
        <el-col :span="12">
          <div id="audiometry-chart-left" :style="myChartStyle"></div>
        </el-col>
      </el-row>
      <!-- 校正折线图 -->
      <el-row
        align="center"
        :gutter="20"
        justify="space-between"
        type="flex"
        class="chart-box"
        v-show="rectifyShow"
      >
        <el-col :span="12">
          <div id="rectify-chart-right" :style="myChartStyle"></div>
        </el-col>
        <el-col :span="12">
          <div id="rectify-chart-left" :style="myChartStyle"></div>
        </el-col>
      </el-row>

      <!-- 测听数据录入 -->
      <el-form
        :model="form"
        ref="form_ref"
        :show-message="false"
        :inline="true"
        style="width: 100%"
        class="form-box"
      >
        <!-- x轴标签对应标题 -->
        <el-row align="center" :gutter="20" justify="space-between" type="flex">
          <el-col :span="12" v-for="a in [1, 2]" :key="a">
            <div class="x-label-box">
              <label v-for="(item, index) in constHzArr" :key="index"
                >{{ item }}Hz</label
              >
            </div>
          </el-col>
        </el-row>

        <!-- 实测气导值 -->
        <el-row align="center" :gutter="20" justify="space-between" type="flex">
          <el-col :span="12" class="input-item">
            <label>实测气导值：</label>
            <div class="input-box">
              <el-input-number
                v-for="(item, index) in constHzArr"
                :key="index"
                :ref="'airConductionByActForRight_' + index + '_ref'"
                v-model="airConductionByActForRight[item].value"
                size="mini"
                :step="1"
                :min="-10"
                :max="120"
                :controls="false"
                :precision="0"
                :id="'inp_' + index"
                readonly
                @keyup.enter.native="enterFocusNext(index)"
                :disabled="
                  (!form.is8000Hz && index === constHzArr.length - 1) ||
                  isReadonly
                "
                :class="
                  Number.isFinite(form['rightGas' + item])
                    ? ''
                    : index < constHzArr.length - 1 ||
                        (form.is8000Hz && index === constHzArr.length - 1)
                      ? 'invalid-error'
                      : ''
                "
                @change="
                  (currentValue, oldValue) =>
                    airConductionByActForRightChange(
                      'value',
                      currentValue,
                      oldValue,
                      item
                    )
                "
                @keyup.up.native.prevent="
                  airConductionByActForRightChange('keyup', $event, null, item)
                "
                @keyup.down.native.prevent="
                  airConductionByActForRightChange('keyup', $event, null, item)
                "
              ></el-input-number>
            </div>
          </el-col>
          <el-col :span="12" class="input-item">
            <label>实测气导值：</label>
            <div class="input-box">
              <el-input-number
                v-for="(item, index) in constHzArr"
                :key="index"
                :ref="'airConductionByActForLeft_' + index + '_ref'"
                v-model="airConductionByActForLeft[item].value"
                size="mini"
                :step="1"
                :min="-10"
                :max="120"
                :controls="false"
                :precision="0"
                :id="'inp_' + (7 + index)"
                @keyup.enter.native="enterFocusNext(7 + index)"
                :disabled="
                  (!form.is8000Hz && index === constHzArr.length - 1) ||
                  isReadonly
                "
                :class="
                  Number.isFinite(form['leftGas' + item])
                    ? ''
                    : index < constHzArr.length - 1 ||
                        (form.is8000Hz && index === constHzArr.length - 1)
                      ? 'invalid-error'
                      : ''
                "
                @change="
                  (currentValue, oldValue) =>
                    airConductionByActForLeftChange(
                      'value',
                      currentValue,
                      oldValue,
                      item
                    )
                "
                @keyup.up.native.prevent="
                  airConductionByActForLeftChange('keyup', $event, null, item)
                "
                @keyup.down.native.prevent="
                  airConductionByActForLeftChange('keyup', $event, null, item)
                "
              ></el-input-number>
            </div>
          </el-col>
        </el-row>

        <!-- 校正气导值 -->
        <el-row align="center" :gutter="20" justify="space-between" type="flex">
          <el-col :span="12" class="input-item">
            <label>校正气导值：</label>
            <div class="input-box">
              <el-input-number
                v-for="(item, index) in constHzArr"
                :key="index"
                v-model="airConductionByCorrForRight[item].value"
                size="mini"
                :controls="false"
                :precision="0"
                disabled
              ></el-input-number>
            </div>
          </el-col>
          <el-col :span="12" class="input-item">
            <label>校正气导值：</label>
            <div class="input-box">
              <el-input-number
                v-for="(item, index) in constHzArr"
                :key="index"
                v-model="airConductionByCorrForLeft[item].value"
                size="mini"
                :value="item"
                :controls="false"
                :precision="0"
                disabled
              ></el-input-number>
            </div>
          </el-col>
        </el-row>

        <!-- 实测骨导值 -->
        <el-row align="center" :gutter="20" justify="space-between" type="flex">
          <el-col :span="12" class="input-item">
            <label>实测骨导值：</label>
            <div class="input-box">
              <el-input-number
                v-for="(item, index) in constHzArr"
                :key="index"
                :ref="'boneConductionByActForRight_' + index + '_ref'"
                v-model="boneConductionByActForRight[item].value"
                size="mini"
                :value="item"
                :step="1"
                :min="-10"
                :max="120"
                :controls="false"
                :precision="0"
                :disabled="index === constHzArr.length - 1 || isReadonly"
                :id="'inp_' + (14 + index)"
                @keyup.enter.native="enterFocusNext(14 + index)"
                @change="
                  (currentValue, oldValue) =>
                    boneConductionByActForRightChange(
                      'value',
                      currentValue,
                      oldValue,
                      item
                    )
                "
                @keyup.up.native.prevent="
                  boneConductionByActForRightChange('keyup', $event, null, item)
                "
                @keyup.down.native.prevent="
                  boneConductionByActForRightChange('keyup', $event, null, item)
                "
              ></el-input-number>
            </div>
          </el-col>
          <el-col :span="12" class="input-item">
            <label>实测骨导值：</label>
            <div class="input-box">
              <el-input-number
                v-for="(item, index) in constHzArr"
                :key="index"
                :ref="'boneConductionByActForLeft_' + index + '_ref'"
                v-model="boneConductionByActForLeft[item].value"
                size="mini"
                :step="1"
                :min="-10"
                :max="120"
                :controls="false"
                :precision="0"
                :disabled="index === constHzArr.length - 1 || isReadonly"
                :id="'inp_' + (21 + index)"
                @keyup.enter.native="enterFocusNext(21 + index)"
                @change="
                  (currentValue, oldValue) =>
                    boneConductionByActForLeftChange(
                      'value',
                      currentValue,
                      oldValue,
                      item
                    )
                "
                @keyup.up.native.prevent="
                  boneConductionByActForLeftChange('keyup', $event, null, item)
                "
                @keyup.down.native.prevent="
                  boneConductionByActForLeftChange('keyup', $event, null, item)
                "
              ></el-input-number>
            </div>
          </el-col>
        </el-row>

        <!-- 校正骨导值 -->
        <el-row align="center" :gutter="20" justify="space-between" type="flex">
          <el-col :span="12" class="input-item">
            <label>校正骨导值：</label>
            <div class="input-box">
              <el-input-number
                v-for="(item, index) in constHzArr"
                :key="index"
                v-model="boneConductionByCorrForRight[item].value"
                size="mini"
                :controls="false"
                :precision="0"
                disabled
              ></el-input-number>
            </div>
          </el-col>
          <el-col :span="12" class="input-item">
            <label>校正骨导值：</label>
            <div class="input-box">
              <el-input-number
                v-for="(item, index) in constHzArr"
                :key="index"
                v-model="boneConductionByCorrForLeft[item].value"
                size="mini"
                :controls="false"
                :precision="0"
                disabled
              ></el-input-number>
            </div>
          </el-col>
        </el-row>

        <!-- 语频 -->
        <el-row align="center" :gutter="20" type="flex">
          <el-col :span="12" class="speech-frequency-box">
            <el-checkbox
              :disabled="
                [null, undefined, ''].includes(form.rightGasLowerRateAverage) ||
                [null, undefined, ''].includes(
                  form.rightBoneLowerRateAverage
                ) ||
                isReadonly
              "
              @change="generateExamConclusion"
              v-model="rightGasLowerRateAverageChecked"
              >右耳语频平均值：</el-checkbox
            >
            <!-- <label>右耳语频平均值：</label> -->
            <div class="item-box">
              <label>气导：</label>
              <el-input-number
                v-model="form.rightGasLowerRateAverage"
                size="mini"
                :controls="false"
                :precision="0"
                disabled
              ></el-input-number>
            </div>

            <div class="item-box">
              <label>骨导：</label>
              <el-input-number
                v-model="form.rightBoneLowerRateAverage"
                size="mini"
                :controls="false"
                :precision="0"
                disabled
              ></el-input-number>
            </div>
          </el-col>

          <el-col :span="12" class="speech-frequency-box">
            <el-checkbox
              :disabled="
                [null, undefined, ''].includes(form.leftGasLowerRateAverage) ||
                [null, undefined, ''].includes(form.leftBoneLowerRateAverage) ||
                isReadonly
              "
              @change="generateExamConclusion"
              v-model="leftGasLowerRateAverageChecked"
              >左耳语频平均值：</el-checkbox
            >
            <!-- <label>左耳语频平均值：</label> -->
            <div class="item-box">
              <label>气导：</label>
              <el-input-number
                v-model="form.leftGasLowerRateAverage"
                size="mini"
                :controls="false"
                :precision="0"
                disabled
              ></el-input-number>
            </div>

            <div class="item-box">
              <label>骨导：</label>
              <el-input-number
                v-model="form.leftBoneLowerRateAverage"
                size="mini"
                :controls="false"
                :precision="0"
                disabled
              ></el-input-number>
            </div>
          </el-col>
        </el-row>

        <!-- 听阈 -->
        <el-row align="center" :gutter="20" type="flex">
          <el-col :span="12" class="hearing-threshold-box">
            <!-- <label>右耳听阈加权值：</label> -->
            <el-checkbox
              :disabled="
                [null, undefined, ''].includes(
                  form.rightGasLowerRateWeighted
                ) ||
                [null, undefined, ''].includes(
                  form.rightBoneLowerRateWeighted
                ) ||
                isReadonly
              "
              @change="generateExamConclusion"
              v-model="rightGasLowerRateWeightedChecked"
              >右耳听阈加权值：</el-checkbox
            >
            <div class="item-box">
              <label>气导：</label>
              <el-input-number
                v-model="form.rightGasLowerRateWeighted"
                size="mini"
                :controls="false"
                :precision="0"
                disabled
              ></el-input-number>
            </div>

            <div class="item-box">
              <label>骨导：</label>
              <el-input-number
                v-model="form.rightBoneLowerRateWeighted"
                size="mini"
                :controls="false"
                :precision="0"
                disabled
              ></el-input-number>
            </div>
          </el-col>

          <el-col :span="12" class="hearing-threshold-box">
            <!-- <label>左耳听阈加权值：</label> -->
            <el-checkbox
              :disabled="
                [null, undefined, ''].includes(form.leftGasLowerRateWeighted) ||
                [null, undefined, ''].includes(
                  form.leftBoneLowerRateWeighted
                ) ||
                isReadonly
              "
              @change="generateExamConclusion"
              v-model="leftGasLowerRateWeightedChecked"
              >左耳听阈加权值：</el-checkbox
            >
            <div class="item-box">
              <label>气导：</label>
              <el-input-number
                v-model="form.leftGasLowerRateWeighted"
                size="mini"
                :controls="false"
                :precision="0"
                disabled
              ></el-input-number>
            </div>

            <div class="item-box">
              <label>骨导：</label>
              <el-input-number
                v-model="form.leftBoneLowerRateWeighted"
                size="mini"
                :controls="false"
                :precision="0"
                disabled
              ></el-input-number>
            </div>
          </el-col>
        </el-row>
        <!-- 双耳 -->
        <el-row align="center" :gutter="20" type="flex">
          <el-col :span="12" class="speech-frequency-box">
            <!-- <label>双耳高频平均值：</label> -->
            <el-checkbox
              :disabled="
                [null, undefined, ''].includes(
                  form.binauralGasHigherRateAverage
                ) ||
                [null, undefined, ''].includes(
                  form.binauralBoneHigherRateAverage
                ) ||
                isReadonly
              "
              @change="generateExamConclusion"
              v-model="binauralGasHigherRateAverageChecked"
              >双耳高频平均值：</el-checkbox
            >
            <div class="item-box">
              <label style="width: 5.8rem; text-align: right">气导：</label>
              <el-input-number
                v-model="form.binauralGasHigherRateAverage"
                size="mini"
                :controls="false"
                :precision="0"
                disabled
              ></el-input-number>
            </div>

            <div class="item-box">
              <label style="width: 4.8rem; text-align: right">骨导：</label>
              <el-input-number
                v-model="form.binauralBoneHigherRateAverage"
                size="mini"
                :controls="false"
                :precision="0"
                disabled
              ></el-input-number>
            </div>
          </el-col>
          <el-col :span="12" class="hearing-threshold-box">
            <!-- <label>双耳高频平均听阀：</label> -->
            <el-checkbox
              @change="generateExamConclusion"
              :disabled="
                [null, undefined, ''].includes(
                  form.binauralBoneHigherRateLGRB
                ) ||
                [null, undefined, ''].includes(
                  form.binauralBoneHigherRateLBRG
                ) ||
                isReadonly
              "
              v-model="binauralBoneHigherRateThresholdChecked"
              >双耳高频平均听阀：</el-checkbox
            >
            <div class="item-box">
              <label>右骨+左气：</label>
              <el-input-number
                v-model="form.binauralBoneHigherRateLGRB"
                size="mini"
                :controls="false"
                :precision="0"
                disabled
              ></el-input-number>
            </div>

            <div class="item-box">
              <label>右气+左骨：</label>
              <el-input-number
                v-model="form.binauralBoneHigherRateLBRG"
                size="mini"
                :controls="false"
                :precision="0"
                disabled
              ></el-input-number>
            </div>
          </el-col>
        </el-row>

        <!-- 检查日期、医生 -->
        <el-row
          align="center"
          :gutter="20"
          justify="space-between"
          type="flex"
          style="margin-bottom: 10px"
        >
          <el-col :span="7" style="display: flex; align-items: center">
            <label>检查日期：</label>
            <el-form-item prop="examTime" required>
              <el-date-picker
                v-model="form.examTime"
                type="date"
                placeholder="请选择日期"
                size="mini"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                style="width: 10rem"
                :picker-options="pickerOptions"
                :disabled="isReadonly"
                @change="examTimeChange"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="7" style="display: flex; align-items: center">
            <label>检查医生：</label>
            <el-form-item prop="doctorName" required>
              <el-select
                v-model="form.doctorName"
                placeholder="请选择"
                size="mini"
                style="width: 10rem"
                filterable
                :disabled="isReadonly"
                @change="doctorNameChange"
              >
                <el-option
                  v-for="item in doctorList"
                  :key="item.operatorCode"
                  :label="item.operatorName"
                  :value="item.operatorName"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="10"> </el-col>
        </el-row>

        <!-- 结论 -->
        <el-row align="center" :gutter="20" justify="space-between" type="flex">
          <el-col :span="24" class="conclusion-box">
            <label style="width: 75px; text-align: right">结论：</label>
            <el-form-item prop="examResult" required>
              <el-input
                type="textarea"
                :disabled="isReadonly"
                :autosize="{ minRows: 2, maxRows: 4 }"
                v-model="form.examResult"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- 底部 -->
    <div class="footer-wrapper">
      <el-row align="center" :gutter="20" justify="space-between" type="flex">
        <el-col :span="4">
          <el-checkbox
            :disabled="isReadonly"
            v-model="form.is8000Hz"
            @change="check8000HzChange"
            >是否启用8000Hz高频</el-checkbox
          >
        </el-col>
        <el-col :span="20" class="btn-box">
          <!-- 操作 -->
          <el-button size="mini" @click="cancel" :loading="loading"
            >关闭</el-button
          >
          <el-button
            type="primary"
            size="mini"
            @click="calc"
            :loading="loading"
            :disabled="isReadonly"
          >
            计算
          </el-button>
          <el-button
            type="primary"
            size="mini"
            @click="confirm"
            :loading="loading"
            :disabled="isReadonly"
          >
            保存
          </el-button>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import { mapGetters } from 'vuex';
import { dataUtils } from '@/common';
// 坐标交点图标
import right_ear_1_1 from '@/assets/img/pureToneAudiometry/right-ear-1-1.svg';
import right_ear_1_2 from '@/assets/img/pureToneAudiometry/right-ear-1-2.svg';
import right_ear_1_3 from '@/assets/img/pureToneAudiometry/right-ear-1-3.svg';
import right_ear_1_4 from '@/assets/img/pureToneAudiometry/right-ear-1-4.svg';
import right_ear_2_1 from '@/assets/img/pureToneAudiometry/right-ear-2-1.svg';
import right_ear_2_2 from '@/assets/img/pureToneAudiometry/right-ear-2-2.svg';
import right_ear_2_3 from '@/assets/img/pureToneAudiometry/right-ear-2-3.svg';
import right_ear_2_4 from '@/assets/img/pureToneAudiometry/right-ear-2-4.svg';
import left_ear_1_1 from '@/assets/img/pureToneAudiometry/left-ear-1-1.svg';
import left_ear_1_2 from '@/assets/img/pureToneAudiometry/left-ear-1-2.svg';
import left_ear_1_3 from '@/assets/img/pureToneAudiometry/left-ear-1-3.svg';
import left_ear_1_4 from '@/assets/img/pureToneAudiometry/left-ear-1-4.svg';
import left_ear_2_1 from '@/assets/img/pureToneAudiometry/left-ear-2-1.svg';
import left_ear_2_2 from '@/assets/img/pureToneAudiometry/left-ear-2-2.svg';
import left_ear_2_3 from '@/assets/img/pureToneAudiometry/left-ear-2-3.svg';
import left_ear_2_4 from '@/assets/img/pureToneAudiometry/left-ear-2-4.svg';

export default {
  name: 'pureToneAudiometry',
  props: {
    patientInfo: {
      type: Object,
      default: () => ({
        regNo: undefined,
        name: null,
        sex: undefined,
        age: undefined
      })
    },
    combCode: {
      type: String,
      default: ''
    },
    // 是否只读模式
    isReadonly: {
      type: Boolean,
      default: false
    },
    // 录入的时间和日期信息
    inputInfo: {
      type: Object,
      default: () => {
        return {
          examTime: '',
          doctorName: ''
        };
      }
    }
  },
  data() {
    return {
      rightGasLowerRateAverageChecked: false,
      leftGasLowerRateAverageChecked: false,
      binauralGasHigherRateAverageChecked: false,
      rightGasLowerRateWeightedChecked: false,
      leftGasLowerRateWeightedChecked: false,
      binauralBoneHigherRateThresholdChecked: false,
      chartRight: null, // 右耳图表
      chartLeft: null, // 左耳图表
      rectifyChartRight: null, // 校正右耳图表
      rectifyChartLeft: null, // 校正左耳图表
      myChartStyle: { width: '100%', height: '400px' }, // 图表样式
      xAxisData: xAxisData, // X轴数据
      constHzArr: [500, 1000, 2000, 3000, 4000, 6000, 8000], // 固定hz数组
      // 动态表单数据
      airConductionByActForRight: {}, // 右耳实测气导值
      airConductionByActForLeft: {}, // 左耳实测气导值
      airConductionByCorrForRight: {}, // 右耳校正气导值
      airConductionByCorrForLeft: {}, // 左耳校正气导值
      boneConductionByActForRight: {}, // 右耳实测骨导值
      boneConductionByActForLeft: {}, // 左耳实测骨导值
      boneConductionByCorrForRight: {}, // 右耳校正骨导值
      boneConductionByCorrForLeft: {}, // 左耳校正骨导值
      // 表单数据
      form: {
        regNo: this.patientInfo.regNo, // 体检号
        age: this.patientInfo.age, // 年龄
        sex: this.patientInfo.sex, // 性别
        leftGas500: 0, // 左耳气导500
        leftGas1000: 0, // 左耳气导1000
        leftGas2000: 0, // 左耳气导2000
        leftGas3000: 0, // 左耳气导3000
        leftGas4000: 0, // 左耳气导4000
        leftGas6000: 0, // 左耳气导6000
        leftGas8000: 0, // 左耳气导8000
        leftGasCorrected500: undefined, // 左耳校正气导500
        leftGasCorrected1000: undefined, // 左耳校正气导1000
        leftGasCorrected2000: undefined, // 左耳校正气导2000
        leftGasCorrected3000: undefined, // 左耳校正气导3000
        leftGasCorrected4000: undefined, // 左耳校正气导4000
        leftGasCorrected6000: undefined, // 左耳校正气导6000
        rightGas500: 0, // 右耳气导500
        rightGas1000: 0, // 右耳气导1000
        rightGas2000: 0, // 右耳气导2000
        rightGas3000: 0, // 右耳气导3000
        rightGas4000: 0, // 右耳气导4000
        rightGas6000: 0, // 右耳气导6000
        rightGas8000: 0, // 右耳气导8000
        rightGasCorrected500: undefined, // 右耳校正气导500
        rightGasCorrected1000: undefined, // 右耳校正气导1000
        rightGasCorrected2000: undefined, // 右耳校正气导2000
        rightGasCorrected3000: undefined, // 右耳校正气导3000
        rightGasCorrected4000: undefined, // 右耳校正气导4000
        rightGasCorrected6000: undefined, // 右耳校正气导6000
        leftBone500: 0, // 左耳骨导500
        leftBone1000: 0, // 左耳骨导1000
        leftBone2000: 0, // 左耳骨导2000
        leftBone3000: 0, // 左耳骨导3000
        leftBone4000: 0, // 左耳骨导4000
        leftBone6000: 0, // 左耳骨导6000
        leftBoneCorrected500: undefined, // 左耳校正骨导500
        leftBoneCorrected1000: undefined, // 左耳校正骨导1000
        leftBoneCorrected2000: undefined, // 左耳校正骨导2000
        leftBoneCorrected3000: undefined, // 左耳校正骨导3000
        leftBoneCorrected4000: undefined, // 左耳校正骨导4000
        leftBoneCorrected6000: undefined, // 左耳校正骨导6000
        rightBone500: 0, // 右耳骨导500
        rightBone1000: 0, // 右耳骨导1000
        rightBone2000: 0, // 右耳骨导2000
        rightBone3000: 0, // 右耳骨导3000
        rightBone4000: 0, // 右耳骨导4000
        rightBone6000: 0, // 右耳骨导6000
        rightBoneCorrected500: undefined, // 右耳校正骨导500
        rightBoneCorrected1000: undefined, // 右耳校正骨导1000
        rightBoneCorrected2000: undefined, // 右耳校正骨导2000
        rightBoneCorrected3000: undefined, // 右耳校正骨导3000
        rightBoneCorrected4000: undefined, // 右耳校正骨导4000
        rightBoneCorrected6000: undefined, // 右耳校正骨导6000
        leftGas500Type: undefined, // 左耳气导500类型
        leftGas1000Type: undefined, // 左耳气导1000类型
        leftGas2000Type: undefined, // 左耳气导2000类型
        leftGas3000Type: undefined, // 左耳气导3000类型
        leftGas4000Type: undefined, // 左耳气导4000类型
        leftGas6000Type: undefined, // 左耳气导6000类型
        leftGas8000Type: undefined, // 左耳气导8000类型
        rightGas500Type: undefined, // 右耳气导500类型
        rightGas1000Type: undefined, // 右耳气导1000类型
        rightGas2000Type: undefined, // 右耳气导2000类型
        rightGas3000Type: undefined, // 右耳气导3000类型
        rightGas4000Type: undefined, // 右耳气导4000类型
        rightGas6000Type: undefined, // 右耳气导6000类型
        rightGas8000Type: undefined, // 右耳气导8000类型
        leftBone500Type: undefined, // 左耳骨导500类型
        leftBone1000Type: undefined, // 左耳骨导1000类型
        leftBone2000Type: undefined, // 左耳骨导2000类型
        leftBone3000Type: undefined, // 左耳骨导3000类型
        leftBone4000Type: undefined, // 左耳骨导4000类型
        leftBone6000Type: undefined, // 左耳骨导6000类型
        rightBone500Type: undefined, // 右耳骨导500类型
        rightBone1000Type: undefined, // 右耳骨导1000类型
        rightBone2000Type: undefined, // 右耳骨导2000类型
        rightBone3000Type: undefined, // 右耳骨导3000类型
        rightBone4000Type: undefined, // 右耳骨导4000类型
        rightBone6000Type: undefined, // 右耳骨导6000类型
        rightGasLowerRateAverage: undefined, // 右耳语频平均值气导
        leftGasLowerRateAverage: undefined, // 左耳语频平均值气导
        rightBoneLowerRateAverage: undefined, // 右耳语频平均值骨导
        leftBoneLowerRateAverage: undefined, // 左耳语频平均值骨导
        rightGasLowerRateWeighted: undefined, // 右耳语频加权值气导
        leftGasLowerRateWeighted: undefined, // 左耳语频加权值气导
        rightBoneLowerRateWeighted: undefined, // 右耳语频加权值骨导
        leftBoneLowerRateWeighted: undefined, // 左耳语频加权值骨导
        binauralGasHigherRateAverage: undefined, // 双耳高频平均听阈气导
        binauralBoneHigherRateAverage: undefined, // 双耳高频平均听阈骨导
        binauralBoneHigherRateLGRB: undefined, // 双耳高频平均听阈右骨左气
        binauralBoneHigherRateLBRG: undefined, // 双耳高频平均听阈左骨右气
        examTime: new Date().toISOString().split('T')[0], // 检查时间
        doctorName: '', // 检查医生
        examResult: '', // 检查结果
        is8000Hz: false // 是否启用8000Hz高频
      },
      doctorList: [], // 医生列表
      loadingMask: false, // 遮罩loading
      loading: false, // 按钮加载状态
      isCalced: false, // 是否计算
      pickerOptions: {
        // 日期范围，不能超过今天
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      },
      rectifyShow: false // 校正图例的显示
    };
  },
  created() {
    const that = this;
    that._form = {};
    Object.keys(that.form).forEach((item) => {
      that._form[item] = that.form[item];
    });
    that.initChartData();
    that.getDoctorList();
  },
  mounted() {
    const that = this;
    console.log(this.$refs.chartRight_Ref.offsetWidth);
    let width = this.$refs.chartRight_Ref.offsetWidth;
    this.myChartStyle.width = width + 'px';
    this.$nextTick(() => {
      that.initChart();
    });
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_sysOperator', 'G_userInfo'])
  },
  methods: {
    // 生成检查结论
    generateExamConclusion() {
      // 1. 分离原始内容
      const original = this.form.examResult || '';

      // 2. 定义所有自动生成内容的模式
      const autoPatterns = [
        '右耳语频平均值：[^。]+?dBHL，骨导 [\\d-]+dBHL',
        '左耳语频平均值：[^。]+?dBHL，骨导 [\\d-]+dBHL',
        '双耳高频平均值：[^。]+?dBHL，骨导 [\\d-]+dBHL',
        '右耳语频加权值：[^。]+?dBHL，骨导 [\\d-]+dBHL',
        '左耳语频加权值：[^。]+?dBHL，骨导 [\\d-]+dBHL',
        '双耳高频平均听阈：右骨\\+左气 [\\d-]+dBHL，右气\\+左骨 [\\d-]+dBHL'
      ].join('|');

      // 3. 清除所有旧的自动生成内容
      const cleanText = original
        .replace(new RegExp(autoPatterns, 'g'), '') // 移除匹配内容
        .replace(/[；。]+$/g, '') // 清理结尾标点
        .trim();

      // 4. 生成新的自动内容
      const newParts = [];

      // 右耳语频平均值
      if (this.rightGasLowerRateAverageChecked) {
        const gas = Math.round(this.form.rightGasLowerRateAverage || 0);
        const bone = Math.round(this.form.rightBoneLowerRateAverage || 0);
        newParts.push(`右耳语频平均值：气导 ${gas}dBHL，骨导 ${bone}dBHL`);
      }

      // 左耳语频平均值
      if (this.leftGasLowerRateAverageChecked) {
        const gas = Math.round(this.form.leftGasLowerRateAverage || 0);
        const bone = Math.round(this.form.leftBoneLowerRateAverage || 0);
        newParts.push(`左耳语频平均值：气导 ${gas}dBHL，骨导 ${bone}dBHL`);
      }

      // 双耳高频平均值
      if (this.binauralGasHigherRateAverageChecked) {
        const gas = Math.round(this.form.binauralGasHigherRateAverage || 0);
        const bone = Math.round(this.form.binauralBoneHigherRateAverage || 0);
        newParts.push(`双耳高频平均值：气导 ${gas}dBHL，骨导 ${bone}dBHL`);
      }

      // 右耳语频加权值
      if (this.rightGasLowerRateWeightedChecked) {
        const gas = Math.round(this.form.rightGasLowerRateWeighted || 0);
        const bone = Math.round(this.form.rightBoneLowerRateWeighted || 0);
        newParts.push(`右耳语频加权值：气导 ${gas}dBHL，骨导 ${bone}dBHL`);
      }

      // 左耳语频加权值
      if (this.leftGasLowerRateWeightedChecked) {
        const gas = Math.round(this.form.leftGasLowerRateWeighted || 0);
        const bone = Math.round(this.form.leftBoneLowerRateWeighted || 0);
        newParts.push(`左耳语频加权值：气导 ${gas}dBHL，骨导 ${bone}dBHL`);
      }

      // 双耳高频听阈
      if (this.binauralBoneHigherRateThresholdChecked) {
        const lgrb = Math.round(this.form.binauralBoneHigherRateLGRB || 0);
        const lbrg = Math.round(this.form.binauralBoneHigherRateLBRG || 0);
        newParts.push(
          `双耳高频平均听阈：右骨+左气 ${lgrb}dBHL，右气+左骨 ${lbrg}dBHL`
        );
      }

      // 5. 智能拼接
      let result = cleanText;

      if (newParts.length > 0) {
        // 添加分隔符（当原始内容存在时加；）
        const separator = cleanText ? '；' : '';
        result += `${separator}${newParts.join('；')}。`;
      }

      // 6. 处理多重句号
      this.form.examResult = result
        .replace(/(；|。)+$/g, (m) => (m.includes('。') ? '。' : '')) // 确保以句号结尾
        .replace(/；+/g, '；') // 合并多个分号
        .trim();
    },
    /**
     * @author: justin
     * @description: 获取医生列表
     * @return {*}
     */
    getDoctorList() {
      const that = this;
      that.$ajax
        .paramsPost(that.$apiUrls.GetOperatorListByRoleCode, {
          roleCode: that.G_EnumList['ConstRoleType'].doctor
        })
        .then((res) => {
          let { success, returnData } = res.data;
          if (!success) return;

          that.doctorList = returnData;
          that.setDoctorName();
        })
        .finally((_) => {});
    },
    /**
     * @author: key
     * @description: 设置医生默认值 (医生列表没有就设置成第一个)
     * @return {*}
     */
    setDoctorName() {
      const that = this;
      const item = that.doctorList.find(
        (item) => item.operatorCode == that.G_userInfo.codeOper?.operatorCode
      );

      if (item) that.form.doctorName = item.operatorName;
      else that.form.doctorName = that.doctorList[0].operatorName;
    },
    /**
     * @author: justin
     * @description: 初始化图表
     * @return {*}
     */
    initChart() {
      this.loadingMask = true;
      this.initChartRight();
      this.initChartLeft();
      this.initRectifyChartRight();
      this.initRectifyChartLeft();
      this.loadingMask = false;
    },

    /**
     * @author: justin
     * @description: 初始化右耳图表
     * @return {*}
     */
    initChartRight() {
      if (!this.chartRight) {
        this.chartRight = echarts.init(
          document.getElementById('audiometry-chart-right')
        );
      }
      pureToneAudChartOption.title.text = `{titleIcon|} 右耳实测听力`;
      pureToneAudChartOption.toolbox.feature.saveAsImage.name =
        '右耳实测听力图表';
      // 实测气导
      pureToneAudChartOption.series[0].data = xAxisData.map((x) => {
        let data = this.airConductionByActForRight[x] || null;
        if (!data) return null;

        data.symbolSize = [30, 30]; // 标记大小[宽,高]
        data.symbolOffset = [0, 0]; // 标记偏移量[x,y]
        switch (data.type) {
          case 0: // 未掩蔽-有反应
            data.symbol = `image://${right_ear_1_1}`;
            break;
          case 1: // 加掩蔽-有反应
            data.symbol = `image://${right_ear_1_2}`;
            data.symbolOffset = [-3, 3];
            break;
          case 2: // 加掩蔽-有反应
            data.symbol = `image://${right_ear_1_3}`;
            data.symbolOffset = [0, -1.5];
            break;
          case 3: // 加掩蔽-无反应
            data.symbol = `image://${right_ear_1_4}`;
            data.symbolOffset = [-4, 3];
            break;
        }
        return data;
      });
      pureToneAudChartOption.series[0].itemStyle.color =
        pureToneAudChartOption.series[0].itemStyle._colorRight;
      // 实测骨导
      pureToneAudChartOption.series[1].data = xAxisData.map((x) => {
        let data = this.boneConductionByActForRight[x] || null;
        if (!data) return null;

        data.symbolSize = [30, 30]; // 标记大小[宽,高]
        data.symbolOffset = [0, 0]; // 标记偏移量[x,y]
        switch (data.type) {
          case 0: // 未掩蔽-有反应
            data.symbol = `image://${right_ear_2_1}`;
            break;
          case 1: // 未掩蔽-无反应
            data.symbol = `image://${right_ear_2_2}`;
            data.symbolOffset = [-4, 5];
            break;
          case 2: // 加掩蔽-有反应
            data.symbol = `image://${right_ear_2_3}`;
            data.symbolOffset = [-6, 0];
            break;
          case 3: // 加掩蔽-无反应
            data.symbol = `image://${right_ear_2_4}`;
            data.symbolOffset = [-10, 5];
            break;
        }
        return data;
      });
      pureToneAudChartOption.series[1].itemStyle.color =
        pureToneAudChartOption.series[1].itemStyle._colorRight;
      this.chartRight.setOption(pureToneAudChartOption);
    },

    /**
     * @author: justin
     * @description: 初始化左耳图表
     * @return {*}
     */
    initChartLeft() {
      if (!this.chartLeft) {
        this.chartLeft = echarts.init(
          document.getElementById('audiometry-chart-left')
        );
      }
      pureToneAudChartOption.title.text = `{titleIcon|} 左耳实测听力`;
      pureToneAudChartOption.toolbox.feature.saveAsImage.name =
        '左耳实测听力图表';
      // 实测气导
      pureToneAudChartOption.series[0].data = xAxisData.map((x) => {
        let data = this.airConductionByActForLeft[x] || null;
        if (!data) return null;

        data.symbolSize = [30, 30]; // 标记大小[宽,高]
        data.symbolOffset = [0, 0]; // 标记偏移量[x,y]
        switch (data.type) {
          case 0: // 未掩蔽-有反应
            data.symbol = `image://${left_ear_1_1}`;
            break;
          case 1: // 未掩蔽-无反应
            data.symbol = `image://${left_ear_1_2}`;
            data.symbolOffset = [3, 3];
            break;
          case 2: // 加掩蔽-有反应
            data.symbol = `image://${left_ear_1_3}`;
            break;
          case 3: // 加掩蔽-无反应
            data.symbol = `image://${left_ear_1_4}`;
            data.symbolOffset = [4, 4];
            break;
        }
        return data;
      });
      pureToneAudChartOption.series[0].itemStyle.color =
        pureToneAudChartOption.series[0].itemStyle._colorLeft;
      // 实测骨导
      pureToneAudChartOption.series[1].data = xAxisData.map((x) => {
        let data = this.boneConductionByActForLeft[x] || null;
        if (!data) return null;

        data.symbolSize = [30, 30]; // 标记大小[宽,高]
        data.symbolOffset = [0, 0]; // 标记偏移量[x,y]
        switch (data.type) {
          case 0: // 未掩蔽-有反应
            data.symbol = `image://${left_ear_2_1}`;
            break;
          case 1: // 未掩蔽-无反应
            data.symbol = `image://${left_ear_2_2}`;
            data.symbolOffset = [4, 5];
            break;
          case 2: // 加掩蔽-有反应
            data.symbol = `image://${left_ear_2_3}`;
            data.symbolOffset = [6, 0];
            break;
          case 3: // 加掩蔽-无反应
            data.symbol = `image://${left_ear_2_4}`;
            data.symbolOffset = [10, 5];
            break;
        }
        return data;
      });
      pureToneAudChartOption.series[1].itemStyle.color =
        pureToneAudChartOption.series[1].itemStyle._colorLeft;
      this.chartLeft.setOption(pureToneAudChartOption);
    },

    /**
     * @author: justin
     * @description: chart数据初始化
     * @return {*}
     */
    initChartData() {
      const that = this;
      const tempData = {
        value: undefined,
        type: undefined
      };
      // 动态输入框内容
      const formDynamicInpArr = [
        'airConductionByActForRight',
        'airConductionByActForLeft',
        'airConductionByCorrForRight',
        'airConductionByCorrForLeft',
        'boneConductionByActForRight',
        'boneConductionByActForLeft',
        'boneConductionByCorrForRight',
        'boneConductionByCorrForLeft'
      ];
      that.constHzArr.forEach((x) => {
        formDynamicInpArr.forEach((y) => {
          that.$set(that[y], x, dataUtils.deepCopy(tempData));
        });
      });
      that.resetInputHandle();
    },

    /**
     * @author: justin
     * @description: 重设图表大小
     * @return {*}
     */
    resizeChart() {
      if (this.chartRight) this.chartRight.resize();

      if (this.chartLeft) this.chartLeft.resize();
    },

    /**
     * @author: justin
     * @description: 重置输入内容事件，上下键盘等
     * @return {*}
     */
    resetInputHandle() {
      const that = this;
      let inputRef;
      that.$nextTick(() => {
        that.constHzArr.forEach((x, i) => {
          // 气导(重设上下键盘事件)
          inputRef = that.$refs[`airConductionByActForRight_${i}_ref`][0];
          inputRef.increase = () => {};
          inputRef.decrease = () => {};
          inputRef = that.$refs[`airConductionByActForLeft_${i}_ref`][0];
          inputRef.increase = () => {};
          inputRef.decrease = () => {};

          // 骨导导(重设上下键盘事件)
          inputRef = that.$refs[`boneConductionByActForRight_${i}_ref`][0];
          inputRef.increase = () => {};
          inputRef.decrease = () => {};
          inputRef = that.$refs[`boneConductionByActForLeft_${i}_ref`][0];
          inputRef.increase = () => {};
          inputRef.decrease = () => {};
        });

        that.getAuditoryResult();
      });
    },

    /**
     * @author: justin
     * @description: 获取气导\骨导数值表示类型
     * @param {*} index
     * @return {*}
     */
    getAuditoryResultType(index) {
      const arr = [0, 1, 2, 3];
      index = index < 0 ? 3 : index > 3 ? 0 : index; // 超出，使用滑动取值
      return arr[index];
    },

    /**
     * @author: justin
     * @description: 右耳-实测气导值变动
     * @param {*} type  value：值改变，keyup：键盘回弹事件
     * @param {*} valOrEvent 值或事件
     * @param {*} oldValue
     * @param {*} index
     * @return {*}
     */
    airConductionByActForRightChange(type, valOrEvent, oldValue, index) {
      const value = this.airConductionByActForRight[index].value;
      this.form[`rightGas${index}`] = value;
      if (!Number.isFinite(value)) return;

      let condType = this.airConductionByActForRight[index].type || 0;
      switch (type) {
        case 'keyup':
          switch (valOrEvent.keyCode) {
            case 38: // 上
              condType--;
              break;
            case 40: // 下
              condType++;
              break;
          }
          break;
      }

      this.isCalced = false;
      this.$set(
        this.airConductionByActForRight[index],
        'type',
        this.getAuditoryResultType(condType)
      );
      this.$set(
        this.airConductionByCorrForRight[index],
        'type',
        this.getAuditoryResultType(condType)
      );
      this.form[`rightGas${index}Type`] = condType;

      this.$nextTick(() => {
        this.initChartRight();
      });
    },

    /**
     * @author: justin
     * @description: 右耳-实测骨导值变动
     * @param {*} type  value：值改变，keyup：键盘回弹事件
     * @param {*} valOrEvent 值或事件
     * @param {*} oldValue
     * @param {*} index
     * @return {*}
     */
    boneConductionByActForRightChange(type, valOrEvent, oldValue, index) {
      const value = this.boneConductionByActForRight[index].value;
      this.form[`rightBone${index}`] = value;
      // if (!Number.isFinite(value)) return;

      let condType = this.boneConductionByActForRight[index].type || 0;
      switch (type) {
        case 'keyup':
          switch (valOrEvent.keyCode) {
            case 38: // 上
              condType--;
              break;
            case 40: // 下
              condType++;
              break;
          }
          break;
      }

      this.isCalced = false;
      this.$set(
        this.boneConductionByActForRight[index],
        'type',
        this.getAuditoryResultType(condType)
      );
      this.$set(
        this.boneConductionByCorrForRight[index],
        'type',
        this.getAuditoryResultType(condType)
      );
      this.form[`rightBone${index}Type`] = condType;

      this.$nextTick(() => {
        this.initChartRight();
      });
    },

    /**
     * @author: justin
     * @description: 右耳-实测气导值变动
     * @param {*} type  value：值改变，keyup：键盘回弹事件
     * @param {*} valOrEvent 值或事件
     * @param {*} oldValue
     * @param {*} index
     * @return {*}
     */
    airConductionByActForLeftChange(type, valOrEvent, oldValue, index) {
      const value = this.airConductionByActForLeft[index].value;
      this.form[`leftGas${index}`] = value;
      if (!Number.isFinite(value)) return;

      let condType = this.airConductionByActForLeft[index].type || 0;
      switch (type) {
        case 'keyup':
          switch (valOrEvent.keyCode) {
            case 38: // 上
              condType--;
              break;
            case 40: // 下
              condType++;
              break;
          }
          break;
      }

      this.isCalced = false;
      this.$set(
        this.airConductionByActForLeft[index],
        'type',
        this.getAuditoryResultType(condType)
      );
      this.$set(
        this.airConductionByCorrForLeft[index],
        'type',
        this.getAuditoryResultType(condType)
      );
      this.form[`leftGas${index}Type`] = condType;

      this.$nextTick(() => {
        this.initChartLeft();
      });
    },

    /**
     * @author: justin
     * @description: 右耳-实测骨导值变动
     * @param {*} type  value：值改变，keyup：键盘回弹事件
     * @param {*} valOrEvent 值或事件
     * @param {*} oldValue
     * @param {*} index
     * @return {*}
     */
    boneConductionByActForLeftChange(type, valOrEvent, oldValue, index) {
      const value = this.boneConductionByActForLeft[index].value;
      this.form[`leftBone${index}`] = value;
      // if (!Number.isFinite(value)) return;

      let condType = this.boneConductionByActForLeft[index].type || 0;
      switch (type) {
        case 'keyup':
          switch (valOrEvent.keyCode) {
            case 38: // 上
              condType--;
              break;
            case 40: // 下
              condType++;
              break;
          }
          break;
      }

      this.isCalced = false;
      this.$set(
        this.boneConductionByActForLeft[index],
        'type',
        this.getAuditoryResultType(condType)
      );
      this.$set(
        this.boneConductionByCorrForLeft[index],
        'type',
        this.getAuditoryResultType(condType)
      );
      this.form[`leftBone${index}Type`] = condType;

      this.$nextTick(() => {
        this.initChartLeft();
      });
    },

    /**
     * @author: justin
     * @description: 是否启用8000Hz选项
     * @return {*}
     */
    check8000HzChange() {
      if (!this.check8000Hz) {
        // 8000Hz选项被取消设置为null
        const hz8000 = this.constHzArr[this.constHzArr.length - 1];
        this.airConductionByActForRight[hz8000] = {
          value: undefined,
          type: undefined
        };
        this.airConductionByActForLeft[hz8000] = {
          value: undefined,
          type: undefined
        };
      }
    },

    /**
     * @author: justin
     * @description: 取消
     * @return {*}
     */
    cancel() {
      this.$emit('cancel');
    },

    /**
     * @author: justin
     * @description: 计算结果
     * @return {*}
     */
    calc() {
      this.$refs.form_ref.clearValidate();
      const data = this.initPostData(false);
      if (!data) return;

      this.loading = true;
      this.$ajax
        .post(this.$apiUrls.CalculateAuditoryResult, data)
        .then(async (res) => {
          let { success, returnData } = res.data;
          if (!success || !returnData) return;

          this.$message.success('计算成功！');
          delete returnData.examTime;
          delete returnData.doctorName;
          delete returnData.filePath;
          this.isCalced = true;
          await this.initFormData(returnData);
          this.generateExamConclusion();
          this.initRectifyChartLeft();
          this.initRectifyChartRight();
          this.$emit('calc', this.form);
        })
        .finally((_) => {
          this.loading = false;
        });
    },

    /**
     * @author: justin
     * @description: 确定
     * @return {*}
     */
    confirm() {
      const data = this.initPostData();
      data.combCode = this.combCode;
      if (!data) return;
      if (!this.isCalced) {
        this.$message.warning('请先点击【计算】按钮，再进行【保存】操作！');
        return;
      }
      this.loading = true;
      this.$ajax
        .post(this.$apiUrls.SaveAuditoryResult, data)
        .then((res) => {
          let { success, returnData } = res.data;
          if (!success) return;

          this.$message.success('保存成功！');
          Promise.all([
            this.mergeAndUploadChartImg(this.chartRight, this.chartLeft),
            this.mergeAndUploadChartImg(
              this.rectifyChartRight,
              this.rectifyChartLeft
            )
          ]).then((r) => {
            this.uploadImg(r);
          });
          this.$nextTick(() => {
            this.$emit('confirm', this.form);
            this.cancel();
          });
        })
        .finally((_) => {
          this.loading = false;
        });
    },

    /**
     * @author: justin
     * @description: 初始化与验证提交表单数据
     * @param {*} validateForm 是否验证表单内容
     * @return {*}
     */
    initPostData(validateForm = true) {
      const that = this;
      let valid = true;

      if (validateForm) {
        this.$refs.form_ref.validate((_valid) => {
          if (!_valid) valid = false;
        });
      }

      for (let index = 0; index < that.constHzArr.length; index++) {
        const x = that.constHzArr[index];
        // 实测气导
        that.form[`rightGas${x}`] = that.airConductionByActForRight[x]?.value;
        that.form[`rightGas${x}Type`] =
          that.airConductionByActForRight[x]?.type;
        that.form[`leftGas${x}`] = that.airConductionByActForLeft[x]?.value;
        that.form[`leftGas${x}Type`] = that.airConductionByActForLeft[x]?.type;

        // 校正气导
        that.form[`rightGasCorrected${x}`] =
          that.airConductionByCorrForRight[x]?.value;
        that.form[`leftGasCorrected${x}`] =
          that.airConductionByCorrForLeft[x]?.value;

        // 实测骨导
        that.form[`rightBone${x}`] = that.boneConductionByActForRight[x]?.value;
        that.form[`rightBone${x}Type`] =
          that.boneConductionByActForRight[x]?.type;
        that.form[`leftBone${x}`] = that.boneConductionByActForLeft[x]?.value;
        that.form[`leftBone${x}Type`] =
          that.boneConductionByActForLeft[x]?.type;

        // 校正骨导
        that.form[`rightBoneCorrected${x}`] =
          that.boneConductionByCorrForRight[x]?.value;
        that.form[`leftBoneCorrected${x}`] =
          that.boneConductionByCorrForLeft[x]?.value;

        // 验证
        if (
          index < that.constHzArr.length - 1 ||
          (that.form.is8000Hz && index === that.constHzArr.length - 1)
        ) {
          if (
            !Number.isFinite(that.form[`rightGas${x}`]) ||
            !Number.isFinite(that.form[`leftGas${x}`])
          ) {
            valid = false;
          }
        }
      }

      if (!valid) {
        this.$message.warning('请填写完整数据！');
        return null;
      }

      return that.form;
    },

    /**
     * @author: justin
     * @description: 获取
     * @return {*}
     */
    async getAuditoryResult() {
      if (!this.form.regNo) return;

      this.loadingMask = true;
      await this.$ajax
        .paramsPost(this.$apiUrls.ReadAuditoryResult, {
          regNo: this.form.regNo
        })
        .then((res) => {
          let { success, returnData } = res.data;
          if (!success || !returnData) return;

          this.isCalced = true;
          this.initFormData(returnData);
          this.initChart();
          this.$emit('data', this.form);
        })
        .finally((_) => {
          this.loadingMask = false;
        });
    },

    /**
     * @author: justin
     * @description: 初始化表单数据
     * @param {*} data
     * @return {*}
     */
    initFormData(data) {
      if (!data) return;

      const that = this;
      that.form = { ...that.form, ...data };

      that.constHzArr.forEach((x) => {
        // 实测气导
        that.airConductionByActForRight[x].value =
          that.form[`rightGas${x}`] ?? undefined;
        that.airConductionByActForRight[x].type = that.form[`rightGas${x}Type`];
        that.airConductionByActForLeft[x].value =
          that.form[`leftGas${x}`] ?? undefined;
        that.airConductionByActForLeft[x].type = that.form[`leftGas${x}Type`];

        // 校正气导
        that.airConductionByCorrForRight[x].value =
          that.form[`rightGasCorrected${x}`] ?? undefined;
        that.airConductionByCorrForRight[x].type =
          that.form[`rightGas${x}Type`];
        that.airConductionByCorrForLeft[x].value =
          that.form[`leftGasCorrected${x}`] ?? undefined;
        that.airConductionByCorrForLeft[x].type = that.form[`leftGas${x}Type`];

        // 实测骨导
        that.boneConductionByActForRight[x].value =
          that.form[`rightBone${x}`] ?? undefined;
        that.boneConductionByActForRight[x].type =
          that.form[`rightBone${x}Type`];
        that.boneConductionByActForLeft[x].value =
          that.form[`leftBone${x}`] ?? undefined;
        that.boneConductionByActForLeft[x].type = that.form[`leftBone${x}Type`];

        // 校正骨导
        that.boneConductionByCorrForRight[x].value =
          that.form[`rightBoneCorrected${x}`] ?? undefined;
        that.boneConductionByCorrForRight[x].type =
          that.form[`rightBone${x}Type`];
        that.boneConductionByCorrForLeft[x].value =
          that.form[`leftBoneCorrected${x}`] ?? undefined;
        that.boneConductionByCorrForLeft[x].type =
          that.form[`leftBone${x}Type`];
      });
    },

    /**
     * @author: justin
     * @description: 下载折线图并上传到服务器
     * @return {*}
     */
    mergeAndUploadChartImg(chartRight, chartLeft) {
      const that = this;
      return new Promise((resolve, reject) => {
        // 假设这是两个Base64编码的图片
        console.log(chartRight, chartLeft);

        const base64Image1 = chartRight.getDataURL({
          type: 'png',
          // 导出的图片分辨率比例，默认为 1。
          pixelRatio: 2,
          // 导出的图片背景色，默认使用 option 里的 backgroundColor
          backgroundColor: '#fff',
          // 忽略组件的列表，例如要忽略 toolbox 就是 ['toolbox']
          excludeComponents: ['toolbox']
        });
        const base64Image2 = chartLeft.getDataURL({
          type: 'png',
          // 导出的图片分辨率比例，默认为 1。
          pixelRatio: 2,
          // 导出的图片背景色，默认使用 option 里的 backgroundColor
          backgroundColor: '#fff',
          // 忽略组件的列表，例如要忽略 toolbox 就是 ['toolbox']
          excludeComponents: ['toolbox']
        });
        // 创建一个Canvas元素
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        // 加载第一个图片
        const img1 = new Image();
        img1.src = base64Image1;
        img1.onload = function () {
          // 设置Canvas的宽度和高度为第一个图片的宽度和高度
          canvas.width = img1.width;
          canvas.height = img1.height;
          // 绘制第一个图片到Canvas上
          ctx.drawImage(img1, 0, 0);

          // 加载第二个图片
          const img2 = new Image();
          img2.src = base64Image2;
          img2.onload = function () {
            // 设置Canvas的宽度为两个图片的宽度之和
            canvas.width = img1.width + img2.width;
            // 设置Canvas的高度为两个图片中较大的高度
            canvas.height = Math.max(img1.height, img2.height);
            // 重新绘制第一个图片到Canvas上
            ctx.drawImage(img1, 0, 0);
            // 绘制第二个图片到Canvas上，并排放置
            ctx.drawImage(img2, img1.width, 0); // 这里假设从第一个图片的右边开始绘制
            // 将合并后的图片转换为Base64编码
            const mergedBase64 = canvas.toDataURL('image/png');
            console.log();

            resolve(mergedBase64);
            // that.uploadImg(mergedBase64);
          };
        };
      });
    },

    /**
     * @author: justin
     * @description: 上传图片
     * @param {*} arr
     * @return {*}
     */
    uploadImg(arr) {
      if (!arr || arr.length === 0 || !this.form.regNo) return;

      this.loading = true;
      let formData = new FormData();
      formData.append(
        'files',
        dataUtils.base64ToFile(arr[0], `${this.form.regNo}_1.png`)
      );
      formData.append(
        'files',
        dataUtils.base64ToFile(arr[1], `${this.form.regNo}_2.png`)
      );
      this.$ajax
        .post(
          this.$apiUrls.UploadAuditoryPicture + `?regNo=${this.form.regNo}`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          }
        )
        .then((r) => {})
        .finally((_) => {
          formData = null;
          this.loading = false;
        });
    },

    /**
     * @author: justin
     * @description: 删除纯音测听数据
     * @return {*}
     */
    removeAudiometryRecord(showConfirm = true) {
      if (!this.form.regNo) return;

      const saveFunc = () => {
        this.loading = true;
        this.$ajax
          .paramsPost(this.$apiUrls.DeleteAuditoryResult, {
            regNo: this.form.regNo
          })
          .then((res) => {
            let { success } = res.data;
            if (!success) return;

            this.initFormData(this._form);
            this.form.is8000Hz = false;
            this.initChart();
            this.$nextTick(() => {
              this.$refs.form_ref.clearValidate();
            });

            this.$emit('removed');
          })
          .finally((_) => {
            this.loading = false;
          });
      };

      if (showConfirm) {
        this.$confirm('确定要删除纯音测听记录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          saveFunc();
        });
      } else {
        saveFunc();
      }
    },
    // 初始化校正右耳图
    initRectifyChartRight() {
      if (!this.rectifyChartRight) {
        this.rectifyChartRight = echarts.init(
          document.getElementById('rectify-chart-right')
        );
      }
      pureToneAudChartOption.title.text = `{titleIcon|} 右耳校正听力`;
      pureToneAudChartOption.toolbox.feature.saveAsImage.name =
        '右耳校正听力图表';
      // 校正气导
      pureToneAudChartOption.series[0].data = xAxisData.map((x) => {
        let data = this.airConductionByCorrForRight[x] || null;
        console.log(data);
        if (!data) return null;

        data.symbolSize = [30, 30]; // 标记大小[宽,高]
        data.symbolOffset = [0, 0]; // 标记偏移量[x,y]
        switch (data.type) {
          case 0: // 未掩蔽-有反应
            data.symbol = `image://${right_ear_1_1}`;
            break;
          case 1: // 加掩蔽-有反应
            data.symbol = `image://${right_ear_1_2}`;
            data.symbolOffset = [-3, 3];
            break;
          case 2: // 加掩蔽-有反应
            data.symbol = `image://${right_ear_1_3}`;
            data.symbolOffset = [0, -1.5];
            break;
          case 3: // 加掩蔽-无反应
            data.symbol = `image://${right_ear_1_4}`;
            data.symbolOffset = [-4, 3];
            break;
        }
        return data;
      });
      pureToneAudChartOption.series[0].itemStyle.color =
        pureToneAudChartOption.series[0].itemStyle._colorRight;
      // 校正骨导
      pureToneAudChartOption.series[1].data = xAxisData.map((x) => {
        let data = this.boneConductionByCorrForRight[x] || null;
        if (!data) return null;

        data.symbolSize = [30, 30]; // 标记大小[宽,高]
        data.symbolOffset = [0, 0]; // 标记偏移量[x,y]
        switch (data.type) {
          case 0: // 未掩蔽-有反应
            data.symbol = `image://${right_ear_2_1}`;
            break;
          case 1: // 未掩蔽-无反应
            data.symbol = `image://${right_ear_2_2}`;
            data.symbolOffset = [-4, 5];
            break;
          case 2: // 加掩蔽-有反应
            data.symbol = `image://${right_ear_2_3}`;
            data.symbolOffset = [-6, 0];
            break;
          case 3: // 加掩蔽-无反应
            data.symbol = `image://${right_ear_2_4}`;
            data.symbolOffset = [-10, 5];
            break;
        }
        return data;
      });
      pureToneAudChartOption.series[1].itemStyle.color =
        pureToneAudChartOption.series[1].itemStyle._colorRight;
      this.rectifyChartRight.setOption(pureToneAudChartOption);
    },

    /**
     * @author: justin
     * @description: 初始化校正左耳图表
     * @return {*}
     */
    initRectifyChartLeft() {
      if (!this.rectifyChartLeft) {
        this.rectifyChartLeft = echarts.init(
          document.getElementById('rectify-chart-left')
        );
      }
      pureToneAudChartOption.title.text = `{titleIcon|} 左耳校正听力`;
      pureToneAudChartOption.toolbox.feature.saveAsImage.name =
        '左耳校正听力图表';
      // 校正气导
      pureToneAudChartOption.series[0].data = xAxisData.map((x) => {
        let data = this.airConductionByCorrForLeft[x] || null;
        if (!data) return null;

        data.symbolSize = [30, 30]; // 标记大小[宽,高]
        data.symbolOffset = [0, 0]; // 标记偏移量[x,y]
        console.log(data.type);

        switch (data.type) {
          case 0: // 未掩蔽-有反应
            data.symbol = `image://${left_ear_1_1}`;
            break;
          case 1: // 未掩蔽-无反应
            data.symbol = `image://${left_ear_1_2}`;
            data.symbolOffset = [3, 3];
            break;
          case 2: // 加掩蔽-有反应
            data.symbol = `image://${left_ear_1_3}`;
            break;
          case 3: // 加掩蔽-无反应
            data.symbol = `image://${left_ear_1_4}`;
            data.symbolOffset = [4, 4];
            break;
        }
        return data;
      });
      pureToneAudChartOption.series[0].itemStyle.color =
        pureToneAudChartOption.series[0].itemStyle._colorLeft;
      // 校正骨导
      pureToneAudChartOption.series[1].data = xAxisData.map((x) => {
        let data = this.boneConductionByCorrForLeft[x] || null;
        if (!data) return null;

        data.symbolSize = [30, 30]; // 标记大小[宽,高]
        data.symbolOffset = [0, 0]; // 标记偏移量[x,y]
        switch (data.type) {
          case 0: // 未掩蔽-有反应
            data.symbol = `image://${left_ear_2_1}`;
            break;
          case 1: // 未掩蔽-无反应
            data.symbol = `image://${left_ear_2_2}`;
            data.symbolOffset = [4, 5];
            break;
          case 2: // 加掩蔽-有反应
            data.symbol = `image://${left_ear_2_3}`;
            data.symbolOffset = [6, 0];
            break;
          case 3: // 加掩蔽-无反应
            data.symbol = `image://${left_ear_2_4}`;
            data.symbolOffset = [10, 5];
            break;
        }
        return data;
      });
      pureToneAudChartOption.series[1].itemStyle.color =
        pureToneAudChartOption.series[1].itemStyle._colorLeft;
      this.rectifyChartLeft.setOption(pureToneAudChartOption);
    },
    // 切换图例
    switchLegeng() {
      this.rectifyShow = !this.rectifyShow;
      if (!this.rectifyShow) return;
      this.$nextTick(() => {
        this.initRectifyChartLeft();
        this.initRectifyChartRight();
      });
    },
    // 回车聚焦下一个输入框
    enterFocusNext(idx) {
      if (
        (!this.form.is8000Hz && idx == 26) ||
        (this.form.is8000Hz && idx == 27)
      )
        return;
      let index = idx + 1;
      let beforeArr = [5, 12];
      if ((!this.form.is8000Hz && beforeArr.includes(idx)) || idx == 19) {
        index = idx + 2;
      }
      let dom = document
        .getElementById(`inp_${index}`)
        .getElementsByTagName('input')[0];
      console.log(dom);

      dom.focus();
    },
    // 医生名字改变的回调
    doctorNameChange() {
      this.$parent.$parent.recordComb.doctorName = this.form.doctorName;
    },
    // 体检日期改变的回调
    examTimeChange() {
      this.$parent.$parent.recordComb.examTime = this.form.examTime;
    }
  },
  watch: {
    'form.rightGas8000': {
      handler(newVal, oldValue) {
        if (Number.isFinite(newVal)) {
          this.form.is8000Hz = true; // 8000Hz选项被选中
        }
      }
    },
    'form.leftGas8000': {
      handler(newVal, oldValue) {
        if (Number.isFinite(newVal)) {
          this.form.is8000Hz = true; // 8000Hz选项被选中
        }
      }
    },
    inputInfo: {
      handler(n, o) {
        this.form.doctorName = n.doctorName;
        this.form.examTime = n.examTime;
      },
      deep: true,
      immediate: true
    }
  }
};

// x轴数据
const xAxisObj = {
  125: 'c-',
  250: 'c-1',
  500: 'c-2',
  750: 'fis-2',
  1000: 'c-3',
  1500: 'fis-3',
  2000: 'c-4',
  3000: 'fis-4',
  4000: 'c-5',
  6000: 'fis-5',
  8000: 'c-6',
  12000: 'fis-6'
};
const xAxisData = Object.keys(xAxisObj).map(Number);
const xAxisDataRemarkArr = Object.values(xAxisObj).map((x, i) => {
  const arr = x.split('-');
  if (arr[0].startsWith('c')) return `{sup1|${arr[1]}}\n{note|${arr[0]}}`;
  else return `{sup2|${arr[1]}}\n{note|${arr[0]}}`;
});

// echart 配置项
let pureToneAudChartOption = {
  title: {
    // 标题
    text: `{titleIcon|} 右耳`,
    left: 'center',
    textStyle: {
      rich: {
        // titleIcon: {
        //   // 标题图标
        //   height: 20,
        //   width: 3,
        //   backgroundColor: "#14A16D",
        //   borderRadius: 3,
        // },
      }
    }
  },
  toolbox: {
    // 工具箱
    show: true,
    orient: 'horizontal',
    right: '60px',
    bottom: '-5px',
    feature: {
      saveAsImage: {
        show: true,
        name: '右耳图表',
        pixelRatio: 2,
        backgroundColor: '#fff'
      }, // 保存图表
      dataView: { show: true, disabled: true }, // 数据视图
      dataZoom: { show: true } // 数据缩放
    }
  },
  tooltip: {
    // 提示框
    trigger: 'axis'
  },
  legend: {
    // 图例
    data: ['气导', '骨导'],
    bottom: 0 // 图例说放在底部
  },
  grid: {
    // 网格
    top: '20%',
    bottom: '10%'
  },
  xAxis: [
    // X轴
    {
      type: 'category',
      name: '(Hz)',
      data: xAxisData,
      position: 'top', // X轴在上方
      boundaryGap: false, // 两边留白
      axisLabel: {
        // X轴标签
        interval: 0,
        formatter: function (value, index) {
          return `${xAxisDataRemarkArr[index]}\n${(value + '').padEnd(5, ' ')}`;
        },
        padding: [0, 0, 3, 0],
        rich: {
          sup1: {
            align: 'right',
            verticalAlign: 'bottom',
            color: '#000',
            fontSize: 10,
            padding: [0, 2, -10, 0]
          },
          sup2: {
            align: 'right',
            verticalAlign: 'bottom',
            color: '#000',
            fontSize: 10,
            padding: [0, -1, -10, 0]
          },
          note: {
            align: 'center',
            color: '#000',
            fontSize: 16,
            padding: [0, 0, 5, 0]
          }
        }
      },
      axisLine: {
        onZero: false, // 坐标轴刻度不从0开始
        lineStyle: {
          type: 'solid', // 设置为实线
          color: '#000', // 设置线的颜色
          width: 1 // 设置线的宽度
        }
      },
      splitLine: {
        // X轴分割线
        lineStyle: {
          type: 'dashed' // 内容区域为虚线
        }
      },
      axisTick: {
        // 刻度线
        show: false // 隐藏刻度线
      }
    },
    {
      type: 'category',
      data: xAxisData,
      position: 'bottom', // X轴在下方
      boundaryGap: false, // 两边留白
      axisLabel: {
        show: false, // 隐藏右侧X轴的值内容
        interval: 0
      },
      axisLine: {
        onZero: false, // 坐标轴刻度不从0开始
        lineStyle: {
          type: 'solid', // 设置为实线
          color: '#000', // 设置线的颜色
          width: 1 // 设置线的宽度
        }
      },
      splitLine: {
        // X轴分割线
        show: true, // 显示X轴竖线
        lineStyle: {
          type: 'dashed' // 内容区域为虚线
        }
      },
      axisTick: {
        show: false // 隐藏刻度线
      }
    }
  ],
  yAxis: [
    // Y轴
    {
      type: 'value',
      name: '(dBHL)',
      min: -10,
      max: 120,
      interval: 10, // 间隔10
      position: 'left',
      inverse: true, // Y轴内容倒序
      boundaryGap: false, // 两边留白
      axisLabel: {
        formatter: '{value}',
        padding: [0, 5, 0, 0]
      },
      axisLine: {
        // Y轴线
        show: true, // 显示Y轴线
        onZero: false, // 坐标轴刻度不从0开始
        lineStyle: {
          type: 'solid', // 设置为实线
          color: '#000', // 设置线的颜色
          width: 1 // 设置线的宽度
        }
      },
      splitLine: {
        // Y轴分割线
        show: true, // 显示Y轴横线
        lineStyle: {
          type: 'dashed' // 内容区域为虚线
        }
      },
      axisTick: {
        show: false // 隐藏刻度线
      }
    },
    {
      type: 'value',
      min: -10,
      max: 120,
      interval: 10, // 间隔10
      position: 'right',
      inverse: true, // Y轴内容倒序
      boundaryGap: false, // 两边留白
      axisLabel: {
        show: false // 隐藏右侧Y轴的值内容
      },
      axisLine: {
        // Y轴线
        show: true, // 显示Y轴线
        onZero: false, // 坐标轴刻度不从0开始
        lineStyle: {
          type: 'solid', // 设置为实线
          color: '#000', // 设置线的颜色
          width: 1 // 设置线的宽度
        }
      },
      splitLine: {
        // Y轴分割线
        show: true, // 显示Y轴横线
        lineStyle: {
          type: 'dashed' // 内容区域为虚线
        }
      },
      axisTick: {
        show: false // 隐藏刻度线
      }
    }
  ],
  series: [
    // 图表数据
    {
      name: '气导',
      type: 'line', //line折线图、 scatter散点图
      data: [],
      connectNulls: true, // 连接空值，要连续连线
      yAxisIndex: 0,
      itemStyle: {
        color: '#D63031', // 设置线、图标颜色
        _colorRight: '#D63031', // 备用颜色
        _colorLeft: '#D63031' // 备用颜色
      },
      symbol: function (value, params) {
        // 自定义图标
        return params.data?.symbol || 'none';
      },
      symbolSize: function (value, params) {
        // 自定义图标大小：[宽,高]
        return params.data?.symbolSize || [30, 30];
      },
      symbolOffset: function (value, params) {
        // 自定义图标偏移量
        return params.data?.symbolOffset || [0, 0];
      }
    },
    {
      name: '骨导',
      type: 'scatter',
      data: [],
      connectNulls: true, // 连接空值，要连续连线
      yAxisIndex: 1,
      itemStyle: {
        color: '#1770DF', // 设置线、图标颜色
        _colorRight: '#1770DF', // 备用颜色
        _colorLeft: '#1770DF' // 备用颜色
      },
      symbol: function (value, params) {
        // 自定义图标
        return params.data?.symbol || 'none';
      },
      symbolSize: function (value, params) {
        // 自定义图标大小：[宽,高]
        return params.data?.symbolSize || [30, 30];
      },
      symbolOffset: function (value, params) {
        // 自定义图标偏移量
        return params.data?.symbolOffset || [0, 0];
      }
    }
  ]
};
</script>

<style lang="less" scoped>
.pure-ton-audiometry-container {
  width: 100%;
  height: 100%;

  .header-wrapper {
    .info-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px;
      .patient-info-box {
        margin-bottom: 5px;

        label {
          margin-right: 20px;
          font-size: 1rem;
          color: #e06464;
        }
      }
    }
  }

  .body-wrapper {
    .chart-box {
      margin-bottom: 15px;
    }

    .form-box {
      label {
        color: #1d1d1d;
      }

      .x-label-box {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 10.5% 10px 16.5%;
      }

      .input-item {
        display: flex;
        align-items: baseline;
        label {
          width: 15%;
        }

        .input-box {
          flex: 1;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 10% 10px 0;
          /deep/.el-input-number {
            width: 12%;
          }
        }
      }

      .speech-frequency-box {
        display: flex;
        justify-content: start;
        align-items: center;
        margin-bottom: 10px;

        .item-box {
          display: flex;
          justify-content: start;
          align-items: center;
          margin-right: 10px;

          /deep/.el-input-number {
            width: 4rem;
          }
        }
      }

      .hearing-threshold-box {
        display: flex;
        justify-content: start;
        align-items: center;
        margin-bottom: 10px;

        .item-box {
          display: flex;
          justify-content: start;
          align-items: center;
          margin-right: 10px;

          /deep/.el-input-number {
            width: 4rem;
          }
        }
      }

      .conclusion-box {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        padding-right: 5% !important;
      }
    }

    /deep/.el-form-item {
      margin-bottom: 0 !important;
      flex: 1;

      .el-form-item__content {
        width: 100%;
      }
    }

    .invalid-error {
      /deep/ .el-input__inner {
        border-color: #f56c6c;
      }
    }
  }

  .footer-wrapper {
    .btn-box {
      display: flex;
      justify-content: end;
      align-items: center;
      padding-right: 5% !important;
    }
  }
}
</style>
