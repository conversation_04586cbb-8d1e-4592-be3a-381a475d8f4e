<!--
 * @FilePath: \shenshan\KrPeis\src\pages\inspectIn\components\smokingHistory.vue
 * @Description:  吸烟史内容
 * @Author: justin
 * @Date: 2024-07-05 08:48:36
 * @Version: 0.0.1
 * @LastEditors: key
 * @LastEditTime: 2024-09-13 13:43:15
*
-->
<template>
  <!-- 吸烟史组件 -->
  <div class="smoking-history-container" v-loading="loadingMask">
    <el-form
      ref="form_ref"
      :inline="true"
      :model="form"
      :show-message="false"
      class="form"
      style="padding: 0 10px"
    >
      <el-radio-group
        v-model="form.smokingStatus"
        @change="smokingStatusChange"
        style="width: 100%"
      >
        <el-row align="center" :gutter="20" class="row-smoked-box">
          <el-row class="left-box">
            <p v-for="item in G_smokingStatus" :key="item.value">
              <el-radio
                v-if="item.value <= smokedMaxStatus"
                :label="Number(item.value)"
                :class="form.smokingStatus ? '' : 'is-error'"
              >
                <label>{{ item.label }}</label>
              </el-radio>
            </p>
          </el-row>

          <el-row
            class="right-box"
            v-if="form.smokingStatus <= smokedMaxStatus"
          >
            <el-form-item
              :required="form.smokingStatus <= smokedMaxStatus"
              prop="smokingCountPerDays"
            >
              <label>平均每日</label>
              <el-input-number
                v-model="form.smokingCountPerDays"
                :controls="false"
                :step="1"
                :min="1"
                :max="99"
                :precision="0"
                size="mini"
                placeholder="几"
              ></el-input-number>
              <label>支</label>
            </el-form-item>

            <el-form-item
              :required="form.smokingStatus <= smokedMaxStatus"
              prop="smokingYears"
            >
              <label>，约</label>
              <el-input-number
                v-model="form.smokingYears"
                :controls="false"
                :step="1"
                :min="0"
                :max="150"
                :precision="0"
                size="mini"
                placeholder="几"
              ></el-input-number>
              <label>年</label>
            </el-form-item>

            <el-form-item
              :required="form.smokingStatus <= smokedMaxStatus"
              prop="smokingMonths"
            >
              <el-input-number
                v-model="form.smokingMonths"
                :controls="false"
                :step="1"
                :min="0"
                :max="1500"
                :precision="0"
                size="mini"
                placeholder="几"
              ></el-input-number>
              <label>月</label>
            </el-form-item>
          </el-row>
        </el-row>

        <el-row
          align="center"
          :gutter="10"
          justify="space-between"
          type="flex"
          class="row-not-smoking-box"
        >
          <el-col :span="24">
            <p v-for="item in G_smokingStatus" :key="item.value">
              <el-radio
                v-if="item.value > smokedMaxStatus"
                :label="Number(item.value)"
                :class="form.smokingStatus ? '' : 'is-error'"
              >
                <label>{{ item.label }}</label>
              </el-radio>
            </p>
          </el-col>
        </el-row>

        <el-row
          align="center"
          :gutter="10"
          justify="space-between"
          type="flex"
          class="footer-box"
        >
          <el-col :span="24" class="btn-box">
            <el-button
              type="default"
              size="mini"
              @click="cancel"
              :loading="loading"
              >关闭</el-button
            >
            <el-button
              type="primary"
              size="mini"
              @click="confirm"
              :loading="loading"
              >保存</el-button
            >
          </el-col>
        </el-row>
      </el-radio-group>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  name: 'smokingHistory',
  props: {
    regNo: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      smokedMaxStatus: 3, // 已吸烟最大区间状态
      form: {
        regNo: this.regNo,
        smokingStatus: -1,
        smokingYears: undefined,
        smokingMonths: undefined,
        smokingCountPerDays: undefined
      },
      loadingMask: false, // 内容loading
      loading: false // 按钮loading
    };
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_smokingStatus'])
  },
  methods: {
    /**
     * @author: justin
     * @description: 获取吸烟史记录
     * @return {*}
     */
    async getSmokingHistory() {
      if (!this.form.regNo) return;

      this.loadingMask = true;
      await this.$ajax
        .paramsPost(this.$apiUrls.ReadSmokingHistory, {
          regNo: this.form.regNo
        })
        .then((res) => {
          const { success, returnData } = res.data;
          if (!success) return;

          this.form = { ...this.form, ...returnData };
          this.$emit('data', this.form);
        })
        .finally((_) => {
          this.loadingMask = false;
        });
    },

    /**
     * @author: justin
     * @description: 吸烟状态改变
     * @return {*}
     */
    smokingStatusChange() {
      if (this.form.smokingStatus > this.smokedMaxStatus) {
        this.form.smokingYears = undefined;
        this.form.smokingMonths = undefined;
        this.form.smokingCountPerDays = undefined;
      }
    },

    /**
     * @author: justin
     * @description: 取消
     * @return {*}
     */
    cancel() {
      this.$emit('cancel');
    },

    /**
     * @author: justin
     * @description: 确认
     * @return {*}
     */
    confirm() {
      if (!this.form.regNo) return;

      if (this.form.smokingStatus <= 0) {
        this.form.smokingStatus = 0;
        return;
      }

      this.$refs.form_ref.validate((valid) => {
        if (valid) {
          if (this.form.smokingYears <= 0 && this.form.smokingMonths <= 0) {
            this.$message.warning('请正确填写吸烟年数或月数！');
            return;
          }
          this.loading = true;
          this.$ajax
            .post(this.$apiUrls.SaveSmokingHistory, this.form)
            .then((res) => {
              const { success } = res.data;
              if (!success) return;

              this.$message.success('保存成功！');
              this.$emit('confirm', this.form);
              this.cancel();
            })
            .finally((_) => {
              this.loading = false;
            });
        }
      });
    },

    /**
     * @author: justin
     * @description: 删除吸烟史记录
     * @return {*}
     */
    removeSmokingHistory(showConfirm = true) {
      const saveFunc = () => {
        this.loading = true;
        this.$ajax
          .post(this.$apiUrls.DeleteSmokingHistory, {
            regNo: this.form.regNo
          })
          .then((res) => {
            const { success } = res.data;
            if (!success) return;

            this.$refs.form_ref.resetFields();
            this.form.smokedMaxStatus = -1;
            this.$emit('remove');
          })
          .finally((_) => {
            this.loading = false;
          });
      };

      if (showConfirm) {
        this.$confirm('确定要删除吸烟史记录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          saveFunc();
        });
      } else {
        saveFunc();
      }
    }
  },
  watch: {
    regNo: {
      handler(newVal, oldVal) {
        this.form.regNo = newVal;
        this.getSmokingHistory();
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
.smoking-history-container {
  .form {
    /deep/.el-form-item__content {
      width: 100%;
    }

    .row-smoked-box {
      margin-bottom: 20px;
      border-bottom: 1px solid #d2d2d2ee;
      margin-left: 0px !important;
      .left-box {
        p {
          margin-bottom: 10px;
        }
      }

      .right-box {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        /deep/ .el-form-item {
          margin-right: 0 !important;
          margin-bottom: 8px;
        }

        /deep/ .el-input-number {
          width: 4rem;
        }

        /deep/ .el-input__inner {
          padding: 0 0 !important;
          border: unset;
          border-bottom: 1px solid #a3a4ab;
          border-bottom-left-radius: 0;
          border-bottom-right-radius: 0;
        }

        /deep/ .el-form-item.is-error .el-input__inner {
          border-bottom: 1px solid #f56c6c;
        }
      }
    }

    .row-not-smoking-box {
      margin-bottom: 10px;
    }

    label {
      color: #000;
    }

    /deep/.el-radio__inner {
      border-radius: 2px;
    }

    /deep/.el-radio__input.is-checked .el-radio__inner::after {
      content: '';
      width: 8px;
      height: 3px;
      border: 1px solid white;
      border-top: transparent;
      border-right: transparent;
      text-align: center;
      display: block;
      position: absolute;
      top: 3px;
      left: 2px;
      transform: rotate(-45deg);
      border-radius: 0px;
      background: none;
    }

    /deep/ .el-radio.is-error .el-radio__inner {
      border: 1px solid #f56c6c !important;
    }

    .footer-box {
      .btn-box {
        display: flex;
        justify-content: flex-end;
        align-items: center;
      }
    }
  }
}
</style>
