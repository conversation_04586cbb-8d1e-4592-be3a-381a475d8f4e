<!--
 * @FilePath: \sansui\KrPeis\src\pages\inspectIn\components\resultEntry\receiveReportResult.vue
 * @Description: 接收结果
 * @Author: justin
 * @Date: 2024-05-20 15:12:00
 * @Version: 0.0.1
 * @LastEditors: key
 * @LastEditTime: 2025-03-03 16:46:43
*
-->
<template>
  <div class="receive-report-result-container">
    <el-dialog
      :visible.sync="dialogVisible"
      title="接收结果"
      width="70%"
      top="10vh"
      :close-on-click-modal="false"
      @closed="closedDialog"
      @opened="getReportRecieveLog"
      class="receive-report-result-dialog"
    >
      <div class="receiveResult_content">
        <header>
          <el-radio-group
            @input="beforeInputEvent"
            :value="resultRadioVal"
            :disabled="loading"
          >
            <el-radio label="Lis">检验结果</el-radio>
            <el-radio label="Exam">非检验结果</el-radio>
            <el-radio label="ExamExtHisApplyComb">检查申请单状态</el-radio>
          </el-radio-group>
          <div>
            <el-input
              size="small"
              clearable
              v-model="resultSearchVal"
              :placeholder="
                '请输入' +
                (applyTypeList.includes(resultRadioVal)
                  ? '报告名称'
                  : '组合代码、名称') +
                '搜索'
              "
            ></el-input>
          </div>
        </header>

        <div class="result_list">
          <template v-if="applyTypeList.includes(resultRadioVal)">
            <PublicTable
              ref="publicTable"
              :theads="resultTheads"
              :url="$apiUrls.GetReportRecieveLog"
              method="paramsPost"
              :params="{
                regNo: regNo
              }"
              rowKey="requestNumber"
              :tableDataFilter="filterTableDataCallback"
              @request-finally="
                () => {
                  loading = false;
                }
              "
              :elStyle="elStyle"
            >
              <template #success="{ scope }">
                {{ scope.row.success ? '接收成功' : '接收失败' }}
              </template>

              <template #reportXML="{ scope }">
                <el-popover placement="left" width="600" trigger="click">
                  <span slot="reference">{{ scope.row.reportXML }}</span>
                </el-popover>
              </template>

              <template #operation="{ scope }">
                <el-link
                  type="primary"
                  @click="viewReportXML(scope.row)"
                  style="margin-right: 10px"
                  >报文</el-link
                >
                <el-button
                  type="primary"
                  plain
                  size="mini"
                  class="view"
                  :loading="scope.row.loading"
                  @click="confirmResult(scope.row)"
                  >重新接收</el-button
                >
              </template>
            </PublicTable>
          </template>

          <template v-else>
            <PublicTable
              ref="publicTable"
              :theads="[
                {
                  prop: 'combCode',
                  label: '组合代码',
                  align: '',
                  width: '250px',
                  sortable: false
                },
                {
                  prop: 'combName',
                  label: '组合名称',
                  align: '',
                  width: '',
                  sortable: false
                },
                {
                  prop: 'applyStatusName',
                  label: '状态',
                  align: '',
                  width: '120px',
                  sortable: false
                },
                {
                  prop: 'operation',
                  label: '操作',
                  align: '',
                  width: 150,
                  sortable: false
                }
              ]"
              :url="$apiUrls.GetExtHisApplyCombs"
              method="post"
              :params="{
                regNo: regNo,
                applyType: 'Exam'
              }"
              rowKey="regCombId"
              :tableDataFilter="filterTableDataCallback"
              @request-finally="
                () => {
                  loading = false;
                }
              "
              :elStyle="elStyle"
            >
              <template #operation="{ scope }">
                <el-popconfirm
                  v-if="scope.row.applyStatus !== '0'"
                  title="确定要重置该申请单的状态吗？"
                  confirm-button-text="确认"
                  cancel-button-text="不 了"
                  popper-class="receive-report-result-popconfirm"
                  @confirm="resetApplyStatus(scope.row)"
                >
                  <el-button
                    slot="reference"
                    type="primary"
                    plain
                    size="mini"
                    class="view"
                    :loading="scope.row.loading"
                    >重置状态</el-button
                  >
                </el-popconfirm>
              </template>
            </PublicTable>
          </template>
        </div>
      </div>
    </el-dialog>

    <!--  接收报文弹窗 -->
    <el-drawer
      title="接收报文"
      :visible.sync="showReportXMLDrawer"
      direction="rtl"
      :with-header="false"
      size="40%"
    >
      <MonacoEditor
        :key="currentRow.requestNumber"
        :showToolbar="false"
        :options="{
          language: 'xml',
          readOnly: true
        }"
        :code="currentRow.reportXML"
      />
    </el-drawer>
  </div>
</template>

<script>
import PublicTable from '@/components/publicTable2.vue';
import { dataUtils } from '@/common';
import MonacoEditor from '@/components/monacoEditor.vue';

export default {
  name: 'receiveReportResult',
  components: {
    PublicTable,
    MonacoEditor
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    regNo: {
      type: String,
      require: true
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      resultRadioVal: 'Lis',
      resultSearchVal: '',
      resultTheads: [
        {
          prop: 'requestNumber',
          label: '申请单号',
          align: '',
          width: 120,
          sortable: false
        },
        {
          prop: 'reportName',
          label: '报告名称',
          align: '',
          width: '',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'success',
          label: '接收标识',
          align: '',
          width: 80,
          sortable: false
        },
        {
          prop: 'createTime',
          label: '首次接收时间',
          align: '',
          width: 155,
          sortable: false
        },
        {
          prop: 'lastTime',
          label: '最后接收时间',
          align: '',
          width: 155,
          sortable: false
        },
        {
          prop: 'errMsg',
          label: '错误消息',
          align: '',
          width: 120,
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'operation',
          label: '操作',
          align: 'center',
          width: 200,
          sortable: false
        }
      ],
      applyTypeList: ['Lis', 'Exam'],
      loading: false,
      elStyle: {
        'show-selection': false
      },
      showReportXMLDrawer: false,
      currentRow: {}
    };
  },
  methods: {
    /**
     * @author: justin
     * @description: 获取接收结果日志列表
     * @return {*}
     */
    getReportRecieveLog() {
      const that = this;
      that.loading = true;
      that.$refs.publicTable.loadData();
    },

    /**
     * @author: justin
     * @description: 获取检查申请单组合列表
     * @return {*}
     */
    getExamExtHisApplyCombList() {
      const that = this;
      that.loading = true;
      that.$refs.publicTable.loadData();
    },

    /**
     * @author: justin
     * @description: 数据过滤回调
     * @param {*} data
     * @return {*}
     */
    filterTableDataCallback(data) {
      if (!data || data.length == 0) return [];

      const that = this;
      const keyword = (that.resultSearchVal || '').trim().toLowerCase();
      switch (that.resultRadioVal) {
        case 'Lis':
          return data[0].logs.filter((x) =>
            x.reportName.toLowerCase().includes(keyword)
          );

        case 'Exam':
          return data[1].logs.filter((x) =>
            x.reportName.toLowerCase().includes(keyword)
          );

        case 'ExamExtHisApplyComb':
          return data.filter(
            (x) =>
              x.combCode.toLowerCase().includes(keyword) ||
              x.combName.toLowerCase().includes(keyword)
          );

        default:
          return [];
      }
    },

    /**
     * @author: justin
     * @description: 手动改变radio值
     * @param {*} value
     * @return {*}
     */
    beforeInputEvent(value) {
      let that = this;
      that.resultSearchVal = '';
      if (
        that.applyTypeList.includes(that.resultRadioVal) &&
        that.applyTypeList.includes(value)
      ) {
      } else if (value === 'ExamExtHisApplyComb') {
        that.getExamExtHisApplyCombList();
      } else {
        that.getReportRecieveLog();
      }
      that.resultRadioVal = value;
    },

    /**
     * @author: justin
     * @description: 重新接收结果
     * @param {*} row
     * @return {*}
     */
    confirmResult(row) {
      let datas = {
        type: this.resultRadioVal,
        requestNumber: row.requestNumber
      };
      this.$set(row, 'loading', true);
      this.$ajax
        .paramsPost(this.$apiUrls.RecieveExtReport, datas)
        .then((res) => {
          let { success } = res.data;
          if (!success) return;
          this.$message.success('接收成功！');
          this.getReportRecieveLog();
        })
        .finally((_) => {
          this.$set(row, 'loading', false);
        });
    },

    /**
     * @author: justin
     * @description: 重置检查申请单状态
     * @param {*} row
     * @return {*}
     */
    resetApplyStatus(row) {
      let rowCopy = dataUtils.deepCopy(row);
      this.$set(row, 'loading', true);
      rowCopy.applyStatus = '0';
      this.$ajax
        .post(this.$apiUrls.BatchModifyExtHisApplyComb, [rowCopy])
        .then((res) => {
          let { success } = res.data;
          if (!success) return;
          this.$message.success('重置成功！');
          this.getExamExtHisApplyCombList();
        })
        .finally((_) => {
          this.$set(row, 'loading', false);
        });
    },

    /**
     * @author: justin
     * @description: 关闭弹窗回调
     * @return {*}
     */
    closedDialog() {
      this.$emit('closed');
    },

    /**
     * <AUTHOR> justin
     * @description  : 查看接收报文XML
     * @param         {*} row:
     * @return        {*}
     */
    viewReportXML(row) {
      this.showReportXMLDrawer = true;
      this.currentRow = row;
    }
  },
  watch: {
    visible() {
      this.dialogVisible = this.visible;
      this.resultRadioVal = 'Lis';
      if (!this.visible) this.$refs.publicTable.resetData();
    }
  }
};
</script>

<style lang="less">
.receive-report-result-popconfirm {
  .el-popconfirm__action {
    margin-top: 10px;
    .el-button--text {
      background: unset !important;
      color: #079c66 !important;
    }
  }
}

.receive-report-result-dialog {
  .el-dialog__body {
    height: 75vh !important;
    overflow: auto;

    .receiveResult_content {
      display: flex;
      flex-flow: column;
      flex: 1;

      header {
        line-height: 35px;
      }

      .result_list {
        flex: 1;
        display: flex;
        height: 100% !important;
      }
    }
  }
}
</style>
