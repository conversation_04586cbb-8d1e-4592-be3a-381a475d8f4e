<template>
  <div class="uploadImgText_page">
    <header>
      <el-select
        v-model="combsVal"
        filterable
        placeholder="请选择"
        @change="uploadCombsChange"
        size="small"
      >
        <el-option
          v-for="item in uploadCombsList"
          :key="item.regCombId"
          :label="item.combName"
          :value="item.regCombId"
        >
        </el-option>
      </el-select>
      <el-button
        type="primary"
        size="mini"
        icon="vxe-icon-swap"
        style="margin-left: 10px"
        @click="videoShow = !videoShow"
        >{{ videoShow ? '看图模式' : '拍照模式' }}</el-button
      >
      <el-upload
        :on-change="uploadChange"
        action=""
        :auto-upload="false"
        :show-file-list="false"
        style="display: inline-block; margin-left: 10px"
        accept=".jpg, .jpeg, .png"
      >
        <el-button size="mini" type="primary">本地上传</el-button>
      </el-upload>
      <el-button
        type="primary"
        size="mini"
        style="margin-left: 10px"
        @click="upload"
        >保存</el-button
      >
    </header>
    <div class="bottom_wrap">
      <div class="preview_wrap">
        <div v-show="videoShow">
          <video
            ref="video"
            id="video"
            width="494"
            height="699"
            x5-video-player-fullscreen="true"
            x5-video-orientation="portraint"
          ></video>
          <el-button type="primary" size="medium" @click="take_a_picture"
            >拍照</el-button
          >
        </div>
        <el-image
          class="photo"
          v-if="!videoShow"
          style="width: 494px; height: 699px; margin: 0 auto"
          :src="previewImgUrl"
        >
        </el-image>
      </div>
      <!-- 小图列表 -->
      <div class="minImg_wrap">
        <header>图片列表</header>
        <ul>
          <li v-for="(item, index) in miniImgList" :key="index">
            <div class="img_div" @click="imgClick(item)">
              <el-image class="photo" style="width: 100%" :src="item.imagePath">
              </el-image>
            </div>
            <i
              class="el-icon-error del_icon"
              title="删除"
              @click="DeleteReportGraphicText"
            ></i>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  name: 'uploadImgText',
  props: {
    combsList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    navList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    headerInfo: {
      type: Object,
      default: () => {
        return {};
      }
    },
    showFlag: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapGetters(['G_userInfo'])
  },
  data() {
    return {
      combsVal: '',
      previewImgUrl: '',
      videoShow: true,
      miniImgList: [],
      uploadCombsList: [],
      fileData: ''
    };
  },
  methods: {
    // 图片点击预览大图
    imgClick(imgInfo) {
      this.videoShow = false;
      this.previewImgUrl = imgInfo.imagePath;
    },
    // 上传图片
    upload() {
      if (this.uploadCombsList?.length == 0) {
        this.$message({
          message: '暂无可上传图文报告的组合！',
          type: 'warning',
          showClose: true
        });
      }
      if (this.fileData == '') return Promise.resolve(); // 如果没有文件，直接返回一个解决的 Promise
      let formData = new FormData();
      var file = new File([this.fileData], `${new Date()}.jpg`, {
        type: this.fileData.type,
        lastModified: Date.now()
      });
      console.log(file.name);
      formData.append('files', file);
      console.log(formData);

      return this.$ajax
        .post(this.$apiUrls.UploadReportGraphFile, formData, {
          query: {
            regNo: this.headerInfo?.regNo,
            regCombId: this.combsVal
          },
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: ''
          }
        })
        .then((r) => {
          this.$message({
            message: '上传成功',
            type: 'success',
            showClose: true
          });
          this.$emit('update');
        })
        .finally((r) => {
          this.$emit('close');
        });
    },
    // 选择组合的改变回调
    uploadCombsChange() {
      this.fileData = '';
      let row = {};
      this.videoShow = true;
      this.previewImgUrl = '';
      this.uploadCombsList.some((item) => {
        if (item.regCombId === this.combsVal) {
          row = item;
          return true;
        }
      });
      if (row.imagePath) {
        this.miniImgList = [row];
        return;
      }
      this.miniImgList = [];
    },
    // 删除图文报告
    DeleteReportGraphicText() {
      let datas = {
        regNo: this.headerInfo?.regNo,
        regCombId: this.combsVal
      };
      this.$ajax
        .paramsPost(this.$apiUrls.DeleteReportGraphicText, datas)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.miniImgList = [];
          this.$emit('update');
          this.$message({
            message: '删除成功！',
            type: 'success',
            showClose: true
          });
        });
    },
    // 打开摄像头
    openCamer(
      videoDom,
      options = {
        video: {
          width: 494,
          height: 699,
          facingMode: 'environment'
          //    facingMode: { exact: "environment" }
        }
      }
    ) {
      var that = this;
      try {
        //访问用户媒体设备的兼容方法
        function getUserMedia(constraints, success, error) {
          if (navigator.mediaDevices.getUserMedia) {
            //最新的标准API
            navigator.mediaDevices
              .getUserMedia(constraints)
              .then(success)
              .catch(error);
          } else if (navigator.webkitGetUserMedia) {
            //webkit核心浏览器
            navigator.webkitGetUserMedia(constraints, success, error);
          } else if (navigator.mozGetUserMedia) {
            //firfox浏览器
            navigator.mozGetUserMedia(constraints, success, error);
          } else if (navigator.getUserMedia) {
            //旧版API
            navigator.getUserMedia(constraints, success, error);
          }
        }

        let video = this.$refs[videoDom];
        function success(stream) {
          that.videoShow = true;
          //兼容webkit核心浏览器
          let CompatibleURL = window.URL || window.webkitURL;
          //将视频流设置为video元素的源
          console.log(stream);

          //video.src = CompatibleURL.createObjectURL(stream);
          video.srcObject = stream;
          // this.stream=stream.getTracks()[1];
          video.play();
        }

        function error(error) {
          that.videoShow = false;
          console.log(`访问用户媒体设备失败${error.name}, ${error.message}`);
          that.$message({
            message: '没有找到摄像头！！',
            type: 'warning'
          });
        }

        if (
          navigator.mediaDevices.getUserMedia ||
          navigator.getUserMedia ||
          navigator.webkitGetUserMedia ||
          navigator.mozGetUserMedia
        ) {
          //调用用户媒体设备, 访问摄像头
          getUserMedia(options, success, error);
        } else {
          alert('不支持访问用户媒体');
        }
      } catch (error) {
        this.videoShow = false;
        console.log('启用摄像头失败，请用localhost或https');
      }
    },
    // 关闭摄像头
    closeCamer(videoDom) {
      var video = this.$refs[videoDom];
      if (video.srcObject) {
        video.srcObject.getTracks()[0].stop();
      }
    },
    // 拍照函数
    take_a_picture_fun(canvasDom, videoDom, obj, w = 494, h = 699) {
      this.$nextTick(() => {
        let canvas = document.createElement('canvas');
        canvas.width = w;
        canvas.height = h;
        let video = this.$refs[videoDom];
        let ctx = canvas.getContext('2d');
        ctx.drawImage(video, 0, 0, w, h);
        this.miniImgList = [
          {
            combCode: '',
            combName: '',
            deletable: '',
            imagePath: canvas.toDataURL('image/png'),
            regCombId: ''
          }
        ];
      });
    },
    async take_a_picture() {
      await this.take_a_picture_fun('canvas', 'video', 'userInfo');
      this.dataURLtoFile(this.miniImgList[0].imagePath, 'image/jpeg');
    },
    // 将图片转换为文件格式
    dataURLtoFile(dataURI, type) {
      // console.log(dataURI.split(',')[1]);
      return new Promise((resolve, reject) => {
        if (!dataURI || !dataURI?.split(',')[1]) {
          resolve();
          return;
        }
        let binary = atob(dataURI.split(',')[1]);
        // let binary = Buffer.from(dataURI.split(',')[1], 'base64').toString('ascii')
        if (!binary) return;
        console.log(binary);
        let array = [];
        for (let i = 0; i < binary.length; i++) {
          array.push(binary.charCodeAt(i));
        }
        this.fileData = new Blob([new Uint8Array(array)], { type: type });
        resolve(binary);
      });
    },
    // 上传图文改变的回调
    uploadChange(file, fileList) {
      console.log(file, fileList);
      let that = this;
      if (file) {
        // 创建一个FileReader对象
        const reader = new FileReader();
        // 设置读取完成后的回调函数
        reader.onload = function (e) {
          const content = e.target.result; // 读取的结果
          const blob = new Blob([content]); // 将结果转换为Blob
          console.log(blob); // 输出Blob对象
          that.fileData = blob;
        };

        // 读取文件内容为ArrayBuffer，然后转换为Blob
        reader.readAsArrayBuffer(file.raw);
        this.miniImgList = [
          {
            combCode: '',
            combName: '',
            deletable: '',
            imagePath: URL.createObjectURL(file.raw),
            regCombId: ''
          }
        ];
      }
    }
  },
  mounted() {},
  watch: {
    combsList: {
      handler(n, o) {
        console.log(n);
        let obj = {};
        n?.forEach((item) => {
          obj[item.regCombId] = item;
        });
        console.log(obj);

        let uploadCombsList = [];
        this.navList?.forEach((item) => {
          let row = obj[item.regCombId];
          if (!row || (row && row.deletable)) {
            uploadCombsList.push({
              combCode: item.combCode,
              combName: item.combName,
              deletable: row?.deletable,
              regCombId: item.regCombId,
              imagePath: row?.imagePath
            });
          }
        });
        this.uploadCombsList = uploadCombsList;
        this.combsVal = this.uploadCombsList[0]?.regCombId;
        let arr = [];
        if (this.combsVal && this.uploadCombsList[0].imagePath) {
          arr.push(this.uploadCombsList[0]);
          this.miniImgList = arr;
        } else {
          this.miniImgList = [];
        }
        if (this.uploadCombsList?.length == 0) {
          this.$message({
            message: '暂无可手动上传图文报告的组合！',
            type: 'warning',
            showClose: true
          });
        }
      },
      deep: true,
      immediate: true
    },
    showFlag: {
      handler(n, o) {
        this.$nextTick(() => {
          if (n) {
            this.openCamer('video');
          } else {
            this.closeCamer('video');
            this.previewImgUrl = '';
          }
        });
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
.uploadImgText_page {
  height: 100%;
  overflow: auto;
  display: flex;
  flex-direction: column;
  padding: 5px;
  .bottom_wrap {
    flex: 1 0 0;
    display: flex;
    overflow: auto;
    .preview_wrap {
      flex: 1 0 0;
      overflow: auto;
      // .video_wrap{
      display: flex;
      flex-direction: column;
      justify-content: center;
      text-align: center;
      height: 100%;
      video {
        background: #000;
        margin: 0 auto 10px;
        display: block;
      }
      // }
    }
    .minImg_wrap {
      width: 201px;
      background: rgba(178, 190, 195, 0.1);
      header {
        height: 50px;
        line-height: 50px;
        padding: 0 5px;
      }
      ul {
        display: flex;
        flex-wrap: wrap;
        display: flex;
        justify-content: center;
      }
      li {
        width: 135px;
        height: 191px;
        border: 1px solid #ccc;
        margin-left: 5px;
        margin-bottom: 5px;
        position: relative;
        .img_div {
          width: 100%;
          height: 100%;
          overflow: hidden;
          display: flex;
          justify-content: center;
        }
        .del_icon {
          position: absolute;
          top: 0;
          left: 0;
          transform: translate(-50%, -50%);
          color: #d63031;
          display: none;
          cursor: pointer;
        }
        &:hover .del_icon {
          display: block;
        }
      }
    }
  }
}
</style>
