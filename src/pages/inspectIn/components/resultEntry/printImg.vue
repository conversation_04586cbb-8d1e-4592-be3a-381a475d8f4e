<template>
  <div class="printImg_page">
    <div class="left_wrap">
      <h3>图例列表</h3>
      <el-menu
        ref="printImgMenu_Ref"
        :default-active="defaultActive"
        class="menu_dom"
        size="mini"
      >
        <el-menu-item
          :index="item.combName"
          v-for="(item, idx) in printImgList"
          :key="idx"
          @click="menuClick(item)"
        >
          <span slot="title">{{ item.combName }}</span>
        </el-menu-item>
      </el-menu>
    </div>
    <div class="right_wrap">
      <header>
        <ul>
          <li>
            <label>体检号：</label>
            <span>{{ headerInfo.regNo }}</span>
          </li>
          <li>
            <label>姓名：</label>
            <span>{{ headerInfo.name }}</span>
          </li>
          <li>
            <label>性别：</label>
            <span>{{ G_EnumList['Sex'][headerInfo.sex] }}</span>
          </li>
          <li>
            <label>年龄：</label>
            <span>{{ headerInfo.age }}</span>
          </li>
          <li>
            <label>单位：</label>
            <span>{{ headerInfo.companyName }}</span>
          </li>
        </ul>
        <el-select
          style="width: 150px; margin-right: 5px"
          size="small"
          v-model="printSetInfo.PrinterName"
          placeholder="请选择"
        >
          <el-option
            v-for="item in printList"
            :key="item"
            :label="item"
            :value="item"
          >
          </el-option>
        </el-select>
        <el-button
          class="green_btn btn"
          size="small"
          icon="iconfont icon-dayin-"
          @click="print"
        >
          打印
        </el-button>
      </header>
      <div class="print_content">
        <img :src="imgSrc" alt="" />
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  name: 'printImg',
  props: {
    printImgList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    headerInfo: {
      type: Object,
      default: () => {
        return {};
      }
    },
    printList: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  computed: {
    ...mapGetters(['G_EnumList'])
  },
  data() {
    return {
      defaultActive: '',
      imgSrc: '',
      printSetInfo: {
        PrintTimes: 1,
        PrinterName: ''
      },
      fileStream: ''
    };
  },
  methods: {
    menuClick(menu) {
      this.imgSrc = menu.imagePath;
      this.get_base64(menu.imagePath).then((r) => {
        this.fileStream = r;
      });
    },
    get_base64(imgUrl) {
      return new Promise((resolve, reject) => {
        let img = new Image();
        img.crossOrigin = 'Anonymous';
        this.$nextTick(() => {
          img.src = imgUrl;

          img.onload = function () {
            let canvas = document.createElement('canvas');
            canvas.width = img.width;
            canvas.height = img.height;
            let ctx = canvas.getContext('2d');
            ctx.drawImage(img, 0, 0);
            let dataURL = canvas.toDataURL('image/png');
            resolve(dataURL);
          };
          img.onerror = function () {
            reject('The image could not be loaded.');
          };
        });
      });
    },
    // 打印
    print() {
      let fileStream = this.fileStream.split(',')[1];
      // return
      // console.log(this.fileStream);
      let obj2 = {
        PrinterName: this.printSetInfo.PrinterName,
        PrintTimes: this.printSetInfo.PrintTimes,
        FileBase64: fileStream,
        FileFormat: 'png'
      };
      let obj = {
        Action: 'print',
        Data: JSON.stringify(obj2)
      };
      var data = JSON.stringify(obj);
      console.log(data);
      // let T = "{'Action':'getPrinterList','Data':''}";
      this.$parent.$parent.ws.sendSock(data);
    }
  },
  created() {}
};
</script>

<style lang="less" scoped>
.printImg_page {
  height: 100%;
  overflow: auto;
  padding: 10px;
  display: flex;
  .left_wrap {
    width: 240px;
    display: flex;
    flex-direction: column;
    h3 {
      height: 65px;
      line-height: 65px;
      font-size: 18px;
      color: #2d3436;
      font-weight: normal;
    }
    .menu_dom {
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
      background: rgba(178, 190, 195, 0.1);
      border: 0;
      li {
        height: 40px;
        line-height: 40px;
      }
    }
  }
  .right_wrap {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-left: 10px;
    header {
      display: flex;
      height: 65px;
      align-items: center;
      justify-content: space-between;
      ul {
        display: flex;
        flex: 1;
        border-radius: 4px;
        flex-shrink: 0;
        justify-content: space-around;
        background: rgba(250, 182, 59, 0.2);
        height: 37px;
        align-items: center;
        margin-right: 15px;
      }
    }
    .print_content {
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
      img {
        width: 100%;
      }
    }
  }
  .btn {
    padding: 6.5px 10px;
  }
}
</style>
