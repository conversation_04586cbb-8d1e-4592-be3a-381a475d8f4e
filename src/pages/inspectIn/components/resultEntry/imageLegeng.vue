<template>
  <div class="printImg_page">
    <div class="top_wrap">
      <header>
        <ul>
          <li>
            <label>体检号：</label>
            <span>{{ headerInfo.regNo }}</span>
          </li>
          <li>
            <label>姓名：</label>
            <span>{{ headerInfo.name }}</span>
          </li>
          <li>
            <label>性别：</label>
            <span>{{ G_EnumList['Sex'][headerInfo.sex] }}</span>
          </li>
          <li>
            <label>年龄：</label>
            <span>{{ headerInfo.age }}</span>
          </li>
          <li>
            <label>单位：</label>
            <span>{{ headerInfo.companyName }}</span>
          </li>
        </ul>
      </header>
    </div>
    <div class="bottom_wrap">
      <div class="left_wrap">
        <!-- 操作栏 -->
        <div class="operate_wrap">
          <el-button size="small" class="btn" @click="downLoadImg"
            >下载图片</el-button
          >
        </div>
        <div class="print_content">
          <div class="checkCollectPictures">
            <div class="topDiv">
              <div class="pictures-box">
                <div class="iconDiv">
                  <div class="iDiv" @click="prevImg">
                    <i class="icon el-icon-arrow-left"> </i>
                  </div>
                </div>
                <div class="bigImg">
                  <el-image :src="bigImage" style="width: 100%"></el-image>
                </div>
                <div class="iconDiv">
                  <div class="iDiv" @click="nextImg">
                    <i class="icon el-icon-arrow-right"> </i>
                  </div>
                </div>
              </div>
            </div>
            <div class="bomDiv">
              <div class="img-box">
                <el-image
                  :class="actIndex === index ? 'item-active' : ''"
                  :ref="'minImg_Ref_' + index"
                  v-for="(url, index) in imgList"
                  :key="index"
                  :src="url"
                  lazy
                  @click="imgClick(url, index)"
                ></el-image>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="right_wrap">
        <h3>影像图例</h3>
        <el-menu
          ref="printImgMenu_Ref"
          :default-active="defaultActive"
          class="menu_dom"
          size="mini"
          v-if="ImageLegengList.length !== 0"
        >
          <el-menu-item
            :index="'' + idx"
            v-for="(item, idx) in ImageLegengList"
            :key="idx"
            @click="menuClick(item)"
          >
            <span slot="title">{{ item.combName }}</span>
          </el-menu-item>
        </el-menu>
        <el-empty description="暂无数据" v-else></el-empty>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { dataUtils } from '../../../../common';
export default {
  name: 'printImg',
  props: {
    headerInfo: {
      type: Object,
      default: () => {
        return {};
      }
    },
    checkComb: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  computed: {
    ...mapGetters(['G_EnumList'])
  },
  data() {
    return {
      defaultActive: '',
      imgSrc: '',
      fileStream: '',
      url: '',
      imgList: [],
      bigImage: '',
      ImageLegengList: [],
      actIndex: 0
    };
  },
  methods: {
    menuClick(menu) {
      this.imgList = menu.imageUrls;
      this.actIndex = 0;
      this.bigImage = menu.imageUrls[0];
    },
    //上一张
    prevImg() {
      if (this.actIndex == 0) {
        this.actIndex = this.imgList.length - 1;
      } else {
        this.actIndex--;
      }
      this.bigImage = this.imgList[this.actIndex];
      this.scrollMinImg();
    },
    //下一张
    nextImg() {
      if (this.actIndex >= this.imgList.length - 1) {
        this.actIndex = 0;
      } else {
        this.actIndex++;
      }
      this.bigImage = this.imgList[this.actIndex];
      this.scrollMinImg();
    },
    imgClick(url, index) {
      this.actIndex = index;
      this.bigImage = url;
      this.scrollMinImg();
    },
    // 滚动到当前的缩略图位置
    scrollMinImg() {
      this.$refs['minImg_Ref_' + this.actIndex][0].$el.scrollIntoView({
        behavior: 'smooth'
      });
    },
    // 打开默认加载第一个
    initFun() {
      console.log(this.ImageLegengList);
      this.$ajax
        .get(this.$apiUrls.RecordImages + `/${this.headerInfo.regNo}`)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.ImageLegengList = returnData || [];
          this.defaultActive = '0';
          this.actIndex = 0;
          this.imgList = this.ImageLegengList[0]?.imageUrls || [];
          this.bigImage = this.imgList[0] || '';
        });
    },
    // 下载图片
    downLoadImg() {
      fetch(this.bigImage)
        .then((r) => r.blob())
        .then((blob) => {
          let link = document.createElement('a');
          let url = URL.createObjectURL(blob);
          link.href = url;
          link.download =
            this.ImageLegengList[this.defaultActive].combName +
            (this.actIndex + 1) +
            '_' +
            dataUtils.getNowDateTiemNo();
          link.click();
        });
    }
  },
  mounted() {
    this.defaultActive = '';
    this.initFun();
  }
};
</script>

<style lang="less" scoped>
.printImg_page {
  height: 100%;
  overflow: auto;
  padding: 10px;
  display: flex;
  flex-direction: column;
  .bottom_wrap {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    display: flex;
    .left_wrap {
      flex: 1;
      flex-shrink: 0;
      display: flex;
      margin-right: 5px;
      overflow: auto;
      flex-direction: column;
      .operate_wrap {
        text-align: right;
        margin-bottom: 5px;
      }
    }
    .right_wrap {
      width: 240px;
      display: flex;
      flex-direction: column;
    }
    h3 {
      height: 30px;
      line-height: 30px;
      font-size: 18px;
      color: #2d3436;
      font-weight: normal;
    }
    .menu_dom {
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
      background: rgba(178, 190, 195, 0.1);
      border: 0;
      li {
        height: 40px;
        line-height: 40px;
      }
    }
    .print_content {
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
      .checkCollectPictures {
        height: 100%;
        color: #2d3436;
        display: flex;
        flex-direction: column;
        // padding: 0 18px 18px 18px;
        .topDiv {
          flex: 1;
          background: rgba(178, 190, 195, 0.1);
          border: 1px solid #eee;
          border-radius: 4px;
          margin: auto;
          overflow: auto;
          width: 100%;
          .pictures-box {
            display: flex;
            flex-direction: row;
            height: 100%;
            justify-content: space-between;
            align-items: center;
            .bigImg {
              // width: calc(100% - 200px);
              flex: 1;
              flex-shrink: 0;
              height: 100%;
              margin: 0 30px;
              overflow: auto;
              .el-image {
                height: 99%;
              }
            }
            .iconDiv {
              //   flex: 1;
              display: flex;
              .iDiv {
                cursor: pointer;
                width: 64px;
                height: 180px;
                line-height: 180px;
                text-align: center;
                background: rgba(178, 190, 195, 0.2);
                i {
                  font-size: 40px;
                }
              }
            }
          }
        }
        .bomDiv {
          height: 140px;
          padding: 10px;
          display: flex;
          flex-direction: row;
          background: rgba(178, 190, 195, 0.1);
          border: 1px solid #eee;
          border-radius: 4px;
          overflow: auto;
          .img-box {
            flex: 1;
            padding: 8px;
            flex-shrink: 0;
            // overflow: auto;
            display: flex;
            .el-image {
              padding: 8px;
              margin-left: 10px;
              width: 170px;
              cursor: pointer;
            }
            .item-active {
              border: 2px solid #1770df;
              border-radius: 4px;
            }
          }
        }
      }
    }
  }
  .top_wrap {
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-bottom: 5px;
    header {
      display: flex;
      //   height: 65px;
      align-items: center;
      justify-content: space-between;
      ul {
        display: flex;
        flex: 1;
        border-radius: 4px;
        flex-shrink: 0;
        justify-content: space-around;
        background: rgba(250, 182, 59, 0.2);
        height: 37px;
        align-items: center;
      }
    }
  }
  .btn {
    padding: 6.5px 10px;
  }
}
</style>
