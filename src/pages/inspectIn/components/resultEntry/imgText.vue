<template>
  <div class="printImg_page">
    <div class="right_wrap">
      <header>
        <ul>
          <li>
            <label>体检号：</label>
            <span>{{ headerInfo.regNo }}</span>
          </li>
          <li>
            <label>姓名：</label>
            <span>{{ headerInfo.name }}</span>
          </li>
          <li>
            <label>性别：</label>
            <span>{{ G_EnumList['Sex'][headerInfo.sex] }}</span>
          </li>
          <li>
            <label>年龄：</label>
            <span>{{ headerInfo.age }}</span>
          </li>
          <li>
            <label>单位：</label>
            <span>{{ headerInfo.companyName }}</span>
          </li>
        </ul>
      </header>
      <div class="print_content">
        <embed
          class="embed_dom"
          v-if="imgSrc.includes('pdf')"
          :src="imgSrc"
          type="application/pdf"
          style="text-align: center"
        />
        <el-image v-else style="max-width: 100%" :src="imgSrc"> </el-image>
      </div>
    </div>
    <div class="left_wrap">
      <div class="left_title">
        <h3>图文报告</h3>
        <div class="operate_wrap">
          <el-button type="primary" size="small" @click="uploadBtnClick"
            >上传</el-button
          >
        </div>
      </div>
      <el-menu
        ref="printImgMenu_Ref"
        :default-active="defaultActive"
        class="menu_dom"
        size="mini"
      >
        <el-menu-item
          :index="item.regCombId"
          v-for="(item, idx) in printImgList"
          :key="idx"
          @click="menuClick(item)"
        >
          <span slot="title">{{ item.combName }}</span>
        </el-menu-item>
      </el-menu>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  name: 'printImg',
  props: {
    printImgList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    headerInfo: {
      type: Object,
      default: () => {
        return {};
      }
    },
    checkComb: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  computed: {
    ...mapGetters(['G_EnumList'])
  },
  data() {
    return {
      defaultActive: '',
      imgSrc: '',
      fileStream: ''
    };
  },
  methods: {
    menuClick(menu) {
      this.imgSrc = menu.imagePath;
    },
    initFun() {
      if (JSON.stringify(this.checkComb) == '{}') return;
      this.printImgList.some((item) => {
        if (item.regCombId == this.checkComb.regCombId) {
          this.defaultActive = item.regCombId;
          this.imgSrc = item.imagePath;
          return true;
        }
      });
    },
    // 上传按钮的点击回调
    uploadBtnClick() {
      this.$emit('uploadBtnClick');
    }
  },
  mounted() {
    this.defaultActive = '';
    this.imgSrc = '';
    this.initFun();
  }
};
</script>

<style lang="less" scoped>
.printImg_page {
  height: 100%;
  overflow: auto;
  padding: 10px;
  display: flex;
  .left_wrap {
    width: 240px;
    display: flex;
    flex-direction: column;
    .left_title {
      display: flex;
      justify-content: space-between;
      .operate_wrap {
        display: flex;
        align-items: center;
      }
    }
    h3 {
      height: 50px;
      line-height: 50px;
      font-size: 18px;
      color: #2d3436;
      font-weight: normal;
    }
    .menu_dom {
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
      background: rgba(178, 190, 195, 0.1);
      border: 0;
      li {
        height: 40px;
        line-height: 40px;
      }
    }
  }
  .right_wrap {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    display: flex;
    flex-direction: column;
    header {
      display: flex;
      height: 50px;
      align-items: center;
      justify-content: space-between;
      ul {
        display: flex;
        flex: 1;
        border-radius: 4px;
        flex-shrink: 0;
        justify-content: space-around;
        background: rgba(250, 182, 59, 0.2);
        height: 37px;
        align-items: center;
        margin-right: 15px;
      }
    }
    .print_content {
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
      text-align: center;
      .embed_dom {
        width: 100%;
        height: 100%;
        overflow: auto;
        body {
          margin: 0;
        }
      }
    }
  }
  .btn {
    padding: 6.5px 10px;
  }
}
</style>
