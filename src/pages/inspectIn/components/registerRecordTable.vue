<!--
 * @FilePath: \shenshan\KrPeis\src\pages\inspectIn\components\registerRecordTable.vue
 * @Description:  医生工作站、结果录入-登记记录信息查询列表
 * @Author: justin
 * @Date: 2024-06-18 14:24:36
 * @Version: 0.0.1
 * @LastEditors: key
 * @LastEditTime: 2024-12-10 16:54:26
*
-->
<script>
import PublicTable from '@/components/publicTable2.vue';
import { mapGetters } from 'vuex';
import BtnCommon from './btnCommon.vue';
import { dataUtils } from '@/common';

export default {
  name: 'registerRecordTable',
  components: {
    PublicTable,
    BtnCommon
  },
  props: {
    type: {
      // 使用类型："doctorWorkStation"-医生工作站，"resultEntry"-结果录入
      type: String,
      default: 'doctorWorkStation'
    }
  },
  data() {
    return {
      searchForm: {
        dateRange: [new Date(), new Date()], // 日期范围
        operCode: '', // 医生编码
        companyCode: '', // 体检单位
        peCls: null, // 体检分类
        startTime: '', // 开始时间
        endTime: '', // 结束时间
        peStatus: 1, // 体检状态（1：未完成 2：已完成）
        jobStatus: null, // 岗位状态
        hazardousCode: '', // 危害因素
        clusterCode: '', // 套餐码
        queryType: 1, // 筛选类型
        deptCode: '', // 科室编码
        keyword: '' // 关键字
      },
      companySelect: [],
      dateTypeList: [
        {
          value: 1,
          label: '体检时间'
        },
        {
          value: 2,
          label: '检查时间'
        }
      ],
      clusterList: [], // 套餐
      clusterLists: [],
      occHazardousList: [], // 危害因素
      occHazardousLoading: false, // 危害因素加载状态
      companyList: [], // 单位
      deptList: [], // 科室
      tabActiveName: 'toExam',
      toExamCount: 0, // (医生工作站)科室未检、(结果录入)未完成
      examedCount: 0, // (医生工作站)本人已检、(结果录入)已完成
      tableTheads: [
        {
          prop: 'regNo',
          label: '体检号',
          align: 'center',
          width: '120px',
          sortable: true
        },
        {
          prop: 'name',
          label: '姓名',
          align: 'center',
          showOverflowTooltip: true
        },
        // {
        //   prop: "age",
        //   label: "年龄",
        //   align: "center",
        //   width: "50px",
        //   showOverflowTooltip: true,
        // },
        {
          prop: 'peStatus',
          label: '状态',
          align: 'center',
          width: '80px',
          showOverflowTooltip: true
        }
      ],
      tableElStyle: {
        'show-selection': false,
        'sort-width': '50px',
        'header-cell-style': {
          height: '20px',
          background: 'rgba(23,112,223,.2)',
          fontSize: '14px',
          color: '#2D3436',
          padding: '0 '
        }
      },
      showDept: true // 是否显示科室选择
    };
  },
  created() {
    switch (this.type) {
      case 'doctorWorkStation': // 医生工作站
        this.searchForm.operCode = this.G_userInfo.codeOper.operatorCode;
        this.searchForm.deptCode = this.G_userInfo.codeOper.deptCode;
        break;

      case 'resultEntry': // 结果录入
        this.showDept = false;
        break;
    }

    this.getDepartmentList();
    this.$nextTick(() => {
      this.getClusterList();
      this.getCompanyList();
      this.getOccHazardousList(null);
      this.search();
    });
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_peClsList',
      'G_userInfo',
      'G_config',
      'G_CodeOccupationalPositionStatus',
      'G_datePickerShortcuts'
    ]),
    /**
     * @author: justin
     * @description:
     * @return {*}
     */
    params1() {
      let params = dataUtils.deepCopy(this.searchForm);
      params.startTime = dataUtils.dateToStrStart(params.dateRange[0]);
      params.endTime = dataUtils.dateToStrEnd(params.dateRange[1]);
      params.peCls = Number.isInteger(params.peCls) ? params.peCls : -1;
      params.peStatus = 1;
      delete params.dateRange;
      return params;
    },
    /**
     * @author: justin
     * @description:
     * @return {*}
     */
    params2() {
      let params = dataUtils.deepCopy(this.params1);
      params.peStatus = 2;
      return params;
    },
    /**
     * @author: justin
     * @description: 未检统计显示字符串
     * @return {*}
     */
    toExamCountStr() {
      let str;
      switch (this.type) {
        case 'doctorWorkStation': // 医生工作站
          str = `科室未检（${this.toExamCount}）`;
          break;

        case 'resultEntry': // 结果录入
          str = `未完成（${this.toExamCount}）`;
          break;
      }
      return str;
    },

    /**
     * @author: justin
     * @description: 已检统计显示字符串
     * @return {*}
     */
    examedCountStr() {
      let str;
      switch (this.type) {
        case 'doctorWorkStation': // 医生工作站
          str = `本人已检（${this.examedCount}）`;
          break;

        case 'resultEntry': // 结果录入
          str = `已完成（${this.examedCount}）`;
          break;
      }

      return str;
    }
  },
  methods: {
    // 清除结果操作的聚焦
    clearResultFocus() {
      this.$emit('clearResultFocus');
    },
    /**
     * @author: justin
     * @description: 获取医生关联科室
     * @return {*}
     */
    getDepartmentList() {
      if (!this.showDept) return;

      this.$ajax
        .post(this.$apiUrls.ReadOperDept, [], {
          query: {
            operCode: this.G_userInfo.codeOper.operatorCode
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          console.log('getDepartmentList',returnData)
          this.deptList = Object.entries(returnData);
        });
    },

    /**
     * @author: justin
     * @description: 获取套餐下拉
     * @return {*}
     */
    getClusterList() {
      this.$ajax.post(this.$apiUrls.Cluster).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.clusterLists = returnData;
        this.clusterList = dataUtils.deepCopy(this.clusterLists);
      });
    },

    /**
     * @author: justin
     * @description: 获取危害因素
     * @param {*} keyword
     * @return {*}
     */
    getOccHazardousList(keyword) {
      const data = {
        keyword: keyword,
        pageNumber: 1,
        pageSize: 50
      };
      this.$ajax
        .post(this.$apiUrls.ReadCodeOccupationalHazardousByPage, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.occHazardousList = returnData;
        });
    },

    /**
     * @author: justin
     * @description: 获取单位下拉
     * @return {*}
     */
    getCompanyList() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    /**
     * @description: 单位下拉变化
     * @param {*} data
     * @return {*}
     * @author: key
     */
    companyChange(data) {
      this.companyDeptCode = '';
      this.companyTimesChange(data);
      if (!data || data.length === 0) return;
      this.searchForm.companyCode = data[0];
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        ?.children?.find((item) => item.value === data[1]).item;
      this.searchForm.dateRange = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.search();
    },
    //次数改变时
    companyTimesChange(data) {
      this.searchForm.clusterCode = '';
      if (!data || data.length === 0) {
        this.clusterList = dataUtils.deepCopy(this.clusterLists);
        return;
      }
      this.$ajax
        .post(this.$apiUrls.ReadCompanyCandidateCluster, '', {
          query: {
            companyCode: data[0],
            companyTimes: data[1]
          }
        })
        .then(async (r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          let companyMeal = returnData || [];
          this.clusterList = [...companyMeal, ...this.clusterLists];
        });
    },
    /**
     * @author: justin
     * @description: 查询
     * @return {*}
     */
    search() {
      this.$refs?.publicTable_Ref1?.loadData();
      this.$refs?.publicTable_Ref2?.loadData();
      // this.searchForm.companyCode = this.searchForm.companyCode[0];
      this.$emit('search', this.searchForm);
    },

    /**
     * @author: justin
     * @description: 切换tab
     * @return {*}
     */
    tabSwitch() {
      let tableRefName = '';
      switch (this.tabActiveName) {
        case 'examed':
          tableRefName = 'publicTable_Ref2';
          break;

        default:
          tableRefName = 'publicTable_Ref1';
          break;
      }

      this.$nextTick(() => {
        let scrollPosition = this.$refs[tableRefName].scrollPosition;
        this.$refs[tableRefName].doLayout();
        this.$refs[tableRefName].recoverScroll(
          scrollPosition.top,
          scrollPosition.left
        );
      });
    },

    /**
     * @author: justin
     * @description: 表格双击事件
     * @param {*} row
     * @param {*} column
     * @param {*} event
     * @return {*}
     */
    rowDblclick(row, column, event) {
      row.deptCode = this.searchForm.deptCode;
      this.$emit('rowDblClick', row, column, event);
    },
    // 切换科室
    switchDepartments() {
      this.search();
      this.$emit('departmentSwitching');
    }
  }
};
</script>
<template>
  <div class="register-record-table-wrapper">
    <!-- 过滤 -->
    <el-row align="center" :gutter="20" type="flex" class="header-wrapper">
      <el-col :span="24">
        <el-form
          :model="searchForm"
          :inline="true"
          ref="searchForm"
          size="mini"
        >
          <el-row style="display: flex; width: 100%">
            <el-form-item prop="queryType" style="flex: 1">
              <el-select
                v-model="searchForm.queryType"
                placeholder="请选择筛选类型"
                @change="search"
                size="mini"
                style="width: 100px"
                @focus="clearResultFocus"
              >
                <el-option
                  v-for="item in dateTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="dateRange" style="flex: 1">
              <el-date-picker
                :picker-options="{ shortcuts: G_datePickerShortcuts }"
                style="width: 274px"
                size="mini"
                v-model="searchForm.dateRange"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="search"
              >
              </el-date-picker>
            </el-form-item>
          </el-row>
          <el-row style="display: flex; width: 100%">
            <el-form-item prop="companyCode" style="width: 55%">
              <el-cascader
                ref="company_cascader_ref"
                v-model="companySelect"
                :options="companyList"
                :props="{ multiple: false }"
                clearable
                filterable
                size="mini"
                collapse-tags
                @change="companyChange"
                placeholder="单位"
                :filter-method="filterMethod"
              >
              </el-cascader>
            </el-form-item>
            <el-form-item
              prop="clusterCode"
              style="width: 45%; margin-left: 5px"
            >
              <el-select
                style="width: 100%"
                v-model="searchForm.clusterCode"
                filterable
                clearable
                size="mini"
                placeholder="套餐"
                @change="search"
                @focus="clearResultFocus"
              >
                <el-option
                  v-for="item in clusterList"
                  :key="item.clusCode"
                  :label="item.clusName"
                  :value="item.clusCode"
                >
                  <p>
                    <i class="group_sign" v-show="item.bindCombs">团</i
                    >{{ item.clusName }}
                  </p>
                </el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row style="display: flex; width: 100%">
            <el-form-item
              v-if="G_config.physicalMode.includes('职检')"
              prop="jobStatus"
              style="width: 35%"
            >
              <el-select
                v-model="searchForm.jobStatus"
                filterable
                clearable
                placeholder="岗位"
                size="mini"
                @change="search"
              >
                <el-option
                  v-for="item in G_CodeOccupationalPositionStatus"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value + ''"
                >
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item
              v-if="G_config.physicalMode.includes('职检')"
              prop="hazardousCode"
              style="flex: 1; margin-left: 5px"
            >
              <el-select
                v-model="searchForm.hazardousCode"
                filterable
                clearable
                size="mini"
                remote
                :remote-method="getOccHazardousList"
                :loading="occHazardousLoading"
                placeholder="危害因素"
                @change="search"
              >
                <el-option
                  v-for="item in occHazardousList"
                  :key="item.hazardousCode"
                  :label="item.hazardousName"
                  :value="item.hazardousCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-row>

          <el-form-item
            prop="peCls"
            style="width: 35%"
            v-if="G_config.physicalMode.includes('普检')"
          >
            <el-select
              v-model="searchForm.peCls"
              filterable
              clearable
              size="mini"
              placeholder="体检分类"
              @change="search"
              @focus="clearResultFocus"
            >
              <el-option
                v-for="item in G_peClsList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item prop="keyword" style="width: 43%; margin: 0 5px">
            <el-input
              placeholder="请输入体检号/姓名检索"
              v-model="searchForm.keyword"
              size="mini"
              clearable
              @keyup.enter.native="search"
            >
            </el-input>
          </el-form-item>

          <el-form-item prop="keyword" style="margin-left: 5px">
            <BtnCommon :btnList="['查询']" @search="search"></BtnCommon>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>

    <!-- 内容数据 -->
    <el-row align="center" :gutter="20" type="flex" class="body-wrapper">
      <el-col :span="24">
        <el-tabs v-model="tabActiveName" @tab-click="tabSwitch">
          <el-tab-pane :label="toExamCountStr" name="toExam">
            <!-- 表格数据 -->
            <public-table
              ref="publicTable_Ref1"
              :theads="tableTheads"
              :url="$apiUrls.GetPatientListByDoctorV2"
              rowKey="regNo"
              remoteByPage
              :params="params1"
              :elStyle="tableElStyle"
              @rowDblclick="rowDblclick"
              @request-success="
                (data) => {
                  this.toExamCount = data.totalNumber;
                }
              "
            >
              <template #name="{ scope }">
                <div class="patient-name-box">
                  <i v-if="scope.row.sex == 1" class="el-icon-male"></i>
                  <i v-if="scope.row.sex == 2" class="el-icon-female"></i>
                  <span class="patient-name">{{ scope.row.name }}</span>
                </div>
              </template>

              <template #peStatus="{ scope }">
                <span>{{ G_EnumList['PeStatus'][scope.row.peStatus] }}</span>
              </template>
            </public-table>
          </el-tab-pane>

          <el-tab-pane :label="examedCountStr" name="examed">
            <!-- 表格数据 -->
            <public-table
              ref="publicTable_Ref2"
              :theads="tableTheads"
              :url="$apiUrls.GetPatientListByDoctorV2"
              rowKey="regNo"
              remoteByPage
              :params="params2"
              :elStyle="tableElStyle"
              @rowDblclick="rowDblclick"
              @request-success="
                (data) => {
                  this.examedCount = data.totalNumber;
                }
              "
            >
              <template #name="{ scope }">
                <div class="patient-name-box">
                  <i v-if="scope.row.sex == 1" class="el-icon-male"></i>
                  <i v-if="scope.row.sex == 2" class="el-icon-female"></i>
                  <span class="patient-name">{{ scope.row.name }}</span>
                </div>
              </template>

              <template #peStatus="{ scope }">
                <span>{{ G_EnumList['PeStatus'][scope.row.peStatus] }}</span>
              </template>
            </public-table>
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>

    <!-- 底部 -->
    <el-row
      v-if="showDept"
      align="center"
      :gutter="20"
      type="flex"
      style="margin-top: 8px"
      class="footer-wrapper"
    >
      <el-col :span="24">
        <span style="font-size: 0.8rem; font-weight: bold">科室：</span>
        <el-select
          v-model="searchForm.deptCode"
          size="mini"
          @change="switchDepartments"
          filterable
        >
          <el-option
            v-for="item in deptList"
            :key="item[0]"
            :label="item[1]"
            :value="item[0]"
          >
          </el-option>
        </el-select>
      </el-col>
    </el-row>
  </div>
</template>

<style lang="less" scoped>
.register-record-table-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 10px;
  background: #fff;
  border-radius: 5px;

  .header-wrapper {
    flex: 0;
    /deep/ .el-form-item {
      margin-bottom: 5px;

      .el-form-item__content {
        width: 100%;
      }

      .el-select,
      .el-cascader {
        width: 100%;
      }
    }
  }

  .body-wrapper {
    flex: 1;
    display: flex;
    /deep/ .el-tabs {
      display: flex;
      flex-direction: column;
      height: 100%;

      & > .el-tabs__header {
        flex: 0;
      }

      & > .el-tabs__content {
        flex: 1 0 0;

        & > .el-tab-pane {
          height: 100%;

          .patient-name-box {
            display: flex;
            align-items: center;
            .el-icon-male {
              color: #037bff;
              background: none;
            }
            .el-icon-female {
              color: #fc73a7;
              background: none;
            }
            i {
              margin: 0 5px;
            }

            .patient-name {
              max-width: 80px;
              text-align: left;
              overflow: hidden; /*溢出的部分隐藏*/
              white-space: nowrap; /*文本不换行*/
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
  }
}
.group_sign {
  border-radius: 100%;
  height: 16px;
  width: 16px;
  display: inline-block;
  background: #1770df;
  font-style: normal;
  vertical-align: middle;
  text-align: center;
  line-height: 16px;
  color: #fff;
  font-size: 12px;
}
/deep/.el-form--inline .el-form-item {
  margin-right: 0px !important;
}
</style>
