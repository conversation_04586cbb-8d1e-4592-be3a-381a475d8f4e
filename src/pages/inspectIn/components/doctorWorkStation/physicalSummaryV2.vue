<!--
 * @FilePath: \KrPeis\src\pages\inspectIn\components\doctorWorkStation\physicalSummaryV2.vue
 * @Description:  项目结果录入小结组件v2版
 * @Author: justin
 * @Date: 2024-06-24 14:54:24
 * @Version: 0.0.1
 * @LastEditors: justin
 * @LastEditTime: 2024-07-12 10:47:04
*
-->

<template>
  <div class="physical-examination-summary">
    <div class="title">小<br />结</div>
    <p class="text_p" v-if="!isModify">
      <span
        :class="{ abnormalColor: item.abnormalType > 0 }"
        v-for="(item, idx) in dynamicTags"
        :key="idx"
        >{{
          item.tag +
          (dynamicTags.length == 0 || idx + 1 == dynamicTags.length ? '' : '；')
        }}</span
      >
    </p>
    <div class="content" v-else>
      <tag-input
        ref="tagInput_ref"
        :value.sync="tags"
        :allowDuplicates="false"
        :draggable="false"
        :addTag="addTagCallback"
        :editTag="editTagCallback"
        @onTagFocus="tagFoucs"
        @onTagBlur="tagBlur"
        @onInputFocus="tagFoucs"
        @onInputBlur="tagBlur"
        @onAddTag="addTagHandler"
      >
        <template #tag="{ scope }">
          <span
            v-text="scope.tag"
            :class="{ abnormalColor: scope.abnormalType > 0 }"
          ></span>
        </template>
      </tag-input>
    </div>
    <!-- 结果的编辑弹窗 -->
    <EditResult
      ref="editPopup_Ref"
      label="physicalExaminationSummary"
      :isOvertop="isOvertop"
      :editPopup.sync="editPopup"
      :top.sync="top"
      :left.sync="left"
      @readCodeDeptWord="readCodeDeptWord"
    />
    <!-- 遮罩 -->
    <div class="mask" v-if="maskShow" @click="resultLeave"></div>
  </div>
</template>

<script>
import TagInput from '@/components/tagInput.vue';
import EditResult from './editResultV2.vue';

export default {
  name: 'physicalExaminationSummary',
  components: {
    TagInput,
    EditResult
  },
  props: {
    dynamicTags: {
      type: Array,
      default: []
    },
    combData: {
      type: Object,
      default: () => {
        return {};
      }
    },
    isModify: {
      type: Boolean,
      default: false
    },
    // 是否纯音测听弹窗显示
    showPureToneAudiometry: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tags: [...this.dynamicTags],
      maskShow: false,
      editPopup: false,
      top: 0,
      left: 0,
      isOvertop: 0,
      curTag: {},
      curIndex: 0,
      cursorIndex: 0
    };
  },
  methods: {
    /**
     * @author: justin
     * @description: 新增标签回调
     * @param {*} tags
     * @param {*} value
     * @return {*}
     */
    addTagCallback(tags, value) {
      tags.push({
        abnormalType: 0,
        bindItemTags: [],
        id: 0,
        isCustom: true,
        tag: value
      });
      return tags;
    },

    /**
     * @author: justin
     * @description: 修改标签回调
     * @param {*} tags
     * @param {*} index
     * @param {*} value
     * @return {*}
     */
    editTagCallback(tags, index, value) {
      let newTag = tags[index];
      newTag.tag = value;
      tags.splice(index, 1, newTag);
      return tags;
    },

    /**
     * @author: justin
     * @description: 添加标签事件
     * @param {*} e
     * @param {*} tags
     * @param {*} value
     * @return {*}
     */
    addTagHandler(e, tags, value) {
      this.tagFoucs(e, null, null); // 重置悬浮常用词组件位置
    },

    /**
     * @author: justin
     * @description: 鼠标点击标签
     * @param {*} e
     * @param {*} tag
     * @param {*} idx
     * @return {*}
     */
    tagFoucs(e, tag, idx) {
      setTimeout(() => {
        this.curTag = e.target;
        // 设置标签浮动(用于标签内容可选择，避免遮罩)
        this.curTag.style.position = 'relative';
        this.curTag.style.zIndex = '99';
        this.curIndex = idx ?? -1;
        this.maskShow = true;
        this.$refs.editPopup_Ref.sightWordVal = tag?.tag || '';
        this.editPopup = true;
        this.resizeDeptWordPopup();
      }, 10);
    },

    /**
     * @author: justin
     * @description:  鼠标离开标签
     * @param {*} e
     * @param {*} tag
     * @param {*} idx
     * @return {*}
     */
    tagBlur(e, tag, idx) {
      // 设置标签不浮动
      this.curTag.style.zIndex = '0';
      if (this.curTag.selectionStart) {
        // 输入框
        this.cursorIndex = this.curTag.selectionStart;
      } else {
        // 非输入框
        const selection = window.getSelection();
        if (selection.rangeCount > 0) {
          const range = selection.getRangeAt(0);
          const preCaretRange = range.cloneRange();
          preCaretRange.selectNodeContents(this.curTag);
          preCaretRange.setEnd(range.endContainer, range.endOffset);
          this.cursorIndex = preCaretRange.toString().length;
        }
      }
    },

    /**
     * @author: justin
     * @description: 常用词接口成功回调
     * @param {*} data
     * @return {*}
     */
    readCodeDeptWord(data) {
      this.resizeDeptWordPopup();
    },

    /**
     * @author: justin
     * @description: 调整常用词弹窗位置
     * @return {*}
     */
    resizeDeptWordPopup() {
      this.$nextTick(() => {
        if (!this.curTag.getBoundingClientRect) return;
        let tagCoord = this.curTag.getBoundingClientRect();
        let popupCoord =
          this.$refs.editPopup_Ref.$refs.editPopupDom_Ref.getBoundingClientRect();
        this.left = this.showPureToneAudiometry
          ? tagCoord.left * 1.24
          : tagCoord.left;
        this.top = this.showPureToneAudiometry
          ? (tagCoord.top - popupCoord.height) * 1.06
          : tagCoord.top - popupCoord.height;
      });
    },

    /**
     * @author: justin
     * @description: 鼠标覆盖弹窗的回调
     * @param {*} popupName
     * @return {*}
     */
    popupMouseenter(popupName) {
      this[popupName] = true;
    },

    /**
     * @author: justin
     * @description: 鼠标离开隐藏
     * @return {*}
     */
    resultLeave() {
      this.editPopup = false;
      this.maskShow = false;
      this.curTag.blur();
      this.$refs.tagInput_ref.blur();
    },

    /**
     * @author: justin
     * @description: 选择常用词回调
     * @param {*} words
     * @return {*}
     */
    setTag(words) {
      const that = this;
      const cursorIndex = that.cursorIndex;
      let tag;
      if (that.curIndex > -1) {
        tag =
          that.tags[that.curIndex].tag.slice(0, cursorIndex - 1) +
          words +
          that.tags[that.curIndex].tag.slice(cursorIndex - 1);
        that.editTagCallback(that.tags, that.curIndex, tag);
      } else {
        tag =
          that.$refs.tagInput_ref.inputValue.slice(0, cursorIndex) +
          words +
          that.$refs.tagInput_ref.inputValue.slice(cursorIndex);
        that.$refs.tagInput_ref.setInputValue(tag);
      }
      that.$nextTick(() => {
        that.setTagCursorPosition(cursorIndex);
      });
    },

    /**
     * @author: justin
     * @description: 设置光标位置
     * @param {*} cursorIndex
     * @return {*}
     */
    setTagCursorPosition(cursorIndex) {
      const that = this;
      that.curTag.focus();
      if (that.curTag.setSelectionRange) {
        // 输入框
        that.curTag.setSelectionRange(cursorIndex, cursorIndex);
      } else {
        // 非输入框
        // 获得文本节点，具体文本内容，非dom元素
        const textNode = that.curTag.firstChild.firstChild;
        // 创建一个范围对象
        const range = document.createRange();
        const selection = window.getSelection();

        // 设置范围的对象为一个文本节点
        range.selectNodeContents(textNode);
        // 挪动光标到文本节点的特定位置
        range.setStart(textNode, cursorIndex);
        range.setEnd(textNode, cursorIndex);

        // 挪动光标到文本节点的末尾
        //range.collapse(false); // false 表示将光标挪到范围的末尾

        // 清除任何已存在的选择，并设置新的选择范围
        selection.removeAllRanges();
        selection.addRange(range);
      }
    },

    /**
     * @author: justin
     * @description: 处理每个项目结果的数组
     * @param {*} arr
     * @return {*}
     */
    disposeEveryProject(arr) {
      let txt = '';
      arr.map((item, idx) => {
        txt += `${idx == 0 ? '' : '；'}` + item.tag;
      });
      return txt;
    }
  },
  watch: {
    dynamicTags: {
      handler(newVal, oldVal) {
        this.tags = newVal;
      },
      immediate: true,
      deep: true
    },
    tags: {
      handler(newVal, oldVal) {
        this.$nextTick(() => {
          this.$emit('update:dynamicTags', newVal);
          this.$emit('onChanged', newVal);
        });
      },
      immediate: true,
      deep: true
    }
  }
};
</script>

<style lang="less" scoped>
.physical-examination-summary {
  display: flex;
  color: #2d3436;
  height: 100%;
  // 异常文字颜色
  .abnormalColor {
    color: #d63031;
  }
  .title {
    font-size: 18px;
    font-weight: 600;
    padding: 14px 17px;
    background: rgba(23, 112, 223, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .content {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
  }
  .text_p {
    font-size: 16px;
    padding: 10px;
    height: 100%;
    width: 100%;
    overflow: auto;
    font-weight: bold;
  }

  .mask {
    z-index: 1;
  }
}
</style>
