<template>
  <div class="physicalExaminationSummary">
    <div class="title">小<br />结</div>
    <p class="text_p" v-if="!isModify">
      {{ disposeEveryProject(dynamicTags) }}
    </p>
    <div class="content" v-else>
      <el-tag
        :key="index"
        v-for="(tag, index) in dynamicTags"
        closable
        :disable-transitions="false"
        @click="tableTagClick($event, tag)"
        @close.stop="handleClose(tag, index)"
        class="tag"
      >
        {{ tag.tag }}
      </el-tag>
      <el-input
        class="input-new-tag"
        v-if="inputVisible"
        v-model="inputValue"
        ref="saveTagInput"
        size="small"
        @keyup.enter.native="handleInputConfirm"
        @blur="handleInputConfirm"
      >
      </el-input>
      <el-button
        v-else
        class="button-new-tag"
        size="small"
        @click="showInput"
        icon="iconfont icon-xinjian"
        >新增</el-button
      >
    </div>
    <!-- 结果的编辑弹窗 -->
    <EditResult
      ref="editPopup_Ref"
      label="physicalExaminationSummary"
      :isOvertop="isOvertop"
      :editPopup.sync="editPopup"
      :top.sync="top"
      :left.sync="left"
    />
    <!-- 遮罩 -->
    <div class="mask" v-if="maskShow" @click="resultLeave"></div>
  </div>
</template>

<script>
import EditResult from './editResult.vue';
import editResultJs from './mixins/editResultJs';

export default {
  name: 'physicalExaminationSummary',
  mixins: [editResultJs],
  components: {
    EditResult
  },
  props: {
    dynamicTags: {
      type: Array,
      default: []
    },
    combData: {
      type: Object,
      default: () => {
        return {};
      }
    },
    isModify: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      inputVisible: false,
      inputValue: '',
      editNodulus: {},
      maskShow: false
    };
  },
  methods: {
    handleClose(tag, index) {
      this.dynamicTags.splice(index, 1);
    },

    showInput() {
      this.inputVisible = true;
      this.$nextTick(() => {
        this.$refs.saveTagInput.focus();
      });
    },

    handleInputConfirm() {
      let inputValue = this.inputValue;
      if (inputValue) {
        this.dynamicTags.push({
          bindItemTags: [],
          id: 0,
          isCustom: true,
          tag: inputValue
        });
      }
      this.inputVisible = false;
      this.inputValue = '';
    },
    // 处理每个项目结果的数组
    disposeEveryProject(arr) {
      let txt = '';
      arr.map((item, idx) => {
        txt += `${idx == 0 ? '' : '；'}` + item.tag;
      });
      return txt;
    }
  }
};
</script>

<style lang="less" scoped>
.physicalExaminationSummary {
  display: flex;
  color: #2d3436;
  height: 100%;
  .title {
    font-size: 18px;
    font-weight: 600;
    padding: 14px 17px;
    background: rgba(23, 112, 223, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .content {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
  }
  .tag {
    margin-left: 10px;
    margin-top: 10px;
    white-space: pre-line;
    word-wrap: break-word;
    height: auto;
    cursor: pointer;
  }
  .button-new-tag {
    margin-left: 10px;
    margin-top: 10px;
    height: 32px;
    line-height: 30px;
    padding-top: 0;
    padding-bottom: 0;
    font-size: 14px !important;
    /deep/.iconfont {
      font-size: 12px;
    }
  }
  .input-new-tag {
    width: 90px;
    margin-left: 10px;
    vertical-align: bottom;
    margin-top: 10px;
  }
  .text_p {
    font-size: 16px;
    padding: 10px;
    height: 100%;
    width: 100%;
    overflow: auto;
  }
}
</style>
