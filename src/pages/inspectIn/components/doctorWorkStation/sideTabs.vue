<template>
  <div class="sideTabs">
    <div class="tabs-title">
      <div
        class="title-item"
        :class="active === 1 ? 'active' : ''"
        @click="tabsClick(1)"
        v-if="dataQueryFlag"
      >
        图例
      </div>
      <div
        class="title-item"
        :class="active === 2 ? 'active' : ''"
        @click="tabsClick(2)"
      >
        <el-tooltip class="item" effect="light" placement="bottom">
          <div slot="content">
            <div class="tooltip_div">
              <span><i class="el-icon-check" style="color: #b2bec3"></i></span>
              <p>表示没有检查完</p>
            </div>
            <div class="tooltip_div">
              <span><i class="el-icon-check" style="color: #1770df"></i></span>
              <p>表示全部检查完</p>
            </div>
            <div class="tooltip_div">
              <span><i class="el-icon-check" style="color: #d63031"></i></span>
              <p>表示有异常</p>
            </div>
            <div class="tooltip_div">
              <span><i class="el-icon-info" style="color: #1770df"></i></span>
              <p>蓝色字体表示已检</p>
            </div>
          </div>
          <span>项目导航</span>
        </el-tooltip>
      </div>
    </div>
    <div class="tabs-content">
      <!-- 图例 -->
      <div v-show="active === 1" class="legend">
        <div class="main-img" v-show="template == 3 && imgList.length > 0">
          <el-image :src="mainImage" @click="bigImg(mainImage)"></el-image>
        </div>
        <div class="img-box">
          <el-image
            class="img-item"
            :class="imgActive === index ? 'item-active' : ''"
            v-for="(url, index) in imgList"
            :key="index"
            :src="url.imagePath"
            @click="imgClick(url.imagePath, index)"
            @dblclick="imgDbClick(url.imagePath, index)"
          ></el-image>
          <div class="img-item">
            <div
              class="item-add"
              @click="addImg"
              v-show="template == 3 && isEnabled == true"
            >
              <span class="iconfont icon-xinjian add-icon"></span>
            </div>
          </div>
        </div>
      </div>
      <!-- 项目导航 -->
      <div v-show="active === 2" class="project-nav">
        <!-- <el-collapse v-model="activeName" accordion @change="collapseChange">
          <el-collapse-item
            :name="item.deptCode"
            class="collapse-item"
            v-for="(item, index) in navList"
            :key="index"
            :class="{'active-collapse' :item.isFinish && !item.isCombError,'active-combError':item.isFinish && item.isCombError}"
          >
            <template slot="title">
              <span class="collapse-title">
                <img
                  v-show="item.isMain"
                  class="examine-icon"
                  src="../../../../assets/img/doctorWorkStation/main.png"
                />
                <img
                  v-show="item.isEnabled && !item.isMain"
                  class="examine-icon"
                  src="../../../../assets/img/doctorWorkStation/bestraddle.png"
                />
                {{ item.deptName }}
              </span>
            </template> -->
        <div class="collapse-content">
          <ul class="content-wrap">
            <li
              :class="[
                liIndex === li.regCombId ? 'active-li' : '',
                colorFormat(li.combStatus)
              ]"
              v-for="(li, i) in navList"
              :key="i"
              @click="navClick(li)"
            >
              {{ li.combName }}
            </li>
          </ul>
        </div>
        <!-- </el-collapse-item>
        </el-collapse> -->
      </div>
    </div>
    <el-drawer
      title="查看采集图片"
      :visible.sync="picturesDrawer"
      :before-close="picturesDrawerClose"
      :wrapperClosable="false"
      size="90%"
      v-if="picturesDrawer"
      ref="pictures"
    >
      <CheckCollectPictures
        :mainImage="mainImage"
        :imgActive="imgActive"
        :headerInfo="headerInfo"
        :imgList="imgList"
      />
    </el-drawer>
    <el-drawer
      title="图片整理"
      :visible.sync="addImgDrawer"
      :before-close="addImgDrawerClose"
      :wrapperClosable="false"
      size="90%"
      v-if="addImgDrawer"
      ref="addImg"
    >
      <ChoosePicture
        :headerInfo="headerInfo"
        :propRegCombId="liIndex"
        :regCombs="regCombs"
        :deptCode="deptCode"
      />
    </el-drawer>
  </div>
</template>

<script>
import CheckCollectPictures from './checkCollectPictures.vue';
import ChoosePicture from './choosePicture.vue';
export default {
  name: 'sideTabs',
  components: { CheckCollectPictures, ChoosePicture },
  props: {
    //行信息
    headerInfo: {
      typeof: Object,
      default: {}
    },
    //项目导航信息
    navList: {
      typeof: Array,
      default: []
    },
    dataQueryFlag: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      picturesDrawer: false,
      addImgDrawer: false,
      active: 2, //标签页选中
      activeName: '', //折叠面板选中
      liIndex: null, //组合选中
      imgActive: 0,
      imgList: [],
      mainImage: '',
      isShow: true,
      template: '',
      isEnabled: '', //当前医生能否操作标志
      deptCode: '',
      checkGroup: {}, //选中的组合
      regCombs: []
    };
  },
  watch: {
    //headerInfo() {
    // setTimeout(() => {
    // this.getImage();
    // }, 300);
    // }
  },
  created() {
    // this.mainImage = this.imgList[0];
  },
  mounted() {
    // this.getImage();
    this.getPacsDeptCode();
  },
  methods: {
    //获取pacs科室代码
    getPacsDeptCode() {
      this.$ajax.post(this.$apiUrls.GetPacsDept).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.deptCode = returnData?.value;
      });
    },
    //获取图片数据
    getImage(regCombId, deptCode) {
      this.$ajax
        .post(this.$apiUrls.GetRecordImage, '', {
          query: {
            regNo: this.headerInfo.regNo,
            regCombId: regCombId,
            deptCode: deptCode
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          if (returnData.length >= 1) {
            // returnData.forEach((item, index) => {
            //   return (item.imagePath =
            //     this.$config.fileUrl + item.imagePath.replace(/\\/g, "/"));
            // });
            this.isShow = true;
            this.imgList = returnData;
            this.mainImage = returnData[0].imagePath;
          } else {
            this.imgList = [];
            this.mainImage = '';
            this.isShow = false;
          }
          console.log('[ this.imgInfoList-277', this.imgInfoList);
        });
    },
    // 标签页点击
    tabsClick(value) {
      this.active = value;
    },
    // 折叠面板切换
    collapseChange(val) {
      console.log('[ val ]-223', val);
      this.liIndex = null;
      this.checkGroup = {};
      this.$parent.examDeptCode = val;
      // this.navList.map(item => {
      //   if (item.deptCode === val) {
      //     let data = {
      //       regCombId: item.regCombs[0].regCombId,
      //       regNo: this.headerInfo.regNo,
      //       combCode: item.regCombs[0].combCode,
      //       template: item.template
      //     };
      //     this.$parent.getCombInfo(data);
      //   }
      // });
    },
    // 组合点击
    navClick(li, item) {
      console.log(
        JSON.stringify(this.$parent.infoData?.recordItems),
        JSON.stringify(this.$parent.fixed_infoData?.recordItems)
      );
      if (
        this.$parent.isModify &&
        JSON.stringify(this.$parent.fixed_infoData) != '{}' &&
        JSON.stringify(this.$parent.infoData?.recordItems) !=
          JSON.stringify(this.$parent.fixed_infoData?.recordItems)
      ) {
        this.$confirm('结果有修改，是否保存?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        })
          .then(() => {
            this.$parent.saves(false).then((r) => {
              this.navClickFun(li);
              this.$parent.initNavList();
            });
          })
          .catch(() => {
            this.navClickFun(li);
          });
        return;
      }
      this.navClickFun(li);
    },
    // 组合点击的通用函数
    navClickFun(li) {
      console.log('[ li ]-238', li);
      this.$parent.checkComb = li;
      this.checkGroup = li;
      this.liIndex = li.regCombId;
      let data = {
        regCombId: li.regCombId,
        regNo: this.headerInfo.regNo,
        template: li.template
      };
      this.regCombs = this.navList;
      console.log('[ this.regCombs ]-252', this.regCombs);
      this.$parent.regCombs = this.navList;
      this.$parent.deptCode = li.examDeptCode;
      this.deptCode = li.examDeptCode;
      this.$parent.getCombInfo(data);
      this.$parent.pageStatus = li.template;
      this.getImage(li.regCombId, this.deptCode);
      this.template = li.template;
      this.isEnabled = li.isEnabled;
      this.$parent.isEnabled = li.isEnabled;
      this.$parent.regCombId = li.regCombId;
      this.$parent.combCode = li.combCode;
      this.$parent.clsCode = li.clsCode;
      this.$parent.$refs.examine_Ref.checkRowIndex = null;
      // this.$parent.isModify = false;
      if (!li.isEnabled) {
        this.$parent.btnList = ['历史报告'];
        this.$parent.errorDisabled = true;
      } else {
        if (li.template == 3 && this.headerInfo.peStatus < 3) {
          this.$parent.errorDisabled = false;
          this.$parent.btnList = ['保存', '修改', '弃检', '采集', '历史报告'];
        } else if (li.template != 3 && this.headerInfo.peStatus < 3) {
          this.$parent.errorDisabled = false;
          this.$parent.btnList = ['保存', '修改', '弃检', '历史报告'];
        }
        if (li.combStatus != 1) {
          this.$parent.btnList.push('删除结果');
        }
      }
    },
    imgClick(item, index) {
      this.imgActive = index;
      this.mainImage = item;
    },
    //双击小图弹出查看图片
    imgDbClick(item, index) {
      this.imgActive = index;
      this.mainImage = item;
      this.picturesDrawer = true;
    },
    picturesDrawerClose() {
      this.picturesDrawer = false;
    },
    bigImg(img) {
      this.picturesDrawer = true;
    },
    addImgDrawerClose() {
      this.addImgDrawer = false;
      this.getImage(this.liIndex, this.deptCode); //重新获取图例
    },
    addImg() {
      if (!this.headerInfo.regNo) {
        this.$message({
          message: '请先点击要添加图片的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.addImgDrawer = true;
    },
    // 导航标题格式化
    colorFormat(val) {
      let color = '';
      switch (val) {
        case 1:
          color = '';
          break;
        case 2:
          color = 'cell_blue';
          break;
        case 3:
          color = 'cell_red';
          break;
      }
      return color;
    }
  }
};
</script>

<style lang="less" scoped>
.sideTabs {
  height: 100%;
  color: #2d3436;
  display: flex;
  flex-direction: column;
  .tabs-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    // height: 62px;
    padding: 0px 18px 6px 18px;
  }
  .title-item {
    padding: 11px 12px;
    padding-bottom: 11px;
    cursor: pointer;
    border-bottom: 2px solid #fff;
    &.active {
      color: #1770df;
      border-bottom: 2px solid #1770df;
    }
  }
  .tabs-content {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    .project-nav {
      border-top: 1px solid #d8dee1;
    }
  }
  .legend {
    border-top: 1px solid #d8dee1;
    padding: 18px 13px;
  }
  .main-img {
    // background: #3cb34f;
    width: 171px;
    height: 121px;
    border-radius: 4px;
    margin: 0 auto;
    // margin-bottom: 18px;
    .el-image {
      width: 171px;
      height: 121px;
    }
  }
  .img-box {
    display: flex;
    flex-wrap: wrap;
    // justify-content: space-between;
    .el-image {
      width: 45%;
      margin: auto;
      margin-top: 10px;
      cursor: pointer;
    }
  }
  .img-item {
    width: 91px;
    height: 72px;
    border-radius: 4px;
    margin-top: 10px;
    margin-bottom: 10px;
    margin-right: 10px;
    padding: 4px;
    &:nth-child(2n) {
      margin-right: 0;
    }
    &.item-active {
      border: 1px solid #1770df;
    }
  }
  .item-img {
    width: 81px;
    height: 62px;
    background: #3cb34f;
    border-radius: 4px;
    cursor: pointer;
  }
  .item-add {
    width: 80px;
    height: 62px;
    background: #f3f3f5;
    border-radius: 4px;
    border: 1px solid #d5dbde;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
  .add-icon {
    color: #b2bec3;
    font-size: 32px;
  }
  .collapse-item {
    &.active-collapse/deep/.el-collapse-item__header {
      background: rgba(23, 112, 223, 0.2);
    }
    &.active-combError/deep/.el-collapse-item__header {
      background: rgba(214, 48, 49, 0.8);
    }
  }
  /deep/.el-collapse-item__header {
    height: 38px;
    background: rgba(178, 190, 195, 0.3);
    border-bottom-color: #b3abab;
  }
  .collapse-title {
    width: 100%;
    padding-left: 28px;
    font-size: 14px;
    font-weight: 600;
    position: relative;
  }
  .examine-icon {
    width: 20px;
    height: 20px;
    position: absolute;
    left: 4px;
    top: 50%;
    transform: translateY(-50%);
  }
  // .collapse-content {
  //   height: 210px;
  //   overflow: auto;
  // }
  .content-wrap {
    margin-left: 18px;
    border-left: 1px solid #b2bec3;

    li {
      // margin-bottom: 18px;
      padding: 7px;
      padding-left: 16px;
      font-size: 14px;
      cursor: pointer;
      position: relative;
      &:last-child {
        margin-bottom: 0;
      }
      &.active-li::before {
        content: '';
        display: block;
        width: 14px;
        height: 14px;
        background: url('../../../../assets/img/doctorWorkStation/position.png')
          no-repeat 100% / cover;
        position: absolute;
        left: -6px;
        top: 50%;
        transform: translateY(-50%);
        z-index: 1;
      }
      &.active-li::after {
        content: '';
        display: block;
        width: 1px;
        height: 100%;
        background: #1770df;
        position: absolute;
        left: -1px;
        top: 0;
      }
    }
  }
  /deep/.el-collapse-item__content {
    padding-bottom: 20px;
  }
}
</style>
<style lang="less">
.tooltip_div {
  display: flex;
  margin-bottom: 5px;
  width: 200px;
  span {
    width: 16px;
    margin-right: 5px;
    flex-shrink: 0;
    i {
      font-size: 16px;
      font-weight: bold;
    }
  }
  p {
    flex: 1;
  }
}
</style>
