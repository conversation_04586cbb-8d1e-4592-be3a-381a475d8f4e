<template>
  <div class="reConsultation">
    <div class="leftCont">
      <div class="typeHead">
        <p>患者信息</p>
        <ul class="type_head">
          <li>
            <div class="every_inp regNo_inp">
              <label>体检号:</label>
              <p>{{ typeHead.regNo }}</p>
            </div>
            <div class="every_inp">
              <label>姓名:</label>
              <p>{{ typeHead.name }}</p>
            </div>
            <div class="every_inp">
              <label>性别:</label>
              <p>{{ G_EnumList['Sex'][typeHead.sex] }}</p>
            </div>
            <div class="every_inp">
              <label>年龄:</label>
              <p>{{ typeHead.age }}</p>
            </div>
          </li>
          <li>
            <div class="every_inp cont_inp">
              <label>单位</label>
              <p :title="typeHead.companyName" style="overflow-y: auto">
                {{ typeHead.companyName }}
              </p>
            </div>
          </li>
        </ul>
      </div>
      <div class="typeCenter">
        <p>
          <span>会诊内容：</span
          ><BtnCommon
            :btnList="['发起会诊']"
            @sendConsultation="getCreateDoctorChat"
          />
        </p>
        <div class="textareaDiv">
          <el-input
            type="textarea"
            placeholder="请输入会诊内容"
            v-model="textareaVal"
          >
          </el-input>
        </div>
        <!-- <div class="tableDiv">
          <p>会诊列表：</p>
          <div class="tableCont">
            <PublicTable
              :viewTableList.sync="tableData"
              :theads.sync="theads"
              :tableLoading.sync="loading"
              :columnWidth="columnWidth"
              @rowClick="rowClick"
              @rowDblclick="rowDblclick"
            >
              <template #sex="{ scope }">
                <div>
                  {{ G_EnumList["Sex"][scope.row.sex] }}
                </div>
              </template>
            </PublicTable>
          </div>
        </div> -->
      </div>
    </div>
    <div class="cenCont">
      <p>选择会诊科室：</p>
      <div>
        <el-input
          size="small"
          style="margin-bottom: 10px"
          placeholder="请输入搜索科室"
          v-model="deparName"
          clearable
          @clear="search"
          @keyup.enter.native="search"
        ></el-input>
      </div>
      <div class="contDiv">
        <div class="ckHead">
          <div style="margin-right: 10px">选择</div>
          <div>科室名称</div>
        </div>
        <el-checkbox-group v-model="checkedRoom" @change="handleCheckedChange">
          <el-checkbox
            v-for="item in departList"
            :label="item.value"
            :key="item.value"
            >{{ item.label }}</el-checkbox
          >
        </el-checkbox-group>
      </div>
    </div>
    <!-- <div class="rightCont">
      <p>会诊回复列表：</p>
      <div class="tableCont">
        <PublicTable
          :viewTableList.sync="rightTableData"
          :theads.sync="rightTheads"
          :tableLoading.sync="loading"
          :columnWidth="columnWidths"
        >
          <template #deptCode="{ scope }">
            <div>
              {{ G_EnumList["CodeDepartment"][scope.row.deptCode] }}
            </div>
          </template>
        </PublicTable>
      </div>
    </div> -->
  </div>
</template>
<script>
import PublicTable from '../../../../components/publicTable.vue';
import BtnCommon from '../btnCommon.vue';
import { mapGetters } from 'vuex';
import { dataUtils } from '@/common';
export default {
  name: 'reConsultation',
  components: { BtnCommon, PublicTable },
  props: {
    //打开响应会诊
    responseConsultation: {
      typeof: Function,
      default: false
    },
    //行信息
    headerInfo: {
      typeof: Object,
      default: {}
    }
  },
  data() {
    return {
      loading: false,
      typeHead: this.headerInfo,
      theads: {
        doctorCode: '发起医生',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        createTime: '发起时间',
        content: '发起内容'
      },
      tableData: [],
      columnWidth: {
        createTime: 180,
        content: 200
      },
      textareaVal: '',
      checkedRoom: [],
      rightTheads: {
        deptCode: '响应科室',
        doctorCode: '医生',
        createTime: '时间',
        content: '回复内容'
      },
      rightTableData: [],
      columnWidths: {
        createTime: 180,
        content: 200
      },
      receiveDeptArr: [],
      index: 0,
      deparName: '',
      departList: []
    };
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_userInfo', 'G_codeDepartment'])
  },
  mounted() {
    this.getReadDoctorChat();
    this.departList = this.G_codeDepartment;
  },

  methods: {
    //科室模糊查询
    search() {
      if (this.deparName.length > 0) {
        this.departList = this.G_codeDepartment.filter((item) => {
          return item.label.indexOf(this.deparName) !== -1;
        });
      } else {
        this.departList = this.G_codeDepartment;
      }
      //console.log(this.departList);
    },
    handleCheckedChange(val) {
      console.log('[ val ]-136', val);
      this.receiveDeptArr = val;
    },
    //发起会诊
    getCreateDoctorChat() {
      console.log('[ this ]-178', this.$parent.$parent);
      if (this.receiveDeptArr.length == 0) {
        this.$message({
          message: '请选择需要会诊的科室',
          type: 'warning',
          showClose: true
        });
        return;
      }

      let data = {
        regNo: this.typeHead.regNo,
        doctorCode: this.G_userInfo.codeOper.operatorCode,
        deptCode: this.G_userInfo.codeOper.deptCode,
        deptArray: this.receiveDeptArr,
        name: this.typeHead.name,
        sex: this.typeHead.sex,
        age: this.typeHead.age,
        content: this.textareaVal,
        receiveDeptCode: this.receiveDeptArr
      };
      // console.log("[ data ]-154", data);
      //  this.sendConsultations(); //连接
      // return
      this.$ajax.post(this.$apiUrls.CreateDoctorChat, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.textareaVal = '';
        // this.sendConsultations(); //连接
        // this.tableData.unshift(r.data.returnData);
        // this.sendConsultations(returnData.chatId); //连接
        //this.tableData.push(r.data.returnData);
        this.$parent.$parent.sendDrawerHandleClose();
        this.$message({
          message: '发起会诊成功！',
          type: 'success',
          showClose: true
        });
        //console.log("[ this.tableData ]-200", this.tableData);
      });

      // this.sendConsultations(); //连接
    },
    //连接发送信息
    sendConsultations(ChatId) {
      let sendData = {
        ChatId,
        SendUser: this.G_userInfo.codeOper.operatorCode,
        DeptArray: this.receiveDeptArr,
        InitFlag: true,
        Content: this.textareaVal
      };
      console.log('[ sendData ]-189', sendData);
      this.$ws.sendSock(JSON.stringify(sendData));
    },
    //会诊列表
    getReadDoctorChat() {
      this.$ajax
        .post(this.$apiUrls.ReadDoctorChat, '', {
          query: { operCode: this.G_userInfo.codeOper.operatorCode }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.tableData = returnData;
        });
    },
    //单击显示对应回复
    rowClick(row) {
      let data = {
        chatId: row.id,
        createTime: row.createTime,
        receiveUser: this.G_userInfo.codeOper.operatorCode
      };
      this.getReadDoctorChatReply(data);
      this.getUpdateChatMsgStauts(data);
    },
    //双击跳转响应页面
    rowDblclick(row) {
      this.responseConsultation(row.id);
    },
    //会诊回复列表
    getReadDoctorChatReply(data) {
      this.$ajax.post(this.$apiUrls.ReadDoctorChatReply, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.rightTableData = returnData;
      });
    },
    //更新消息状态为已读
    getUpdateChatMsgStauts(data) {
      this.$ajax.post(this.$apiUrls.UpdateChatMsgStauts, data).then((r) => {
        let { success } = r.data;
        if (!success) return;
      });
    }
  }
};
</script>
<style lang="less" scoped>
.reConsultation {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  padding: 0 20px 20px 20px;
  .leftCont {
    flex: 1;
    display: flex;
    flex-direction: column;
    & > p {
      font-weight: 600;
    }
    .typeHead {
      display: flex;
      flex-direction: column;
      & > p {
        font-weight: 600;
      }
    }
    /deep/.el-textarea__inner,
    .el-textarea,
    .textareaDiv {
      height: 100%;
    }
    .tableDiv {
      flex: 1;
      display: flex;
      flex-direction: column;
      background: rgba(178, 190, 195, 0.1);
      border: 0 solid #b2bec3;
      border-radius: 4px;
      border-radius: 4px;
      & > p {
        font-weight: 600;
      }
      .tableCont {
        flex: 1;
        flex-shrink: 0;
        overflow: auto;
        border: 1px solid #d8dee1;
        border-radius: 4px;
      }
    }
    .typeCenter {
      display: flex;
      flex-direction: column;
      flex: 1;
      & > p {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        font-weight: 600;
      }
    }
  }
  .type_head {
    background: rgba(23, 112, 223, 0.1);
    border-radius: 4px;
    border-radius: 4px;
    padding: 10px;
    li {
      display: flex;
      height: 32px;
      font-family: PingFangSC-Medium;
      font-size: 14px;
      color: #2d3436;
    }

    .every_inp {
      display: flex;
      align-items: center;
      margin-right: 10px;
      width: 20%;
      label {
        margin-right: 10px;
        width: auto;
        text-align: right;
      }

      p {
        flex: 1;
      }
    }
    .regNo_inp {
      width: 40%;
    }
    .cont_inp {
      width: 100%;
    }
  }
  .cenCont {
    width: 247px;
    height: 100%;
    padding: 0 20px;
    display: flex;
    flex-direction: column;
    & > p {
      font-weight: 600;
      width: 160px;
    }
    .contDiv {
      flex: 1;
      border: 1px solid #eee;
      display: flex;
      flex-direction: column;
      overflow: auto;
      .ckHead {
        display: flex;
        height: 30px;
        line-height: 30px;
        background: rgba(23, 112, 223, 0.2);
        border-radius: 4px;
        border-radius: 4px;
        width: 100%;
        padding: 0 10px;
      }
      .el-checkbox-group {
        flex: 1;
        flex-shrink: 0;
        overflow: auto;
        display: flex;
        flex-direction: column;
        padding: 10px 10px 0 10px;
        width: 100%;
        .el-checkbox {
          margin-right: 0;
          display: flex;
          padding: 10px 0 0 0px;
          /deep/.el-checkbox__inner {
            width: 21px;
            height: 21px;
          }
          /deep/.el-checkbox__input {
            width: 32px;
          }
          /deep/.el-checkbox__inner::after {
            height: 10px;
            left: 6px;
            position: absolute;
            top: 3px;
          }
          /deep/.el-checkbox__label {
            display: inline-block;
            padding-left: 10px;
            line-height: 21px;
            font-family: PingFangSC-Regular;
            font-size: 14px;
            color: #2d3436;
          }
        }
      }
    }
  }
  .rightCont {
    width: 460px;
    display: flex;
    flex-direction: column;
    overflow: auto;
    & > p {
      font-weight: 600;
    }
    .tableCont {
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
      border: 1px solid #d8dee1;
      border-radius: 4px;
    }
  }
  p {
    height: 48px;
    line-height: 48px;
    font-family: PingFangSC-Medium;
    font-size: 18px;
    color: #2d3436;
  }
}
</style>
