<template>
  <div class="examine_page" style="height: 100%" @click.stop>
    <!-- 弃检图片 -->
    <img
      src="@/assets/img/qijian.png"
      alt=""
      class="qijian_img"
      v-if="
        JSON.stringify(combData) !== '{}'
          ? combData.recordComb.doctorName === '弃检'
          : false
      "
    />
    <vxe-table
      border
      ref="xTable"
      size="small"
      :row-config="{ isCurrent: true, isHover: true }"
      :column-config="{ resizable: true }"
      :data.sync="combData.recordItems"
      @cell-click="cellClick"
      @cell-mouseenter="cellMouseEnter"
      @current-change="currentChange"
      @toggle-row-expand="expandChange"
    >
      <!-- @cell-mouseleave="resultLeave" -->
      <vxe-column type="expand" width="40">
        <template #content="{ row }">
          <div class="fold_wrap">
            <!-- 折线图 -->
            <div
              v-if="row.resultType == 1"
              :id="'project_' + row.itemCode"
              class="canvas_div"
              style="width: 100%; height: 300px"
            ></div>
            <!-- 表格 -->
            <div v-if="row.resultType == 0" style="padding: 0 10px">
              <el-table :data="row.historyList" border style="width: 100%">
                <el-table-column
                  v-for="th in row.historyTheads"
                  :key="th"
                  :prop="th"
                  :label="th"
                  min-width="100"
                  :width="th == '日期' ? 100 : ''"
                >
                </el-table-column>
              </el-table>
            </div>
          </div>
        </template>
      </vxe-column>
      <vxe-column field="itemName" title="项目名称" width="300">
        <template #default="scope">
          <div class="result_div" :title="scope.row.itemCode">
            <el-popover
              placement="right"
              popper-class="my_popover"
              width="200"
              trigger="hover"
              @hide="onPopoverHide"
            >
              <ul class="popover_ul">
                <li>
                  <label for="">项目代码：</label>
                  <span>{{ scope.row.itemCode }}</span>
                </li>
              </ul>
              <span slot="reference">{{ scope.row.itemName }}</span>
            </el-popover>
          </div>
        </template>
      </vxe-column>
      <!-- 内科模板 -->
      <template v-if="mode == 2">
        <vxe-column title="异常程度" field="abnormalType" width="100">
          <template #default="scope">
            <div class="abnormal_level">
              <span>{{
                scope.row.abnormalType == 0
                  ? ''
                  : G_EnumList['AbnormalType'][scope.row.abnormalType]
              }}</span>
              <i
                class="el-icon-circle-close select_icon"
                @click="abnormalLevelClear"
                v-if="enterCheckRow.itemCode == scope.row.itemCode"
              ></i>
            </div>
          </template>
        </vxe-column>
        <vxe-column field="itemTags" title="本次结果">
          <template #default="scope">
            <div class="this_result">
              <!-- 输入数值类型 -->
              <div v-if="isModify && scope.row.resultType == 1">
                <el-input
                  style="width: 100%"
                  :ref="'inpFocus_' + scope.rowIndex"
                  @blur="numResultBlur(scope.row, scope)"
                  @keypress.enter.native="nextRowCurrent(scope)"
                  v-model="scope.row.numberResult"
                ></el-input>
              </div>
              <p v-if="!isModify">
                {{ disposeEveryProject(scope.row.itemTags) }}
              </p>
              <!-- 多选标签类型 -->
              <div v-if="isModify && scope.row.resultType == 0 && false">
                <el-tag
                  @close.stop="tableTagClose(tag, idx)"
                  @click.stop="tableTagClick($event, tag, idx)"
                  class="table_tag"
                  :type="tag.abnormalType != 0 ? 'danger' : ''"
                  v-for="(tag, idx) in scope.row.itemTags"
                  :key="tag.recItemId"
                >
                  {{ tag.tag }}
                  <i
                    class="el-icon-check tag_i"
                    title="生成小结"
                    @click.stop="createSummary(tag)"
                  ></i>
                  <i
                    class="el-icon-close tag_i"
                    title="删除结果"
                    @click.stop="tableTagClose(tag, idx)"
                  ></i>
                </el-tag>
              </div>
              <!-- 编辑框和标签组件 -->
              <InputCom
                :ref="'inpFocus_' + scope.rowIndex"
                :labelList="scope.row.itemTags"
                :itemCode="scope.row.itemCode"
                @inputBlur="inputBlur"
                @inputComEnter="inputComEnter"
                @labelDel="labelDel"
                @inputChange="inputChange"
                @createSummary="createSummary"
                v-if="isModify && scope.row.resultType == 0"
              />
              <!-- 候选按钮 -->
              <div
                class="candidate_btn"
                :class="
                  scope.rowIndex === checkRowIndex && resultPopup
                    ? 'el-icon-arrow-down'
                    : 'el-icon-arrow-right'
                "
                title="候选结果"
                v-if="isModify && scope.row.resultType == 0"
                @click="candidateBtnClick(scope)"
              ></div>
              <!-- <i class="el-icon-circle-close select_icon" @click.stop="resultClear"
                v-if="enterCheckRow.itemCode==scope.row.itemCode"></i> -->
            </div>
          </template>
        </vxe-column>
        <vxe-column
          title="单位"
          field="unit"
          width="80"
          v-if="checkComb.isShowUnits"
        ></vxe-column>
      </template>
      <!-- 检验模板 -->
      <template v-if="mode == 1">
        <vxe-column title="异常" width="58">
          <template #default="scope">
            <span v-if="scope.row.resultType == 1" class="abnormal_span">
              <!-- {{ C_abnormal(scope.row) }} -->
              {{ scope.row.hint }}
            </span>
            <span v-else class="abnormal_span_txt">
              <!-- {{ C_abnormal(scope.row)}} -->
              {{ scope.row.hint }}
            </span>
          </template>
        </vxe-column>
        <vxe-column field="itemTags" title="结果">
          <template #default="scope">
            <div class="this_result" :ref="'itemTags_' + scope.rowIndex">
              <!-- 输入数值类型 -->
              <div v-if="isModify && scope.row.resultType == 1">
                <el-input
                  style="width: 100%"
                  :ref="'inpFocus_' + scope.rowIndex"
                  @blur="numResultBlur(scope.row, scope)"
                  @keypress.enter.native="nextRowCurrent(scope)"
                  v-model="scope.row.numberResult"
                ></el-input>
              </div>
              <p v-if="!isModify">
                {{ disposeEveryProject(scope.row.itemTags) }}
              </p>
              <!-- 多选标签类型 -->
              <!-- <div v-if="isModify && scope.row.resultType == 0 && false">
                <el-tag
                  @close.stop="tableTagClose(tag, idx)"
                  @click.stop="tableTagClick($event, tag, idx)"
                  class="table_tag"
                  v-for="(tag, idx) in scope.row.itemTags"
                  :key="tag.recItemId"
                  :type="tag.abnormalType != 0 ? 'danger' : ''"
                >
                  {{ tag.tag }}
                  <i class="el-icon-check tag_i" title="生成小结" @click.stop="createSummary(tag)"></i>
                  <i class="el-icon-close tag_i" title="删除结果" @click.stop="tableTagClose(tag, idx)"></i>
                </el-tag>
              </div> -->
              <InputCom
                :ref="'inpFocus_' + scope.rowIndex"
                :labelList="scope.row.itemTags"
                :itemCode="scope.row.itemCode"
                @inputComEnter="inputComEnter"
                @inputBlur="inputBlur"
                @labelDel="labelDel"
                @inputChange="inputChange"
                @createSummary="createSummary"
                @resultSpanBlur="resultSpanBlur"
                v-if="isModify && scope.row.resultType == 0"
              />
              <!-- 候选按钮 -->
              <div
                class="candidate_btn"
                :class="
                  scope.rowIndex === checkRowIndex && resultPopup
                    ? 'el-icon-arrow-down'
                    : 'el-icon-arrow-right'
                "
                title="候选结果"
                v-if="isModify && scope.row.resultType == 0"
                @click="candidateBtnClick(scope)"
              ></div>
              <!-- <i class="el-icon-circle-close select_icon" @click.stop="resultClear"
                v-if="enterCheckRow.itemCode==scope.row.itemCode"></i> -->
            </div>
          </template>
        </vxe-column>
        <vxe-column
          title="单位"
          field="unit"
          width="80"
          v-if="checkComb.isShowUnits"
        ></vxe-column>
        <vxe-column
          title="参考值"
          width="80"
          v-if="checkComb.isShowReferenceRange"
        >
          <template #default="scope">
            <el-popover
              placement="right"
              width="300"
              trigger="hover"
              :content="disposeFKVal(scope.row)"
            >
              <div class="reference_div" slot="reference">
                {{ disposeFKVal(scope.row) }}
              </div>
            </el-popover>
          </template>
        </vxe-column>
      </template>

      <vxe-column
        title="上次结果"
        field="lastItemResult"
        width="135"
        v-if="checkComb.isShowLastResult"
      ></vxe-column>
    </vxe-table>
    <!-- 异常程度的弹窗 -->
    <div
      class="popup_style select_popup"
      v-show="abnormalPopup"
      ref="selectPopup_Ref"
      @mouseenter="popupMouseenter('abnormalPopup')"
      :style="{
        [isOvertop >= 0 ? 'top' : 'bottom']: top + 'px',
        left: left + 'px'
      }"
    >
      <p
        v-for="(item, idx) in G_abnormalType"
        :key="idx"
        @click="abnormalClick(item)"
      >
        {{ item.label }}
      </p>
    </div>
    <!-- 结果的弹窗 -->
    <div
      class="popup_style result_popup"
      v-show="resultPopup"
      ref="resultPopup_Ref"
      @mouseenter="popupMouseenter('resultPopup')"
      :style="{
        [isOvertop >= 0 ? 'top' : 'bottom']: top + 'px',
        left: left + 'px',
        width: width + 'px'
      }"
    >
      <!-- 自定义 -->
      <div class="custom_result">
        <el-button
          type="primary"
          size="mini"
          @click="customClick"
          icon="el-icon-plus"
          plain
          >自定义</el-button
        >
      </div>
      <!-- 正常结果 -->
      <div class="result_style normal_result">
        <h3>正常结果：</h3>
        <div class="result_wrap">
          <el-tag
            type="info"
            disable-transitions
            @click="resultClick($event, item)"
            effect="plain"
            hit
            class="result_tag"
            :class="popupActive(item) ? 'normal_active' : ''"
            v-for="(item, idx) in normalResult"
            :key="idx"
          >
            <template v-if="!item.isSelectable">
              {{ item.resultDesc }}
            </template>
            <template v-else>
              <span
                class="tag_span_select"
                v-for="txt in C_txtList(item.resultDesc)"
                :key="txt"
              >
                <i v-if="txt.indexOf('select') == -1">{{ txt }}</i>
                <ResultSelect
                  v-else
                  @dropdownMouseenter="popupMouseenter('resultPopup')"
                  :list="C_txtObj(item.resultDesc, txt)"
                />
              </span>
            </template>
          </el-tag>
          <!-- <el-tag style="max-width:100%;white-space: pre-wrap;">
            <span class="tag_span_select" v-for="txt in C_txtList" :key="txt">
              <i v-if="txt.indexOf('select')==-1">{{txt}}</i>
              <ResultSelect v-else @dropdownMouseenter="popupMouseenter('resultPopup')" :list="C_txtObj(txt)" />
            </span>
            
          </el-tag> -->
        </div>
      </div>
      <!-- 异常结果 -->
      <div class="result_style abnormal_result">
        <h3>异常结果：</h3>
        <div class="result_wrap">
          <el-tag
            type="info"
            disable-transitions
            @click="resultClick($event, item)"
            effect="plain"
            hit
            class="result_tag"
            :class="popupActive(item) ? 'abnormal_active' : ''"
            v-for="(item, idx) in abnormalResult"
            :key="idx"
          >
            <template v-if="!item.isSelectable">
              {{ item.resultDesc }}
            </template>
            <template v-else>
              <span
                class="tag_span_select"
                v-for="txt in C_txtList(item.resultDesc)"
                :key="txt"
              >
                <i v-if="txt.indexOf('select') == -1">{{ txt }}</i>
                <ResultSelect
                  v-else
                  @dropdownMouseenter="popupMouseenter('resultPopup')"
                  :list="C_txtObj(item.resultDesc, txt)"
                />
              </span>
            </template>
          </el-tag>
        </div>
      </div>
    </div>
    <!-- 结果的编辑弹窗 -->
    <EditResult
      ref="editPopup_Ref"
      :isDefineClick.sync="isDefineClick"
      :isOvertop.sync="isOvertop"
      label="examine"
      :editPopup.sync="editPopup"
      :top.sync="top"
      :left.sync="left"
    />
    <!-- <div class="popup_style edit_popup" v-show="editPopup" ref="editPopup_Ref" @mouseenter="popupMouseenter('editPopup')"
      @mouseleave="resultLeave" :style="{ top: top + 'px', left: left + 'px'}">
      <div class="sightWord_input">
        <el-input ref="sightWordInput_Ref" v-model="sightWordVal" @keypress.enter.native="sightWordInputEnter" @blur="handleInputBlur" size="small" width="100%"></el-input>
      </div>
      <div class="sight_word">
        <h3>常用词：<i class="el-icon-edit-outline" @click="editOutlineClick"></i></h3>
        <div class="sightWord_wrap">
          <el-tag type="info" disable-transitions effect="plain" @click="sightWordClick(item)" :closable="isSightWordEdit" hit class="sightWord_tag"
             @close="sightWordClose(item)" v-for="(item,idx) in sightWordList" :key="idx">{{item.words}}
          </el-tag>
        </div>
        <div class="custom_result">
          <el-input
            class="input-new-tag"
            v-if="isAddSightWord"
            v-model.trim="addSightWordVal"
            ref="saveTagInput"
            size="small"
            @keyup.enter.native="handleInputConfirm"
            @blur="handleInputConfirm"
          >
          </el-input>
          <el-button @click="showInput" type="primary" size="mini" icon="el-icon-plus" v-else plain>新增</el-button>
        </div>
      </div>
    </div> -->
    <!-- 遮罩 -->
    <div class="examine_mask" v-if="maskShow" @click="resultLeave"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import { mapGetters } from 'vuex';
import EditResult from './editResult.vue';
import Template from '../../../../views/template.vue';
import ResultSelect from './component/resultSelect';
import InputCom from '@/components/input';
import inputMixins from './mixins/inputMixins';
import Vue from 'vue';
import { dataUtils } from '../../../../common';

export default {
  name: 'examine',
  mixins: [inputMixins],
  props: {
    mode: {
      default: 1
    },
    isModify: {
      type: Boolean,
      default: true
    },
    combData: {
      type: Object,
      default: () => {
        return {};
      }
    },
    headerInfo: {
      type: Object,
      default: () => {
        return {};
      }
    },
    checkComb: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  components: {
    ResultSelect,
    EditResult,
    Template,
    InputCom
  },
  computed: {
    ...mapGetters(['G_abnormalType', 'G_EnumList', 'G_btnAuthList']),
    C_txtList() {
      return (txt) => {
        // let txt = 'dfgfd[二尖瓣区,三尖瓣区,主动脉区,肺动脉区,胸骨左缘Ⅱ-Ⅲ肋间,胸骨左缘Ⅲ-Ⅳ肋间][收缩期，舒张期][I级,II级,III级,IV级,V级,VI级][隆隆样，叹息样，吹风样]杂音';
        let reg = /\[([^\[\]]*)\]/g;
        let idx = 0;
        let text = txt.replace(reg, (r, i) => {
          let nTxt = `@select${idx}@`;
          idx += 1;
          return nTxt;
        });

        let txtArr = text.split('@');
        let nTxtArr = [];
        txtArr.map((item) => {
          if (item != '') {
            nTxtArr.push(item);
          }
        });
        return nTxtArr;
      };
    },
    C_txtObj() {
      return (txt, key) => {
        // let txt = 'dfgfd[二尖瓣区,三尖瓣区,主动脉区,肺动脉区,胸骨左缘Ⅱ-Ⅲ肋间,胸骨左缘Ⅲ-Ⅳ肋间][收缩期，舒张期][I级,II级,III级,IV级,V级,VI级][隆隆样，叹息样，吹风样]杂音';
        let reg = /\[([^\[\]]*)\]/g;
        let idx = 0;
        let obj = {};
        let text = txt.replace(reg, (r, i) => {
          obj['select' + idx] = i.split(/[,|，]/);
          idx += 1;
        });
        return obj[key];
      };
    },
    popupActive() {
      return function (tag) {
        let isExist = false;
        this.enterCheckRow.itemTags?.map((item) => {
          if (item.resultId == tag.resultId) {
            isExist = true;
          }
        });
        return isExist;
      };
    },
    C_combData() {
      let itemCodeArr = [];
      this.combData.recordItems?.map((item) => {
        itemCodeArr.push(item.itemCode);
      });
      return itemCodeArr;
    },
    // 计算异常
    C_abnormal() {
      return (row, down, up) => {
        // console.log(row);
        let txt = '';
        row.abnormalType = 0;
        // 数值类型
        if (row.resultType == 1 && row.itemTags.length != 0) {
          let tag = Number(row.itemTags[0].tag);
          // 危急
          let dLLimit = Number(row.dangerLowerLimit);
          let dULimit = Number(row.dangerUpperLimit);
          // 重大阳性
          let pLLimit = Number(row.positiveLowerLimit);
          let pULimit = Number(row.positiveUpperLimit);
          // 正常
          let lLimit = Number(row.lowerLimit);
          let uLimit = Number(row.upperLimit);
          console.log(lLimit, uLimit, dLLimit, dULimit);
          let lLType = typeof lLimit === 'number';
          let uLType = typeof uLimit === 'number';
          let dLType = typeof dLLimit === 'number';
          let dUType = typeof dULimit === 'number';
          if (
            row.lowerLimit !== '' &&
            row.dangerLowerLimit !== '' &&
            lLType &&
            dLType &&
            tag < lLimit &&
            tag >= dLLimit
          ) {
            row.abnormalType = 1;
            row.hint = '↓';
            return;
            // return "↓";
          }
          if (
            row.upperLimit !== '' &&
            row.dangerUpperLimit !== '' &&
            uLType &&
            dUType &&
            tag > uLimit &&
            tag <= dULimit
          ) {
            row.abnormalType = 1;
            row.hint = '↑';
            return;
            // return "↑";
          }
          // debugger
          // 异常
          // if (lLimit && pLLimit && tag < lLimit && tag >= pLLimit) {
          //   row.abnormalType = 1;
          //   return "↓";
          // }
          // if (uLimit && pULimit && tag > uLimit && tag <= pULimit) {
          //   row.abnormalType = 1;
          //   return "↑";
          // }

          // // 重大异常
          // if (dLLimit && pLLimit && tag < pLLimit && tag >= dLLimit) {
          //   row.abnormalType = 2;
          //   return "↓↓";
          // }
          // if (dULimit && pULimit && tag > pULimit && tag <= dULimit) {
          //   row.abnormalType = 2;
          //   return "↑↑";
          // }

          // 危急
          if (row.dangerLowerLimit !== '' && dLType && tag < dLLimit) {
            row.abnormalType = 3;
            row.hint = '↓';
            return;
            // return "↓";
          }
          if (row.dangerUpperLimit !== '' && dUType && tag > dULimit) {
            row.abnormalType = 3;
            row.hint = '↑';
            return;
            // return "↑";
          }

          // 单边的上下限
          if (row.upperLimit !== '' && lLType && !uLimit && tag < lLimit) {
            row.abnormalType = 1;
            row.hint = '↓';
            return;
            // return "↓";
          }
          if (row.lowerLimit !== '' && !lLimit && uLType && tag > uLimit) {
            row.abnormalType = 1;
            row.hint = '↑';
            return;
            // return "↑";
          }
        }

        // 多标签类型
        if (row.resultType == 0 && row.itemTags.length != 0) {
          // let tag = row.itemTags[0].tag;
          row.itemTags.map((item) => {
            // 阴性   阳性
            if (
              (row.lowerLimit != '' && item.tag != row.lowerLimit) ||
              (row.upperLimit != '' && item.tag != row.upperLimit)
            ) {
              row.abnormalType = 1;
              txt = '异';
            }
          });
          row.hint = txt;
          return;
          // return txt;
        }
        row.hint = txt;
        return;
        // return txt;
      };
    },
    // 计算异常程度
    C_abnormalLevel() {
      return (row) => {
        let level = 0;
        row.itemTags.map((item) => {
          if (item.abnormalType > level) {
            level = item.abnormalType;
          }
        });
        row.abnormalType = level;
        if (row.abnormalType == 0) return '';
        return this.G_EnumList['AbnormalType'][row.abnormalType];
      };
    }
  },
  watch: {
    editPopup(n, o) {
      if (n) {
        this.getReadCodeDeptWord();
      }
    },
    'combData.recordItems': {
      handler() {
        this.checkRowIndex = null;
      }
    }
  },
  data() {
    return {
      shortcutList: {
        9: this.nextRowCurrent
      },
      checkRowIndex: null,
      maskShow: false,
      tableData: [],
      enterCheckRow: {}, //选中的行数据
      fixed_enterCheckRow: {}, //
      abnormalPopup: false,
      resultPopup: false,
      editPopup: false,
      top: 0,
      left: 0,
      width: 350,
      // 正常结果
      normalResult: [],
      // 异常结果
      abnormalResult: [],
      // 常用词的配置
      editResult: {}, //选中结果编辑
      sightWordVal: '',
      isSightWordEdit: false,
      cursorIndex: '', //光标在输入框的位置
      sightWordList: [],
      isAddSightWord: false, // 是否显示新增的输入框
      addSightWordVal: '',
      oldRowInfo: {}, //当前编辑行的初始数据
      isDefineClick: false, //是否自定义按钮点击的
      direction: 'top',
      isOvertop: 0, //判断结果弹窗定位top或者bottom
      checkCellIdx: null
    };
  },
  methods: {
    // 销毁前的回调
    onPopoverHide() {
      let node = document.querySelector('body>.my_popover'); //隐藏时删除
      node.remove();
    },
    abnormalLevel() {
      let level = 0;
      this.enterCheckRow.itemTags?.map((item) => {
        if (item.abnormalType > level) {
          level = item.abnormalType;
        }
      });
      this.enterCheckRow.abnormalType = level;
    },
    tagTest(e) {
      console.log(e.target.innerText);
    },
    // 处理参考值
    disposeFKVal(row) {
      let lLimit = row.lowerLimit;
      let uLimit = row.upperLimit;
      if (lLimit && !uLimit) {
        return `${Number(lLimit) ? '≥' : ''}${lLimit}`;
      }
      if (uLimit && !lLimit) {
        return `${Number(uLimit) ? '≤' : ''}${uLimit}`;
      }
      if (lLimit && uLimit) {
        return `${lLimit}-${uLimit}`;
      }
    },
    // 单元格的点击回调
    cellClick({ row, column, cell, $event, rowIndex }) {
      let oldIdx = this.checkCellIdx;
      this.$nextTick(() => {
        console.log(this.combData);
        if (!this.isModify) return;
        console.log(row, column, cell, $event);
        this.checkCellIdx = rowIndex;
        this.enterCheckRow = row;
        this.fixed_enterCheckRow = row;
        this.editPopup = false;
        this.oldRowInfo = JSON.parse(JSON.stringify(row));
        if (column.property == 'abnormalType') {
          this.getResultList(row.itemCode);
          this.abnormalPopup = true;
          this.showPopupFun('selectPopup_Ref', cell, $event);
          this.maskShow = true;
        }
        // if (column.property == "itemTags" && row.resultType === 0) {
        //   this.getResultList(row.itemCode);
        //   this.resultPopup = true;
        //   this.showPopupFun("resultPopup_Ref", cell, $event);
        //   this.maskShow = true;
        // }
        if (oldIdx === rowIndex) return;
        this.maskShow = false;
        this.resultPopup = false;
      });
    },
    // 候选按钮的点击回调
    candidateBtnClick(scope) {
      let oldIdx = this.checkCellIdx;
      console.log(oldIdx);
      setTimeout(() => {
        let cell =
          this.$refs['itemTags_' + this.checkCellIdx].parentNode.parentNode;
        // if (scope.column.property == "abnormalType") {
        //   this.getResultList(scope.row.itemCode);
        //   this.showPopupFun("selectPopup_Ref", cell, {view:window});
        //   this.maskShow = true;
        //   this.abnormalPopup = true;
        // }
        if (scope.column.property == 'itemTags' && scope.row.resultType === 0) {
          this.getResultList(scope.row.itemCode);
          this.showPopupFun('resultPopup_Ref', cell, { view: window });
          if (oldIdx === this.checkRowIndex && this.resultPopup) {
            console.log(77777);
            this.maskShow = false;
            this.resultPopup = false;
            return;
          }
          if (oldIdx === this.checkRowIndex && !this.resultPopup) {
            this.maskShow = true;
            this.resultPopup = true;
            console.log(888888);
            return;
          }
          if (oldIdx !== this.checkRowIndex && !this.resultPopup) {
            this.maskShow = true;
            this.resultPopup = true;
          }
        }
      }, 100);
    },
    // 鼠标进入单元格的回调
    cellMouseEnter({ row }) {
      if (!this.isModify) return;
      // this.enterCheckRow = row;
    },
    // 显示弹窗的封装
    showPopupFun(ref, cell, event) {
      // return new Promise((resolve,reject)=>{
      this.$nextTick(() => {
        let popupDom = this.$refs[ref].getBoundingClientRect();
        let coordObj = cell.getBoundingClientRect();
        console.log(coordObj);
        this.left = coordObj.left;
        this.width = coordObj.width;
        this.isOvertop =
          event.view.innerHeight -
          coordObj.top -
          coordObj.height -
          popupDom.height;

        if (coordObj.top < 0) {
          this.top = 50;
          return;
        }
        if (this.isOvertop >= 0) {
          this.top = coordObj.top + coordObj.height - 1;
        } else {
          // this.top = coordObj.bottom + popupDom.height - 1;
          this.top = window.innerHeight - coordObj.top;
        }
      });

      // })
    },
    // 鼠标离开隐藏
    resultLeave() {
      this.abnormalPopup = false;
      this.resultPopup = false;
      this.editPopup = false;
      this.enterCheckRow = {};
      this.maskShow = false;
    },
    // 鼠标覆盖弹窗的回调
    popupMouseenter(popupName) {
      this[popupName] = true;
      this.enterCheckRow = this.fixed_enterCheckRow;
    },
    // 关闭弹窗
    closePopup(e) {
      console.log(8888);
      this.enterCheckRow = {};
      this.abnormalPopup = false;
      this.resultPopup = false;

      this.isSightWordEdit = false;
    },
    // 异常值改变的回调
    abnormalClick(row) {
      // return
      this.enterCheckRow.abnormalType = row.value;
      console.log(this.enterCheckRow);
      console.log(row);
      this.closePopup();
    },
    // 结果的点击回调
    resultClick(e, tag) {
      let isExist = false;
      let editMode = 1;
      let rowTag = {};
      let tagDom;
      // console.log(this.checkRowIndex,this.$refs['inpFocus_'+this.checkRowIndex]);
      this.$refs['inpFocus_' + this.checkRowIndex].wrapClick();
      e.composedPath().some((item) => {
        console.log(item);
        try {
          console.log(item.getAttribute('class'));
          if (item.getAttribute('class').indexOf('result_tag') != -1) {
            tagDom = item;
            return true;
          }
        } catch (error) {}
      });
      this.enterCheckRow.itemTags.map((item, idx, arr) => {
        if (item.resultId == tag.resultId) {
          arr.splice(idx, 1);
          isExist = true;
          editMode = 3;
          rowTag = item;
        }
      });
      console.log(tagDom);
      if (!isExist) {
        rowTag = {
          id: 0,
          itemCode: this.enterCheckRow.itemCode,
          tag: tag.isSelectable ? tagDom?.innerText : tag.resultDesc,
          abnormalType: tag.abnormalType,
          resultId: tag.resultId,
          isCalcResul: false,
          calcItemTagIds: []
        };
        this.enterCheckRow.itemTags.push(rowTag);
        let cell =
          this.$refs['itemTags_' + this.checkCellIdx].parentNode.parentNode;
        this.showPopupFun('resultPopup_Ref', cell, { view: window });
        console.log(
          this.checkRowIndex,
          this.$refs['inpFocus_' + this.checkRowIndex]
        );
        this.$refs['inpFocus_' + this.checkRowIndex].wrapClick();
      }
      // return;
      this.paramFun(editMode, rowTag);
      this.abnormalLevel();
      if (!isExist) {
        return;
      }
      setTimeout(() => {
        let cell =
          this.$refs['itemTags_' + this.checkCellIdx].parentNode.parentNode;
        console.log(cell);
        this.showPopupFun('resultPopup_Ref', cell, { view: window });
      }, 500);
    },
    // 表格内的删除结果
    tableTagClose(tag, idx) {
      this.enterCheckRow.itemTags.splice(idx, 1);
      this.paramFun(3, tag);
      this.abnormalLevel();
    },
    // 生成小结
    createSummary(tag) {
      let isHave = false;
      let delIdx = null;
      this.$parent.dynamicTags.map((item, idx) => {
        if (item.tag == '未见明显异常') {
          delIdx = idx;
        }
        if (
          tag.tag == item.tag ||
          (item.isCustom && item.bindItemTags.includes(tag.id))
        ) {
          isHave = true;
        }
      });
      // 删除 未见明显异常 小结
      if (delIdx !== null) {
        this.$parent.dynamicTags.splice(delIdx, 1);
      }
      if (isHave) return;
      this.$parent.dynamicTags.push({
        bindItemTags: [tag.id],
        id: 0,
        isCustom: true,
        tag: tag.tag
      });
    },
    // 表格内的结果点击回调
    tableTagClick(e, tag, idx) {
      console.log(tag);
      this.editResult = tag;
      this.$refs.editPopup_Ref.sightWordVal = tag.tag;
      this.resultPopup = false;
      this.isDefineClick = false;
      this.editPopup = true;
      this.maskShow = true;
      console.log(1111);
      this.$nextTick(() => {
        console.log(e.target.getBoundingClientRect());
        let tagCoord = e.target.getBoundingClientRect();
        let popupCoord =
          this.$refs.editPopup_Ref.$refs.editPopupDom_Ref.getBoundingClientRect();
        this.left = tagCoord.left;
        this.isOvertop =
          e.view.innerHeight -
          tagCoord.top -
          tagCoord.height -
          popupCoord.height;

        if (tagCoord.top < 0) {
          this.top = 50;
          return;
        }
        if (this.isOvertop >= 0) {
          this.top = tagCoord.top + tagCoord.height - 1;
        } else {
          // this.top = tagCoord.top - popupCoord.height + 1;
          this.top = window.innerHeight - tagCoord.top;
        }
      });
    },
    // 清空异常状态
    abnormalLevelClear() {
      this.enterCheckRow.abnormalType = 0;
    },
    // 清空项目的结果
    resultClear() {
      // console.log(this.enterCheckRow);
      this.$confirm(`是否清空${this.enterCheckRow.itemName}的结果？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          console.log(888);
          this.fixed_enterCheckRow.itemTags = [];
          this.fixed_enterCheckRow.numberResult = '';
        })
        .catch(() => {});
    },
    // 处理每个项目结果的数组
    disposeEveryProject(arr) {
      let txt = '';
      arr.map((item, idx) => {
        txt += `${idx == 0 ? '' : '；'}` + item.tag;
      });
      return txt;
    },
    // 自定义的点击事件
    customClick() {
      console.log(this.enterCheckRow);

      // this.resultPopup = false;
      this.$refs.editPopup_Ref.sightWordVal = '';
      this.isDefineClick = true;
      this.editPopup = true;
    },
    // 常用词的开启或关闭编辑状态
    editOutlineClick() {
      this.isSightWordEdit = !this.isSightWordEdit;
    },
    // 获取光标的位置
    handleInputBlur(e) {
      this.cursorIndex = e.srcElement.selectionStart;
      console.log(this.cursorIndex);
    },
    // 选择常用词
    sightWordClick(item) {
      this.sightWordVal =
        this.sightWordVal.slice(0, this.cursorIndex) +
        item.words +
        this.sightWordVal.slice(this.cursorIndex);
      this.cursorIndex += item.words.length;
      // this.$refs.sightWordInput_Ref.focus()
      var a = this.$refs.sightWordInput_Ref.$el.querySelector('input');
      a.focus();
      this.$nextTick(() => {
        a.setSelectionRange(this.cursorIndex, this.cursorIndex);
      });
    },
    // 编辑输入框的回车回调
    sightWordInputEnter() {
      if (this.sightWordVal.trim() === '') {
        this.$message({
          message: '结果不能为空！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.isDefineClick) {
        let tag = {
          id: 0,
          itemCode: this.enterCheckRow.itemCode,
          tag: this.sightWordVal,
          abnormalType: 0,
          resultId: 0,
          isCalcResul: false,
          calcItemTagIds: []
        };
        this.enterCheckRow.itemTags.push(tag);
        this.paramFun(1, tag);
      } else {
        this.editResult.tag = this.sightWordVal;
        this.paramFun(2, this.editResult);
      }
    },
    // 显示新增常用词的输入框
    showInput() {
      this.isAddSightWord = true;
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    // 确定新增常用词
    handleInputConfirm() {
      if (this.addSightWordVal) {
        let datas = {
          deptCode: this.combData.recordComb.examDeptCode,
          words: this.addSightWordVal
        };
        this.$ajax.post(this.$apiUrls.CreateCodeDeptWord, datas).then((r) => {
          this.getReadCodeDeptWord();
        });
      }
      this.isAddSightWord = false;
      this.addSightWordVal = '';
    },
    // 删除常用词
    sightWordClose(item) {
      this.$ajax.post(this.$apiUrls.DeleteCodeDeptWord, [item]).then((r) => {
        this.getReadCodeDeptWord();
      });
    },
    // 表格展开行的回调
    expandChange({ row }) {
      console.log(row);
      let datas = {
        cardNo: this.headerInfo.cardNo,
        itemCode: row.itemCode
      };
      this.$ajax.post(this.$apiUrls.GetItemHistoryResult, datas).then((r) => {
        this.renderChart(row, r.data.returnData || []);
      });

      // this.$nextTick(()=>{

      // })
    },
    // 渲染折线图
    renderChart(row, returnData) {
      let titleArr = [];
      let seriesData = [];
      let historyList = [{}];
      returnData.map((item) => {
        titleArr.push(item.examTime);
        seriesData.push(item.itemResult);
        historyList[0][item.examTime] = item.itemResult;
      });
      row.historyTheads = ['日期', ...titleArr];
      row.historyList = [{ 日期: '结果', ...historyList[0] }];
      console.log(row.historyList);
      var chartDom = document.getElementById('project_' + row.itemCode);
      console.log(chartDom);
      if (!chartDom) return;
      var myChart = echarts.init(chartDom);
      var option;

      option = {
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: titleArr
        },
        yAxis: {
          type: 'value'
        },
        tooltip: {
          trigger: 'axis',
          formatter: `{b} <br/>{c0} ${row.unit}` //此处显示单位使用，a0代表显示的第一个圆柱，以此类推
        },
        series: [
          {
            data: seriesData,
            type: 'line'
          }
        ]
      };
      option && myChart.setOption(option);
    },
    // 根据项目代码获取结果列表
    getResultList(itemCode) {
      this.normalResult = [];
      this.abnormalResult = [];
      this.$ajax
        .paramsPost(this.$apiUrls.GetDefaultItemResultByItem, {
          itemCode
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          returnData?.resultDiseases.map((item) => {
            // 正常结果
            if (item.abnormalType === 0) {
              this.normalResult.push(item);
            }
            // 异常结果
            if (item.abnormalType !== 0) {
              this.abnormalResult.push(item);
            }
          });
        });
    },
    // 数值类型输入框的回调
    async numResultBlur(row, rowInfo) {
      // await this.nextRowCurrent(rowInfo)
      let editMode = 1;
      this.$parent.resultState = 2;
      let oldRowInfo = this.oldRowInfo;
      console.log(oldRowInfo);
      if (!row.numberResult) {
        row.itemTags = [];
      } else {
        if (row.itemTags.length == 0) {
          row.itemTags = [
            {
              id: row.itemTags.length == 0 ? null : row.itemTags[0].id,
              itemCode: row.itemCode,
              tag: row.numberResult,
              abnormalType: 0,
              resultId: 0,
              isCalcResul: false,
              calcItemTagIds: []
            }
          ];
        } else {
          let itemTag = row.itemTags[0];
          row.itemTags = [
            {
              id: itemTag.id,
              itemCode: row.itemCode,
              tag: row.numberResult,
              abnormalType: itemTag.abnormalType,
              resultId: itemTag.resultId,
              isCalcResul: itemTag.isCalcResul,
              calcItemTagIds: itemTag.calcItemTagIds
            }
          ];
        }
      }

      // debugger
      console.log(row);
      console.log(this.oldRowInfo.itemTags);
      console.log(
        this.oldRowInfo.itemTags.length,
        row.itemTags[0]?.tag,
        this.oldRowInfo.itemTags[0]?.tag
      );
      console.log(row, this.oldRowInfo);
      if (row.numberResult !== this.oldRowInfo.numberResult) {
        if (row.itemTags.length != 0 && oldRowInfo.itemTags.length == 0) {
          editMode = 1;
        }
        if (
          oldRowInfo.itemTags.length != 0 &&
          row.itemTags[0]?.tag != oldRowInfo.itemTags[0]?.tag
        ) {
          editMode = 2;
        }
        if (row.itemTags.length == 0 && oldRowInfo.itemTags.length != 0) {
          editMode = 3;
        }
        if (row.itemTags.length == 0 && oldRowInfo.itemTags.length == 0) {
          return;
        }
        let datas = {
          editMode: editMode, //1 添加，2修改，3删除
          currentItemTagCopy:
            editMode == 3 ? oldRowInfo.itemTags[0] : row.itemTags[0],
          // {
          //   id: editMode==2 || editMode==3?this.oldRowInfo.itemTags[0]?.id : 0,
          //   itemCode: row.itemCode,
          //   tag: row.numberResult,
          //   resultId: 0,
          //   bindDiseaseCode: "",
          //   isCalcResul: false,
          //   calcItemTagIds: []
          // },
          recordComb: {
            recordComb: this.combData.recordComb,
            combTags: this.$parent.dynamicTags,
            recordItems: this.combData.recordItems
          }
        };
        console.log(datas);
        // return
        this.$ajax
          .post(this.$apiUrls.EditItemTag, datas)
          .then(async (r) => {
            let { success, returnData } = r.data;
            if (editMode != 3) {
              row.itemTags[0] = returnData.currentItemTag;
            }
            console.log(this.combData.recordItems);
            await this.getRelevancyCallback(returnData);
            this.$parent.resultState = 1;
          })
          .catch((e) => {
            this.$parent.resultState = 1;
            console.log(this.combData.recordItems);
          });
        this.C_abnormal(row);
      } else {
        this.$parent.resultState = 1;
      }
      // this.$nextTick(()=>{
      //   console.log(rowInfo);
      //   this.nextRowCurrent(rowInfo);
      // })
    },
    // 获取疾病列表的参数封装
    paramFun(editMode, tag) {
      console.log(tag);
      let datas = {
        editMode: editMode, //1 添加，2修改，3删除
        currentItemTagCopy: tag,
        recordComb: {
          recordComb: this.combData.recordComb,
          combTags: this.$parent.dynamicTags,
          recordItems: this.combData.recordItems
        }
      };
      console.log(datas);
      this.$ajax.post(this.$apiUrls.EditItemTag, datas).then((r) => {
        let { success, returnData } = r.data;
        if (editMode != 3) {
          tag.id = returnData.currentItemTag?.id;
          (tag.itemCode = returnData.currentItemTag?.itemCode),
            (tag.tag = returnData.currentItemTag?.tag),
            (tag.resultId = returnData.currentItemTag?.resultId),
            (tag.isCalcResul = returnData.currentItemTag?.isCalcResul),
            (tag.calcItemTagIds = returnData.currentItemTag?.calcItemTagIds);
          tag.abnormalType = returnData.currentItemTag?.abnormalType;
        }
        this.getRelevancyCallback(returnData);
      });
    },
    // 获取疾病关联后的回调
    getRelevancyCallback(returnData) {
      console.log(this.C_combData);

      // 删除的结果
      returnData.otherDelItemTags.map((item) => {
        let idx = this.C_combData.indexOf(item.itemCode);
        let row = this.combData.recordItems[idx];

        if (row.resultType == 1) {
          row.numberResult = '';
        }
        row.itemTags?.some((twoItem, twoIdx, arr) => {
          if (twoItem.id == item.id) {
            arr.splice(twoIdx, 1);
            return true;
          }
        });
      });
      // 修改的结果
      returnData.otherAlterItemTags.map((item) => {
        let idx = this.C_combData.indexOf(item.itemCode);
        let row = this.combData.recordItems[idx];

        if (row.resultType == 1) {
          row.numberResult = item.tag;
        }
        row.itemTags?.some((twoItem, twoIdx, arr) => {
          console.log(twoItem.id == item.id);
          if (twoItem.id == item.id) {
            twoItem.tag = item.tag;
            return true;
          }
        });
      });
      // 添加的结果
      let itemTags = [];
      returnData.otherAddItemTags.map((item) => {
        let idx = this.C_combData.indexOf(item.itemCode);
        let row = this.combData.recordItems[idx];
        console.log(item, row);
        // row.itemTags.push(item);

        if (row.resultType == 1) {
          itemTags.push(item);
          row.itemTags = itemTags;
          row.numberResult = item.tag;
          console.log(item.tag);
        } else {
          row.itemTags.push(item);
        }
        if (item.itemCode === this.oldRowInfo.itemCode) {
          this.oldRowInfo = dataUtils.deepCopy(row);
        }
      });

      console.log(this.combData);
      // 小结
      this.$parent.dynamicTags = returnData.combTags;
    },
    // 根据选择的结果获取异常程度
    getAbnormalFun() {
      let grade = 0;
      // this.enterCheckRow.itemTags.map(item=>{
      //   if(grade < item)
      // })
    },
    // 获取常用词
    getReadCodeDeptWord() {
      console.log(this.combData);
      let datas = {
        deptCode: this.combData.recordComb.examDeptCode
      };
      this.$ajax.paramsPost(this.$apiUrls.ReadCodeDeptWord, datas).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.sightWordList = returnData || [];
      });
    },
    // 获取项目历史
    getItemHistory() {
      let datas = {
        cardNo: this.headerInfo.cardNo,
        itemCode: ''
      };
    },
    // 处理字符串里面的数组规则
    strFun() {
      let txt =
        'dfgfd[二尖瓣区,三尖瓣区,主动脉区,肺动脉区,胸骨左缘Ⅱ-Ⅲ肋间,胸骨左缘Ⅲ-Ⅳ肋间][收缩期，舒张期][I级,II级,III级,IV级,V级,VI级][隆隆样，叹息样，吹风样]杂音';
      let reg = /\[([^\[\]]*)\]/g;
      let idx = 0;
      let text = txt.replace(reg, (r, i) => {
        let nTxt = `@select${idx}@`;
        idx += 1;
        return nTxt;
      });

      let txtArr = text.split('@');
      let nTxtArr = [];
      txtArr.map((item) => {
        if (item != '') {
          nTxtArr.push(item);
        }
      });
      console.log(nTxtArr);
    },
    // 聚焦表格下一行
    nextRowCurrent(scope) {
      setTimeout(() => {
        let xTable = this.$refs.xTable;
        // console.log(scope);
        let rowIdx = scope.rowIndex === null ? 0 : scope.rowIndex + 1;
        if (rowIdx >= this.combData.recordItems?.length) {
          rowIdx = 0;
        }
        this.checkRowIndex = rowIdx;
        console.log(this.checkCellIdx);
        console.log(this.combData);
        if (this.combData.recordItems?.length <= rowIdx) {
          this.$refs['inpFocus_' + scope.rowIndex].blur();
          return;
        }
        let cell = this.$refs['itemTags_' + rowIdx].parentNode.parentNode;
        this.$refs.xTable.setCurrentRow(this.combData.recordItems[rowIdx]);

        let row = xTable.getCurrentRecord();
        let column = { property: 'itemTags' };
        let $event = {
          view: window
        };

        console.log(this.$refs['inpFocus_' + rowIdx].focus);
        if (this.$refs['inpFocus_' + rowIdx].focus) {
          this.cellClick({ row, column, cell, $event, rowIndex: rowIdx });
          this.$refs['inpFocus_' + rowIdx].focus();
        } else {
          this.cellClick({ row, column, cell, $event, rowIndex: rowIdx });
          this.$refs['inpFocus_' + rowIdx].wrapClick();
        }
      }, 50);
    },
    // 输入框回车的回调
    inputEnter(e) {
      console.log(e);
      e.target.blur();
    },
    currentChange({ row, rowIndex }) {
      console.log(row);
      this.checkRowIndex = rowIndex;
    },
    // 键盘按下事件
    keyDownFun(e) {
      let keyArr = Object.keys(this.shortcutList);
      if (keyArr.includes(e.keyCode + '')) {
        e.preventDefault();
      }
    },
    // 表格的键盘事件
    keyUpFun(e) {
      e.preventDefault();
      e.stopPropagation();
      let keyArr = Object.keys(this.shortcutList);
      // console.log(e);
      let code = e.keyCode.toString();
      // console.log(code);
      let idx = keyArr.indexOf(code);
      // console.log(idx);
      if (idx != -1 && this.isModify) {
        if (
          this.checkRowIndex !== null &&
          !this.$refs['inpFocus_' + this.checkRowIndex].focus &&
          JSON.stringify(this.enterCheckRow) !== '{}'
        ) {
          let txt =
            this.$refs['inpFocus_' + this.checkRowIndex].$refs.input_Ref
              .innerText;
          console.log(txt);
          if (txt !== '') {
            let tag = {
              id: 0,
              itemCode: this.enterCheckRow.itemCode,
              tag: txt,
              abnormalType: 0,
              resultId: 0,
              isCalcResul: false,
              calcItemTagIds: []
            };
            this.enterCheckRow.itemTags.push(tag);
            this.paramFun(1, tag);
            this.$refs[
              'inpFocus_' + this.checkRowIndex
            ].$refs.input_Ref.innerHTML = '';
          }
        }
        // this.$nextTick(()=>{
        //   this.nextRowCurrent({rowIndex:this.checkRowIndex});
        // })
      }
    }
  },
  created() {
    console.log(this.G_btnAuthList);
    this.strFun();
  },
  mounted() {
    addEventListener('keyup', this.keyUpFun);
    addEventListener('keydown', this.keyDownFun);
  },
  activated() {
    if (this.isOpenKeysFlag) return;
    addEventListener('keyup', this.keyUpFun);
    addEventListener('keydown', this.keyDownFun);
  },
  beforeDestroy() {
    removeEventListener('keyup', this.keyUpFun);
    removeEventListener('keydown', this.keyDownFun);
  },
  deactivated() {
    removeEventListener('keyup', this.keyUpFun);
    removeEventListener('keydown', this.keyDownFun);
  }
};
</script>

<style lang="less" scoped>
.examine_page {
  overflow: auto;
  position: relative;
  .examine_mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10;
  }
  .this_result {
    position: relative;
    z-index: 11;
    .candidate_btn {
      // border-radius: 100%;
      // border:1px solid #1770df;
      // height: 25px;
      // width: 25px;
      // text-align: center;
      // line-height: 23px;
      position: absolute;
      top: -8px;
      right: -8px;
      font-size: 16px;
      // color: #1770df;
      cursor: pointer;
    }
  }
  .reference_div {
    display: -webkit-box;
    -webkit-line-clamp: 3; /* 显示行数 */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .qijian_img {
    position: fixed;
    left: 50%;
    transform: translate(-50%, -50%);
    top: 50%;
    z-index: 5;
  }
  .fold_wrap {
    padding: 10px 0;
  }
  /deep/ .row--current {
    background: #fff;
    td {
      border-top: 2px solid #1770df;
      border-bottom: 2px solid #1770df;
      &:first-child {
        border-left: 2px solid #1770df;
      }
      &:last-child {
        border-right: 2px solid #1770df;
      }
    }
  }
  .popup_style {
    background: #f7f8f9;
    position: fixed;
    z-index: 1000;
    border: 1px solid #b2bec3;
    border-radius: 2px;
    height: 250px;
    overflow: auto;
  }

  .select_popup {
    min-width: 100px;

    p {
      height: 38px;
      line-height: 38px;
      padding: 0 9px;
      font-size: 14px;
      cursor: pointer;
      color: #2d3436;
    }
  }

  .select_icon {
    position: absolute;
    top: 50%;
    right: 5px;
    transform: translateY(-50%);
    cursor: pointer;
  }
  .edit_popup,
  .result_popup {
    width: 350px;
    padding: 10px 15px;
  }

  .result_style {
    h3 {
      font-size: 14px;
      color: #1770df;
    }

    .result_wrap {
      padding-top: 10px;
    }

    .result_tag {
      margin-bottom: 10px;
      margin-right: 10px;
      color: #000;
      cursor: pointer;
      max-width: 100%;
      white-space: pre-wrap;
      height: auto;
      min-height: 32px;
      &:last-child {
        margin-right: 0;
      }
    }

    .normal_active {
      background: #1770df;
      color: #fff;
      border-color: #1770df;
    }

    .abnormal_active {
      background: #d63031;
      color: #fff;
      border-color: #d63031;
    }
  }
  .sight_word {
    h3 {
      font-size: 14px;
      i {
        color: #1770df;
        font-size: 18px;
        cursor: pointer;
      }
    }
    .sightWord_wrap {
      padding-top: 10px;
    }
    .sightWord_tag {
      margin-bottom: 10px;
      margin-right: 10px;
      color: #000;
      cursor: pointer;
      &:last-child {
        margin-right: 0;
      }
    }
  }
  .sightWord_input {
    padding: 15px 0;
  }
  .abnormal_result {
    h3 {
      color: #d63031;
    }
  }

  .table_tag {
    margin-right: 5px;
    margin-bottom: 5px;
    max-width: 100%;
    white-space: pre-wrap;
    height: auto;
    min-height: 32px;
    font-size: 16px;
    padding: 0 5px;
    cursor: pointer;
    .tag_i {
      display: none;
      color: #35dc35;
      &:last-child {
        color: #d63031;
      }
    }
    &:hover .tag_i {
      display: inline-block;
    }
  }
  .abnormal_level {
    span {
      color: #d63031;
    }
  }
  .abnormal_span {
    color: #d63031;
    font-size: 24px;
  }
  .abnormal_span_txt {
    color: #d63031;
  }
  .tag_span_select {
    i {
      font-style: normal;
    }
  }
  /deep/ .vxe-cell {
    font-weight: bold;
    font-size: 16px;
  }
  /deep/ .el-table__cell {
    font-weight: bold;
    font-size: 16px;
  }
}
</style>
<style lang="less">
.examine_page {
  .el-table__body-wrapper {
    overflow-y: auto;
  }
}
</style>
