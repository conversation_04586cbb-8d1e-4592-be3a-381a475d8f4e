<template>
  <div class="choosePicture">
    <div class="headInfo">
      <div class="typeHeader">
        <div class="every_inp comb_sel">
          <label>组合名称:</label>
          <p>
            <el-select
              placeholder="请选择"
              size="small"
              v-model.trim="regCombId"
              filterable
              @change="combChange"
            >
              <el-option
                v-for="item in combArr"
                :value="item.regCombId"
                :label="item.combName"
                :key="item.combCode"
              >
              </el-option>
            </el-select>
          </p>
        </div>
      </div>
      <BtnCommon
        :btnList="['选中', '取消', '导入', '导出', '删除']"
        @chooses="checkPacsImage2Print"
        @cancels="uncheckPacsImage2Print"
        @importFile="importFile"
        @deletes="deleteCollectedPacsImage"
      />
    </div>
    <div class="bodyDiv">
      <div class="leftDiv">
        <p class="pCss">
          <span>全部图片：</span>
          <span class="lenColor">{{ imgInfoList.length }}</span>
        </p>
        <div class="img-box">
          <el-checkbox-group v-model="checkedVal" @change="handleCheckedChange">
            <el-checkbox
              v-for="(img, index) in imgInfoList"
              :key="index"
              :label="img.imagePath"
              @click="imgLeftClick(img.imagePath, index)"
              :disabled="Boolean(img.combCode)"
            >
              <div class="imgDiv">
                <el-image
                  :class="imgActIdx === index ? 'itemActIdx' : ''"
                  :src="img.imagePath"
                  lazy
                ></el-image>
                <div class="imgName">
                  <i
                    class="iconfont icon-zhifuchenggong cellBlue"
                    v-show="img.recImageId"
                  ></i
                  >{{ img.combName }}
                </div>
              </div>
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
      <div class="centerDiv">
        <p class="pCss">放大选中图片：</p>
        <div class="pictures-box">
          <el-image :src="bigImage" v-show="bigImage"></el-image>
        </div>
        <div class="centerBtn">
          <el-button
            class="blue_btn btn"
            size="small"
            style="background: #1770df"
            icon="iconfont icon-Leftxiangzuo35"
            :disabled="isdisabledDel"
            @click="unchecked"
            >取消选中</el-button
          >
          <el-button
            class="blue_btn btn"
            size="small"
            style="background: #1770df"
            icon="iconfont icon-Rightxiangyou34"
            :disabled="isdisabledAdd"
            @click="checkCollected"
            >确认选中</el-button
          >
        </div>
      </div>
      <div class="rightDiv">
        <div class="pCss">
          <span>已选中图片：</span>
          <span class="lenColor">{{ rightImgList.length }}</span>
        </div>
        <div class="img-box">
          <el-checkbox-group
            v-model="rightCheckedVal"
            @change="rightCheckedChange"
          >
            <el-checkbox
              v-for="(img, idx) in rightImgList"
              :key="idx"
              :label="img.imagePath"
            >
              <div class="imgDiv">
                <el-image
                  :class="imgActIdx === idx ? 'itemActIdx' : ''"
                  :src="img.imagePath"
                  lazy
                  @click="imgRightClick(img.imagePath, idx)"
                ></el-image>
                <div class="imgName">
                  <i
                    class="iconfont icon-zhifuchenggong cellBlue"
                    v-show="img.recImageId"
                  ></i
                  >{{ img.combName }}
                </div>
              </div>
            </el-checkbox>
          </el-checkbox-group>
          <!-- <div class="imgDiv" v-for="(imagePath, index) in rightImgList" :key="index">
            <el-image
              :class="imgActIdx === index ? 'itemActIdx' : ''"
              :src="imagePath.imagePath"
              @click="imgRightClick(imagePath.imagePath, index)"
            ></el-image>
            <div class="imgName">
              <i
                class="iconfont icon-zhifuchenggong cellBlue"
                v-show="index % 2 == 1"
              ></i
              >{{ imagePath.combName }}
            </div>
          </div> -->
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import BtnCommon from '../btnCommon.vue';
import { mapGetters } from 'vuex';
export default {
  name: 'choosePicture',
  components: { BtnCommon },
  props: {
    //行信息
    headerInfo: {
      typeof: Object,
      default: {}
    },
    propRegCombId: {
      typeof: String,
      default: ''
    },
    regCombs: {
      type: Array,
      default: () => {
        return [];
      }
    },
    deptCode: {
      typeof: String,
      default: ''
    }
  },
  data() {
    return {
      combArr: this.regCombs,
      typeHead: this.headerInfo,
      imgActIdx: '',
      imgActive: '',
      isdisabledDel: false,
      isdisabledAdd: false,
      imgInfoList: [],
      rightImgList: [],
      bigImage: '',
      checkedVal: [],
      rightCheckedVal: [],
      idArr: [],
      regCombId: this.propRegCombId
      //  deptCode: "",
    };
  },
  created() {},
  mounted() {
    // this.getPacsDeptCode();
    this.getCollectedPacsImage();
    this.getRightPacsImage();
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_userInfo'])
  },
  methods: {
    //采集
    importFile(event) {
      let file = event.target.files[0];
      let formData = new FormData();
      formData.append('files', file);
      this.$ajax
        .post(
          this.$apiUrls.SaveCollectedPacsImage +
            '?regNo=' +
            this.typeHead.regNo +
            '&regCombId=' +
            this.regCombId +
            '&operatorCode=' +
            this.G_userInfo.codeOper.operatorCode +
            '&deptCode=' +
            this.deptCode,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
              Authorization: ''
            }
          }
        )
        .then((r) => {
          let { success } = r.data;
          if (!success) return;
          this.getCollectedPacsImage();
          this.getRightPacsImage();
        });
    },
    //删除右边采集
    deleteCollectedPacsImage() {
      if (this.idArr.length < 1) {
        this.$message({
          message: '请先选择要删除采集的图片!',
          type: 'warning'
        });
        return false;
      }
      this.$confirm(`是否确定删除?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax
            .post(this.$apiUrls.DeleteCollectedPacsImage, this.idArr, {
              query: {
                regNo: this.typeHead.regNo,
                operatorCode: this.G_userInfo.codeOper.operatorCode
              }
            })
            .then((r) => {
              let { success } = r.data;
              if (!success) return;
              this.$message({
                message: '删除成功',
                type: 'success'
              });
              this.getCollectedPacsImage();
              this.getRightPacsImage();
              this.checkedVal = [];
              this.bigImage = '';
            });
        })
        .catch(() => {});
    },
    //取消选中
    unchecked() {
      if (this.idArr.length < 1) {
        this.$message({
          message: '请先勾选需要取消的图片!',
          type: 'warning',
          showClose: true
        });
        return false;
      }
      this.$ajax
        .post(this.$apiUrls.UncheckCollectedPacsImage4Comb, this.idArr, {
          query: {
            operatorCode: this.G_userInfo.codeOper.operatorCode
          }
        })
        .then((r) => {
          let { success } = r.data;
          if (!success) return;
          this.$message({
            message: '取消选中成功',
            type: 'success'
          });
          this.getCollectedPacsImage();
          this.getRightPacsImage();
          this.rightCheckedVal = [];
        });
    },
    //确定选中
    checkCollected() {
      if (this.idArr.length < 1) {
        this.$message({
          message: '请先勾选需要选中的图片!',
          type: 'warning',
          showClose: true
        });
        return false;
      }
      if (!this.regCombId) {
        this.$message({
          message: '请先选择组合!',
          type: 'warning',
          showClose: true
        });
        return false;
      } else {
        this.$ajax
          .post(this.$apiUrls.CheckCollectedPacsImage4Comb, this.idArr, {
            query: {
              regNo: this.typeHead.regNo,
              regCombId: this.regCombId,
              operatorCode: this.G_userInfo.codeOper.operatorCode
            }
          })
          .then((r) => {
            let { success } = r.data;
            if (!success) return;
            this.$message({
              message: '确定选中成功',
              type: 'success'
            });
            this.getCollectedPacsImage();
            this.getRightPacsImage();
            this.checkedVal = [];
          });
      }
    },

    imgLeftClick(imagePath, index) {
      this.imgActIdx = index;
      this.bigImage = imagePath;
      this.isdisabledDel = true;
      this.isdisabledAdd = false;
      this.imgActive = '';
      const isIncludes = this.rightImgList.filter((item) =>
        imagePath.includes(item.imagePath)
      );
      if (isIncludes.length > 0) {
        this.isdisabledDel = true;
        this.isdisabledAdd = true;
      }
    },
    imgRightClick(imagePath, index) {
      this.imgActive = index;
      this.bigImage = imagePath;
      this.isdisabledDel = false;
      this.isdisabledAdd = true;
      this.imgActIdx = '';
    },
    //获取pacs科室代码
    // getPacsDeptCode() {
    //   this.$ajax.post(this.$apiUrls.GetPacsDept).then((r) => {
    //
    //     let { success, returnData } = r.data;
    //     if (!success) return;
    //     this.deptCode = returnData.value;
    //     this.getPacsComb();
    //   });
    // },
    //获取pacs组合
    // getPacsComb() {
    //   this.$ajax
    //     .post(this.$apiUrls.GetPacsComb, "", {
    //       query: { regNo: this.typeHead.regNo, deptCode: this.deptCode },
    //     })
    //     .then((r) => {
    //
    //       let { success, returnData } = r.data;
    //       if (!success) return;
    //       this.combArr = returnData || [];
    //       this.getCollectedPacsImage();
    //       this.getRightPacsImage();
    //     });
    // },

    combChange(val) {
      this.regCombId = val;
      this.getRightPacsImage();
    },
    //获取采集全部的pacs图像
    getCollectedPacsImage() {
      this.$ajax
        .post(this.$apiUrls.GetCollectedPacsImage, '', {
          query: {
            regNo: this.typeHead.regNo,
            regCombId: 0,
            deptCode: this.deptCode
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          if (returnData.length >= 1) {
            returnData.forEach((item, index) => {
              this.combArr.forEach((items) => {
                if (item.combCode == items.combCode) {
                  return (returnData[index].combName = items.combName);
                }
              });
              return (item.imagePath =
                this.$config.fileUrl + item.imagePath.replace(/\\/g, '/'));
            });
            this.imgInfoList = returnData;
          } else {
            this.imgInfoList = [];
          }
        });
    },
    //右边数据
    getRightPacsImage() {
      this.$ajax
        .post(this.$apiUrls.GetCollectedPacsImage, '', {
          query: {
            regNo: this.typeHead.regNo,
            regCombId: this.regCombId,
            deptCode: this.deptCode
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          if (returnData.length >= 1) {
            returnData.forEach((item, index) => {
              this.combArr.forEach((items) => {
                if (item.combCode == items.combCode) {
                  return (returnData[index].combName = items.combName);
                }
              });
              return (item.imagePath =
                this.$config.fileUrl + item.imagePath.replace(/\\/g, '/'));
            });
            this.rightImgList = returnData;
          } else {
            this.rightImgList = [];
          }
        });
    },
    //全部图片选择勾选
    handleCheckedChange(val) {
      if (val.length < 1) {
        return (this.bigImage = '');
      }
      this.rightCheckedVal = [];
      this.imgActive = '';
      this.checkedVal = val;
      let lastImg = this.checkedVal.slice(-1);
      this.idArr = [];
      //获取图片对应id集合
      this.imgInfoList.forEach((item, index) => {
        this.checkedVal.forEach((items, idx) => {
          if (items == item.imagePath) {
            if (lastImg == this.checkedVal[idx]) {
              this.imgActIdx = idx;
              this.bigImage = this.checkedVal[idx];
            }
            this.idArr.push(item.id);
          }
        });
      });
      console.log('[ idArr ]-174', this.idArr);
      this.isdisabledDel = true;
      this.isdisabledAdd = false;
    },
    //已选图片选择勾选
    rightCheckedChange(val) {
      if (val.length < 1) {
        return (this.bigImage = '');
      }
      this.recImageIdArr = [];
      this.checkedVal = [];
      this.imgActIdx = '';
      this.rightCheckedVal = val;
      console.log('[ this.rightCheckedVal ]-308', this.rightCheckedVal);
      let lastImg = this.rightCheckedVal.slice(-1);
      this.idArr = [];
      //获取图片对应id集合
      this.imgInfoList.forEach((item, index) => {
        this.rightCheckedVal.forEach((items, idx) => {
          if (items == item.imagePath) {
            if (lastImg == this.rightCheckedVal[idx]) {
              this.imgActive = idx;
              this.bigImage = this.rightCheckedVal[idx];
            }
            this.idArr.push(item.id);
            this.recImageIdArr.push(item.recImageId);
          }
        });
      });
      console.log('[ this.idArr ]-414', this.idArr);
      this.isdisabledDel = false;
      this.isdisabledAdd = true;
    },

    //取消选中组合中的采集的pacs图像用于报告打印
    uncheckPacsImage2Print() {
      this.$ajax
        .post(this.$apiUrls.UncheckPacsImage2Print, this.idArr, {
          query: {
            operatorCode: this.G_userInfo.codeOper.operatorCode
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
        });
    },
    //获取用于报告打印的pacs图像
    getPacsImage2Print() {
      this.$ajax
        .post(this.$apiUrls.GetPacsImage2Print, '', {
          query: {
            regNo: this.typeHead.regNo,
            regCombId: this.regCombId
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
        });
    },
    //保存
    //选中组合中的采集的pacs图像用于报告打印
    checkPacsImage2Print() {
      if (this.idArr.length < 1) {
        this.$message({
          message: '请先勾选需要保存的图片!',
          type: 'warning',
          showClose: true
        });
        return false;
      }
      this.$ajax
        .post(this.$apiUrls.CheckPacsImage2Print, this.idArr, {
          query: {
            operatorCode: this.G_userInfo.codeOper.operatorCode
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          if (returnData) {
            this.$message({
              message: '成功',
              type: 'success'
            });
          }
          this.getCollectedPacsImage();
          this.getRightPacsImage();
          this.rightCheckedVal = [];
        });
    },
    //取消保存
    uncheckPacsImage2Print() {
      if (this.recImageIdArr.length < 1) {
        this.$message({
          message: '请先选择要取消的图片!',
          type: 'warning'
        });
        return false;
      }
      let recImageIdArr = this.recImageIdArr.filter((item) => {
        return item !== null;
      });
      this.$confirm(`是否确定取消?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax
            .post(this.$apiUrls.UncheckPacsImage2Print, recImageIdArr, {
              query: {
                operatorCode: this.G_userInfo.codeOper.operatorCode
              }
            })
            .then((r) => {
              let { success } = r.data;
              if (!success) return;
              this.$message({
                message: '取消成功',
                type: 'success'
              });
              this.getCollectedPacsImage();
              this.getRightPacsImage();
              this.rightCheckedVal = [];
            });
        })
        .catch(() => {});
    }
  }
};
</script>
<style lang="less" scoped>
.choosePicture {
  height: 100%;
  color: #2d3436;
  display: flex;
  flex-direction: column;
  padding: 0 18px 18px 18px;
  .headInfo {
    line-height: 56px;
    display: flex;
    flex-direction: row;
    .typeHeader {
      flex: 1;
      display: flex;
      flex-direction: row;
      font-family: PingFangSC-Medium;
      font-size: 14px;
      color: #2d3436;
      .every_inp {
        display: flex;
        flex-direction: row;
        label {
          margin-right: 10px;
          width: 70px;
          text-align: right;
          font-weight: 600;
        }
        p {
          flex: 1;
        }
      }
    }
  }
  .bodyDiv {
    flex: 1;
    display: flex;
    flex-direction: row;
    flex-shrink: 0;
    overflow: auto;
    .leftDiv {
      width: 260px;
      display: flex;
      flex-direction: column;
      background: rgba(178, 190, 195, 0.1);
      border: 1px solid #eee;
      border-radius: 4px;
      .img-box {
        flex: 1;
        flex-shrink: 0;
        overflow: auto;
        padding: 0 18px;
        .el-checkbox {
          margin-top: 18px;
          display: flex;
          margin-right: 0;
        }
        /deep/.el-checkbox__inner {
          width: 20px;
          height: 19px;
          border: 2px solid #d0d0d0;
        }

        /deep/.el-checkbox__inner::after {
          height: 9px;
          left: 6px;
        }
        // .el-image {
        //   margin-top: 10px;
        // }
        // .itemActIdx {
        //   border: 2px solid #1770df;
        // }
      }
    }
    .imgDiv {
      width: 192px;
      height: 128px;
      margin-bottom: 10px;
    }
    .imgName {
      background: #eee;
      text-align: center;
    }
    .cellBlue {
      color: #1770df;
      margin-right: 5px;
    }
    .centerDiv {
      flex: 1;
      margin: 0 20px;
      background: rgba(178, 190, 195, 0.1);
      border: 1px solid #eee;
      border-radius: 4px;
      display: flex;
      flex-direction: column;
      .pictures-box {
        width: 100%;
        flex: 1;
        overflow: auto;
        text-align: center;
        .el-image {
          background: #fff;
          // margin: 12px;
          height: 99%;
          border: 1px solid #eee;
        }
      }
      .centerBtn {
        height: 105px;
        line-height: 105px;
        text-align: center;
      }
    }
    .rightDiv {
      width: 260px;

      display: flex;
      flex-direction: column;
      background: rgba(178, 190, 195, 0.1);
      border: 1px solid #eee;
      border-radius: 4px;
      .img-box {
        flex: 1;
        flex-shrink: 0;
        overflow: auto;
        padding: 0 18px;
        // .el-image {
        //   margin-top: 10px;
        // }
        .el-checkbox {
          margin-top: 18px;
          margin-right: 0;
          display: flex;
        }
        /deep/.el-checkbox__inner {
          width: 20px;
          height: 19px;
          border: 2px solid #d0d0d0;
        }

        /deep/.el-checkbox__inner::after {
          height: 9px;
          left: 6px;
        }
      }
      // .itemActIdx {
      //   border: 2px solid #1770df;
      // }
    }
  }

  .pCss {
    height: 38px;
    line-height: 38px;
    font-family: PingFangSC-Medium;
    font-size: 18px;
    color: #2d3436;
    background: rgba(23, 112, 223, 0.2);
    padding: 0 18px;
    display: flex;
    justify-content: space-between;
    .lenColor {
      font-family: PingFangSC-Medium;
      font-size: 18px;
      color: #1770df;
      text-align: right;
    }
  }
}
</style>
