<template>
  <div class="response">
    <div class="leftCont">
      <div class="leftTop">
        <p>我发起的会诊：</p>
        <div class="contDiv">
          <PublicTable
            :viewTableList.sync="G_chatRoomList"
            :theads.sync="leftTheads"
            :tableLoading.sync="loading"
            :columnWidth="columnWidths"
            @rowClick="rowClick"
            ref="leftTable"
          >
            <template #sex="{ scope }">
              <div>
                {{ G_EnumList['Sex'][scope.row.sex] }}
              </div>
            </template>
            <template #deptCode="{ scope }">
              <div>
                {{ G_EnumList['CodeDepartment'][scope.row.deptCode] }}
              </div>
            </template>
          </PublicTable>
        </div>
      </div>
      <div class="leftTop">
        <p>我收到的会诊：</p>
        <div class="contDiv">
          <PublicTable
            :viewTableList.sync="G_chatReceiveList"
            :theads.sync="leftTheads"
            :tableLoading.sync="loading"
            :columnWidth="columnWidths"
            @rowClick="receiveClick"
            ref="receiveTable"
          >
            <template #sex="{ scope }">
              <div>
                {{ G_EnumList['Sex'][scope.row.sex] }}
              </div>
            </template>
            <template #deptCode="{ scope }">
              <div>
                {{ G_EnumList['CodeDepartment'][scope.row.deptCode] }}
              </div>
            </template>
          </PublicTable>
        </div>
      </div>
    </div>
    <div class="rightCont">
      <div class="typeHead">
        <p>患者信息</p>
        <ul class="type_head">
          <li>
            <div class="every_inp regNo_inp">
              <label>体检号:</label>
              <p>{{ typeHead.regNo }}</p>
            </div>
            <div class="every_inp">
              <label>姓名:</label>
              <p>{{ typeHead.name }}</p>
            </div>
            <div class="every_inp">
              <label>性别:</label>
              <p>{{ G_EnumList['Sex'][typeHead.sex] }}</p>
            </div>
            <div class="every_inp">
              <label>年龄:</label>
              <p>{{ typeHead.age }}</p>
            </div>
          </li>
          <li>
            <div class="every_inp cont_inp">
              <label>会诊内容：</label>
              <p :title="typeHead.content" style="overflow-y: auto">
                {{ typeHead.content }}
              </p>
            </div>
          </li>
        </ul>
      </div>

      <div class="tableDiv">
        <p>会诊回复列表：</p>
        <div class="tableCont">
          <PublicTable
            :viewTableList.sync="G_resData"
            :theads.sync="rightTheads"
            :tableLoading.sync="loading"
            :columnWidth="columnWidths"
          >
            <template #deptCode="{ scope }">
              <div>
                {{ G_EnumList['CodeDepartment'][scope.row.deptCode] }}
              </div>
            </template>
          </PublicTable>
        </div>
      </div>
      <div class="typeCenter">
        <p>
          <span>会诊回复：</span
          ><BtnCommon :btnList="['发送']" @sendInfo="sendInfo" />
        </p>
        <div class="textareaDiv">
          <el-input
            type="textarea"
            :rows="5"
            placeholder="请输入会诊内容"
            v-model="textareaVal"
          >
          </el-input>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import PublicTable from '../../../../components/publicTable.vue';
import BtnCommon from '../btnCommon.vue';
import { mapGetters, mapMutations } from 'vuex';
export default {
  name: 'response',
  components: { BtnCommon, PublicTable },
  props: {
    //行信息
    headerInfo: {
      typeof: Object,
      default: {}
    },
    id: {
      typeof: Object,
      default: null
    },
    routeRow: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      loading: false,
      leftTheads: {
        deptCode: '发起科室',
        doctorCode: '发起医生',
        // name: "姓名",
        // sex: "性别",
        // age: "年龄",
        createTime: '发起时间',
        content: '发起内容'
      },
      leftTableData: [],
      // columnWidth: {
      //   deptCode:80,
      //   doctorCode: 80,
      //   createTime: 170
      // },
      textareaVal: '',
      typeHead: this.headerInfo,
      rightTheads: {
        deptCode: '响应科室',
        doctorCode: '医生',
        sendTime: '时间',
        content: '回复内容'
      },
      rightTableData: [],
      columnWidths: {
        deptCode: 80,
        doctorCode: 80,
        createTime: 170
      },
      row: {},
      index: 0,
      ids: this.id
    };
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_userInfo',
      'G_codeDepartment',
      'G_resData',
      'G_chatRoomList',
      'G_chatReceiveList'
    ]),
    C_chatRoomList() {
      return this.G_chatRoomList;
    }
  },
  mounted() {
    // this.$nextTick(() => {
    //   if (this.G_chatRoomList.length < 1) {
    //     return;
    //   }
    //   if (this.content) {
    //     console.log("[ this.content ]-182", this.content);
    //     this.G_chatRoomList.forEach((item, index) => {
    //       if (item.content == this.content) {
    //         this.index = index;
    //         console.log("[ this.index ]-184", this.index);
    //       }
    //     });
    //   }
    //   console.log("[ this.index ]-185", this.index);
    //   this.$refs.leftTable.$refs.tableCom_Ref.setCurrentRow(
    //     this.G_chatRoomList[this.index]
    //   );
    //   this.rowClick(this.G_chatRoomList[this.index]);
    // });
  },

  methods: {
    ...mapMutations(['M_getReadDoctorChatReply', 'M_resData', 'M_rowRes']),
    //会诊请求列表
    // getReadDoctorChat() {
    //   this.$ajax
    //     .post(this.$apiUrls.ReadDoctorChat, "", {
    //       query: { operCode: this.G_userInfo.codeOper.operatorCode },
    //     })
    //     .then((r) => {
    //       let { success, returnData } = r.data;
    //       if (!success) return;
    //       this.leftTableData = returnData;
    //       this.$nextTick(() => {
    //         if (returnData.length < 1) {
    //           return;
    //         }

    //         if (this.content) {
    //           console.log("[ this.content ]-182", this.content);
    //           returnData.forEach((item, index) => {
    //             if (item.content == this.content) {
    //               this.index = index;
    //               console.log("[ this.index ]-184", this.index);
    //             }
    //           });
    //         }
    //         console.log("[ this.index ]-185", this.index);
    //         this.$refs.leftTable.$refs.tableCom_Ref.setCurrentRow(
    //           returnData[this.index]
    //         );
    //         this.rowClick(returnData[this.index]);
    //       });
    //     });
    // },
    //我发的
    rowClick(row) {
      this.comCowClick(row);
      this.$refs.receiveTable.$refs.tableCom_Ref.setCurrentRow(); // 取消高亮
    },
    //别人发的
    receiveClick(row) {
      this.comCowClick(row);
      this.$refs.leftTable.$refs.tableCom_Ref.setCurrentRow(); // 取消高亮
    },

    //点击行公用函数
    comCowClick(row) {
      this.row = row;
      this.typeHead = row;
      this.ids = row.ids;
      let data = {
        chatId: row.chatId,
        createTime: row.createTime,
        receiveUser: this.G_userInfo.codeOper.operatorCode
      };
      console.log(row);
      this.M_resData(row);
      this.M_rowRes(row);
      this.M_getReadDoctorChatReply(data);
      this.getUpdateChatMsgStauts(data);
    },

    //会诊回复列表
    // getReadDoctorChatReply(data) {
    //   this.$ajax.post(this.$apiUrls.ReadDoctorChatReply, data).then(r => {
    //     console.log("[ r ]-169", r);
    //     let { success, returnData } = r.data;
    //     if (!success) return;
    //     this.rightTableData = returnData;
    //     this.textareaVal = "";
    //   });
    // },
    //更新消息状态为已读
    getUpdateChatMsgStauts(data) {
      this.$ajax.post(this.$apiUrls.UpdateChatMsgStauts, data).then((r) => {
        let { success } = r.data;
        if (!success) return;
      });
    },

    //回复
    sendInfo() {
      console.log(this.row);
      let sendData = {
        chatId: this.row.chatId,
        SendUser: this.G_userInfo.codeOper.operatorCode,
        DeptCode: this.G_userInfo.codeOper.deptCode,
        // InitFlag: false,
        Content: this.textareaVal,
        createTime: this.row.createTime
      };
      console.log(sendData);
      this.$ajax.post(this.$apiUrls.SendDoctorMsg, sendData).then((r) => {
        this.M_resData({
          ...r.data.returnData,
          doctorCode: r.data.returnData.sendUser
        });
      });
      return;
      this.$ws.sendSock(JSON.stringify(sendData), () => {
        console.log('回复');
        let data = {
          chatId: this.row.id,
          createTime: this.row.createTime,
          receiveUser: this.G_userInfo.codeOper.operatorCode
        };
        this.M_getReadDoctorChatReply(data);
        this.getUpdateChatMsgStauts(data);
      });
    }
  },
  watch: {
    C_chatRoomList: {
      handler(n, o) {
        this.$nextTick(() => {
          if (n.length < 1) {
            return;
          }
          // if (this.ids) {
          //   n.forEach((item, index) => {
          //     if (item.id == this.ids) {
          //       this.index = index;
          //     }
          //   });
          // } else {
          //   this.index = 0;
          // }
          // this.$refs.leftTable.$refs.tableCom_Ref.setCurrentRow(n[this.index]);
          // this.rowClick(n[this.index]);
        });
      },
      immediate: true
    },
    routeRow: {
      handler(n, o) {
        console.log(555555555, n, o);
        console.log(this.G_chatReceiveList);
        this.$nextTick(() => {
          if (JSON.stringify(n) != '{}') {
            this.G_chatRoomList.some((item) => {
              if (item.chatId == n.subjectId) {
                this.$refs.leftTable.$refs.tableCom_Ref.setCurrentRow(item);
                this.comCowClick(item);
              }
            });
            this.G_chatReceiveList.some((item) => {
              if (item.chatId == n.subjectId) {
                this.$refs.receiveTable.$refs.tableCom_Ref.setCurrentRow(item);
                this.comCowClick(item);
              }
            });
          }
        });
      },
      immediate: true
    }
  }
};
</script>
<style lang="less" scoped>
.response {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  padding: 0 20px 20px 20px;
  .leftCont {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
    .leftTop {
      flex: 1;
      overflow: auto;
      display: flex;
      flex-direction: column;
      & > p {
        font-weight: 600;
      }
      .contDiv {
        flex: 1;
        flex-shrink: 0;
        overflow: auto;
        border: 1px solid #d8dee1;
        border-radius: 4px;
      }
    }
  }
  .type_head {
    background: rgba(23, 112, 223, 0.1);
    border-radius: 4px;
    border-radius: 4px;
    padding: 10px;
    li {
      display: flex;
      height: 32px;
      font-family: PingFangSC-Medium;
      font-size: 14px;
      color: #2d3436;
    }

    .every_inp {
      display: flex;
      align-items: center;
      margin-right: 10px;
      width: 20%;
      label {
        margin-right: 10px;
        width: auto;
        text-align: right;
      }

      p {
        flex: 1;
      }
    }
    .regNo_inp {
      width: 45%;
    }
    .cont_inp {
      width: 100%;
    }
  }
  .rightCont {
    flex: 1;
    padding-left: 20px;
    display: flex;
    flex-direction: column;
    overflow: auto;
    .typeHead {
      display: flex;
      flex-direction: column;
      & > p {
        font-weight: 600;
      }
    }
    .tableDiv {
      flex: 1;
      display: flex;
      flex-direction: column;
      border: 0 solid #b2bec3;
      border-radius: 4px;
      border-radius: 4px;
      & > p {
        font-weight: 600;
      }
      .tableCont {
        flex: 1;
        flex-shrink: 0;
        overflow: auto;
        border: 1px solid #d8dee1;
        border-radius: 4px;
      }
    }
    .typeCenter {
      display: flex;
      flex-direction: column;
      & > p {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        font-weight: 600;
      }
    }
  }
  p {
    height: 48px;
    line-height: 48px;
    font-family: PingFangSC-Medium;
    font-size: 18px;
    color: #2d3436;
  }
}
</style>
