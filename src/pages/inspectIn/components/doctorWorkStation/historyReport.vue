<template>
  <div class="historyReport" id="historyReport">
    <div class="leftDiv" id="leftDiv">
      <div class="divInfo">
        <p class="pCss">体检综述</p>
        <div class="contTxt">
          <div class="contDiv">
            <div class="contInfos">
              <p class="contP">{{ timeFormat(infoList.examTime) }}</p>
              <p class="contP">{{ infoList.examTimeLast }}</p>
            </div>

            <div class="contInfo">
              <div class="cont" style="white-space: pre-wrap">
                {{ infoList.summary.result }}
              </div>
              <div class="cont" style="white-space: pre-wrap">
                {{ infoList.summary.resultLast }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="divInfo">
        <p class="pCss">诊断建议</p>
        <div class="contTxt">
          <div class="contDiv">
            <div class="contInfos">
              <p class="contP">{{ timeFormat(infoList.examTime) }}</p>
              <p class="contP">{{ infoList.examTimeLast }}</p>
            </div>

            <div class="contInfo">
              <div class="cont" style="white-space: pre-wrap">
                {{ infoList.suggestion.result }}
              </div>
              <div class="cont" style="white-space: pre-wrap">
                {{ infoList.suggestion.resultLast }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="divInfo"
        v-for="(item, index) in infoList.examItems"
        :key="index"
        :id="'id' + index"
      >
        <p class="pCss">{{ item.combName }}</p>

        <div class="combDiv">
          <p>
            <span>项目名称</span><span>{{ timeFormat(infoList.examTime) }}</span
            ><span>{{ infoList.examTimeLast }}</span>
          </p>
          <p
            class="contDiv"
            v-for="(items, idx) in item.itemResults"
            :key="idx"
          >
            <span>{{ items.itemName }}</span
            ><span>{{ items.result }}</span
            ><span>{{ items.resultLast }}</span>
          </p>
        </div>
      </div>
    </div>
    <div class="rightDiv">
      <div class="collapse-content">
        <ul class="content-wrap">
          <li
            :class="activeIdx === index ? 'active-li' : ''"
            v-for="(item, index) in navList"
            :key="index"
            @click="navClick(item, index)"
          >
            {{ item.title }}
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import PublicTable from '../../../../components/publicTable.vue';
export default {
  name: 'historyReport',
  components: { PublicTable },
  props: {
    //行信息
    regNo: {
      typeof: String,
      default: ''
    }
  },
  data() {
    return {
      navList: [
        {
          id: 1,
          title: '体检综述'
        },
        {
          id: 2,
          title: '诊断建议'
        }
      ],
      infoList: {
        examTime: '',
        examTimeLast: '',
        summary: {
          result: '',
          resultLast: ''
        },
        suggestion: {
          result: '',
          resultLast: ''
        },
        examItems: []
      },
      // infoLists: [
      //   {
      //     combName: "体检综述",
      //     id: 1,
      //     itemResults: [
      //       {
      //         examTime: "2022.09.20",
      //         result: "测试数据测试\n内容测试数据测试数据测试\n内容测试",
      //       },
      //       {
      //         examTime: "2022.09.21",
      //         result: "测试数据测试数据测试数据测试数据测试数据",
      //       },
      //     ],
      //   },
      //   {
      //     combName: "诊断建议",
      //     id: 2,
      //     itemResults: [
      //       {
      //         examTime: "2022.09.20",
      //         result: "测试数据测试数据测试数据测试数据测试数据",
      //       },
      //       {
      //         examTime: "2022.09.21",
      //         result: "测试数据测试数据测试数据测试数据测试数据",
      //       },
      //     ],
      //   },
      //   {
      //     examTime: "2022.10.10",
      //     examTimeLast: "2022.10.01",
      //     combName: "内科",
      //     id: 3,
      //     itemResults: [
      //       {
      //         itemName: "发育",
      //         result: "正常",
      //         resultLast: "正常",
      //       },
      //       {
      //         itemName: "胸廓",
      //         result: "无畸形",
      //         resultLast: "无畸形",
      //       },
      //       {
      //         itemName: "肺部",
      //         result: "未见异常",
      //         resultLast: "未见异常",
      //       },
      //     ],
      //   },
      //   {
      //     examTime: "2022.10.10",
      //     examTimeLast: "2022.10.01",
      //     combName: "外科",
      //     id: 4,
      //     itemResults: [
      //       {
      //         itemName: "发育",
      //         result: "正常",
      //         resultLast: "正常",
      //       },
      //       {
      //         itemName: "胸廓",
      //         result: "无畸形",
      //         resultLast: "无畸形",
      //       },
      //       {
      //         itemName: "肺部",
      //         result: "未见异常",
      //         resultLast: "未见异常",
      //       },
      //     ],
      //   },
      //   {
      //     examTime: "2022.10.10",
      //     examTimeLast: "2022.10.01",
      //     combName: "耳鼻咽喉科",
      //     id: 5,
      //     itemResults: [
      //       {
      //         itemName: "发育",
      //         result: "正常",
      //         resultLast: "正常",
      //       },
      //       {
      //         itemName: "胸廓",
      //         result: "无畸形",
      //         resultLast: "无畸形",
      //       },
      //       {
      //         itemName: "肺部",
      //         result: "未见异常",
      //         resultLast: "未见异常",
      //       },
      //     ],
      //   },
      //   {
      //     examTime: "2022.10.10",
      //     examTimeLast: "2022.10.01",
      //     combName: "眼科",
      //     id: 6,
      //     itemResults: [
      //       {
      //         itemName: "发育",
      //         result: "正常",
      //         resultLast: "正常",
      //       },
      //       {
      //         itemName: "胸廓",
      //         result: "无畸形",
      //         resultLast: "无畸形",
      //       },
      //       {
      //         itemName: "肺部",
      //         result: "未见异常",
      //         resultLast: "未见异常",
      //       },
      //     ],
      //   },
      // ],
      activeIdx: 0,
      index: 5
      // regNo: this.headerInfo.regNo
    };
  },
  mounted() {
    this.getHistoryReport();
    document
      .getElementById('historyReport')
      .addEventListener('scroll', this.onScroll, true);
  },
  destroy() {
    // 移除监听器
    document
      .getElementById('historyReport')
      .removeEventListener('scroll', this.onScroll, false);
  },
  methods: {
    getHistoryReport() {
      this.navList = [
        {
          id: 1,
          title: '体检综述'
        },
        {
          id: 2,
          title: '诊断建议'
        }
      ];
      this.$ajax
        .post(this.$apiUrls.GetHistoryReport, '', {
          query: {
            regNo: this.regNo
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;

          if (returnData.examItems) {
            returnData.examItems.map((item, index) => {
              this.navList.push({
                id: index + 3,
                title: item.combName
              });
            });
            this.infoList = returnData || [];
          } else {
            this.$message({
              message: '没有历史报告的数据!',
              type: 'warning',
              showClose: true
            });
            return;
            //测试数据
            // this.infoList =
            //   returnData.combItemResults || [].concat(this.infoLists);
            // this.infoList.map((item, index) => {
            //   this.navList.push({
            //     id: index + 1,
            //     title: item.combName,
            //   });
            // });
          }
          console.log('[ this.infoList ]-234', this.infoList);
        });
    },
    // navClick(item, index) {
    //   this.activeIdx = index;
    //   this.currentIndex = index;
    //   this.$el.querySelector(`#id${index}`).scrollIntoView({
    //     behavior: "smooth", // 平滑过渡
    //     block: "start", // start 上边框  center 中间  end 底部边框 与视窗顶部平齐
    //   });
    // },
    // 滚动监听器
    onScroll() {
      let scrollBottom =
        document.getElementById('leftDiv').scrollHeight -
        document.getElementById('leftDiv').scrollTop -
        document.getElementById('leftDiv').clientHeight;
      if (scrollBottom == 0) {
        this.activeIdx = this.index;
        return;
      }
      // 获取所有锚点元素
      const navContents = document.querySelectorAll('.leftDiv .divInfo');
      // 所有锚点元素的 offsetTop
      const offsetTopArr = [];
      navContents.forEach((item) => {
        offsetTopArr.push(item.offsetTop);
      });
      // 获取当前文档流的 scrollTop
      const scrollTop = document.getElementById('leftDiv').scrollTop + 62; //62为头部到当前文档流的距离
      // console.log("[ scrollTop ]-229", scrollTop);
      // 定义当前点亮的导航下标
      let navIndex = 0;
      for (let n = 0; n < offsetTopArr.length; n++) {
        if (scrollTop >= offsetTopArr[n]) {
          navIndex = n;
        }
        // let clientHeight = document.documentElement.clientHeight; //可视高度
        // if (
        //   scrollTop + clientHeight >=
        //   document.getElementById("leftDiv").scrollHeight
        // ) {
        //   navIndex = offsetTopArr.length - 1;
        //   console.log("[ navIndex ]-242", navIndex);
        // }
      }

      this.activeIdx = navIndex;
    },
    //导航定位内容位置
    navClick(item, index) {
      this.index = index;
      this.activeIdx = index;
      console.log('[ this.activeIdx ]-269', this.activeIdx);
      const navContents = document.querySelectorAll('.leftDiv .divInfo');
      // 所有锚点元素的 offsetTop
      const offsetTopArr = [];
      navContents.forEach((item) => {
        offsetTopArr.push(item.offsetTop);
      });
      const targetOffsetTop = offsetTopArr[index] - 62;
      // console.log("[targetOffsetTop  ]-76", targetOffsetTop);
      // 获取当前 offsetTop
      let scrollTop = document.getElementById('leftDiv').scrollTop;
      // console.log("[ scrollTop ]-79", scrollTop);
      // 定义一次跳 50 个像素
      const STEP = 50;
      // 判断是往下滑还是往上滑
      if (scrollTop > targetOffsetTop) {
        // 往上滑
        smoothUp();
      } else {
        // 往下滑
        smoothDown();
      }
      // 定义往下滑函数
      function smoothDown() {
        // 如果当前 scrollTop 小于 targetOffsetTop 说明视口还没滑到指定位置
        if (scrollTop < targetOffsetTop) {
          if (targetOffsetTop - scrollTop >= STEP) {
            scrollTop += STEP;
          } else {
            scrollTop = targetOffsetTop;
          }
          document.getElementById('leftDiv').scrollTop = scrollTop;
          requestAnimationFrame(smoothDown);
        }
      }
      // 定义往上滑函数
      function smoothUp() {
        if (scrollTop > targetOffsetTop) {
          if (scrollTop - targetOffsetTop >= STEP) {
            scrollTop -= STEP;
          } else {
            scrollTop = targetOffsetTop;
          }
          document.getElementById('leftDiv').scrollTop = scrollTop;
          requestAnimationFrame(smoothUp);
        }
      }
    },
    // 格式化时间
    timeFormat(date) {
      if (date) {
        let newDate = /\d{4}-\d{1,2}-\d{1,2}/g.exec(date);
        return newDate[0];
      } else {
        return '';
      }
    }
  }
};
</script>

<style lang="less" scoped>
.historyReport {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  padding: 20px;
  font-family: PingFangSC-Semibold;
  font-size: 14px;
  color: #2d3436;
  .leftDiv {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    .divInfo {
      width: 100%;
      min-height: 300px;
      display: flex;
      flex-direction: column;
      border: 1px solid #eee;
      margin-bottom: 18px;
      &:last-child {
        margin-bottom: 0;
      }
      .pCss {
        background: rgba(23, 112, 223, 0.2);
        border-radius: 1px;
        border-radius: 1px;
        line-height: 38px;
        font-family: PingFangSC-Medium;
        font-size: 18px;
        // color: #ffffff;
        padding: 0 20px;
      }
      .contInfos {
        display: flex;
        flex-direction: row;
        border-bottom: 1px solid #ccc;
        .contP {
          line-height: 32px;
          border-bottom: 1px solid #eee;
          padding: 0 20px;
          flex: 1;
          border-right: 1px solid #ccc;
        }
      }
      .contTxt {
        flex: 1;
        display: flex;
        flex-direction: row;
        overflow: auto;
        .contDiv {
          flex: 1;
          flex-shrink: 0;
          border: 1px solid #eee;
          display: flex;
          flex-direction: column;
          .contInfo {
            flex: 1;
            display: flex;
            flex-direction: row;

            .contP {
              line-height: 32px;
              border-bottom: 1px solid #eee;
              padding: 0 20px;
              flex: 1;
              border-right: 1px solid #ccc;
            }
            .cont {
              padding: 0 20px;
              flex: 1;
              flex-shrink: 0;
              overflow: auto;
              border-right: 1px solid #ccc;
            }
          }
        }
      }
      .combDiv {
        flex: 1;
        overflow: auto;
        p {
          display: flex;
          flex-direction: row;
          line-height: 32px;
          border-bottom: 1px dashed #eee;
          padding: 0 20px;
          border-bottom: 1px solid #ccc;
          & > span {
            flex: 1;
          }
        }
      }
    }
  }
  .rightDiv {
    width: 300px;
    overflow: auto;
    margin-left: 20px;
    background: rgba(178, 190, 195, 0.1);
    border-radius: 1px;
    border-radius: 1px;
    .active-li {
      font-family: PingFangSC-Medium;
      font-size: 14px;
      color: #1770df;
      background: rgba(23, 112, 223, 0.1);
    }
    .collapse-content {
      height: 100%;
    }
    .content-wrap {
      margin-left: 18px;
      border-left: 1px solid #b2bec3;

      li {
        padding: 7px;
        padding-left: 16px;
        font-size: 14px;
        cursor: pointer;
        position: relative;
        &:last-child {
          margin-bottom: 0;
        }
        &.active-li::before {
          content: '';
          display: block;
          width: 14px;
          height: 14px;
          background: url('../../../../assets/img/doctorWorkStation/position.png')
            no-repeat 100% / cover;
          position: absolute;
          left: -6px;
          top: 50%;
          transform: translateY(-50%);
          z-index: 1;
        }
        &.active-li::after {
          content: '';
          display: block;
          width: 1px;
          height: 100%;
          background: #1770df;
          position: absolute;
          left: -1px;
          top: 0;
        }
      }
    }
  }
}
</style>
