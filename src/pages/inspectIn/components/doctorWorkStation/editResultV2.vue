<!--
 * @FilePath: \KrPeis\src\pages\inspectIn\components\doctorWorkStation\editResultV2.vue
 * @Description:  结果的编辑弹窗v2
 * @Author: justin
 * @Date: 2024-06-24 15:02:56
 * @Version: 0.0.1
 * @LastEditors: justin
 * @LastEditTime: 2024-07-11 17:38:02
*
-->
<template>
  <!-- 结果的编辑弹窗 -->
  <div
    class="popup_style edit_popup"
    v-show="editPopup"
    ref="editPopupDom_Ref"
    @mouseenter="popupMouseenter"
    :style="{
      [isOvertop >= 0 ? 'top' : 'bottom']: top + 'px',
      left: left + 'px'
    }"
  >
    <!-- <div class="sightWord_input">
            <el-input ref="sightWordInput_Ref" type="textarea"
  :autosize="{ minRows: 1, maxRows: 3}" v-model="sightWordVal" @keypress.enter.native="sightWordInputEnter"
                @blur="handleInputBlur" size="small" width="100%"></el-input>
        </div> -->
    <div class="sight_word">
      <h3>
        常用词：<i class="el-icon-edit-outline" @click="editOutlineClick"></i>
      </h3>
      <div class="sightWord_wrap">
        <el-tag
          type="info"
          disable-transitions
          effect="plain"
          @click="sightWordClick(item)"
          :closable="isSightWordEdit"
          hit
          class="sightWord_tag"
          @close="sightWordClose(item)"
          v-for="(item, idx) in sightWordList"
          :key="idx"
          >{{ item.words }}
        </el-tag>
      </div>
      <!-- 自定义 -->
      <div class="custom_result">
        <el-input
          class="input-new-tag"
          v-if="isAddSightWord"
          v-model.trim="addSightWordVal"
          ref="saveTagInput"
          size="small"
          @keyup.enter.native="handleInputConfirm"
          @blur="handleInputConfirm"
        >
        </el-input>
        <el-button
          @click="showInput"
          type="primary"
          size="mini"
          icon="el-icon-plus"
          v-else
          plain
          >新增</el-button
        >
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'editResult',
  props: {
    editPopup: {
      type: Boolean,
      default: false
    },
    top: {
      type: Number,
      default: 0
    },
    left: {
      type: Number,
      default: 0
    },
    label: {
      type: String,
      default: ''
    },
    isDefineClick: {
      type: Boolean,
      default: false
    },
    isOvertop: {
      type: Number,
      default: 0
    }
  },
  watch: {
    editPopup(n, o) {
      if (n) {
        this.getReadCodeDeptWord();
        // this.$nextTick(()=>{
        //     this.$refs.sightWordInput_Ref.focus()
        // })
      }
    }
  },
  data() {
    return {
      sightWordVal: '',
      isSightWordEdit: false,
      isAddSightWord: false, // 是否显示新增的输入框
      addSightWordVal: '',
      sightWordList: []
    };
  },
  methods: {
    // 获取常用词
    getReadCodeDeptWord() {
      if (!this.$parent.combData || !this.$parent.combData.recordComb) return;
      let datas = {
        deptCode: this.$parent.combData.recordComb.examDeptCode
      };
      this.$ajax.paramsPost(this.$apiUrls.ReadCodeDeptWord, datas).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.sightWordList = returnData || [];
        this.$emit('readCodeDeptWord', this.sightWordList);
      });
    },
    // 编辑输入框的回车回调
    sightWordInputEnter() {
      if (this.sightWordVal.trim() === '') {
        this.$message({
          message: '结果不能为空！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      // 结果的编辑
      if (this.label == 'examine') {
        if (this.isDefineClick) {
          let tag = {
            id: 0,
            itemCode: this.$parent.enterCheckRow.itemCode,
            tag: this.sightWordVal,
            resultId: 0,
            isCalcResul: false,
            calcItemTagIds: [],
            abnormalType: 0
          };
          this.$parent.enterCheckRow.itemTags.push(tag);
          this.$parent.paramFun(1, tag);
        } else {
          this.$parent.editResult.tag = this.sightWordVal;
          this.$parent.paramFun(2, this.$parent.editResult);
        }
      }
      // 小结的编辑
      if (this.label == 'physicalExaminationSummary') {
        this.$parent.editNodulus.tag = this.sightWordVal;
      }
    },
    // 获取光标的位置
    handleInputBlur(e) {
      this.cursorIndex = e.srcElement.selectionStart;
      console.log(this.cursorIndex);
    },
    // 常用词的开启或关闭编辑状态
    editOutlineClick() {
      this.isSightWordEdit = !this.isSightWordEdit;
    },
    // 选择常用词
    sightWordClick(item) {
      // this.sightWordVal = this.sightWordVal.slice(0, this.cursorIndex) + item.words + this.sightWordVal.slice(this.cursorIndex);
      // this.cursorIndex += item.words.length
      // var a = this.$refs.sightWordInput_Ref.$el.querySelector('input');
      // a.focus();
      // this.$nextTick(() => {
      //     a.setSelectionRange(this.cursorIndex, this.cursorIndex)
      // })
      this.$parent.setTag(item.words);
    },
    // 删除常用词
    sightWordClose(item) {
      this.$ajax.post(this.$apiUrls.DeleteCodeDeptWord, [item]).then((r) => {
        this.getReadCodeDeptWord();
      });
    },
    // 确定新增常用词
    handleInputConfirm() {
      if (this.addSightWordVal) {
        let datas = {
          deptCode: this.$parent.combData.recordComb.examDeptCode,
          words: this.addSightWordVal
        };
        this.$ajax.post(this.$apiUrls.CreateCodeDeptWord, datas).then((r) => {
          this.getReadCodeDeptWord();
        });
      }
      this.isAddSightWord = false;
      this.addSightWordVal = '';
    },
    // 鼠标离开的回调
    resultLeave() {
      this.$parent.resultLeave();
    },
    // 显示新增常用词的输入框
    showInput() {
      this.isAddSightWord = true;
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    popupMouseenter() {
      this.$parent.popupMouseenter('editPopup');
    }
  },
  created() {}
};
</script>

<style lang="less" scoped>
.edit_popup {
  width: 350px;
  padding: 10px 15px;

  background: #f7f8f9;
  position: fixed;
  z-index: 10;
  border: 1px solid #b2bec3;
  border-radius: 2px;
  max-height: 350px;
  overflow: auto;
  .sightWord_input {
    padding: 15px 0;
  }

  .sight_word {
    h3 {
      font-size: 14px;

      i {
        color: #1770df;
        font-size: 18px;
        cursor: pointer;
      }
    }

    .sightWord_wrap {
      padding-top: 10px;
    }

    .sightWord_tag {
      margin-bottom: 10px;
      margin-right: 10px;
      color: #000;
      cursor: pointer;

      &:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
