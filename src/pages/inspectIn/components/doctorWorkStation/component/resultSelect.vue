<template>
  <el-dropdown @command="handleCommand" style="margin-right: 10px">
    <span class="el-dropdown-link">
      {{ val }}<i class="el-icon-arrow-down el-icon--right"></i>
    </span>
    <el-dropdown-menu slot="dropdown" @mouseenter.native="dropdownMouseenter">
      <el-dropdown-item
        @click="dropItemClick(item)"
        v-for="item in list"
        :key="item"
        :command="item"
        >{{ item }}</el-dropdown-item
      >
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
export default {
  name: 'resultSelect',
  props: {
    list: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  data() {
    return {
      val: ''
    };
  },
  methods: {
    dropdownMouseenter() {
      this.$emit('dropdownMouseenter');
    },
    handleCommand(command) {
      console.log(command);
      this.val = command;
    }
  },
  mounted() {
    this.val = this.list[0];
  }
};
</script>

<style></style>
