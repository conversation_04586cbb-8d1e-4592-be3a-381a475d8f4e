export default {
  data() {
    return {};
  },
  methods: {
    inputComEnter(txt) {
      // console.log(txt);
      // if(txt.trim() !== ''){
      //     let tag = {
      //         id: 0,
      //         itemCode: this.enterCheckRow.itemCode,
      //         tag: txt,
      //         resultId: 0,
      //         isCalcResul: false,
      //         calcItemTagIds: [],
      //         abnormalType:0
      //     }
      //     this.enterCheckRow.itemTags.push(tag)
      //     this.paramFun(1, tag);
      // }
      this.$refs['inpFocus_' + this.checkRowIndex].inputHandle();
      this.$nextTick(() => {
        this.nextRowCurrent({ rowIndex: this.checkRowIndex });
      });
    },
    inputBlur(txt) {
      // console.log(txt);
      if (txt.trim() !== '') {
        let tag = {
          id: 0,
          itemCode: this.enterCheckRow.itemCode,
          tag: txt,
          resultId: 0,
          isCalcResul: false,
          calcItemTagIds: [],
          abnormalType: 0
        };
        this.enterCheckRow.itemTags.push(tag);
        this.paramFun(1, tag);
      }
      console.log(this.enterCheckRow);
    },
    // 删除结果
    labelDel(tag) {
      console.log(tag);
      this.paramFun(3, tag);
      this.abnormalLevel();
    },
    // 输入框的实时变化回调
    inputChange() {
      let cell =
        this.$refs['itemTags_' + this.checkCellIdx].parentNode.parentNode;
      console.log(cell);
      this.showPopupFun('resultPopup_Ref', cell, { view: window });
    },
    // 修改结果离开输入框的回调
    resultSpanBlur(tag) {
      console.log(tag);
      this.paramFun(2, tag);
    },
    // 手动修改异常状态
    abnormal(tag) {
      console.log(tag);
      this.filterResult();
    }
  }
};
