export default {
  data() {
    return {
      editPopup: false,
      top: 0,
      left: 0,
      isOvertop: 0
    };
  },
  methods: {
    // 小结的点击回调
    tableTagClick(e, tag, idx) {
      console.log(tag);
      this.maskShow = true;
      this.editNodulus = tag;
      this.$refs.editPopup_Ref.sightWordVal = tag.tag;
      this.editPopup = true;
      console.log(1111);
      let tagCoord = e.target.getBoundingClientRect();
      console.log(tagCoord);
      this.$nextTick(() => {
        let popupCoord =
          this.$refs.editPopup_Ref.$refs.editPopupDom_Ref.getBoundingClientRect();
        this.left = tagCoord.left;
        this.isOvertop =
          e.view.innerHeight -
          tagCoord.top -
          tagCoord.height -
          popupCoord.height;

        console.log(
          e.view.innerHeight,
          tagCoord.top,
          tagCoord.height,
          popupCoord.height
        );
        if (this.isOvertop >= 0) {
          this.top = tagCoord.top + tagCoord.height - 1;
        } else {
          this.top = window.innerHeight - tagCoord.top;
        }
      });
    },
    // 鼠标覆盖弹窗的回调
    popupMouseenter(popupName) {
      this[popupName] = true;
    },
    // 鼠标离开隐藏
    resultLeave() {
      console.log(777);
      this.editPopup = false;
      this.maskShow = false;
    }
  },
  created() {}
};
