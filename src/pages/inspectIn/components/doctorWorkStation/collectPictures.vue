<template>
  <div class="collectPictures">
    <div class="headInfo">
      <div class="typeHeader">
        <div class="every_inp comb_sel">
          <label>组合名称:</label>
          <p>
            <el-select
              placeholder="请选择"
              size="small"
              v-model.trim="regCombId"
              filterable
              @change="combChange"
            >
              <el-option
                v-for="item in combArr"
                :value="item.regCombId"
                :label="item.combName"
                :key="item.combCode"
              >
              </el-option>
            </el-select>
          </p>
        </div>
        <div class="every_inp comb_sel">
          <label>体检号:</label>
          <p>{{ typeHead.regNo }}</p>
        </div>
        <div class="every_inp lowWidth">
          <label>姓名:</label>
          <p>{{ typeHead.name }}</p>
        </div>
        <div class="every_inp lowWidth">
          <label>性别:</label>
          <p>{{ sexList[typeHead.sex] }}</p>
        </div>
        <div class="every_inp lowWidth">
          <label>年龄:</label>
          <p>{{ typeHead.age }}</p>
        </div>
        <div class="every_inp companyWidth">
          <label>单位:</label>
          <p :title="typeHead.companyName" class="moreHidden">
            {{ typeHead.companyName }}
          </p>
        </div>
      </div>
      <BtnCommon
        :btnList="['选中', '取消', '删除']"
        @chooses="checkPacsImage2Print"
        @cancels="uncheckPacsImage2Print"
        @deletes="deleteCollectedPacsImage"
      />
    </div>
    <div class="bodyDiv">
      <div class="leftDiv">
        <p class="pCss" style="justify-content: space-between; padding: 0 20px">
          <span
            >采集区：<input
              id="categoryPic"
              accept="image/*"
              type="file"
              name="image"
              @change="getFile($event)"
            />
          </span>
        </p>
        <div class="pictures-box"></div>
      </div>
      <div class="rightDiv">
        <div class="pCss">
          <p class="p1" :title="combName">{{ combName }}</p>
          <p class="p2">
            <span>采集图片：</span>
            <span class="lenColor">{{ imgInfoList.length }}</span>
          </p>
        </div>
        <div class="img-box">
          <el-checkbox-group v-model="checkedVal" @change="handleCheckedChange">
            <el-checkbox
              v-for="(img, index) in imgInfoList"
              :key="index"
              :label="img.imagePath"
              @click="imgLeftClick(img.imagePath, index)"
            >
              <div class="imgDiv">
                <el-image :src="img.imagePath" lazy></el-image>
                <div class="imgName">
                  <i
                    class="iconfont icon-zhifuchenggong cellBlue"
                    v-show="img.recImageId"
                  ></i
                  >{{ img.combName }}
                </div>
              </div>
            </el-checkbox>
          </el-checkbox-group>
        </div>
        <!-- <div class="img-box">
          <el-checkbox-group v-model="checkedVal" @change="handleCheckedChange">
            <el-checkbox
              v-for="url in imgInfoList"
              :label="url.imagePath"
              :key="url.id"
            >
              <el-image
                style="width: 280px; height: 200px"
                :src="url.imagePath"
              >
              </el-image>
            </el-checkbox>
          </el-checkbox-group>
        </div> -->
      </div>
    </div>
  </div>
</template>
<script>
import BtnCommon from '../btnCommon.vue';
import { mapGetters } from 'vuex';
export default {
  name: 'collectPictures',
  components: { BtnCommon },
  props: {
    //行信息
    headerInfo: {
      typeof: Object,
      default: {}
    },
    propRegCombId: {
      typeof: String,
      default: ''
    },
    regCombs: {
      type: Array,
      default: () => {
        return [];
      }
    },
    deptCode: {
      typeof: String,
      default: ''
    }
  },
  data() {
    return {
      // regCombs:[],
      checkedVal: [],
      regCombId: this.propRegCombId,
      combArr: this.regCombs,
      typeHead: this.headerInfo,
      sexList: {
        null: '',
        0: '通用',
        1: ' 男',
        2: '女'
      },
      imgActive: '',
      imgInfoList: [],
      combName: '',
      idArr: [],
      recImageIdArr: [],
      fileData: '',
      fileNum: 'xx'
    };
  },
  created() {},
  mounted() {
    console.log('1111111111111111111111', this.regCombs);
    // this.getPacsDeptCode();
    this.getCollectedPacsImage();
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_userInfo'])
  },
  methods: {
    //采集
    getFile: function (event) {
      let file = event.target.files[0];
      let formData = new FormData();
      formData.append('files', file);
      // formData.append("regNo", this.typeHead.regNo);
      // formData.append("regCombId", this.regCombId);
      //console.log("[ formData ]-174", formData);
      //console.log(formData.get("file"));
      // let read = new FileReader();
      // read.readAsDataURL(file);
      // read.onload=(e)=>{
      //   console.log(e);
      //   this.testSrc = e.target.result;
      // }
      this.$ajax
        .post(
          this.$apiUrls.SaveCollectedPacsImage +
            '?regNo=' +
            this.typeHead.regNo +
            '&regCombId=' +
            this.regCombId +
            '&operatorCode=' +
            this.G_userInfo.codeOper.operatorCode +
            '&deptCode=' +
            this.deptCode,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
              Authorization: ''
            }
          }
        )
        .then((r) => {
          let { success } = r.data;
          if (!success) return;
          this.getCollectedPacsImage();
        });
    },
    combChange(val) {
      this.regCombId = val;
      this.getCollectedPacsImage();
    },
    //勾选
    handleCheckedChange(val) {
      this.checkedVal = val;
      this.idArr = [];
      this.recImageIdArr = [];
      //获取图片对应id集合
      this.imgInfoList.forEach((item, index) => {
        this.checkedVal.forEach((items, idx) => {
          if (items == item.imagePath) {
            this.idArr.push(item.id);
            this.recImageIdArr.push(item.recImageId);
          }
        });
      });
      console.log('[ this.recImageIdArr ]-228', this.recImageIdArr);
      console.log('[ idArr ]-174', this.idArr);
    },

    // //获取pacs组合
    // getPacsComb() {
    //   this.$ajax
    //     .post(this.$apiUrls.GetPacsComb, "", {
    //       query: { regNo: this.typeHead.regNo, deptCode: this.deptCode },
    //     })
    //     .then((r) => {
    //
    //       let { success, returnData } = r.data;
    //       if (!success) return;
    //       this.combArr = returnData || [];
    //       this.getCollectedPacsImage();
    //     });
    // },
    // //获取pacs科室代码
    // getPacsDeptCode() {
    //   this.$ajax.post(this.$apiUrls.GetPacsDept).then((r) => {
    //
    //     let { success, returnData } = r.data;
    //     if (!success) return;
    //     this.deptCode = returnData.value;
    //     this.getPacsComb();
    //     console.log("[ this.deptCode ]-240", this.deptCode);
    //   });
    // },
    //获取采集的pacs图像
    getCollectedPacsImage() {
      this.$ajax
        .post(this.$apiUrls.GetCollectedPacsImage, '', {
          query: {
            regNo: this.typeHead.regNo,
            regCombId: this.regCombId,
            deptCode: this.deptCode
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          if (returnData.length >= 1) {
            returnData.forEach((item) => {
              return (item.imagePath =
                this.$config.fileUrl + item.imagePath.replace(/\\/g, '/'));
            });

            this.imgInfoList = returnData;
            this.imgInfoList.forEach((item, index) => {
              this.combArr.forEach((items) => {
                if (item.combCode == items.combCode) {
                  return (this.imgInfoList[index].combName = items.combName);
                }
              });
            });
          } else {
            this.imgInfoList = [];
          }
          console.log('[ this.imgInfoList-277', this.imgInfoList);
        });
    },
    //保存
    //选中组合中的采集的pacs图像用于报告打印
    checkPacsImage2Print() {
      if (this.idArr.length < 1) {
        this.$message({
          message: '请先勾选需要保存的图片!',
          type: 'warning',
          showClose: true
        });
        return false;
      }
      this.$ajax
        .post(this.$apiUrls.CheckPacsImage2Print, this.idArr, {
          query: {
            operatorCode: this.G_userInfo.codeOper.operatorCode
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          if (returnData) {
            this.$message({
              message: '成功',
              type: 'success'
            });
          }
          this.getCollectedPacsImage();
          this.checkedVal = [];
        });
    },
    //取消保存
    uncheckPacsImage2Print() {
      if (this.recImageIdArr.length < 1) {
        this.$message({
          message: '请先选择要取消的图片!',
          type: 'warning'
        });
        return false;
      }
      let recImageIdArr = this.recImageIdArr.filter((item) => {
        return item !== null;
      });
      this.$confirm(`是否确定取消?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax
            .post(this.$apiUrls.UncheckPacsImage2Print, recImageIdArr, {
              query: {
                operatorCode: this.G_userInfo.codeOper.operatorCode
              }
            })
            .then((r) => {
              let { success } = r.data;
              if (!success) return;
              this.$message({
                message: '取消成功',
                type: 'success'
              });
              this.getCollectedPacsImage();
              this.checkedVal = [];
            });
        })
        .catch(() => {});
    },
    //取消采集
    deleteCollectedPacsImage() {
      if (this.idArr.length < 1) {
        this.$message({
          message: '请先选择要删除采集的图片!',
          type: 'warning'
        });
        return false;
      }
      this.$confirm(`是否确定删除?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax
            .post(this.$apiUrls.DeleteCollectedPacsImage, this.idArr, {
              query: {
                regNo: this.typeHead.regNo,
                operatorCode: this.G_userInfo.codeOper.operatorCode
              }
            })
            .then((r) => {
              let { success } = r.data;
              if (!success) return;
              this.$message({
                message: '删除成功',
                type: 'success'
              });
              this.getCollectedPacsImage();
              this.checkedVal = [];
            });
        })
        .catch(() => {});
    }
  }
};
</script>
<style lang="less" scoped>
.collectPictures {
  height: 100%;
  color: #2d3436;
  display: flex;
  flex-direction: column;
  padding: 0 18px 18px 18px;
  .headInfo {
    line-height: 56px;
    display: flex;
    flex-direction: row;
    .typeHeader {
      flex: 1;
      display: flex;
      flex-direction: row;
      white-space: nowrap;
      font-family: PingFangSC-Medium;
      font-size: 14px;
      color: #2d3436;
      .every_inp {
        display: flex;
        flex-direction: row;
        label {
          margin-right: 10px;
          width: 70px;
          text-align: right;
          font-weight: 600;
        }

        p {
          flex: 1;
        }
      }
      .comb_sel {
        width: 20%;
      }

      .lowWidth {
        width: 8%;
        label {
          width: 50px;
        }
      }
      .companyWidth {
        width: 36%;
      }
    }
  }
  .bodyDiv {
    flex: 1;
    display: flex;
    flex-direction: row;
    flex-shrink: 0;
    overflow: auto;
    .leftDiv {
      flex: 1;
      background: rgba(178, 190, 195, 0.1);
      border: 0 solid #b2bec3;
      border-radius: 4px;
      border-radius: 4px;
    }
    .rightDiv {
      width: 348px;
      margin-left: 20px;
      display: flex;
      flex-direction: column;
      background: rgba(178, 190, 195, 0.1);
      border: 1px solid #eee;
      border-radius: 4px;
      .img-box {
        flex: 1;
        flex-shrink: 0;
        overflow: auto;
        padding: 0 18px;
        .el-checkbox {
          margin-top: 18px;
          display: flex;
          margin-right: 0;
        }
        .item-active {
          border: 2px solid #1770df;
        }
        /deep/.el-checkbox__inner {
          width: 20px;
          height: 19px;
          border: 2px solid #d0d0d0;
        }
        // /deep/.el-checkbox__input {
        //   margin-top: 18px;
        // }
        /deep/.el-checkbox__inner::after {
          height: 9px;
          left: 6px;
        }
      }
    }
  }
  .imgName {
    background: #eee;
    text-align: center;
  }
  .cellBlue {
    color: #1770df;
    margin-right: 5px;
  }
  .pCss {
    height: 38px;
    line-height: 38px;
    font-family: PingFangSC-Medium;
    font-size: 18px;
    color: #2d3436;
    background: rgba(23, 112, 223, 0.2);
    padding-left: 18px;
    display: flex;
    .p1 {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .p2 {
      width: 160px;
    }
    .lenColor {
      font-family: PingFangSC-Medium;
      font-size: 18px;
      color: #1770df;
      text-align: right;
    }
  }
  .moreHidden {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
</style>
