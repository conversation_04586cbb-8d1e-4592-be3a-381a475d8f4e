<template>
  <div class="nomal">
    <PublicTable
      :viewTableList.sync="pageData.recordItems"
      :theads.sync="theads"
      :tableLoading.sync="loading"
      :columnWidth="columnWidth"
      :isSortShow="false"
      :isStripe="false"
      :tableRowClassName="dialogRowClassName"
    >
      <template #numberResult="{ scope }">
        <div v-if="isModify && scope.row.resultType == 0">
          <el-input
            style="width: 100%"
            size="mini"
            @blur="numResultBlur(scope.row)"
            v-model="scope.row.numberResult"
            oninput="value=value.replace(/[^0-9.]/g,'')"
            clearable
          ></el-input>
        </div>
      </template>
      <template #lowerLimit="{ scope }">
        <div>
          {{
            scope.row.lowerLimit == null
              ? ''
              : scope.row.lowerLimit + '~' + scope.row.upperLimitt == null
                ? ''
                : scope.row.upperLimitt
          }}
        </div>
      </template>
      <template #hint="{ scope }">
        <div>
          <i class="icon el-icon-bottom cellBlue" v-if="scope.row.hint == 1">
          </i>
          <i class="icon el-icon-top cellRed" v-if="scope.row.hint == 2"></i>
        </div>
      </template>
      <template #lastItemResult="{ scope }">
        <div v-if="scope.row.hint == 1" class="cellBlue">
          {{ scope.row.lastItemResult }}
        </div>
        <div v-if="scope.row.hint == 2" class="cellRed">
          {{ scope.row.lastItemResult }}
        </div>
        <div v-if="scope.row.hint == ''" class="cellNomal">
          {{ scope.row.lastItemResult }}
        </div>
      </template>
    </PublicTable>
  </div>
</template>
<script>
import PublicTable from '../../../../components/publicTable.vue';
import { dataUtils } from '@/common';
export default {
  name: 'nomal',
  components: { PublicTable },
  props: {
    pageData: {
      typeof: Object,
      default: () => {
        return {};
      }
    },
    isModify: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      theads: {
        itemName: '项目名称',
        hint: '异常',
        numberResult: '结果',
        unit: '单位',
        lowerLimit: '参考值',
        lastItemResult: '2022.10.24'
      },
      tableDatas: [],
      columnWidth: {
        hint: 50,
        lastItemResult: 115,
        unit: 110,
        lowerLimit: 110
      }
    };
  },
  computed: {},
  mounted() {},
  methods: {
    //行背景颜色
    dialogRowClassName({ row, rowIndex }) {
      // if (row.hint == "1") {
      //   return "tr_lightRed";
      // } else if (row.hint == "2") {
      //   return "tr_lightBlue";
      // } else {
      //   console.log("cellNomal");
      //   return "";
      // }
    },
    // 数值类型输入框的回调
    numResultBlur(row) {
      row.itemTags.tag = row.numberResult;
    }
  }
};
</script>
<style lang="less" scoped>
.nomal {
  width: auto;
  height: 100%;
  .cellRed {
    color: #d63031;
    font-weight: 900;
  }
  .cellBlue {
    color: #1770df;
    font-weight: 900;
  }
  .cellNomal {
    color: #2d3436;
  }
}
</style>
