<template>
  <div class="checkboxGroup">
    <!-- 弃检图片 -->
    <img
      src="@/assets/img/qijian.png"
      alt=""
      class="qijian_img"
      v-if="
        JSON.stringify(infoData) !== '{}'
          ? infoData.recordComb.doctorName === '弃检'
          : false
      "
    />
    <div class="checkbox-wrap">
      <div
        class="wrap-item"
        v-for="(item, index) in listData"
        :key="item.itemCode"
      >
        <div class="wrap-title">
          <div class="title-text">
            {{ item.itemName }} (已选：{{ item.itemTags.length }}项)
          </div>
          <div class="select-input">
            <div>异常程度</div>
            <el-select
              v-model.trim="item.abnormalType"
              placeholder="请选择"
              size="small"
              class="select"
              clearable
              :disabled="!isModify"
            >
              <el-option
                :label="abnormalType.label"
                :value="abnormalType.value"
                v-for="abnormalType in G_abnormalType"
                :key="abnormalType.value"
              ></el-option>
            </el-select>
          </div>
        </div>
        <div class="checkbox-list">
          <el-checkbox-group
            v-model="item.checked"
            @change="checkboxGroupChange($event, item)"
          >
            <el-checkbox
              :label="checkbox.resultId"
              class="checkbox-item"
              v-for="(checkbox, index) in item.checkboxList"
              :key="index"
              :disabled="!isModify"
              @change="checkboxChange($event, checkbox, item)"
            >
              <span>
                {{ checkbox.tag }}
                <span
                  class="iconfont icon-point abnormal"
                  v-show="checkbox.abnormalType === 1"
                ></span>
              </span>
            </el-checkbox>
          </el-checkbox-group>
        </div>
        <div class="result">
          <div class="result-title">结<br />果</div>
          <div class="content">
            <span :key="index" v-for="(tags, index) in item.itemTags">
              <el-tag
                :closable="isModify"
                :disable-transitions="true"
                @close="handleClose(tags, item)"
                class="tag"
                @click.native="tagClick(tags)"
                v-if="!tags.editShow"
              >
                {{ tags.tag }}
              </el-tag>
              <el-input
                class="input-new-tag"
                type="textarea"
                v-model="tags.tag"
                @blur="tagInpBlur(tags)"
                v-focus
                style="width: calc(100% - 10px)"
                autosize
                v-else
              >
              </el-input>
            </span>

            <el-input
              class="input-new-tag"
              v-if="item.showResultInput"
              v-model="item.resultInput"
              :ref="`saveTagInput${index}`"
              size="small"
              @keyup.enter.native="handleInputConfirm(item)"
              @blur="handleInputConfirm(item)"
            >
            </el-input>
            <span v-else>
              <el-button
                class="button-new-tag"
                size="small"
                @click="showInput(item, index)"
                icon="iconfont icon-xinjian"
                v-show="isModify"
                >新增</el-button
              >
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  name: 'checkboxGroup',
  props: {
    infoData: {
      type: Object,
      default: () => {}
    },
    isModify: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      checkList: [],
      selectValue: 0,
      inputVisible: false,
      inputValue: '',
      listData: [],
      inputIndex: 0,
      focusFlag: false
    };
  },
  computed: {
    ...mapGetters(['G_abnormalType'])
  },
  mounted() {},
  methods: {
    tagClick(tag) {
      this.$set(tag, 'editShow', true);
    },
    tagInpBlur(tag) {
      this.$set(tag, 'editShow', false);
    },
    // 获取复选框结果数据
    getCheckboxList(combCode) {
      this.listData = [];
      this.$ajax
        .post(this.$apiUrls.GetDefaultItemResultByComb, '', {
          query: {
            combCode: combCode
          }
        })
        .then((r) => {
          console.log('GetDefaultItemResultByComb', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.infoData.recordItems.map((item) => {
            returnData.map((i) => {
              if (item.itemCode === i.itemCode) {
                let checkboxList = [];
                i.resultDiseases.map((result) => {
                  checkboxList.push({
                    id: 0,
                    resultId: result.resultId,
                    isCalcResul: false,
                    calcItemTagIds: [],
                    itemCode: i.itemCode,
                    tag: result.resultDesc,
                    abnormalType: result.abnormalType
                  });
                });
                let checked = [];
                if (item.itemTags.length > 0) {
                  checked = item.itemTags.map((tag) => tag.resultId);
                }
                this.listData.push({
                  ...item,
                  checkboxList: checkboxList,
                  showResultInput: false,
                  resultInput: '',
                  checked: checked
                });
              }
            });
          });
          console.log('this.listData: ', this.listData);
        });
    },
    // 复选框组选择事件
    checkboxGroupChange(value, item) {
      // console.log("item: ", item);
      // console.log("value: ", value);
      // let tags = [];
      // if (item.itemTags.length > 0) {
      //   item.itemTags.map(itemTags => {
      //     tags.push(itemTags);
      //   });
      // }
      // this.infoData.recordItems.map(i => {
      //   if (i.itemCode === item.itemCode) {
      //     value.map(val => {
      //       item.checkboxList.map(list => {
      //         if (list.resultId === val) {
      //           tags.push(list);
      //         }
      //       });
      //     });
      //     // 去重赋值
      //     let newArr = [];
      //     tags.map(it => {
      //       if (!newArr.includes(it)) {
      //         newArr.push(it);
      //       }
      //     });
      //     i.itemTags = newArr;
      //     item.itemTags = newArr;
      //   }
      // });
    },
    // 复选框选择事件
    checkboxChange(value, checkbox, item) {
      if (value) {
        item.itemTags.push(checkbox);
        this.getSummary(1, checkbox);
      } else {
        let index = item.itemTags.findIndex(
          (i) => i.resultId === checkbox.resultId
        );
        item.itemTags.splice(index, 1);
        this.getSummary(3, checkbox);
      }
    },
    // 获取小结
    getSummary(editMode, tag) {
      let data = {
        editMode: editMode,
        currentItemTagCopy: tag,
        recordComb: {
          recordComb: this.infoData.recordComb,
          combTags: this.$parent.dynamicTags,
          recordItems: this.infoData.recordItems
        }
      };
      this.$ajax.post(this.$apiUrls.EditItemTag, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        tag.id = returnData.currentItemTag?.id;
        this.$parent.dynamicTags = returnData.combTags;
      });
    },
    // 标签关闭按钮
    handleClose(tag, item) {
      item.itemTags.splice(item.itemTags.indexOf(tag), 1);
      if (tag.resultId) {
        item.checked.splice(item.checked.indexOf(tag.resultId), 1);
      }
      this.getSummary(3, tag);
    },
    // 显示输入框
    showInput(item, index) {
      this.inputIndex = index;
      item.showResultInput = true;
      this.$nextTick(() => {
        this.$refs[`saveTagInput${this.inputIndex}`][0].focus();
      });
    },
    // 输入框确认
    handleInputConfirm(item) {
      console.log('item: ', item);
      if (item.resultInput) {
        item.itemTags.push({
          id: 0,
          itemCode: item.itemCode,
          tag: item.resultInput,
          resultId: 0,
          isCalcResul: false,
          calcItemTagIds: [],
          abnormalType: 0
        });
      }
      item.showResultInput = false;
      item.resultInput = '';
    }
  }
};
</script>

<style lang="less" scoped>
.checkboxGroup {
  flex: 1;
  flex-shrink: 0;
  overflow: auto;
  color: #2d3436;
  display: flex;
  flex-direction: column;
  .qijian_img {
    position: fixed;
    left: 50%;
    transform: translate(-50%, -50%);
    top: 50%;
    z-index: 5;
  }
  .checkbox-wrap {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
  }
  .wrap-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 9px 17px;
    background: rgba(23, 112, 223, 0.2);
    font-size: 14px;
    border-bottom: 1px solid #d8dee1;
  }
  .title-text {
    font-weight: 600;
  }
  .select-input {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    width: 30%;
    font-weight: 600;
  }
  .select {
    width: 60%;
    margin-left: 10px;
  }
  .checkbox-list {
    padding: 18px;
  }
  .checkbox-item {
    // width: 170px;
    margin-bottom: 20px;
    margin-right: 34px;
    color: #2d3436;
    /deep/.el-checkbox__label {
      display: inline-grid;
      width: 194px;
      white-space: pre-line;
      word-wrap: break-word;
      line-height: 20px;
    }
    /deep/.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
      background-color: #409eff;
      border-color: #409eff;
    }
    /deep/.el-checkbox__input.is-disabled.is-checked
      .el-checkbox__inner::after {
      border-color: #fff;
    }
  }
  .abnormal {
    color: #d63031;
    font-weight: 600;
  }
  .state {
    padding: 9px 11px;
    background: rgba(23, 112, 223, 0.1);
    border-top: 1px solid #d8dee1;
    border-bottom: 1px solid #d8dee1;
    font-size: 14px;
    span {
      font-weight: 600;
    }
  }
  .result {
    min-height: 120px;
    max-height: 500px;
    display: flex;
    border-top: 1px solid #d8dee1;
    border-bottom: 1px solid #d8dee1;
  }
  .result-title {
    padding: 32px 19px;
    background: #f0f2f3;
    font-weight: 600;
    font-size: 14px;
    border-right: 1px solid #d8dee1;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .content {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    padding-bottom: 10px;
  }
  .tag {
    margin-left: 10px;
    margin-top: 10px;
    white-space: pre-line;
    word-wrap: break-word;
    height: auto;
  }
  .button-new-tag {
    margin-left: 10px;
    margin-top: 10px;
    height: 32px;
    line-height: 30px;
    padding-top: 0;
    padding-bottom: 0;
    font-size: 14px !important;
    /deep/.iconfont {
      font-size: 12px;
    }
  }
  .input-new-tag {
    width: 90px;
    margin-left: 10px;
    margin-top: 10px;
    vertical-align: bottom;
  }
}
</style>
