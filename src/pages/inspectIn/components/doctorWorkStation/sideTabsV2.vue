<!--
 * @FilePath: \KrPeis\src\pages\inspectIn\components\doctorWorkStation\sideTabsV2.vue
 * @Description: 项目导航组件v2版 (用于医生工作站右侧导航等)
 * @Author: justin
 * @Date: 2024-06-24 09:07:08
 * @Version: 0.0.1
 * @LastEditors: justin
 * @LastEditTime: 2024-06-24 14:14:40
*
-->

<template>
  <div class="sideTabs">
    <div class="tabs-title">
      <div class="title-item">
        <el-tooltip class="item" effect="light" placement="bottom">
          <div slot="content">
            <div class="tooltip_div">
              <span><i class="el-icon-check" style="color: #b2bec3"></i></span>
              <p>表示没有检查完</p>
            </div>
            <div class="tooltip_div">
              <span><i class="el-icon-check" style="color: #1770df"></i></span>
              <p>表示全部检查完</p>
            </div>
            <div class="tooltip_div">
              <span><i class="el-icon-check" style="color: #d63031"></i></span>
              <p>表示有异常</p>
            </div>
            <div class="tooltip_div">
              <span><i class="el-icon-info" style="color: #1770df"></i></span>
              <p>蓝色字体表示已检</p>
            </div>
          </div>
          <span>项目导航</span>
        </el-tooltip>
      </div>
    </div>
    <div class="tabs-content">
      <!-- 项目导航 -->
      <div v-show="active === 2" class="project-nav">
        <div class="collapse-content">
          <ul class="content-wrap">
            <li
              :class="[
                liIndex === li.regCombId ? 'active-li' : '',
                colorFormat(li.combStatus)
              ]"
              v-for="(li, i) in navList"
              :key="i"
              @click="navClick(li)"
            >
              {{ li.combName }}
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'sideTabs',
  props: {
    //行信息
    headerInfo: {
      typeof: Object,
      default: {}
    },
    //项目导航信息
    navList: {
      typeof: Array,
      default: []
    },
    dataQueryFlag: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      picturesDrawer: false,
      addImgDrawer: false,
      active: 2, //标签页选中
      activeName: '', //折叠面板选中
      liIndex: null, //组合选中
      isShow: true,
      template: '',
      isEnabled: '', //当前医生能否操作标志
      deptCode: '',
      checkGroup: {}, //选中的组合
      regCombs: []
    };
  },
  watch: {},
  created() {},
  mounted() {
    this.getPacsDeptCode();
  },
  methods: {
    //获取pacs科室代码
    getPacsDeptCode() {
      this.$ajax.post(this.$apiUrls.GetPacsDept).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.deptCode = returnData?.value;
      });
    },
    // 折叠面板切换
    collapseChange(val) {
      this.liIndex = null;
      this.checkGroup = {};
      this.$parent.examDeptCode = val;
    },
    // 组合点击
    navClick(li, item) {
      if (
        this.$parent.isModify &&
        JSON.stringify(this.$parent.fixed_infoData) != '{}' &&
        JSON.stringify(this.$parent.infoData?.recordItems) !=
          JSON.stringify(this.$parent.fixed_infoData?.recordItems)
      ) {
        this.$confirm('结果有修改，是否保存?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        })
          .then(() => {
            this.$parent.saves(false).then((r) => {
              this.navClickFun(li);
              this.$parent.initNavList();
            });
          })
          .catch(() => {
            this.navClickFun(li);
          });
        return;
      }
      this.navClickFun(li);
    },
    // 组合点击的通用函数
    navClickFun(li) {
      this.$parent.checkComb = li;
      this.checkGroup = li;
      this.liIndex = li.regCombId;
      let data = {
        regCombId: li.regCombId,
        regNo: this.headerInfo.regNo,
        template: li.template
      };
      this.regCombs = this.navList;
      this.$parent.regCombs = this.navList;
      this.$parent.deptCode = li.examDeptCode;
      this.deptCode = li.examDeptCode;
      this.$parent.getCombInfo(data);
      this.$parent.pageStatus = li.template;
      this.template = li.template;
      this.isEnabled = li.isEnabled;
      this.$parent.isEnabled = li.isEnabled;
      this.$parent.regCombId = li.regCombId;
      this.$parent.combCode = li.combCode;
      this.$parent.clsCode = li.clsCode;
      if (this.$parent.$refs.examine_Ref) {
        this.$parent.$refs.examine_Ref.checkRowIndex = null;
      }
      this.$parent.dynamicTags = [];
      // this.$parent.isModify = false;
      if (!li.isEnabled) {
        this.$parent.btnList = [];
        this.$parent.errorDisabled = true;
      } else {
        if (li.template == 3 && this.headerInfo.peStatus < 3) {
          this.$parent.errorDisabled = false;
          this.$parent.btnList = ['保存', '修改', '弃检', '采集'];
        } else if (li.template != 3 && this.headerInfo.peStatus < 3) {
          this.$parent.errorDisabled = false;
          this.$parent.btnList = ['保存', '修改', '弃检'];
        }
        if (li.combStatus != 1) {
          this.$parent.btnList.push('删除结果');
        }
      }
      this.$emit('navClickFun', li);
    },

    // 导航标题格式化
    colorFormat(val) {
      let color = '';
      switch (val) {
        case 1:
          color = '';
          break;
        case 2:
          color = 'cell_blue';
          break;
        case 3:
          color = 'cell_red';
          break;
      }
      return color;
    }
  }
};
</script>

<style lang="less" scoped>
.sideTabs {
  height: 100%;
  color: #2d3436;
  display: flex;
  flex-direction: column;
  .tabs-title {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0px 18px 6px 18px;
    border-bottom: 1px solid #d8dee1;
    font-family: PingFangSC-Medium;
    font-size: 16px;
    color: #1770df;
    font-weight: 550;
  }
  .title-item {
    padding: 11px 12px;
    padding-bottom: 11px;
    cursor: pointer;
    border-bottom: 2px solid #fff;

    &.active {
      color: #1770df;
      border-bottom: 2px solid #1770df;
    }
  }
  .tabs-content {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
  }
  .legend {
    border-top: 1px solid #d8dee1;
    padding: 18px 13px;
  }
  .item-add {
    width: 80px;
    height: 62px;
    background: #f3f3f5;
    border-radius: 4px;
    border: 1px solid #d5dbde;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
  .add-icon {
    color: #b2bec3;
    font-size: 32px;
  }
  .collapse-item {
    &.active-collapse/deep/.el-collapse-item__header {
      background: rgba(23, 112, 223, 0.2);
    }
    &.active-combError/deep/.el-collapse-item__header {
      background: rgba(214, 48, 49, 0.8);
    }
  }
  /deep/.el-collapse-item__header {
    height: 38px;
    background: rgba(178, 190, 195, 0.3);
    border-bottom-color: #b3abab;
  }
  .collapse-title {
    width: 100%;
    padding-left: 28px;
    font-size: 14px;
    font-weight: 600;
    position: relative;
  }
  .examine-icon {
    width: 20px;
    height: 20px;
    position: absolute;
    left: 4px;
    top: 50%;
    transform: translateY(-50%);
  }
  .content-wrap {
    margin-left: 18px;
    border-left: 1px solid #b2bec3;

    li {
      padding: 7px;
      padding-left: 16px;
      font-size: 14px;
      cursor: pointer;
      position: relative;
      &:last-child {
        margin-bottom: 0;
      }
      &.active-li::before {
        content: '';
        display: block;
        width: 14px;
        height: 14px;
        background: url('../../../../assets/img/doctorWorkStation/position.png')
          no-repeat 100% / cover;
        position: absolute;
        left: -6px;
        top: 50%;
        transform: translateY(-50%);
        z-index: 1;
      }
      &.active-li::after {
        content: '';
        display: block;
        width: 1px;
        height: 100%;
        background: #1770df;
        position: absolute;
        left: -1px;
        top: 0;
      }
    }
  }
  /deep/.el-collapse-item__content {
    padding-bottom: 20px;
  }
}
@media screen and(max-width: 1440px) {
  .sideTabs {
    .tabs-title {
      padding: 4px;
    }
    .title-item {
      padding: 0;
    }
  }
}
</style>
<style lang="less">
.tooltip_div {
  display: flex;
  margin-bottom: 5px;
  width: 200px;
  span {
    width: 16px;
    margin-right: 5px;
    flex-shrink: 0;
    i {
      font-size: 16px;
      font-weight: bold;
    }
  }
  p {
    flex: 1;
  }
}
</style>
