<template>
  <div class="checkCollectPictures">
    <div class="topDiv">
      <div class="pictures-box">
        <div class="iconDiv">
          <div class="iDiv" @click="prevImg">
            <i class="icon el-icon-arrow-left"> </i>
          </div>
        </div>
        <div class="bigImg">
          <el-image :src="bigImage"></el-image>
        </div>
        <div class="iconDiv">
          <div class="iDiv" @click="nextImg">
            <i class="icon el-icon-arrow-right"> </i>
          </div>
        </div>
      </div>
    </div>
    <div class="bomDiv">
      <div class="img-box">
        <el-image
          :class="actIndex === index ? 'item-active' : ''"
          v-for="(url, index) in urls"
          :key="index"
          :src="url.imagePath"
          lazy
          @click="imgClick(url.imagePath, index)"
        ></el-image>
      </div>
    </div>
  </div>
</template>
<script>
import BtnCommon from '../btnCommon.vue';
export default {
  name: 'checkCollectPictures',
  props: {
    // 图片
    mainImage: {
      type: String,
      default: () => {
        return '';
      }
    },
    // 图片
    imgActive: {
      type: Number,
      default: () => {
        return null;
      }
    },

    imgList: {
      typeof: Array,
      default: () => {
        return [];
      }
    }
  },
  components: { BtnCommon },
  data() {
    return {
      bigImage: this.mainImage,
      actIndex: this.imgActive,
      urls: this.imgList
    };
  },
  mounted() {},
  methods: {
    //上一张
    prevImg() {
      if (this.actIndex == 0) {
        this.actIndex = this.urls.length - 1;
      } else {
        this.actIndex--;
      }
      for (var i = 0; i < this.urls.length; i++) {
        if (i == this.actIndex) {
          this.bigImage = this.urls[i].imagePath;
        }
      }
    },
    //下一张
    nextImg() {
      if (this.actIndex >= this.urls.length - 1) {
        this.actIndex = 0;
      } else {
        this.actIndex++;
      }
      for (var i = 0; i < this.urls.length; i++) {
        if (i == this.actIndex) {
          this.bigImage = this.urls[i].imagePath;
        }
      }
    },
    imgClick(url, index) {
      this.actIndex = index;
      this.bigImage = url;
      console.log(this.actIndex);
    }
  }
};
</script>
<style lang="less" scoped>
.checkCollectPictures {
  height: 100%;
  color: #2d3436;
  display: flex;
  flex-direction: column;
  padding: 0 18px 18px 18px;
  .topDiv {
    flex: 1;
    background: rgba(178, 190, 195, 0.1);
    border: 1px solid #eee;
    border-radius: 4px;
    margin: auto;
    overflow: auto;
    width: 100%;
    .pictures-box {
      display: flex;
      flex-direction: row;
      height: 100%;
      justify-content: space-between;
      align-items: center;
      .bigImg {
        // width: calc(100% - 200px);
        height: 100%;
        margin: 0 30px;
        overflow: auto;
        .el-image {
          height: 99%;
        }
      }
      .iconDiv {
        flex: 1;
        display: flex;
        .iDiv {
          cursor: pointer;
          width: 64px;
          height: 180px;
          line-height: 180px;
          text-align: center;
          background: rgba(178, 190, 195, 0.2);
          i {
            font-size: 40px;
          }
        }
      }
    }
  }
  .bomDiv {
    height: 140px;
    padding: 10px;
    display: flex;
    flex-direction: row;
    background: rgba(178, 190, 195, 0.1);
    border: 1px solid #eee;
    border-radius: 4px;
    overflow: auto;
    .img-box {
      flex: 1;
      padding: 8px;
      flex-shrink: 0;
      // overflow: auto;
      display: flex;
      .el-image {
        padding: 8px;
        margin-left: 10px;
        width: 170px;
        cursor: pointer;
      }
      .item-active {
        border: 2px solid #1770df;
        border-radius: 4px;
      }
    }
  }
}
</style>
