<template>
  <div class="OPOrdering_page pages">
    <iframe
      :src="`https://gdbyway-apps.sschospital.cn/app/emr-cpoe-fe/loginClinical?isClientGrant=true&authorization=MjAwMTk6UEVfU1lTVEVN&userId=${infoData.userId}&branchId=${infoData.branchId}&deptName=${infoData.deptName}&deptId=${infoData.deptId}&username=${infoData.userCode}&staffName=${infoData.staffName}&mrsSwitch=true&mrsSwitchPermission=fullView&mrsSwitchPermission=showHomeQueueIcon&patId=`"
      frameborder="0"
    ></iframe>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  name: 'OPOrdering',
  computed: {
    ...mapGetters(['G_userInfo'])
  },
  data() {
    return {
      infoData: {}
    };
  },
  created() {
    let userInfo = this.G_userInfo.clinicUserInfo;
    let text = {
      userId: userInfo?.userId,
      userCode: userInfo?.userId,
      staffName: encodeURIComponent(userInfo?.staffName),
      deptName: encodeURIComponent(userInfo?.deptName),
      deptId: userInfo?.deptId,
      branchId: userInfo?.branchId,
      loginType: 'normal'
    };
    this.infoData = {
      ...text
    };
  }
};
</script>

<style lang="less" scoped>
.OPOrdering_page {
  display: flex;
  flex-direction: column;
  iframe {
    flex: 1;
    width: 100%;
    overflow: auto;
  }
}
</style>
