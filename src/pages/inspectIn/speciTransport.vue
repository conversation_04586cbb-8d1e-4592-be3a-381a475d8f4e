<template>
  <div class="speciTransport">
    <ul class="group_head">
      <li>
        <div class="every_inp radio">
          <label style="width: 0"></label>
          <p>
            <el-radio-group v-model.trim="radioSel" @change="radioChange">
              <el-radio :label="0">全部</el-radio>
              <el-radio :label="1">个人</el-radio>
              <el-radio :label="2">团体</el-radio>
            </el-radio-group>
          </p>
        </div>
        <div class="every_inp">
          <label>单位</label>
          <p>
            <el-cascader
              ref="company_cascader_ref"
              v-model="sampleForm.companyCode"
              :options="companyList"
              :props="{ multiple: false }"
              clearable
              filterable
              size="small"
              collapse-tags
              @change="companyChange"
              :filter-method="filterMethod"
            >
            </el-cascader>
          </p>
        </div>
        <div class="every_inp times">
          <label>采集时间</label>
          <p>
            <el-date-picker
              :picker-options="{ shortcuts: G_datePickerShortcuts }"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              v-model.trim="acquisitionTime"
              :clearable="false"
              size="small"
            >
            </el-date-picker>
          </p>
        </div>
        <div class="every_inp">
          <label><span class="redColor">*</span>标本类别</label>
          <p>
            <el-select
              placeholder="请选择"
              size="small"
              v-model.trim="sampleForm.sampleCode"
              filterable
            >
              <el-option
                v-for="item in codeSampleArr"
                :value="item.sampCode"
                :label="item.sampName"
                :key="item.sampCode"
              >
              </el-option>
            </el-select>
          </p>
        </div>
        <div class="every_inp btn_inp">
          <label style="width: 0"> </label>
          <p>
            <BtnCommon :btnList="['查询']" @search="search" />
          </p>
        </div>
      </li>
      <li>
        <div class="every_inp">
          <label>条码分类</label>
          <p>
            <el-select
              placeholder="请选择"
              size="small"
              v-model.trim="sampleForm.barcodeType"
              clearable
              filterable
            >
              <el-option
                v-for="item in barcodeArr"
                :label="item.barcodeName"
                :value="item.barcode"
                :key="item.barcode"
              ></el-option>
            </el-select>
          </p>
        </div>
        <div class="every_inp">
          <label>条码号</label>
          <p>
            <el-input
              size="small"
              placeholder="请输入"
              clearable
              v-model.trim="sampleNo"
              @keyup.enter.native="readUnPackedSampleBySampleNo"
            ></el-input>
          </p>
        </div>
        <div class="every_inp times">
          <label><span class="redColor">*</span>序号段</label>
          <p style="display: flex; align-items: center">
            <el-input
              size="small"
              placeholder="请输入"
              clearable
              v-model.trim="sampleForm.beginBarcodeSN"
              onkeyup="this.value=this.value.replace(/\D|^/g,'')"
            ></el-input
            >-
            <el-input
              size="small"
              placeholder="请输入"
              clearable
              v-model.trim="sampleForm.endBarcodeSN"
              onkeyup="this.value=this.value.replace(/\D|^/g,'')"
            ></el-input>
          </p>
        </div>
        <div class="every_inp">
          <label>包号</label>
          <p>
            <el-input
              size="small"
              placeholder="请输入"
              clearable
              v-model.trim="searchPackageNo"
              @keyup.enter.native="readSamplePackageTime"
            ></el-input>
          </p>
        </div>
        <div class="every_inp" style="width: 10%">
          <label style="width: 0"> </label>
          <p></p>
        </div>
      </li>
    </ul>

    <div class="contDiv">
      <div class="leftCont">
        <div class="searchHear">
          <span class="title">
            <span>
              已选：<span class="colorBlue">{{ sampleNoArr.length }} 个</span>
            </span>
          </span>
          <BtnCommon :btnList="['打包运送']" @packRansport="packRansport" />
        </div>
        <div class="tableCont">
          <PublicTable
            ref="leftTable"
            :viewTableList.sync="tableData"
            :theads.sync="theads"
            :tableLoading.sync="loading"
            @selectionChange="handleSelRow"
            isCheck
            :columnWidth="columnWidth"
          >
            <template #sex="{ scope }">
              <div>
                {{ sexList[scope.row.sex] }}
              </div>
            </template>
          </PublicTable>
        </div>
      </div>
      <div class="rightCont">
        <div class="rightTop">
          <p class="headerTitle">
            <span>
              包列表：<span class="headerTitle-text">（双击剔除）</span>
            </span>
            <BtnCommon
              :btnList="['重新传输']"
              @retransmission="retransmission"
            />
          </p>
          <div class="rightTableCont">
            <PublicTable
              ref="rightTable"
              :viewTableList.sync="rightTopTableData"
              :theads.sync="rightToptheads"
              :tableLoading.sync="loading"
              @rowDblclick="deletePackage"
              @rowClick="readSampleByPackageNo"
            >
            </PublicTable>
          </div>
        </div>
        <div class="rightBom">
          <p class="headerTitle">
            <span>
              包号：{{ packageNo
              }}<span class="headerTitle-text">（双击剔除）</span>
            </span>
            <BtnCommon :btnList="['打印']" @prints="prints" />
          </p>
          <div class="rightTableCont">
            <PublicTable
              ref="bomTable"
              :viewTableList.sync="rightBomTableData"
              :theads.sync="rightBomtheads"
              :tableLoading.sync="loading"
              :isSortShow="false"
              @rowDblclick="deletePackageSample"
              :columnWidth="columnWidth"
            >
              <template #sex="{ scope }">
                <div>
                  {{ sexList[scope.row.sex] }}
                </div>
              </template>
            </PublicTable>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import BtnCommon from './components/btnCommon.vue';
import PublicTable from '../../components/publicTable.vue';
import { mapGetters } from 'vuex';
import { dataUtils } from '@/common';
import shortcut from '@/common/shortcut';
export default {
  name: 'speciTransport',
  mixins: [shortcut],
  components: { BtnCommon, PublicTable },
  data() {
    return {
      shortcutList: {
        112: this.packRansport,
        113: this.retransmission,
        114: this.prints
      },
      companyList: [], //单位
      codeSampleArr: [],
      barcodeArr: [],
      acquisitionTime: [dataUtils.getDate(), dataUtils.getDate()],
      radioSel: 0,
      sampleForm: {
        isAll: true,
        isCompanyCheck: false,
        companyCode: '',
        beginGatherTime: '',
        endGatherTime: '',
        sampleCode: '',
        barcodeType: '',
        beginBarcodeSN: null,
        endBarcodeSN: null
      },
      sampleNo: '', //条码号
      searchPackageNo: '', //包号
      loading: false,
      theads: {
        activeTime: '体检日期',
        barcodeSN: '条码序号',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        regNo: '体检号',
        barcodeType: '体检分类',
        sampleNo: '试管条码'
      },
      tableData: [],
      sexList: {
        '-1': '全部',
        0: '通用',
        1: ' 男',
        2: '女'
      },
      columnWidth: { activeTime: 180, regNo: 150, sampleNo: 150 },
      sampleNoArr: [],

      rightToptheads: {
        packageNo: '包号',
        packageTime: '打包时间'
      },
      rightTopTableData: [],
      rightBomtheads: {
        barcodeSN: '条码序号',
        sampleNo: '条码号',
        name: '姓名',
        regNo: '体检号',
        sex: '性别',
        age: '年龄'
      },
      rightBomTableData: [],
      packageNo: '',
      byPackageNoGetLists: []
    };
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_sysOperator',
      'G_userInfo',
      'G_datePickerShortcuts'
    ])
  },
  mounted() {
    this.getCompany();
    this.getCodeSample();
    this.getBarcodeType();
  },

  methods: {
    //获取单位下拉
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) return;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.acquisitionTime = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.search();
    },

    //获取标本类别下拉
    getBarcodeType() {
      this.$ajax.post(this.$apiUrls.BarcodeType).then((r) => {
        console.log('BarcodeType: ', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.barcodeArr = returnData || [];
      });
    },

    //获取条码分类下拉
    getCodeSample() {
      this.$ajax.post(`${this.$apiUrls.RD_CodeSample}/Read `, []).then((r) => {
        console.log('RD_CodeSample: ', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.codeSampleArr = returnData || [];
      });
    },

    //左边表格勾选操作
    handleSelRow(row) {
      this.sampleNoArr = [];
      row.map((item, index) => {
        this.sampleNoArr.push(item.sampleNo);
      });
      console.log('[this.sampleNoArr]-320', this.sampleNoArr);
    },
    //切换radio时,条件通过就查数据
    radioChange() {
      if (
        this.sampleForm.sampleCode &&
        this.sampleForm.beginBarcodeSN &&
        this.sampleForm.endBarcodeSN
      ) {
        this.search();
      }
    },
    //查询
    search() {
      this.readUnPackedSample();
    },

    //标本运送：按条件查询未打包标本信息(查询左边表格列表)
    readUnPackedSample() {
      if (this.radioSel == 0) {
        this.sampleForm.isAll = true;
        this.sampleForm.isCompanyCheck = false;
      } else if (this.radioSel == 1) {
        this.sampleForm.isAll = false;
        this.sampleForm.isCompanyCheck = false;
      } else {
        this.sampleForm.isAll = false;
        this.sampleForm.isCompanyCheck = true;
      }
      if (!this.acquisitionTime) {
        this.sampleForm.beginGatherTime = dataUtils.getDate();
        this.sampleForm.endGatherTime = dataUtils.getDate();
      } else {
        this.sampleForm.beginGatherTime = this.acquisitionTime[0];
        this.sampleForm.endGatherTime = this.acquisitionTime[1];
      }
      if (!this.sampleForm.sampleCode) {
        this.$message({
          message: '标本类别必须选择一项',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (!this.sampleForm.beginBarcodeSN || !this.sampleForm.endBarcodeSN) {
        this.$message({
          message: '序号段必须填写完整',
          type: 'warning',
          showClose: true
        });
        //标本运送：获取标本包列表
        // this.readSamplePackage();
        return;
      }
      if (
        this.sampleForm.beginBarcodeSN > ********* ||
        this.sampleForm.endBarcodeSN > *********
      ) {
        this.$message({
          message: '序号段不能大于九位数',
          type: 'warning',
          showClose: true
        });
        return;
      }
      console.log('[  this.sampleForm ]-315', this.sampleForm);
      const temp = JSON.parse(JSON.stringify(this.sampleForm));
      temp.companyCode = temp.companyCode?.[0] || '';

      this.$ajax.post(this.$apiUrls.ReadUnPackedSample, temp).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        if (returnData.length >= 1) {
          this.tableData = returnData;
        } else {
          this.tableData = [];
          // this.$message({
          //   message: "暂无数据",
          //   type: "success",
          //   showClose: true,
          // });
        }
        let datas = {
          beginPackageTime: this.acquisitionTime[0],
          endPackageTime: this.acquisitionTime[1],
          sampleCode: this.sampleForm.sampleCode
        };
        //标本运送：获取标本包列表
        this.readSamplePackage(datas);
      });
    },

    //标本运送：获取标本包列表(查询右边包列表)
    readSamplePackage(datas) {
      this.$ajax
        .post(this.$apiUrls.ReadSamplePackage, '', {
          query: datas
        })
        .then((r) => {
          let { returnData, success } = r.data;
          if (!success) return;
          if (returnData.length >= 1) {
            this.rightTopTableData = returnData;
          } else {
            this.rightTopTableData = [];
            this.rightBomTableData = [];
            //查询都无数据统一提示
            this.$nextTick(() => {
              if (
                this.tableData.length < 1 &&
                this.rightTopTableData.length < 1
              ) {
                this.$message({
                  message: '暂无数据',
                  type: 'success',
                  showClose: true
                });
              }
            });
          }
        });
    },

    //标本运送：条码号获取未打包标本信息
    readUnPackedSampleBySampleNo() {
      let datas = { sampleNo: this.sampleNo };
      this.$ajax
        .post(this.$apiUrls.ReadUnPackedSampleBySampleNo, '', {
          query: datas
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          //有数据时
          if (returnData) {
            const isInclueData = this.tableData.filter(
              (item) => returnData.sampleNo == item.sampleNo
            );
            //判断列表是否有该数据,有就把它放置第一行并选中
            if (isInclueData.length > 0) {
              console.log('[ this.tableData ]-421', this.tableData);
              const noInclueData = this.tableData.filter(
                (item) => returnData.sampleNo != item.sampleNo
              );
              this.tableData = [...isInclueData, ...noInclueData];
              console.log('[ this.tableData ]-445', this.tableData);
            } else {
              //列表没有该数据就放置第一行
              this.tableData.unshift(returnData);
            }
            //数据排序后默认选中第一行(该行就是第一条数据)
            this.$nextTick(() => {
              this.$refs.leftTable.$refs.tableCom_Ref.setCurrentRow(
                this.tableData[0]
              );
              this.$refs.rightTable.$refs.tableCom_Ref.setCurrentRow();
              this.$refs.rightTable.$refs.tableCom_Ref.toggleRowSelection(
                this.rightTopTableData[0],
                false
              );
              this.packageNo = '';
              this.rightBomTableData = [];
            });
          } else {
            //查询为空->执行另一条接口//标本运送：条码号获取取条码号所在包
            this.readPackageBySampleNo();
          }
        });
    },
    //标本运送：获取包标本编码与打包时间
    readSamplePackageTime() {
      let datas = { packageNo: this.searchPackageNo };
      this.$ajax
        .post(this.$apiUrls.ReadSamplePackageTime, '', {
          query: datas
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          console.log(returnData);
          if (returnData) {
            let datas = {
              beginPackageTime: this.formatDate(returnData.packageTime),
              endPackageTime: this.formatDate(returnData.packageTime),
              sampleCode: returnData.sampleCode
            };
            this.bySamplePackage(datas);
          } else {
            this.$message({
              message: '查无该包号数据',
              type: 'success',
              showClose: true
            });
          }
        });
    },
    //  时间字符串格式化 yyyy-mm-dd
    formatDate(date) {
      var d = new Date(date),
        month = '' + (d.getMonth() + 1),
        day = '' + d.getDate(),
        year = d.getFullYear();
      if (month.length < 2) month = '0' + month;
      if (day.length < 2) day = '0' + day;
      return [year, month, day].join('-');
    },

    //通过包号查询返回标本类别查询包列表
    bySamplePackage(datas) {
      this.$ajax
        .post(this.$apiUrls.ReadSamplePackage, '', {
          query: datas
        })
        .then((r) => {
          let { returnData, success } = r.data;
          if (!success) return;
          if (returnData.length >= 1) {
            this.rightTopTableData = returnData;
            this.$nextTick(() => {
              this.byPackageNoFindList();
            });
          } else {
            this.$message({
              message: '查无该包号数据',
              type: 'success',
              showClose: true
            });
          }
        });
    },
    //通过包号去查找该包号所在位置
    byPackageNoFindList() {
      let packageNo = this.searchPackageNo;
      if (packageNo) {
        const isInclueData = this.rightTopTableData.filter(
          (item) => packageNo == item.packageNo
        );
        //判断列表是否含有该包号数据,有就把它放置第一行
        if (isInclueData.length > 0) {
          const noInclueData = this.rightTopTableData.filter(
            (item) => packageNo != item.packageNo
          );

          console.log('[  isInclueData]-492', isInclueData);

          console.log('[ noInclueData ]-494', noInclueData);
          this.rightTopTableData = [...isInclueData, ...noInclueData];
        } else {
          this.$message({
            message: '包列表暂无包号数据!',
            type: 'success',
            showClose: true
          });
          return;
        }
        //数据排序后默认选中第一行(该行就是第一条数据)
        this.$nextTick(() => {
          this.$refs.rightTable.$refs.tableCom_Ref.setCurrentRow(
            this.rightTopTableData[0]
          );
        });
        let packageNos = { packageNo: packageNo };
        this.readSampleByPackageNo(packageNos, '');
      }
    },

    //标本运送：打包运送
    packRansport() {
      if (this.sampleNoArr.length < 1) {
        this.$message({
          message: '请先选择要打包运送的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let datas = {
        sampleNos: this.sampleNoArr,
        operatorCode: this.G_userInfo.codeOper.operatorCode
      };
      console.log('[ datas ]-466233333333', datas);
      this.$ajax.post(this.$apiUrls.PackTransportSample, datas).then((r) => {
        let { success, returnData, returnMsg } = r.data;
        if (!success) return;
        if (!returnData) {
          this.$message({
            message: returnMsg,
            type: 'error',
            showClose: true
          });
          return;
        }
        this.$message({
          message: returnMsg,
          type: 'success',
          showClose: true
        });
        //手动过滤已打包运送数据
        this.sampleNoArr.forEach((val, index) => {
          this.tableData.forEach((v, i) => {
            if (val == v.sampleNo) {
              this.tableData.splice(i, 1);
            }
          });
        });
        console.log('[ this.tableData ]-483', this.tableData);
        let datas = {
          beginPackageTime: this.acquisitionTime[0],
          endPackageTime: this.acquisitionTime[1],
          sampleCode: this.sampleForm.sampleCode
        };
        this.readSamplePackage(datas);
      });
    },

    //标本运送：条码号获取取条码号所在包
    readPackageBySampleNo() {
      this.$ajax
        .post(this.$apiUrls.ReadPackageBySampleNo, '', {
          query: {
            sampleNo: this.sampleNo
          }
        })
        .then((r) => {
          let { returnData, success } = r.data;
          if (!success) return;
          //有数据时
          if (returnData) {
            let arr = returnData.packageNo;
            const isInclueData = this.rightTopTableData.filter((item) =>
              arr.includes(item.packageNo)
            );
            //判断列表是否有该数据,有就把它放置第一行
            if (isInclueData.length > 0) {
              let checkArr = returnData.packageNo;
              const noInclueData = this.rightTopTableData.filter(
                (item) => !checkArr.includes(item.packageNo)
              );
              this.rightTopTableData = [...isInclueData, ...noInclueData];
            } else {
              //列表没有该数据就放置第一行
              this.rightTopTableData.unshift(returnData);
            }
            //数据排序后默认选中第一行(该行就是第一条数据)
            this.$nextTick(() => {
              this.$refs.rightTable.$refs.tableCom_Ref.setCurrentRow(
                this.rightTopTableData[0]
              );
            });
            let packageNos = { packageNo: returnData.packageNo };
            this.readSampleByPackageNo(packageNos, this.sampleNo);
          } else {
            this.$message({
              message: '暂无该条码号数据!',
              type: 'success',
              showClose: true
            });
          }
        });
    },

    // 标本运送：删除包信息
    deletePackage(row) {
      this.$confirm('是否确认剔除该条数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          //查询删除包所包含的数据
          this.byPackageNoGetList(row.packageNo);
          this.$ajax
            .post(this.$apiUrls.DeletePackage, '', {
              query: { packageNo: row.packageNo }
            })
            .then((r) => {
              console.log('r111: ', r);
              const { returnData, success } = r.data;
              if (!success) {
                return;
              }
              this.$message.success('删除成功!');
              this.rightBomTableData = [];
              //手动过滤已删除数据
              this.rightTopTableData.forEach((v, i) => {
                if (row.packageNo == v.packageNo) {
                  this.rightTopTableData.splice(i, 1);
                }
              });
              //把删除包所包含的数据恢复到左边表格
              if (this.byPackageNoGetLists.length >= 1) {
                this.byPackageNoGetLists.forEach((item, index) => {
                  this.bySampleNoGetList(item.sampleNo);
                });
                this.packageNo = '';
              }
            });
        })
        .catch(() => {
          return;
        });
    },

    //标本运送：根据包号获取标本列表
    readSampleByPackageNo(row, sampleNo) {
      this.$ajax
        .post(this.$apiUrls.ReadSampleByPackageNo, '', {
          query: { packageNo: row.packageNo }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.packageNo = row.packageNo;
          if (returnData.length >= 1) {
            this.rightBomTableData = returnData;
            if (sampleNo) {
              const isInclueData = this.rightBomTableData.filter(
                (item) => sampleNo == item.sampleNo
              );
              //判断列表是否含有该条码数据,有就把它放置第一行
              if (isInclueData.length > 0) {
                const noInclueData = this.rightBomTableData.filter(
                  (item) => sampleNo != item.sampleNo
                );
                this.rightBomTableData = [...isInclueData, ...noInclueData];
              }
              //数据排序后默认选中第一行(该行就是第一条数据)
              this.$nextTick(() => {
                this.$refs.bomTable.$refs.tableCom_Ref.setCurrentRow(
                  this.rightBomTableData[0]
                );
              });
            }
          } else {
            this.rightBomTableData = [];
            // this.$message({
            //   message: "暂无数据",
            //   type: "success",
            //   showClose: true,
            // });
          }
        });
    },

    // 标本运送：删除包标本
    deletePackageSample(row) {
      this.$confirm('是否确认剔除该条数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax
            .post(this.$apiUrls.DeletePackageSample, '', {
              query: { sampleNo: row.sampleNo }
            })
            .then((r) => {
              console.log('r111: ', r);
              const { returnData, success } = r.data;
              if (!success) {
                return;
              }
              this.$message.success('删除成功!');
              //手动过滤已删除数据
              this.rightBomTableData.forEach((v, i) => {
                if (row.sampleNo == v.sampleNo) {
                  this.rightBomTableData.splice(i, 1);
                }
              });
              //把该条数据恢复到左边表格
              this.bySampleNoGetList(row.sampleNo);
            });
        })
        .catch(() => {
          return;
        });
    },
    //通过包号获取左边表格数据
    byPackageNoGetList(packageNo) {
      this.$ajax
        .post(this.$apiUrls.ReadSampleByPackageNo, '', {
          query: { packageNo: packageNo }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.byPackageNoGetLists = returnData || [];
        });
    },
    //通过条码获取左边表格数据
    bySampleNoGetList(sampleNo) {
      let datas = { sampleNo: sampleNo };
      this.$ajax
        .post(this.$apiUrls.ReadUnPackedSampleBySampleNo, '', {
          query: datas
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          //有数据时
          if (returnData) {
            this.tableData.unshift(returnData);
          }
        });
    },
    //重新传输
    retransmission() {},

    //打印
    prints() {}
  }
};
</script>

<style lang="less" scoped>
.speciTransport {
  display: flex;
  width: 100%;
  height: 100%;
  border-radius: 4px;
  display: flex;
  color: #2d3436;
  flex-direction: column;

  .group_head {
    background: #fff;
    padding: 10px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 10px;
    border-radius: 4px;
    li {
      display: flex;
      height: 32px;
      margin-bottom: 18px;
      &:last-child {
        margin-bottom: 0;
      }
    }

    .every_inp {
      display: flex;
      align-items: center;
      margin-right: 10px;
      width: 20%;
      label {
        margin-right: 10px;
        width: 75px;
        text-align: right;
      }

      p {
        flex: 1;
      }

      &:nth-child(4) {
        flex: 1;
      }
    }
    .times {
      width: 30%;
      .el-date-editor--daterange.el-input__inner {
        width: 100%;
      }
    }
    .radio {
      label {
        margin-right: 0;
      }
    }
    .btn_inp {
      width: 10%;
    }
  }
  .headerTitle-text {
    font-size: 14px;
    font-weight: normal;
  }
  .contDiv {
    flex: 1;
    width: 100%;
    display: flex;
    overflow: auto;
    .leftCont {
      width: 55%;
      background-color: #fff;
      padding: 10px;
      display: flex;
      flex-direction: column;
      border-radius: 4px;
      .colorBlue {
        color: #1770df;
      }
      .searchHear {
        display: flex;
        justify-content: space-between;
        font-size: 18px;
        color: #2d3436;
        font-weight: 600;
        margin-bottom: 10px;
      }
      .tableCont {
        flex: 1;
        flex-shrink: 0;
        overflow: auto;
        border: 1px solid #d8dee1;
        border-radius: 4px;
      }
    }
    .rightCont {
      flex: 1;
      width: calc(35% - 18px);
      margin-left: 10px;
      background-color: #fff;
      padding: 10px;
      display: flex;
      flex-direction: column;
      overflow: auto;
      border-radius: 4px;
      .rightTop {
        display: flex;
        height: 40%;
        flex-direction: column;
        margin-bottom: 10px;
      }
      .rightBom {
        display: flex;
        flex: 1;
        flex-direction: column;
        overflow: auto;
      }
      .headerTitle {
        font-size: 18px;
        color: #2d3436;
        display: flex;
        justify-content: space-between;
        font-weight: 600;
      }
      .rightTableCont {
        flex: 1;
        margin-top: 10px;
        overflow: auto;
        border: 1px solid #d8dee1;
        border-radius: 4px;
      }
    }
  }

  .price_wrap {
    line-height: 36px;
    background: rgba(23, 112, 223, 0.1);
    border-radius: 4;
    overflow: hidden;
    padding: 0 15px;
    & > div {
      display: flex;
      justify-content: space-between;
    }
    .colorTitlte {
      font-family: PingFangSC-Medium;
      font-size: 18px;
      color: #2d3436;
    }
    .redColor {
      font-family: PingFangSC-Medium;
      font-size: 18px;
      color: #d63031;
    }
  }
  /deep/.el-dialog {
    height: 80%;
    width: 700px;
    display: flex;
    flex-direction: column;
    .dialogHear {
      display: flex;
      justify-content: space-between;
      line-height: 32px;
      height: 42px;
    }
    .dialogTable {
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
      border: 1px solid #d8dee1;
    }
  }
  .el-form-item {
    margin-bottom: 10px;
  }
  /deep/.el-select {
    width: 100%;
  }
  /deep/.el-input__inner,
  /deep/.el-range__icon {
    height: 32px;
    line-height: 32px;
  }
  /deep/.el-dialog__header {
    height: 50px;
    line-height: 50px;
    background: rgba(23, 112, 223, 0.2);
    padding: 0 18px;
  }
  /deep/.el-dialog__headerbtn {
    top: 10px;
    font-size: 30px;
  }
  /deep/.el-dialog__body {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 10px 18px 18px 18px;
    flex-shrink: 0;
    overflow: auto;
  }
  /deep/.el-form-item__label,
  /deep/.el-descriptions__body,
  /deep/.el-descriptions__body
    .el-descriptions__table
    .el-descriptions-item__cell {
    color: #2d3436;
    font-weight: 600 !important;
  }
  /deep/.el-form-item__label,
  /deep/.el-form-item__content {
    line-height: 32px;
  }
  .redColor {
    color: #d63031;
  }
  .blueColor {
    font-family: PingFangSC-Medium;
    font-size: 18px;
    color: #1770df;
  }
}
</style>
