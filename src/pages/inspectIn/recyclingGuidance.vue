<template>
  <!-- 回收指引单 -->
  <div class="recyclingGuidance">
    <div class="main-info">
      <el-form ref="form" :model="form" label-width="78px" class="main-form">
        <el-form-item label="姓名">
          <el-input
            v-model="form.name"
            size="small"
            placeholder=""
            readonly
          ></el-input>
        </el-form-item>
        <el-form-item label="档案卡号">
          <el-input
            v-model="form.patCode"
            size="small"
            placeholder=""
            readonly
          ></el-input>
        </el-form-item>
        <el-form-item label="体检次数">
          <el-input
            v-model="form.companyTimes"
            size="small"
            readonly
          ></el-input>
        </el-form-item>
        <el-form-item label="性别">
          <el-input v-model="form.sex" size="small" readonly></el-input>
        </el-form-item>
        <el-form-item label="年龄">
          <el-input v-model="form.age" size="small" readonly></el-input>
        </el-form-item>
        <el-form-item label="工作单位">
          <el-input v-model="form.companyCode" size="small" readonly></el-input>
        </el-form-item>
        <el-form-item label="身份证号">
          <el-input
            v-model="form.cardNo"
            size="small"
            placeholder=""
            readonly
          ></el-input>
        </el-form-item>
        <el-form-item label="联系地址">
          <el-input
            v-model="form.address"
            size="small"
            placeholder=""
            readonly
          ></el-input>
        </el-form-item>
        <el-form-item label="联系电话">
          <el-input
            v-model="form.tel"
            size="small"
            placeholder=""
            readonly
          ></el-input>
        </el-form-item>
        <el-form-item label="登记类型">
          <el-input
            v-model="form.registerType"
            size="small"
            readonly
          ></el-input>
        </el-form-item>
        <el-form-item label="登记员">
          <el-input
            v-model="form.operatorName"
            size="small"
            readonly
          ></el-input>
        </el-form-item>
        <el-form-item label="体检分类">
          <el-input v-model="form.peCls" size="small" readonly></el-input>
        </el-form-item>
        <el-form-item label="登记时间">
          <el-input
            v-model="form.registerTime"
            size="small"
            readonly
          ></el-input>
        </el-form-item>
        <el-form-item label="收表时间">
          <el-input
            v-model="form.guidanceRecyclyTime"
            size="small"
            readonly
          ></el-input>
        </el-form-item>
        <el-form-item label="收表人">
          <el-input
            v-model="form.guidanceRecycler"
            size="small"
            readonly
          ></el-input>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            type="textarea"
            :autosize="{ minRows: 2 }"
            placeholder="请输入备注"
            :readonly="form.isRecyle"
            v-model="form.guidanceRecycleNote"
          >
          </el-input>
        </el-form-item>
      </el-form>
    </div>
    <div class="main-wrap">
      <div class="btn-wrap">
        <el-input
          ref="search_input"
          class="btn-input"
          size="small"
          placeholder="体检号/姓名"
          clearable
          v-model="searchInput"
          @keyup.enter.native="searchClick"
        ></el-input>
        <BtnCommon
          :btnList="['查询', '列表']"
          @search="searchClick"
          @listClick="listClick"
        />
      </div>
      <div class="main-content">
        <div class="rejected-items">
          <span class="rejected-label">弃检化验项目：</span>
          <el-button
            class="btns"
            :class="item.color"
            size="small"
            v-for="item in rejectButtonList"
            :key="item.sampCode"
            :icon="item.icon"
            @click="rejectAllClick(item)"
            >{{ item.sampName }}</el-button
          >
        </div>
        <div class="content-title">
          <h3>项目情况</h3>
          <BtnCommon>
            <template #footAdd>
              <el-button
                @click="collectionClick"
                class="blue_btn btn"
                size="small"
                icon="iconfont icon-biaodan1"
                :disabled="form.isRecyle"
                >收表</el-button
              >
              <el-button
                @click="cancelCollection"
                class="yellow_btn btn"
                size="small"
                icon="iconfont icon-zuofei"
                :disabled="!form.isRecyle"
                >取消收表</el-button
              >
            </template>
          </BtnCommon>
        </div>
        <div class="list-wrap">
          <div class="list-left">
            <div class="list-title">未完成项目</div>
            <div class="items-wrap">
              <div style="background: #fff">
                <div
                  class="list-item"
                  v-for="item in unfinishCombs"
                  :key="item.combCode"
                >
                  <span>{{ item.combName }}</span>
                  <el-button
                    type="text"
                    class="text-btn"
                    @click="rejectClick(item)"
                    >弃检</el-button
                  >
                </div>
              </div>
            </div>
          </div>
          <div class="list-right">
            <div class="rejection-list">
              <div class="list-title">弃检列表</div>
              <div class="items-wrap">
                <div style="background: #fff">
                  <div
                    class="list-item"
                    v-for="item in refuseCombs"
                    :key="item.combCode"
                  >
                    <span>{{ item.combName }}</span>
                    <el-button
                      type="text"
                      class="text-btn"
                      @click="cancelRejectClick(item)"
                      :disabled="form.isRecyle"
                      >取消弃检</el-button
                    >
                  </div>
                </div>
              </div>
            </div>
            <div class="finished-items">
              <div class="list-title">完成项目</div>
              <div class="items-wrap">
                <div style="background: #fff">
                  <div
                    class="list-item"
                    v-for="item in finishCombs"
                    :key="item.combCode"
                  >
                    <span>{{ item.combName }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      :visible.sync="showUserInfoList"
      width="800px"
      top="4%"
      custom-class="showUserInfoList"
      :close-on-click-modal="false"
    >
      <div slot="title" class="dialog-title">个人信息列表</div>
      <div class="InfoList-table">
        <PublicTable
          ref="user_table_ref"
          :theads="userInfoTheads"
          url=""
          row-key="regNo"
          @rowDblclick="rowDblclick"
        >
          <template #companyCode="{ scope }">
            {{ companyData[scope.row.companyCode] }}
          </template>
          <template #sex="{ scope }">
            {{ G_EnumList['Sex'][scope.row.sex] }}
          </template>
          <template #peStatus="{ scope }">
            {{ G_EnumList['PeStatus'][scope.row.peStatus] }}
          </template>
          <template #activeTime="{ scope }">
            {{ scope.row.activeTime }}
          </template>
        </PublicTable>
      </div>
    </el-dialog>

    <!-- 收表查询窗口 -->
    <el-dialog
      :visible.sync="showList"
      width="1313px"
      top="4%"
      custom-class="showList"
      :close-on-click-modal="false"
    >
      <div slot="title" class="dialog-title">收表查询窗口</div>
      <div class="dialog-btn">
        <el-radio-group
          v-model="dialogForm.peType"
          @change="getList"
          class="search-swtich-personal-box"
        >
          <el-radio :label="0">所有</el-radio>
          <el-radio :label="1">个人</el-radio>
          <el-radio :label="2">团体</el-radio>
        </el-radio-group>

        <div style="margin-right: 10px">
          <span class="label">单位</span>
          <el-cascader
            ref="cascader_ref"
            v-model="dialogForm.companyCasList"
            :options="companyList"
            :filter-method="filterMethod"
            :props="{ multiple: false }"
            clearable
            filterable
            size="small"
            collapse-tags
            @change="companyChange"
            style="width: 200px"
          >
          </el-cascader>
        </div>

        <el-radio-group
          v-model="dialogForm.status"
          @change="getList"
          class="search-swtich-collected-box"
        >
          <el-radio :label="1">未收列表</el-radio>
          <el-radio :label="2">已收列表</el-radio>
        </el-radio-group>

        <div class="time">
          <span class="label">体检日期</span>
          <el-date-picker
            :picker-options="{ shortcuts: G_datePickerShortcuts }"
            v-model="dialogForm.date"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
            @change="getList"
            style="width: 250px"
          >
          </el-date-picker>
        </div>
        <BtnCommon
          :btnList="['查询']"
          @search="getList"
          @prints="printsClick"
        />
      </div>
      <div class="dialog-table">
        <PublicTable
          ref="collect_table_ref"
          :theads="dialogFormTheads"
          :url="$apiUrls.RecycleGuidanceQuery"
          :params="dialogFormParams"
          row-key="regNo"
          remoteByPage
          @rowDblclick="rowDblclick"
        >
          <template #isCompanyCheck="{ scope }">
            <el-checkbox
              v-model="scope.row.isCompanyCheck"
              disabled
              class="checkbox"
            ></el-checkbox>
          </template>
          <template #peCls="{ scope }">
            {{ G_EnumList['PeCls'][scope.row.peCls] }}
          </template>
          <template #companyCode="{ scope }">
            {{ companyData[scope.row.companyCode] }}
          </template>
          <template #sex="{ scope }">
            {{ G_EnumList['Sex'][scope.row.sex] }}
          </template>
          <template #peStatus="{ scope }">
            {{ G_EnumList['PeStatus'][scope.row.peStatus] }}
          </template>
        </PublicTable>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import BtnCommon from './components/btnCommon.vue';
import PublicTable from '@/components/publicTable2.vue';
import { mapGetters } from 'vuex';
import { dataUtils } from '@/common';
export default {
  name: 'recyclingGuidance',
  components: {
    BtnCommon,
    PublicTable
  },
  data() {
    return {
      searchInput: '',
      form: {
        name: '',
        patCode: '',
        companyTimes: null,
        sex: null,
        age: null,
        companyCode: '',
        cardNo: '',
        address: '',
        tel: '',
        registerType: '',
        operatorCode: '',
        peCls: null,
        registerTime: '',
        guidanceRecyclyTime: '',
        guidanceRecycler: '',
        isRecyle: false
      },
      userInfoTheads: [
        {
          prop: 'peStatus',
          label: '状态',
          align: '',
          width: '80px',
          sortable: true,
          cellClassName: 'cell_red'
        },
        {
          prop: 'regNo',
          label: '体检号',
          align: '',
          width: '120px',
          sortable: true
        },
        {
          prop: 'name',
          label: '姓名',
          align: '',
          width: '80px',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'sex',
          label: '性别',
          align: '',
          width: '60px',
          sortable: false
        },
        {
          prop: 'age',
          label: '年龄',
          align: '',
          width: '60px',
          sortable: false
        },
        {
          prop: 'activeTime',
          label: '体检时间',
          align: '',
          width: '160px',
          sortable: true
        },
        {
          prop: 'patCode',
          label: '档案卡号',
          align: '',
          width: '120px',
          sortable: false
        },
        {
          prop: 'cardNo',
          label: '身份证',
          align: '',
          width: '180px',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'tel',
          label: '电话',
          align: '',
          width: '120px',
          sortable: false
        },
        {
          prop: 'companyCode',
          label: '单位',
          align: '',
          width: '200px',
          sortable: false,
          showOverflowTooltip: true
        }
      ],
      showList: false,
      showUserInfoList: false,
      dialogForm: {
        status: 1,
        peType: 0,
        date: [new Date(), new Date()],
        companyCasList: [],
        companyCode: '',
        companyTimes: 0
      },
      dialogFormTheads: [
        {
          prop: 'peStatus',
          label: '状态',
          align: '',
          width: '100px',
          sortable: true,
          cellClassName: 'cell_red'
        },
        {
          prop: 'isCompanyCheck',
          label: '团体',
          align: '',
          width: '60px',
          sortable: false
        },
        {
          prop: 'peCls',
          label: '体检分类',
          align: '',
          width: '100px',
          sortable: true
        },
        {
          prop: 'name',
          label: '姓名',
          align: '',
          width: '100px',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'tel',
          label: '电话',
          align: '',
          width: '120px',
          sortable: false
        },
        {
          prop: 'activeTime',
          label: '体检时间',
          align: '',
          width: '160px',
          sortable: true
        },
        {
          prop: 'regNo',
          label: '体检号',
          align: '',
          width: '120px',
          sortable: true
        },
        {
          prop: 'sex',
          label: '性别',
          align: '',
          width: '60px',
          sortable: false
        },
        {
          prop: 'age',
          label: '年龄',
          align: '',
          width: '60px',
          sortable: false
        },
        {
          prop: 'companyCode',
          label: '单位',
          align: '',
          width: '200px',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'operatorName',
          label: '登记员',
          align: '',
          width: '80px',
          sortable: false,
          showOverflowTooltip: true
        }
      ],
      dialogFormParams: {},
      unfinishCombs: [],
      refuseCombs: [],
      finishCombs: [],
      companyList: [],
      companyData: {},
      collectionType: '',
      disabled: false,
      rejectButtonList: [
        {
          sampCode: '01',
          sampName: '血',
          color: 'red_btn',
          icon: 'iconfont icon-xieye'
        },
        {
          sampCode: '02',
          sampName: '尿',
          color: 'yellow_btn',
          icon: 'iconfont icon-iconnyfx'
        },
        {
          sampCode: '03',
          sampName: '粪',
          color: 'brown_btn',
          icon: 'iconfont icon-chongwubianbianqu'
        },
        {
          sampCode: '08',
          sampName: '妇科',
          color: 'violet_btn',
          icon: 'iconfont icon-women-full'
        }
      ]
    };
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_userInfo',
      'G_peClsList',
      'G_sexList',
      'G_datePickerShortcuts'
    ])
  },
  created() {
    this.getCompanyWithTimesList();
  },
  mounted() {
    this.focusInput();
  },
  activated() {
    this.focusInput();
  },
  methods: {
    //搜索框获取焦点
    focusInput() {
      this.$refs.search_input?.$el.querySelector('input').focus();
    },
    /**
     * @description: 获取单位及次数
     * @return {*}
     */
    async getCompanyWithTimesList() {
      await this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;

        this.companyList = returnData.map((item) => {
          this.$set(this.companyData, item.companyCode, item.companyName);
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    /**
     * @author: justin
     * @description:  获取用户信息列表
     * @return {*}
     */
    getUserInfoList() {
      let data = {
        keyWord: this.searchInput,
        status: 0,
        startTime: null,
        endTime: null
      };
      this.$ajax.post(this.$apiUrls.RecycleGuidanceQuery, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        if (!returnData || returnData.length === 0) {
          this.$message({
            message: '暂无相关信息!',
            type: 'warning',
            showClose: true
          });
          return;
        }

        if (returnData.length === 1) {
          this.form = {
            ...returnData[0],
            sex: this.G_EnumList['Sex'][returnData[0].sex],
            companyCode: this.companyData[returnData[0].companyCode],
            peCls: this.G_EnumList['PeCls'][returnData[0].peCls]
          };
          this.searchInput = '';
          this.disabled = true;
          this.getCombData();
          return;
        }

        this.showUserInfoList = true;
        this.searchInput = '';
        this.disabled = true;
        this.$nextTick(() => {
          this.$refs.user_table_ref.staticLoad(returnData || []);
        });
      });
    },

    /**
     * @author: justin
     * @description: 获取回收指引单列表
     * @return {*}
     */
    getList() {
      let startTime = null;
      let endTime = null;
      if (this.dialogForm.date) {
        startTime = this.dialogForm.date[0];
        endTime = this.dialogForm.date[1];
      }
      this.dialogFormParams = {
        keyWord: '',
        status: this.dialogForm.status,
        peType: this.dialogForm.peType,
        startTime: dataUtils.dateToStrStart(startTime),
        endTime: dataUtils.dateToStrEnd(endTime),
        companyCode: this.dialogForm.companyCode,
        companyTimes: this.dialogForm.companyTimes
      };
      this.$nextTick(() => {
        this.$refs.collect_table_ref.loadData();
      });
    },

    /**
     * @author: justin
     * @description: 单位选择器内容改变回调事件
     * @param {*} val
     * @return {*}
     */
    companyChange(val) {
      const that = this;
      if (!val || val.length === 0) {
        that.dialogForm.companyCode = '';
        that.dialogForm.companyTimes = 0;
        that.setDateRangeByCompanyTimes();
        that.getList();
        return;
      }

      that.dialogForm.companyCode = that.dialogForm.companyCasList[0];
      that.dialogForm.companyTimes = that.dialogForm.companyCasList[1];
      that.setDateRangeByCompanyTimes();
      that.getList();
    },

    /**
     * @author: justin
     * @description: 根据单位体检次数所在时间，设置日期范围
     * @return {*}
     */
    setDateRangeByCompanyTimes() {
      if (!this.dialogForm.companyCode || !this.dialogForm.companyTimes) {
        this.dialogForm.date = [dataUtils.getDate(), dataUtils.getDate()];
        return;
      }

      const company = this.companyList.find(
        (x) => x.value == this.dialogForm.companyCode
      );
      if (!company) return;

      let dateArr = [];
      company.children
        .filter((x) => this.dialogForm.companyTimes == x.value)
        .forEach((x) => {
          dateArr.push(new Date(x.item.beginDate || new Date())?.getTime());
          dateArr.push(new Date(x.item.endDate || new Date())?.getTime());
        });
      if (dateArr.length == 0) return;

      const minDate = new Date(Math.min(...dateArr));
      const maxDate = new Date(Math.max(...dateArr));
      this.dialogForm.date = [minDate, maxDate];
    },

    // 获取项目数据
    getCombData() {
      this.$ajax
        .post(this.$apiUrls.ReadCombInfo, '', {
          query: { regNo: this.form.regNo }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.unfinishCombs = returnData.unfinishCombs || [];
          this.finishCombs = returnData.finishCombs || [];
          this.refuseCombs = returnData.refuseCombs || [];
        });
    },
    // 查询按钮
    searchClick() {
      if (!this.searchInput) {
        this.$message({
          message: '请输入搜索内容!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.getUserInfoList();
    },
    // 表格双击
    rowDblclick(row) {
      this.form = {
        ...row,
        sex: this.G_EnumList['Sex'][row.sex],
        companyCode: this.companyData[row.companyCode],
        peCls: this.G_EnumList['PeCls'][row.peCls]
      };

      this.showUserInfoList = false;
      this.showList = false;
      this.getCombData();
    },
    // 列表按钮
    listClick() {
      this.showList = true;
      this.getList();
    },
    // 收表请求和取消收表
    collection() {
      if (!this.form.regNo) {
        this.$message({
          message:
            this.collectionType === '/Recycle'
              ? '请选择需要收表的人!'
              : '请选择需要取消收表的人!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let data = {
        regNo: this.form.regNo,
        recycler: this.G_userInfo.codeOper.operatorCode,
        guidanceRecycleNote: this.form.guidanceRecycleNote
      };

      this.$ajax
        .post(this.$apiUrls.WhetherRecycleGuidance + this.collectionType, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$message({
            message:
              this.collectionType === '/Recycle'
                ? '收表成功!'
                : '取消收表成功!',
            type: 'success',
            showClose: true
          });
          this.searchInput = this.form.regNo;
          this.getUserInfoList();
        });
    },
    // 收表按钮
    collectionClick() {
      // if (this.unfinishCombs.length > 0) {
      //   this.$message({
      //     message: "请先处理未完成的项目再进行收表!",
      //     type: "warning",
      //     showClose: true
      //   });
      //   return;
      // }
      this.collectionType = '/Recycle';
      this.collection();
      this.focusInput();
    },
    // 取消收表
    cancelCollection() {
      this.collectionType = '/NoRecycle';
      this.collection();
      this.focusInput();
    },
    // 弃检
    rejectClick(item) {
      this.$confirm(`是否要弃检${item.combName}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          let data = {
            regNo: this.form.regNo,
            regCombId: item.regCombId,
            operName: this.G_userInfo.codeOper.name
            // sampCode: ""
          };
          this.$ajax.post(this.$apiUrls.AbandonComb, data).then((r) => {
            let { success, returnData } = r.data;
            if (!success) return;
            this.$message({
              message: '弃检成功!',
              type: 'success',
              showClose: true
            });
            this.getCombData();
          });
        })
        .catch(() => {});
    },
    // 取消拒检
    cancelRejectClick(item) {
      this.$confirm(`是否要取消弃检${item.combName}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax
            .post(this.$apiUrls.CancleAbandonComb, '', {
              query: { regNo: this.form.regNo, regCombId: item.regCombId }
            })
            .then((r) => {
              let { success, returnData } = r.data;
              if (!success) return;
              this.$message({
                message: '取消弃检成功!',
                type: 'success',
                showClose: true
              });
              this.getCombData();
            });
        })
        .catch(() => {});
    },
    // 所有项目拒检
    rejectAllClick(item) {
      if (!this.form.regNo) {
        this.$message({
          message: '请选择需要弃检项目的人!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.unfinishCombs.length === 0) {
        this.$message({
          message: '没有可以弃检的项目!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm(`是否要弃检所有的${item.sampName}类项目?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          let data = {
            regNo: this.form.regNo,
            // regCombId: null,
            operName: this.G_userInfo.codeOper.name,
            sampCode: item.sampCode
          };
          this.$ajax
            .post(this.$apiUrls.RefuseCombsByAssayType, data)
            .then((r) => {
              let { success, returnData } = r.data;
              if (!success) return;
              this.$message({
                message: `弃检${item.sampName}类项目成功!`,
                type: 'success',
                showClose: true
              });
              this.getCombData();
            });
        })
        .catch(() => {});
    },
    // 打印
    printsClick() {}
  }
};
</script>

<style lang="less" scoped>
.recyclingGuidance {
  display: flex;
  // flex-direction: column;
  color: #2d3436;
  .btn-wrap {
    display: flex;
    margin-bottom: 18px;
  }
  .btn-input {
    width: 40%;
    margin-right: 10px;
  }
  .main-wrap {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
  }
  .main-info,
  .main-content {
    background: #fff;
    border-radius: 4px;
    padding: 18px;
  }
  .main-info {
    width: 360px;
    margin-right: 10px;
    overflow: auto;
  }
  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
  }
  .main-form {
    /deep/.el-form-item__label {
      color: #2d3436;
      font-weight: 600;
    }
    /deep/.el-form-item {
      margin-bottom: 8px;
    }
  }
  .select {
    width: 100%;
  }
  .rejected-items {
    padding: 12px 18px;
    background: rgba(178, 190, 195, 0.1);
    border-radius: 4px;
    display: flex;
    align-items: center;
  }
  .rejected-label {
    font-size: 14px;
    font-weight: 600;
  }
  .btn {
    padding: 6.5px 10px;
  }
  .btns {
    width: 80px;
    padding: 6.5px 10px;
    font-size: 14px !important;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .content-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 15px 0;
    h3 {
      font-size: 18px;
    }
  }
  .list-wrap {
    flex: 1;
    flex-shrink: 0;
    display: flex;
    font-size: 14px;
    overflow: auto;
  }
  .list-left,
  .list-right {
    flex: 1;
    flex-shrink: 0;
  }
  .list-left {
    margin-right: 18px;
    background: rgba(178, 190, 195, 0.1);
    border: 1px solid #d8dee1;
    border-radius: 4px;
  }
  .list-title {
    font-weight: 600;
    background: rgba(23, 112, 223, 0.2);
    padding: 9px 18px;
  }
  .items-wrap {
    height: calc(100% - 37px);
    overflow: auto;
  }
  .list-item {
    padding: 9px 0;
    margin: 0 18px;
    border-bottom: 1px solid #e8ebed;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .list-right {
    display: flex;
    flex-direction: column;
  }
  .rejection-list {
    margin-bottom: 18px;
    height: 266px;
  }
  .finished-items {
    height: calc(100% - 284px);
  }
  .rejection-list,
  .finished-items {
    background: rgba(178, 190, 195, 0.1);
    border: 1px solid #d8dee1;
    border-radius: 4px;
  }
  .text-btn {
    padding: 0;
  }
  .dialog-title {
    background: #d1e2f9;
    font-size: 18px;
    padding: 18px;
    font-weight: 600;
  }
  /deep/.el-dialog__header {
    padding: 0;
  }
  .dialog-btn {
    display: flex;
    align-items: center;
    color: #2d3436;
    margin-bottom: 18px;
    .label {
      font-weight: 600;
      margin-right: 10px;
    }
    .time {
      margin-left: 48px;
      margin-right: 10px;
    }
  }
  .dialog-table {
    height: calc(100% - 50px);
    overflow: auto;
    border: 1px solid #d8dee1;
    border-radius: 4px;
  }
  .InfoList-table {
    height: 100%;
    overflow: auto;
    border: 1px solid #d8dee1;
    border-radius: 4px;
  }
  .checkbox {
    /deep/.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
      background-color: #409eff;
      border-color: #409eff;
    }
    /deep/.el-checkbox__input.is-disabled.is-checked
      .el-checkbox__inner::after {
      border-color: #fff;
    }
  }
}
</style>
<style lang="less">
.recyclingGuidance {
  .showUserInfoList {
    height: 85vh;
    .el-dialog__body {
      padding: 18px;
      height: calc(100% - 60px);
    }
  }
  .showList {
    height: 85vh;
    .el-dialog__body {
      padding: 18px;
      height: calc(100% - 60px);

      .search-swtich-personal-box {
        margin-right: 30px;

        label {
          width: 40px;
        }
      }

      .search-swtich-collected-box {
        label {
          width: 60px;
        }
      }
    }
  }
}
</style>
