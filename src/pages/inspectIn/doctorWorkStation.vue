<template>
  <div class="doctorWorkStation_page" @click="pageClick">
    <div class="rightBody">
      <div class="rightTop">
        <div class="btnHeader">
          <!-- <span class="title">医生工作站结果录入</span> -->
        </div>
        <div class="typeHeader">
          <div class="typeHeader-left">
            <el-popover
              placement="bottom-start"
              width="100%"
              trigger="click"
              popper-class="popverClass"
              ref="listPopover"
              @after-enter="search"
            >
              <div class="leftBody">
                <ul class="group_head">
                  <li>
                    <div class="every_inp times">
                      <label>体检时间</label>
                      <p>
                        <el-date-picker
                          :picker-options="{ shortcuts: G_datePickerShortcuts }"
                          type="daterange"
                          format="yyyy-MM-dd"
                          value-format="yyyy-MM-dd"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          :clearable="false"
                          size="small"
                          v-model="searchTime"
                          @change="search"
                        >
                        </el-date-picker>
                      </p>
                    </div>
                    <div class="every_inp times marginDiv">
                      <label>体检单位</label>
                      <p>
                        <el-cascader
                          ref="company_cascader_ref"
                          v-model="companyCode"
                          :filter-method="filterMethod"
                          :options="companyList"
                          :props="{ multiple: false }"
                          clearable
                          filterable
                          size="small"
                          collapse-tags
                          @change="companyChange"
                        >
                        </el-cascader>
                      </p>
                    </div>
                  </li>
                  <li v-if="G_config.physicalMode.includes('普检')">
                    <div class="every_inp times">
                      <label>体检分类</label>
                      <p>
                        <el-select
                          placeholder="请选择"
                          size="small"
                          filterable
                          clearable
                          v-model="tjCls"
                          @clear="changeTjCls"
                          style="width: 100%"
                          @change="search"
                        >
                          <el-option
                            v-for="item in G_peClsList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          >
                          </el-option>
                        </el-select>
                      </p>
                    </div>
                    <div class="every_inp marginDiv">
                      <p>
                        <el-input
                          size="small"
                          placeholder="体检号/姓名"
                          clearable
                          v-model="searchIpt"
                          @keyup.enter.native="search"
                          @clear="search"
                        ></el-input>
                      </p>
                      <div style="margin-left: 5px">
                        <el-radio-group
                          v-model.trim="searchRadioVal"
                          @change="search"
                        >
                          <el-radio :label="0">全部</el-radio>
                          <el-radio :label="1">本人</el-radio>
                        </el-radio-group>
                      </div>
                      <label class="btnLabel">
                        <BtnCommon :btnList="['查询']" @search="search" />
                      </label>
                    </div>
                  </li>
                </ul>
                <div class="tableDiv">
                  <el-tabs
                    v-model="activeName"
                    @tab-click="handleClick"
                    class="tabs_wrap"
                  >
                    <el-tab-pane :label="notChecked" name="notChecked">
                      <PublicTable
                        :viewTableList.sync="notCheckedData"
                        :theads.sync="checkedTheads"
                        :tableLoading.sync="loading"
                        :columnWidth="columnWidth"
                        @rowDblclick="rowDblclick"
                        :columnSort="columnSort"
                        :isOnlySortShow="true"
                      >
                        <template #sex="{ scope }">
                          <div>
                            {{ sexList[scope.row.sex] }}
                          </div>
                        </template>
                        <template #peStatus="{ scope }">
                          {{ G_EnumList['PeStatus'][scope.row.peStatus] }}
                        </template>
                        <template #peCls="{ scope }">
                          <div>
                            <span>{{
                              G_EnumList['PeCls'][scope.row.peCls]
                            }}</span>
                          </div>
                        </template>
                        <template #companyName="{ scope }">
                          <div class="container" :title="scope.row.companyName">
                            {{ scope.row.companyName }}
                          </div>
                        </template>
                        <template #isDepartComplete="{ scope }">
                          <div class="container">
                            {{
                              scope.row.isDepartComplete ? '已检完' : '未检完'
                            }}
                          </div>
                        </template>
                      </PublicTable>
                    </el-tab-pane>
                    <el-tab-pane :label="isChecked" name="isChecked">
                      <PublicTable
                        :viewTableList.sync="isCheckedData"
                        :theads.sync="checkedTheads"
                        :tableLoading.sync="loading"
                        :columnWidth="columnWidth"
                        @rowDblclick="rowDblclick"
                        :columnSort="columnSort"
                        :isOnlySortShow="true"
                      >
                        <template #sex="{ scope }">
                          <div>
                            {{ sexList[scope.row.sex] }}
                          </div>
                        </template>
                        <template #peStatus="{ scope }">
                          {{ G_EnumList['PeStatus'][scope.row.peStatus] }}
                        </template>
                        <template #peCls="{ scope }">
                          <div>
                            <span>{{
                              G_EnumList['PeCls'][scope.row.peCls]
                            }}</span>
                          </div>
                        </template>
                        <template #companyName="{ scope }">
                          <div class="container" :title="scope.row.companyName">
                            {{ scope.row.companyName }}
                          </div>
                        </template>
                        <template #isDepartComplete="{ scope }">
                          <div class="container">
                            {{
                              scope.row.isDepartComplete ? '已检完' : '未检完'
                            }}
                          </div>
                        </template>
                      </PublicTable>
                    </el-tab-pane>
                  </el-tabs>
                </div>
              </div>
              <el-button
                slot="reference"
                class="blue_btn btn"
                size="small"
                icon="iconfont icon-liebiao"
                >查询列表</el-button
              >
            </el-popover>
            <!-- <div style="display: flex; align-items: center">
              <span style="width: 70px">体检号</span>
              <el-input
                size="small"
                placeholder="体检号"
                clearable
                v-model="searchIpt"
                @keyup.enter.native="search"
              ></el-input>
            </div> -->
            <div class="header-info">
              <div class="every_inp">
                <p>
                  <el-input
                    size="small"
                    placeholder="体检号"
                    clearable
                    v-model="searchRegNoIpt"
                    @keyup.enter.native="searchRegNo"
                    class="searchIpt"
                    ref="regNoInput_Ref"
                    autofocus
                  >
                  </el-input>
                </p>
              </div>
              <div class="every_inp">
                <label>体检号:</label>
                <p>{{ headerInfo.regNo }}</p>
              </div>
              <div class="every_inp lowWidth">
                <label>姓名:</label>
                <p>{{ headerInfo.name }}</p>
              </div>
              <div class="every_inp lowWidth">
                <label>性别:</label>
                <p>{{ sexList[headerInfo.sex] }}</p>
              </div>
              <div class="every_inp lowWidth">
                <label>年龄:</label>
                <p>{{ headerInfo.age }}</p>
              </div>
              <!-- <div class="every_inp companyWidth">
                <label>单位:</label>
                <p :title="headerInfo.companyName" style="overflow-y: auto">
                  {{ headerInfo.companyName }}
                </p>
              </div> -->
            </div>
          </div>
          <el-button
            class="yellow_btn btn"
            size="small"
            style="margin-right: 10px"
            @click.native="pilotInspectionIsShow"
            :icon="
              isShowPilotInspection ? 'el-icon-arrow-down' : 'el-icon-arrow-up'
            "
            >导检</el-button
          >
          <el-button
            class="yellow_btn btn"
            size="small"
            style="margin-right: 10px"
            @click="consultList"
            icon="iconfont icon-changyonghuifu"
            >咨询列表</el-button
          >
          <BtnCommon
            :btnList="['发起会诊', '响应会诊']"
            @sendConsultation="sendConsultation"
            @responseConsultation="responseConsultation"
          />
        </div>
        <!-- 导检栏 -->
        <div class="pilotInspection" v-show="isShowPilotInspection">
          <div class="pilotInfoDiv">
            <p>
              <span class="labelSpan">叫&emsp;号：</span
              ><span class="redSpan">{{ callName }}</span>
            </p>
            <p>
              <span class="labelSpan">下一站：</span
              ><span class="redSpan">{{ nextStation }}</span>
            </p>
          </div>
          <div class="pilotBtnDiv">
            <el-button size="mini" @click="call" :loading="loadingCall">{{
              loadingCall ? '正在叫号（重呼）中' : '叫号（重呼）'
            }}</el-button>
            <el-button size="mini" @click="done" :loading="loadingDone">{{
              loadingDone ? '正在完成中' : '完成'
            }}</el-button>
            <el-button size="mini" @click="back" :loading="loadingBack">{{
              loadingBack ? '正在置后中' : '置后'
            }}</el-button>
          </div>
        </div>

        <div class="rightcont">
          <div class="btnHeaderDiv">
            <span class="title"> {{ recordComb.combName }}</span>
            <BtnCommon
              :btnList="btnList"
              @historyReport="historyReport"
              @collection="collection"
              @amend="amend"
              :isModify.sync="isModify"
              @saves="savesBtn"
              @delResult="deleteRecordComb"
              @refuseCheck="waiveBtn"
              :infoData="infoData"
              :headerInfo="headerInfo"
            >
              <template #footAdd>
                <el-button
                  size="small"
                  class="green_btn btn"
                  @click="imgTextClick"
                  >图文报告</el-button
                >
                <el-button
                  size="small"
                  class="green_btn btn"
                  @click="followUpClick"
                  >添加随访</el-button
                >
                <el-button
                  size="small"
                  class="green_btn btn"
                  @click="followUpRecodeClick"
                  v-if="isHasFollowUp"
                  >随访记录</el-button
                >
              </template>
            </BtnCommon>
          </div>
          <div class="contDiv">
            <div class="contLeft">
              <div class="typeInfo">
                <div class="every_inp">
                  <!-- <label
                    ><el-checkbox v-model="typeInfo.isAbnormal"></el-checkbox
                  ></label>
                  <p>异常</p> -->
                  <el-checkbox
                    v-model="recordComb.isError"
                    :disabled="errorDisabled"
                    >异常</el-checkbox
                  >
                </div>
                <div class="every_inp" style="justify-content: flex-end">
                  <label>操作员:</label>
                  <p>
                    {{
                      recordComb.operName === null
                        ? G_userInfo.codeOper.name
                        : recordComb.operName
                    }}
                  </p>
                </div>
                <div class="every_inp">
                  <label>检查日期</label>
                  <p>
                    <el-date-picker
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      :clearable="false"
                      size="mini"
                      v-model="recordComb.examTime"
                      readonly
                    >
                    </el-date-picker>
                  </p>
                </div>
                <div class="every_inp">
                  <label>体检医师</label>
                  <p>
                    <el-input
                      size="mini"
                      clearable
                      v-model="recordComb.doctorName"
                      readonly
                    ></el-input>
                  </p>
                </div>
              </div>
              <div class="rightTableDiv">
                <!-- <Nomal
                  v-show="pageStatus == 1"
                  ref="nomal"
                  :isModify.sync="isModify"
                  :pageData="infoData"
                /> -->
                <CheckboxGroup
                  :infoData="infoData"
                  v-show="pageStatus == 3"
                  ref="checkboxGroup"
                  :isModify.sync="isModify"
                />
                <div style="flex: 1; flex-shrink: 0; overflow: auto">
                  <Examine
                    :isModify.sync="isModify"
                    :combData.sync="infoData"
                    :mode="pageStatus"
                    :headerInfo="headerInfo"
                    :checkComb.sync="checkComb"
                    ref="examine_Ref"
                    v-show="pageStatus == 2 || pageStatus == 1"
                  />
                </div>
                <div class="nodulus">
                  <PhysicalSummary
                    :isModify.sync="isModify"
                    :combData.sync="infoData"
                    :dynamicTags="dynamicTags"
                  />
                </div>
              </div>
            </div>
            <div class="contRight">
              <SideTabs
                :headerInfo="headerInfo"
                :navList="navList"
                ref="sideTabs"
              />
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="rightBom">
        <div class="nodulus">
          <PhysicalSummary
            :isModify.sync="isModify"
            :combData.sync="infoData"
            :dynamicTags="dynamicTags"
          />
        </div>
      </div> -->
    </div>
    <el-drawer title="图文报告" size="90%" :visible.sync="ImgTextShow">
      <ImgText
        ref="ImgText_Ref"
        :printImgList.sync="ImgTextList"
        :headerInfo="headerInfo"
        :checkComb.sync="checkComb"
        v-if="ImgTextShow"
      />
    </el-drawer>
    <el-drawer
      title="请求会诊"
      :visible.sync="sendDrawer"
      :before-close="sendDrawerHandleClose"
      :wrapperClosable="false"
      size="90%"
      v-if="sendDrawer"
    >
      <ReConsultation
        :responseConsultation="responseConsultation"
        :headerInfo="headerInfo"
      />
    </el-drawer>
    <el-drawer
      title="响应会诊"
      :visible.sync="resDrawer"
      :before-close="handleClose"
      :wrapperClosable="false"
      size="90%"
      v-if="resDrawer"
    >
      <Response :routeRow="routeRow" :headerInfo="headerInfo" :id="id" />
    </el-drawer>
    <el-drawer
      title="历史报告"
      :visible.sync="hisDrawer"
      :before-close="hisDrawerClose"
      :wrapperClosable="false"
      size="90%"
      v-if="hisDrawer"
    >
      <HistoryReport :regNo="headerInfo.regNo" />
    </el-drawer>
    <el-drawer
      title="采集图片"
      :visible.sync="pictureDrawer"
      :before-close="pictureDrawerClose"
      :wrapperClosable="false"
      size="90%"
      v-if="pictureDrawer"
    >
      <CollectPictures
        :headerInfo="headerInfo"
        :propRegCombId="regCombId"
        :regCombs="regCombs"
        :deptCode="deptCode"
      />
    </el-drawer>
    <el-dialog
      :visible.sync="consultationReply"
      width="1260px"
      top="6%"
      custom-class="consultationReply"
      :close-on-click-modal="false"
      @close="consultationReply = false"
    >
      <div slot="title" class="dialog-title">咨询回复</div>
      <h3 class="replyTitle">医生咨询列表：</h3>
      <div class="replyTable">
        <PublicTable
          :viewTableList.sync="replyData"
          :theads.sync="theads"
          :isSortShow="false"
          :columnWidth="{
            regNo: 124,
            combName: 120,
            questionTime: 166,
            questionContent: 200,
            replyTime: 166,
            replyContent: 200
          }"
        >
          <template #columnRight>
            <el-table-column width="60" label="操作">
              <template slot-scope="scope">
                <el-popover
                  placement="bottom-end"
                  width="380"
                  v-model="scope.row.backoutPopoverShow"
                  trigger="click"
                >
                  <el-input
                    type="textarea"
                    :autosize="{ minRows: 6, maxRows: 6 }"
                    placeholder="请输入内容"
                    v-model.trim="scope.row.replyContent"
                  >
                  </el-input>
                  <div class="reply-footer">
                    <el-button
                      @click="scope.row.backoutPopoverShow = false"
                      size="small"
                      >取消</el-button
                    >
                    <el-button
                      class="blue_btn"
                      @click="replySubmit(scope.row)"
                      size="small"
                      >确定</el-button
                    >
                  </div>
                  <div slot="reference" class="replyText">回复</div>
                </el-popover>
              </template>
            </el-table-column>
          </template>
        </PublicTable>
      </div>
    </el-dialog>
    <el-drawer
      title="医生咨询回复一览表"
      :visible.sync="consultDialog"
      :before-close="consultDialogClose"
      :wrapperClosable="false"
      size="90%"
      v-if="consultDialog"
    >
      <Consult :conclusionList="conclusionList" />
    </el-drawer>
    <!-- 添加随访 -->
    <FollowUp
      :regNo="headerInfo.regNo"
      :followUpShow.sync="followUpShow"
      @followUpClose="followUpClose"
    />
  </div>
</template>

<script>
import BtnCommon from './components/btnCommon.vue';
import PublicTable from '../../components/publicTable.vue';
import PhysicalSummary from './components/doctorWorkStation/physicalSummary.vue';
import SideTabs from './components/doctorWorkStation/sideTabs.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
import Nomal from './components/doctorWorkStation/nomal.vue'; //血常规（五分类）表格页面
import CheckboxGroup from './components/doctorWorkStation/checkboxGroup.vue';
import Response from './components/doctorWorkStation/response.vue';
import ReConsultation from './components/doctorWorkStation/reConsultation.vue';
import HistoryReport from './components/doctorWorkStation/historyReport.vue';
import CollectPictures from './components/doctorWorkStation/collectPictures.vue';
import Examine from './components/doctorWorkStation/examine.vue';
import urgent from './mixins/urgent';
import shortcut from '@/common/shortcut';
import consultMsgTips from './mixins/consultMsgTips';
import consultList from './mixins/consultList';
import Consult from '../mainInspectionPage/consult.vue';
import ImgText from './components/resultEntry/imgText.vue';
import imgTextJs from './mixins/imgText';
import FollowUp from '@/components/followUp';
import pilotInspection from './mixins/pilotInspection';
export default {
  name: 'doctorWorkStation',
  mixins: [
    urgent,
    shortcut,
    consultMsgTips,
    consultList,
    imgTextJs,
    pilotInspection
  ],
  components: {
    BtnCommon,
    PublicTable,
    Nomal,
    PhysicalSummary,
    SideTabs,
    CheckboxGroup,
    ReConsultation,
    Response,
    HistoryReport,
    CollectPictures,
    Examine,
    Consult,
    ImgText,
    FollowUp
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_peClsList',
      'G_userInfo',
      'G_codeDepartment',
      'G_datePickerShortcuts',
      'G_config'
    ])
  },
  data() {
    return {
      HisPatInfo: {},
      searchRadioVal: 0,
      resultState: 1, //获取关联结果的请求状态，1 表示获取完成，2表示正在获取中；
      shortcutList: {
        112: this.savesBtn,
        113: this.amend,
        114: this.waiveBtn,
        115: this.deleteRecordComb
      },
      companyList: [],
      sendDrawer: false,
      resDrawer: false,
      hisDrawer: false,
      pictureDrawer: false,
      searchTime: [dataUtils.getDate(), dataUtils.getDate()],
      searchInfo: {},
      regCombId: '',
      peStatus: 0, //体检状态 (查全部 = -1,未检查 = 0, 正在检查 = 1, 已检完 = 2, 已总检 = 3, 已审核 = 4)
      companyCode: '',
      tjCls: null,
      searchIpt: '',
      notChecked: '未完成人员(0)',
      isChecked: '已完成人员(0)',
      activeName: 'notChecked',
      loading: false,
      checkedTheads: {
        peCls: '体检分类',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        companyName: '体检单位',
        tel: '手机号',
        regNo: '体检号',
        registerTime: '登记时间',
        activeTime: '体检时间',
        isDepartComplete: '科室状态',
        peStatus: '体检状态'
      },
      notCheckedData: [], //未检人员数据
      isCheckedData: [], //已检人员数据
      headerInfo: {}, //行信息
      columnWidth: {
        registerTime: 170,
        activeTime: 170,
        regNo: 125,
        peCls: 110,
        companyName: 150,
        name: 80,
        sex: 60,
        age: 60,
        tel: 115,
        isDepartComplete: 80,
        peStatus: 100
      },
      columnSort: [
        'peCls',
        'name',
        'sex',
        'age',
        'companyName',
        'registerTime',
        'activeTime',
        'peStatus'
      ],
      sexList: {
        null: '',
        0: '通用',
        1: ' 男',
        2: '女'
      },
      headerInfo: {
        regNo: '',
        name: '',
        sex: null,
        age: null,
        companyName: ''
      },
      titleName: '血常规（五分类）',
      typeInfo: {
        isAbnormal: false,
        operatorCode: '系统员',
        checkDate: dataUtils.getDate(),
        doctor: '系统员'
      },
      pageStatus: '',
      btnList: ['历史报告'],
      dynamicTags: [],
      isModify: true, //是否修改
      navList: [],
      infoData: {},
      backoutInitData: {}, //撤销时使用的初始化数据
      recordComb: {},
      isEnabled: false,
      combCode: '',
      clsCode: '',
      examDeptCode: '',
      id: null,
      errorDisabled: true,
      regCombs: [],
      deptCode: '',
      // consultationReply: false,
      timerName: '',
      routeRow: {}, //路由参数
      isSave: false,
      searchRegNoIpt: '',
      followUpShow: false,
      isShowPilotInspection: false
    };
  },
  created() {
    // this.GetPeQuestionToDoctors();
    // this.timerName = setInterval(this.GetPeQuestionToDoctors, 60000);
  },
  mounted() {
    //this.search();
    this.getCompany();
  },
  methods: {
    //显示隐藏导检
    pilotInspectionIsShow() {
      this.isShowPilotInspection = !this.isShowPilotInspection;
      //console.log('[ this.isShowPilotInspection ]-713', this.isShowPilotInspection);
    },
    pageClick() {
      this.$refs.examine_Ref.resultLeave();
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    //获取单位下拉
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) return;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      console.log('🚀 ~ companyChange ~ currentlySelected:', currentlySelected);

      this.searchTime = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.search();
    },
    rowDblclick(row) {
      console.log(row);
      if (
        this.isModify &&
        JSON.stringify(this.fixed_infoData) != '{}' &&
        JSON.stringify(this.infoData?.recordItems) !=
          JSON.stringify(this.fixed_infoData?.recordItems)
      ) {
        this.$confirm('结果有修改，是否保存?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        })
          .then(() => {
            this.saves(false).then((r) => {
              this.rowClickFun(row);
            });
          })
          .catch(() => {
            this.rowClickFun(row);
          });
        return;
      }
      this.rowClickFun(row);
    },
    // 队列的双击回调
    patientListDbClick(row) {
      console.log(888888888);
      this.rowDblclick(row);
    },
    rowClickFun(row) {
      console.log(row);
      this.headerInfo = row;
      this.searchRegNoIpt = '';
      this.infoData = {};
      this.pageStatus = '';
      this.recordComb = {};
      this.dynamicTags = [];
      this.getNavList().then(() => {
        this.autoCheckObj();
      });
      this.getImgTextList(row.regNo);
      this.$refs.sideTabs.template = '';
      this.$refs.sideTabs.liIndex = null;
      this.$refs.sideTabs.activeName = '';
      this.$refs.sideTabs.imgList = [];
      this.isSave = false;
      // if(row.peStatus > 1){
      //   this.isModify = false;
      // }else{
      //   this.isModify = true;
      // }
    },
    // 自动选中未检的项目
    autoCheckObj() {
      this.navList.some((item, idx, arr) => {
        if (item.combStatus == 1) {
          this.$refs.sideTabs.liIndex = item.regCombId;
          this.$refs.sideTabs.navClickFun(item);
          return true;
        }
        if (item.combStatus > 1 && idx === arr.length - 1) {
          this.$refs.sideTabs.liIndex = arr[0].regCombId;
          this.$refs.sideTabs.navClickFun(arr[0]);
          return true;
        }

        // if(item.isFinish && idx === this.navList.length-1){
        //   let parentObj = this.navList[0];
        //   this.$refs.sideTabs.activeName = parentObj.deptCode;
        //   this.$refs.sideTabs.liIndex = this.navList[0].regCombs[0].regCombId;
        //   this.$refs.sideTabs.navClickFun(parentObj.regCombs[0],parentObj)
        // }
        // if (!item.isFinish) {
        //   this.$refs.sideTabs.activeName = item.deptCode;
        //   item.regCombs.some(twoItem=>{
        //     if(twoItem.combStatus == 1){
        //       this.$refs.sideTabs.liIndex = twoItem.regCombId;
        //       this.$refs.sideTabs.navClickFun(twoItem,item)
        //       return true;
        //     }
        //   })
        //   return true;
        // }
      });
    },
    //查询
    async search() {
      // this.$refs.nomal.tableDatas = [];
      // this.navList = [];
      this.searchNotChecked();
      // this.$refs.sideTabs.template = "";
      // this.$refs.sideTabs.imgList = [];
    },
    //体检号回车查询
    searchRegNo() {
      if (
        this.isModify &&
        JSON.stringify(this.fixed_infoData) != '{}' &&
        JSON.stringify(this.infoData?.recordItems) !=
          JSON.stringify(this.fixed_infoData?.recordItems)
      ) {
        this.$confirm('结果有修改，是否保存?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        })
          .then(() => {
            this.saves(false).then((r) => {
              this.infoData = {};
              this.regNoGetInfo(this.searchRegNoIpt);
            });
          })
          .catch(() => {
            this.infoData = {};
            this.regNoGetInfo(this.searchRegNoIpt);
          });
        return;
      }
      this.infoData = {};
      this.regNoGetInfo(this.searchRegNoIpt);
    },
    //清空
    clearRegNo() {
      this.btnList = [];
      this.headerInfo = {};
      this.infoData = {};
      this.pageStatus = '';
      this.recordComb = {};
      this.navList = [];
    },
    //获取内容信息
    regNoGetInfo(searchRegNoIpt) {
      this.infoData = {};
      this.pageStatus = '';
      this.recordComb = {};
      this.getNavList(searchRegNoIpt).then(() => {
        // this.navList.some((item, idx) => {
        //   if (item.isFinish && idx === this.navList.length - 1) {
        //     let parentObj = this.navList[0];
        //     this.$refs.sideTabs.activeName = parentObj.deptCode;
        //     this.$refs.sideTabs.liIndex = this.navList[0].regCombs[0].regCombId;
        //     this.$refs.sideTabs.navClickFun(parentObj.regCombs[0], parentObj);
        //   }
        //   if (!item.isFinish) {
        //     this.$refs.sideTabs.activeName = item.deptCode;
        //     item.regCombs.some((twoItem) => {
        //       if (twoItem.combStatus == 1) {
        //         this.$refs.sideTabs.liIndex = twoItem.regCombId;
        //         this.$refs.sideTabs.navClickFun(twoItem, item);
        //         return true;
        //       }
        //     });
        //     return true;
        //   }
        // });
        this.navList.some((item, idx, arr) => {
          if (item.combStatus == 1) {
            this.$refs.sideTabs.liIndex = item.regCombId;
            this.$refs.sideTabs.navClickFun(item);
            return true;
          }
          if (item.combStatus > 1 && idx === arr.length - 1) {
            this.$refs.sideTabs.liIndex = arr[0].regCombId;
            this.$refs.sideTabs.navClickFun(arr[0]);
            return true;
          }
        });
      });
      this.getImgTextList(searchRegNoIpt);
      this.$refs.sideTabs.template = '';
      this.$refs.sideTabs.liIndex = null;
      this.$refs.sideTabs.activeName = '';
      this.$refs.sideTabs.imgList = [];
      this.isSave = false;
    },
    // 获取项目导航
    getNavList(regNo) {
      return new Promise((resolve, reject) => {
        let data = {
          // operCode: this.G_userInfo.codeOper.operatorCode,
          // regNo: regNo|| this.headerInfo.regNo
          regNo: regNo || this.headerInfo.regNo,
          filterDoctor: true,
          doctorCode: this.G_userInfo.codeOper.operatorCode
        };
        this.headerInfo = {};
        this.$ajax.post(this.$apiUrls.GetExamCombList, data).then((r) => {
          // this.$ajax.post(this.$apiUrls.GetExamItemsByDoctor, data).then(r => {
          console.log('GetExamItemsByDoctor', r);
          this.searchRegNoIpt = '';
          let { success, returnData, returnMsg } = r.data;
          if (!success) return;
          if (returnData == null) {
            this.$message({
              message: returnMsg,
              type: 'success',
              showClose: true
            });
            this.btnList = [];
            this.headerInfo = {};
            this.infoData = {};
            this.pageStatus = '';
            this.recordComb = {};
            this.navList = [];
            return;
          }
          // if(returnData.person.peStatus > 2){
          //   this.isModify = false;
          // }else{
          //   this.isModify = true;
          // }
          this.headerInfo = returnData.person;
          this.navList = returnData.examCombs;
          // ?.filter((item)=>{
          //   return item.isEnabled || !this.$config.isFilterDept;
          // })
          this.$refs.listPopover.doClose();
          this.getFollowUpState();
          // if (!this.isSave) {
          //   this.navList.map(item => {
          //     if (item.isMain) {
          //       this.$refs.sideTabs.activeName = item.deptCode;
          //       this.$refs.sideTabs.liIndex = null;
          //     }
          //   });
          // } else {
          //   this.$refs.sideTabs.activeName = this.examDeptCode;
          // }
          resolve();
        });
      });
    },
    // 获取组合内容
    getCombInfo(data, focusFlag = true) {
      this.pageStatus = data.template;
      this.infoData = {};
      this.fixed_infoData = {};
      this.$ajax.post(this.$apiUrls.ReadRecordComb, data).then((r) => {
        console.log('ReadPeRecordByComb', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.infoData = dataUtils.deepCopy(returnData || {});
        setTimeout(() => {
          this.fixed_infoData = dataUtils.deepCopy(this.infoData || {});
        }, 200);
        this.backoutInitData = JSON.parse(JSON.stringify(returnData));
        this.recordComb = returnData.recordComb;
        this.dynamicTags = returnData.combTags;
        if (this.pageStatus === 3) {
          this.$refs.checkboxGroup.getCheckboxList(
            returnData.recordComb.combCode
          );
        }
        this.$nextTick(() => {
          if (
            this.headerInfo.peStatus > 2 ||
            returnData.recordComb.doctorName === '弃检'
          ) {
            this.isModify = false;
          } else {
            this.isModify = true;
            // 默认开启聚焦功能；
            focusFlag &&
              this.$refs.examine_Ref.nextRowCurrent({ rowIndex: null });
          }
        });
        // if(returnData.recordComb.doctorName === '弃检'){
        //   this.isModify = false;
        // }
        // if (this.pageStatus == 1) {
        //   this.$refs.nomal.tableDatas = [];
        //   returnData.recordItems.map(item => {
        //     this.$refs.nomal.tableDatas.push({
        //       itemName: item.itemName,
        //       hint: item.hint,
        //       itemResult: item.itemTags[0]?.tag,
        //       unit: item.unit,
        //       referenceVal:
        //         item.lowerLimit == null
        //           ? ""
        //           : item.lowerLimit + "~" + item.upperLimitt == null
        //           ? ""
        //           : item.upperLimitt,
        //       lastItemResult: item.lastItemResult
        //     });
        //   });
        // }
      });
    },
    changeTjCls() {
      this.tjCls = null;
    },
    //查询未检人员
    searchNotChecked() {
      // this.headerInfo = {};
      (this.searchInfo = {
        operCode: this.G_userInfo.codeOper.operatorCode,
        startTime: this.searchTime[0],
        endTime: this.searchTime[1],
        keyWord: this.searchIpt,
        peStatus: 1,
        companyCode: this.companyCode[0],
        peCls: !this.tjCls ? -1 : this.tjCls
      }),
        this.$ajax
          .post(this.$apiUrls.GetPatientListByDoctor, this.searchInfo)
          .then((r) => {
            console.log('r', r);
            let { success, returnData } = r.data;
            if (!success) return;
            if (this.searchRadioVal !== 0) {
              returnData = returnData.filter((item) => {
                return (
                  item.operatorCode == this.G_userInfo.codeOper.operatorCode
                );
              });
            }
            if (returnData.length >= 1) {
              this.notCheckedData = returnData;
              this.notChecked = '未完成人员' + '(' + returnData.length + ')';
            } else {
              this.notCheckedData = [];
              this.notChecked = '未完成人员' + '(0)';
              // this.$message({
              //   message: "暂无数据!",
              //   type: "success",
              //   showClose: true,
              // });
            }
            this.searchIsCheckedData();
          });
    },
    //查询已检人员
    searchIsCheckedData() {
      (this.searchInfo = {
        operCode: this.G_userInfo.codeOper.operatorCode,
        startTime: this.searchTime[0],
        endTime: this.searchTime[1],
        keyWord: this.searchIpt,
        peStatus: 2,
        companyCode: this.companyCode[0],
        peCls: this.tjCls === null ? -1 : this.tjCls
      }),
        this.$ajax
          .post(this.$apiUrls.GetPatientListByDoctor, this.searchInfo)
          .then((r) => {
            console.log('r', r);
            let { success, returnData } = r.data;
            if (!success) return;
            if (this.searchRadioVal !== 0) {
              returnData = returnData.filter((item) => {
                return (
                  item.operatorCode == this.G_userInfo.codeOper.operatorCode
                );
              });
            }
            if (returnData?.length >= 1) {
              this.isCheckedData = returnData;
              this.isChecked = '已完成人员' + '(' + returnData.length + ')';
            } else {
              this.isCheckedData = [];
              this.isChecked = '已完成人员' + '(0)';
              // this.$message({
              //   message: "暂无数据!",
              //   type: "success",
              //   showClose: true,
              // });
            }
            if (
              this.notCheckedData.length < 1 &&
              this.isCheckedData.length < 1
            ) {
              this.$message({
                message: '暂无数据!',
                type: 'success',
                showClose: true
              });
            }
          });
    },
    //关闭抽屉
    sendDrawerHandleClose() {
      this.sendDrawer = false;
    },
    //关闭抽屉
    handleClose() {
      this.resDrawer = false;
      this.routeRow = {};
    },
    //关闭历史报告弹出
    hisDrawerClose() {
      this.hisDrawer = false;
    },
    historyReport() {
      if (!this.headerInfo.regNo) {
        this.$message({
          message: '请先点击要查看历史报告的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.hisDrawer = true;
    },
    pictureDrawerClose() {
      console.log('[ this.regCombId ]-496', this.regCombId);
      this.pictureDrawer = false;
      this.$refs.sideTabs.getImage(
        this.regCombId,
        this.$refs.sideTabs.deptCode
      ); //重新获取图例
    },

    collection() {
      if (!this.headerInfo.regNo) {
        this.$message({
          message: '请先点击要添加采集图片的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.pictureDrawer = true;
    },
    sendConsultation() {
      if (!this.headerInfo.regNo) {
        this.$message({
          message: '请先点击要查看发起会诊的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.sendDrawer = true;
      // let sendData = {
      //   SendUser: "admin",
      //   ReceiveUser: "1234",
      //   DeptArray: ["00", "01"],
      //   InitFlag: true,
      //   Content: "168168168168168168"
      // };
      // this.$ws.sendSock(JSON.stringify(sendData), function () {
      //   console.log(123);
      // });
    },
    responseConsultation(id) {
      if (id) {
        this.id = id;
      } else {
        this.id = null;
      }
      // if (!this.headerInfo.regNo) {
      //   this.$message({
      //     message: "请先点击要查看响应会诊的数据!",
      //     type: "warning",
      //     showClose: true
      //   });
      //   return;
      // }
      this.sendDrawer = false;
      this.resDrawer = true;
    },
    handleClick(tab, event) {
      console.log(this.activeName);
      // this.headerInfo = {};
      console.log(tab, event);
      // if ((this.activeName = "notChecked")) {
      //   this.peStatus = 0;
      // } else {
      //   this.peStatus = 1;
      // }
      // this.search();
    },
    // 修改按钮的点击回调
    amend() {
      console.log(this.infoData);
      if (this.isModify) {
        this.$confirm('是否撤销所有编辑？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        })
          .then(() => {
            this.infoData = JSON.parse(JSON.stringify(this.backoutInitData));
            this.isModify = !this.isModify;
            this.$message({
              type: 'success',
              message: '撤销成功!'
            });
            console.log(this.infoData);
          })
          .catch(() => {});
      } else {
        this.isModify = !this.isModify;
      }
    },
    savesBtn() {
      if (this.$refs.examine_Ref.checkRowIndex !== null) {
        if (
          this.$refs.examine_Ref.$refs[
            'inpFocus_' + this.$refs.examine_Ref.checkRowIndex
          ]?.focus
        ) {
          this.$refs.examine_Ref.$refs[
            'inpFocus_' + this.$refs.examine_Ref.checkRowIndex
          ].blur();
        } else {
          this.$refs.examine_Ref.$refs[
            'inpFocus_' + this.$refs.examine_Ref.checkRowIndex
          ].inputHandle();
          let itemComb =
            this.infoData.recordItems[this.$refs.examine_Ref.checkRowIndex];
          this.$nextTick(() => {
            itemComb.itemTags.map((item, idx) => {
              console.log(
                this.$refs.examine_Ref.$refs[
                  'inpFocus_' + this.$refs.examine_Ref.checkRowIndex
                ].$refs['inputSpan_Ref_' + itemComb.itemCode + '_' + idx],
                idx
              );
              this.$refs.examine_Ref.$refs[
                'inpFocus_' + this.$refs.examine_Ref.checkRowIndex
              ].$refs[
                'inputSpan_Ref_' + itemComb.itemCode + '_' + idx
              ][0].blur();
            });
          });
        }
      }
      setTimeout(() => {
        this.saves(false).then((r) => {
          this.$refs.examine_Ref.abnormalPopup = false;
          this.$refs.examine_Ref.resultPopup = false;
          this.$refs.examine_Ref.editPopup = false;
          this.getNavList().then(() => {
            // this.autoCheckObj();
            let datas = {
              regCombId: this.infoData.recordComb.regCombId,
              regNo: this.infoData.recordComb.regNo,
              template: this.$refs.sideTabs.checkGroup.template
            };
            this.getCombInfo(datas, false);
            this.$refs.regNoInput_Ref.focus();
            // this.fixed_infoData = dataUtils.deepCopy(this.infoData)
          });
        });
      }, 300);
    },
    // 保存的通用函数
    saves(flag = true) {
      return new Promise((resolve, reject) => {
        console.log(8798798798798798789879879879, this.resultState);
        console.log(this.infoData);
        console.log(this.dynamicTags);
        if (this.resultState == 2) {
          setTimeout(() => {
            this.saves();
          }, 200);
          return;
        }

        let datas = {
          recordComb: {
            regNo: this.headerInfo.regNo,
            regCombId: this.regCombId,
            combCode: this.combCode,
            combName: this.recordComb.combName,
            clsCode: this.clsCode,
            // examDeptCode: this.examDeptCode,
            operName: this.G_userInfo.codeOper.name,
            doctorName: this.G_userInfo.codeOper.name,
            isError: this.recordComb.isError,
            examTime: this.recordComb.examTime
          },
          // 项目
          recordItems: this.infoData.recordItems,
          // 小结
          combTags: this.dynamicTags
        };
        console.log(datas);
        // setTimeout(() => {
        this.$ajax.post(this.$apiUrls.SaveRecordComb, datas).then((r) => {
          let { success } = r.data;
          if (!success) return;
          // this.isModify = false;
          this.$message({
            message: '保存成功!',
            type: 'success',
            showClose: true
          });
          this.isSave = true;
          this.isSendNotice(this.infoData.recordItems, this.headerInfo.regNo);
          if (flag) {
            this.initNavList();
          }
          this.recordComb.doctorName = this.G_userInfo.codeOper.name;
          this.errorDisabled = true;
          resolve();
          // this.btnList = ["历史报告"];
          this.btnList.push('删除结果');
        });
        // }, 1500);
      });
    },
    //删除结果
    deleteRecordComb() {
      let data = {
        regNo: this.headerInfo.regNo,
        regCombId: this.regCombId
      };
      if (!this.regCombId) {
        this.$message({
          message: '请先选择要组合项目结果记录!',
          type: 'warning'
        });
        return false;
      }
      this.$confirm(`是否确定删除?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      })
        .then(() => {
          this.$ajax.post(this.$apiUrls.DeleteRecordComb, data).then((r) => {
            let { success } = r.data;
            if (!success) return;
            this.$message({
              message: '删除结果成功!',
              type: 'success',
              showClose: true
            });
            this.initNavList();
            // this.pageStatus = null;
            // this.dynamicTags = [];
            let datas = {
              regCombId: this.infoData.recordComb.regCombId,
              regNo: this.infoData.recordComb.regNo,
              template: this.$refs.sideTabs.checkGroup.template
            };
            this.getCombInfo(datas);
            this.errorDisabled = true;
            let idx = this.btnList.indexOf('删除结果');
            if (idx != -1) {
              this.btnList.splice(idx, 1);
            }

            // this.btnList = ["历史报告"];
          });
        })
        .catch(() => {});
    },
    // 弃检
    waiveBtn() {
      if (this.infoData.recordComb.doctorName == '弃检') {
        this.$message({
          message: '该组合已经弃检！',
          type: 'warning',
          showClose: true
        });
        return;
      }

      this.$confirm(`是否要弃检${this.infoData.recordComb.combName}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      })
        .then(() => {
          let data = {
            regNo: this.infoData.recordComb.regNo,
            regCombId: this.infoData.recordComb.regCombId,
            operName: this.G_userInfo.codeOper.name
            // sampCode: ""
          };
          this.$ajax.post(this.$apiUrls.AbandonComb, data).then((r) => {
            console.log(99999999999999999999, r);
            let { success, returnData } = r.data;
            if (!success) return;
            this.$message({
              message: '弃检成功!',
              type: 'success',
              showClose: true
            });
            let datas = {
              regCombId: this.infoData.recordComb.regCombId,
              regNo: this.infoData.recordComb.regNo,
              template: this.$refs.sideTabs.checkGroup.template
            };
            console.log(datas);
            this.getCombInfo(datas);
            this.initNavList();
          });
        })
        .catch(() => {});
    },
    // 初始化右边导航菜单
    initNavList() {
      let examDeptCode = this.$refs.sideTabs.activeName;
      let liIndex = this.$refs.sideTabs.liIndex;
      console.log(examDeptCode, this.$refs.sideTabs.liIndex);
      this.getNavList().then((r) => {
        this.$refs.sideTabs.activeName = examDeptCode;
        this.$refs.sideTabs.liIndex = liIndex;
      });
    },
    followUpClose(flag) {
      console.log(flag);
      this.followUpShow = flag;
    },
    // 添加随访的弹窗显示
    followUpClick() {
      if (!this.headerInfo.regNo) {
        this.$message({
          message: '请先选择需要随访的人！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.followUpShow = true;
    }
  },
  watch: {
    infoData: {
      handler(n, o) {
        console.log(98989898989, n, o);
        this.isOpenKeysFlag = false;
        if (this.infoData?.recordComb?.doctorName == '弃检') {
          delete this.shortcutList[113];
          this.shortcutList[114] = this.deleteRecordComb;
        } else {
          this.shortcutList[113] = this.amend;
          this.shortcutList[114] = this.waiveBtn;
        }
        if (JSON.stringify(n) !== '{}') {
          addEventListener('keyup', this.keyUpFun);
          addEventListener('keydown', this.keyDownFun);
        } else {
          console.log(99898998489489489484);
          this.isOpenKeysFlag = true;
          this.$nextTick(() => {
            removeEventListener('keyup', this.keyUpFun);
            removeEventListener('keydown', this.keyDownFun);
          });
        }
      },
      immediate: true,
      deep: true
    },
    headerInfo: {
      handler: function (n, o) {
        this.HisPatInfo = n;
      },
      deep: true
    }
  },
  // 组件激活
  activated() {
    let obj = this.$route.params;
    this.routeRow = obj;
    console.log(obj);
    if (JSON.stringify(obj) != '{}') {
      this.resDrawer = true;
    }

    this.$refs.regNoInput_Ref.focus();
  }
};
</script>

<style lang="less" scoped>
.doctorWorkStation_page {
  display: flex;
  width: 100%;
  height: 100%;
  border-radius: 4px;
  display: flex;
  color: #2d3436;
  flex-direction: row;
  // .leftBody {
  //   padding: 10px;
  //   background: #ffffff;
  //   border-radius: 4px;
  //   border-radius: 4px;
  //   display: flex;
  //   flex-direction: column;
  // }
  // .group_head {
  //   li {
  //     display: flex;
  //     height: 32px;
  //     margin-bottom: 18px;
  //   }

  //   .every_inp {
  //     display: flex;
  //     align-items: center;
  //     margin-right: 10px;
  //     width: 100%;
  //     font-family: PingFangSC-Medium;
  //     font-size: 14px;
  //     color: #2d3436;
  //     label {
  //       margin-right: 10px;
  //       width: 75px;
  //     }
  //     .btnLabel {
  //       margin-left: 10px;
  //       margin-right: 0;
  //     }
  //     p {
  //       flex: 1;
  //     }

  //     &:nth-child(4) {
  //       flex: 1;
  //     }
  //   }
  //   .times {
  //     width: 100%;
  //     .el-date-editor--daterange.el-input__inner {
  //       width: 100%;
  //     }
  //   }
  // }
  // .el-tab-pane {
  //   height: 100%;
  //   display: flex;
  //   flex-direction: column;
  //   overflow: auto;
  // }

  // .tableDiv {
  //   flex: 1;
  //   flex-shrink: 0;
  //   overflow: auto;
  // }
  // .tabs_wrap {
  //   height: 100%;
  //   display: flex;
  //   flex-direction: column;

  //   /deep/.el-tabs__content {
  //     padding: 0;
  //     flex: 1;
  //     flex-shrink: 0;
  //     overflow: auto;
  //   }

  //   .el-tab-pane {
  //     height: 100%;
  //     display: flex;
  //     flex-direction: column;
  //     overflow: auto;
  //   }

  //   .table_wrap {
  //     flex: 1;
  //     overflow: auto;
  //   }
  // }

  .rightBody {
    flex: 1;
    display: flex;
    flex-direction: column;
    // margin-left: 18px;
    overflow: auto;
    .rightTop {
      flex: 1;
      display: flex;
      flex-direction: column;
      flex-shrink: 0;
      overflow: auto;
      .btnHeader {
        display: flex;
        justify-content: space-between;
        font-family: PingFangSC-Medium;
        font-size: 18px;
        color: #2d3436;
        font-weight: 600;
      }
      .typeHeader {
        display: flex;
        // justify-content: space-between;
        align-items: center;
        background: #fff;
        font-size: 14px;
        border-radius: 4px;
        margin-bottom: 10px;
        padding: 10px;
        .every_inp {
          display: flex;
          align-items: center;
          margin-right: 10px;
          flex: 1;
          // width: 200px;
          label {
            margin-right: 10px;
            font-weight: 600;
            min-width: 50px;
          }
          p {
            flex: 1;
          }

          //   &:nth-child(4) {
          //     flex: 1;
          //   }
        }
        // .lowWidth {
        //   flex: 12%;
        // }
        .companyWidth {
          flex: 44%;
        }
        .header-info {
          display: flex;
          flex: 1;
          margin-left: 20px;
        }
        .typeHeader-left {
          display: flex;
          justify-content: space-between;
          flex: 1;
        }
      }
      .pilotInspection {
        height: 48px;
        color: #2d3436;
        display: flex;
        flex-direction: row;
        margin-bottom: 10px;
        padding: 5px 10px;
        background: #fff;
        .pilotInfoDiv {
          height: 38px;
          flex: 1;
          display: flex;
          flex-direction: row;
          line-height: 38px;
          p {
            height: 38px;
            font-size: 16px;
            color: #000;
            font-weight: 600;
            display: flex;
            flex-direction: row;
            min-width: 150px;
            margin-right: 20px;
            .redSpan {
              color: red;
              flex: 1;
            }
          }
        }
        .pilotBtnDiv {
          display: flex;
          justify-content: space-around;
          align-items: center;
        }
      }
      .rightcont {
        flex: 1;
        background: #fff;
        display: flex;
        flex-direction: column;
        padding: 0 10px;
        flex-shrink: 0;
        overflow: auto;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        .btnHeaderDiv {
          display: flex;
          justify-content: space-between;
          line-height: 52px;
          font-family: PingFangSC-Medium;
          font-size: 18px;
          color: #2d3436;
          height: 52px;
          .title {
            font-family: PingFangSC-Medium;
            font-size: 18px;
            color: #1770df;
            font-weight: 600;
            overflow: hidden; /*超出隐藏*/
            text-overflow: ellipsis; /*隐藏后添加省略号*/
            white-space: nowrap; /*强制不换行*/
          }
        }

        .contDiv {
          flex: 1;
          background: #fff;
          display: flex;
          flex-shrink: 0;
          overflow: auto;
          .contLeft {
            flex: 1;
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
            border: 1px solid #eee;
            border-radius: 4px;
            overflow: auto;
            .typeInfo {
              height: 52px;
              display: flex;
              flex-direction: row;
              line-height: 52px;
              padding: 0 18px;
              border-bottom: 1px solid #d8dee1;
              .every_inp {
                font-size: 14px;
                width: 25%;
                display: flex;
                align-items: center;
                /deep/.el-checkbox__input.is-checked .el-checkbox__inner {
                  background: #d63031;
                  border: 1px solid #d63031;
                  border-radius: 2px;
                  border-radius: 2px;
                }
                /deep/.el-checkbox__input.is-checked + .el-checkbox__label {
                  font-family: PingFangSC-Regular;
                  font-size: 14px;
                  color: #d63031;
                }
                /deep/.el-checkbox__input.is-disabled.is-checked
                  .el-checkbox__inner::after {
                  border-color: #fff;
                }
                /deep/.el-checkbox__inner::after {
                  height: 10px;
                  left: 8px;
                  position: absolute;
                  top: 2px;
                }
                & > label {
                  font-family: PingFangSC-Medium;
                  color: #000;
                  text-align: right;
                  margin-right: 10px;
                  font-weight: 600;
                }
                & > p {
                  font-family: PingFangSC-Regular;
                  font-size: 14px;
                  color: #2d3436;
                }
                &:nth-child(1) {
                  width: 12%;
                  & > label {
                    margin-right: 10px;
                  }

                  /deep/.el-checkbox__inner {
                    width: 21px;
                    height: 21px;
                  }
                }
                &:nth-child(2) {
                  width: 23%;
                  & > label {
                    width: 56px;
                  }
                }
                &:nth-child(3) {
                  width: 35%;
                  & > label {
                    width: 110px;
                  }
                }
                &:nth-child(4) {
                  width: 30%;
                  margin-left: 10px;
                  & > label {
                    width: 150px;
                  }
                }
              }
            }
            .rightTableDiv {
              flex: 1;
              flex-shrink: 0;
              overflow: auto;
              display: flex;
              flex-direction: column;
            }
          }
          .contRight {
            width: 220px;
            height: 100%;
            overflow: auto;
            border: 1px solid #eee;
            border-radius: 4px;
          }
        }
      }
    }
    .rightBom {
      padding: 10px;
      background: #fff;
      border-bottom-left-radius: 4px;
      border-bottom-right-radius: 4px;
    }
    .nodulus {
      height: 100px;
      border: 1px solid #eee;
      overflow: auto;
      border-radius: 4px;
    }
  }
  /deep/.el-date-editor.el-input,
  /deep/.el-date-editor.el-input__inner {
    width: 100%;
  }
  /deep/.el-drawer__header {
    align-items: center;
    display: flex;
    background: rgba(23, 112, 223, 0.2);
    border-radius: 4px 4px 0 0;
    line-height: 42px;
    font-family: PingFangSC-Medium;
    font-size: 18px;
    padding: 0;
    padding-left: 20px;
    color: #2d3436;
    margin-bottom: 0;
  }
  /deep/.el-drawer__close-btn {
    font-size: 30px;
  }
  /deep/.el-dialog__header {
    height: 50px;
    line-height: 50px;
    background: rgba(23, 112, 223, 0.2);
    padding: 0 18px;
  }
  /deep/.el-dialog__headerbtn {
    top: 10px;
    font-size: 30px;
  }
  /deep/.el-dialog__body {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 10px 18px 18px 18px;
    flex-shrink: 0;
    overflow: auto;
    height: calc(100% - 50px);
  }
  .replyTable {
    height: calc(100% - 50px);
    border: 1px solid rgba(178, 190, 195, 0.5);
    border-radius: 4px;
  }
  .replyTitle {
    font-size: 18px;
    color: #2d3436;
    margin-top: 8px;
    margin-bottom: 18px;
  }
  .replyText {
    color: #1770df;
    cursor: pointer;
  }
  .reply-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
  }
  .btn {
    padding: 6.5px 10px;
  }
  .searchIpt {
    width: 140px;
  }
}
/deep/ .el-table__cell {
  vertical-align: baseline;
}
/deep/.el-table th.el-table__cell > .cell {
  padding-left: 3px;
  padding-right: 3px;
}
</style>
<style lang="less">
.container {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.popverClass {
  height: calc(100% - 200px);
  .leftBody {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .group_head {
    li {
      display: flex;
      margin-bottom: 12px;
      &:last-child {
        margin-bottom: 0;
      }
    }

    .every_inp {
      display: flex;
      align-items: center;
      margin-right: 10px;
      width: 100%;
      font-family: PingFangSC-Medium;
      font-size: 14px;
      color: #2d3436;
      label {
        width: 70px;
      }
      .btnLabel {
        margin-left: 10px;
        margin-right: 0;
      }
      p {
        flex: 1;
      }

      &:nth-child(4) {
        flex: 1;
      }
    }
    .times {
      width: 100%;
      .el-date-editor--daterange.el-input__inner {
        width: 100%;
      }
    }
  }
  .el-tab-pane {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: auto;
  }

  .tableDiv {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
  }
  .tabs_wrap {
    height: 100%;
    display: flex;
    flex-direction: column;

    .el-tabs__content {
      padding: 0;
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
    }

    .el-tab-pane {
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: auto;
    }

    .table_wrap {
      flex: 1;
      overflow: auto;
    }
  }
}
/deep/.hisDialogClass {
  height: 800px !important;
  display: flex;
  flex-direction: column;
  padding: 15px;
}
.consultationReply {
  height: 700px;
  overflow: auto;
}
.reply-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}
</style>
