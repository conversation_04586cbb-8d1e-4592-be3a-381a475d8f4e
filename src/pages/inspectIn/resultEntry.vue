<template>
  <div class="doctorWorkStation_page" @click="pageClick">
    <div class="rightBody">
      <div class="rightTop">
        <div :class="mainInspectionFlag ? 'typeHeader' : 'typeHeaders'">
          <div class="typeHeader-left">
            <el-popover
              v-if="mainInspectionFlag"
              placement="bottom-start"
              width="100%"
              trigger="click"
              popper-class="popverClass"
              ref="listPopover"
              @after-enter="search"
            >
              <div class="leftBody">
                <ul class="group_head">
                  <li>
                    <div class="every_inp times">
                      <label>体检时间</label>
                      <p>
                        <el-date-picker
                          :picker-options="{ shortcuts: G_datePickerShortcuts }"
                          type="daterange"
                          format="yyyy-MM-dd"
                          value-format="yyyy-MM-dd"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          :clearable="false"
                          size="small"
                          v-model="searchTime"
                          @change="search"
                        >
                        </el-date-picker>
                      </p>
                    </div>
                    <div class="every_inp times marginDiv">
                      <label>体检单位</label>
                      <p>
                        <!-- <el-select
                          placeholder="请选择"
                          size="small"
                          filterable
                          clearable
                          v-model="companyCode"
                          style="width: 100%"
                          @change="search"
                        >
                          <el-option
                            v-for="item in companyList"
                            :key="item.companyCode"
                            :label="item.companyName"
                            :value="item.companyCode"
                          >
                          </el-option> -->
                        <el-cascader
                          ref="company_cascader_ref"
                          v-model="companyCode"
                          :options="companyList"
                          :props="{ multiple: false }"
                          :filter-method="filterMethod"
                          clearable
                          filterable
                          size="small"
                          collapse-tags
                          @change="companyChange"
                        >
                        </el-cascader>
                      </p>
                    </div>
                  </li>
                  <li v-if="G_config.physicalMode.includes('普检')">
                    <div class="every_inp times">
                      <label>体检分类</label>
                      <p>
                        <el-select
                          placeholder="请选择"
                          size="small"
                          filterable
                          clearable
                          v-model="tjCls"
                          @clear="changeTjCls"
                          style="width: 100%"
                          @change="search"
                        >
                          <el-option
                            v-for="item in G_peClsList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          >
                          </el-option>
                        </el-select>
                      </p>
                    </div>
                    <div class="every_inp marginDiv">
                      <p>
                        <el-input
                          size="small"
                          placeholder="体检号/姓名"
                          clearable
                          v-model="searchIpt"
                          @keyup.enter.native="search"
                          @clear="search"
                        ></el-input>
                      </p>
                      <label class="btnLabel">
                        <BtnCommon :btnList="['查询']" @search="search" />
                      </label>
                    </div>
                  </li>
                </ul>
                <div class="tableDiv">
                  <el-tabs
                    v-model="activeName"
                    @tab-click="handleClick"
                    class="tabs_wrap"
                  >
                    <el-tab-pane :label="notChecked" name="notChecked">
                      <PublicTable
                        :viewTableList.sync="notCheckedData"
                        :theads.sync="checkedTheads"
                        :tableLoading.sync="loading"
                        :columnWidth="columnWidth"
                        @rowDblclick="rowDblclick"
                        :columnSort="columnSort"
                        :isOnlySortShow="true"
                      >
                        <template #sex="{ scope }">
                          <div>
                            {{ sexList[scope.row.sex] }}
                          </div>
                        </template>
                        <template #peStatus="{ scope }">
                          {{ G_EnumList['PeStatus'][scope.row.peStatus] }}
                        </template>
                        <template #peCls="{ scope }">
                          <div>
                            <span>{{
                              G_EnumList['PeCls'][scope.row.peCls]
                            }}</span>
                          </div>
                        </template>
                        <template #companyName="{ scope }">
                          <div class="container" :title="scope.row.companyName">
                            {{ scope.row.companyName }}
                          </div>
                        </template>
                      </PublicTable>
                    </el-tab-pane>
                    <el-tab-pane :label="isChecked" name="isChecked">
                      <PublicTable
                        :viewTableList.sync="isCheckedData"
                        :theads.sync="checkedTheads"
                        :tableLoading.sync="loading"
                        :columnWidth="columnWidth"
                        @rowDblclick="rowDblclick"
                        :columnSort="columnSort"
                        :isOnlySortShow="true"
                      >
                        <template #sex="{ scope }">
                          <div>
                            {{ sexList[scope.row.sex] }}
                          </div>
                        </template>
                        <template #peCls="{ scope }">
                          <div>
                            <span>{{
                              G_EnumList['PeCls'][scope.row.peCls]
                            }}</span>
                          </div>
                        </template>
                        <template #peStatus="{ scope }">
                          {{ G_EnumList['PeStatus'][scope.row.peStatus] }}
                        </template>
                        <template #companyName="{ scope }">
                          <div class="container" :title="scope.row.companyName">
                            {{ scope.row.companyName }}
                          </div>
                        </template>
                      </PublicTable>
                    </el-tab-pane>
                  </el-tabs>
                </div>
              </div>
              <el-button
                slot="reference"
                class="blue_btn btn"
                size="small"
                icon="iconfont icon-liebiao"
                >查询列表</el-button
              >
            </el-popover>
            <div v-if="!mainInspectionFlag">
              <span class="header-title">{{ recordComb.combName }}</span>
            </div>
            <div class="header-info" v-if="mainInspectionFlag">
              <div class="every_inp">
                <p>
                  <el-input
                    size="small"
                    placeholder="体检号"
                    clearable
                    v-model="searchRegNoIpt"
                    @keyup.enter.native="searchRegNo"
                    class="searchIpt"
                    v-focus
                  >
                  </el-input>
                </p>
              </div>
              <div class="every_inp">
                <label>体检号:</label>
                <p>{{ headerInfo.regNo }}</p>
              </div>
              <div class="every_inp lowWidth">
                <label>姓名:</label>
                <p>{{ headerInfo.name }}</p>
              </div>
              <div class="every_inp lowWidth">
                <label>性别:</label>
                <p>{{ sexList[headerInfo.sex] }}</p>
              </div>
              <div class="every_inp lowWidth">
                <label>年龄:</label>
                <p>{{ headerInfo.age }}</p>
              </div>
              <!-- <div class="every_inp companyWidth">
                <label>单位:</label>
                <p :title="headerInfo.companyName" style="overflow-y: auto">
                  {{ headerInfo.companyName }}
                </p>
              </div> -->
            </div>
          </div>
          <div>
            <el-button
              class="blue_btn btn"
              size="small"
              icon="iconfont icon-shenhe1"
              v-if="mainInspectionFlag"
              @click="mainTest"
            >
              跳转主检
            </el-button>
            <el-button
              class="yellow_btn btn"
              size="small"
              icon="iconfont icon-dianhuazixun-dianhua"
              @click="phoneBtn"
              v-if="mainInspectionFlag"
            >
              电话
            </el-button>
            <!-- <el-button
              class="green_btn btn"
              size="small"
              icon="iconfont icon-dayin-"
              @click="printImg"
              v-if="mainInspectionFlag"
            >
              打印图例
            </el-button> -->
            <el-button
              class="violet_btn btn"
              size="small"
              icon="iconfont icon-jieshou"
              @click="resultBtnClick"
              v-if="mainInspectionFlag"
            >
              接收结果
            </el-button>
            <el-button
              class="violet_btn btn"
              size="small"
              icon="iconfont icon-zaixianzixun"
              @click="consultingClick"
              v-if="!mainInspectionFlag && !processFlag && isMain == 2"
            >
              咨询
            </el-button>
          </div>
        </div>

        <div class="rightcont">
          <div class="btnHeaderDiv" v-if="mainInspectionFlag">
            <span class="title"> {{ recordComb.combName }}</span>
            <BtnCommon
              :btnList="btnList"
              @historyReport="historyReport"
              @collection="collection"
              @amend="amend"
              :isModify.sync="isModify"
              @saves="savesBtn"
              @delResult="deleteRecordComb"
              @refuseCheck="waiveBtn"
              :infoData="infoData"
              :headerInfo="headerInfo"
            >
              <template #footAdd>
                <el-button
                  class="yellow_btn btn"
                  size="small"
                  @click="consultList"
                  icon="iconfont icon-changyonghuifu"
                  >咨询列表</el-button
                >
                <el-button
                  size="small"
                  class="green_btn btn"
                  @click="imgTextClick"
                  >图文报告</el-button
                >
                <el-button
                  size="small"
                  class="green_btn btn"
                  @click="followUpClick"
                  >添加随访</el-button
                >
                <el-button
                  size="small"
                  class="green_btn btn"
                  @click="followUpRecodeClick"
                  v-if="isHasFollowUp"
                  >随访记录</el-button
                >
              </template>
            </BtnCommon>
          </div>
          <div class="contDiv">
            <div class="contLeft">
              <div class="typeInfo">
                <div class="every_inp">
                  <!-- <label
                      ><el-checkbox v-model="typeInfo.isAbnormal"></el-checkbox
                    ></label>
                    <p>异常</p> -->
                  <el-checkbox
                    v-model="recordComb.isError"
                    v-if="mainInspectionFlag"
                    :disabled="errorDisabled"
                    >异常</el-checkbox
                  >
                  <el-checkbox
                    v-model="recordComb.isError"
                    disabled
                    v-if="!mainInspectionFlag"
                    >异常</el-checkbox
                  >
                </div>
                <div class="every_inp" style="justify-content: flex-end">
                  <label>操作员:</label>
                  <p>
                    {{
                      recordComb.operName === null
                        ? G_userInfo.codeOper.name
                        : recordComb.operName
                    }}
                  </p>
                </div>
                <div class="every_inp">
                  <label>检查日期</label>
                  <p>
                    <el-date-picker
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      :clearable="false"
                      size="mini"
                      v-model="recordComb.examTime"
                    >
                    </el-date-picker>
                  </p>
                </div>
                <div
                  class="every_inp"
                  title="第三方传入的数据显示第三方医生名字"
                >
                  <label>体检医师</label>
                  <p>
                    <!-- <el-input
                        size="mini"
                        clearable
                        v-model="recordComb.doctorName"
                        readonly
                      ></el-input> -->
                    <el-select
                      v-model="recordComb.doctorName"
                      :disabled="!isModify"
                      size="mini"
                      clearable
                      filterable
                      placeholder="请选择"
                      allow-create
                      default-first-option
                      remote
                      @visible-change="doctorListShow"
                      :remote-method="doctorfilterMethod"
                    >
                      <el-option
                        v-for="item in doctorList"
                        :key="item.index"
                        :label="item.label"
                        :value="item.label"
                      >
                      </el-option>
                    </el-select>
                  </p>
                </div>
              </div>
              <div class="rightTableDiv">
                <!-- <Nomal
                    v-show="pageStatus == 1"
                    ref="nomal"
                    :isModify.sync="isModify"
                    :pageData="infoData"
                  /> -->
                <CheckboxGroup
                  :infoData="infoData"
                  v-show="pageStatus == 3"
                  ref="checkboxGroup"
                  :isModify.sync="isModify"
                />
                <div style="flex: 1; flex-shrink: 0; overflow: auto">
                  <Examine
                    :isModify.sync="isModify"
                    :combData.sync="infoData"
                    :mode="pageStatus"
                    :headerInfo="headerInfo"
                    :checkComb.sync="checkComb"
                    ref="examine_Ref"
                    v-show="pageStatus == 2 || pageStatus == 1"
                  />
                </div>
                <div class="nodulus">
                  <PhysicalSummary
                    :isModify.sync="isModify"
                    :combData.sync="infoData"
                    :dynamicTags="dynamicTags"
                  />
                </div>
              </div>
            </div>
            <div class="contRight">
              <SideTabs
                :headerInfo="headerInfo"
                :navList="navList"
                ref="sideTabs"
                :dataQueryFlag="dataQueryFlag"
              />
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="rightBom">
        <div class="nodulus">
          <PhysicalSummary
            :isModify.sync="isModify"
            :combData.sync="infoData"
            :dynamicTags="dynamicTags"
          />
        </div>
      </div> -->
    </div>
    <el-drawer
      title="请求会诊"
      :visible.sync="sendDrawer"
      :before-close="sendDrawerHandleClose"
      :wrapperClosable="false"
      size="90%"
      v-if="sendDrawer"
    >
      <ReConsultation
        :responseConsultation="responseConsultation"
        :headerInfo="headerInfo"
      />
    </el-drawer>
    <el-drawer
      title="响应会诊"
      :visible.sync="resDrawer"
      :before-close="handleClose"
      :wrapperClosable="false"
      size="90%"
      v-if="resDrawer"
    >
      <Response :headerInfo="headerInfo" :id="id" />
    </el-drawer>
    <el-drawer
      title="历史报告"
      :visible.sync="hisDrawer"
      :before-close="hisDrawerClose"
      :wrapperClosable="false"
      size="90%"
      v-if="hisDrawer"
    >
      <HistoryReport :regNo="headerInfo.regNo" />
    </el-drawer>
    <el-drawer
      title="采集图片"
      :visible.sync="pictureDrawer"
      :before-close="pictureDrawerClose"
      :wrapperClosable="false"
      size="90%"
      v-if="pictureDrawer"
    >
      <CollectPictures
        :headerInfo="headerInfo"
        :propRegCombId="regCombId"
        :regCombs="regCombs"
        :deptCode="deptCode"
      />
    </el-drawer>
    <!-- 打印图例 -->
    <!-- <el-drawer
      title="打印图例"
      size="90%"
      :visible.sync="printImgShow"
      @closed="printImgClosed"
    >
      <PrintImg
        ref="printImg_Ref"
        :printList.sync="printList"
        :printImgList.sync="printImgList"
        :headerInfo="headerInfo"
      />
    </el-drawer> -->
    <el-drawer title="图文报告" size="90%" :visible.sync="ImgTextShow">
      <ImgText
        ref="ImgText_Ref"
        :printImgList.sync="ImgTextList"
        :headerInfo="headerInfo"
        :checkComb.sync="checkComb"
        v-if="ImgTextShow"
      />
    </el-drawer>
    <!-- 电话 -->
    <el-dialog
      title="电话联系记录"
      :visible.sync="phoneShow"
      width="60%"
      @closed="phoneClosed"
    >
      <el-dialog
        width="30%"
        title="联系内容"
        :visible.sync="innerVisible"
        append-to-body
      >
        <el-input
          type="textarea"
          :autosize="{ minRows: 4, maxRows: 4 }"
          placeholder="请输入内容"
          @keypress.enter.native="phoneConfirm"
          v-model.trim="phoneContent"
        >
        </el-input>
        <div style="text-align: right; padding-top: 10px">
          <el-button class="blue_btn btn" size="small" @click="phoneConfirm">
            确定
          </el-button>
        </div>
      </el-dialog>
      <div class="">
        <div class="phone_btn">
          <el-button
            class="blue_btn btn"
            size="small"
            icon="iconfont icon-xinjian"
            @click="addPhone"
          >
            添加
          </el-button>
          <el-button
            class="red_btn btn"
            size="small"
            icon="iconfont icon-shanchu"
            @click="delPhone"
          >
            删除
          </el-button>
        </div>
        <div style="height: 500px">
          <PublicTable
            :theads="phoneTheads"
            :columnWidth="phoneColumn"
            :viewTableList="phoneList"
            @rowClick="phoneRowClick"
            :isSortShow="false"
          />
        </div>
      </div>
    </el-dialog>
    <!-- 咨询弹窗 -->
    <el-dialog
      :visible.sync="consultingShow"
      width="800px"
      top="12%"
      custom-class="consultingShow"
      :close-on-click-modal="false"
      @closed="cancel"
    >
      <div slot="title" class="dialog-title">咨询录入窗口</div>
      <el-input
        type="textarea"
        :rows="20"
        placeholder="请输入咨询内容"
        v-model="consultingInput"
        style="margin-top: 20px"
      >
      </el-input>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel" size="small">取消</el-button>
        <el-button class="blue_btn" @click="consultingSubmit" size="small"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <!-- 咨询消息列表 -->
    <el-dialog
      :visible.sync="C_consultationReply"
      width="1260px"
      top="6%"
      custom-class="consultationReply"
      :close-on-click-modal="false"
      @close="consultationReply = false"
    >
      <div slot="title" class="dialog-title">咨询回复</div>
      <h3 class="replyTitle">医生咨询列表：</h3>
      <div class="replyTable">
        <PublicTable
          :viewTableList.sync="replyData"
          :theads.sync="theads"
          :isSortShow="false"
          :columnWidth="{
            regNo: 124,
            combName: 120,
            questionTime: 166,
            questionContent: 200,
            replyTime: 166,
            replyContent: 200
          }"
        >
          <template #columnRight>
            <el-table-column width="60" label="操作">
              <template slot-scope="scope">
                <el-popover
                  placement="bottom-end"
                  width="380"
                  v-model="scope.row.backoutPopoverShow"
                  trigger="click"
                >
                  <el-input
                    type="textarea"
                    :autosize="{ minRows: 6, maxRows: 6 }"
                    placeholder="请输入内容"
                    v-model.trim="scope.row.replyContent"
                  >
                  </el-input>
                  <div class="reply-footer">
                    <el-button
                      @click="scope.row.backoutPopoverShow = false"
                      size="small"
                      >取消</el-button
                    >
                    <el-button
                      class="blue_btn"
                      @click="replySubmit(scope.row)"
                      size="small"
                      >确定</el-button
                    >
                  </div>
                  <div slot="reference" class="replyText">回复</div>
                </el-popover>
              </template>
            </el-table-column>
          </template>
        </PublicTable>
      </div>
    </el-dialog>
    <el-drawer
      title="医生咨询回复一览表"
      :visible.sync="consultDialog"
      :before-close="consultDialogClose"
      :wrapperClosable="false"
      size="90%"
      v-if="consultDialog"
    >
      <Consult :conclusionList="conclusionList" />
    </el-drawer>
    <!-- 添加随访 -->
    <FollowUp
      :regNo="headerInfo.regNo"
      :followUpShow.sync="followUpShow"
      @followUpClose="followUpClose"
    />

    <!-- 接收结果 -->
    <ReceiveReportResult
      :visible="visibleRecReportResult"
      :regNo="headerInfo.regNo"
      @closed="closedRecReportResult"
    />
  </div>
</template>

<script>
import BtnCommon from './components/btnCommon.vue';
import PublicTable from '../../components/publicTable.vue';
import PhysicalSummary from './components/doctorWorkStation/physicalSummary.vue';
import SideTabs from './components/doctorWorkStation/sideTabs.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
import Nomal from './components/doctorWorkStation/nomal.vue'; //血常规（五分类）表格页面
import CheckboxGroup from './components/doctorWorkStation/checkboxGroup.vue';
import Response from './components/doctorWorkStation/response.vue';
import ReConsultation from './components/doctorWorkStation/reConsultation.vue';
import HistoryReport from './components/doctorWorkStation/historyReport.vue';
import CollectPictures from './components/doctorWorkStation/collectPictures.vue';
import Examine from './components/doctorWorkStation/examine.vue';
import PrintImg from './components/resultEntry/printImg';
import printMixins from '@/components/printMixins';
import urgent from './mixins/urgent';
import shortcut from '@/common/shortcut';
import consultMsgTips from './mixins/consultMsgTips';
import consultList from './mixins/consultList';
import Consult from '../mainInspectionPage/consult.vue';
import ImgText from './components/resultEntry/imgText.vue';
import imgTextJs from './mixins/imgText';
import FollowUp from '@/components/followUp';
import ReceiveReportResult from './components/resultEntry/receiveReportResult';

export default {
  name: 'resultEntry',
  mixins: [
    printMixins,
    urgent,
    shortcut,
    consultMsgTips,
    consultList,
    imgTextJs
  ],
  props: {
    // 是否主检
    isMain: {
      type: Number,
      default: 2
    },
    // 是否审核页面
    processFlag: {
      type: Boolean,
      default: false
    },
    mainInspectionFlag: {
      type: Boolean,
      default: true
    },
    dataQueryFlag: {
      type: Boolean,
      default: true
    },
    // 是否需要显示咨询弹窗
    isConsultationReplyFlag: {
      type: Boolean,
      default: true
    },
    // 是否开启编辑模式
    P_isModify: {
      type: Boolean,
      default: true
    }
  },
  components: {
    BtnCommon,
    PublicTable,
    Nomal,
    PhysicalSummary,
    SideTabs,
    CheckboxGroup,
    ReConsultation,
    Response,
    HistoryReport,
    CollectPictures,
    Examine,
    PrintImg,
    Consult,
    ImgText,
    FollowUp,
    ReceiveReportResult
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_peClsList',
      'G_userInfo',
      'G_codeDepartment',
      'G_sysOperator',
      'G_datePickerShortcuts',
      'G_config'
    ]),
    C_consultationReply() {
      return this.consultationReply && this.consultationReplyFlag;
    }
  },
  data() {
    return {
      HisPatInfo: {},
      resultState: 1, //获取关联结果的请求状态，1 表示获取完成，2表示正在获取中；
      shortcutList: {
        112: this.savesBtn,
        113: this.amend,
        114: this.waiveBtn,
        115: this.deleteRecordComb
      },
      companyList: [],
      sendDrawer: false,
      resDrawer: false,
      hisDrawer: false,
      pictureDrawer: false,
      searchTime: [dataUtils.getDate(), dataUtils.getDate()],
      searchInfo: {},
      regCombId: '',
      peStatus: 0, //体检状态 (查全部 = -1,未检查 = 0, 正在检查 = 1, 已检完 = 2, 已总检 = 3, 已审核 = 4)
      companyCode: '',
      tjCls: null,
      searchIpt: '',
      searchRegNoIpt: '',
      notChecked: '未完成人员(0)',
      isChecked: '已完成人员(0)',
      activeName: 'notChecked',
      loading: false,
      checkedTheads: {
        peCls: '体检分类',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        companyName: '体检单位',
        tel: '手机号',
        regNo: '体检号',
        registerTime: '登记时间',
        activeTime: '体检时间',
        peStatus: '体检状态'
      },
      notCheckedData: [], //未检人员数据
      isCheckedData: [], //已检人员数据
      headerInfo: {}, //行信息
      columnWidth: {
        registerTime: 170,
        activeTime: 170,
        regNo: 125,
        peCls: 110,
        companyName: 150,
        name: 80,
        sex: 60,
        age: 60,
        tel: 115,
        peStatus: 100
      },
      columnSort: [
        'peCls',
        'name',
        'sex',
        'age',
        'companyName',
        'registerTime',
        'activeTime',
        'peStatus'
      ],
      sexList: {
        null: '',
        0: '通用',
        1: ' 男',
        2: '女'
      },
      headerInfo: {
        regNo: '',
        name: '',
        sex: null,
        age: null,
        companyName: ''
      },
      titleName: '血常规（五分类）',
      typeInfo: {
        isAbnormal: false,
        operatorCode: '系统员',
        checkDate: dataUtils.getDate(),
        doctor: '系统员'
      },
      pageStatus: '',
      btnList: [],
      dynamicTags: [],
      isModify: this.P_isModify, //是否修改
      navList: [],
      infoData: {},
      backoutInitData: {}, //撤销时使用的初始化数据
      recordComb: {},
      isEnabled: false,
      combCode: '',
      clsCode: '',
      examDeptCode: '',
      id: null,
      errorDisabled: true,
      printImgShow: false,
      phoneShow: false,
      phoneTheads: {
        createTime: '联系时间',
        contactPerson: '联系人',
        content: '联系内容'
      },
      phoneColumn: {},
      phoneList: [],
      innerVisible: false,
      phoneContent: '',
      checkPhone: {},
      printImgList: [],
      printList: [], //打印机列表
      consultingShow: false,
      consultingInput: '',
      isSave: false,
      doctorList: [],
      followUpShow: false,
      visibleRecReportResult: false
    };
  },
  created() {},
  mounted() {
    //this.search();
    this.getCompany();
  },
  methods: {
    pageClick() {
      this.$refs.examine_Ref.resultLeave();
    },
    doctorListShow(flag) {
      console.log(flag);
      if (flag) {
        this.doctorList = this.G_sysOperator;
      }
    },
    doctorfilterMethod(query) {
      console.log(query);
      if (query !== '') {
        setTimeout(() => {
          this.doctorList = this.G_sysOperator.filter((item) => {
            return (
              item.label.indexOf(query) != -1 || item.value.indexOf(query) != -1
            );
          });
        }, 200);
      } else {
        this.doctorList = this.G_sysOperator;
      }
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    //获取单位下拉
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    //单位下拉修改
    companyChange(data) {
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.searchTime = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.search();
    },
    rowDblclick(row) {
      console.log(row);
      if (
        this.isModify &&
        JSON.stringify(this.fixed_infoData) != '{}' &&
        JSON.stringify(this.infoData?.recordItems) !=
          JSON.stringify(this.fixed_infoData?.recordItems)
      ) {
        this.$confirm('结果有修改，是否保存?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        })
          .then(() => {
            this.saves(false).then((r) => {
              this.rowClickFun(row);
            });
          })
          .catch(() => {
            this.rowClickFun(row);
          });
        return;
      }
      this.rowClickFun(row);
    },
    rowClickFun(row) {
      console.log(row);
      this.searchRegNoIpt = '';
      this.headerInfo = row;
      this.infoData = {};
      this.pageStatus = '';
      this.recordComb = {};
      this.dynamicTags = [];
      this.getNavList().then(() => {
        this.autoCheckObj();
      });
      this.getImgTextList(row.regNo);
      this.$refs.sideTabs.template = '';
      this.$refs.sideTabs.liIndex = null;
      this.$refs.sideTabs.activeName = '';
      this.$refs.sideTabs.imgList = [];
      this.isSave = false;
      // if(row.peStatus > 1){
      //   this.isModify = false;
      // }else{
      //   this.isModify = true;
      // }
    },
    // 自动选中未检的项目
    autoCheckObj() {
      this.navList.some((item, idx, arr) => {
        if (item.combStatus == 1) {
          this.$refs.sideTabs.liIndex = item.regCombId;
          this.$refs.sideTabs.navClickFun(item);
          return true;
        }
        if (item.combStatus > 1 && idx === arr.length - 1) {
          this.$refs.sideTabs.liIndex = arr[0].regCombId;
          this.$refs.sideTabs.navClickFun(arr[0]);
          return true;
        }
      });
    },
    //查询
    async search() {
      // this.$refs.nomal.tableDatas = [];
      // this.navList = [];
      this.searchNotChecked();
      // this.$refs.sideTabs.template = "";
      // this.$refs.sideTabs.imgList = [];
    },

    //体检号回车查询
    searchRegNo() {
      // this.infoData = {};
      // this.regNoGetInfo(this.searchRegNoIpt); //获取内容信息
      // this.infoData = {};
      // this.pageStatus = "";
      // this.recordComb = {};
      if (
        this.isModify &&
        JSON.stringify(this.fixed_infoData) != '{}' &&
        JSON.stringify(this.infoData?.recordItems) !=
          JSON.stringify(this.fixed_infoData?.recordItems)
      ) {
        this.$confirm('结果有修改，是否保存?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        })
          .then(() => {
            this.saves(false).then((r) => {
              this.infoData = {};
              this.regNoGetInfo(this.searchRegNoIpt);
            });
          })
          .catch(() => {
            this.infoData = {};
            this.regNoGetInfo(this.searchRegNoIpt);
          });
        return;
      }
      this.infoData = {};
      this.regNoGetInfo(this.searchRegNoIpt);
    },
    //清空
    clearRegNo() {
      this.btnList = [];
      this.headerInfo = {};
      this.infoData = {};
      this.pageStatus = '';
      this.recordComb = {};
      this.navList = [];
    },
    //获取内容信息
    regNoGetInfo(searchRegNoIpt) {
      this.infoData = {};
      this.pageStatus = '';
      this.recordComb = {};
      this.getNavList(searchRegNoIpt).then(() => {
        this.navList.some((item, idx, arr) => {
          if (item.combStatus == 1) {
            this.$refs.sideTabs.liIndex = item.regCombId;
            this.$refs.sideTabs.navClickFun(item);
            return true;
          }
          if (item.combStatus > 1 && idx === arr.length - 1) {
            this.$refs.sideTabs.liIndex = arr[0].regCombId;
            this.$refs.sideTabs.navClickFun(arr[0]);
            return true;
          }
        });
      });
      this.getImgTextList(searchRegNoIpt);
      this.$refs.sideTabs.template = '';
      this.$refs.sideTabs.liIndex = null;
      this.$refs.sideTabs.activeName = '';
      this.$refs.sideTabs.imgList = [];
      this.isSave = false;
    },
    // 获取项目导航
    getNavList(regNo) {
      return new Promise((resolve, reject) => {
        let data = {
          // operCode: this.G_userInfo.codeOper.operatorCode,
          // regNo: regNo || this.headerInfo.regNo,
          regNo: regNo || this.headerInfo.regNo,
          filterDoctor: false,
          doctorCode: this.G_userInfo.codeOper.operatorCode
        };
        this.headerInfo = {};
        this.$ajax.post(this.$apiUrls.GetExamCombList, data).then((r) => {
          console.log('GetExamCombList', r);
          this.searchRegNoIpt = '';
          let { success, returnData, returnMsg } = r.data;
          if (!success) return;
          if (returnData == null) {
            this.$message({
              message: returnMsg,
              type: 'success',
              showClose: true
            });
            this.btnList = [];
            this.headerInfo = {};
            this.infoData = {};
            this.pageStatus = '';
            this.recordComb = {};
            this.navList = [];
            return;
          }
          // if(returnData.person.peStatus > 2){
          //   this.isModify = false;
          // }else{
          //   this.isModify = true;
          // }
          this.headerInfo = returnData.person;
          this.navList = returnData.examCombs;
          if (this.mainInspectionFlag) {
            this.$refs.listPopover.doClose();
          }
          this.getFollowUpState();
          // if (!this.isSave) {
          //   this.navList.map(item => {
          //     if (item.isMain) {
          //       this.$refs.sideTabs.activeName = item.deptCode;
          //       this.$refs.sideTabs.liIndex = null;
          //     }
          //   });
          // } else {
          //   this.$refs.sideTabs.activeName = this.examDeptCode;
          // }
          resolve();
        });
      });
    },
    // 获取组合内容
    getCombInfo(data) {
      this.pageStatus = data.template;
      this.infoData = {};
      this.fixed_infoData = {};
      this.$ajax.post(this.$apiUrls.ReadRecordComb, data).then((r) => {
        console.log('ReadPeRecordByComb', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.infoData = dataUtils.deepCopy(returnData || {});
        console.log(this.infoData);
        setTimeout(() => {
          this.fixed_infoData = dataUtils.deepCopy(this.infoData || {});
        }, 200);
        this.backoutInitData = JSON.parse(JSON.stringify(returnData));
        this.recordComb = returnData.recordComb;
        this.dynamicTags = returnData.combTags;
        if (this.pageStatus === 3) {
          this.$refs.checkboxGroup.getCheckboxList(
            returnData.recordComb.combCode
          );
        }
        if (!this.P_isModify) {
          this.isModify = false;
          return;
        }
        this.$nextTick(() => {
          if (
            this.headerInfo.peStatus > 2 ||
            returnData.recordComb.doctorName === '弃检'
          ) {
            this.isModify = false;
          } else {
            this.isModify = true;
            this.$refs.examine_Ref.nextRowCurrent({ rowIndex: null });
          }
        });

        // if (this.pageStatus == 1) {
        //   this.$refs.nomal.tableDatas = [];
        //   returnData.recordItems.map(item => {
        //     this.$refs.nomal.tableDatas.push({
        //       itemName: item.itemName,
        //       hint: item.hint,
        //       itemResult: item.itemTags[0]?.tag,
        //       unit: item.unit,
        //       referenceVal:
        //         item.lowerLimit == null
        //           ? ""
        //           : item.lowerLimit + "~" + item.upperLimitt == null
        //           ? ""
        //           : item.upperLimitt,
        //       lastItemResult: item.lastItemResult
        //     });
        //   });
        // }
      });
    },
    //清空体检分类初始化值
    changeTjCls() {
      this.tjCls = null;
    },
    //查询未检人员
    searchNotChecked() {
      // this.headerInfo = {};
      (this.searchInfo = {
        operCode: this.G_userInfo.codeOper.operatorCode,
        startTime: this.searchTime[0],
        endTime: this.searchTime[1],
        keyWord: this.searchIpt,
        peStatus: 1,
        companyCode: this.companyCode[0],
        peCls: !this.tjCls ? -1 : this.tjCls
      }),
        this.$ajax
          .post(this.$apiUrls.RecordGetPatientList, this.searchInfo)
          .then((r) => {
            console.log('r', r);
            let { success, returnData } = r.data;
            if (!success) return;
            if (returnData.length >= 1) {
              this.notCheckedData = returnData;
              this.notChecked = '未完成人员' + '(' + returnData.length + ')';
            } else {
              this.notCheckedData = [];
              this.notChecked = '未完成人员' + '(0)';
              // this.$message({
              //   message: "暂无数据!",
              //   type: "success",
              //   showClose: true,
              // });
            }
            this.searchIsCheckedData();
          });
    },
    //查询已检人员
    searchIsCheckedData() {
      (this.searchInfo = {
        operCode: this.G_userInfo.codeOper.operatorCode,
        startTime: this.searchTime[0],
        endTime: this.searchTime[1],
        keyWord: this.searchIpt,
        peStatus: 2,
        companyCode: this.companyCode[0],
        peCls: this.tjCls === null ? -1 : this.tjCls
      }),
        this.$ajax
          .post(this.$apiUrls.RecordGetPatientList, this.searchInfo)
          .then((r) => {
            console.log('r', r);
            let { success, returnData } = r.data;
            if (!success) return;
            if (returnData.length >= 1) {
              this.isCheckedData = returnData;
              this.isChecked = '已完成人员' + '(' + returnData.length + ')';
            } else {
              this.isCheckedData = [];
              this.isChecked = '已完成人员' + '(0)';
              // this.$message({
              //   message: "暂无数据!",
              //   type: "success",
              //   showClose: true,
              // });
            }
            if (
              this.notCheckedData.length < 1 &&
              this.isCheckedData.length < 1
            ) {
              this.$message({
                message: '暂无数据!',
                type: 'success',
                showClose: true
              });
            }
          });
    },
    //关闭抽屉
    sendDrawerHandleClose() {
      this.sendDrawer = false;
    },
    //关闭抽屉
    handleClose() {
      this.resDrawer = false;
    },
    //关闭历史报告弹出
    hisDrawerClose() {
      this.hisDrawer = false;
    },
    historyReport() {
      if (!this.headerInfo.regNo) {
        this.$message({
          message: '请先点击要查看历史报告的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.hisDrawer = true;
    },
    pictureDrawerClose() {
      console.log('[ this.regCombId ]-496', this.regCombId);
      this.pictureDrawer = false;
      this.$refs.sideTabs.getImage(
        this.regCombId,
        this.$refs.sideTabs.deptCode
      ); //重新获取图例
    },

    collection() {
      if (!this.headerInfo.regNo) {
        this.$message({
          message: '请先点击要添加采集图片的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.pictureDrawer = true;
    },
    sendConsultation() {
      if (!this.headerInfo.regNo) {
        this.$message({
          message: '请先点击要查看发起会诊的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.sendDrawer = true;
      // let sendData = {
      //   SendUser: "admin",
      //   ReceiveUser: "1234",
      //   DeptArray: ["00", "01"],
      //   InitFlag: true,
      //   Content: "168168168168168168"
      // };
      // this.$ws.sendSock(JSON.stringify(sendData), function () {
      //   console.log(123);
      // });
    },
    responseConsultation(id) {
      if (id) {
        this.id = id;
      } else {
        this.id = null;
      }
      // if (!this.headerInfo.regNo) {
      //   this.$message({
      //     message: "请先点击要查看响应会诊的数据!",
      //     type: "warning",
      //     showClose: true
      //   });
      //   return;
      // }
      this.sendDrawer = false;
      this.resDrawer = true;
    },
    handleClick(tab, event) {
      console.log(this.activeName);
      // this.headerInfo = {};
      console.log(tab, event);
      // if ((this.activeName = "notChecked")) {
      //   this.peStatus = 0;
      // } else {
      //   this.peStatus = 1;
      // }
      // this.search();
    },
    // 修改按钮的点击回调
    amend() {
      console.log(this.infoData);
      if (this.isModify) {
        this.$confirm('是否撤销所有编辑？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        })
          .then(() => {
            this.infoData = JSON.parse(JSON.stringify(this.backoutInitData));
            this.isModify = !this.isModify;
            this.$message({
              type: 'success',
              message: '撤销成功!'
            });
            console.log(this.infoData);
          })
          .catch(() => {});
      } else {
        this.isModify = !this.isModify;
      }
    },
    savesBtn() {
      if (this.$refs.examine_Ref.checkRowIndex !== null) {
        if (
          this.$refs.examine_Ref.$refs[
            'inpFocus_' + this.$refs.examine_Ref.checkRowIndex
          ]?.focus
        ) {
          this.$refs.examine_Ref.$refs[
            'inpFocus_' + this.$refs.examine_Ref.checkRowIndex
          ].blur();
        } else {
          this.$refs.examine_Ref.$refs[
            'inpFocus_' + this.$refs.examine_Ref.checkRowIndex
          ].inputHandle();
          let itemComb =
            this.infoData.recordItems[this.$refs.examine_Ref.checkRowIndex];
          this.$nextTick(() => {
            itemComb.itemTags.map((item, idx) => {
              console.log(
                this.$refs.examine_Ref.$refs[
                  'inpFocus_' + this.$refs.examine_Ref.checkRowIndex
                ].$refs['inputSpan_Ref_' + itemComb.itemCode + '_' + idx],
                idx
              );
              this.$refs.examine_Ref.$refs[
                'inpFocus_' + this.$refs.examine_Ref.checkRowIndex
              ].$refs[
                'inputSpan_Ref_' + itemComb.itemCode + '_' + idx
              ][0].blur();
            });
          });
        }
      }

      setTimeout(() => {
        this.saves(false).then((r) => {
          this.$refs.examine_Ref.abnormalPopup = false;
          this.$refs.examine_Ref.resultPopup = false;
          this.$refs.examine_Ref.editPopup = false;
          this.getNavList().then(() => {
            let datas = {
              regCombId: this.infoData.recordComb.regCombId,
              regNo: this.infoData.recordComb.regNo,
              template: this.$refs.sideTabs.checkGroup.template
            };
            this.getCombInfo(datas);
          });
        });
      }, 300);
    },
    // 保存的通用函数
    saves(flag = true) {
      return new Promise((resolve, reject) => {
        console.log(this.infoData);
        console.log(this.dynamicTags);
        if (this.resultState == 2) {
          setTimeout(() => {
            this.saves();
          }, 200);
          return;
        }

        if (!this.recordComb.doctorName) {
          this.$message({
            message: '请选择体检医师再保存!',
            type: 'warning',
            showClose: true
          });
          return;
        }
        let datas = {
          recordComb: {
            regNo: this.headerInfo.regNo,
            regCombId: this.regCombId,
            combCode: this.combCode,
            combName: this.recordComb.combName,
            clsCode: this.clsCode,
            // examDeptCode: this.examDeptCode,
            operName: this.recordComb.operName || this.G_userInfo.codeOper.name,
            doctorName: this.recordComb.doctorName,
            isError: this.recordComb.isError,
            examTime: this.recordComb.examTime
          },
          // 项目
          recordItems: this.infoData.recordItems,
          // 小结
          combTags: this.dynamicTags
        };
        console.log(datas);
        // setTimeout(() => {
        this.$ajax.post(this.$apiUrls.SaveRecordComb, datas).then((r) => {
          let { success } = r.data;
          if (!success) return;
          // this.isModify = false;
          this.$message({
            message: '保存成功!',
            type: 'success',
            showClose: true
          });
          // this.isModify = false;
          this.isSendNotice(this.infoData.recordItems, this.headerInfo.regNo);
          if (flag) {
            this.initNavList();
          }
          this.errorDisabled = true;
          resolve();
          // this.btnList = [];
          this.btnList.push('删除结果');
        });
        // }, 1500);
      });
    },
    //删除结果
    deleteRecordComb() {
      let examDeptCode = this.examDeptCode;
      let liIndex = this.$refs.sideTabs.liIndex;
      let data = {
        regNo: this.headerInfo.regNo,
        regCombId: this.regCombId
      };
      if (!this.regCombId) {
        this.$message({
          message: '请先选择要组合项目结果记录!',
          type: 'warning'
        });
        return false;
      }
      this.$confirm(`是否确定删除?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      })
        .then(() => {
          this.$ajax.post(this.$apiUrls.DeleteRecordComb, data).then((r) => {
            let { success } = r.data;
            if (!success) return;
            this.$message({
              message: '删除成功!',
              type: 'success',
              showClose: true
            });
            this.getNavList().then((r) => {
              this.$refs.sideTabs.activeName = examDeptCode;
              this.$refs.sideTabs.liIndex = liIndex;
            });
            this.errorDisabled = true;
            // this.btnList = [];
            let datas = {
              regCombId: this.infoData.recordComb.regCombId,
              regNo: this.infoData.recordComb.regNo,
              template: this.$refs.sideTabs.checkGroup.template
            };
            this.getCombInfo(datas);
            let idx = this.btnList.indexOf('删除结果');
            if (idx != -1) {
              this.btnList.splice(idx, 1);
            }
          });
        })
        .catch(() => {});
    },
    // 弃检
    waiveBtn() {
      if (this.infoData.recordComb.doctorName == '弃检') {
        this.$message({
          message: '该组合已经弃检！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let examDeptCode = this.examDeptCode;
      let liIndex = this.$refs.sideTabs.liIndex;
      this.$confirm(`是否要弃检${this.infoData.recordComb.combName}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      })
        .then(() => {
          let data = {
            regNo: this.infoData.recordComb.regNo,
            regCombId: this.infoData.recordComb.regCombId,
            operName: this.G_userInfo.codeOper.name
            // sampCode: ""
          };
          this.$ajax.post(this.$apiUrls.AbandonComb, data).then((r) => {
            console.log(99999999999999999999, r);
            let { success, returnData } = r.data;
            if (!success) return;
            this.$message({
              message: '弃检成功!',
              type: 'success',
              showClose: true
            });
            let datas = {
              regCombId: this.infoData.recordComb.regCombId,
              regNo: this.infoData.recordComb.regNo,
              template: this.$refs.sideTabs.checkGroup.template
            };
            console.log(datas);
            this.getCombInfo(datas);
            this.getNavList().then((r) => {
              this.$refs.sideTabs.activeName = examDeptCode;
              this.$refs.sideTabs.liIndex = liIndex;
            });
          });
        })
        .catch(() => {});
    },
    printImg() {
      if (!this.headerInfo.regNo) {
        this.$message({
          message: '请选择体检人',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.printImgList = [];
      this.printImgShow = true;
      this.$nextTick(() => {
        console.log();
        this.$refs.printImg_Ref.$refs.printImgMenu_Ref.activeIndex = null;
        let datas = {
          regNo: this.headerInfo.regNo
        };
        this.$ajax.paramsPost(this.$apiUrls.ReadPrintImage, datas).then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.printImgList = returnData || [];
        });
      });
    },
    // 电话
    phoneBtn() {
      if (!this.headerInfo.regNo) {
        this.$message({
          message: '请选择体检人',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.phoneShow = true;
      this.getPhoneList();
    },
    // 获取电话联系列表
    getPhoneList() {
      let datas = {
        regNo: this.headerInfo.regNo
      };
      this.$ajax
        .paramsPost(this.$apiUrls.ReadContactRecord, datas)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.phoneList = returnData || [];
        });
    },
    // 添加咨询
    addPhone() {
      this.innerVisible = true;
    },
    // 确认联系
    phoneConfirm() {
      if (!this.phoneContent) {
        this.$message({
          message: '请输入联系内容！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.innerVisible = false;
      let datas = {
        id: 0,
        content: this.phoneContent,
        createTime: '2022-11-29T06:40:13.143Z',
        regNo: this.headerInfo.regNo,
        contactPerson: this.G_userInfo.codeOper.name
      };
      this.$ajax.post(this.$apiUrls.CreateContactRecord, datas).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.getPhoneList();
      });
    },
    // 联系列表的点击回调
    phoneRowClick(row) {
      this.checkPhone = row;
    },
    // 删除联系信息
    delPhone() {
      if (!this.checkPhone.regNo) {
        this.$message({
          message: '请选择要删除的联系！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      })
        .then(() => {
          let datas = {
            id: this.checkPhone.id
          };
          this.$ajax
            .paramsPost(this.$apiUrls.DeleteContactRecord, datas)
            .then((r) => {
              let { success, returnData } = r.data;
              if (!success) return;
              this.getPhoneList();
              this.$message({
                type: 'success',
                message: '删除成功!',
                showClose: true
              });
            });
        })
        .catch(() => {});
    },
    // 电话弹窗关闭的回调
    phoneClosed() {
      this.checkPhone = {};
      console.log(this.checkPhone);
    },
    // 打印图例关闭的回调
    printImgClosed() {
      this.$refs.printImg_Ref.imgSrc = '';
    },
    // 咨询按钮点击
    consultingClick() {
      if (!this.combCode) {
        this.$message({
          message: '请选择要咨询的项目！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$ajax
        .post(this.$apiUrls.IsExistQueue, '', {
          query: {
            questionerCode: this.G_userInfo.codeOper.operatorCode,
            combCode: this.combCode
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          if (returnData) {
            this.$message({
              message: '已咨询过该项目！',
              type: 'warning',
              showClose: true
            });
          } else {
            this.consultingShow = true;
          }
        });
    },
    // 咨询发送
    consultingSubmit() {
      if (!this.consultingInput) {
        this.$message({
          message: '请输入咨询内容！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let data = {
        regNo: this.$parent.$parent.$parent.regNo,
        combCode: this.combCode,
        combName: this.recordComb.combName,
        questionerCode: this.G_userInfo.codeOper.operatorCode,
        questionerName: this.G_userInfo.codeOper.name,
        questionContent: this.consultingInput,
        // replyerCode: "",
        replyerName: this.recordComb.operName || this.G_userInfo.codeOper.name
      };
      this.$ajax.post(this.$apiUrls.Question, data).then((r) => {
        console.log('r: ', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.$message({
          type: 'success',
          message: '咨询问题发送成功!',
          showClose: true
        });
        this.consultingShow = false;
      });
    },
    // 取消
    cancel() {
      this.consultingShow = false;
    },
    // 主检
    mainTest() {
      if (this.headerInfo.regNo == '') {
        this.$message({
          type: 'warning',
          message: '请先选择体检人!',
          showClose: true
        });
        return;
      }
      this.$router.push({
        name: 'mainInspection',
        params: {
          regNo: this.headerInfo.regNo
        }
      });
    },
    // 接收结果
    acceptResult() {
      if (this.headerInfo.regNo == '') {
        this.$message({
          type: 'warning',
          message: '请先选择体检人!',
          showClose: true
        });
        return;
      }
      this.$ajax
        .paramsPost(this.$apiUrls.ManuallyAcceptResults, {
          regNo: this.headerInfo.regNo
        })
        .then((r) => {
          let { success } = r.data;
          if (!success) return;
          this.$message({
            type: 'success',
            message: '接收结果成功！',
            showClose: true
          });
        });
    },
    // 显示接收结果弹窗
    resultBtnClick() {
      if (this.headerInfo.regNo == '') {
        this.$message({
          type: 'warning',
          message: '请先选择体检人!',
          showClose: true
        });
        return;
      }
      this.visibleRecReportResult = true;
    },
    // 初始化右边导航菜单
    initNavList() {
      let examDeptCode = this.$refs.sideTabs.activeName;
      let liIndex = this.$refs.sideTabs.liIndex;
      console.log(examDeptCode, this.$refs.sideTabs.liIndex);
      this.getNavList().then((r) => {
        this.$refs.sideTabs.activeName = examDeptCode;
        this.$refs.sideTabs.liIndex = liIndex;
      });
    },
    followUpClose(flag) {
      console.log(flag);
      this.followUpShow = flag;
    },
    // 添加随访的弹窗显示
    followUpClick() {
      if (!this.headerInfo.regNo) {
        this.$message({
          message: '请先选择需要随访的人！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.followUpShow = true;
    },
    /**
     * @author: justin
     * @description: 关闭接收结果弹窗回调
     * @return {*}
     */
    closedRecReportResult() {
      this.visibleRecReportResult = false;
    }
  },
  watch: {
    printImgShow: {
      handler(n, o) {
        if (n) {
          this.connectPrint((r) => {
            let dataInfo = JSON.parse(r.data);
            console.log(dataInfo);
            this.printList = dataInfo.Data;
          });
        } else {
          if (JSON.stringify(this.ws) == '{}') return;
          this.ws.closeWebSocket && this.ws.closeWebSocket();
          this.ws.reConnect = () => {};
          this.ws.createWebSocket = () => {};
          this.ws.heartCheck.stop();
        }
      }
    },
    infoData: {
      handler(n, o) {
        console.log(98989898989, n, o);
        this.isOpenKeysFlag = false;
        if (this.infoData?.recordComb?.doctorName == '弃检') {
          delete this.shortcutList[113];
          this.shortcutList[114] = this.deleteRecordComb;
        } else {
          this.shortcutList[113] = this.amend;
          this.shortcutList[114] = this.waiveBtn;
        }
        if (JSON.stringify(n) !== '{}') {
          addEventListener('keyup', this.keyUpFun);
          addEventListener('keydown', this.keyDownFun);
        } else {
          console.log(99898998489489489484);
          this.isOpenKeysFlag = true;
          this.$nextTick(() => {
            removeEventListener('keyup', this.keyUpFun);
            removeEventListener('keydown', this.keyDownFun);
          });
        }
      },
      immediate: true,
      deep: true
    },
    G_sysOperator: {
      handler(n, o) {
        console.log(n, o);
        this.doctorList = n;
      },
      immediate: true,
      deep: true
    },
    headerInfo: {
      handler: function (n, o) {
        this.HisPatInfo = n;
      },
      deep: true
    }
  }
};
</script>

<style lang="less" scoped>
.doctorWorkStation_page {
  display: flex;
  width: 100%;
  height: 100%;
  border-radius: 4px;
  display: flex;
  color: #2d3436;
  flex-direction: row;
  // .leftBody {
  //   width: 332px;
  //   padding: 10px;
  //   background: #ffffff;
  //   border-radius: 4px;
  //   border-radius: 4px;
  //   display: flex;
  //   flex-direction: column;
  //   margin-right: 18px;
  //   .group_head {
  //     li {
  //       display: flex;
  //       height: 32px;
  //       margin-bottom: 18px;
  //     }

  //     .every_inp {
  //       display: flex;
  //       align-items: center;
  //       margin-right: 10px;
  //       width: 100%;
  //       font-family: PingFangSC-Medium;
  //       font-size: 14px;
  //       color: #2d3436;
  //       label {
  //         margin-right: 10px;
  //         width: 75px;
  //       }
  //       .btnLabel {
  //         margin-left: 10px;
  //         margin-right: 0;
  //       }
  //       p {
  //         flex: 1;
  //       }

  //       &:nth-child(4) {
  //         flex: 1;
  //       }
  //     }
  //     .times {
  //       width: 100%;
  //       .el-date-editor--daterange.el-input__inner {
  //         width: 100%;
  //       }
  //     }
  //   }
  //   .el-tab-pane {
  //     height: 100%;
  //     display: flex;
  //     flex-direction: column;
  //     overflow: auto;
  //   }

  //   .tableDiv {
  //     flex: 1;
  //     flex-shrink: 0;
  //     overflow: auto;
  //   }
  // }
  // .tabs_wrap {
  //   height: 100%;
  //   display: flex;
  //   flex-direction: column;

  //   /deep/.el-tabs__content {
  //     padding: 0;
  //     flex: 1;
  //     flex-shrink: 0;
  //     overflow: auto;
  //   }

  //   .el-tab-pane {
  //     height: 100%;
  //     display: flex;
  //     flex-direction: column;
  //     overflow: auto;
  //   }

  //   .table_wrap {
  //     flex: 1;
  //     overflow: auto;
  //   }
  // }

  .rightBody {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
    // background: #fff;
    border-radius: 4px;
    .rightTop {
      flex: 1;
      display: flex;
      flex-direction: column;
      flex-shrink: 0;
      overflow: auto;
      .btnHeader {
        display: flex;
        justify-content: space-between;
        // line-height: 42px;
        font-family: PingFangSC-Medium;
        font-size: 18px;
        color: #2d3436;
        // padding-left: 20px;
        padding: 18px 18px 0 18px;
        font-weight: 600;
      }
      .typeHeaders {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
      }
      .typeHeader {
        background: rgba(250, 182, 59, 0.2);
        display: flex;
        // justify-content: space-between;
        align-items: center;
        font-size: 14px;
        border-radius: 4px;
        margin-bottom: 10px;
        padding: 10px;
        .every_inp {
          display: flex;
          align-items: center;
          margin-right: 10px;
          flex: 1;
          label {
            margin-right: 10px;
            font-weight: 600;
            min-width: 50px;
          }

          p {
            flex: 1;
          }

          //   &:nth-child(4) {
          //     flex: 1;
          //   }
        }
        // .lowWidth {
        //   flex: 12%;
        // }
        .companyWidth {
          flex: 44%;
        }
        .header-info {
          display: flex;
          margin-left: 10px;
          flex: 1;
        }
        .typeHeader-left {
          display: flex;
          justify-content: space-between;
          flex: 1;
        }
      }
      .header-title {
        color: #1770df;
        font-weight: 600;
        font-size: 18px;
      }
      .rightcont {
        flex: 1;
        background: #fff;
        display: flex;
        flex-direction: column;
        padding: 0 10px;
        flex-shrink: 0;
        overflow: auto;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        .btnHeaderDiv {
          display: flex;
          justify-content: space-between;
          line-height: 52px;
          font-family: PingFangSC-Medium;
          font-size: 18px;
          color: #2d3436;
          height: 52px;
          .title {
            font-family: PingFangSC-Medium;
            font-size: 18px;
            color: #1770df;
            font-weight: 600;
            overflow: hidden; /*超出隐藏*/
            text-overflow: ellipsis; /*隐藏后添加省略号*/
            white-space: nowrap; /*强制不换行*/
          }
        }

        .contDiv {
          flex: 1;
          background: #fff;
          display: flex;
          flex-direction: row;
          flex-shrink: 0;
          overflow: auto;
          .contLeft {
            flex: 1;
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
            border: 1px solid #eee;
            border-radius: 4px;
            overflow: auto;
            .typeInfo {
              height: 52px;
              display: flex;
              flex-direction: row;
              line-height: 52px;
              padding: 0 18px;
              border-bottom: 1px solid #d8dee1;
              .every_inp {
                font-size: 14px;
                width: 25%;
                display: flex;
                align-items: center;
                /deep/.el-checkbox__input.is-checked .el-checkbox__inner {
                  background: #d63031;
                  border: 1px solid #d63031;
                  border-radius: 2px;
                  border-radius: 2px;
                }
                /deep/.el-checkbox__input.is-checked + .el-checkbox__label {
                  font-family: PingFangSC-Regular;
                  font-size: 14px;
                  color: #d63031;
                }
                /deep/.el-checkbox__input.is-disabled.is-checked
                  .el-checkbox__inner::after {
                  border-color: #fff;
                }
                /deep/.el-checkbox__inner::after {
                  height: 10px;
                  left: 8px;
                  position: absolute;
                  top: 2px;
                }
                & > label {
                  font-family: PingFangSC-Medium;
                  color: #000;
                  text-align: right;
                  margin-right: 10px;
                  font-weight: 600;
                }
                & > p {
                  font-family: PingFangSC-Regular;
                  font-size: 14px;
                  color: #2d3436;
                }
                &:nth-child(1) {
                  width: 12%;
                  & > label {
                    margin-right: 10px;
                  }

                  /deep/.el-checkbox__inner {
                    width: 21px;
                    height: 21px;
                  }
                }
                &:nth-child(2) {
                  width: 23%;
                  & > label {
                    width: 56px;
                  }
                }
                &:nth-child(3) {
                  width: 35%;
                  & > label {
                    width: 110px;
                  }
                }
                &:nth-child(4) {
                  width: 30%;
                  margin-left: 10px;
                  & > label {
                    width: 150px;
                  }
                }
              }
            }
            .rightTableDiv {
              flex: 1;
              flex-shrink: 0;
              overflow: auto;
              display: flex;
              flex-direction: column;
            }
          }
          .contRight {
            width: 220px;
            height: 100%;
            overflow: auto;
            border: 1px solid #eee;
            border-radius: 4px;
          }
        }
      }
    }
    .rightBom {
      height: 150px;
      padding: 10px;
      background: #fff;
      border-bottom-left-radius: 4px;
      border-bottom-right-radius: 4px;
    }
    .nodulus {
      border: 1px solid #eee;
      height: 100px;
      overflow: auto;
      border-radius: 4px;
    }
  }
  /deep/.el-date-editor.el-input,
  /deep/.el-date-editor.el-input__inner {
    width: 100%;
  }
  /deep/.el-drawer__header {
    align-items: center;
    display: flex;
    background: rgba(23, 112, 223, 0.2);
    border-radius: 4px 4px 0 0;
    line-height: 42px;
    font-family: PingFangSC-Medium;
    font-size: 18px;
    padding: 0;
    padding-left: 20px;
    color: #2d3436;
    margin-bottom: 0;
  }
  /deep/.el-drawer__close-btn {
    font-size: 30px;
  }
  /deep/.el-dialog__header {
    height: 50px;
    line-height: 50px;
    background: rgba(23, 112, 223, 0.2);
    padding: 0 18px;
  }
  /deep/.el-dialog__headerbtn {
    top: 10px;
    font-size: 30px;
  }
  /deep/.el-dialog__body {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 10px 18px 18px 18px;
    flex-shrink: 0;
    overflow: auto;
  }
  .btn {
    padding: 6.5px 10px;
  }
  .phone_btn {
    text-align: right;
    margin-bottom: 10px;
  }
  .searchIpt {
    width: 140px;
  }
  /deep/.el-dialog__header {
    height: 50px;
    line-height: 50px;
    background: rgba(23, 112, 223, 0.2);
    padding: 0 18px;
  }
  /deep/.el-dialog__headerbtn {
    top: 10px;
    font-size: 30px;
  }
  /deep/.el-dialog__body {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 10px 18px 18px 18px;
    flex-shrink: 0;
    overflow: auto;
    height: calc(100% - 50px);
  }
  .replyTable {
    height: calc(100% - 50px);
    border: 1px solid rgba(178, 190, 195, 0.5);
    border-radius: 4px;
  }
  .replyTitle {
    font-size: 18px;
    color: #2d3436;
    margin-top: 8px;
    margin-bottom: 18px;
  }
  .replyText {
    color: #1770df;
    cursor: pointer;
  }
  .reply-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
  }
}
/deep/ .el-table__cell {
  vertical-align: baseline;
}
/deep/.el-table th.el-table__cell > .cell {
  padding-left: 3px;
  padding-right: 3px;
}
</style>
<style lang="less">
.container {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/deep/.hisDialogClass {
  height: 800px !important;
  display: flex;
  flex-direction: column;
  padding: 15px;
}
.popverClass {
  height: calc(100% - 200px);
  .leftBody {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .group_head {
    li {
      display: flex;
      margin-bottom: 12px;
      &:last-child {
        margin-bottom: 0;
      }
      .marginDiv {
        margin-left: 15px;
      }
    }

    .every_inp {
      display: flex;
      align-items: center;
      margin-right: 10px;
      width: 100%;
      font-family: PingFangSC-Medium;
      font-size: 14px;
      color: #2d3436;
      label {
        width: 70px;
      }
      .btnLabel {
        margin-left: 10px;
        margin-right: 0;
      }
      p {
        flex: 1;
      }

      &:nth-child(4) {
        flex: 1;
      }
    }
    .times {
      width: 100%;
      .el-date-editor--daterange.el-input__inner {
        width: 100%;
      }
    }
  }

  .el-tab-pane {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: auto;
  }

  .tableDiv {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
  }
  .tabs_wrap {
    height: 100%;
    display: flex;
    flex-direction: column;

    .el-tabs__content {
      padding: 0;
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
    }

    .el-tab-pane {
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: auto;
    }

    .table_wrap {
      flex: 1;
      overflow: auto;
    }
  }
}
.consultationReply {
  height: 700px;
  overflow: auto;
}
.reply-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}
.receiveResult_content {
  header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
  }
  .result_list {
    height: 500px;
  }
}
</style>
