export default {
  data() {
    return {
      consultationReply: false,
      replyData: [],
      timerName: '',
      theads: {
        regNo: '体检号',
        // combCode: "组合代码",
        combName: '组合名称',
        // sex: "发起人",
        // name: "姓名",
        questionerName: '咨询医生',
        questionContent: '咨询内容',
        questionTime: '咨询时间',
        replyerName: '回复医生',
        replyContent: '回复内容',
        replyTime: '回复时间'
        // unlockTime: "解锁时间"
      }
    };
  },
  methods: {
    // 获取医生本人的待答复列表
    GetPeQuestionToDoctors() {
      this.$ajax
        .post(this.$apiUrls.GetPeQuestionToDoctors, '', {
          query: { replyerName: this.G_userInfo.codeOper.name }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          returnData?.map((item) => {
            item.backoutPopoverShow = false;
          });
          this.replyData = returnData || [];
          if (returnData.length == 0) {
            this.consultationReply = false;
          }
          if (returnData.length > 0) {
            this.consultationReply = true;
          }
        });
    },
    // 回复提交
    replySubmit(row) {
      if (!row.replyContent) {
        this.$message({
          message: '请输入回复内容',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let data = [row];
      this.$ajax.post(this.$apiUrls.ReplyQuestion, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.$message({
          message: '回复发送成功!',
          type: 'success',
          showClose: true
        });
        row.backoutPopoverShow = false;
        this.GetPeQuestionToDoctors();
      });
    }
  },
  created() {
    this.GetPeQuestionToDoctors();
    this.timerName = setInterval(this.GetPeQuestionToDoctors, 60000);
  },
  // 组件激活
  activated() {
    if (this.timerName) return;
    this.timerName = setInterval(this.GetPeQuestionToDoctors, 60000);
  },
  // 组件失活
  deactivated() {
    clearInterval(this.timerName);
    this.timerName = '';
  },
  // 销毁
  destroyed() {
    clearInterval(this.timerName);
    this.timerName = '';
  }
};
