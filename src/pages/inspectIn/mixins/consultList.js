export default {
  data() {
    return {
      consultDialog: false,
      conclusionList: [],
      isHasFollowUp: false //是否已随访
    };
  },
  methods: {
    // 咨询列表按钮
    consultList() {
      this.consultDialog = true;
      this.getQuestionList();
    },
    consultDialogClose() {
      this.consultDialog = false;
    },
    // 获取咨询列表
    getQuestionList() {
      this.$ajax
        .post(this.$apiUrls.ReadQuestionListByCheck, '', {
          query: { questionerCode: this.G_userInfo.codeOper.operatorCode }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.conclusionList = returnData || [];
        });
    },
    // 获取随访状态
    getFollowUpState() {
      let datas = {
        regNo: this.headerInfo.regNo
      };
      this.$ajax.paramsPost(this.$apiUrls.HasFollowUp, datas).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.isHasFollowUp = returnData;
      });
    },
    // 跳转随访登记页面
    followUpRecodeClick() {
      this.$router.push({
        name: 'followUpReg',
        query: {
          regNo: this.headerInfo.regNo
        }
      });
    }
  }
};
