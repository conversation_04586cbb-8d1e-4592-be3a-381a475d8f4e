import { storage } from '@/common';
import WS from '@/common/websocket';
export default {
  data() {
    return {
      totalPages: 0,
      time: 300,
      currentNum: 0
    };
  },
  methods: {
    print() {
      if (this.printSetInfo.barcodeName == '') {
        this.$message({
          message: '请先选择打印机！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.selectionData.length == 0) {
        this.$message({
          message: '请选择需要打印的条码',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.currentNum = 0;
      this.maskShow = true;
      this.batchPrint(this.selectionData);
    },
    batchPrint(list) {
      console.log(list);
      // this.message = `正在打印...`;
      // this.percentage = Math.floor((this.currentNum / list.length).toFixed(2) * 100);
      console.log(this.currentNum);
      if (this.currentNum >= list.length) {
        setTimeout(() => {
          this.$message({
            message: '打印完成',
            type: 'success',
            showClose: true
          });
          this.maskShow = false;
          this.searchClick();
        }, this.time);
        return;
      }
      this.batchGetPDF(list[this.currentNum]).then((r) => {
        setTimeout(() => {
          this.currentNum += 1;
          this.batchPrint(list);
        }, this.time);
      });
    },
    batchGetPDF(dataInfo) {
      console.log(dataInfo);
      return new Promise((resolve, reject) => {
        let datas = {
          reportCode: '30232e1288c544aba755930cbd7a6029',
          queryString:
            'regNo=' + dataInfo.regNo + '&type=0&sampleNo=' + dataInfo.sampleNo
        };
        console.log(datas);
        this.$ajax
          .post(`${this.$config.pdfFileUrl}Home/ExportToPdf`, datas, {
            responseType: 'arraybuffer'
          })
          .then((r) => {
            this.PrintSample(dataInfo);
            if (this.collectFlag && dataInfo.status < 3) {
              this.GatherSample(dataInfo);
            }
            // let blob = new Blob([r.data],{
            //     type:'application/pdf'
            // })
            // console.log(blob);
            // this.pdfSrc = URL.createObjectURL(blob);
            const blob = new Blob([r.data], {
              type: 'application/pdf'
            });
            let reader = new FileReader();

            reader.onload = (e) => {
              console.log(e);
              let data;
              if (typeof e.target.result === 'object') {
                data = window.URL.createObjectURL(e.target.result);
              } else {
                data = e.target.result;
              }
              // this.pdfSrc = data;
              // this.fileStream = data;
              this.printStart(data);
            };
            //转化为base64
            reader.readAsDataURL(blob);

            let file = new File([r.data], '', {
              type: 'application/pdf'
            });
            let fileReader = new FileReader();
            fileReader.readAsBinaryString(file);
            fileReader.onloadend = (e) => {
              console.log(fileReader);
              var count = fileReader.result.match(
                /\/Type[\s]*\/Page[^s]/g
              ).length;
              console.log('Number of Pages:', count);
              this.totalPages = count;
              resolve(this.totalPages);
            };
          });
      });
    },
    // 批打印开始
    printStart(fileStream) {
      if (
        (this.$w_print &&
          this.$w_print?.websock?.readyState === WebSocket.CLOSED) ||
        (this.$w_print && !this.$w_print?.websock)
      ) {
        this.$w_print.closeWebSocket();
        Vue.prototype.$w_print = new WS(`${this.$config.printUrl}printer`);
      }
      let fileStreams = fileStream.split(',')[1];
      // return
      // console.log(this.fileStream);
      let obj2 = {
        PrinterName: this.printSetInfo.barcodeName,
        PrintTimes: 1,
        FileBase64: fileStreams,
        FileFormat: 'pdf'
      };
      let obj = {
        Action: 'print',
        Data: JSON.stringify(obj2)
      };
      var data = JSON.stringify(obj);
      console.log(data);
      // let T = "{'Action':'getPrinterList','Data':''}";
      this.$w_print.sendSock(data);
    },
    // 更新打印的状态
    PrintSample(row) {
      this.$ajax
        .paramsPost(this.$apiUrls.PrintSample, { sampleNo: row.sampleNo })
        .then((r) => {});
    },
    // 更新采集的状态
    GatherSample(row) {
      this.$ajax
        .paramsPost(this.$apiUrls.GatherSample, { sampleNo: row.sampleNo })
        .then((r) => {});
    }
  }
};
