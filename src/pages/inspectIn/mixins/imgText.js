export default {
  data() {
    return {
      ImgTextShow: false,
      ImgTextList: [],
      checkComb: {},
      innerDrawer: false
    };
  },
  methods: {
    // 获取图文报告列表
    getImgTextList(regNo) {
      this.$ajax
        .paramsPost(this.$apiUrls.GetAllRecordImage, { regNo })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) {
            return;
          }
          this.ImgTextList = returnData || [];
        });
    },
    // 图文报告的点击回调
    imgTextClick() {
      this.ImgTextShow = true;
    },
    // 上传的点击回调
    uploadBtnClick() {
      this.innerDrawer = true;
    }
  }
};
