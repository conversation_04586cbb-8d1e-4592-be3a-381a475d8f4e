// 危急通知
export default {
  data() {
    return {};
  },
  methods: {
    // 是否发送危急通知
    isSendNotice(obj, regNo) {
      console.log(obj);
      let abnormal = []; //重大异常
      let urgent = []; //危急
      obj.map((item) => {
        if (item.abnormalType == 2) {
          abnormal.push({
            itemCode: item.itemCode,
            itemName: item.itemName,
            itemTags: item.itemTags,
            lowerLimit: item.lowerLimit,
            upperLimit: item.upperLimit
          });
        }
        if (item.abnormalType == 3) {
          urgent.push({
            itemCode: item.itemCode,
            itemName: item.itemName,
            itemTags: item.itemTags,
            lowerLimit: item.lowerLimit,
            upperLimit: item.upperLimit
          });
        }
      });
      console.log(abnormal, urgent);
      if (abnormal.length != 0) {
        this.sendNoticeFun(2, regNo, abnormal, '重大异常');
      }
      if (urgent.length != 0) {
        this.sendNoticeFun(1, regNo, urgent, '危急').then((r) => {
          if (abnormal.length != 0) {
            this.sendNoticeFun(2, regNo, abnormal, '重大异常');
          }
        });
      }
    },
    sendNoticeFun(type, regNo, items, txt) {
      return new Promise((resolve, reject) => {
        this.$confirm(`是否发送${txt}通知?`, `${txt}提示`, {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            let datas = {
              regNo: regNo,
              type: type,
              confirmer: this.G_userInfo.codeOper.operatorCode,
              items: items
            };
            console.log(datas);
            resolve();
            this.$ajax
              .post(this.$apiUrls.ConfirmCriticalValue, datas)
              .then((r) => {});
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });
      });
    }
  }
};
