export default {
  data() {
    return {
      ImageLegengShow: false,
      isHaveImgListFlag: false
    };
  },
  methods: {
    // 影像图例的点击回调
    imageLegengBtnClick() {
      if (!this.headerInfo.regNo) {
        this.$message({
          message: '请先选择体检人!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.ImageLegengShow = true;
    },
    // 查询是否有影响图例
    getImgList() {
      this.$ajax
        .get(this.$apiUrls.RecordImages + `/${this.headerInfo.regNo}`)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          if (returnData) {
            this.isHaveImgListFlag = true;
          }
        });
    }
  }
};
