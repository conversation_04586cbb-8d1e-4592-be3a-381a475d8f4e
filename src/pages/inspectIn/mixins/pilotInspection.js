import { Promise } from 'core-js';

export default {
  data() {
    return {
      callName: '', //叫号
      nextStation: '', //下一站
      macCode: '', //mac码
      loadingCall: false,
      loadingDone: false,
      loadingBack: false,
      wsMsg: '获取mac码失败,请检查导检程序是否处于运行状态！'
    };
  },
  mounted() {},
  methods: {
    //ws获取macCode
    wsGetMacCode() {
      if (!this.macCode) {
        let _this = this;
        return new Promise((resolve, reject) => {
          const ws = new WebSocket(_this.$config.wsGetMacCodeUrl);
          let closed = false; // 添加标志位控制关闭逻辑
          ws.onclose = function () {
            // ws = new WebSocket(this.$config.previewUrl);
            if (!closed) {
              // 避免重复操作
              _this.macCode = '';
              //console.log('[ _this.macCode ]-33', _this.macCode);
              closed = true;
              _this.loadingCall = false;
              _this.loadingDone = false;
              _this.loadingBack = false;
            }
            //ws.close(); // 确保WebSocket被关闭
          };

          ws.onerror = (e) => {
            //console.error(e);
            _this.$message({
              message: _this.wsMsg,
              type: 'warning',
              showClose: true
            });
            //清空
            _this.macCode = '';
            _this.callName = '';
            _this.nextStation = '';
            _this.loadingCall = false;
            _this.loadingDone = false;
            _this.loadingBack = false;
          };

          const openHandler = () => {
            ws.send('MacCode');
            ws.onmessage = (e) => {
              const macCode = e.data;
              //console.log('[ macCode ]-43', macCode);
              _this.macCode = macCode;
              resolve();
            };
          };
          ws.onopen = openHandler;
        });
      }
    },
    // 请求叫号，添加错误处理
    callFun() {
      if (!this.macCode) {
        this.$message({
          message: this.wsMsg,
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$ajax
        .get(`${this.$apiUrls.Call}/${this.macCode}`)
        .then((r) => {
          this.loadingCall = false;
          const { success, returnMsg, returnData } = r.data;
          if (!success) {
            this.loadingCall = false;
            return;
          }
          this.callName = returnData;
          this.nextStation = '';
          this.$message({
            message: returnMsg,
            type: 'success',
            showClose: true
          });
        })
        .catch((error) => {
          this.loadingCall = false;
          console.error('API请求错误:', error);
        });
    },

    // 叫号
    async call() {
      try {
        this.loadingCall = true;
        await this.wsGetMacCode(); // 确保macCode被正确获取
        this.callFun(); // 使用macCode进行叫号
      } catch (error) {
        this.loadingCall = false;
        console.error('调用出错:', error);
      }
    },
    //请求完成函数
    doneFun() {
      if (!this.macCode) {
        this.$message({
          message: this.wsMsg,
          type: 'warning',
          showClose: true
        });
        return;
      }
      let data = {
        macCode: this.macCode,
        userCode: this.G_userInfo.codeOper.operatorCode,
        regNo: this.headerInfo.regNo
      };
      this.$ajax
        .post(this.$apiUrls.Done, data)
        .then((r) => {
          this.loadingDone = false;
          let { success, returnMsg, returnData } = r.data;
          if (!success) {
            this.loadingDone = false;
            return;
          }
          this.nextStation = returnData;
          this.callName = '';
          this.$message({
            message: returnMsg,
            type: 'success',
            showClose: true
          });
        })
        .catch((error) => {
          this.loadingDone = false;
          console.error('API请求错误:', error);
        });
    },
    //执行完成
    async doFinish() {
      try {
        this.loadingDone = true;
        await this.wsGetMacCode(); // 确保macCode被正确获取
        this.doneFun(); // 使用macCode进行完成
      } catch (error) {
        this.loadingDone = false;
        console.error('调用出错:', error);
      }
    },
    //点击完成
    done() {
      if (!this.headerInfo.regNo) {
        this.$message({
          message: '当前没有可完成的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      const hintMsg = `请确认是否完成${this.headerInfo.name}的检查？`;
      this.$confirm(hintMsg, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      })
        .then(() => {
          this.loadingDone = true;
          this.doFinish();
        })
        .catch(() => {
          this.loadingDone = false;
        });
    },
    //请求置后函数
    backFun() {
      if (!this.macCode) {
        this.$message({
          message: this.wsMsg,
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$ajax
        .get(`${this.$apiUrls.Back}/${this.macCode}`)
        .then((r) => {
          this.loadingBack = false;
          let { success, returnMsg } = r.data;
          if (!success) {
            this.loadingBack = false;
            return;
          }
          this.callName = '';
          this.nextStation = '';
          this.$message({
            message: returnMsg,
            type: 'success',
            showClose: true
          });
        })
        .catch((error) => {
          this.loadingBack = false;
          console.error('API请求错误:', error);
        });
    },
    //点击置后
    async back() {
      try {
        this.loadingBack = true;
        await this.wsGetMacCode(); // 确保macCode被正确获取
        this.backFun(); // 使用macCode进行置后
      } catch (error) {
        this.loadingBack = false;
        console.error('调用出错:', error);
      }
    }
  }
};
