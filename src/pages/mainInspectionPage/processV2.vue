<!--
 * @FilePath: \shenshan\KrPeis\src\pages\mainInspectionPage\processV2.vue
 * @Description:  审核页面
 * @Author: 
 * @Date: 2024-03-19 17:23:58
 * @Version: 0.0.1
 * @LastEditors: key
 * @LastEditTime: 2025-04-17 10:31:54
*
-->
<template>
  <div
    class="process mainInspection_media"
    @mouseover="anchorPointEnter(false)"
  >
    <!-- 锚点显示的按钮 -->
    <div
      class="anchorPoint_btn"
      @mousemove="anchorPointEnter(true)"
      @mouseover.stop
    ></div>
    <div class="topBtn">
      <div class="leftTop">
        <el-popover
          placement="bottom-start"
          width="100%"
          trigger="click"
          ref="popoverRef"
          popper-class="popverClass"
          @show="getPatientList"
        >
          <el-tabs v-model="activeMain3" @tab-click="checkboxChange">
            <el-tab-pane label="待审核" name="main2">
              <NoDistribution
                :viewTableList="viewTableList2"
                :queryTimeType.sync="noDistributionQueryTimeType2"
                :loading="noDistributionLoading2"
                @getReportConclution="getReportConclution"
                @filterCls="filterCls"
                @dateChange="getPatientList2"
                @queryTimeTypeChange="getPatientList2"
                v-model="dateVal2"
                :occupationalParameters="true"
                :harmList="harmList"
                :jobStatus.sync="jobStatus2"
                :hazardousCodes.sync="hazardousCodes2"
              ></NoDistribution>
            </el-tab-pane>
            <el-tab-pane label="已审核" name="main3">
              <NoDistribution
                :viewTableList="viewTableList3"
                :queryTimeType.sync="noDistributionQueryTimeType3"
                :loading="noDistributionLoading3"
                @getReportConclution="getReportConclution"
                @filterCls="filterCls"
                @dateChange="getPatientList3"
                @queryTimeTypeChange="getPatientList3"
                v-model="dateVal3"
                :occupationalParameters="true"
                :harmList="harmList"
                :jobStatus.sync="jobStatus3"
                :hazardousCodes.sync="hazardousCodes3"
              ></NoDistribution>
            </el-tab-pane>
          </el-tabs>
          <MainBtnCom :btnList="['审核列表']" slot="reference" />
        </el-popover>
        <el-popover
          placement="bottom-start"
          trigger="click"
          ref="popoverRef2"
          class="projectResult_btn"
          popper-class="popverClass projectResult_popover"
        >
          <div class="con_wrap">
            <div class="projectResult_header">
              项目结果
              <p>
                <el-popover
                  placement="right-start"
                  title="快速定位"
                  trigger="click"
                  popper-class="anchor_popover"
                >
                  <!-- 锚点列表 -->
                  <div class="projectNav_wrap">
                    <a
                      v-for="item in projectResult"
                      :key="item.combCode"
                      @click="anchorPointClick('#nav_' + item.combCode)"
                      >{{ item.combName }}</a
                    >
                  </div>
                  <el-button size="mini" class="green_btn btn" slot="reference"
                    >定位</el-button
                  >
                </el-popover>
                <el-button
                  size="mini"
                  class="green_btn btn"
                  v-if="ImgTextList.length != 0"
                  @click="imgTextClick"
                  >图文报告</el-button
                >
              </p>
            </div>
            <div class="bottom_wrap">
              <el-collapse
                v-model="activeNames"
                @change="handleChange"
                size="small"
              >
                <el-collapse-item
                  :title="item.combName"
                  :name="item.combCode"
                  v-for="item in projectResult"
                  :key="item.combCode"
                  :id="'nav_' + item.combCode"
                >
                  <template slot="title">
                    <div class="collapse_div">
                      <span>
                        {{ item.combName }}
                        <img
                          title="引用项目"
                          src="@/assets/img/yinyong.png"
                          v-if="item.isQuote"
                          alt=""
                        />
                      </span>
                      <div style="display: flex; align-items: center">
                        <span class="operNameClass"
                          >体检医生:{{
                            item.doctorName ? item.doctorName : '无'
                          }}</span
                        >
                      </div>
                    </div>
                  </template>
                  <!-- 项目列表 -->
                  <el-table
                    size="small"
                    :data="item.projects"
                    style="width: 100%; font-size: 14px"
                    :header-cell-style="headerCellStyle"
                  >
                    <el-table-column
                      prop="itemName"
                      label="项目名称"
                      width="180"
                    >
                      <template #default="scope">
                        <div :class="{ redClass: scope.row.abnormalType > 0 }">
                          {{ scope.row.itemName }}
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="address" label="结果">
                      <template #default="scope">
                        <div class="result_div">
                          <el-popover
                            placement="right"
                            popper-class="my_popover"
                            width="500"
                            trigger="hover"
                            @hide="onPopoverHide"
                          >
                            <ul class="popover_ul">
                              <li>
                                <!-- <label for="">参考值：</label> -->
                                <span>{{ C_result(scope.row.results) }}</span>
                              </li>
                              <!-- <li>
                                  <label for="">异常程度：</label>
                                  <span>{{scope.row.hint}}</span>
                                </li> -->
                            </ul>
                            <div
                              slot="reference"
                              class="popover_div"
                              :class="
                                scope.row.isError ? 'redClass' : 'defaultClass'
                              "
                            >
                              <i class="popover_i">{{ scope.row.hint }}</i
                              >{{ C_result(scope.row.results) }}
                            </div>
                          </el-popover>
                          <!-- <span class="result_span" v-for="tag in scope.row.results" :key="tag.index">{{
                              tag.itemTag
                            }}</span> -->
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="参考值"
                      v-if="item.isShowReferenceRange"
                    >
                      <template #default="scope">
                        <span
                          :class="{ redClass: scope.row.abnormalType > 0 }"
                          >{{ disposeFKVal(scope.row) }}</span
                        >
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="unit"
                      label="单位"
                      v-if="item.isShowUnits"
                    >
                      <template #default="scope">
                        <span
                          :class="{ redClass: scope.row.abnormalType > 0 }"
                          >{{ scope.row.unit }}</span
                        >
                      </template>
                    </el-table-column>
                    <el-table-column prop="address" label="上次结果">
                      <template #default="scope">
                        <div class="result_div">
                          <el-popover
                            placement="right"
                            popper-class="my_popover"
                            width="500"
                            trigger="hover"
                            @hide="onPopoverHide"
                          >
                            <ul class="popover_ul">
                              <li>
                                <span>{{ scope.row.lastResult }}</span>
                              </li>
                            </ul>
                            <div
                              slot="reference"
                              class="popover_div"
                              :class="
                                scope.row.isError ? 'redClass' : 'defaultClass'
                              "
                            >
                              {{ scope.row.lastResult }}
                            </div>
                          </el-popover>
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                  <!-- 体检小结 -->
                  <div class="project_result">
                    <span>体检小结：</span>
                    <div>
                      <el-tag
                        size="small"
                        :class="
                          result.abnormalType > 0 ? 'tags_red' : 'tags_sty'
                        "
                        v-for="result in item.combinations"
                        :key="result.index"
                        >{{ result.combTag }}</el-tag
                      >
                    </div>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
          <el-button
            style="padding: 6.5px 10px; font-size: 14px"
            slot="reference"
            size="mini"
            >项目结果</el-button
          >
        </el-popover>
        <p>
          <el-input
            size="small"
            placeholder="体检号/姓名"
            v-model="keyword"
            clearable
            @keyup.enter.native="searchByKeyword"
          ></el-input>
        </p>
        <el-checkbox v-model="checked" @change="checkboxChange"
          >过滤本人主检</el-checkbox
        >
      </div>
      <MainBtnCom
        :btnList="['查询', '审核', '历史报告', '主检回复', '撤回主检']"
        @temporarySave="temporarySave"
        @examineClick="examineClick"
        @search="search"
        @historyReport="historyReport"
        @previewReport="previewReport"
        @mainInspectionReply="mainInspectionReply"
        @withdrawMainTest="withdrawMainTest"
        :isExamine="isExamine"
        :isMain="isMain"
        :auditLoading="auditLoading"
      >
        <template #footAdd>
          <el-button
            class="yellow_btn btn"
            size="small"
            icon="el-icon-headset"
            v-if="PureToneAudiometryBtnShow"
            @click="showPureToneAudiometry = true"
            >纯音测听</el-button
          >
          <el-button
            class="green_btn btn"
            size="small"
            icon="el-icon-tickets"
            :class="{ 'questionnaire-disabled': questionnaireDisabled }"
            @click="questionnaire"
            >问卷调查</el-button
          >
          <el-button size="small" class="green_btn btn" @click="followUpClick"
            >添加随访</el-button
          >
          <el-button
            size="small"
            class="green_btn btn"
            @click="followUpRecodeClick"
            v-if="isHasFollowUp"
            >随访记录</el-button
          >
          <div
            class="positive_btn"
            :class="{ is_positive: isPositiveFlag }"
            @click="PositiveResultShow"
          >
            <!-- <i class="el-icon-d-arrow-left"></i> -->
            重阳
          </div>
        </template>
      </MainBtnCom>
    </div>
    <div class="topTitle">
      <el-collapse class="collapse">
        <el-collapse-item>
          <template slot="title">
            <ul class="type_head">
              <li>
                <div class="every_inp regNo_inp">
                  <label>体检号: </label>
                  <p>{{ reportConclution.patient.regNo }}</p>
                </div>
                <div class="every_inp less_inp">
                  <label>姓名: </label>
                  <p
                    class="text-overflow-hidden"
                    :title="reportConclution.patient.name"
                  >
                    {{ reportConclution.patient.name }}
                  </p>
                </div>
                <div class="every_inp less_inp">
                  <label>性别: </label>
                  <p>{{ G_EnumList['Sex'][reportConclution.patient.sex] }}</p>
                </div>
                <div class="every_inp less_inp">
                  <label>年龄: </label>
                  <p>
                    {{
                      reportConclution.patient.ageUnit
                        ? reportConclution.patient.age +
                          G_EnumList['AgeUnit'][
                            reportConclution.patient.ageUnit
                          ]
                        : reportConclution.patient.age
                    }}
                  </p>
                </div>
                <div class="every_inp flex_inp">
                  <label>单位: </label>
                  <p
                    :title="reportConclution.patient.companyName"
                    style="overflow-y: auto"
                    class="text-overflow-hidden"
                  >
                    {{ reportConclution.patient.companyName }}
                  </p>
                </div>
                <div class="every_inp flex_inp">
                  <label>工龄: </label>
                  <p
                    :title="reportConclution.patient.totalYearsOfWork"
                    style="overflow-y: auto"
                    class="text-overflow-hidden"
                  >
                    {{ reportConclution.patient.totalYearsOfWork }}
                  </p>
                </div>
                <div class="every_inp flex_inp">
                  <label>在岗状态: </label>
                  <p
                    :title="reportConclution.patient.jobstatusName"
                    style="overflow-y: auto"
                    class="text-overflow-hidden"
                  >
                    {{ reportConclution.patient.jobstatusName }}
                  </p>
                </div>
                <div class="every_inp cont_inp">
                  <label>危害因素: </label>
                  <p
                    :title="reportConclution.patient.hazardousFators"
                    style="
                      width: 100%;
                      overflow: hidden;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                    "
                  >
                    {{ reportConclution.patient.hazardousFators }}
                  </p>
                </div>
              </li>
            </ul>
          </template>
          <div>
            <ul class="type_head">
              <li>
                <div class="every_inp regNo_inp">
                  <label>联系电话: </label>
                  <p>{{ reportConclution.patient.tel }}</p>
                </div>
                <div class="every_inp num_inp">
                  <label>身份证: </label>
                  <p>{{ reportConclution.patient.cardNo }}</p>
                </div>
                <div class="every_inp less_inp">
                  <label>婚姻状况: </label>
                  <p>
                    {{
                      G_EnumList['MarryStatus'][
                        reportConclution.patient.marryStatus
                      ]
                    }}
                  </p>
                </div>
                <div class="every_inp cont_inp">
                  <label>联系地址: </label>
                  <p
                    :title="reportConclution.patient.address"
                    style="
                      width: 100%;
                      overflow: hidden;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                    "
                  >
                    {{ reportConclution.patient.address }}
                  </p>
                </div>
                <div class="every_inp flex_inp">
                  <label>体检类型: </label>
                  <p>
                    {{ G_EnumList['PeCls'][reportConclution.patient.peCls] }}
                  </p>
                </div>
              </li>
            </ul>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
    <div class="centerCont">
      <div
        class="con_wrap left_wrap"
        ref="project_Ref"
        :class="{ left_wrap_fold: foldFlag }"
      >
        <header>
          <p v-if="!foldFlag">项目结果：</p>
          <div class="head_operation">
            <el-button
              v-if="!foldFlag && ImgTextList.length != 0"
              size="mini"
              class="green_btn btn"
              @click="imgTextClick"
              >图文报告</el-button
            >
            <el-dropdown placement="bottom" v-if="!foldFlag">
              <span class="el-dropdown-link">
                <i class="el-icon-tickets"></i> 目录
              </span>
              <el-dropdown-menu slot="dropdown" class="catalogue_dropdown">
                <el-dropdown-item
                  v-for="item in projectResult"
                  :key="item.combCode"
                  @click.native="anchorPointClick('#nav_' + item.combCode)"
                >
                  {{ item.combName }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <span @click="foldClick">
              <i :class="foldFlag ? 'el-icon-s-unfold' : 'el-icon-s-fold'"></i>
              收起
            </span>
          </div>
        </header>
        <!-- <p>
          项目结果
          <el-button
            size="mini"
            class="green_btn btn"
            @click="imgTextClick"
            v-if="ImgTextList.length != 0"
            >图文报告</el-button
          >
        </p> -->
        <!-- 收起的显示 -->
        <div class="fold_div" v-if="foldFlag">
          <div>项目结果</div>
        </div>
        <div class="bottom_wrap" v-if="!foldFlag">
          <!-- 锚点列表 -->
          <!-- <div
            class="projectNav_wrap"
            :class="anchorPointShow ? 'projectNav_wrap_active' : ''"
            @mouseover.stop
          >
            <a
              v-for="item in projectResult"
              :key="item.combCode"
              @click="anchorPointClick('#nav_' + item.combCode)"
              >{{ item.combName }}</a
            >
          </div> -->
          <el-collapse
            v-model="activeNames"
            @change="handleChange"
            size="small"
          >
            <el-collapse-item
              :title="item.combName"
              :name="item.combCode"
              v-for="item in projectResult"
              :key="item.combCode"
              :id="'nav_' + item.combCode"
            >
              <template slot="title">
                <div class="collapse_div">
                  <span>
                    {{ item.combName }}
                    <img
                      title="引用项目"
                      src="@/assets/img/yinyong.png"
                      v-if="item.isQuote"
                      alt=""
                    />
                  </span>
                  <div style="display: flex; align-items: center">
                    <span class="operNameClass"
                      >体检医生:{{
                        item.doctorName ? item.doctorName : '无'
                      }}</span
                    >
                  </div>
                </div>
              </template>
              <!-- 项目列表 -->
              <el-table
                size="small"
                :data="item.projects"
                style="width: 100%; font-size: 14px"
                :header-cell-style="headerCellStyle"
              >
                <el-table-column prop="itemName" label="项目名称" width="180">
                </el-table-column>
                <el-table-column prop="address" label="结果">
                  <template #default="scope">
                    <div class="result_div">
                      <el-popover
                        placement="right"
                        popper-class="my_popover"
                        width="500"
                        trigger="hover"
                        @hide="onPopoverHide"
                      >
                        <ul class="popover_ul">
                          <li>
                            <!-- <label for="">参考值：</label> -->
                            <span>{{ C_result(scope.row.results) }}</span>
                          </li>
                          <!-- <li>
                            <label for="">异常程度：</label>
                            <span>{{scope.row.hint}}</span>
                          </li> -->
                        </ul>
                        <div
                          slot="reference"
                          class="popover_div"
                          :class="
                            scope.row.isError ? 'redClass' : 'defaultClass'
                          "
                        >
                          <i class="popover_i">{{ scope.row.hint }}</i
                          >{{ C_result(scope.row.results) }}
                        </div>
                      </el-popover>
                      <!-- <span class="result_span" v-for="tag in scope.row.results" :key="tag.index">{{
                        tag.itemTag
                      }}</span> -->
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  label="参考值"
                  v-if="item.isShowReferenceRange"
                >
                  <template #default="scope">
                    <span :class="{ redClass: scope.row.abnormalType > 0 }">{{
                      disposeFKVal(scope.row)
                    }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="unit"
                  label="单位"
                  v-if="item.isShowUnits"
                >
                  <template #default="scope">
                    <span :class="{ redClass: scope.row.abnormalType > 0 }">{{
                      scope.row.unit
                    }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="address" label="上次结果">
                  <template #default="scope">
                    <div class="result_div">
                      <el-popover
                        placement="right"
                        popper-class="my_popover"
                        width="500"
                        trigger="hover"
                        @hide="onPopoverHide"
                      >
                        <ul class="popover_ul">
                          <li>
                            <span>{{ scope.row.lastResult }}</span>
                          </li>
                        </ul>
                        <div
                          slot="reference"
                          class="popover_div"
                          :class="
                            scope.row.isError ? 'redClass' : 'defaultClass'
                          "
                        >
                          {{ scope.row.lastResult }}
                        </div>
                      </el-popover>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <!-- 体检小结 -->
              <div class="project_result">
                <span>体检小结：</span>
                <div>
                  <el-tag
                    size="small"
                    :class="result.abnormalType > 0 ? 'tags_red' : 'tags_sty'"
                    v-for="result in item.combinations"
                    :key="result.index"
                    >{{ result.combTag }}</el-tag
                  >
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
      <div class="right_wrap">
        <!-- 综述和建议 -->
        <!-- <Summary
          ref="summaryCom_Ref"
          :isSummary="!isSummary"
          :patientInfo.sync="reportConclution.patient"
          mainFlag
          :isMain="isMain"
          :isExamine="isExamine"
          isAuditPage
          :recordCombItems="projectResult"
        /> -->
        <Summary
          ref="summaryCom_Ref"
          :isSummary="!isSummary"
          :patientInfo.sync="reportConclution.patient"
          mainFlag
          :isMain="isMain"
          :isExamine="isExamine"
          :recordCombItems="projectResult"
          v-model="harmSummary"
          :reportConclusionHazards.sync="
            reportConclution.reportConclusionHazards
          "
          :reportConclution="reportConclution"
          @reviewShow="reviewBtnShow"
        />
        <!-- 结论 -->
        <div class="conclusion_wrap" v-if="false">
          <div class="footerCont">
            <div class="footer1">
              <label>本次体检结论</label>
              <p>
                <el-input
                  size="small"
                  :disabled="isExamine !== 1"
                  v-model="reportConclution.conclusion"
                  placeholder="请输入内容"
                ></el-input>
                <!-- <el-select
                  placeholder="请选择"
                  size="small"
                  filterable
                  clearable
                  v-model="reportConclution.conclusion"
                  :disabled="isExamine !== 1"
                >
                  <el-option
                    v-for="item in conclusionLists"
                    :key="item.tempCode"
                    :label="item.result"
                    :value="item.result"
                  >
                  </el-option>
                </el-select> -->
              </p>
            </div>
            <div class="footer2">
              <div class="foot_inp">
                <label style="width: 40px">总结</label>
                <p>
                  <el-input
                    size="small"
                    clearable
                    readonly
                    v-model="harmSummary"
                  ></el-input>
                </p>
              </div>
              <div class="foot_inp">
                <label>主检医师</label>
                <p>
                  <el-input
                    size="small"
                    clearable
                    readonly
                    v-model="reportConclution.checkDoctorName"
                  ></el-input>
                </p>
              </div>
              <div class="foot_inp">
                <label>主检日期</label>
                <p>
                  <el-date-picker
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    :clearable="false"
                    size="small"
                    readonly
                    v-model="reportConclution.checkTime"
                  >
                  </el-date-picker>
                </p>
              </div>
              <div class="foot_inp">
                <label>审核医师</label>
                <p>
                  <el-input
                    size="small"
                    clearable
                    readonly
                    v-model="reportConclution.auditDoctorName"
                  ></el-input>
                </p>
              </div>
              <div class="foot_inp">
                <label>审核日期</label>
                <p>
                  <el-date-picker
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    :clearable="false"
                    size="small"
                    readonly
                    v-model="reportConclution.auditTime"
                  >
                  </el-date-picker>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <el-drawer
      title="查询"
      :visible.sync="searchDialog"
      :before-close="searchDialogClose"
      :wrapperClosable="false"
      size="90%"
      v-if="searchDialog"
    >
      <MainSearch @getReportConclution="getReportConclution" />
    </el-drawer>
    <el-drawer
      title="历史报告"
      :visible.sync="hisDrawer"
      :before-close="hisDrawerClose"
      :wrapperClosable="false"
      size="90%"
      v-if="hisDrawer"
    >
      <HistoryReport :regNo="reportConclution.patient.regNo" />
    </el-drawer>
    <el-dialog
      :visible.sync="consultingShow"
      width="800px"
      top="12%"
      custom-class="consultingShow"
      :close-on-click-modal="false"
      :before-close="cancel"
    >
      <div slot="title" class="dialog-title">撤回主检咨询窗口</div>
      <el-input
        type="textarea"
        :rows="20"
        placeholder="请输入咨询内容"
        v-model="consultingInput"
      >
      </el-input>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel" size="small">取消</el-button>
        <el-button class="blue_btn" @click="consultingSubmit" size="small"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-drawer
      title="主检咨询回复一览表"
      :visible.sync="consultDialog"
      :before-close="consultDialogClose"
      :wrapperClosable="false"
      size="90%"
      v-if="consultDialog"
    >
      <Consult :conclusionList="conclusionList" />
    </el-drawer>
    <!-- 重大阳性按钮 -->
    <!-- <div class="positive_btn" @click="PositiveResultShow">
      <i class="el-icon-d-arrow-left"></i>
      重阳
    </div> -->
    <!-- 重大阳性按钮 -->
    <!-- <div
      class="positive_btn"
      :class="{ is_positive: isPositiveFlag }"
      @click="PositiveResultShow"
    >
      <i class="el-icon-d-arrow-left"></i>
      重阳
    </div> -->
    <!-- 重大阳性抽屉 -->
    <el-drawer
      title="重大阳性结果"
      :visible.sync="positiveResultDialog"
      :wrapperClosable="false"
      size="90%"
    >
      <PositiveResult
        :patientInfo.sync="reportConclution"
        :P_projectResult="projectResult"
        v-if="positiveResultDialog"
      />
    </el-drawer>
    <el-drawer title="图文报告" size="90%" :visible.sync="ImgTextShow">
      <ImgText
        ref="ImgText_Ref"
        :printImgList.sync="ImgTextList"
        :headerInfo="reportConclution.patient"
        :checkComb.sync="checkComb"
        v-if="ImgTextShow"
      />
    </el-drawer>
    <!-- 添加随访 -->
    <FollowUp
      :regNo="reportConclution.patient.regNo"
      :followUpShow.sync="followUpShow"
      @followUpClose="followUpClose"
    />

    <!-- 体检号/姓名搜索列表内容 -->
    <choicePeReisterDialog
      :keyword="keyword"
      :peStatuss="[3, 4]"
      :show.sync="showChoiceRegister"
      @confirm="confrimChoiceRegDataHandle"
      @cancel="cancelChoiceRegDataHandle"
      @show-event="showChoiceRegisterHandle"
      ref="choicePeReisterDialog"
    />
    <el-dialog
      title="问卷调查表"
      :visible.sync="isShowdialogTable"
      class="questionnaire_dig"
    >
      <Questionnaire
        :formInfo="formInfo"
        @submitSuccess="submitSuccess"
        :questionnaireDisabled="questionnaireDisabled"
      ></Questionnaire>
    </el-dialog>
    <!-- 纯音测听组件 -->
    <el-dialog
      title="纯音测听"
      width="80%"
      top="0px"
      :visible.sync="showPureToneAudiometry"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="pure-tone-audiometry-dialog process_pure-tone-audiometry-dialog"
      :key="'pureToneAudiometryDialog_' + reportConclution.patient.regNo"
    >
      <PureToneAudiometry
        ref="pureToneAudiometry_Ref"
        :isReadonly="true"
        :patientInfo="reportConclution.patient"
      />
    </el-dialog>
  </div>
</template>
<script>
import Process from './mixins/processNewV2';
import { mapGetters } from 'vuex';
import mainInspectionMixins from './mixins/mainInspectionV2';
import PositiveResult from './positiveResult.vue';
import ImgText from '../inspectIn/components/resultEntry/imgText.vue';
import imgTextJs from '../inspectIn/mixins/imgText';
import FollowUp from '@/components/followUp';
import Summary from './component/summaryV2';
import choicePeReisterDialog from './component/choicePeReisterDialog';
import Questionnaire from '@/pages/register/components/questionnaire.vue';
import PureToneAudiometry from '../inspectIn/components/pureToneAudiometry.vue'; // 纯音测听

export default {
  name: 'processV2',
  mixins: [Process, mainInspectionMixins, imgTextJs],
  components: {
    PositiveResult,
    ImgText,
    FollowUp,
    Summary,
    choicePeReisterDialog,
    Questionnaire,
    PureToneAudiometry
  },
  computed: {
    ...mapGetters(['G_EnumList']),
    C_result() {
      return function (result) {
        let txt = '';
        (result || []).map((item, idx) => {
          txt += item.itemTag + (idx + 1 == result.length ? '' : ';');
        });
        return txt;
      };
    }
  },
  data() {
    return {
      harmList: [],
      hazardousCodes2: [],
      jobStatus2: '',
      hazardousCodes3: [],
      jobStatus3: '',
      questionnaireDisabled: true,
      isShowdialogTable: false,
      formInfo: {
        regNo: '', //体检号
        name: '', //姓名
        sex: '', //性别（0通用1男2女）
        age: '', //年龄
        tel: '', //手机号
        familyMedicalHistory: '', //家族史
        pastMedicalHistory: '', //既往病史
        operationStatus: '', //手术状况
        smokingHabit: '', //吸烟习惯
        drinkingHabit: '', //喝酒习惯
        livingHabit: '', //生活习惯
        currentCondition: '', //现在病况
        questionnaireAnswer: '' //问卷答案
      },
      HisPatInfo: {},
      // 表头样式
      headerCellStyle: {
        background: 'rgba(23,112,223,.2)',
        fontSize: '14px',
        color: '#2D3436'
      },
      isSummary: true,
      tabPosition: 'left',
      checked: false,
      isExamine: 1,
      consultingShow: false,
      consultingInput: '',
      isOnly: false,
      harmSummary: '',
      auditLoading: false
    };
  },
  created() {},
  mounted() {
    this.getHarmList();
  },
  methods: {
    getHarmList() {
      let datas = {
        pageSize: 0,
        pageNumber: 0
      };
      this.$ajax
        .post(this.$apiUrls.ReadCodeOccupationalHazardousByPage, datas)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.harmList = returnData;
        });
    },
    submitSuccess() {
      this.isShowdialogTable = false;
      this.questionnaireDisabled = false;
    },
    //问卷调查
    questionnaire() {
      if (!this.reportConclution.patient.regNo) {
        this.$message({
          message: '请先选择需要问卷调查的体检信息！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.isShowdialogTable = true;
    },
    //通过体检号获取问卷数据
    GetQuestionDataByRegNo(userInfo) {
      this.formInfo = {
        regNo: userInfo.regNo, //体检号
        name: userInfo.name, //姓名
        sex: this.G_EnumList['Sex'][userInfo.sex], //性别（0通用1男2女）
        age: userInfo.age == '0' ? userInfo.age + '月' : userInfo.age + '岁', //年龄
        tel: userInfo.tel //手机号
      };
      this.$ajax
        .post(this.$apiUrls.GetQuestionDataByRegNo, '', {
          query: {
            regNo: userInfo.regNo
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          if (!returnData) {
            this.questionnaireDisabled = true;
            return;
          }
          this.questionnaireDisabled = false;
          let temp = Object.assign(this.formInfo, returnData);
          this.formInfo = { ...temp };
        });
    },
    // 是否显示复查
    reviewBtnShow(flag) {
      this.isReview = flag;
    },
    // 暂存
    temporarySave() {
      if (!this.reportConclution.patient.regNo) {
        this.$message({
          message: '请先选择体检人信息!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$nextTick(async () => {
        if (this.$refs.summaryCom_Ref.isHaveModify) {
          setTimeout(() => {
            this.temporarySave();
          }, 300);
          return;
        }
        await this.$refs.summaryCom_Ref.handleSort();
        this.reportConclution.auditDoctorCode =
          this.G_userInfo.codeOper.operatorCode;
        this.reportConclution.auditDoctorName = this.G_userInfo.codeOper.name;
        this.reportConclution.combSummarys =
          this.$refs.summaryCom_Ref.summaryList;
        this.reportConclution.suggestions =
          this.$refs.summaryCom_Ref.newSuggestions;

        if (!this.reportConclution.conclusion) {
          this.reportConclution.conclusion = '';
        }
        // this.reportConclution.patient.isAudit = false;
        this.reportConclution.isOccupation = true;
        this.isOnly = true;
        this.$ajax
          .post(this.$apiUrls.TempSaveReportConclusionV2, this.reportConclution)
          .then((r) => {
            let { success, returnData } = r.data;
            if (!success) return;
            this.$message({
              message: '暂存成功!',
              type: 'success',
              showClose: true
            });
            // this.getReportConclution(this.reportConclution.patient.regNo);
            // this.getPatientList();
          });
      });
    },
    //审核
    examineClick() {
      this.isOnly = false;
      if (!this.reportConclution.patient.regNo) {
        this.$message({
          message: '请先选择体检人信息!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.isExamine === 2) {
        this.cancelAudit();
        return;
      }
      this.auditLoading = true;
      this.$nextTick(async () => {
        if (this.$refs.summaryCom_Ref.isHaveModify) {
          setTimeout(() => {
            this.examineClick(true);
          }, 300);
          return;
        }
        await this.$refs.summaryCom_Ref.handleSort();
        this.reportConclution.auditDoctorCode =
          this.G_userInfo.codeOper.operatorCode;
        this.reportConclution.auditDoctorName = this.G_userInfo.codeOper.name;
        this.reportConclution.combSummarys =
          this.$refs.summaryCom_Ref.summaryList;
        this.reportConclution.suggestions =
          this.$refs.summaryCom_Ref.newSuggestions;

        if (!this.reportConclution.conclusion) {
          this.reportConclution.conclusion = '';
        }
        // this.reportConclution.patient.isAudit = true;
        this.reportConclution.isOccupation = true;
        this.$ajax
          .post(
            this.$apiUrls.ConfirmReportConclusionV2,
            this.reportConclution,
            {
              query: { oper: 1 }
            }
          )
          .then((r) => {
            let { success, returnData } = r.data;
            if (!success) return;
            this.$message({
              message: '审核成功!',
              type: 'success',
              showClose: true
            });
            this.getReportConclution(this.reportConclution.patient.regNo);
            this.getPatientList();
          })
          .finally((r) => {
            this.auditLoading = false;
          });
      });
    },
    // 取消审核
    cancelAudit() {
      if (!this.reportConclution.patient.regNo) {
        this.$message({
          message: '请先选择要取消保存的结论!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$ajax
        .post(this.$apiUrls.CancelReportConclusionNewV2, [], {
          query: {
            regNo: this.reportConclution.patient.regNo,
            doctorCode: this.G_userInfo.codeOper.operatorCode,
            oper: 1,
            isOccupation: true
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$message({
            message: '取消审核成功!',
            type: 'success',
            showClose: true
          });
          this.getReportConclution(this.reportConclution.patient.regNo); //刷新数据
          this.getPatientList();
        });
    },
    // 预览报告
    previewReport() {},
    // 主检回复
    mainInspectionReply() {
      this.consultDialog = true;
      this.getReturnChecked();
    },
    // 主检回复请求
    getReturnChecked() {
      this.$ajax
        .post(this.$apiUrls.GetReturnCheckedNew, '', {
          query: {
            operatorCode: this.G_userInfo.codeOper.operatorCode,
            isOccupation: true
          }
        })
        .then((r) => {
          console.log('GetReturnChecked: ', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.conclusionList = returnData || [];
        });
    },
    // 撤回主检
    withdrawMainTest() {
      if (!this.reportConclution.patient.regNo) {
        this.$message({
          message: '请先选择撤回主检人员!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.consultingShow = true;
      this.consultingInput = '';
    },
    // 撤回主检提交
    consultingSubmit() {
      if (!this.consultingInput) {
        this.$message({
          message: '请输入咨询内容!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let data = {
        regNo: this.reportConclution.patient.regNo,
        questionerCode: this.G_userInfo.codeOper.operatorCode,
        questionerName: this.G_userInfo.codeOper.name,
        questionContent: this.consultingInput,
        replyerCode: this.reportConclution.checkDoctorCode,
        replyerName: this.reportConclution.checkDoctorName,
        isOccupation: true
      };
      this.$ajax.post(this.$apiUrls.ReturnCheckedNew, data).then((r) => {
        console.log('ReturnChecked: ', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.$message({
          message: '撤回主检已发送!',
          type: 'success',
          showClose: true
        });
        this.consultingShow = false;
      });
    },
    // 取消
    cancel() {
      this.consultingShow = false;
    },
    // 过滤本人主检
    checkboxChange() {
      if (this.activeMain3 == 'main2') {
        if (this.checked) {
          this.viewTableList2 = this.viewTableListCopy2.filter((item) => {
            return (
              item.checkDoctorCode !== this.G_userInfo.codeOper.operatorCode
            );
          });
        } else {
          this.viewTableList2 = this.viewTableListCopy2;
        }
      } else if (this.activeMain3 == 'main3') {
        if (this.checked) {
          this.viewTableList3 = this.viewTableListCopy3.filter((item) => {
            return (
              item.checkDoctorCode !== this.G_userInfo.codeOper.operatorCode
            );
          });
        } else {
          this.viewTableList3 = this.viewTableListCopy3;
        }
      }
    },
    // 处理参考值
    disposeFKVal(row) {
      let lLimit = row.lowerLimit;
      let uLimit = row.upperLimit;
      if (lLimit && !uLimit) {
        return `${Number(lLimit) ? '≥' : ''}${lLimit}`;
      }
      if (uLimit && !lLimit) {
        return `${Number(uLimit) ? '≤' : ''}${uLimit}`;
      }
      if (lLimit && uLimit) {
        return `${lLimit}-${uLimit}`;
      }
    },
    // 销毁前的回调
    onPopoverHide() {
      let node = document.querySelector('body>.my_popover'); //隐藏时删除
      node?.remove();
    }
  },
  watch: {
    'reportConclution.patient': {
      handler: function (n, o) {
        this.HisPatInfo = n;
      },
      deep: true
    }
  }
};
</script>
<style lang="less" scoped>
@custom-color-1: #089c66;
@custom-color-2: #1770df;
@custom-color-3: #d63031;

.process {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  display: flex;
  color: #2d3436;
  flex-direction: column;
  .process_pure-tone-audiometry-dialog {
    /deep/.el-dialog__header {
      padding-bottom: 5px;
      padding-top: 5px;
      .el-dialog__headerbtn {
        top: 9px;
      }
    }
    /deep/.el-dialog__body {
      padding: 0 10px 5px;
    }
  }
  .anchorPoint_btn {
    width: 10px;
    height: calc(100% - 164px);
    position: absolute;
    left: 0;
    z-index: 11;
    bottom: 0;
  }
  /deep/.el-table__row--striped td.el-table__cell,
  .vxe-table--render-default .vxe-body--row.row--stripe {
    background-color: #e7f0fb !important;
  }
  .topBtn {
    // height: 68px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 5px 5px 12px;
    background: #fff;
    .leftTop {
      display: flex;
      align-items: center;
      p {
        // line-height: 68px;
        display: flex;
        align-items: center;
        margin-left: 20px;
        margin-right: 20px;
        width: 170px;
        label {
          width: 130px;
          font-size: 14px;
          font-weight: 600;
        }
      }
    }
  }
  /deep/.el-popover {
    height: calc(100% - 142px);
    height: 600px !important;
  }
  .topTitle {
    // height: 90px;
    background: #fef0d8;
    box-shadow: 0 2px 8px 0 rgba(186, 133, 37, 0.1);
    border-radius: 2px;
    border-radius: 2px;
    margin-bottom: 5px;
  }
  .el-collapse {
    border: none;
  }
  .collapse /deep/.el-collapse-item__header {
    // background: #fef0d8;
    height: 28px;
  }
  .type_head {
    padding: 0 10px 0 18px !important;
    height: 100%;
    li {
      height: 100% !important;
    }
  }
  /deep/.el-collapse-item__content {
    padding-bottom: 0;
  }
  .type_head {
    width: 90%;
    height: 100%;
    // margin-left: 100px;
    &:last-child {
      padding-top: 0;
    }

    li {
      display: flex;
      height: 32px;
      font-family: PingFangSC-Medium;
      font-size: 14px;
      color: #2d3436;
    }

    .every_inp {
      display: flex;
      align-items: center;
      margin-right: 10px;
      line-height: normal;

      label {
        margin-right: 10px;
        min-width: 37px;
        text-align: right;
        font-family: PingFangSC-Semibold;
        font-size: 14px;
        color: #2b3436;
        text-align: right;
        // font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      p {
        flex: 1;
        flex-shrink: 0;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #2b3436;
        font-weight: 600;
      }
    }
    .regNo_inp {
      width: 180px;
    }
    .less_inp {
      width: 100px;
    }
    .cont_inp {
      width: 300px;
    }
    .num_inp {
      width: 210px;
    }
    .flex_inp {
      flex: 1;
      flex-shrink: 0;
    }
  }
  .centerCont {
    flex: 1;
    overflow: auto;
    background: #fff;
    padding: 0 5px 5px;
    display: flex;
    .el-table {
      color: #000;
    }
    .con_wrap {
      display: flex;
      flex-direction: column;
      margin-right: 5px;

      .bottom_wrap,
      .fold_div {
        border: 1px solid #b2bec3;
        border-radius: 4px;
        flex: 1;
        flex-shrink: 0;
        overflow: auto;
        .projectNav_wrap {
          height: 100%;
          overflow: auto;
          position: absolute;
          top: 0;
          left: -260px;
          border: 1px solid #b2bec3;
          border-radius: 4px;
          width: 250px;
          z-index: 12;
          background: #fff;
          transition: all 0.3s ease-in-out;
          a {
            display: block;
            padding: 10px;
            list-style: none;
            text-decoration: none;
            border-bottom: 1px solid #b2bec3;
            font-size: 16px;
            color: #000;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            &:hover {
              color: #1770df;
            }
          }
        }
        .projectNav_wrap_active {
          transform: translateX(253px);
        }
        .result_div .popover_div {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        /deep/.el-collapse-item__header {
          background: rgba(178, 190, 195, 0.1);
          padding: 0 5px;
          font-size: 18px;
          color: #2d3436;
          line-height: normal;
          font-weight: 600;
          height: auto;
          min-height: 48px;
        }
        .project_result {
          padding: 0 5px;
          margin-top: 5px;
          display: flex;
          span {
            font-size: 14px;
            color: #1770df;
          }
          & > div {
            flex: 1;
            flex-shrink: 0;
          }
          .tags_sty {
            margin-right: 5px;
            margin-bottom: 5px;
            white-space: normal;
            height: auto;
            color: #1770df;
            &:last-child {
              margin-right: 0;
            }
          }
          .tags_red {
            margin-right: 5px;
            margin-bottom: 5px;
            white-space: normal;
            height: auto;
            color: red;
            &:last-child {
              margin-right: 0;
            }
          }
        }
      }
      .redClass {
        color: red;
        font-weight: 600;
      }
      .defaultClass {
        color: #000;
        font-weight: 600;
      }
      /deep/.SummaryCom {
        flex: 1;
        flex-shrink: 0;
        overflow: auto;
      }
      /deep/.leftCont {
        overflow: auto;
      }
    }
    .left_wrap {
      width: 30%;
      flex-shrink: 0;
      position: relative;
    }
    .right_wrap {
      flex: 1;
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      .center_wrap {
        flex: 1;
        flex-shrink: 0;
        display: flex;
        overflow: auto;
        /deep/.SummaryCom .title > span {
          font-size: 16px;
          font-weight: normal;
        }
        /deep/.SummaryCom .title {
          margin: 5px 0;
        }
        /deep/.SummaryCom .leftDiv .leftCont .contDiv .titleLeft {
          height: auto;
          padding: 5px;
          line-height: normal;
          span:first-child {
            flex: 1;
            flex-shrink: 0;
          }
        }
      }
      .overview {
        flex-basis: 40%;
      }
      .suggest {
        flex: 1;
        flex-shrink: 0;
        margin-right: 0;
      }
    }
  }
  .centerCont-tabs {
    /deep/.el-tabs__item {
      padding: 0 10px;
    }
  }
  .tab-pane {
    padding: 10px 10px 10px 0;
  }
  .footerCont {
    // height: 118px;
    width: 100%;
    background: #fff;
    .footer1 {
      display: flex;
      line-height: 32px;
      width: 100%;
      margin: 3px 0;
      label {
        padding-right: 10px;
        font-size: 18px;
        font-weight: 600;
      }
      p {
        flex: 1;
        & > div {
          width: 100%;
        }
        /deep/.el-input--small .el-input__inner {
          color: #000;
          font-weight: 600;
        }
      }
    }
    .footer2 {
      display: flex;
      line-height: 32px;
      .foot_inp {
        // flex: 1;
        width: 190px;
        display: flex;
        align-items: center;
        margin-right: 20px;
        overflow: auto;
        &:nth-child(1) {
          flex: 1;
          flex-shrink: 0;
        }
        &:nth-child(even) {
          /deep/input {
            padding-left: 5px;
          }
        }
        /deep/input {
          padding-right: 5px;
        }
        &:last-child {
          margin-right: 0;
        }
        label {
          width: 70px;
          font-size: 14px;
          font-weight: 600;
        }

        p {
          flex: 1;
          overflow: hidden;
        }
        /deep/.el-input--small .el-input__inner {
          color: #000;
          font-weight: 600;
        }
      }
      .el-date-editor.el-input,
      .el-date-editor.el-input__inner {
        width: 100%;
      }
    }
  }
  /deep/.el-date-editor.el-input,
  /deep/.el-date-editor.el-input__inner {
    width: 100%;
  }
  /deep/.el-drawer__header {
    align-items: center;
    display: flex;
    background: rgba(23, 112, 223, 0.2);
    border-radius: 4px 4px 0 0;
    line-height: 42px;
    font-family: PingFangSC-Medium;
    font-size: 18px;
    padding: 0;
    padding-left: 20px;
    color: #2d3436;
    margin-bottom: 0;
  }
  /deep/.el-drawer__close-btn {
    font-size: 30px;
  }
  .firstPage {
    flex: 1;
    overflow: auto;
  }
  .el-tabs {
    height: 100%;
    display: flex;
    flex-direction: row;
  }
  /deep/.el-tabs__content {
    flex: 1;
  }
  .el-tab-pane {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  /deep/.btnHeader {
    padding: 0 !important;
    margin-bottom: 10px !important;
  }
  /deep/.rightcont {
    padding: 0 !important;
  }
  /deep/.rightBom {
    padding: 10px 0 0 !important;
  }
  .dialog-title {
    background: #d1e2f9;
    font-size: 18px;
    padding: 18px;
    font-weight: 600;
  }
  .positive_btn {
    margin-left: 10px;
    background: #c9c9c9;
    font-size: 14px;
    height: 29px;
    line-height: 29px;
    width: 60px;
    text-align: center;
    color: #fff;
    cursor: pointer;
  }
  .is_positive {
    background: #d63031;
  }
  .collapse_div {
    display: flex;
    flex: 1;
    justify-content: space-between;
    .operNameClass {
      margin-left: 10px;
      margin-right: 8px;
      text-align: right;
      font-weight: 400;
      font-size: 14px;
      width: -moz-max-content;
      width: max-content;
    }
  }
  .text-overflow-hidden {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .projectResult_btn {
    display: none;
  }
  @media screen and (max-width: 1440px) {
    .left_wrap {
      display: none !important;
    }
    .projectResult_btn {
      display: block;
    }
  }
}
@import url(./css/mainInspectionMedia.less);
</style>

<style lang="less">
@custom-color-1: #089c66;
@custom-color-2: #1770df;
@custom-color-3: #d63031;

.popverClass {
  height: calc(100% - 200px);
  /* overflow: auto; */
  /* left: 20px !important; */
}
.popverClass .el-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.popverClass .el-tabs__content {
  flex: 1;
  overflow: auto;
}
.popverClass .el-tab-pane {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.popoverDiv {
  display: flex;
  flex-direction: row;
  height: 100%;
}
.popoverDiv .poDiv1 {
  margin-right: 20px;
}
.consultingShow {
  .el-dialog__header {
    padding: 0;
  }
  .el-dialog__body {
    padding-top: 20px;
    padding-right: 20px;
    padding-bottom: 20px;
  }
  .el-dialog__footer {
    padding-top: 0;
    padding-right: 20px;
  }
}
.my_popover {
  background: #dae4f2;
  .popper__arrow::after {
    border-right-color: rgba(0, 0, 0, 0.6) !important;
  }
  li {
    color: #000;
  }
}
.popover_ul {
  font-size: 16px;
  display: flex;
  color: #fff;
  li {
    flex: 1;
    flex-shrink: 0;
    // &:last-child span{
    //   color:#d63031;
    // }
  }
}
.popover_i {
  font-style: normal;
  color: #d63031;
  font-size: 18px;
  margin-right: 5px;
}
.projectResult_popover {
  min-width: 670px;
  .con_wrap {
    height: 100%;
    overflow: auto;
    display: flex;
    flex-direction: column;
    .bottom_wrap {
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
      .icon-green {
        color: #089c66;
      }
    }
    .projectResult_header {
      display: flex;
      justify-content: space-between;
      height: 41px;
      align-items: center;
      margin-left: 20px;
      font-family: PingFangSC-Medium;
      font-size: 16px;
      color: #1770df;
      font-weight: 550;
    }
  }
  .el-collapse-item {
    margin-bottom: 2px;
    .el-collapse-item__header {
      background: #eeeeee;
      padding: 0 5px;
      font-size: 18px;
      color: #2d3436;
      line-height: normal;
      font-weight: 600;
      height: auto;
      min-height: 48px;

      .el-icon-s-comment {
        font-size: 20px;
        margin-top: 5px;
      }

      .el-collapse-item__arrow {
        display: none;
      }
    }

    .el-table__cell {
      border-bottom: unset;
    }
  }
  .collapse_div {
    display: flex;
    flex: 1;
    justify-content: space-between;
    .operNameClass {
      margin-left: 10px;
      margin-right: 8px;
      text-align: right;
      font-weight: 400;
      font-size: 14px;
      width: -moz-max-content;
      width: max-content;
    }
  }
  .project_result {
    padding: 0 5px;
    margin-top: 5px;
    display: flex;

    span {
      font-size: 14px;
      color: @custom-color-2;
    }

    & > div {
      flex: 1;
      flex-shrink: 0;
    }

    .tags_sty {
      margin-right: 5px;
      margin-bottom: 5px;
      white-space: normal;
      height: auto;
      color: @custom-color-2;
      font-family: PingFangSC-Medium;
      &:last-child {
        margin-right: 0;
      }
    }
    .tags_red {
      margin-right: 5px;
      margin-bottom: 5px;
      white-space: normal;
      height: auto;
      color: red;
      &:last-child {
        margin-right: 0;
      }
    }
  }
  .el-collapse-item__content {
    padding-bottom: 0;
  }
}
.anchor_popover {
  height: calc(100% - 200px);
  display: flex;
  flex-direction: column;
  .projectNav_wrap {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    border-top: 1px solid #b2bec3;
    width: 250px;
    background: #fff;
    a {
      display: block;
      cursor: pointer;
      padding: 10px;
      list-style: none;
      text-decoration: none;
      border-bottom: 1px solid #b2bec3;
      font-size: 16px;
      color: #000;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      &:hover {
        color: @custom-color-2;
      }
    }
  }
}
@import url(./css/mainInspectionCommon.less);
</style>
