<template>
  <div class="list" v-loading="loading">
    <ul>
      <li class="every_inp" v-if="G_config.physicalMode.includes('普检')">
        <label>体检分类</label>
        <p>
          <el-select
            placeholder="请选择"
            size="small"
            filterable
            clearable
            v-model="tjCls"
            @change="changeCls"
            @clear="changeCls"
          >
            <el-option
              v-for="item in G_peClsList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </p>
      </li>
      <li class="every_inp">
        <el-select
          size="small"
          placeholder="请选择"
          v-model="localQueryTimeType"
          filterable
          @change="queryTimeTypeChange"
          class="label"
        >
          <el-option
            v-for="item in queryTimeTypeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>

        <p style="min-width: 250">
          <el-date-picker
            :picker-options="{ shortcuts: G_datePickerShortcuts }"
            v-model="dateValue"
            type="daterange"
            size="small"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            @change="dateChange"
            :clearable="false"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </p>
      </li>
      <li class="every_inp" v-if="occupationalParameters">
        <el-select
          class="select"
          v-model.trim="jobStatusProxy"
          placeholder="在岗状态"
          size="small"
          width="100%"
          filterable
          @keyup.enter.native.stop
          clearable
          @change="handleJobStatusChange"
          @clear="handleJobStatusClear"
        >
          <el-option
            v-for="(item, index) in G_CodeOccupationalPositionStatus"
            :key="index"
            :value="item.value"
            :label="item.label"
          ></el-option>
        </el-select>
      </li>
      <li class="every_inp" v-if="occupationalParameters">
        <vxe-select
          style="width: 340px"
          @change="handleHazardousChange"
          @clear="handleHazardousClear"
          placeholder="危害因素"
          size="small"
          clearable
          multiple
          :popper-append-to-body="true"
          :option-props="{ value: 'hazardousCode', label: 'hazardousName' }"
          v-model="hazardousCodesProxy"
          :options="harmList"
          filterable
        >
        </vxe-select>
      </li>
      <li class="every_inp" v-if="isFilterRadioShow">
        <p>
          <el-radio-group v-model.trim="searchRadioVal" @change="changeCls">
            <el-radio :label="0">全部</el-radio>
            <el-radio :label="1">本人</el-radio>
          </el-radio-group>
        </p>
      </li>
    </ul>

    <div class="tableCont">
      <PublicTable
        :viewTableList.sync="viewTableList"
        :theads.sync="viewTheads"
        :columnWidth="columnWidth"
        :columnMinWidth="{ companyName: 180 }"
        @rowDblclick="rowDblclick"
        :isOnlySortShow="true"
        :tableCellClassName="stateRow"
        :columnSort="columnSort"
        showOverflowTooltip
        border
      >
        <template #companyName="{ scope }">
          <div class="container" :title="scope.row.companyName">
            {{ scope.row.companyName }}
          </div>
        </template>
        <template #peCls="{ scope }">
          <div>
            <span>{{ G_EnumList['PeCls'][scope.row.peCls] }}</span>
          </div>
        </template>
        <template #sex="{ scope }">
          <div>
            {{ G_sex(scope.row.sex) }}
          </div>
        </template>
        <template #allocatedDoctorCode="{ scope }">
          <div>
            {{ G_operator(scope.row.allocatedDoctorCode) }}
          </div>
        </template>
      </PublicTable>
    </div>
  </div>
</template>
<script>
import PublicTable from '../../components/publicTable.vue';
import { mapGetters } from 'vuex';

export default {
  name: 'list',
  model: {
    prop: 'dateVal',
    event: 'dateChange'
  },
  props: {
    // 表格数据
    viewTableList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    loading: {
      type: Boolean,
      default: false
    },
    occupationalParameters: {
      type: Boolean,
      default: false
    },
    harmList: {
      type: Array,
      default: () => []
    },
    hazardousCodes: {
      type: Array,
      default: () => []
    },
    jobStatus: {
      type: String,
      default: ''
    },
    isFilterRadioShow: {
      type: Boolean,
      default: false
    },
    dateVal: {
      type: Array,
      default: () => []
    },
    // 1: 登记时间，2：体检时间，3：已检完时间, 4：主检时间，5：审核时间
    queryTimeType: {
      type: Number,
      default: 2
    }
  },
  components: {
    PublicTable
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_peClsList',
      'G_sysOperator',
      'G_datePickerShortcuts',
      'G_config',
      'G_CodeOccupationalPositionStatus'
    ]),
    G_sex() {
      return function (sex) {
        return this.G_EnumList['Sex'][sex];
      };
    },
    G_operator() {
      return function (operator) {
        return this.G_EnumList['SysOperator'][operator];
      };
    },
    jobStatusProxy: {
      get() {
        return this.jobStatus;
      },
      set(value) {
        this.$emit('update:jobStatus', value);
      }
    },
    hazardousCodesProxy: {
      get() {
        return this.hazardousCodes;
      },
      set(value) {
        this.$emit('update:hazardousCodes', value);
      }
    }
  },
  data() {
    return {
      searchRadioVal: 0,
      dateValue: this.dateVal,
      viewTheads: {
        peCls: '体检分类',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        companyName: '体检单位',
        tel: '手机号',
        regNo: '体检号',
        commonTime: '时间'
      },
      tjCls: '',
      columnWidth: {
        commonTime: 150,
        regNo: 120,
        peCls: 110,
        name: 75,
        sex: 60,
        age: 60,
        tel: 110
      },
      columnSort: ['peCls', 'name', 'sex', 'age', 'companyName', 'commonTime'],
      queryTimeTypeList: [
        { label: '审核时间', value: 4 },
        { label: '主检时间', value: 3 },
        { label: '已检完时间', value: 2 },
        { label: '体检时间', value: 1 },
        { label: '登记时间', value: 0 }
      ],
      localQueryTimeType: 0
    };
  },
  created() {},
  mounted() {
    this.localQueryTimeType = this.queryTimeType;
    this.queryTimeTypeList = this.queryTimeTypeList.filter(
      (item) => item.value <= this.localQueryTimeType
    );
  },
  methods: {
    handleJobStatusChange(value) {
      this.$emit('update:jobStatus', value);
      this.$emit('dateChange', this.dateValue);
    },
    handleJobStatusClear() {
      this.$emit('update:jobStatus', '');
    },
    handleHazardousChange(value) {
      this.$emit('update:hazardousCodes', value.value);
      this.$emit('dateChange', this.dateValue);
    },
    handleHazardousClear() {
      this.$emit('update:hazardousCodes', []);
    },
    rowDblclick(row) {
      this.$emit('getReportConclution', row.regNo);
    },
    changeCls() {
      this.$emit('filterCls', this.tjCls, this.searchRadioVal);
    },
    // 根据标本状态变化行颜色
    stateRow({ row }) {
      if (row.isPriority) {
        return 'background: rgb(154 145 226) ';
      }
    },
    dateChange() {
      this.tjCls = '';
      this.searchRadioVal = 0;
      this.$emit('dateChange', this.dateValue);
    },
    /**
     * <AUTHOR> justin
     * @description  : 时间查询类型切换
     * @return        {*}
     */
    queryTimeTypeChange() {
      this.$emit('update:queryTimeType', this.localQueryTimeType);
      this.$emit('queryTimeTypeChange', this.localQueryTimeType);
    }
  },
  watch: {
    queryTimeType: {
      handler(newVal, oldVal) {
        this.localQueryTimeType = newVal;
      }
    },
    localQueryTimeType: {
      handler(newVal, oldVal) {
        this.viewTheads['commonTime'] = this.queryTimeTypeList.find(
          (item) => item.value === newVal
        ).label;
      }
    },
    occupationalParameters: {
      handler(newVal) {
        // 创建新对象，避免直接修改原对象
        const newTheads = { ...this.viewTheads };
        const newColumnWidth = { ...this.columnWidth };

        if (newVal) {
          // 使用 Vue.set 或直接赋值（需替换整个对象）
          newTheads.jobStatusName = '在岗状态';
          newTheads.hazardNames = '危害因素';
          newColumnWidth.jobStatusName = 80;
          newColumnWidth.hazardNames = 250;
        } else {
          delete newTheads.jobStatusName;
          delete newTheads.hazardNames;
          delete newColumnWidth.jobStatusName;
          delete newColumnWidth.hazardNames;
        }

        // 通知父组件更新 theads 和 columnWidth
        this.viewTheads = newTheads;
        this.columnWidth = newColumnWidth;
      },
      immediate: true // 初始化时立即执行一次
    }
  }
};
</script>
<style lang="less" scoped>
.list {
  display: flex;
  width: 100%;
  height: 100%;
  border-radius: 4px;
  display: flex;
  color: #2d3436;
  flex-direction: column;
  ul {
    display: flex;
  }
  .every_inp {
    display: flex;
    align-items: center;
    line-height: 48px;
    max-width: 380px;
    flex-shrink: 0;
    margin-right: 10px;

    label {
      width: 80px;
      margin-right: 10px;
      // width: auto;
      text-align: right;
    }

    .label {
      max-width: 120px;
      text-align: right;
    }

    p {
      flex: 1;
      .el-select {
        width: 100%;
      }
    }
  }
  .tableCont {
    flex: 1;
    overflow: auto;
  }
  .el-date-editor--daterange.el-input,
  .el-date-editor--daterange.el-input__inner,
  .el-date-editor--timerange.el-input,
  .el-date-editor--timerange.el-input__inner {
    width: 100%;
  }
  /deep/.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    background-color: #409eff;
    border-color: #409eff;
  }
  /deep/.el-table th.el-table__cell > .cell {
    padding-left: 3px;
    padding-right: 3px;
  }
  .indexDiv {
    display: flex;
  }
  .title {
    font-size: 12px;
    text-decoration: none;
    color: var(--title-color);
    --title-color: #337efb;
  }

  .badge {
    display: inline-block;
    width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    border-radius: 50%;
    background: var(--title-color);
    color: white;
  }
  .container {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  /deep/ .el-table__cell {
    vertical-align: baseline;
  }
}
</style>
