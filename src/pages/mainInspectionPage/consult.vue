<template>
  <div class="consult">
    <div class="headrCont">
      <el-input
        size="small"
        placeholder="体检号/姓名"
        class="searchIpt"
      ></el-input>
      <MainBtnCom :btnList="['查询']" />
    </div>

    <div class="tableCont">
      <PublicTable
        :viewTableList.sync="conclusionList"
        :theads.sync="theads"
        :tableLoading.sync="loading"
        :isSortShow="false"
        :columnWidth="{
          regNo: 140,
          combName: 120,
          replyContent: 200,
          questionContent: 200,
          questionTime: 170,
          replyTime: 170
        }"
      >
      </PublicTable>
    </div>
  </div>
</template>

<script>
import PublicTable from '../../components/publicTable.vue';
import MainBtnCom from './mainBtnCom.vue';
import { mapGetters } from 'vuex';
export default {
  name: 'consult',
  components: { PublicTable, MainBtnCom },
  props: {
    cancel: {
      type: Function,
      default: null
    },
    conclusionList: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  data() {
    return {
      loading: false,
      searchForm: {}, //查询条件
      tableData: [], //表单数据
      theads: {
        regNo: '体检号',
        combCode: '组合代码',
        combName: '组合名称',
        // sex: "发起人",
        // name: "姓名",
        questionerName: '咨询医生',
        questionContent: '咨询内容',
        questionTime: '咨询时间',
        replyerName: '回复医生',
        replyContent: '回复内容',
        replyTime: '回复时间'
        // tjCls: "解锁时间"
      },

      columnWidth: {
        groupPackage: 100,
        company: 180,
        depart: 180,
        regNo: 150,
        patCode: 150
      },
      rules: {},
      companyList: [],
      radioVal: 1
    };
  },
  created() {},
  mounted() {
    this.getCompany();
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_peClsList', 'G_userInfo', 'G_shareSexList'])
  },
  methods: {
    getCompany() {
      this.$ajax.post(this.$apiUrls.Company).then((r) => {
        this.companyList = r.data.returnData;
      });
    }
  }
};
</script>

<style lang="less" scoped>
.consult {
  display: flex;
  flex-direction: column;
  height: 100%;
  /deep/.formBtn .el-form-item__content {
    margin-left: 10px !important;
  }
  .headrCont {
    display: flex;
    flex-direction: row;
    // background: rgba(178, 190, 195, 0.2);
    border-radius: 4px;
    padding: 15px;
    .el-row {
      display: flex;
    }
    .el-form-item {
      margin-bottom: 10px;
      /deep/ .el-form-item__label,
      /deep/.el-form-item__content {
        height: 32px;
        line-height: 32px;
      }
    }
    .select {
      width: 100%;
    }
    .headerLeft {
      flex: 1;
      /deep/.el-input__inner {
        height: 32px;
        line-height: 32px;
      }

      .titleCont {
        line-height: 32px;
        display: flex;
        margin-left: 0 !important;
        padding-bottom: 10px;
        .titleRadio {
          padding-left: 20px;
          background: #fff;
          /deep/.el-radio {
            margin-right: 15px;
            /deep/.el-radio__inner {
              width: 18px;
              height: 18px;
            }
          }
        }
      }
    }
  }

  .tableCont {
    flex: 1;
    margin: 0 15px 15px 15px;
    overflow: auto;
    border: 1px solid #dde2e5;
    border-radius: 4px;
  }

  /deep/.el-checkbox__inner {
    width: 18px;
    height: 18px;
  }
  .searchIpt {
    width: 50%;
    margin-right: 20px;
  }
}
</style>
