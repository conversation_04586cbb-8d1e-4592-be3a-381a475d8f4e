<template>
  <div class="list">
    <div class="every_inp">
      <label>体检号</label>
      <p>
        <el-input
          size="small"
          placeholder="体检号"
          clearable
          v-model="regNo"
          @keyup.enter.native="enterRegNo"
        ></el-input>
      </p>
    </div>
    <div class="every_inp">
      <label>体检日期</label>
      <p>
        <el-date-picker
          :picker-options="{ shortcuts: G_datePickerShortcuts }"
          v-model.trim="tjDate"
          type="daterange"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          clearable
          size="small"
          @change="changeTime"
          @clear="changeTime"
        >
        </el-date-picker>
      </p>
    </div>
    <div class="tableCont">
      <PublicTable
        :viewTableList.sync="viewTableList"
        :theads.sync="viewTheads"
        :columnWidth="columnWidth"
        @rowDblclick="rowDblclick"
        :isOnlySortShow="true"
        :columnSort="columnSort"
      >
        <template #sex="{ scope }">
          <div>
            {{ G_EnumList['Sex'][scope.row.sex] }}
          </div>
        </template>

        <template #companyName="{ scope }">
          <div class="container" :title="scope.row.companyName">
            {{ scope.row.companyName }}
          </div>
        </template>
        <template #peCls="{ scope }">
          <div>
            <span>{{ G_EnumList['PeCls'][scope.row.peCls] }}</span>
          </div>
        </template>
      </PublicTable>
    </div>
  </div>
</template>
<script>
import PublicTable from '../../components/publicTable.vue';
import { mapGetters } from 'vuex';
import { dataUtils } from '@/common';
export default {
  name: 'list',
  props: {},
  components: {
    PublicTable
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_peClsList', 'G_datePickerShortcuts'])
  },
  data() {
    return {
      viewTheads: {
        peCls: '体检分类',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        companyName: '体检单位',
        tel: '手机号',
        regNo: '体检号',
        registerTime: '登记时间'
      },
      viewTableList: [],
      tjCls: '',
      tjDate: [dataUtils.getDate(), dataUtils.getDate()], //体检时间
      regNo: '',
      searchInfo: {
        regNo: '',
        beginActiveDate: '',
        endActiveDate: ''
      },
      columnWidth: {
        registerTime: 180,
        regNo: 132,
        peCls: 110,
        companyName: 180,
        name: 75,
        sex: 60,
        age: 60,
        tel: 180
      },
      columnSort: ['peCls', 'name', 'sex', 'age', 'companyName', 'registerTime']
    };
  },
  created() {},
  mounted() {
    this.changeTime();
  },
  methods: {
    rowDblclick(row) {
      this.$emit('getReportConclution', row.regNo);
      console.log('[ row ]-121', row);
    },
    //体检号回车
    enterRegNo() {
      this.searchInfo = {
        regNo: this.regNo,
        beginActiveDate: '',
        endActiveDate: ''
      };
      this.$ajax
        .post(this.$apiUrls.QueryCheckedPatientList, '', {
          query: this.searchInfo
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.viewTableList = returnData;
        });
    },
    changeTime() {
      if (!this.tjDate) {
        this.searchInfo = {
          regNo: '',
          beginActiveDate: '',
          endActiveDate: ''
        };
      } else {
        this.searchInfo = {
          regNo: '',
          beginActiveDate: this.tjDate[0] || '',
          endActiveDate: this.tjDate[1] || ''
        };
      }
      this.$ajax
        .post(this.$apiUrls.QueryCheckedPatientList, '', {
          query: this.searchInfo
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.viewTableList = returnData;
        });
    }
  }
};
</script>
<style lang="less" scoped>
.list {
  display: flex;
  width: 100%;
  height: 100%;
  border-radius: 4px;
  display: flex;
  color: #2d3436;
  flex-direction: column;
  .every_inp {
    display: flex;
    align-items: center;
    line-height: 48px;

    label {
      width: 100px;
      margin-right: 10px;
      // width: auto;
      text-align: right;
    }

    p {
      flex: 1;
      .el-select {
        width: 100%;
      }
    }
  }
  .tableCont {
    flex: 1;
    overflow: auto;
  }
  .container {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .el-date-editor--daterange.el-input,
  .el-date-editor--daterange.el-input__inner,
  .el-date-editor--timerange.el-input,
  .el-date-editor--timerange.el-input__inner {
    width: 100%;
  }
  /deep/.el-table th.el-table__cell > .cell {
    padding-left: 3px;
    padding-right: 3px;
  }
  /deep/ .el-table__cell {
    vertical-align: baseline;
  }
}
</style>
