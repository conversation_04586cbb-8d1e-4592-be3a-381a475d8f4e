<template>
  <div class="list">
    <div class="tableCont">
      <PublicTable
        :viewTableList.sync="viewTableList"
        :theads.sync="viewTheads"
        :columnWidth="columnWidth"
        @rowDblclick="rowDblclick"
        :isOnlySortShow="true"
        :columnSort="columnSort"
      >
        <template #sex="{ scope }">
          <div>
            {{ G_EnumList['Sex'][scope.row.sex] }}
          </div>
        </template>

        <template #companyName="{ scope }">
          <div class="container" :title="scope.row.companyName">
            {{ scope.row.companyName }}
          </div>
        </template>
        <template #peCls="{ scope }">
          <div>
            <span>{{ G_EnumList['PeCls'][scope.row.peCls] }}</span>
          </div>
        </template>
      </PublicTable>
    </div>
  </div>
</template>
<script>
import PublicTable from '../../components/publicTable.vue';
import { mapGetters } from 'vuex';
import { dataUtils } from '@/common';
export default {
  name: 'list',
  props: {
    // 表格数据
    viewTableList: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  components: {
    PublicTable
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_peClsList'])
  },
  data() {
    return {
      viewTheads: {
        peCls: '体检分类',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        companyName: '体检单位',
        tel: '手机号',
        regNo: '体检号',
        registerTime: '登记时间'
      },
      columnWidth: {
        registerTime: 180,
        regNo: 132,
        peCls: 110,
        companyName: 180,
        name: 75,
        sex: 60,
        age: 60,
        tel: 180
      },
      columnSort: ['peCls', 'name', 'sex', 'age', 'companyName', 'registerTime']
    };
  },
  created() {},
  mounted() {},
  methods: {
    rowDblclick(row) {
      this.$emit('getReportConclution', row.regNo);
      console.log('[ row ]-121', row);
    }
  }
};
</script>
<style lang="less" scoped>
.list {
  display: flex;
  width: 100%;
  height: 100%;
  border-radius: 4px;
  display: flex;
  color: #2d3436;
  flex-direction: column;
  .every_inp {
    display: flex;
    align-items: center;
    line-height: 48px;

    label {
      margin-right: 10px;
      // width: auto;
      text-align: right;
      width: 100px;
    }

    p {
      flex: 1;
      .el-select {
        width: 100%;
      }
    }
  }
  .tableCont {
    flex: 1;
    overflow: auto;
  }
  .container {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .el-date-editor--daterange.el-input,
  .el-date-editor--daterange.el-input__inner,
  .el-date-editor--timerange.el-input,
  .el-date-editor--timerange.el-input__inner {
    width: 100%;
  }
  /deep/.el-table th.el-table__cell > .cell {
    padding-left: 3px;
    padding-right: 3px;
  }
  /deep/ .el-table__cell {
    vertical-align: baseline;
  }
}
</style>
