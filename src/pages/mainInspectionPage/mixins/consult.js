export default {
  data() {
    return {
      checkComb: {},
      consultingInput: '',
      consultingShow: false
    };
  },
  methods: {
    // 咨询按钮点击
    consultingClick(comb) {
      console.log(comb);
      this.checkComb = comb;
      this.$ajax
        .post(this.$apiUrls.IsExistQueue, '', {
          query: {
            questionerCode: this.G_userInfo.codeOper.operatorCode,
            combCode: this.checkComb.combCode
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          if (returnData) {
            this.$message({
              message: '已咨询过该项目！',
              type: 'warning',
              showClose: true
            });
          } else {
            this.consultingShow = true;
          }
        });
    },
    // 取消
    cancel() {
      this.consultingShow = false;
    },
    // 咨询发送
    consultingSubmit() {
      if (!this.consultingInput) {
        this.$message({
          message: '请输入咨询内容！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let data = {
        regNo: this.reportConclution.patient.regNo,
        combCode: this.checkComb.combCode,
        combName: this.checkComb.combName,
        questionerCode: this.G_userInfo.codeOper.operatorCode,
        questionerName: this.G_userInfo.codeOper.name,
        questionContent: this.consultingInput,
        // replyerCode: "",
        replyerName: this.checkComb.doctorName
      };
      this.$ajax.post(this.$apiUrls.Question, data).then((r) => {
        console.log('r: ', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.$message({
          type: 'success',
          message: '咨询问题发送成功!',
          showClose: true
        });
        this.consultingShow = false;
      });
    }
  }
};
