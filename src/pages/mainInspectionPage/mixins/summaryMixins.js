import XEUtils from 'xe-utils';
import { _throttle } from '@/common/throttle.js';
import { dataUtils } from '../../../common';

export default {
  data() {
    return {
      tagSpanCurrentIdx: 0,
      suggestCurrentIdx: 0,
      tagSpanEditFlag: false,
      noteFocusFlag: false,
      isTabFlag: false,
      floatViewClass: ''
    };
  },
  methods: {
    // 添加自定义疾病
    addDefine() {
      this.$refs.addRight.doClose(); //关闭弹出
      let len = this.newSuggestions?.length;
      let diseaseId = Date.now();
      let addSuggestions = {
        id: diseaseId + '',
        sortIndex: len + 1,
        sugContent: '',
        deaTags: [
          {
            id: len + 1,
            diseaseCode: '',
            diseaseName: this.rightValue.trim(),
            bindSummTags: [],
            sortIndex: 0,
            diseaseNameRemark: ''
          }
        ]
      };
      this.newSuggestions.unshift(addSuggestions);
    },
    // 修改建议的疾病名称弹窗显示的回调
    popoverEnter(tag) {
      console.log(tag);
      this.rightValue = tag.diseaseName;
      if (tag.diseaseCode) {
        this.rightValue = tag.diseaseNameRemark;
      } else {
        this.rightValue = tag.diseaseName;
      }
      this.$nextTick(() => {
        this.sugTagSelDisease(tag);
        console.log(this.$refs.sugTagInp_Ref);
        this.$refs.sugTagInp_Ref.forEach((item) => {
          item.focus();
        });
      });
    },
    // 删除popover的Dom;
    removeTagPopover() {
      console.log(this.$refs.editSugTagPop_Ref);
      this.$refs.editSugTagPop_Ref.forEach((element) => {
        element.popperElm?.remove();
      });
    },
    // 确定修改
    sugTagConfirm(tag) {
      let rightValue = this.rightValue?.trim();
      if (rightValue === '' && !tag?.diseaseCode) {
        this.$message({
          message: '疾病名称不能为空！',
          type: 'warning'
        });
        return;
      }
      if (!tag?.diseaseCode) {
        const matchObj = XEUtils.findTree(this.rightAddData, (item) =>
          item.title.includes(rightValue)
        );
        if (matchObj) {
          this.$message({
            message: '请从列表中选择相似疾病！',
            type: 'warning'
          });
          return;
        }
      }

      if (tag.diseaseCode) {
        tag.diseaseNameRemark = rightValue;
      } else {
        tag.diseaseName = rightValue;
      }
      this.closePopover();
    },
    // 关闭弹窗
    closePopover() {
      this.$refs.editSugTagPop_Ref.forEach((element) => {
        element.doClose();
      });
    },
    // 替换全自定义的疾病名称
    replaceSugTag(row, tag, parent) {
      console.log(row, tag, parent);
      if (parent.deaTags.length === 1) {
        parent.sugContent = row.suggestContent;
      }
      tag.diseaseCode = row.diseaseCode;
      tag.diseaseName = row.diseaseName;
      this.closePopover();
    },
    // 修改疾病的搜索过滤
    sugTagSelDisease(tag) {
      console.log(tag);
      if (!!tag?.diseaseCode) return;
      if (this.rightValue?.trim()) {
        this.queryDiseaseSuggestions(1);
        this.isShowRight = true;
      } else {
        this.isShowRight = false;
      }
    },

    // 实时输入的回调
    inputFun() {
      let idxString = `${this.suggestCurrentIdx}_${this.tagSpanCurrentIdx}`;
      console.log(this.$refs[`tagSpanInput_Ref_${idxString}`]);
      this.$refs[`tagInput_Ref_${idxString}`][0].checkNum = null;
      this.$refs[`tagInput_Ref_${idxString}`][0].GetDiseasesByName(
        this.$refs[`tagSpanInput_Ref_${idxString}`][0].innerText
      );
    },
    // 输入框失焦回调
    tagSpanBlur(tag, item, tagIdx, index) {
      // if(!this.$refs.tagSpanInput_Ref) return;
      let idxString = `${this.suggestCurrentIdx}_${this.tagSpanCurrentIdx}`;
      let tagSpanInputText =
        this.$refs[`tagSpanInput_Ref_${idxString}`][0].innerText;
      if (!tagSpanInputText.trim()) {
        this.delRightMeal(item, tagIdx, index);
        return;
      }
      if (tagSpanInputText == tag.diseaseName && !this.noteFocusFlag) {
        this.tagSpanEditFlag = false;
        return;
      }
      if (this.noteFocusFlag && tagSpanInputText == tag.diseaseName) {
        setTimeout(() => {
          let noteDom = this.$refs[`noteSpanInput_Ref_${idxString}`][0];
          noteDom.focus();
          dataUtils.setCursorPosition(
            noteDom,
            this.isTabFlag ? noteDom.innerText.length : 0
          );
          this.isTabFlag = false;
        }, 10);
        this.noteFocusFlag = false;
        return;
      }
      this.tagSpanEditFlag = false;
      // setTimeout(()=>{
      tag.diseaseName = tagSpanInputText;
      this.EditSuggesstionDisease(tag, index, tagIdx);
      // },200)
    },
    // 疾病输入框的tab键回调
    tagSpanTab(event, tag, noteShow) {
      let currentLabel = event.target;
      if (tag.diseaseNameRemark || noteShow) {
        this.noteFocusFlag = true;
        this.isTabFlag = true;
        currentLabel.blur();
      }
    },
    // 疾病的enter键回调
    tagSpanEnter(tag) {
      let idxString = `${this.suggestCurrentIdx}_${this.tagSpanCurrentIdx}`;
      let row =
        this.$refs[`tagInput_Ref_${idxString}`][0].diseaseList[
          this.$refs[`tagInput_Ref_${idxString}`][0].checkNum
        ];
      console.log(row);
      if (!row) return;
      tag.diseaseCode = row.diseaseCode;
      tag.diseaseName = row.diseaseName;
      this.$refs[`tagSpanInput_Ref_${idxString}`][0].innerText =
        row.diseaseName;
    },
    // 建议疾病输入框聚焦的回调
    tagSpanFocus(sugIdx, tagIdx) {
      console.log(sugIdx, tagIdx);
      this.suggestCurrentIdx = sugIdx;
      this.tagSpanCurrentIdx = tagIdx;
    },
    // 编辑疾病建议
    EditSuggesstionDisease(tag, sugIdx, tagIdx) {
      this.$ajax.post(this.$apiUrls.EditSuggesstionDisease, tag).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        tag.bindSummTags = returnData.bindSummTags;
        tag.diseaseCode = returnData.diseaseCode;
        tag.id = returnData.id;
        this.$nextTick(() => {
          if (this.noteFocusFlag) {
            let idxString = `${sugIdx}_${tagIdx}`;
            let noteDom = this.$refs[`noteSpanInput_Ref_${idxString}`][0];
            noteDom.focus();
            dataUtils.setCursorPosition(
              noteDom,
              this.isTabFlag ? noteDom.innerText.length : 0
            );
            this.isTabFlag = false;
            this.noteFocusFlag = false;
          }
        });
      });
    },
    // 疾病列表的点击回调
    selectDisease(argument, tags) {
      let row = argument[0];
      console.log(row);
      this.$refs[
        `tagSpanInput_Ref_${this.suggestCurrentIdx}_${this.tagSpanCurrentIdx}`
      ][0].innerText = row.diseaseName;
      let tag = {
        ...tags[this.tagSpanCurrentIdx],
        diseaseCode: row.diseaseCode,
        diseaseName: row.diseaseName
      };
      tags[this.tagSpanCurrentIdx] = tag;
      // tag.diseaseCode = row.diseaseCode;
      // tag.diseaseName = row.diseaseName;
    },
    // 疾病输入框的上键
    tagSpanKeyUp(index, tagIdx) {
      let idxString = `${this.suggestCurrentIdx}_${this.tagSpanCurrentIdx}`;
      if (this.$refs[`tagInput_Ref_${idxString}`][0].checkNum === null)
        return (this.$refs[`tagInput_Ref_${idxString}`][0].checkNum = 0);
      if (this.$refs[`tagInput_Ref_${idxString}`][0].checkNum <= 0) return;
      this.$refs[`tagInput_Ref_${idxString}`][0].checkNum--;
    },
    // 疾病输入框的下键
    tagSpanKeyDown() {
      let idxString = `${this.suggestCurrentIdx}_${this.tagSpanCurrentIdx}`;
      if (this.$refs[`tagInput_Ref_${idxString}`][0].checkNum === null)
        return (this.$refs[`tagInput_Ref_${idxString}`][0].checkNum = 0);
      if (
        this.$refs[`tagInput_Ref_${idxString}`][0].checkNum + 1 >=
        this.$refs[`tagInput_Ref_${idxString}`][0].diseaseList.length
      )
        return;
      this.$refs[`tagInput_Ref_${idxString}`][0].checkNum++;
    },
    // 疾病输入框的右键回调
    async tagSpanRight(event, sugIdx, tagIdx, tag, noteShow) {
      console.log(event, sugIdx, tagIdx);
      if (event.ctrlKey) return;
      let currentLabel = event.target;
      let cursorIdx = await dataUtils.getCaretPosition(currentLabel);

      if (
        currentLabel.textContent.length === cursorIdx &&
        (tag.diseaseNameRemark || noteShow)
      ) {
        this.noteFocusFlag = true;
        currentLabel.blur();
      }
    },
    // 备注输入框的左键回调
    async noteSpanLeft(event, sugIdx, tagIdx) {
      console.log(event, sugIdx, tagIdx);
      let currentLabel = event.target;
      let cursorIdx = await dataUtils.getCaretPosition(currentLabel);
      let idxString = `${sugIdx}_${tagIdx}`;

      if (cursorIdx == 0) {
        currentLabel.blur();
        setTimeout(() => {
          let tagSpanDom = this.$refs[`tagSpanInput_Ref_${idxString}`][0];
          let len = tagSpanDom.textContent.length;
          tagSpanDom.focus();
          dataUtils.setCursorPosition(tagSpanDom, len);
        }, 10);
      }
    },
    // 添加备注
    addNote(item, sugIdx, tagIdx) {
      let idxString = `${sugIdx}_${tagIdx}`;
      this.tagSpanEditFlag = true;
      setTimeout(() => {
        let tagInputDom = this.$refs[`tagInput_Ref_${idxString}`][0];
        tagInputDom.noteShow = true;

        this.$nextTick(() => {
          console.log(this.$refs[`noteSpanInput_Ref_${idxString}`]);
          let noteDom = this.$refs[`noteSpanInput_Ref_${idxString}`][0];
          noteDom.focus();
        });
      }, 300);
    },
    // 备注输入框失焦的回调
    noteBlur(tag, sugIdx, tagIdx) {
      let idxString = `${sugIdx}_${tagIdx}`;
      let noteDom = this.$refs[`noteSpanInput_Ref_${idxString}`][0];
      tag.diseaseNameRemark = noteDom.innerText;
      if (tag.diseaseNameRemark.trim() == '') {
        let tagInputDom = this.$refs[`tagInput_Ref_${idxString}`][0];
        tagInputDom.noteShow = false;
      }
    },
    // 疾病输入框的鼠标覆盖事件回调
    tagSpanMouseenter(e) {
      let parentCoord = this.$refs.rightCont_Ref.getBoundingClientRect();
      let tagCoord = e.target.getBoundingClientRect();
      if (tagCoord.top - parentCoord.top >= 25) {
        this.floatViewClass = '';
      } else {
        this.floatViewClass = 'float_view_bottom';
      }
    },
    // 编辑疾病
    editBtn(index, tagIdx) {
      this.tagSpanEditFlag = true;
      let idxString = `${index}_${tagIdx}`;
      setTimeout(() => {
        let tagSpanDom = this.$refs[`tagSpanInput_Ref_${idxString}`][0];
        let len = tagSpanDom.textContent.length;
        tagSpanDom.focus();
        dataUtils.setCursorPosition(tagSpanDom, len);
      }, 200);
    }
  },
  created() {
    this.tagSpanChange = _throttle(this.inputFun, 500);
  },
  mounted() {
    this.addDiseaseClick();
  }
};
