import PublicTable from '@/components/publicTable.vue';
import Consult from '../consult.vue';
import MainBtnCom from '..//mainBtnCom.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
import MainSearch from '../mainSearch.vue';
import SummaryCom from '../summaryCom.vue';
import Distribution from '../distribution.vue';
import NoDistribution from '../noDistribution.vue';
import ResultEntry from '../../inspectIn/resultEntry.vue';
import HistoryReport from '../../inspectIn/components/doctorWorkStation/historyReport.vue';
import moment from 'moment';
export default {
  name: 'process',
  components: {
    PublicTable,
    MainBtnCom,
    MainSearch,
    Consult,
    SummaryCom,
    Distribution,
    NoDistribution,
    ResultEntry,
    HistoryReport,
    ResultEntry
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_userInfo'])
  },
  data() {
    return {
      mainInspectionFlag: false,
      activeMain3: 'main2',
      searchDialog: false,
      consultDialog: false,
      tjCls: '',
      activeName: 'first',
      viewTableList1: [],
      viewTableList2: [],
      viewTableList3: [],
      viewTableListCopy1: [],
      viewTableListCopy2: [],
      viewTableListCopy3: [],
      isMain: 2, //2表示未检，3表示已主检
      conclusionList: [],
      conclusionLists: [],
      conclusionLists_fixed: [],
      keyword: '', //体检号/姓名,
      returnCheckedId: null, //撤回主检id
      reportConclution: {
        patient: {}, //个人信息
        summary: {}, //综述
        suggestions: [] //建议
      },
      checkStatus: 1, //主检的各个状态（1：待主检；2：已主检；3：已审核）
      hisDrawer: false,
      dateVal2: [],
      dateVal3: [],
      showChoiceRegister: false, // 是否显示关键字检索列表
      noDistributionQueryTimeType2: 3,
      noDistributionLoading2: false,
      noDistributionQueryTimeType3: 4,
      noDistributionLoading3: false
    };
  },
  created() {
    let startDate = moment().add(-7, 'd').format('YYYY-MM-DD');
    let endDate = moment().startOf('day').format('YYYY-MM-DD');
    this.dateVal2 = [startDate, endDate];
    this.dateVal3 = [startDate, endDate];
  },
  mounted() {
    this.getConclusion();
    this.getPatientList();
  },
  methods: {
    getPatientList() {
      this.getPatientList2();
      this.getPatientList3();
    },
    getPatientList2() {
      let data = {
        operatorCode: this.G_userInfo.codeOper.operatorCode,
        checkStatus: 2,
        beginDate: this.dateVal2[0],
        endDate: this.dateVal2[1],
        isOccupation: false,
        queryTimeType: this.noDistributionQueryTimeType2
      };
      this.noDistributionLoading2 = true;
      this.$ajax
        .post(this.$apiUrls.GetPatientList4NotAllocateV2, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.viewTableList2 = returnData;
          this.viewTableListCopy2 = returnData;
        })
        .finally((_) => {
          this.noDistributionLoading2 = false;
        });
    },
    getPatientList3() {
      let data = {
        operatorCode: this.G_userInfo.codeOper.operatorCode,
        checkStatus: 3,
        beginDate: this.dateVal3[0],
        endDate: this.dateVal3[1],
        isOccupation: false,
        queryTimeType: this.noDistributionQueryTimeType3
      };
      this.noDistributionLoading3 = true;
      this.$ajax
        .post(this.$apiUrls.GetPatientList4NotAllocateV2, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.viewTableList3 = returnData;
          this.viewTableListCopy3 = returnData;
        })
        .finally((_) => {
          this.noDistributionLoading3 = false;
        });
    },
    //主检优先分配
    precedence() {
      if (!this.reportConclution.patient.regNo) {
        this.$message({
          message: '没有体检号无法优先分配!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$ajax
        .post(this.$apiUrls.PriorityAllocation, [], {
          query: {
            operatorCode: this.G_userInfo.codeOper.operatorCode,
            regNo: this.reportConclution.patient.regNo
          }
        })
        .then((r) => {
          let { success } = r.data;
          if (!success) return;
          this.$message({
            message: '主检优先分配成功!',
            type: 'success',
            showClose: true
          });
        });
    },
    /**
     * @author: justin
     * @description: 关键字查询
     * @return {*}
     */
    async searchByKeyword() {
      const that = this;
      if (dataUtils.isRegNo(that.keyword)) {
        that.searchByRegNo(that.keyword);
      } else {
        // 关键字查询列表
        that.$refs.choicePeReisterDialog.getReportPeRegisters(false);
      }
    },

    /**
     * @author: justin
     * @description: 根据体检号直接获取报告结论
     * @param {*} regNo 体检号
     * @return {*}
     */
    searchByRegNo(regNo) {
      // 直接获取报告结论
      this.getReportConclution(regNo).then((r) => {
        this.keyword = '';
        if (this.activeName == 'third') {
          this.$refs.resultEntry_Ref.headerInfo = r.patient;
          this.$refs.resultEntry_Ref.getNavList();
        }
      });
    },
    //清空操作
    clearSearch() {
      this.reportConclution = {
        patient: {}, //个人信息
        summary: {}, //综述
        suggestions: [] //建议
      };
      this.projectResult = [];
      this.suggestions = [];
      this.$refs.summaryCom_Ref.summaryList = [];
      this.ImgTextList = [];
    },
    //获取报告结论
    getReportConclution(regNo) {
      this.projectResult = [];
      this.suggestions = [];
      this.$refs.summaryCom_Ref.summaryList = [];
      this.$refs.summaryCom_Ref.newSuggestions = [];
      this.$refs.summaryCom_Ref.fixed_newSuggestions = [];
      this.$refs.summaryCom_Ref.mergeObj = {};
      return new Promise((resolve, reject) => {
        if (!regNo) {
          this.$message({
            message: '没有体检号无法查询!',
            type: 'warning',
            showClose: true
          });
          return;
        }
        this.$refs.popoverRef.doClose();
        this.searchDialog = false;
        //this.regNo = regNo; //双击列表把体检号赋值文本框
        this.GetSimpleItemResult(regNo);
        this.$ajax
          .post(this.$apiUrls.GetReportConclusionV2, [], {
            query: { regNo: regNo, oper: 1, isOccupation: false }
          })
          .then((r) => {
            this.$refs.summaryCom_Ref.recordFlag = true;
            this.$refs.summaryCom_Ref.record = [];
            let { success, returnData } = r.data;
            if (!success) return;
            if (returnData.patient.peStatus === 4) {
              this.isExamine = 2;
            } else {
              this.isExamine = 1;
            }
            this.reportConclution = returnData;
            this.returnCheckedId = returnData?.returnCheckedId || null;
            this.activeName = 'first';
            this.suggestions = dataUtils.deepCopy(returnData.suggestions);
            this.$refs.summaryCom_Ref.newSuggestions = dataUtils.deepCopy(
              returnData.suggestions
            );
            this.$refs.summaryCom_Ref.fixed_newSuggestions = dataUtils.deepCopy(
              returnData.suggestions
            );
            this.isMain = returnData.patient.peStatus;
            this.$refs.summaryCom_Ref.summaryList = dataUtils.deepCopy(
              returnData.combSummarys
            );
            console.log('[ this.reportConclution ]-354', this.reportConclution);
            this.getFollowUpState();
            this.ReadMedicalAdvice();
            resolve(returnData);
          });
        this.getImgTextList(regNo);
      });
    },
    getConclusion() {
      this.$ajax
        .post(this.$apiUrls.RD_CodeConclusionTemplate + '/Read', [])
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.conclusionLists_fixed = returnData || [];
          this.conclusionLists = returnData || [];
        });
    },
    //保存主检
    mainTest() {
      //主检医生
      // this.reportConclution.checkDoctorCode =
      //   this.G_userInfo.codeOper.operatorCode;
      // this.reportConclution.checkDoctorName = this.G_userInfo.codeOper.name;
      this.reportConclution.summary.sumCombs =
        this.$refs.summaryCom.newSumCombs;
      this.reportConclution.suggestions = this.$refs.summaryComs.newSuggestions;
      console.log('[ this.reportConclution) ]-433', this.reportConclution);
      if (!this.reportConclution.conclusion) {
        this.reportConclution.conclusion = '';
      }
      this.$ajax
        .post(this.$apiUrls.SaveReportConclusion, this.reportConclution)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$message({
            message: '保存成功!',
            type: 'success',
            showClose: true
          });
          this.isMain = 3;
          this.getReportConclution(this.reportConclution.patient.regNo); //刷新数据
        });
    },
    // 取消保存主检
    cancelMainTest() {
      if (!this.reportConclution.patient.regNo) {
        this.$message({
          message: '请先选择要取消保存的结论!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$ajax
        .post(this.$apiUrls.CancelReportConclusion, [], {
          query: { regNo: this.reportConclution.patient.regNo }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$message({
            message: '取消主检成功!',
            type: 'success',
            showClose: true
          });
          this.isMain = 2;
          this.getReportConclution(this.reportConclution.patient.regNo); //刷新数据
        });
    },
    mainCheckList() {},
    //主检列表点击
    mainClick3(tab, event) {
      console.log('[ this.activeMain3 ]-520', this.activeMain3);
      console.log(tab, event);
    },
    //体检分类过滤
    filterCls(peCls) {
      if (this.activeMain3 == 'main1') {
        this.viewTableList1 = this.viewTableListCopy1.filter((item) => {
          return peCls === '' ? true : item.peCls == peCls;
        });
      } else if (this.activeMain3 == 'main2') {
        this.viewTableList2 = this.viewTableListCopy2.filter((item) => {
          return peCls === '' ? true : item.peCls == peCls;
        });
      } else {
        this.viewTableList3 = this.viewTableListCopy3.filter((item) => {
          return peCls === '' ? true : item.peCls == peCls;
        });
      }
    },
    handleClick(tab, event) {
      console.log(tab, event);
      this.$refs.summaryComs.newSuggestions = this.reportConclution.suggestions;
      if (this.activeName == 'third') {
        this.$refs.resultEntry_Ref.headerInfo = this.reportConclution.patient;
        this.$refs.resultEntry_Ref.getNavList();
      }
    },
    search() {
      this.searchDialog = true;
    },
    searchDialogClose() {
      this.searchDialog = false;
    },
    consultList() {
      this.consultDialog = true;
    },
    consultDialogClose() {
      this.consultDialog = false;
    },
    // 生成结论
    createReport() {
      if (!this.reportConclution.patient.regNo) {
        this.$message({
          message: '没有体检号无法生成结论!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.isMain !== 2) {
        this.$message({
          message: '已主检，无法重新生成结论!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm('是否确定要生成结论?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.generateSummarySuggestions();
        })
        .catch(() => {});
    },
    //生成结论
    generateSummarySuggestions() {
      this.suggestions = [];
      this.$refs.summaryCom_Ref.summaryList = [];
      this.$refs.summaryCom_Ref.newSuggestions = [];
      this.$refs.summaryCom_Ref.fixed_newSuggestions = [];
      this.$refs.summaryCom_Ref.mergeObj = {};
      this.$ajax
        .post(this.$apiUrls.GenerateSummarySuggestions, [], {
          query: { regNo: this.reportConclution.patient.regNo }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$refs.summaryCom_Ref.newSuggestions = returnData.suggestions;
          this.$refs.summaryCom_Ref.summaryList = returnData.comSummarys;
          this.$message({
            type: 'success',
            message: '已重新生成结论!'
          });
        });
    },
    //关闭历史报告弹出
    hisDrawerClose() {
      this.hisDrawer = false;
    },
    historyReport() {
      if (!this.reportConclution.patient.regNo) {
        this.$message({
          message: '请先点击要查看历史报告的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.hisDrawer = true;
    },
    // 队列的双击回调
    patientListDbClick(row) {
      this.getReportConclution(row.regNo);
    },
    /**
     * @author: justin
     * @description: 显示选择身份信息
     * @return {*}
     */
    showChoiceRegisterHandle() {
      this.showChoiceRegister = true;
    },

    /**
     * @author: justin
     * @description: 确认选择身份信息
     * @param {*} data 数据
     * @return {*}
     */
    confrimChoiceRegDataHandle(data) {
      if (!data) {
        this.$message.warning('请选择身份信息!');
        return;
      }

      this.cancelChoiceRegDataHandle();
      this.searchByRegNo(data.regNo);
    },

    /**
     * @author: justin
     * @description: 取消选择身份信息
     * @return {*}
     */
    cancelChoiceRegDataHandle() {
      this.showChoiceRegister = false;
      this.keyword = '';
    },
    // 取消撤销
    cancelReturnChecked() {
      this.$ajax
        .paramsPost(this.$apiUrls.CancelReturnChecked, {
          id: this.returnCheckedId
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$message.success('撤回成功!');
          this.returnCheckedId = null;
        });
    }
  }
};
