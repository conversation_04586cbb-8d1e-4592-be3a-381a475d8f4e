export default {
  computed: {
    C_route() {
      return this.$route.name == 'mainInspection' ? false : true;
    }
  },
  data() {
    return {
      timerName: '', //定时器,
      backoutThead: {
        regNo: '体检号',
        questionerName: '咨询医生',
        questionerTime: '咨询时间',
        questionContent: '咨询内容'
      },
      backoutWidth: {
        regNo: '150',
        questionerName: '150',
        questionerTime: '200'
      },
      backoutList: [
        {
          regNo: '体检号',
          questionerName: '咨询医生',
          questionerTime: '咨询时间',
          questionContent: '咨询内容',
          replyContent: '',
          backoutPopoverShow: false
        },
        {
          regNo: '体检号',
          questionerName: '咨询医生',
          questionerTime: '咨询时间',
          questionContent: '咨询内容',
          replyContent: '',
          backoutPopoverShow: false
        }
      ],
      backoutShow: false,
      unlockShow: false, //主检解锁是否弹出
      tableData: [],
      locksTimer: ''
    };
  },
  methods: {
    // 获取未答复的撤回主检的信息
    GetNotReplyReturn() {
      let datas = {
        replyerCode: this.G_userInfo.codeOper.operatorCode,
        isOccupation: this.C_route
      };
      console.log(111111);
      this.$ajax
        .paramsPost(this.$apiUrls.GetNotReplyReturnNew, datas)
        .then((r) => {
          console.log(7777777777, r);
          let { success, returnData } = r.data;
          if (!success) return;

          returnData?.map((item) => {
            item.backoutPopoverShow = false;
          });
          this.backoutList = returnData || [];
          if (returnData.length == 0) {
            this.backoutShow = false;
          }
          if (returnData.length > 0) {
            this.backoutShow = true;
          }
        });
    },
    // replyBtn(row){
    //     row.backoutPopoverShow = true;
    //     console.log(row,this.backoutList);
    // },
    // 回复
    reply(row, isMainInspection = false) {
      if (!row.replyContent) {
        this.$message({
          message: '请输入回复内容',
          type: 'warning',
          showClose: true
        });
        return;
      }
      row.isOccupation = this.C_route;
      let datas = [row];
      console.log(datas);
      this.$ajax.post(this.$apiUrls.ReplyReturnNew, datas).then((r) => {
        let { success } = r.data;
        if (!success) return;
        row.backoutPopoverShow = false;
        this.$message({
          message: '回复成功！',
          type: 'success',
          showClose: true
        });
        if (isMainInspection) this.getReportConclution(row.regNo);
        this.GetNotReplyReturn();
      });
    },
    //获取列表
    GetPeQuestionlocks() {
      this.$ajax
        .post(this.$apiUrls.GetPeQuestionlocks, '', {
          query: { operatorCode: this.G_userInfo.codeOper.operatorCode }
        })
        .then((r) => {
          console.log('r111: ', r);
          const { returnData, success } = r.data;
          if (!success) {
            return;
          }
          this.tableData = returnData || [];
          if (this.tableData.length == 0) {
            this.unlockShow = false;
          }
          if (this.tableData.length > 0) {
            this.unlockShow = true;
          }
        });
    },
    //双击解锁
    dbUnlock(row) {
      this.$ajax
        .post(this.$apiUrls.UnlockQuestion, '', {
          query: { questionId: row.id }
        })
        .then((r) => {
          console.log('r111: ', r);
          const { returnData, success } = r.data;
          if (!success) return;
          this.GetPeQuestionlocks();
          this.$message({
            message: '解锁成功!',
            type: 'success',
            showClose: true
          });
          return;
        });
    }
  },
  mounted() {},
  created() {
    this.GetNotReplyReturn();
    this.timerName = setInterval(this.GetNotReplyReturn, 60000);
    this.GetPeQuestionlocks();
    this.locksTimer = setInterval(this.GetPeQuestionlocks, 60000);
  },

  // 组件激活
  activated() {
    if (this.timerName) return;
    this.timerName = setInterval(this.GetNotReplyReturn, 60000);
    if (this.locksTimer) return;
    this.locksTimer = setInterval(this.GetPeQuestionlocks, 60000);
  },
  // 组件失活
  deactivated() {
    clearInterval(this.timerName);
    this.timerName = '';
    clearInterval(this.locksTimer);
    this.locksTimer = '';
  },
  // 销毁
  destroyed() {
    clearInterval(this.timerName);
    this.timerName = '';
    clearInterval(this.locksTimer);
    this.locksTimer = '';
  }
};
