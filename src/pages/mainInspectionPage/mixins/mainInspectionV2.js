export default {
  data() {
    return {
      projectResult: [], //项目结果
      suggestions: [], //建议列表
      activeNames: [],
      positiveResultDialog: false,
      isPositiveFlag: false,
      followUpShow: false,
      anchorPointShow: false,
      isHasFollowUp: false, //是否已随访
      foldFlag: false,
      showPureToneAudiometry: false,
      PureToneAudiometryBtnShow: false // 是否显示纯音测听的按钮
    };
  },
  methods: {
    getAuditoryResult() {
      this.PureToneAudiometryBtnShow = false;
      this.$ajax
        .paramsPost(this.$apiUrls.ReadAuditoryResult, {
          regNo: this.reportConclution.patient?.regNo
        })
        .then((res) => {
          console.log(res);
          let { success, returnData } = res.data;
          if (!success || !returnData) return;
          this.PureToneAudiometryBtnShow = true;
        })
        .finally((_) => {});
    },
    // 折叠项目结果
    foldClick() {
      this.foldFlag = !this.foldFlag;
    },
    // 获取项目结果
    GetSimpleItemResult(regNo) {
      let activeNames = [];
      this.isPositiveFlag = false;
      this.$ajax
        .paramsPost(this.$apiUrls.GetSimpleItemResultNew, {
          regNo,
          isOccupation: true
        })
        .then((r) => {
          console.log(1111111111111111, r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.projectResult = returnData || [];
          this.projectResult.map((item) => {
            activeNames.push(item.combCode);
          });
          this.activeNames = activeNames;
        });
      // 获取重大阳性列表
      this.$ajax
        .paramsPost(this.$apiUrls.PositiveResultReview, { regNo })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          if (returnData?.selectedData?.length > 0) {
            this.isPositiveFlag = true;
          }
        });
    },
    // 重大阳性按钮的点击回调
    PositiveResultShow() {
      if (!this.reportConclution.patient?.regNo) {
        this.$message({
          message: '请先选择体检人!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.positiveResultDialog = true;
    },
    handleChange(val) {
      console.log(val);
    },
    followUpClose(flag) {
      console.log(flag);
      this.followUpShow = flag;
    },
    // 添加随访的弹窗显示
    followUpClick() {
      if (!this.reportConclution.patient.regNo) {
        this.$message({
          message: '请先选择需要随访的人！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.followUpShow = true;
    },
    // 显示锚点弹窗
    anchorPointEnter(flag) {
      this.anchorPointShow = flag;
    },
    // anchorPointClick(idName){
    //     let dom = document.querySelectorAll(idName);
    //     if(window.getComputedStyle(this.$refs.project_Ref).display == 'none'){
    //         dom[1].scrollIntoView({ behavior: "smooth" });
    //         return;
    //     }
    //     dom[0].scrollIntoView({ behavior: "smooth" });
    // },
    anchorPointClick(idName) {
      const mainContainer = this.$refs.project_Ref;

      const targets = [
        ...(mainContainer?.querySelectorAll(idName) || []),
        ...document.querySelectorAll(idName)
      ].filter(Boolean);

      if (targets.length > 0) {
        const target =
          targets.find(
            (el) => window.getComputedStyle(el).display !== 'none'
          ) || targets[0];

        target.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest'
        });
      } else {
        console.warn('锚点元素未找到:', idName);
        this.$message.warning('当前视图下无法定位该项目');
      }
    },
    // 获取随访状态
    getFollowUpState() {
      let datas = {
        regNo: this.reportConclution?.patient?.regNo
      };
      this.$ajax.paramsPost(this.$apiUrls.HasFollowUp, datas).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.isHasFollowUp = returnData;
      });
    },
    // 跳转随访登记页面
    followUpRecodeClick() {
      this.$router.push({
        name: 'followUpReg',
        query: {
          regNo: this.reportConclution?.patient?.regNo
        }
      });
    },
    // 监听窗口大小的变化
    handleResize() {
      console.log(
        'Window resized. New width:',
        window.innerWidth,
        'New height:',
        window.innerHeight
      );
      if (window.innerWidth <= 1440) {
        this.foldFlag = true;
        return;
      }
      this.foldFlag = false;
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.handleResize();
      window.addEventListener('resize', this.handleResize);
    });
  },
  activated() {
    this.$nextTick(() => {
      this.handleResize();
      window.addEventListener('resize', this.handleResize);
    });
  },
  deactivated() {
    window.removeEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
  }
};
