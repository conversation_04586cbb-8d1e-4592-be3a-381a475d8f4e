export default {
  data() {
    return {
      projectResult: [], //项目结果
      suggestions: [], //建议列表
      activeNames: [],
      positiveResultDialog: false,
      isPositiveFlag: false,
      followUpShow: false,
      anchorPointShow: false,
      isHasFollowUp: false, //是否已随访
      showPull: false,
      foldFlag: false,
      guidanceDialogShow: false,
      guidanceForm: {
        specialistVisit: '',
        regularReview: ''
      },
      guidanceDialogLoading: false
    };
  },
  methods: {
    // 折叠项目结果
    foldClick() {
      this.foldFlag = !this.foldFlag;
    },
    // 本次体检结论的聚焦回调
    focusEvent() {
      this.conclusionLists = this.conclusionLists_fixed;
      this.showPull = true;
    },
    // 本次体检结论的实时输入回调
    keyupEvent() {
      this.conclusionLists = this.reportConclution.conclusion
        ? this.conclusionLists_fixed.filter(
            (item) => item.result.indexOf(this.reportConclution.conclusion) > -1
          )
        : this.conclusionLists_fixed;
    },
    // 本次体检结论弹窗的选择回调
    selectEvent(item) {
      this.reportConclution.conclusion = item.result;
      this.showPull = false;
      setTimeout(() => {
        this.conclusionLists = this.conclusionLists_fixed;
      }, 150);
    },
    // 页面点击回调
    pageClick() {
      this.showPull = false;
    },
    // 获取项目结果
    GetSimpleItemResult(regNo) {
      let activeNames = [];
      this.isPositiveFlag = false;
      this.$ajax
        .paramsPost(this.$apiUrls.GetSimpleItemResultNew, {
          regNo,
          isOccupation: false
        })
        .then((r) => {
          console.log(1111111111111111, r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.projectResult = returnData || [];
          this.projectResult.map((item) => {
            activeNames.push(item.combCode);
          });
          this.activeNames = activeNames;
        });
      // 获取重大阳性列表
      this.$ajax
        .paramsPost(this.$apiUrls.PositiveResultReview, { regNo })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          if (returnData?.selectedData?.length > 0) {
            this.isPositiveFlag = true;
          }
        });
    },
    // 重大阳性按钮的点击回调
    PositiveResultShow() {
      if (!this.reportConclution.patient?.regNo) {
        this.$message({
          message: '请先选择体检人!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.positiveResultDialog = true;
    },
    handleChange(val) {
      console.log(val);
    },
    followUpClose(flag) {
      console.log(flag);
      this.followUpShow = flag;
    },
    // 添加随访的弹窗显示
    followUpClick() {
      if (!this.reportConclution.patient.regNo) {
        this.$message({
          message: '请先选择需要随访的人！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.followUpShow = true;
    },
    // 显示锚点弹窗
    anchorPointEnter(flag) {
      this.anchorPointShow = flag;
    },
    anchorPointClick(idName) {
      document.querySelector(idName).scrollIntoView({ behavior: 'smooth' });
    },
    // 获取随访状态
    getFollowUpState() {
      let datas = {
        regNo: this.reportConclution?.patient?.regNo
      };
      this.$ajax.paramsPost(this.$apiUrls.HasFollowUp, datas).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.isHasFollowUp = returnData;
      });
    },
    // 跳转随访登记页面
    followUpRecodeClick() {
      this.$router.push({
        name: 'followUpReg',
        query: {
          regNo: this.reportConclution?.patient?.regNo
        }
      });
    },
    // 监听窗口大小的变化
    handleResize() {
      console.log(
        'Window resized. New width:',
        window.innerWidth,
        'New height:',
        window.innerHeight
      );
      if (window.innerWidth <= 1440) {
        this.foldFlag = true;
        return;
      }
      this.foldFlag = false;
    },
    // 医疗指导按钮点击回调
    guidance() {
      if (!this.reportConclution.patient.regNo) {
        this.$message({
          message: '请先选择体检人！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.guidanceDialogShow = true;
      this.ReadMedicalAdvice();
    },
    // 获取医疗指导
    ReadMedicalAdvice() {
      this.$ajax
        .paramsPost(this.$apiUrls.ReadMedicalAdvice, {
          regNo: this.reportConclution?.patient?.regNo
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.guidanceForm = {
            regularReview: returnData?.regularReview ?? '',
            specialistVisit: returnData?.specialistVisit ?? ''
          };
        });
    },
    // 医疗指导的保存
    guidanceSubmit() {
      console.log(this.guidanceForm);
      let datas = {
        regNo: this.reportConclution.patient.regNo,
        ...this.guidanceForm
      };
      console.log(datas);

      this.guidanceDialogLoading = true;
      this.$ajax
        .post(this.$apiUrls.SaveMedicalAdvice, datas)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$message({
            message: '医疗指导保存成功！',
            type: 'success',
            showClose: true
          });
        })
        .finally(() => {
          this.guidanceDialogShow = false;
          this.guidanceDialogLoading = false;
        });
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.handleResize();
      window.addEventListener('resize', this.handleResize);
    });
  },
  activated() {
    this.$nextTick(() => {
      this.handleResize();
      window.addEventListener('resize', this.handleResize);
    });
  },
  deactivated() {
    window.removeEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
  }
};
