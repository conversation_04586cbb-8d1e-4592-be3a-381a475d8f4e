import PublicTable from '@/components/publicTable.vue';
import Consult from '../consult.vue';
import MainBtnCom from '..//mainBtnCom.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
import MainSearch from '../mainSearch.vue';
import SummaryCom from '../summaryCom.vue';
import Distribution from '../distribution.vue';
import NoDistribution from '../noDistribution.vue';
import ResultEntry from '../../inspectIn/resultEntry.vue';
import HistoryReport from '../../inspectIn/components/doctorWorkStation/historyReport.vue';
import moment from 'moment';
export default {
  name: 'process',
  components: {
    PublicTable,
    MainBtnCom,
    MainSearch,
    Consult,
    SummaryCom,
    Distribution,
    NoDistribution,
    ResultEntry,
    HistoryReport,
    ResultEntry
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_userInfo'])
  },
  data() {
    return {
      mainInspectionFlag: false,
      activeMain3: 'main2',
      searchDialog: false,
      consultDialog: false,
      tjCls: '',
      activeName: 'first',
      viewTableList1: [],
      viewTableList2: [],
      viewTableList3: [],
      viewTableListCopy1: [],
      viewTableListCopy2: [],
      viewTableListCopy3: [],
      isMain: 2, //2表示未检，3表示已主检
      conclusionList: [],
      conclusionLists: [],
      keyword: '', //体检号/姓名,
      reportConclution: {
        patient: {}, //个人信息
        summary: {}, //综述
        suggestions: [] //建议
      },
      checkStatus: 1, //主检的各个状态（1：待主检；2：已主检；3：已审核）
      hisDrawer: false,
      dateVal2: [],
      dateVal3: [],
      showChoiceRegister: false, // 是否显示关键字检索列表
      noDistributionQueryTimeType2: 3,
      noDistributionLoading2: false,
      noDistributionQueryTimeType3: 4,
      noDistributionLoading3: false
    };
  },
  created() {
    let startDate = moment().add(-7, 'd').format('YYYY-MM-DD');
    let endDate = moment().startOf('day').format('YYYY-MM-DD');
    this.dateVal2 = [startDate, endDate];
    this.dateVal3 = [startDate, endDate];
  },
  mounted() {
    // this.getConclusion();
    this.getPatientList();
  },
  methods: {
    getPatientList() {
      this.getPatientList2();
      this.getPatientList3();
    },
    getPatientList2() {
      let data = {
        operatorCode: this.G_userInfo.codeOper.operatorCode,
        checkStatus: 2,
        beginDate: this.dateVal2[0],
        endDate: this.dateVal2[1],
        isOccupation: true,
        queryTimeType: this.noDistributionQueryTimeType2,
        hazardousCodes: this.hazardousCodes2,
        jobStatus: this.jobStatus2
      };
      this.noDistributionLoading2 = true;
      this.$ajax
        .post(this.$apiUrls.GetPatientList4NotAllocateV2, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.viewTableList2 = returnData;
          this.viewTableListCopy2 = returnData;
        })
        .finally((_) => {
          this.noDistributionLoading2 = false;
        });
    },
    getPatientList3() {
      let data = {
        operatorCode: this.G_userInfo.codeOper.operatorCode,
        checkStatus: 3,
        beginDate: this.dateVal3[0],
        endDate: this.dateVal3[1],
        isOccupation: true,
        queryTimeType: this.noDistributionQueryTimeType3,
        hazardousCodes: this.hazardousCodes3,
        jobStatus: this.jobStatus3
      };
      this.noDistributionLoading3 = true;
      this.$ajax
        .post(this.$apiUrls.GetPatientList4NotAllocateV2, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.viewTableList3 = returnData;
          this.viewTableListCopy3 = returnData;
        })
        .finally((_) => {
          this.noDistributionLoading3 = false;
        });
    },
    //主检优先分配
    precedence() {
      if (!this.reportConclution.patient.regNo) {
        this.$message({
          message: '没有体检号无法优先分配!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$ajax
        .post(this.$apiUrls.PriorityAllocation, [], {
          query: {
            operatorCode: this.G_userInfo.codeOper.operatorCode,
            regNo: this.reportConclution.patient.regNo
          }
        })
        .then((r) => {
          let { success } = r.data;
          if (!success) return;
          this.$message({
            message: '主检优先分配成功!',
            type: 'success',
            showClose: true
          });
        });
    },
    /**
     * @author: justin
     * @description: 关键字查询
     * @return {*}
     */
    async searchByKeyword() {
      const that = this;
      if (dataUtils.isRegNo(that.keyword)) {
        that.searchByRegNo(that.keyword);
      } else {
        // 关键字查询列表
        that.$refs.choicePeReisterDialog.getReportPeRegisters(true);
      }
    },

    /**
     * @author: justin
     * @description: 根据体检号直接获取报告结论
     * @param {*} regNo 体检号
     * @return {*}
     */
    searchByRegNo(regNo) {
      // 直接获取报告结论
      this.getReportConclution(regNo).then((r) => {
        this.keyword = '';
        if (this.activeName == 'third') {
          this.$refs.resultEntry_Ref.headerInfo = r.patient;
          this.$refs.resultEntry_Ref.getNavList();
        }
      });
    },
    //清空操作
    clearSearch() {
      this.reportConclution = {
        patient: {}, //个人信息
        summary: {}, //综述
        suggestions: [] //建议
      };
      this.projectResult = [];
      this.suggestions = [];
      this.$refs.summaryCom_Ref.summaryList = [];
      this.ImgTextList = [];
    },

    //获取报告结论
    getReportConclution(regNo) {
      this.projectResult = [];
      this.suggestions = [];
      this.$refs.summaryCom_Ref.summaryList = [];
      this.$refs.summaryCom_Ref.newSuggestions = [];
      this.$refs.summaryCom_Ref.fixed_newSuggestions = [];
      this.$refs.summaryCom_Ref.mergeObj = {};
      return new Promise((resolve, reject) => {
        if (!regNo) {
          this.$message({
            message: '没有体检号无法查询!',
            type: 'warning',
            showClose: true
          });
          return;
        }
        this.$refs.popoverRef.doClose();
        this.searchDialog = false;
        //this.regNo = regNo; //双击列表把体检号赋值文本框
        this.GetSimpleItemResult(regNo);
        this.$ajax
          .post(this.$apiUrls.GetReportConclusionV2, [], {
            query: { regNo: regNo, oper: 1, isOccupation: true }
          })
          .then((r) => {
            this.$refs.summaryCom_Ref.recordFlag = true;
            this.$refs.summaryCom_Ref.record = [];
            let { success, returnData } = r.data;
            if (!success) return;
            if (returnData.patient.peStatus === 4) {
              this.isExamine = 2;
            } else {
              this.isExamine = 1;
            }
            this.reportConclution = returnData;
            this.activeName = 'first';
            this.suggestions = dataUtils.deepCopy(returnData.suggestions);
            this.$refs.summaryCom_Ref.newSuggestions = dataUtils.deepCopy(
              returnData.suggestions
            );
            this.$refs.summaryCom_Ref.fixed_newSuggestions = dataUtils.deepCopy(
              returnData.suggestions
            );
            this.isMain = returnData.patient.peStatus;
            this.$refs.summaryCom_Ref.summaryList = dataUtils.deepCopy(
              returnData.combSummarys
            );
            console.log('[ this.reportConclution ]-354', this.reportConclution);
            this.getAuditoryResult();
            this.getFollowUpState();
            this.GetQuestionDataByRegNo(this.reportConclution.patient);
            resolve(returnData);
          });
        this.getImgTextList(regNo);
      });
    },
    // getConclusion() {
    //   this.$ajax
    //     .post(this.$apiUrls.RD_CodeConclusionTemplate + "/Read", [])
    //     .then((r) => {
    //
    //       let { success, returnData } = r.data;
    //       if (!success) return;
    //       this.conclusionLists = returnData || [];
    //     });
    // },
    //体检分类过滤
    filterCls(peCls) {
      if (this.activeMain3 == 'main1') {
        this.viewTableList1 = this.viewTableListCopy1.filter((item) => {
          return peCls === '' ? true : item.peCls == peCls;
        });
      } else if (this.activeMain3 == 'main2') {
        this.viewTableList2 = this.viewTableListCopy2.filter((item) => {
          return peCls === '' ? true : item.peCls == peCls;
        });
      } else {
        this.viewTableList3 = this.viewTableListCopy3.filter((item) => {
          return peCls === '' ? true : item.peCls == peCls;
        });
      }
    },
    search() {
      this.searchDialog = true;
    },
    searchDialogClose() {
      this.searchDialog = false;
    },
    consultList() {
      this.consultDialog = true;
    },
    consultDialogClose() {
      this.consultDialog = false;
    },
    //关闭历史报告弹出
    hisDrawerClose() {
      this.hisDrawer = false;
    },
    historyReport() {
      if (!this.reportConclution.patient.regNo) {
        this.$message({
          message: '请先点击要查看历史报告的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.hisDrawer = true;
    },
    // 队列的双击回调
    patientListDbClick(row) {
      this.getReportConclution(row.regNo);
    },
    /**
     * @author: justin
     * @description: 显示选择身份信息
     * @return {*}
     */
    showChoiceRegisterHandle() {
      this.showChoiceRegister = true;
    },

    /**
     * @author: justin
     * @description: 确认选择身份信息
     * @param {*} data 数据
     * @return {*}
     */
    confrimChoiceRegDataHandle(data) {
      if (!data) {
        this.$message.warning('请选择身份信息!');
        return;
      }

      this.cancelChoiceRegDataHandle();
      this.searchByRegNo(data.regNo);
    },

    /**
     * @author: justin
     * @description: 取消选择身份信息
     * @return {*}
     */
    cancelChoiceRegDataHandle() {
      this.showChoiceRegister = false;
      this.keyword = '';
    }
  }
};
