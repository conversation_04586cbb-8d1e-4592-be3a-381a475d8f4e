import PublicTable from '@/components/publicTable.vue';
import Consult from '../consult.vue';
import MainBtnCom from '..//mainBtnCom.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
import MainSearch from '../mainSearch.vue';
import SummaryCom from '../summaryCom.vue';
import Distribution from '../distribution.vue';
import NoDistribution from '../noDistribution.vue';
import ResultEntry from '../../inspectIn/resultEntry.vue';
import HistoryReport from '../../inspectIn/components/doctorWorkStation/historyReport.vue';
import moment from 'moment';
export default {
  name: 'process',
  components: {
    PublicTable,
    MainBtnCom,
    MainSearch,
    Consult,
    SummaryCom,
    Distribution,
    NoDistribution,
    ResultEntry,
    HistoryReport,
    ResultEntry
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_userInfo'])
  },
  data() {
    return {
      mainInspectionFlag: false,
      activeMain3: 'main2',
      searchDialog: false,
      consultDialog: false,
      tjCls: '',
      activeName: 'first',
      viewTableList1: [],
      viewTableList2: [],
      viewTableList3: [],
      viewTableListCopy1: [],
      viewTableListCopy2: [],
      viewTableListCopy3: [],
      isMain: 2, //2表示未检，3表示已主检
      conclusionList: [],
      conclusionLists: [],
      enum_marrStatus: {
        0: '未知',
        1: '未婚',
        2: '已婚',
        3: '离异',
        4: '丧偶'
      },
      //性别 (查全部 = -1,通用 = 0, 男 = 1, 女 = 2)
      enum_sex: {
        '-1': '全部',
        0: '通用',
        1: '男',
        2: '女'
      },
      enum_ageUnit: {
        null: '',
        0: '岁',
        1: '月'
      },
      regNo: '', //体检号,
      reportConclution: {
        patientInfo: {}, //个人信息
        summary: {}, //综述
        suggestions: [] //建议
      },
      checkStatus: 1, //主检的各个状态（1：待主检；2：已主检；3：已审核）
      hisDrawer: false,
      dateVal2: [],
      dateVal3: []
    };
  },
  created() {
    let startDate = moment().add(-7, 'd').format('YYYY-MM-DD');
    let endDate = moment().startOf('day').format('YYYY-MM-DD');
    this.dateVal2 = [startDate, endDate];
    this.dateVal3 = [startDate, endDate];
  },
  mounted() {
    this.getConclusion();
    this.getPatientList();
  },
  methods: {
    getPatientList() {
      this.getPatientList1();
      this.getPatientList2();
      this.getPatientList3();
    },
    getPatientList1() {
      let data = {
        operatorCode: this.G_userInfo.codeOper.operatorCode,
        checkStatus: 1,
        isOccupation: false
      };
      this.$ajax
        .post(this.$apiUrls.GetPatientList4NotAllocateV2, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.viewTableList1 = returnData;
          this.viewTableListCopy1 = returnData;
        });
    },
    getPatientList2() {
      let data = {
        operatorCode: this.G_userInfo.codeOper.operatorCode,
        checkStatus: 2,
        beginDate: this.dateVal2[0],
        endDate: this.dateVal2[1],
        isOccupation: false
      };
      this.$ajax
        .post(this.$apiUrls.GetPatientList4NotAllocateV2, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.viewTableList2 = returnData;
          this.viewTableListCopy2 = returnData;
        });
    },
    getPatientList3() {
      let data = {
        operatorCode: this.G_userInfo.codeOper.operatorCode,
        checkStatus: 3,
        beginDate: this.dateVal3[0],
        endDate: this.dateVal3[1],
        isOccupation: false
      };
      console.log(data);
      this.$ajax
        .post(this.$apiUrls.GetPatientList4NotAllocateV2, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.viewTableList3 = returnData;
          this.viewTableListCopy3 = returnData;
        });
    },
    //主检优先分配
    precedence() {
      if (!this.reportConclution.patientInfo.regNo) {
        this.$message({
          message: '没有体检号无法优先分配!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$ajax
        .post(this.$apiUrls.PriorityAllocation, [], {
          query: {
            operatorCode: this.G_userInfo.codeOper.operatorCode,
            regNo: this.reportConclution.patientInfo.regNo
          }
        })
        .then((r) => {
          let { success } = r.data;
          if (!success) return;
          this.$message({
            message: '主检优先分配成功!',
            type: 'success',
            showClose: true
          });
        });
    },
    //体检号回车查询
    async regNoSearch() {
      this.getReportConclution(this.regNo).then((r) => {
        this.regNo = '';
        if (this.activeName == 'third') {
          this.$refs.resultEntry_Ref.headerInfo = r.patientInfo;
          this.$refs.resultEntry_Ref.getNavList();
        }
      });
    },
    //获取报告结论
    getReportConclution(regNo) {
      this.projectResult = [];
      this.suggestions = [];
      this.$refs.summaryCom.newSumCombs = [];
      return new Promise((resolve, reject) => {
        if (!regNo) {
          this.$message({
            message: '没有体检号无法查询!',
            type: 'warning',
            showClose: true
          });
          return;
        }
        this.$refs.popoverRef.doClose();
        this.searchDialog = false;
        //this.regNo = regNo; //双击列表把体检号赋值文本框
        this.GetSimpleItemResult(regNo);
        this.$ajax
          .post(this.$apiUrls.GetReportConclution, [], {
            query: { regNo: regNo, checkAudit: 1 }
          })
          .then((r) => {
            let { success, returnData } = r.data;
            if (!success) return;
            if (returnData.patientInfo.peStatus === 4) {
              this.isExamine = 2;
            } else {
              this.isExamine = 1;
            }
            this.reportConclution = returnData;
            this.activeName = 'first';
            this.suggestions = dataUtils.deepCopy(returnData.suggestions);
            this.$nextTick(() => {
              this.$refs.summaryCom.getRecordCombs(regNo);
            });
            this.isMain = returnData.patientInfo.peStatus;
            console.log('[ this.reportConclution ]-354', this.reportConclution);
            resolve(returnData);
          });
        this.getImgTextList(regNo);
      });
    },
    getConclusion() {
      this.$ajax
        .post(this.$apiUrls.RD_CodeConclusionTemplate + '/Read', [])
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.conclusionLists = returnData || [];
        });
    },
    //保存主检
    mainTest() {
      //主检医生
      // this.reportConclution.checkDoctorCode =
      //   this.G_userInfo.codeOper.operatorCode;
      // this.reportConclution.checkDoctorName = this.G_userInfo.codeOper.name;
      this.reportConclution.summary.sumCombs =
        this.$refs.summaryCom.newSumCombs;
      this.reportConclution.suggestions = this.$refs.summaryComs.newSuggestions;
      console.log('[ this.reportConclution) ]-433', this.reportConclution);
      if (!this.reportConclution.conclusion) {
        this.reportConclution.conclusion = '';
      }
      this.$ajax
        .post(this.$apiUrls.SaveReportConclusion, this.reportConclution)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$message({
            message: '保存成功!',
            type: 'success',
            showClose: true
          });
          this.isMain = 3;
          this.getReportConclution(this.reportConclution.patientInfo.regNo); //刷新数据
        });
    },
    // 取消保存主检
    cancelMainTest() {
      if (!this.reportConclution.patientInfo.regNo) {
        this.$message({
          message: '请先选择要取消保存的结论!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$ajax
        .post(this.$apiUrls.CancelReportConclusion, [], {
          query: { regNo: this.reportConclution.patientInfo.regNo }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$message({
            message: '取消主检成功!',
            type: 'success',
            showClose: true
          });
          this.isMain = 2;
          this.getReportConclution(this.reportConclution.patientInfo.regNo); //刷新数据
        });
    },
    mainCheckList() {},
    //主检列表点击
    mainClick3(tab, event) {
      console.log('[ this.activeMain3 ]-520', this.activeMain3);
      console.log(tab, event);
    },
    //体检分类过滤
    filterCls(peCls) {
      if (this.activeMain3 == 'main1') {
        this.viewTableList1 = this.viewTableListCopy1.filter((item) => {
          return peCls === '' ? true : item.peCls == peCls;
        });
      } else if (this.activeMain3 == 'main2') {
        this.viewTableList2 = this.viewTableListCopy2.filter((item) => {
          return peCls === '' ? true : item.peCls == peCls;
        });
      } else {
        this.viewTableList3 = this.viewTableListCopy3.filter((item) => {
          return peCls === '' ? true : item.peCls == peCls;
        });
      }
    },
    handleClick(tab, event) {
      console.log(tab, event);
      this.$refs.summaryComs.newSuggestions = this.reportConclution.suggestions;
      if (this.activeName == 'third') {
        this.$refs.resultEntry_Ref.headerInfo =
          this.reportConclution.patientInfo;
        this.$refs.resultEntry_Ref.getNavList();
      }
    },
    search() {
      this.searchDialog = true;
    },
    searchDialogClose() {
      this.searchDialog = false;
    },
    consultList() {
      this.consultDialog = true;
    },
    consultDialogClose() {
      this.consultDialog = false;
    },
    // 生成结论
    createReport() {
      if (!this.reportConclution.patientInfo.regNo) {
        this.$message({
          message: '没有体检号无法生成结论!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.isMain !== 2) {
        this.$message({
          message: '已主检，无法重新生成结论!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm('是否确定要生成结论?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.generateSummarySuggestions();
        })
        .catch(() => {});
    },
    //生成结论
    generateSummarySuggestions() {
      this.$ajax
        .post(this.$apiUrls.GenerateSummarySuggestions, [], {
          query: { regNo: this.reportConclution.patientInfo.regNo }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$refs.summaryComs.newSuggestions = returnData.suggestions;
          this.$refs.summaryCom.newSumCombs = returnData.summary.sumCombs;
          this.$nextTick(() => {
            this.$refs.summaryCom.getRecordCombs(
              this.reportConclution.patientInfo.regNo
            );
          });
          this.$message({
            type: 'success',
            message: '已重新生成结论!'
          });
        });
    },
    //关闭历史报告弹出
    hisDrawerClose() {
      this.hisDrawer = false;
    },
    historyReport() {
      if (!this.reportConclution.patientInfo.regNo) {
        this.$message({
          message: '请先点击要查看历史报告的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.hisDrawer = true;
    },
    // 队列的双击回调
    patientListDbClick(row) {
      console.log(888888888);
      this.getReportConclution(row.regNo);
    }
  }
};
