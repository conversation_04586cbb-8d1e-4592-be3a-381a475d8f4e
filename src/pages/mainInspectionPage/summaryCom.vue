<template>
  <div class="SummaryCom">
    <div class="leftDiv" v-show="isSummary">
      <div class="title">
        <span @click="deleteSug">本次体检综述：</span>
        <p>
          <el-popover
            placement="bottom"
            width="300"
            trigger="click"
            v-model="addLeft"
            ref="addLeft"
            popper-class="addLeft"
          >
            <div class="selDiv">
              <el-input
                class="input-new-tag"
                v-model="inputValue"
                ref="addTagInput"
                size="small"
                clearable
                @clear="selDepartment"
                @keyup.enter.native="selDepartment"
                placeholder="请输入"
              >
              </el-input>
              <MainBtnCom :btnList="['查询']" @search="selDepartment" />
            </div>
            <div class="liDiv">
              <li
                v-for="(item, index) in combList"
                :key="index"
                :class="{ active: activeIndex == index }"
                @click="activeClick(index)"
                @dblclick="dbAddLeft(item)"
              >
                {{ item.combName }}
              </li>
            </div>
            <MainBtnCom
              :btnList="['添加']"
              slot="reference"
              :isAabled="isExamine !== 1"
              @addFun="addDisDepartmentClick"
            />
          </el-popover>
        </p>
      </div>
      <div class="leftCont">
        <div class="contDiv" v-if="isAdd">
          <div class="titleLeft">
            <p>
              <span>{{ combName }}：</span>
              <span class="iconSpan">
                <span class="iconfont icon-xinjian"></span>
                <span class="iconfont icon-shanchu"></span>
              </span>
            </p>
          </div>
          <el-input
            class="input-new-tag"
            v-model="addTagValue"
            ref="addTagInput2"
            size="small"
            clearable
            @blur="addTagConfirm(addSumObj)"
            @keyup.enter.native="$event.target.blur()"
            type="textarea"
            :rows="3"
            v-focus
          >
          </el-input>
        </div>
        <div
          class="contDiv"
          v-for="(item, idx) in newSumCombs"
          :key="idx"
          :id="item.combCode"
          :class="checkRowIdx === idx ? 'check_sort' : ''"
        >
          <div class="titleLeft">
            <p>
              <span>{{ item.combName }}：</span>
              <span
                class="iconSpan"
                v-if="isMain === 2 || isMain === 3 || isExamine === 1"
              >
                <span
                  class="iconfont icon-xinjian"
                  @click="addTagFun(item)"
                ></span>
                <span
                  class="iconfont icon-shunxu"
                  title="排序"
                  @click="combSort(idx)"
                ></span>
                <span
                  class="iconfont icon-shanchu"
                  @click="delTagFun(item)"
                ></span>
              </span>
            </p>
          </div>
          <div class="meal_tag_wrap">
            <el-tag
              v-for="(items, index) in item.sumTags"
              :key="index"
              class="meal_tag"
              @click="editLiftTag(item, items)"
              @close="delLeftMeal(item, items, idx)"
              :closable="isMain === 2 || isMain === 3 || isExamine === 1"
            >
              {{ items.tag }}
            </el-tag>
            <el-input
              class="input-new-tag3"
              v-model="addTagValue"
              v-if="addItem.combCode == item.combCode"
              size="small"
              clearable
              @blur="addTagConfirm(item)"
              @keyup.enter.native="$event.target.blur()"
              type="textarea"
              :rows="3"
              v-focus
            >
            </el-input>
          </div>
        </div>
      </div>
    </div>
    <div class="rightDiv" v-show="!isSummary">
      <div class="title">
        <span>本次体检建议：</span>
        <p>
          <el-popover
            placement="bottom-end"
            width="370"
            trigger="click"
            v-model="addRight"
            ref="addRight"
            popper-class="addRight"
            @after-leave="popoverHide"
            :disabled="isExamine !== 1"
          >
            <div class="selDiv">
              <el-input
                class="input-new-tag"
                v-model="rightValue"
                ref="rightInput"
                size="small"
                clearable
                @clear="selDisease"
                @keyup.enter.native="selDisease"
                placeholder="请输入"
                v-focus
              >
              </el-input>
              <MainBtnCom :btnList="['查询']" @search="selDisease" />
            </div>
            <vxe-list
              v-show="!isShowRight"
              height="100%"
              class="suggDiv"
              :data="rightAddDataCopy"
              :loading="loading"
            >
              <template #default="{ items }">
                <div
                  class="suggDiv-list-item"
                  @dblclick="normalSuggest"
                  style="padding: 5px"
                >
                  <i class="iconfont icon-biaodan icons"></i>
                  <span>正常建议</span>
                </div>
                <div
                  class="suggDiv-list-item"
                  v-for="(item, index) in items"
                  :key="index"
                  :class="[
                    `level-${item._LEVEL}`,
                    {
                      'has-child': item._HAS_CHILDREN,
                      'is-expand': item._EXPAND
                    }
                  ]"
                  :style="{ paddingLeft: `${item._LEVEL * 20}px` }"
                  @click="toggleTreeNode(item)"
                  @dblclick="
                    (item._LEVEL == 1 && !item.children) || item._LEVEL == 2
                      ? addDisFun(item.title)
                      : ''
                  "
                >
                  <i
                    class="icons"
                    v-if="
                      item._LEVEL == 0 &&
                      item.children &&
                      item.children.length != 0
                    "
                    :class="
                      item._EXPAND
                        ? 'el-icon-caret-bottom'
                        : 'el-icon-caret-right'
                    "
                  ></i>
                  <i
                    class="icons"
                    v-if="
                      item._LEVEL == 0 &&
                      item.children &&
                      item.children.length != 0
                    "
                    :class="
                      item._EXPAND
                        ? 'iconfont icon-wenjianjia'
                        : 'iconfont icon-folder1-fill'
                    "
                  ></i>
                  <i
                    class="iconfont icon-biaodan icons"
                    v-if="
                      (item._LEVEL == 0 && !item.children) || item._LEVEL == 1
                    "
                  ></i>
                  <span :title="item.title">{{ item.title }}</span>
                </div>
              </template>
            </vxe-list>
            <vxe-list
              v-show="isShowRight"
              height="100%"
              class="suggDiv"
              :loading="loading1"
              :data="queryDiseaseSuggestionsList"
            >
              <template v-slot="{ items }">
                <div
                  class="suDis"
                  v-for="(item, index) in items"
                  :key="index"
                  @dblclick="addSugg(item)"
                >
                  <p class="disP">
                    <i class="iconfont icon-biaodan icon"></i>
                    {{ item.diseaseName }}
                  </p>
                  <p class="suggP" :title="item.suggestContent">
                    {{ item.suggestContent }}
                  </p>
                </div>
              </template>
            </vxe-list>
            <MainBtnCom
              :btnList="['添加']"
              slot="reference"
              class="btn"
              @addFun="addDiseaseClick"
              :isAabled="isExamine !== 1"
            />
          </el-popover>
          <MainBtnCom
            v-if="isMain === 2 || isMain === 3 || isExamine === 1"
            :btnList="['清空', '撤销修改']"
            @revocationBtn="revocationBtn"
            @emptys="emptys"
            :isRevocation="C_revocation"
          />
        </p>
      </div>

      <div class="rightCont">
        <Vuedraggable
          v-model="newSuggestions"
          forceFallback="true"
          animation="1000"
          handle=".move"
          :scroll="true"
          @start="onStart"
          filter=".rightTitle"
          group="item-tag"
          @end="onEnd"
          @add="tagStart(arguments, newSuggestions)"
        >
          <transition-group>
            <div
              class="contDiv"
              :class="item.id == mergeObj.id ? 'merge_active' : ''"
              v-for="(item, index) in newSuggestions"
              :key="item.id"
            >
              <div
                class="rightTag"
                v-if="
                  item.deaTags.length != 0 &&
                  item.deaTags[0].diseaseCode !== '正常建议'
                "
              >
                <span class="index">{{ index + 1 }}.</span>
                <Vuedraggable
                  v-model="item.deaTags"
                  forceFallback="true"
                  :disabled="isMain !== 2 || isExamine !== 1"
                  animation="1000"
                  :scroll="true"
                  group="item-tag"
                  :move="onMove(item.deaTags)"
                  @end="tagEnd"
                  @add="tagStart(arguments, item.deaTags, item)"
                  class="meal_tag_wrap item-tag"
                >
                  {{ item }}
                  <el-tag
                    v-for="(tags, tagIdx) in item.deaTags"
                    :key="tags.id"
                    :closable="isMain === 2 || isMain === 3 || isExamine === 1"
                    class="meal_tag"
                    @click="tagClick(tags)"
                    @close="delRightMeal(item, tagIdx, index)"
                  >
                    {{ tags.tag }}
                  </el-tag>
                </Vuedraggable>
                <span
                  class="iconSpan"
                  v-if="isMain === 2 || isMain === 3 || isExamine === 1"
                >
                  <span
                    class="iconfont icon-qingqiuhebing merge"
                    @click="mergeFun(item)"
                    title="合并"
                  ></span>
                  <span class="iconfont icon-shunxu move" title="排序"></span>
                  <span
                    class="iconfont icon-shanchu"
                    @click="removeAt(index)"
                    title="删除"
                  ></span>
                </span>
              </div>
              <div class="rightTitle">
                <p
                  v-if="selectSuggest.id !== item.id"
                  @pointerdown.stop
                  @dblclick="suggestDBL(item)"
                >
                  {{ item.sugContent }}
                </p>
                <el-input
                  v-if="selectSuggest.id == item.id"
                  type="textarea"
                  autosize
                  v-focus
                  placeholder="请输入内容"
                  @blur="suggestInputBlur"
                  @pointerdown.stop.native
                  v-model="item.sugContent"
                >
                </el-input>
              </div>
            </div>
          </transition-group>
        </Vuedraggable>
      </div>
    </div>
  </div>
</template>
<script>
import Vuedraggable from 'vuedraggable';
import MainBtnCom from './mainBtnCom.vue';
import { mapGetters } from 'vuex';
import { dataUtils } from '@/common';
import XEUtils from 'xe-utils';
import VXETable from 'vxe-table';
export default {
  name: 'SummaryCom',
  components: {
    MainBtnCom,
    Vuedraggable
  },
  props: {
    // 选中的体检人信息
    patientInfo: {
      type: Object,
      default: () => {
        return {};
      }
    },
    //综述
    sumCombs: {
      type: Array,
      default: () => {
        return [];
      }
    },
    //建议
    suggestions: {
      type: Array,
      default: () => {
        return [];
      }
    },
    regNo: {
      type: String,
      default: () => {
        return '';
      }
    },
    // 主检状态
    isMain: {
      type: Number,
      default: 2
    },
    isSummary: {
      type: Boolean,
      default: () => {
        return true;
      }
    },
    processFlag: {
      type: Boolean,
      default: () => {
        return false;
      }
    },
    // 主检标识
    mainFlag: {
      type: Boolean,
      default: false
    },
    // 主检标识
    isExamine: {
      type: Number,
      default: 1
    }
  },
  computed: {},
  data() {
    return {
      itemTag: {
        name: 'itxst', //组名为itxst
        pull: (e) => {
          console.log(e);
          return false;
        }, //是否允许拖出当前组
        put: true //是否允许拖入当前组
      },
      checkComb: {},
      newSumCombs: dataUtils.deepCopy(this.sumCombs),
      fixed_newSumCombs: dataUtils.deepCopy(this.sumCombs),
      newSuggestions: dataUtils.deepCopy(this.suggestions),
      fixed_newSuggestions: dataUtils.deepCopy(this.suggestions),
      isAdd: false,
      activeIndex: '',
      inputValue: '',
      addLeft: false,
      isAabled: false,
      addRight: false,
      rightValue: '',
      rightAddData: [],
      rightAddDataCopy: [],
      combList: [],
      combListCopy: [],
      muenId: '',
      defaultOpened: [],
      openedMenus: [],
      sumTagEdit: {
        editMode: 1, //1：新增；2：修改；3：删除
        sumTag: {},
        suggestions: []
      },
      sumTagId: 0,
      isEdit: false, //是否是修改综述标签
      editTag: {},
      isHave: false,
      combCode: '',
      combName: '',
      addTagValue: '',
      drag: false,
      mergeObj: {}, //合并的对象
      selectSuggest: {},
      isShowRight: false,
      queryDiseaseSuggestionsList: [],
      addSuggestions: {
        id: 0,
        sortIndex: 0,
        sugContent: '',
        deaTags: [
          {
            id: 0,
            diseaseCode: '',
            tag: '',
            bindSummTagIds: []
          }
        ]
      },
      loading1: true,
      loading: true,
      addSumObj: {},
      addItem: {}, //点击组合的数据
      checkRowIdx: ''
    };
  },
  created() {},
  mounted() {},
  computed: {
    ...mapGetters(['G_EnumList', 'G_codeDepartment']),
    C_revocation() {
      return (
        JSON.stringify(this.fixed_newSuggestions) ==
          JSON.stringify(this.newSuggestions) &&
        JSON.stringify(this.fixed_newSumCombs) ==
          JSON.stringify(this.newSumCombs)
      );
    },
    C_deaTags() {
      return (deaTags) => {
        return deaTags?.length == 1;
      };
    }
  },
  methods: {
    activeClick(index) {
      this.activeIndex = index;
    },
    addDisDepartmentClick() {
      this.addTagValue = '';
      setTimeout(() => {
        this.$refs.addTagInput.focus();
      }, 300);
    },
    // 疾病建议添加
    addDiseaseClick() {
      Promise.all([this.getDeptDisease()]).then((r) => {
        console.log('r: ', r);
        this.loading = false;
        setTimeout(async () => {
          // 将树结构拍平，构建列表树结构
          XEUtils.eachTree(r[0], (item, index, items, paths, parent, nodes) => {
            // 层级
            item._LEVEL = nodes.length - 1;
            // 是否展开
            item._EXPAND = false;
            // 是否可视
            item._VISIBLE = !item._LEVEL;
            // 是否有子节点
            item._HAS_CHILDREN = item.children && item.children.length > 0;
            // 是否叶子节点
            item._IS_LEAF = !item._HAS_CHILDREN;
          });
          this.rightAddData = r[0];
          this.refreshTree();
          this.$nextTick(() => {
            this.$refs.rightInput.focus();
          });
        }, 200);
      });
    },
    //疾病列表
    getDeptDisease() {
      return new Promise((resolve, reject) => {
        this.$ajax.post(this.$apiUrls.GetDiseaseInDept, []).then((r) => {
          console.log('GetDiseaseInDept: ', r);
          let { success, returnData } = r.data;
          if (!success) return;
          let newData = [];
          returnData.forEach((item, index) => {
            newData.push({
              id: index,
              title: item.clsName,
              children: []
            });
            item.diseases.forEach((children, childIndex) => {
              newData[index].children.push({
                id: children.diseaseCode,
                title: children.diseaseName
              });
            });
          });
          resolve(newData);
        });
      });
    },
    // 切换树节点的展开、收缩
    toggleTreeNode(row) {
      if (row._HAS_CHILDREN) {
        this.setTreeExpand(row, !row._EXPAND);
      }
    },
    // 设置树节点的展开、收缩
    setTreeExpand(row, isExpand) {
      const matchObj = XEUtils.findTree(
        this.rightAddData,
        (item) => item === row
      );
      row._EXPAND = isExpand;
      if (matchObj) {
        XEUtils.eachTree(
          matchObj.item.children,
          (item, index, items, path, parent) => {
            item._VISIBLE = parent
              ? parent._EXPAND && parent._VISIBLE
              : isExpand;
          }
        );
      }
      this.refreshTree();
    },
    // 刷新树节点
    refreshTree() {
      const treeList = XEUtils.toTreeArray(this.rightAddData);
      this.rightAddDataCopy = treeList.filter((item) => item._VISIBLE);
    },
    // 疾病建议添加框隐藏
    popoverHide() {
      this.loading = true;
      this.loading1 = true;
      this.rightAddDataCopy = [];
      this.rightValue = '';
      this.selDisease();
    },
    // 获取已录入结果的组合，用于综述
    getRecordCombs(regNo) {
      this.mergeObj = {};
      this.newSumCombs = dataUtils.deepCopy(this.sumCombs);
      this.fixed_newSumCombs = dataUtils.deepCopy(this.sumCombs);
      this.newSuggestions = dataUtils.deepCopy(this.suggestions);
      console.log('[ this.newSuggestions ]-435', this.newSuggestions);
      this.fixed_newSuggestions = dataUtils.deepCopy(this.suggestions);
      this.$ajax
        .post(this.$apiUrls.GetRecordCombs, '', {
          query: { regNo: regNo }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          console.log('[ returnData ]-193', returnData);
          let checkArr = this.sumCombs.map((item) => item.combCode);
          const newData = returnData.filter(
            (item) => !checkArr.includes(item.combCode)
          );
          console.log('[ newData ]-285', newData);
          if (newData.length < 1) {
            this.isAabled = true;
          } else {
            this.isAabled = false;
          }
          this.combList = newData;
          this.combListCopy = newData;
        });
    },
    //过滤查询左边添加弹出
    selDepartment() {
      this.combList = this.combListCopy.filter((item) => {
        return item.combName.indexOf(this.inputValue) !== -1;
      });
    },
    //双击选中添加
    dbAddLeft(addItem) {
      if (this.isMain != 2 && this.isMain != 3 && this.processFlag == false) {
        this.$message({
          message: '不是未检状态，不可添加!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      console.log('[ addItem ]-269', addItem);
      this.$refs.addLeft.doClose(); //关闭弹出
      this.activeIndex = ''; //取消点击颜色
      this.isAdd = true;
      this.isEdit = false;
      this.combCode = addItem.combCode;
      this.combName = addItem.combName;
      this.addSumObj = {
        ...addItem,
        sumTags: []
      };
      this.$nextTick(() => {
        this.$refs.addTagInput2.focus();
      });
      //弹出页删除已添加行
      this.combList = this.combList.filter((item) => {
        return item !== addItem;
      });
      if (this.combList.length < 1) {
        this.isAabled = true;
      } else {
        this.isAabled = false;
      }
    },
    //添加
    handleInputConfirm() {
      let inputValue = this.inputValue;
      let sumTags = [];
      if (inputValue) {
        sumTags.push({
          id: 0,
          tag: inputValue
        });
        this.newSumCombs.unshift({
          isChange: false,
          combCode: this.combCode,
          combName: this.combName,
          sumTags: sumTags
        });
      } else {
        this.combList.unshift(item);
        this.combListCopy = this.combList;
        this.isAabled = false;
      }
      this.isAdd = false;
      this.inputValue = '';
    },
    //增加综述标签
    addTagFun(tagItem) {
      this.addTagValue = '';
      this.isEdit = false;
      tagItem.isChange = true;
      this.addItem = tagItem;
      console.log('[this.newSumCombs  ]-700', this.newSumCombs);
    },
    //删除
    delTagFun(tagItem) {
      console.log(tagItem);
      this.$confirm('是否确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          let checkArr = [tagItem.combCode];
          this.newSumCombs = this.newSumCombs.filter(
            (item) => !checkArr.includes(item.combCode)
          );
          tagItem.sumTags.map((item) => {
            this.deleteSug(item);
          });
          this.combList.unshift(tagItem);
          if (this.combList.length < 1) {
            this.isAabled = true;
          } else {
            this.isAabled = false;
          }
          return;
        })
        .catch(() => {
          return;
        });
    },
    //左边确定添加/修改综述标签
    addTagConfirm(item) {
      console.log('[ item ]-387', item);
      if (this.isAdd) {
        // if(this.addTagValue){
        this.newSumCombs.unshift(this.addSumObj);
        this.newSumCombs.sort((a, b) => {
          return a.sortIndex - b.sortIndex;
        });
        this.$nextTick(() => {
          console.log('[  this.newSumCombs ]-659', this.newSumCombs);
          // 获取要滚动到的元素。
          const scrollEle = document.getElementById(item.combCode);
          // 滚动到元素，使用 `smooth` 滚动方式。
          scrollEle.scrollIntoView({ behavior: 'smooth', block: 'end' });
        });

        // } else {
        //   this.combList.unshift(item)
        // }
        this.isAdd = false;
      }
      item.sumTags.forEach((items) => {
        if (items.tag == this.addTagValue) {
          this.isHave = true;
          return;
        }
      });
      if (this.isHave) {
        this.$message({
          message: '已经有该标签，不能重复!',
          type: 'warning',
          showClose: true
        });
        this.isHave = false;
        item.isChange = false;
        this.addItem = {};
        return;
      }
      if (this.addTagValue) {
        if (this.isEdit) {
          this.sumTagEdit = {
            editMode: 2, //1：新增；2：修改；3：删除
            sumTag: { id: this.editTag.id, tag: this.addTagValue },
            suggestions: this.newSuggestions
          };
          item.sumTags.forEach((items) => {
            if (items.tag == this.editTag.tag) {
              items.tag = this.addTagValue;
            }
          });
        } else {
          this.sumTagEdit = {
            editMode: 1, //1：新增；2：修改；3：删除
            sumTag: { id: 0, tag: this.addTagValue },
            suggestions: this.newSuggestions
          };
          this.editSumTag(item);
        }

        return (this.addItem = {});
      } else {
        this.addItem = {};
      }
    },
    //左边标签删除
    delLeftMeal(item, items, idx) {
      console.log('[ items ]-449', item, items);
      this.sumTagEdit = {
        editMode: 3, //1：新增；2：修改；3：删除
        sumTag: items,
        suggestions: dataUtils.deepCopy(this.newSuggestions)
      };
      console.log(this.sumTagEdit);
      this.removeObject(items);
      // this.editSumTag(item, items,idx);
      // this.deleteSug(items)
    },
    //前端删除综述
    removeObject(obj) {
      this.newSumCombs = this.newSumCombs
        .map((item) => ({
          ...item,
          sumTags: item.sumTags.filter(
            (tag) => tag.id !== obj.id || tag.tag !== obj.tag
          )
        }))
        .filter((item) => item.sumTags.length > 0);
      //console.log("[ this.newSumCombs ]-844", this.newSumCombs);
    },
    // 删除关联的建议
    deleteSug(tag) {
      console.log(tag, this.$parent.$refs.summaryComs.newSuggestions);
      this.$parent.$refs.summaryComs.newSuggestions.map((item, idx, arr) => {
        let deaTags = item.deaTags.filter((twoItem, twoIdx, twoArr) => {
          let deaTagIdx = twoItem.bindSummTagIds.indexOf(tag.id);
          console.log(deaTagIdx);
          if (deaTagIdx != -1) {
            twoItem.bindSummTagIds.splice(deaTagIdx, 1);
          }
          return twoItem.bindSummTagIds.length !== 0;
        });
        item.deaTags = deaTags;
        if (item.deaTags.length === 0) {
          arr.splice(idx, 1);
        }
      });
    },
    //修改左边标签
    editLiftTag(item, items) {
      console.log(item, items);
      if (this.isMain != 2 && this.isMain != 3) return;
      if (JSON.stringify(this.checkComb) !== '{}') {
        this.checkComb.isChange = false;
      }
      this.checkComb = item;
      this.isEdit = true;
      this.editTag = items;
      this.addTagValue = items.tag;
      return (this.addItem = item);
    },
    //新增编辑删除标签editMode://1：新增；2：修改；3：删除
    editSumTag(item, items, idx) {
      this.$ajax.post(this.$apiUrls.EditSumTag, this.sumTagEdit).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        // this.addTagValue = '';
        this.newSuggestions = returnData.suggestions;
        returnData.suggestions?.map((sugItem) => {
          let flag = true;
          this.$parent.$refs.summaryComs.newSuggestions.some(
            (twoItem, twoIdx, arr) => {
              console.log(sugItem, twoItem);
              if (sugItem.sugContent == twoItem.sugContent) {
                let deaTags = [];
                sugItem.deaTags.map((sugTagItem) => {
                  let isHaveDeaTag = false;
                  twoItem.deaTags.map((deaTagItem) => {
                    if (deaTagItem.id == sugTagItem.id) {
                      isHaveDeaTag = true;
                    }
                  });
                  if (!isHaveDeaTag) {
                    deaTags.push(sugTagItem);
                  }
                });
                twoItem.deaTags.push(...deaTags);
                flag = false;
              }
            }
          );
          if (flag) {
            this.$parent.$refs.summaryComs.newSuggestions.push(sugItem);
          }
        });
        this.sumTagId = returnData.sumTagId;
        if (this.sumTagEdit.editMode == 1) {
          item.sumTags.push({
            id: this.sumTagId,
            tag: this.addTagValue
          });
          this.addSumObj = {};
        } else if (this.sumTagEdit.editMode == 3) {
          item.sumTags.splice(item.sumTags.indexOf(items), 1);
          if (item.sumTags.length === 0) {
            this.newSumCombs.splice(idx, 1);
            this.combList.unshift(item);
            if (this.combList.length < 1) {
              this.isAabled = true;
            } else {
              this.isAabled = false;
            }
          }
        }
      });
    },
    //过滤查询右边疾病添加弹出
    selDisease() {
      if (this.rightValue) {
        this.queryDiseaseSuggestions();
        this.isShowRight = true;
      } else {
        this.isShowRight = false;
      }
      // let selData = JSON.parse(JSON.stringify(this.rightAddDataCopy));
      // if (this.rightValue) {
      //   let data = [];
      //   let newArray = [];
      //   selData.forEach((now, index) => {
      //     newArray[index] = now;
      //     newArray[index].diseases = now.diseases.filter(item => {
      //       return item.diseaseName.indexOf(this.rightValue) !== -1;
      //     });
      //   });
      //   newArray.forEach((item, index) => {
      //     if (item.diseases.length != 0) {
      //       data.push(item);
      //     }
      //   });
      //   this.rightAddData = data;
      // } else {
      //   this.rightAddData = this.rightAddDataCopy;
      // }
    },
    //关键词查询疾病建议，用于添加新建议
    queryDiseaseSuggestions() {
      this.$ajax
        .post(this.$apiUrls.QueryDiseaseSuggestions, '', {
          query: { keyword: this.rightValue }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          console.log('[ returnData ]-193', returnData);
          this.queryDiseaseSuggestionsList = returnData || [];
          this.loading1 = false;
        });
    },
    //右边添加新的建议疾病
    addSugg(item) {
      if (!this.patientInfo.regNo) {
        this.$message({
          message: '请先选择体检人信息!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.isMain != 2 && this.isMain != 3) {
        this.$message({
          message: '不是未检状态，不可添加!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.newSuggestions[0]?.deaTags[0].diseaseCode === '正常建议') {
        this.newSuggestions = [];
      }
      let len = this.newSuggestions?.length;
      let diseaseId = Date.now();
      this.addSuggestions = {
        id: diseaseId + '',
        sortIndex: len + 1,
        sugContent: item.suggestContent,
        deaTags: [
          {
            id: len + 1,
            diseaseCode: item.diseaseCode,
            tag: item.diseaseName,
            bindSummTagIds: []
          }
        ]
      };
      this.newSuggestions.unshift(this.addSuggestions);
      this.$refs.addRight.doClose(); //关闭弹出
    },
    //直接添加
    addDisFun(keyword) {
      if (!this.patientInfo.regNo) {
        this.$message({
          message: '请先选择体检人信息!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.isMain != 2 && this.isMain != 3) {
        this.$message({
          message: '不是未检状态，不可添加!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.newSuggestions[0]?.deaTags[0].diseaseCode === '正常建议') {
        this.newSuggestions = [];
      }
      this.$ajax
        .post(this.$apiUrls.QueryDiseaseSuggestions, '', {
          query: { keyword: keyword }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          console.log('[ returnData ]-629', returnData);
          this.$refs.addRight.doClose(); //关闭弹出
          let len = this.newSuggestions?.length;
          this.addSuggestions = {
            id: len + 1,
            sortIndex: len + 1,
            sugContent: returnData[0]?.suggestContent,
            deaTags: [
              {
                id: len + 1,
                diseaseCode: returnData[0]?.diseaseCode,
                tag: returnData[0]?.diseaseName,
                bindSummTagIds: []
              }
            ]
          };
          this.newSuggestions.unshift(this.addSuggestions);
          console.log('[ this.newSuggestions ]-676', this.newSuggestions);
        });
    },
    // 右边标签删除
    delRightMeal(item, index, parentIdx) {
      if (item.deaTags?.length == 1) {
        this.newSuggestions.splice(parentIdx, 1);
        return;
      }
      item.deaTags.splice(index, 1);
    },
    tagClick(tag) {
      console.log(tag);
    },
    // 开始拖拽事件
    onStart() {
      this.drag = true;
    },
    // 拖拽结束事件
    onEnd() {
      this.drag = false;
    },
    // 建议删除
    removeAt(index) {
      this.$confirm('是否确认删除这条建议?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.newSuggestions.splice(index, 1);
        })
        .catch(() => {});
    },
    // handleOpen(key, keyPath) {
    //   console.log(key, keyPath, this.muenId);
    //   this.openedMenus = this.$refs.elMenu_Ref.$data.openedMenus;
    //   console.log(this.openedMenus);
    // },
    // handleClose(key, keyPath) {
    //   this.openedMenus = this.$refs.elMenu_Ref.$data.openedMenus;
    //   console.log(this.openedMenus);
    // },
    // handleSelect(index, indexPath) {},
    // 合并
    mergeFun(row) {
      console.log(row);
      if (JSON.stringify(this.mergeObj) == '{}') {
        this.mergeObj = row;
        return;
      }
      if (this.mergeObj.id == row.id) {
        this.mergeObj = {};
        return;
      }
      // 合并处理
      row.deaTags.push(...this.mergeObj.deaTags);
      if (
        row.sugContent
          .replace(/[\r\n]/g, '')
          .trim()
          .indexOf(this.mergeObj.sugContent.replace(/[\r\n]/g, '').trim()) == -1
      ) {
        row.sugContent += this.mergeObj.sugContent;
      }
      this.newSuggestions.splice(this.newSuggestions.indexOf(this.mergeObj), 1);
      this.mergeObj = {};
    },
    // 撤销修改
    revocationBtn() {
      this.$confirm('是否撤销修改?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.newSuggestions = dataUtils.deepCopy(this.suggestions);
          this.newSumCombs = dataUtils.deepCopy(this.fixed_newSumCombs);
          this.$message({
            type: 'success',
            message: '撤销修改成功!'
          });
        })
        .catch(() => {});
    },
    // 建议的双击回调
    suggestDBL(row) {
      if (this.isMain != 2 && this.isMain != 3) return;
      this.selectSuggest = row;
    },
    // 建议编辑输入框失去焦点和回车的回调
    suggestInputBlur() {
      this.selectSuggest = {};
    },
    // tag移动开始回调
    tagStart(r, arr, parent) {
      console.log(r, arr, parent);
      let argument = r[0];
      let addObj = arr[argument.newIndex];
      let oldObj = dataUtils.deepCopy(arr[argument.newIndex]);
      console.log(oldObj);
      let datas = {
        diseaseCode: addObj.diseaseCode
      };
      this.$ajax.paramsPost(this.$apiUrls.GetSuggestion, datas).then((r) => {
        // 合并
        if (parent) {
          if (
            parent.sugContent
              .replace(/[\r\n]/g, '')
              .trim()
              .indexOf(r.data.returnData.replace(/[\r\n]/g, '').trim()) == -1
          ) {
            parent.sugContent += r.data.returnData;
          }
        } else {
          console.log(888888888888888888, oldObj);
          // 拆分
          addObj.deaTags = [{ ...oldObj }];
          addObj.id = '0';
          setTimeout(() => {
            addObj.id = oldObj.id;
            addObj.sortIndex = argument.newIndex;
            addObj.sugContent = r.data.returnData;
            console.log(addObj);
          }, 50);
        }
        this.newSuggestions.map((item, idx, oldArr) => {
          if (item.deaTags?.length == 0) {
            oldArr.splice(idx, 1);
          }
          if (item.id == oldObj.id) {
            item.id = item.deaTags[0].id;
          }
        });
      });
    },
    // tag移动结束回调
    tagEnd(r) {},
    // 自定义控制
    onMove(deaTags) {
      return () => {
        if (deaTags?.length == 1) return false;
        return true;
      };
    },
    //清空
    emptys() {
      if (this.newSuggestions.length < 1) {
        this.$message({
          message: '没有可清空的体检建议!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm('是否清空本次体检建议?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.newSuggestions = [];
          this.$message({
            type: 'success',
            message: '已清空本次体检建议!'
          });
        })
        .catch(() => {});
    },
    // 正常建议的双击回调
    normalSuggest() {
      if (!this.patientInfo.regNo) {
        this.$message({
          message: '请先选择体检人信息!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.isMain != 2 && this.isMain != 3) {
        this.$message({
          message: '不是未检状态，不可添加!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.newSuggestions = [];
      this.$ajax.post(this.$apiUrls.AddNormalSuggestions).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.newSuggestions.unshift({
          ...returnData[0],
          sugContent: returnData[0]?.deaTags[0].tag
        });
        this.addRight = false;
        this.selectSuggest = this.newSuggestions[0];
      });
    },
    // 综述的排序
    combSort(rowIdx) {
      if (this.checkRowIdx === '') {
        this.checkRowIdx = rowIdx;
        return;
      }
      if (this.checkRowIdx === rowIdx) {
        this.checkRowIdx = '';
        return;
      }
      console.log(rowIdx);
      this.swapArrayElements(this.newSumCombs, this.checkRowIdx, rowIdx);
      setTimeout(() => {
        console.log(this.newSumCombs);
      }, 200);
    },
    // 数组位置互换
    swapArrayElements(arr, index1, index2) {
      [arr[index1], arr[index2]] = [arr[index2], arr[index1]];
      this.$forceUpdate();
      this.checkRowIdx = '';
    }
  },
  watch: {
    suggestions: function (n, o) {
      this.newSuggestions = dataUtils.deepCopy(n);
    }
  }
};
</script>
<style lang="less" scoped>
.SummaryCom {
  display: flex;
  width: 100%;
  height: 100%;
  border-radius: 4px;
  display: flex;
  color: #2d3436;
  flex-direction: row;
  .el-tag {
    margin-bottom: 3px;
    margin-right: 3px;
    font-family: PingFangSC-Medium;
    font-size: 16px;
    color: #1770df;
    white-space: normal;
    max-width: 100%;
    word-wrap: break-word;
    height: auto;
    line-height: 25px;
  }
  .leftDiv {
    // width: 430px;
    width: 100%;
    display: flex;
    flex-direction: column;

    p {
      display: flex;
      margin: auto 0;
      & > span {
        // margin-right: 10px;
        font-size: 16px;
        font-weight: 600;
      }
    }
    .leftCont {
      flex: 1;
      overflow: auto;
      border: 1px solid #ccc;
      border-radius: 4px;
      padding: 3px;
      background: rgba(178, 190, 195, 0.1);
      .contDiv {
        // margin-bottom: 5px;
        .meal_tag_wrap {
          background: #fff;
          padding: 3px 3px;
        }

        .titleLeft {
          height: 38px;
          line-height: 38px;
          border-radius: 4px;
          padding: 0 5px;
          & > p {
            display: flex;
            justify-content: space-between;
            font-family: PingFangSC-Semibold;
            font-size: 14px;
            color: #2d3436;
          }
          .iconSpan {
            width: 60px;
            display: flex;
            justify-content: space-between;
            margin: auto 5px;
            & > span {
              cursor: pointer;
              color: rgba(45, 52, 54, 0.6);
              font-weight: normal;
              flex: 1;
            }
          }
        }
      }
      .check_sort {
        border: 2px solid #079c66;
      }
    }
  }

  .rightDiv {
    // flex: 1;
    width: 100%;
    overflow: auto;
    display: flex;
    flex-direction: column;
    p {
      display: flex;
      margin: auto 0;
      & > span {
        margin-right: 10px;
      }
    }
    .rightCont {
      flex: 1;
      overflow: auto;
      border: 1px solid #ccc;
      border-radius: 4px;
      padding: 3px;
      background: rgba(178, 190, 195, 0.1);
      .contDiv {
        // margin-bottom: 5px;
        border-bottom: 1px dashed rgba(178, 190, 195, 0.7);
        padding: 3px 0;
        &:first-child {
          padding-top: 0;
        }
        .meal_tag_wrap {
          min-height: 33px;
          background: #fff;
          padding: 3px 3px 0 3px;
          border-radius: 4px;
        }
        .el-tag {
          margin-bottom: 3px;
          margin-right: 3px;
          font-family: PingFangSC-Medium;
          font-size: 16px;
          color: #1770df;
          white-space: pre-line;
          word-wrap: break-word;
          height: auto;
          line-height: 25px;
        }
        .rightTag {
          min-height: 38px;
          border-radius: 4px;
          display: flex;
          align-items: center;
          padding-bottom: 3px;
          .index {
            margin: auto;
            width: 22px;
            text-align: center;
            font-weight: 600;
          }
          .iconSpan {
            display: flex;
            & > span {
              cursor: pointer;
              margin-left: 18px;
              color: rgba(45, 52, 54, 0.6);
            }
            // .merge,
            .move {
              cursor: move;
            }
          }
        }
        .rightTitle {
          margin-left: 22px;
          // padding: 5px 0;
          & > p {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-family: PingFangSC-Semibold;
            font-size: 16px;
            color: #2d3436;
            min-height: 30px;
            // line-height: 38px;
            padding: 3px 3px;
            background: rgba(23, 112, 223, 0.1);
            border-radius: 4px;
          }
          /deep/ textarea {
            font-size: 16px;
            padding: 3px;
          }
        }
      }
      .merge_active {
        border: 2px solid #1770df;
      }
    }

    .index {
      font-family: PingFangSC-Semibold;
      font-size: 14px;
      color: #2d3436;
      margin-right: 10px;
    }
    i {
      cursor: pointer;
      margin: auto 0;
      font-size: 20px;
    }
    header {
      background-color: #fff;
      justify-content: flex-start;
      align-items: center;
      display: flex;
      line-height: 38px;
    }
    .itemTitle {
      background: rgba(23, 112, 223, 0.1);
      border-radius: 4px;
      line-height: 38px;
      margin-left: 20px;
      padding: 0 10px;
      font-size: 14px;
    }
    .meal_tag_wrap {
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
      // white-space: nowrap;
    }
    .meal_tag:hover {
      cursor: move;
    }
    // .meal_tag + .meal_tag {
    //   margin-left: 5px;
    // }
  }
  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    & > span {
      font-family: PingFangSC-Medium;
      font-size: 18px;
      color: #2d3436;
      font-size: 18px;
      font-weight: 600;
    }
  }
  li {
    background: rgba(23, 112, 223, 0.1);
    border: 1px solid #1770df;
    border-radius: 4px;
    border-radius: 4px;
    font-family: PingFangSC-Semibold;
    font-size: 14px;
    color: #1770df;
    padding: 5px;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-bottom: 10px;
    & > span {
      padding: 0 5px;
    }
    & > i {
      cursor: pointer;
      margin: auto 0;
      font-size: 20px;
    }
  }
  /deep/.el-dialog__header {
    height: 50px;
    line-height: 50px;
    background: rgba(23, 112, 223, 0.2);
    padding: 0 18px;
  }
  /deep/.el-dialog__headerbtn {
    top: 10px;
    font-size: 30px;
  }
}
</style>
<style>
.addLeft,
.addRight {
  height: calc(100% - 400px);
  display: flex;
  flex-direction: column;
  /* overflow: auto;
  z-index: 99999;
  display: flex;
  flex-direction: column; */
}
.el-menu-vertical-right {
  height: calc(100% - 50px);
  overflow: auto;
  width: 100%;
  border-right: 0;
}
.suggDiv {
  height: calc(100% - 50px);
  overflow: auto;
}
.suggDiv-list-item {
  padding: 5px 0;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.suggDiv-list-item:hover {
  background: rgba(23, 112, 223, 0.1);
}
.icons {
  margin-right: 10px;
}
.suDis {
  padding-bottom: 10px;
  border-bottom: 1px solid #ccc;
  cursor: pointer;
}
.suDis:hover {
  background: rgba(23, 112, 223, 0.1);
}
.disP {
  line-height: 36px;
  color: #1770df;
  font-size: 16px;
  font-weight: 600;
}
.suggP {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  color: #000;
  margin-left: 24px;
}
.addLeft li {
  line-height: 32px;
  cursor: pointer;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #2d3436;
}
.addLeft .liDiv {
  flex: 1;
  overflow: auto;
}
.liDiv .active {
  color: #409eff;
}
.selDiv {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
.selDiv .el-input {
  width: 70%;
}
.disNo {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #b2bec3;
  pointer-events: none;
}
.addRight .icons {
  font-size: 16px;
  margin-right: 8px;
}
.addRight .icon {
  margin-right: 6px;
  font-size: 14px;
}
.addRight .el-submenu .el-menu-item:focus,
.el-menu-item.is-active {
  outline: 0;
  color: #fff;
  background-color: #1770df;
  border-radius: 4px;
}
.addRight .el-submenu__title {
  font-size: 14px;
  height: 32px;
  line-height: 32px;
}
.addRight .el-submenu .el-menu-item,
.only {
  height: 32px;
  line-height: 32px;
}
</style>
