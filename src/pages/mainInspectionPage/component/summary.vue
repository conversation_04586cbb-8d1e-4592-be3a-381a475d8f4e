<template>
  <div class="summary_com">
    <div class="com_div letf_dom">
      <div class="title">
        <span>本次体检综述：</span>
        <p>
          <el-popover
            placement="bottom"
            trigger="click"
            ref="addLeft"
            popper-class="addLeft"
            v-model="showRecordCombItems"
            @show="sumPopover"
          >
            <el-row
              :gutter="20"
              align="bottom"
              type="flex"
              style="margin-bottom: 5px"
            >
              <el-col :span="8"> </el-col>
              <el-col :span="8">
                <el-input
                  class="input-new-tag"
                  v-model="summarySearchVal"
                  ref="addTagInput"
                  size="small"
                  clearable
                  placeholder="请输入项目名称"
                >
                </el-input>
              </el-col>
              <el-col :span="6">
                <el-button
                  type="primary"
                  size="mini"
                  icon="el-icon-search"
                  @click="sumPopover"
                  >查询</el-button
                >
              </el-col>
              <el-col :span="2">
                <i
                  style="
                    float: right;
                    margin: 0 5px 5px 0;
                    font-size: 25px;
                    cursor: pointer;
                  "
                  class="el-icon-close"
                  @click="showRecordCombItems = !showRecordCombItems"
                ></i>
              </el-col>
            </el-row>
            <el-row :gutter="20" align="center" type="flex">
              <el-col :span="24">
                <PublicTable
                  ref="tableRecordCombItem_ref"
                  url=""
                  rowKey="combCode"
                  :elStyle="{
                    height: 'calc(100vh - 460px)',
                    'show-selection': false,
                    'show-sort': false,
                    stripe: false
                  }"
                  :theads="[
                    {
                      label: '项目',
                      prop: 'combName',
                      width: 200,
                      showOverflowTooltip: true,
                      cellClassName: 'cell_blue'
                    },
                    {
                      label: '小结',
                      prop: 'combinations',
                      width: 200
                    },
                    {
                      label: '医生',
                      prop: 'doctorName',
                      width: 100,
                      showOverflowTooltip: true
                    },
                    {
                      label: '检查时间',
                      prop: 'examTime',
                      width: 160,
                      sortable: true
                    },
                    {
                      label: '操作',
                      prop: 'operation',
                      width: 150
                    }
                  ]"
                  :tableDataFilter="filterRecordCombItems"
                >
                  <template #operation="{ scope }">
                    <el-button
                      type="primary"
                      size="mini"
                      @click="newSumByRecordCombItem(scope.row)"
                      >添加</el-button
                    >
                    <el-button
                      type="primary"
                      size="mini"
                      @click="useSumByRecordCombItem(scope.row)"
                      >生成</el-button
                    >
                  </template>

                  <template #combinations="{ scope }">
                    <el-tooltip
                      placement="right"
                      effect="light"
                      popper-class="tooltip-combinations"
                    >
                      <div slot="content">
                        <el-row
                          :gutter="20"
                          align="center"
                          type="flex"
                          v-for="(item, index) in scope.row.combinations"
                          :key="index"
                        >
                          <el-col :span="24">
                            <el-tag size="small">{{ item.combTag }}</el-tag>
                          </el-col>
                        </el-row>
                      </div>
                      <div>
                        <span class="combinations">{{
                          scope.row.combinations
                            .map((x) => x.combTag)
                            .join('、')
                        }}</span>
                      </div>
                    </el-tooltip>
                  </template>
                </PublicTable>
              </el-col>
            </el-row>

            <el-button
              slot="reference"
              type="primary"
              size="mini"
              icon="el-icon-plus"
              class="blue_btn btn"
              :disabled="!C_isEdit || unselectSumList.length === 0"
              >添加</el-button
            >
          </el-popover>
        </p>
      </div>
      <div class="content_dom" ref="summary_Ref">
        <Vuedraggable
          v-model="summaryList"
          handle=".mover"
          animation="300"
          :scroll="true"
          @end="sumOnEnd"
          forceFallback="true"
        >
          <!-- <transition-group> -->
          <div class="contDiv" v-for="item in summaryList" :id="item.combCode">
            <div class="titleLeft">
              <p>
                <span :title="item.combName">{{ item.combName }}：</span>
                <span class="iconSpan" v-if="C_isEdit">
                  <span
                    class="iconfont icon-xinjian"
                    @click="addSummary(item)"
                  ></span>
                  <span class="iconfont icon-shunxu mover" title="排序"></span>
                  <span
                    class="iconfont icon-shanchu"
                    @click="delComb(item, idx)"
                  ></span>
                </span>
              </p>
            </div>
            <div class="meal_tag_wrap">
              <div
                class="sumTag"
                v-for="(items, index) in item.summTags"
                :key="index"
              >
                <span
                  :contenteditable="C_isEdit"
                  :ref="'sumTagRef_' + item.combCode + '_' + index"
                  @blur="sumTagBlur(items, index, idx, item)"
                  @keydown="sumInpKeydown($event, item, index)"
                  @paste="sumTagPaste"
                  v-text="items.tag"
                ></span>
                <i
                  class="el-icon-document-copy copy_btn"
                  title="复制小结"
                  v-if="C_isEdit"
                  v-clipboard:copy="items.tag"
                  v-clipboard:success="firstCopySuccess"
                ></i>
                <i
                  class="el-icon-error del_btn"
                  title="删除"
                  v-if="C_isEdit"
                  @click="sumDel(items, index, item, idx)"
                ></i>
              </div>
            </div>
          </div>
          <!-- </transition-group> -->
        </Vuedraggable>
      </div>
    </div>
    <div class="com_div right_dom rightDiv">
      <div class="title">
        <span>本次体检建议：</span>
        <p>
          <AiProposal
            v-if="this.G_config.ai.display"
            :url="this.G_config.ai.url"
            :parameter="C_pgParameter"
            ref="pgAiProposal"
            :thinkingProcess="this.G_config.ai.thinkingProcess"
            :btnName="this.G_config.ai.btnName"
            :btnDisabled="btnDisabled"
            :dataProcessing="this.G_config.ai.dataProcessing"
          />
          <AiProposal
            v-if="this.G_config.ai2.display"
            :url="this.G_config.ai2.url"
            :parameter="C_dsParameter"
            :btnName="this.G_config.ai2.btnName"
            ref="aiProposal"
            :thinkingProcess="this.G_config.ai2.thinkingProcess"
            :btnDisabled="btnDisabled"
            :dataProcessing="this.G_config.ai2.dataProcessing"
          />
          <AiProposal
            v-if="this.G_config.ai3.display"
            :url="this.G_config.ai3.url"
            :parameter="C_zKParameter"
            ref="zkAiProposal"
            :thinkingProcess="this.G_config.ai3.thinkingProcess"
            :btnName="this.G_config.ai3.btnName"
            :btnDisabled="btnDisabled"
            :dataProcessing="this.G_config.ai3.dataProcessing"
          />
          <el-popover
            placement="bottom-end"
            width="370"
            trigger="click"
            v-model="addRight"
            ref="addRight"
            popper-class="addRight"
            @after-leave="popoverHide"
            :disabled="isExamine !== 1"
          >
            <div class="selDiv">
              <el-input
                class="input-new-tag"
                v-model.trim="rightValue"
                ref="rightInput"
                size="small"
                clearable
                @clear="selDisease"
                @keyup.enter.native="selDisease"
                @blur="selDisease"
                placeholder="请输入"
                v-focus
              >
              </el-input>
              <MainBtnCom :btnList="['查询']" @search="selDisease" />
            </div>
            <el-button
              size="small"
              @click="addDefine"
              v-if="isShowRight && queryDiseaseSuggestionsList.length === 0"
              >添加自定义</el-button
            >
            <ul>
              <li v-for="item in rightAddDataCopy">{{ item }}</li>
            </ul>
            <ul>
              <li v-for="item in queryDiseaseSuggestionsList">{{ item }}</li>
            </ul>
            <!-- <vxe-list
              v-show="!isShowRight"
              height="100%"
              class="suggDiv"
              :data="rightAddDataCopy"
              :loading="loading"
            >
              <template #default="{ items }">
                
                <div
                  class="suggDiv-list-item"
                  v-for="(item, index) in items"
                  :key="index"
                  :class="[
                    `level-${item._LEVEL}`,
                    {
                      'has-child': item._HAS_CHILDREN,
                      'is-expand': item._EXPAND
                    }
                  ]"
                  :style="{ paddingLeft: `${item._LEVEL * 20}px` }"
                  @click="toggleTreeNode(item)"
                  @dblclick="
                    (item._LEVEL == 1 && !item.children) || item._LEVEL == 2
                      ? addDisFun(item.title)
                      : ''
                  "
                >
                  <i
                    class="icons"
                    v-if="
                      item._LEVEL == 0 &&
                      item.children &&
                      item.children.length != 0
                    "
                    :class="
                      item._EXPAND
                        ? 'el-icon-caret-bottom'
                        : 'el-icon-caret-right'
                    "
                  ></i>
                  <i
                    class="icons"
                    v-if="
                      item._LEVEL == 0 &&
                      item.children &&
                      item.children.length != 0
                    "
                    :class="
                      item._EXPAND
                        ? 'iconfont icon-wenjianjia'
                        : 'iconfont icon-folder1-fill'
                    "
                  ></i>
                  <i
                    class="iconfont icon-biaodan icons"
                    v-if="
                      (item._LEVEL == 0 && !item.children) || item._LEVEL == 1
                    "
                  ></i>
                  <span :title="item.title">{{ item.title }}</span>
                </div>
              </template>
            </vxe-list>
            <vxe-list
              v-show="isShowRight"
              height="100%"
              class="suggDiv"
              :loading="loading1"
              :data="queryDiseaseSuggestionsList"
              style="overflow: hidden"
            >
              <template v-slot="{ items }">
                <div
                  class="suDis"
                  v-for="(item, index) in items"
                  :key="index"
                  @dblclick="addSugg(item)"
                >
                  <p class="disP">
                    <i class="iconfont icon-biaodan icon"></i>
                    {{ item.diseaseName }}
                  </p>
                  <el-tooltip
                    class="item"
                    effect="light"
                    placement="left"
                    v-if="item.diseaseEntries.length > 0"
                  >
                    <template slot="content">
                      <div style="max-width: 311px">
                        <el-tag
                          size="mini"
                          style="margin: 3px"
                          v-for="(tag, index) in item.diseaseEntries"
                          :key="index"
                          type="success"
                          >{{ tag }}</el-tag
                        >
                      </div>
                    </template>
                    <div
                      class="disease-entries"
                      style="white-space: nowrap; margin-left: 24px"
                    >
                      <el-tag
                        style="margin: 5px"
                        size="mini"
                        v-for="(tag, index) in item.diseaseEntries"
                        :key="index"
                        type="success"
                        >{{ tag }}</el-tag
                      >
                    </div>
                  </el-tooltip>
                  <div class="disease-entries" style="margin-left: 24px" v-else>
                    <span>-</span>
                  </div>
                  <p class="suggP" :title="item.suggestContent">
                    {{ item.suggestContent }}
                  </p>
                </div>
              </template>
            </vxe-list> -->
            <MainBtnCom
              :btnList="['添加']"
              slot="reference"
              class="btn"
              @addFun="addDiseaseClick"
              :isAabled="!C_isEdit || isExamine !== 1"
            />
          </el-popover>
          <MainBtnCom
            v-if="C_isEdit"
            :btnList="['清空', '撤回上一步']"
            :isRevocation="record.length === 0"
            @revocationBtn="revocationBtn"
            @emptys="emptys"
          />
        </p>
      </div>
      <div class="content_dom">
        <div class="rightDiv">
          <div class="rightCont" ref="rightCont_Ref">
            <Vuedraggable
              v-model="newSuggestions"
              :forceFallback="true"
              animation="300"
              handle=".move"
              :scroll="true"
              @start="onStart"
              filter=".rightTitle"
              :group="groupA"
              @end="onEnd"
              @add="tagStart(arguments, newSuggestions)"
            >
              <transition-group>
                <div
                  class="contDiv"
                  :class="item.id == mergeObj.id ? 'merge_active' : ''"
                  v-for="(item, index) in newSuggestions"
                  :key="item.id"
                >
                  <div
                    class="rightTag"
                    v-if="
                      item.deaTags &&
                      item.deaTags.length != 0 &&
                      item.deaTags[0].diseaseCode !== '正常建议'
                    "
                  >
                    <span class="index">{{ index + 1 }}.</span>
                    <Vuedraggable
                      v-model="item.deaTags"
                      :forceFallback="true"
                      :disabled="!C_isEdit"
                      animation="300"
                      :scroll="true"
                      group="item-tag"
                      handle=".tag_dom"
                      :move="onMove(item.deaTags)"
                      @end="tagEnd"
                      @add="tagStart(arguments, item.deaTags, item)"
                      class="meal_tag_wrap item-tag"
                    >
                      <TagInput
                        :ref="'tagInput_Ref_' + index + '_' + tagIdx"
                        v-for="(tags, tagIdx) in item.deaTags"
                        :key="JSON.stringify(tags)"
                        :tag="tags"
                        :className="{ define_disease: !tags.diseaseCode }"
                        class="tag_dom"
                        @selectDisease="selectDisease(arguments, item.deaTags)"
                      >
                        <template #tagSpanInput="{ noteShow }">
                          <span
                            :contenteditable="tagSpanEditFlag && C_isEdit"
                            :ref="'tagSpanInput_Ref_' + index + '_' + tagIdx"
                            @keydown.enter.prevent="tagSpanEnter(tags)"
                            @keydown.right.stop="
                              tagSpanRight(
                                $event,
                                index,
                                tagIdx,
                                tags,
                                noteShow
                              )
                            "
                            @keydown.tab.stop="
                              tagSpanTab($event, tags, noteShow)
                            "
                            @input="tagSpanChange"
                            @blur="tagSpanBlur(tags, item, tagIdx, index)"
                            @focus="tagSpanFocus(index, tagIdx)"
                            @keydown.up.prevent="tagSpanKeyUp(index, tagIdx)"
                            @keydown.down.prevent="
                              tagSpanKeyDown(index, tagIdx)
                            "
                            @mouseenter.stop="tagSpanMouseenter"
                            class="tagSpan_input"
                            v-text="tags.diseaseName"
                          >
                          </span
                          ><span v-if="tags.diseaseNameRemark || noteShow"
                            >(<span
                              :ref="'noteSpanInput_Ref_' + index + '_' + tagIdx"
                              class="note_span"
                              :contenteditable="tagSpanEditFlag && C_isEdit"
                              @keydown.left.stop="
                                noteSpanLeft($event, index, tagIdx)
                              "
                              @blur="noteBlur(tags, index, tagIdx)"
                              v-text="tags.diseaseNameRemark"
                            >
                            </span
                            >)
                          </span>
                          <div
                            class="float_view"
                            :class="floatViewClass"
                            v-if="C_isEdit"
                          >
                            <p class="top_arrows" v-if="floatViewClass"></p>
                            <ul>
                              <!-- <li title="拖动拆分" class="tag_dom" v-if="item.deaTags.length > 1">
                                <img src="@/assets/img/summary/tuodong.png" />
                                <span>移动</span>
                              </li> -->
                              <li
                                title="编辑"
                                @click.stop="editBtn(index, tagIdx)"
                              >
                                <img src="@/assets/img/summary/beizhu.png" />
                                <span>编辑</span>
                              </li>
                              <li @click.stop="addNote(item, index, tagIdx)">
                                <img src="@/assets/img/summary/beizhu.png" />
                                <span>备注</span>
                              </li>
                              <!-- <li @click.stop="addNote(item, index, tagIdx)">
                                <span>A</span>
                              </li>
                              <li @click.stop="addNote(item, index, tagIdx)">
                                <span>B</span>
                              </li> -->
                              <!-- <li @click.stop="delRightMeal(item, tagIdx, index)">
                                <img src="@/assets/img/summary/shanchu.png" />
                                <span>删除</span>
                              </li> -->
                            </ul>
                            <p class="bottom_arrows" v-if="!floatViewClass"></p>
                          </div>
                          <i
                            class="el-icon-error close_i"
                            v-if="C_isEdit"
                            @click.stop="delRightMeal(item, tagIdx, index)"
                          ></i>
                          <!-- <i class="iconfont icon-shunxu sort_i tag_dom" title="拖动拆分" v-if="C_isEdit"></i>
                          <i class="el-icon-edit-outline note_icon" v-if="C_isEdit" title="添加备注" @click.stop="addNote(item, index, tagIdx)"></i> -->
                        </template>
                      </TagInput>
                      <!-- <el-tag
                        v-for="(tags, tagIdx) in item.deaTags"
                        :key="JSON.stringify(tags)"
                        :closable="C_isEdit"
                        class="meal_tag"
                        :class="{'define_disease': !tags.diseaseCode}"
                        @click="tagClick(tags)"
                        @close="delRightMeal(item, tagIdx, index)"
                        disable-transitions
                      >
                        <span class="tag_dom">
                          {{ tags.diseaseName + (tags.diseaseNameRemark?`(${tags.diseaseNameRemark})`:'') }}
                        </span>
                        <el-popover
                          placement="bottom-start"
                          width="370"
                          trigger="click"
                          :popper-class="'tag_popover ' + (queryDiseaseSuggestionsList.length !== 0?'tagPopover_height':'')"
                          ref="editSugTagPop_Ref"
                          @after-leave="popoverHide"
                          @after-enter="popoverEnter(tags)"
                          @hide="removeTagPopover"
                          :disabled="isExamine !== 1"
                        >
                          <div class="selDiv">
                            <el-input
                              class="input-new-tag"
                              v-model.trim="rightValue"
                              size="small"
                              ref="sugTagInp_Ref"
                              clearable
                              @input="sugTagSelDisease(tags)"
                              placeholder="请输入"
                              v-focus
                            >
                            </el-input>
                            <el-button size="small" @click="sugTagConfirm(tags)">确定</el-button>
                          </div>
                          <div class="tag_vxeList">
                            <vxe-list
                              v-if="isShowRight && !tags.diseaseCode"
                              height="100%"
                              :loading="loading1"
                              :data="queryDiseaseSuggestionsList"
                            >
                              <template v-slot="{ items }">
                                <div
                                  class="suDis"
                                  v-for="(diseaseItem) in items"
                                  :key="JSON.stringify(diseaseItem)"
                                  @dblclick="replaceSugTag(diseaseItem, tags, item)"
                                >
                                  <p class="disP">
                                    <i class="iconfont icon-biaodan icon"></i>
                                    {{ diseaseItem.diseaseName }}
                                  </p>
                                  <p class="suggP" :title="diseaseItem.suggestContent">
                                    {{ diseaseItem.suggestContent }}
                                  </p>
                                </div>
                              </template>
                            </vxe-list>
                          </div>
                          
                          <i slot="reference" title="编辑" class="el-icon-edit-outline editSugTag_icon" :class="{editSugTag_icon_show: (C_isEdit || isExamine === 1)}"></i>
                        </el-popover>
                      </el-tag> -->
                    </Vuedraggable>
                    <span class="iconSpan" v-if="C_isEdit">
                      <span
                        class="iconfont icon-qingqiuhebing merge"
                        @click="mergeFun(item)"
                        title="合并"
                      ></span>
                      <span
                        class="iconfont icon-shunxu move"
                        title="排序"
                        @mouseover.prevent="sortBtn()"
                      ></span>
                      <span
                        class="iconfont icon-shanchu"
                        @click="removeAt(index)"
                        title="删除"
                      ></span>
                    </span>
                  </div>
                  <div class="rightTitle">
                    <!-- <p
                      v-if="selectSuggest.id !== item.id"
                      @pointerdown.stop
                      @click="suggestDBL(item)"
                    >
                      {{ item.sugContent }}
                    </p> -->
                    <!-- <el-input
                      :disabled="!C_isEdit"
                      type="textarea"
                      autosize
                      v-focus
                      placeholder="请输入内容"
                      @blur="suggestInputBlur(item)"
                      @pointerdown.stop.native
                      v-model="item.sugContent"
                    >
                    </el-input> -->
                    <TextInput
                      :isEdit="!C_isEdit"
                      :sugContent="item.sugContent"
                      ref="textInput_Ref"
                      @blur="suggestInputBlur(arguments, item)"
                    />
                  </div>
                </div>
              </transition-group>
            </Vuedraggable>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { dataUtils } from '../../../common';
import Vuedraggable from 'vuedraggable';
import { mapGetters } from 'vuex';
import MainBtnCom from '../mainBtnCom.vue';
import XEUtils from 'xe-utils';
import TextInput from './textInput.vue';
import PublicTable from '@/components/publicTable2.vue';
import SummaryMixins from '../mixins/summaryMixins';
import TagInput from './tagInput.vue';
import AiProposal from '@/components/aiProposal.vue';
export default {
  mixins: [SummaryMixins],
  components: {
    Vuedraggable,
    MainBtnCom,
    TextInput,
    PublicTable,
    TagInput,
    AiProposal
  },
  props: {
    // 选中的体检人信息
    patientInfo: {
      type: Object,
      default: () => {
        return {};
      }
    },
    // 主检状态
    isMain: {
      type: Number,
      default: 2
    },
    isSummary: {
      type: Boolean,
      default: () => {
        return true;
      }
    },
    processFlag: {
      type: Boolean,
      default: () => {
        return false;
      }
    },
    // 主检标识
    mainFlag: {
      type: Boolean,
      default: false
    },
    // 主检标识
    isExamine: {
      type: Number,
      default: 1
    },
    // 是否是审核页面；
    isAuditPage: {
      type: Boolean,
      default: false
    },
    // 体检组合列表，用于显示综述信息检索列表
    recordCombItems: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      btnDisabled: true,
      btnDisabledZk: true,
      groupA: {
        name: 'item-tag',
        pull: false, //可以拖出
        put: true //可以拖入
      },
      regNo: '',
      record: [],
      recordFlag: false, // 获取综述建议不需要记录修改的判断；
      summarySearchVal: '',
      unselectSumList: [], //未选的综述列表
      summaryList: [], //综述列表
      activeIndex: '',
      isHaveModify: false,

      //   旧的数据
      checkComb: {},
      newSuggestions: [],
      fixed_newSuggestions: [],
      isAdd: false,
      inputValue: '',
      showRecordCombItems: false,
      addRight: false,
      rightValue: '',
      rightAddData: [],
      rightAddDataCopy: [],
      combList: [],
      combListCopy: [],
      muenId: '',
      defaultOpened: [],
      openedMenus: [],
      sumTagEdit: {
        editMode: 1, //1：新增；2：修改；3：删除
        sumTag: {},
        suggestions: []
      },
      sumTagId: 0,
      isEdit: false, //是否是修改综述标签
      editTag: {},
      isHave: false,
      combCode: '',
      combName: '',
      addTagValue: '',
      drag: false,
      mergeObj: {}, //合并的对象
      selectSuggest: {},
      isShowRight: false,
      queryDiseaseSuggestionsList: [],
      addSuggestions: {
        id: 0,
        sortIndex: 0,
        sugContent: '',
        deaTags: [
          {
            id: 0,
            diseaseCode: '',
            diseaseName: '',
            bindSummTags: []
          }
        ]
      },
      loading1: true,
      loading: true,
      addSumObj: {},
      addItem: {}, //点击组合的数据
      checkRowIdx: '',
      summarySuggestion: {},
      historicalOverview: ''
    };
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_codeDepartment', 'G_config']),
    C_isEdit() {
      return (
        !!this.regNo &&
        (this.isMain === 2 ||
          (this.isMain === 3 && this.isAuditPage) ||
          this.isExamine === 1)
      );
    },
    C_revocation() {
      let summaryList = dataUtils.deepCopy(this.summaryList);
      let arr = summaryList?.filter((item) => {
        let twoArr = item.summTags.filter((twoItem) => twoItem.tag != '');
        item.summTags = twoArr;
        return item.summTags?.length !== 0;
      });
      console.log(arr);
      let obj = {
        summaryList: [...arr],
        newSuggestions: [...dataUtils.deepCopy(this.newSuggestions || [])]
      };
      return obj;
    },
    C_isAi() {
      return this.G_config?.isAi || false;
    },
    C_summary() {
      //小结拼接
      let recordCombItems = dataUtils.deepCopy(this.recordCombItems);
      if (!this.historicalOverview && recordCombItems.length === 0) {
        this.btnDisabled = true;
        return false;
      }
      let trueList = recordCombItems.filter(
        (item) => item.isMedicalImageResult === true
      );
      let falseList = recordCombItems.filter(
        (item) => item.isMedicalImageResult !== true
      );
      let result = falseList
        .map((comb) => {
          const projectsStr = comb.projects
            .map((p) => {
              const value = p.results[p.results.length - 1]?.itemTag || '';
              return `${p.itemName}:${value}${p.unit || ''}`;
            })
            .join('、');

          let summary = comb.combinations.map((c) => c.combTag).join('、');
          return `${comb.combName}（${projectsStr}），小结：${summary}`;
        })
        .join('；\n');
      let imgRes = trueList
        .map((comb) => {
          const projectsStr = comb.projects
            .map((p) => {
              const value = p.results[p.results.length - 1]?.itemTag || '';
              return `${p.itemName}:${value}${p.unit || ''}`;
            })
            .join('、');

          let summary = comb.combinations.map((c) => c.combTag).join('、');
          return `${comb.combName}（${projectsStr}），小结：${summary}`;
        })
        .join('；\n');

      return {
        result,
        imgRes
      };
    },
    C_summarize() {
      let summaryList = dataUtils.deepCopy(this.summaryList);
      if (!this.historicalOverview && summaryList.length === 0) {
        return false;
      }
      let trueList = summaryList.filter(
        (item) => item.isMedicalImageResult === true
      );
      let falseList = summaryList.filter(
        (item) => item.isMedicalImageResult !== true
      );

      let result = falseList
        .map((item) => {
          const combName = item.combName;
          const tags = item.summTags.map((tagItem) => tagItem.tag).join(', ');
          return `${combName}: ${tags}`;
        })
        .join(', ');
      let imgRes = trueList
        .map((item) => {
          const combName = item.combName;
          const tags = item.summTags.map((tagItem) => tagItem.tag).join(', ');
          return `${combName}: ${tags}`;
        })
        .join(', ');
      return {
        result,
        imgRes
      };
    },
    C_result() {
      if (!this.C_summary) {
        this.btnDisabled = true;
        return '';
      }
      let historicalOverview = this.historicalOverview
        ? `根据前期来体检中心做检查，他的一些历年的体检数据如下:《${this.historicalOverview}》;`
        : '';
      let result = `${historicalOverview} \n本次体检的数据如下:《${this.C_summary.result}\n影像结果：\n${this.C_summary.imgRes || '暂无'}》;`;
      this.btnDisabled = false;
      return result;
    },
    C_resultZk() {
      if (!this.C_summarize && !this.C_summary) {
        this.btnDisabledZk = true;
        return '';
      }
      let result = `本次：\n项目结果：${this.C_summary.result}；\n项目影像结果：\n${this.C_summary.imgRes || '暂无'};\n综述: \n${this.C_summarize.result}；\n综述影像结果：${this.C_summarize.imgRes || '暂无'};\n历史：\n${this.historicalOverview || '暂无'}`;
      this.btnDisabledZk = false;
      return result;
    },
    C_dsParameter() {
      const data = {
        model: this.G_config?.ai2.model,
        stream: true,
        messages: [
          {
            role: 'system',
            content: this.G_config?.ai2.prompt
          },
          {
            role: 'user',
            content: `sex：${this.patientInfo.sex === 1 ? '男' : '女'}，age：${this.patientInfo.age}，${this.C_result} 按照上面的描述，给出体检中心专业的健康建议;历史的体检作为参考信息,以本次的体检作为输出项，不得输出非本次的项目,因为其他是作为参考作用，可能已经正常，如果实在需要输出请打上(参考历史数据);`
          }
        ]
      };
      return {
        headers: {
          Authorization: `Bearer ${this.G_config?.ai2.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      };
    },
    C_zKParameter() {
      const data = {
        model: this.G_config?.ai3.model,
        stream: true,
        messages: [
          {
            role: 'system',
            content: this.G_config?.ai3.prompt
          },
          {
            role: 'user',
            content: this.C_resultZk
          }
        ]
      };
      return {
        headers: {
          Authorization: `Bearer ${this.G_config?.ai3.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      };
    },
    C_pgParameter() {
      const data = {
        inputs: {
          type: '今天版本',
          prompt: this.G_config?.ai.prompt
        },
        query: this.C_result,
        type: '今天版本',
        response_mode: 'streaming',
        user: 'text'
      };
      return {
        headers: {
          Authorization: `Bearer ${this.G_config?.ai.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      };
    }
  },
  methods: {
    //获取历史小结
    getReadHistorySummary() {
      this.historicalOverview = '';
      if (!this.regNo) return;
      this.$ajax
        .paramsPost(this.$apiUrls.ReadHistoryCombResult, { regNo: this.regNo })
        .then((r) => {
          let { success } = r.data;
          if (success) {
            this.historicalOverview = r.data.returnData || '';
          }
        });
    },
    processingDataPg(res) {
      if (res.event !== 'ping') {
        let obj = JSON.parse(res.data);
        if (obj.event == 'message') {
          return {
            reflection: '',
            content: obj.answer
          };
        }
      }
    },
    processingDataDs(res) {
      let obj = JSON.parse(res.data);
      let data = {
        reflection: '',
        content: ''
      };
      data.reflection = obj.choices[0].delta.reasoning_content
        ? obj.choices[0].delta.reasoning_content
        : '';

      data.content = obj.choices[0].delta.content
        ? obj.choices[0].delta.content
        : '';

      return data;
    },
    sumTagPaste(e) {
      e.preventDefault();
      // 内容
      var text = '';
      // 贴贴数据
      var clp = (e.originalEvent || e).clipboardData;
      // 贴贴内容
      if (clp && clp.getData) {
        text = clp.getData('text/plain') || '';
      } else if (window.clipboardData && window.clipboardData.getData) {
        text = window.clipboardData.getData('text') || '';
      }
      // 内容不为空
      if (text !== '') {
        // 数据是否满足指定格式
        if (clp === undefined || clp === null) {
          // 是否有光标位置
          if (window.getSelection) {
            // 有则插入指定位置
            var newNode = document.createElement('span');
            newNode.innerHTML = text;
            window.getSelection().getRangeAt(0).insertNode(newNode);
          } else {
            // 没有则直接贴贴
            document.selection.createRange().pasteHTML(text);
          }
          // 需要手动调用 oninput 输入事件
        } else {
          // 插入内容，会自动调用 oninput 输入事件
          console.log(text);
          document.execCommand('insertText', false, text);
        }
      }
    },

    /**
     * @author: justin
     * @description: 新增综述内容
     * @param {*} item
     * @return {*}
     */
    addSumItem(item) {
      let sumItem = null; // 综述
      let suggestionItem = null; // 建议
      if (this.summarySuggestion) {
        sumItem =
          this.summarySuggestion.comSummarys.length > 0
            ? this.summarySuggestion.comSummarys[0]
            : null;
        suggestionItem =
          this.summarySuggestion.suggestions.length > 0
            ? this.summarySuggestion.suggestions
            : null;
      }
      if (!sumItem) {
        sumItem = {
          combCode: item.combCode,
          combName: item.combName,
          combSortIndex: 0,
          clsSortIndex: 0,
          checkCls: item.checkCls,
          sortIndex: 0,
          summTags: [
            {
              id: 0,
              tag: ''
            }
          ]
        };
      }
      this.summaryList.unshift(sumItem);
      if (suggestionItem) {
        this.newSuggestions.unshift(...suggestionItem);
      }
      this.$nextTick(() => {
        // 光标聚焦文字后面处理
        const inputRef = this.$refs[`sumTagRef_${item.combCode}_0`][0];
        inputRef.focus();
        // 创建一个范围对象
        let range = document.createRange();
        let selection = window.getSelection();
        // 设置范围的对象为一个文本节点
        range.selectNodeContents(inputRef);
        // 挪动光标到文本节点的末尾
        range.collapse(false); // false 表示将光标挪到范围的末尾
        // 清除任何已存在的选择，并设置新的选择范围
        selection.removeAllRanges();
        selection.addRange(range);
      });
    },
    // 综述编辑框失焦的回调
    sumTagBlur(tag, tagIndex, parentIdx, parentItem) {
      let tagTxt =
        this.$refs['sumTagRef_' + parentItem.combCode + '_' + tagIndex][0]
          .innerText;

      let editMode; //1 新增，2 修改
      if (tag.id === 0) {
        editMode = 1;
      } else {
        editMode = 2;
      }
      //   如果修改时，文字未改变则不提交
      if (tag.id !== 0 && tagTxt === tag.tag && tagTxt.trim() !== '') return;
      if (tag.id === 0 && tagTxt.trim() === '') {
        this.summaryList[parentIdx].summTags.splice(tagIndex, 1);
        this.$nextTick(() => {
          if (tagIndex === 0) {
            this.summaryList.splice(parentIdx, 1);
          }
        });
        return;
      }
      // 修改综述，ID不为空，输入框为空的处理
      if (tag.id !== 0 && tagTxt.trim() === '') {
        this.$confirm(`是否删除${tag.tag}综述？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.summaryList[parentIdx].summTags.splice(tagIndex, 1);
            this.sumDel(tag, tagIndex, parentItem, parentIdx, true);
            this.$nextTick(() => {
              if (tagIndex === 0) {
                this.summaryList.splice(parentIdx, 1);
              }
            });
          })
          .catch(() => {
            this.$refs[
              'sumTagRef_' + parentItem.combCode + '_' + tagIndex
            ][0].focus();
          });
        return;
      }
      let tagTxtArr = parentItem.summTags.map((item) => item.tag);
      let suggest = this.suggestHandle(this.newSuggestions);
      console.log(tagTxtArr);
      if (tag.id === 0 && tagTxtArr.includes(tagTxt)) {
        this.$refs[
          'sumTagRef_' + parentItem.combCode + '_' + tagIndex
        ][0].innerHTML = '';
        this.$refs[
          'sumTagRef_' + parentItem.combCode + '_' + tagIndex
        ][0].focus();
        this.$message({
          message: '已有相同的综述！',
          type: 'warning'
        });
        return;
      }
      tag.tag = tagTxt;
      let datas;
      if (tag.id === 0) {
        datas = {
          editMode,
          summTag: {
            id: 0,
            tag: tag.tag,
            combCode: parentItem.combCode
          },
          bindDiseaseTags: []
        };
      } else {
        let bindDiseaseTags = [];
        suggest.map((item) => {
          item.bindSummTags?.some((twoItem) => {
            if (tag.id === twoItem.summTagId) {
              bindDiseaseTags.push(item);
            }
          });
        });
        datas = {
          editMode,
          summTag: {
            id: tag.id,
            tag: tag.tag,
            combCode: parentItem.combCode
          },
          bindDiseaseTags: bindDiseaseTags
        };
      }
      console.log(datas);
      this.isHaveModify = true;
      this.$ajax
        .post(this.$apiUrls.EditSummaryTagV2, datas)
        .then((r) => {
          this.isHaveModify = false;

          let { success, returnData } = r.data;
          // 处理新增综述的ID
          if (editMode === 1) {
            tag.id = returnData.summTag.id;
            tag.tag = returnData.summTag.tag;
          }
          returnData.addSuggestions.map((item) => {
            let isHave = false;
            item.deaTags.map((deaTagItem) => {
              this.newSuggestions.map((suggestItem) => {
                suggestItem.deaTags.map((sugDeaTagItem) => {
                  if (deaTagItem.diseaseName === sugDeaTagItem.diseaseName) {
                    sugDeaTagItem.bindSummTags.push(deaTagItem.bindSummTags[0]);
                    isHave = true;
                  }
                });
              });
            });
            if (!isHave) {
              this.newSuggestions.unshift(item);
            }
          });
          returnData.modifyDiseases.map((item) => {
            suggest.map((sugItem) => {
              if (sugItem.id === item.id) {
                sugItem.diseaseName = item.diseaseName;
                sugItem.bindSummTags = item.bindSummTags;
              }
            });
          });
          returnData.deleteDiseases.map((item) => {
            this.newSuggestions.map((sugItem, idx) => {
              sugItem.deaTags.map((sugDeaTagItem, index) => {
                if (sugDeaTagItem.id == item) {
                  console.log(item);
                  if (sugItem.deaTags.length === 1) {
                    this.newSuggestions.splice(idx, 1);
                    idx--;
                  } else {
                    sugItem.deaTags.splice(index, 1);
                    index--;
                  }
                }
              });
            });
          });
          console.log(this.newSuggestions);
        })
        .catch(() => {
          this.isHaveModify = false;
        });
    },
    // 处理建议深层数组
    suggestHandle(suggestArr) {
      let deaTags = [];
      suggestArr.map((item) => {
        item?.deaTags.map((twoItem) => {
          deaTags.push(twoItem);
        });
      });
      return deaTags;
    },
    // 删除综述
    sumDel(tag, tagIndex, parentItem, parentIndex, flag = false) {
      let suggest = this.suggestHandle(this.newSuggestions);
      let delSuggest = [];
      console.log(suggest);
      suggest.map((item, idx) => {
        item.bindSummTags?.some((twoItem, twoIdx) => {
          console.log(twoItem, twoItem.summTagId, tag.id);
          if (twoItem.summTagId === tag.id) {
            item.bindSummTags.splice(twoIdx, 1);
            twoIdx--;
            console.log(item.bindSummTags);
            if (item.bindSummTags?.length === 0) {
              delSuggest.push(item.id);
            }
            return true;
          }
        });
      });
      console.log(delSuggest);
      let newSuggestions = [];
      this.newSuggestions.map((item) => {
        let deaTags = [];
        item?.deaTags.map((twoItem) => {
          if (!delSuggest.includes(twoItem.id)) {
            deaTags.push(twoItem);
          }
        });
        item.deaTags = deaTags;
        if (item.deaTags.length == 0 && item.id == this.mergeObj.id) {
          this.mergeObj = {};
        }
        if (item.deaTags.length !== 0) {
          newSuggestions.push(item);
        }
      });
      this.newSuggestions = newSuggestions;
      console.log(this.newSuggestions);
      if (flag) return;

      let summTags = dataUtils.deepCopy(parentItem.summTags);
      summTags.splice(tagIndex, 1);
      let row = dataUtils.deepCopy(parentItem);
      // parentItem.summTags = [];
      // console.log(parentItem);
      row.summTags = summTags;
      this.$set(this.summaryList, parentIndex, row);
      this.$nextTick(() => {
        if (row.summTags.length === 0) {
          this.summaryList.splice(parentIndex, 1);
        }
      });
    },
    // 复制成功的回调
    firstCopySuccess() {
      this.$message({
        message: '复制成功',
        type: 'success',
        showClose: true
      });
    },
    // 删除整个组合综述
    delComb(combItem, combIdx) {
      this.$confirm('是否删除整个组合综述？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          combItem.summTags.map((item, idx) => {
            console.log(item);
            this.sumDel(item, idx, combItem, combIdx, true);
          });
          let summaryList = dataUtils.deepCopy(this.summaryList);
          summaryList.splice(combIdx, 1);
          this.summaryList = summaryList;
        })
        .catch(() => {});
    },
    // 添加综述弹窗显示的回调
    sumPopover() {
      let checkArr = this.summaryList.map((item) => item.combCode);
      const newData = this.recordCombItems.filter(
        (item) => !checkArr.includes(item.combCode)
      );
      this.unselectSumList = dataUtils.deepCopy(newData);
      this.$nextTick(() => {
        this.$refs.tableRecordCombItem_ref.staticLoad(this.unselectSumList);
      });
    },
    sumInpKeydown(e, parentItem, tagIndex) {
      console.log(e);
      switch (e.keyCode) {
        case 13:
          e.preventDefault();
          this.$refs[`sumTagRef_${parentItem.combCode}_${tagIndex}`][0].blur();
          break;
        default:
          break;
      }
    },
    // 添加综述
    addSummary(sumItem) {
      console.log(sumItem);
      let tagTxtArr = sumItem.summTags.map((item) => item.tag);
      console.log(tagTxtArr);
      if (tagTxtArr.includes('')) {
        this.$refs[
          `sumTagRef_${sumItem.combCode}_${sumItem.summTags.length - 1}`
        ][0].focus();
        return;
      }
      sumItem.summTags.push({
        id: 0,
        tag: ''
      });
      this.$nextTick(() => {
        this.$refs[
          `sumTagRef_${sumItem.combCode}_${sumItem.summTags.length - 1}`
        ][0].innerHTML = '';
        this.$refs[
          `sumTagRef_${sumItem.combCode}_${sumItem.summTags.length - 1}`
        ][0].focus();
      });
    },
    // 处理综述和建议的排序
    handleSort() {
      this.summaryList?.map((item, index) => {
        item.sortIndex = index;
      });
      this.newSuggestions?.map((item, idx) => {
        item.sortIndex = idx;
        item.deaTags?.forEach((twoItem, twoIdx) => {
          twoItem.sortIndex = twoIdx;
        });
      });
    },
    // 综述移动结束的回调
    sumOnEnd(e) {
      console.log(e);
      let arr = dataUtils.deepCopy(this.summaryList);
      this.summaryList = [];
      this.$nextTick(() => {
        this.summaryList = arr;
      });
    },

    addDisDepartmentClick() {
      this.addTagValue = '';
      setTimeout(() => {
        this.$refs.addTagInput.focus();
      }, 300);
    },
    // 疾病建议添加
    addDiseaseClick() {
      Promise.all([this.getDeptDisease()]).then((r) => {
        console.log('r: ', r);
        this.loading = false;
        setTimeout(async () => {
          // 将树结构拍平，构建列表树结构
          XEUtils.eachTree(r[0], (item, index, items, paths, parent, nodes) => {
            // 层级
            item._LEVEL = nodes.length - 1;
            // 是否展开
            item._EXPAND = false;
            // 是否可视
            item._VISIBLE = !item._LEVEL;
            // 是否有子节点
            item._HAS_CHILDREN = item.children && item.children.length > 0;
            // 是否叶子节点
            item._IS_LEAF = !item._HAS_CHILDREN;
          });
          this.rightAddData = r[0];
          this.refreshTree();
          this.$nextTick(() => {
            this.$refs.rightInput.focus();
          });
        }, 200);
      });
    },
    //疾病列表
    getDeptDisease() {
      return new Promise((resolve, reject) => {
        this.$ajax.post(this.$apiUrls.GetDiseaseInDept, []).then((r) => {
          console.log('GetDiseaseInDept: ', r);
          let { success, returnData } = r.data;
          if (!success) return;
          let newData = [];
          returnData.forEach((item, index) => {
            newData.push({
              id: index,
              title: item.clsName,
              children: []
            });
            item.diseases.forEach((children, childIndex) => {
              newData[index].children.push({
                id: children.diseaseCode,
                title: children.diseaseName
              });
            });
          });
          resolve(newData);
        });
      });
    },
    // 切换树节点的展开、收缩
    toggleTreeNode(row) {
      if (row._HAS_CHILDREN) {
        this.setTreeExpand(row, !row._EXPAND);
      }
    },
    // 设置树节点的展开、收缩
    setTreeExpand(row, isExpand) {
      const matchObj = XEUtils.findTree(
        this.rightAddData,
        (item) => item === row
      );
      row._EXPAND = isExpand;
      if (matchObj) {
        XEUtils.eachTree(
          matchObj.item.children,
          (item, index, items, path, parent) => {
            item._VISIBLE = parent
              ? parent._EXPAND && parent._VISIBLE
              : isExpand;
          }
        );
      }
      this.refreshTree();
    },
    // 刷新树节点
    refreshTree() {
      const treeList = XEUtils.toTreeArray(this.rightAddData);
      this.rightAddDataCopy = treeList.filter((item) => item._VISIBLE);
      console.log(this.rightAddDataCopy);
    },
    // 疾病建议添加框隐藏
    popoverHide() {
      this.loading = true;
      this.loading1 = true;
      this.rightAddDataCopy = [];
      this.rightValue = '';
      this.queryDiseaseSuggestionsList = [];
      this.selDisease();
    },
    //过滤查询左边添加弹出
    selDepartment() {
      this.combList = this.combListCopy.filter((item) => {
        return item.combName.indexOf(this.inputValue) !== -1;
      });
    },
    //添加
    handleInputConfirm() {
      let inputValue = this.inputValue;
      let summTags = [];
      if (inputValue) {
        summTags.push({
          id: 0,
          tag: inputValue
        });
        this.newSumCombs.unshift({
          isChange: false,
          combCode: this.combCode,
          combName: this.combName,
          summTags: summTags
        });
      } else {
        this.combList.unshift(item);
        this.combListCopy = this.combList;
      }
      this.isAdd = false;
      this.inputValue = '';
    },
    //增加综述标签
    addTagFun(tagItem) {
      this.addTagValue = '';
      this.isEdit = false;
      tagItem.isChange = true;
      this.addItem = tagItem;
      console.log('[this.newSumCombs  ]-700', this.newSumCombs);
    },
    //删除
    delTagFun(tagItem) {
      console.log(tagItem);
      this.$confirm('是否确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          let checkArr = [tagItem.combCode];
          this.newSumCombs = this.newSumCombs.filter(
            (item) => !checkArr.includes(item.combCode)
          );
          tagItem.summTags.map((item) => {
            this.deleteSug(item);
          });
          this.combList.unshift(tagItem);
          return;
        })
        .catch(() => {
          return;
        });
    },
    //左边确定添加/修改综述标签
    addTagConfirm(item) {
      console.log('[ item ]-387', item);
      if (this.isAdd) {
        // if(this.addTagValue){
        this.newSumCombs.unshift(this.addSumObj);
        this.newSumCombs.sort((a, b) => {
          return a.sortIndex - b.sortIndex;
        });
        this.$nextTick(() => {
          console.log('[  this.newSumCombs ]-659', this.newSumCombs);
          // 获取要滚动到的元素。
          const scrollEle = document.getElementById(item.combCode);
          // 滚动到元素，使用 `smooth` 滚动方式。
          scrollEle.scrollIntoView({ behavior: 'smooth', block: 'end' });
        });

        // } else {
        //   this.combList.unshift(item)
        // }
        this.isAdd = false;
      }
      item.summTags.forEach((items) => {
        if (items.tag == this.addTagValue) {
          this.isHave = true;
          return;
        }
      });
      if (this.isHave) {
        this.$message({
          message: '已经有该标签，不能重复!',
          type: 'warning',
          showClose: true
        });
        this.isHave = false;
        item.isChange = false;
        this.addItem = {};
        return;
      }
      if (this.addTagValue) {
        if (this.isEdit) {
          this.sumTagEdit = {
            editMode: 2, //1：新增；2：修改；3：删除
            sumTag: { id: this.editTag.id, tag: this.addTagValue },
            suggestions: this.newSuggestions
          };
          item.summTags.forEach((items) => {
            if (items.tag == this.editTag.tag) {
              items.tag = this.addTagValue;
            }
          });
        } else {
          this.sumTagEdit = {
            editMode: 1, //1：新增；2：修改；3：删除
            sumTag: { id: 0, tag: this.addTagValue },
            suggestions: this.newSuggestions
          };
          this.editSumTag(item);
        }

        return (this.addItem = {});
      } else {
        this.addItem = {};
      }
    },
    //左边标签删除
    delLeftMeal(item, items, idx) {
      console.log('[ items ]-449', item, items);
      this.sumTagEdit = {
        editMode: 3, //1：新增；2：修改；3：删除
        sumTag: items,
        suggestions: dataUtils.deepCopy(this.newSuggestions)
      };
      console.log(this.sumTagEdit);
      this.removeObject(items);
      // this.editSumTag(item, items,idx);
      // this.deleteSug(items)
    },
    //前端删除综述
    removeObject(obj) {
      this.newSumCombs = this.newSumCombs
        .map((item) => ({
          ...item,
          summTags: item.summTags.filter(
            (tag) => tag.id !== obj.id || tag.tag !== obj.tag
          )
        }))
        .filter((item) => item.summTags.length > 0);
      //console.log("[ this.newSumCombs ]-844", this.newSumCombs);
    },
    // 删除关联的建议
    deleteSug(tag) {
      console.log(tag, this.$parent.$refs.summaryComs.newSuggestions);
      this.$parent.$refs.summaryComs.newSuggestions.map((item, idx, arr) => {
        let deaTags = item.deaTags.filter((twoItem, twoIdx, twoArr) => {
          let deaTagIdx = twoItem.bindSummTags.indexOf(tag.id);

          if (deaTagIdx != -1) {
            twoItem.bindSummTags.splice(deaTagIdx, 1);
          }
          return twoItem.bindSummTags?.length !== 0;
        });
        item.deaTags = deaTags;
        if (item.deaTags.length === 0) {
          arr.splice(idx, 1);
          idx--;
        }
      });
    },
    //修改左边标签
    editLiftTag(item, items) {
      console.log(item, items);
      if (this.isMain != 2 && this.isMain != 3) return;
      if (JSON.stringify(this.checkComb) !== '{}') {
        this.checkComb.isChange = false;
      }
      this.checkComb = item;
      this.isEdit = true;
      this.editTag = items;
      this.addTagValue = items.tag;
      return (this.addItem = item);
    },
    //新增编辑删除标签editMode://1：新增；2：修改；3：删除
    editSumTag(item, items, idx) {
      this.$ajax.post(this.$apiUrls.EditSumTag, this.sumTagEdit).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        // this.addTagValue = '';
        this.newSuggestions = returnData.suggestions;
        returnData.suggestions?.map((sugItem) => {
          let flag = true;
          this.$parent.$refs.summaryComs.newSuggestions.some(
            (twoItem, twoIdx, arr) => {
              console.log(sugItem, twoItem);
              if (sugItem.sugContent == twoItem.sugContent) {
                let deaTags = [];
                sugItem.deaTags.map((sugTagItem) => {
                  let isHaveDeaTag = false;
                  twoItem.deaTags.map((deaTagItem) => {
                    if (deaTagItem.id == sugTagItem.id) {
                      isHaveDeaTag = true;
                    }
                  });
                  if (!isHaveDeaTag) {
                    deaTags.push(sugTagItem);
                  }
                });
                twoItem.deaTags.push(...deaTags);
                flag = false;
              }
            }
          );
          if (flag) {
            this.$parent.$refs.summaryComs.newSuggestions.push(sugItem);
          }
        });
        this.sumTagId = returnData.sumTagId;
        if (this.sumTagEdit.editMode == 1) {
          item.summTags.push({
            id: this.sumTagId,
            tag: this.addTagValue
          });
          this.addSumObj = {};
        } else if (this.sumTagEdit.editMode == 3) {
          item.summTags.splice(item.summTags.indexOf(items), 1);
          if (item.summTags.length === 0) {
            this.newSumCombs.splice(idx, 1);
            this.combList.unshift(item);
          }
        }
      });
    },
    //过滤查询右边疾病添加弹出
    selDisease() {
      if (this.rightValue) {
        this.queryDiseaseSuggestions(0);
        this.isShowRight = true;
      } else {
        this.isShowRight = false;
      }
      // let selData = JSON.parse(JSON.stringify(this.rightAddDataCopy));
      // if (this.rightValue) {
      //   let data = [];
      //   let newArray = [];
      //   selData.forEach((now, index) => {
      //     newArray[index] = now;
      //     newArray[index].diseases = now.diseases.filter(item => {
      //       return item.diseaseName.indexOf(this.rightValue) !== -1;
      //     });
      //   });
      //   newArray.forEach((item, index) => {
      //     if (item.diseases.length != 0) {
      //       data.push(item);
      //     }
      //   });
      //   this.rightAddData = data;
      // } else {
      //   this.rightAddData = this.rightAddDataCopy;
      // }
    },
    //关键词查询疾病建议，用于添加新建议
    queryDiseaseSuggestions(queryType) {
      this.$ajax
        .post(this.$apiUrls.GetDiseaseInfo, '', {
          query: { keyword: this.rightValue?.trim(), queryType }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          console.log('[ returnData ]-193', returnData);
          this.queryDiseaseSuggestionsList = returnData || [];
          this.loading1 = false;
        });
    },
    //右边添加新的建议疾病
    addSugg(item) {
      if (!this.patientInfo.regNo) {
        this.$message({
          message: '请先选择体检人信息!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.isMain != 2 && this.isMain != 3) {
        this.$message({
          message: '不是未检状态，不可添加!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.newSuggestions[0]?.deaTags[0].diseaseCode === '正常建议') {
        this.newSuggestions = [];
      }
      let len = this.newSuggestions?.length;
      let diseaseId = Date.now();
      this.addSuggestions = {
        id: diseaseId + '',
        sortIndex: len + 1,
        sugContent: item.suggestContent,
        deaTags: [
          {
            id: len + 1,
            diseaseCode: item.diseaseCode,
            diseaseName: item.diseaseName,
            bindSummTags: []
          }
        ]
      };
      this.newSuggestions.unshift(this.addSuggestions);
      this.$refs.addRight.doClose(); //关闭弹出
    },
    //直接添加
    addDisFun(keyword) {
      if (!this.patientInfo.regNo) {
        this.$message({
          message: '请先选择体检人信息!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.isMain != 2 && this.isMain != 3) {
        this.$message({
          message: '不是未检状态，不可添加!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.newSuggestions[0]?.deaTags[0].diseaseCode === '正常建议') {
        this.newSuggestions = [];
      }
      this.$ajax
        .post(this.$apiUrls.QueryDiseaseSuggestions, '', {
          query: { keyword: keyword }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          console.log('[ returnData ]-629', returnData);
          this.$refs.addRight.doClose(); //关闭弹出
          let len = this.newSuggestions?.length;
          let diseaseId = Date.now();
          this.addSuggestions = {
            id: diseaseId + '',
            sortIndex: len + 1,
            sugContent: returnData[0]?.suggestContent,
            deaTags: [
              {
                id: len + 1,
                diseaseCode: returnData[0]?.diseaseCode,
                diseaseName: returnData[0]?.diseaseName,
                bindSummTags: []
              }
            ]
          };
          this.newSuggestions.unshift(this.addSuggestions);
          console.log('[ this.newSuggestions ]-676', this.newSuggestions);
        });
    },
    // 右边标签删除
    delRightMeal(item, index, parentIdx) {
      if (item.deaTags?.length == 1) {
        this.newSuggestions.splice(parentIdx, 1);
        return;
      }
      item.deaTags.splice(index, 1);
    },
    tagClick(tag) {
      console.log(tag);
    },
    // 开始拖拽事件
    onStart() {
      this.drag = true;
    },
    sortBtn() {
      this.$refs['textInput_Ref'].forEach((item) => {
        item.$refs.text_Ref.blur();
      });
    },
    // 拖拽结束事件
    onEnd() {
      this.drag = false;
    },
    // 建议删除
    removeAt(index) {
      this.$confirm('是否确认删除这条建议?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          if (this.newSuggestions[index].id == this.mergeObj?.id) {
            this.mergeObj = {};
          }
          this.newSuggestions.splice(index, 1);
        })
        .catch(() => {});
    },
    // handleOpen(key, keyPath) {
    //   console.log(key, keyPath, this.muenId);
    //   this.openedMenus = this.$refs.elMenu_Ref.$data.openedMenus;
    //   console.log(this.openedMenus);
    // },
    // handleClose(key, keyPath) {
    //   this.openedMenus = this.$refs.elMenu_Ref.$data.openedMenus;
    //   console.log(this.openedMenus);
    // },
    // handleSelect(index, indexPath) {},
    // 合并
    mergeFun(row) {
      console.log(row);
      if (JSON.stringify(this.mergeObj) == '{}') {
        this.mergeObj = row;
        return;
      }
      if (this.mergeObj.id == row.id) {
        this.mergeObj = {};
        return;
      }
      // 合并处理
      row.deaTags.push(...this.mergeObj.deaTags);
      console.log(
        row.sugContent
          .replace(/[\r\n]/g, '')
          .trim()
          .indexOf(this.mergeObj.sugContent.replace(/[\r\n]/g, '').trim())
      );
      if (
        row.sugContent
          .replace(/[\r\n]/g, '')
          .trim()
          .indexOf(this.mergeObj.sugContent.replace(/[\r\n]/g, '').trim()) == -1
      ) {
        row.sugContent += `\n${this.mergeObj.sugContent}`;
      }
      this.newSuggestions.splice(this.newSuggestions.indexOf(this.mergeObj), 1);
      this.mergeObj = {};
    },
    // 撤销修改
    revocationBtn() {
      console.log('this.record', this.record);
      // return
      this.recordFlag = true;
      let obj = this.record.pop();
      console.log(obj);
      this.summaryList = [];
      this.newSuggestions = [];
      this.mergeObj = {};
      this.$nextTick(() => {
        this.recordFlag = true;
        this.summaryList = dataUtils.deepCopy(obj.summaryList);
        this.newSuggestions = dataUtils.deepCopy(obj.newSuggestions);
      });
      return;
    },
    // 建议的双击回调
    suggestDBL(row) {
      if (this.isMain != 2 && this.isMain != 3) return;
      this.selectSuggest = row;
    },
    // 建议编辑输入框失去焦点和回车的回调
    suggestInputBlur(argument, row) {
      console.log(argument, row);
      row.sugContent = argument[0];
      this.selectSuggest = {};
    },
    // tag移动开始回调
    tagStart(r, arr, parent) {
      console.log(r, arr, parent);
      let argument = r[0];
      let addObj = arr[argument.newIndex];
      let oldObj = dataUtils.deepCopy(arr[argument.newIndex]);
      console.log(oldObj);
      let datas = {
        diseaseCode: addObj.diseaseCode
      };
      this.$ajax.paramsPost(this.$apiUrls.GetSuggestion, datas).then((r) => {
        // 合并
        if (parent) {
          if (
            parent.sugContent
              .replace(/[\r\n]/g, '')
              .trim()
              .indexOf(r.data.returnData.replace(/[\r\n]/g, '').trim()) == -1
          ) {
            parent.sugContent += r.data.returnData;
          }
        } else {
          console.log(888888888888888888, oldObj);
          // 拆分
          addObj.deaTags = [{ ...oldObj }];
          addObj.id = '0';
          setTimeout(() => {
            addObj.id = oldObj.id;
            addObj.sortIndex = argument.newIndex;
            addObj.sugContent = r.data.returnData;
            console.log(addObj);
          }, 50);
        }
        this.newSuggestions.map((item, idx, oldArr) => {
          if (item.deaTags?.length == 0) {
            oldArr.splice(idx, 1);
            idx--;
          }
          if (item.id == oldObj.id) {
            item.id = item.deaTags[0].id;
          }
        });
      });
    },
    // tag移动结束回调
    tagEnd(r) {},
    // 自定义控制
    onMove(deaTags) {
      return () => {
        if (deaTags?.length == 1) return false;
        return true;
      };
    },
    //清空
    emptys() {
      if (this.newSuggestions.length < 1) {
        this.$message({
          message: '没有可清空的体检建议!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm('是否清空本次体检建议?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.mergeObj = {};
          this.newSuggestions = [];
          this.$message({
            type: 'success',
            message: '已清空本次体检建议!'
          });
        })
        .catch(() => {});
    },
    // 正常建议的双击回调
    normalSuggest() {
      if (!this.patientInfo.regNo) {
        this.$message({
          message: '请先选择体检人信息!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.isMain != 2 && this.isMain != 3) {
        this.$message({
          message: '不是未检状态，不可添加!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.newSuggestions = [];
      this.$ajax.post(this.$apiUrls.AddNormalSuggestions).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.newSuggestions.unshift({
          ...returnData[0],
          sugContent: returnData[0]?.deaTags[0].diseaseName
        });
        this.addRight = false;
        this.selectSuggest = this.newSuggestions[0];
      });
    },
    // 综述的排序
    combSort(rowIdx) {
      if (this.checkRowIdx === '') {
        this.checkRowIdx = rowIdx;
        return;
      }
      if (this.checkRowIdx === rowIdx) {
        this.checkRowIdx = '';
        return;
      }
      console.log(rowIdx);
      this.swapArrayElements(this.newSumCombs, this.checkRowIdx, rowIdx);
      setTimeout(() => {
        console.log(this.newSumCombs);
      }, 200);
    },
    // 数组位置互换
    swapArrayElements(arr, index1, index2) {
      [arr[index1], arr[index2]] = [arr[index2], arr[index1]];
      this.$forceUpdate();
      this.checkRowIdx = '';
    },

    /**
     * @author: justin
     * @description: 根据组合编号，生成综述建议
     * @param {*} combCodes 组合编号数组
     * @return {*}
     */
    async generateSummarySuggestions(combCodes) {
      if (!combCodes || !Array.isArray(combCodes)) return null;

      let summarySuggestion = null;
      await this.$ajax
        .post(this.$apiUrls.GenerateSummarySuggestionFromRecordV2, combCodes, {
          query: { regNo: this.patientInfo.regNo, isOccupation: false }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;

          summarySuggestion = returnData;
        });

      return summarySuggestion;
    },
    /**
     * @author: justin
     * @description: 新建综述建议
     * @param {*} row
     * @return {*}
     */
    newSumByRecordCombItem(row) {
      const that = this;
      that.showRecordCombItems = false; //关闭弹出
      that.summarySuggestion = null;
      that.addSumItem(row);
      that.summarySearchVal = '';
    },

    /**
     * @author: justin
     * @description: 引用综述建议
     * @param {*} row
     * @return {*}
     */
    async useSumByRecordCombItem(row) {
      const that = this;
      that.showRecordCombItems = false; //关闭弹出
      that.summarySuggestion = await that.generateSummarySuggestions([
        row.combCode
      ]);
      that.addSumItem(row);
      that.summarySearchVal = '';
    },
    /**
     * @author: justin
     * @description: 过滤添加综述的项目
     * @param {*} data
     * @return {*}
     */
    filterRecordCombItems(data) {
      if (!data) return [];
      if (!this.summarySearchVal || this.summarySearchVal.trim().length === 0)
        return data;

      return data.filter((item) => {
        return item.combName
          .toLowerCase()
          .includes(this.summarySearchVal.trim().toLowerCase());
      });
    }
  },
  watch: {
    summaryList: {
      handler(n, o) {
        this.sumPopover();
        console.log(n);
      },
      immediate: true
    },
    C_revocation: {
      handler(n, o) {
        if (this.recordFlag) {
          this.recordFlag = false;
          return;
        }
        if (
          JSON.stringify(n) == JSON.stringify(o) ||
          JSON.stringify(o).indexOf(`"id":0,`) != -1
        ) {
          return;
        }
        this.record.push(dataUtils.deepCopy(o));
      },
      deep: true
    },
    recordCombItems: {
      handler(n, o) {
        const that = this;
        that.$nextTick(() => {
          that.sumPopover();
        });
      },
      immediate: true
    },
    patientInfo: {
      handler(n, o) {
        const that = this;
        if (that.$refs.aiProposal) that.$refs.aiProposal.clear();
        if (that.$refs.pgAiProposal) that.$refs.pgAiProposal.clear();
        if (that.$refs.zkAiProposal) that.$refs.pgAiProposal.clear();
        that.$nextTick(() => {
          that.regNo = n?.regNo;
          that.getReadHistorySummary();
          // 切换患者，重置滚动条位置
          that.$refs.rightCont_Ref.scrollTop = 0;
        });
      },
      immediate: true,
      deep: true
    }
  },
  created() {},
  mounted() {}
};
</script>
<style lang="less" scoped>
.summary_com {
  flex: 1;
  overflow: auto;
  flex-shrink: 0;
  display: flex;
  .com_div {
    overflow: auto;
    display: flex;
    flex-direction: column;
    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 5px 0;
      & > span {
        font-family: PingFangSC-Medium;
        font-size: 16px;
        color: #2d3436;
      }
    }
    .content_dom {
      border: 1px solid #b2bec3;
      border-radius: 4px;
      flex: 1;
      overflow: auto;
      background: rgba(178, 190, 195, 0.1);
    }
  }
  .letf_dom {
    width: 40%;
    margin-right: 5px;
    .titleLeft {
      height: 38px;
      line-height: 38px;
      border-radius: 4px;
      padding: 0 5px;
      & > p {
        display: flex;
        justify-content: space-between;
        font-family: PingFangSC-Semibold;
        font-size: 14px;
        color: #2d3436;
        span:first-child {
          font-size: 16px;
          font-weight: 600;
          flex: 1;
          flex-shrink: 0;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
      .iconSpan {
        width: 60px;
        display: flex;
        justify-content: space-between;
        margin: auto 5px;
        & > span {
          cursor: pointer;
          color: rgba(45, 52, 54, 0.6);
          font-weight: normal;
          flex: 1;
        }
        .mover {
          cursor: move;
        }
      }
    }
    .meal_tag_wrap {
      min-height: 33px;
      background: #fff;
      padding: 3px;
      border-radius: 4px;
      // overflow: hidden;
      padding-bottom: 0;
      .sumTag {
        display: inline-block;
        position: relative;
        vertical-align: middle;
        margin: 0 3px 3px 0;
        span {
          display: inline-block;
          min-width: 24px;
          background-color: #ecf5ff;
          border-color: #d9ecff;
          padding: 8px 5px;
          font-size: 14px;
          border-width: 1px;
          border-style: solid;
          border-radius: 4px;
          color: #409eff;
        }
        i {
          font-size: 18px;
          color: #f21919;
          cursor: pointer;
          display: none;
          position: absolute;
          top: -5px;
        }
        .del_btn {
          left: -3px;
        }
        .copy_btn {
          right: -3px;
        }
        &:hover i {
          display: block;
        }
      }
    }
  }
  .right_dom {
    flex: 1;
    flex-shrink: 0;
  }

  .rightDiv {
    // flex: 1;
    width: 100%;
    height: 100%;
    overflow: auto;
    display: flex;
    flex-direction: column;
    p {
      display: flex;
      margin: auto 0;
      & > span {
        margin-right: 10px;
      }
    }
    .rightCont {
      flex: 1;
      height: 100%;
      overflow: auto;
      //   border: 1px solid #ccc;
      //   border-radius: 4px;
      //   padding: 3px;
      background: rgba(178, 190, 195, 0.1);
      .contDiv {
        // margin-bottom: 5px;
        border-bottom: 1px dashed rgba(178, 190, 195, 0.7);
        padding: 3px 0;
        &:first-child {
          padding-top: 0;
        }
        .meal_tag_wrap {
          min-height: 33px;
          background: #fff;
          padding: 3px 3px 0 3px;
          border-radius: 4px;
          .tag_dom {
            -webkit-user-select: none; /* Safari */
            -moz-user-select: none; /* Firefox */
            -ms-user-select: none; /* IE10+/Edge */
            user-select: none; /* 标准语法 */
          }
        }
        .el-tag {
          margin-bottom: 3px;
          margin-right: 3px;
          font-family: PingFangSC-Medium;
          font-size: 16px;
          color: #1770df;
          white-space: pre-line;
          word-wrap: break-word;
          height: auto;
          line-height: 25px;
        }
        .rightTag {
          min-height: 38px;
          border-radius: 4px;
          display: flex;
          align-items: center;
          padding-bottom: 3px;
          .index {
            margin: auto;
            width: 22px;
            text-align: center;
            font-weight: 600;
          }
          .iconSpan {
            display: flex;
            & > span {
              cursor: pointer;
              margin-left: 18px;
              color: rgba(45, 52, 54, 0.6);
            }
            // .merge,
            .move {
              cursor: move;
            }
          }
        }
        .rightTitle {
          margin-left: 22px;
          height: auto !important;
          // padding: 5px 0;
          & > p {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-family: PingFangSC-Semibold;
            font-size: 16px;
            color: #2d3436;
            min-height: 30px;
            // line-height: 38px;
            padding: 3px 3px;
            background: rgba(23, 112, 223, 0.1);
            border-radius: 4px;
          }
          /deep/ textarea {
            font-size: 16px;
            padding: 3px;
            background: #dddddd;
            color: #2b3436;
            &:disabled {
              opacity: 1 !important;
            }
          }
        }
      }
      .merge_active {
        border: 2px solid #1770df;
      }
    }

    .index {
      font-family: PingFangSC-Semibold;
      font-size: 14px;
      color: #2d3436;
      margin-right: 10px;
    }
    i {
      cursor: pointer;
      margin: auto 0;
      font-size: 20px;
    }
    header {
      background-color: #fff;
      justify-content: flex-start;
      align-items: center;
      display: flex;
      line-height: 38px;
    }
    .itemTitle {
      background: rgba(23, 112, 223, 0.1);
      border-radius: 4px;
      line-height: 38px;
      margin-left: 20px;
      padding: 0 10px;
      font-size: 14px;
    }
    .meal_tag_wrap {
      flex: 1;
      flex-shrink: 0;
      // overflow: auto;
      // white-space: nowrap;
    }
    .meal_tag {
      position: relative;
      .editSugTag_icon {
        position: absolute;
        top: 0;
        left: 0;
        transform: translate(-50%, -50%);
        color: #f21919;
        display: none;
      }
    }
    .meal_tag:hover {
      cursor: move;
      .editSugTag_icon_show {
        display: block;
      }
    }
    // .define_disease{
    //   color: #333 !important;
    //   background: #eee;
    // }
  }
}
@media screen and(max-width: 1440px) {
  .summary_com {
    .com_div {
      .title {
        margin: 1px 0 !important;
      }
    }
    .letf_dom {
      .titleLeft {
        height: 24px;
        line-height: 24px;
      }
      .meal_tag_wrap .sumTag span {
        padding: 2px 5px;
      }
    }
  }
}
</style>

<style lang="less">
.addLeft {
  .combinations {
    display: block;
    width: 190px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.tooltip-combinations {
  border: 1px solid #b2bec3 !important;

  .el-tag {
    max-width: 200px;
    margin-bottom: 5px;
    white-space: normal;
    height: auto;
  }
}
.tag_popover {
  display: flex;
  flex-direction: column;
  overflow: auto;
  .tag_vxeList {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    .vxe-list {
      height: 100%;
      overflow: auto;
    }
  }
}
.tagPopover_height {
  height: 450px;
}
// .ai-popover{
//   .ai-txt{
//         width: 100%;
//         display: flex;
//         white-space: normal;
//         overflow:hidden;
//         flex-direction: column;
//         gap: 10px;
//         height: calc(100vh - 80px);
//         &>div:nth-child(1){
//           width: 100%;
//           flex: 1;
//           overflow-y: auto;
//           white-space: normal;
//         }
//     }
//     table {
//       width: 100%;
//       border-collapse: collapse;
//       margin: 20px 0;
//       font-size: 14px;
//       text-align: left;
//     }

//     th, td {
//       padding: 5px;
//       border: 1px solid #ddd;
//     }

//     th {
//       background-color: #f2f2f2;
//       white-space: nowrap;
//     }

//     tr:nth-child(even) {
//       background-color: #f9f9f9;
//     }
//     h1, h2, h3, h4, h5, h6, p, ol, li {
//       margin: 10px 0;
//     }
//     .btn{
//        margin-top: auto;
//        height: 30px;
//        display: flex;
//        align-items: center;
//        justify-content: center;
//     }
//   }
</style>
