<!--
 * @FilePath: \KrPeis\src\pages\mainInspectionPage\component\choicePeReisterDialog.vue
 * @Description:  主检、审核：
 根据关键字（体检号、姓名）检索后，显示检索结果的弹窗选择列表
 * @Author: justin
 * @Date: 2024-04-11 14:44:23
 * @Version: 0.0.1
 * @LastEditors: justin
 * @LastEditTime: 2024-04-15 08:58:02
*
-->
<template>
  <el-dialog
    :title="title"
    :visible.sync="_show"
    custom-class="dialog-table"
    @open="getReportPeRegisters"
    @close="cancel"
    append-to-body
  >
    <div class="dialog-body">
      <!-- 表格 -->
      <PublicTable
        :viewTableList.sync="gridData"
        :theads.sync="columnName"
        :columnWidth="columnWidth"
        :columnSort="columnSorts"
        @currentChange="handleCurrentChanges"
        @rowDblclick="rowDblclick"
        :isSortShow="false"
        ref="patientTable"
        style="height: 320px"
      >
        <template
          v-for="prop in Object.keys(columnName)"
          v-slot:[prop]="{ scope }"
        >
          <div :key="prop">
            {{ scope.row[prop] }}
          </div>
        </template>
      </PublicTable>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="cancel">取 消</el-button>
      <el-button type="primary" @click="confirm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { dataUtils } from '@/common';
import PublicTable from '@/components/publicTable.vue';
export default {
  name: 'choicePeReisterDialog',
  components: {
    PublicTable
  },
  props: {
    title: {
      type: String,
      default: '身份信息列表'
    },
    keyword: {
      // 关键字
      type: String,
      default: ''
    },
    peStatuss: {
      // 体检状态集合
      type: Array,
      default: []
    },
    show: {
      // 是否显示
      type: Boolean,
      default: false
    },
    useCheckStatusShow: {
      // 是否使用检查状态展示,否则默认是体检状态
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      columnName: {
        statusName: '状态',
        name: '姓名',
        sexName: '性别',
        age: '年龄',
        regNo: '体检号',
        patCode: '档案号',
        peClsName: '体检分类',
        registerTime: '登记时间'
      },
      columnWidth: {
        statusName: 80,
        name: 80,
        sexName: 50,
        age: 50,
        regNo: 120,
        patCode: 100,
        peClsName: 80,
        registerTime: 160
      },
      columnSorts: ['statusName', 'name', 'regNo', 'patCode', 'registerTime'],
      gridData: [],
      choiceData: null,
      loading: false
    };
  },
  computed: {
    _show: {
      get: function () {
        return this.show;
      },
      set: function (val) {
        this.$emit('update:show', val);
      }
    }
  },
  methods: {
    /**
     * @author: justin
     * @description: 主页登记/患者信息根据体检号/姓名检索
     * @return {*}
     **/
    getReportPeRegisters(isOccupation) {
      const that = this;
      if (dataUtils.isEmpty(that.keyword)) {
        that.$message.warning('请输入检索关键字');
        return;
      }

      that.loading = true;
      let data = {
        keyword: that.keyword,
        peStatuss: that.peStatuss,
        isOccupation: isOccupation
      };
      that.$ajax
        .post(that.$apiUrls.GetReportPeRegistersV2, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;

          if (!returnData || returnData.length === 0) {
            that.$message.success(`暂未找到符合条件的记录：${that.keyword}`);
            return;
          } else if (returnData && returnData.length === 1) {
            that.choiceData = returnData[0];
            that.confirm();
            return;
          } else {
            that.$emit('show-event');
          }

          that.gridData = returnData.map((item) => {
            item.statusName = that.useCheckStatusShow
              ? item.checkStatusName
              : item.peStatusName;
            return item;
          });
        })
        .finally((_) => {
          that.loading = false;
        });
    },

    /**
     * @author: justin
     * @description: 行双击事件
     * @param {*} row
     * @return {*}
     */
    rowDblclick(row) {
      this.choiceData = row;
      this.confirm();
    },

    /**
     * @author: justin
     * @description: 当前行数据
     * @param {*} row
     * @return {*}
     */
    handleCurrentChanges(row) {
      if (!row) return;
      this.choiceData = row;
    },

    /**
     * @author: justin
     * @description: 确认选择行数据
     * @return {*}
     */
    confirm() {
      if (!this.choiceData) {
        this.$message.warning('请选择记录信息');
        return;
      }

      this.$emit('confirm', this.choiceData);
    },

    /**
     * @author: justin
     * @description: 取消选择
     * @return {*}
     */
    cancel() {
      this.resetData();
      this.$emit('cancel');
    },

    /**
     * @author: justin
     * @description: 重置数据
     * @return {*}
     */
    resetData() {
      this.gridData = [];
      this.choiceData = null;
      this.loading = false;
    }
  }
};
</script>

<style lang="less">
.dialog-table {
  height: 520px !important;
  width: 800px;
  display: flex;
  flex-direction: column;
  padding: 15px;
  /deep/.el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #409eff;
    border-color: #409eff;
  }
}
</style>
