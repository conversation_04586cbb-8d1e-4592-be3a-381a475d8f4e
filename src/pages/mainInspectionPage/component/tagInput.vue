<template>
  <div class="tag-select-container">
    <span class="tagSpan_wrap" :class="className">
      <slot name="tagSpanInput" :noteShow="noteShow"></slot>
    </span>
    <el-select
      ref="diseaseSelect"
      v-model="selectedDisease"
      filterable
      remote
      reserve-keyword
      :remote-method="GetDiseasesByName"
      :loading="loading"
      popper-class="disease-select-dropdown"
      v-show="showPull"
      @change="handleSelectChange"
      @visible-change="handleVisibleChange"
      placeholder="请输入疾病名称"
      popper-append-to-body
    >
      <el-option
        v-for="(item, idx) in diseaseList"
        :key="item.diseaseCode"
        :label="item.diseaseName"
        :value="item"
        :class="{ 'is-active': checkNum === idx }"
      >
        <span>{{ item.diseaseName }}</span>
      </el-option>
    </el-select>
  </div>
</template>

<script>
import { _throttle } from '@/common/throttle.js';
export default {
  name: 'tagSpanInput',
  props: {
    className: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      diseaseList: [],
      showPull: false,
      checkNum: null,
      noteShow: false,
      loading: false,
      selectedDisease: null
    };
  },
  methods: {
    // 获取疾病列表
    GetDiseasesByName(diseaseName) {
      if (!diseaseName) {
        this.diseaseList = [];
        return;
      }

      this.loading = true;
      this.$ajax
        .paramsPost(this.$apiUrls.GetDiseasesByName, {
          diseaseName
        })
        .then((r) => {
          this.loading = false;
          let { success, returnData } = r.data;
          if (!success) return;
          this.diseaseList = returnData || [];

          // 如果有数据，自动显示下拉框
          if (this.diseaseList.length > 0) {
            this.showPull = true;
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 处理选择变化
    handleSelectChange(value) {
      if (value) {
        this.selectDisease(value);
        this.selectedDisease = null; // 清空选择，以便下次选择
        this.showPull = false;
      }
    },
    // 处理下拉框显示状态变化
    handleVisibleChange(visible) {
      // 更新showPull状态，与el-select的visible状态保持一致
      this.showPull = visible;
    },
    // 疾病列表的点击回调
    selectDisease(row) {
      this.$emit('selectDisease', row);
    },
    // 删除疾病
    tagSpanDel() {
      this.$emit('tagSpanDel');
    }
  }
};
</script>

<style lang="less" scoped>
.tag-select-container {
  position: relative;
  display: inline-block;

  .tagSpan_wrap {
    position: relative;
    display: inline-block;
    border: 1px solid #d9ecff;
    border-radius: 4px;
    margin-bottom: 3px;
    margin-right: 3px;
    background-color: #ecf5ff;
    font-size: 16px;

    &:hover .float_view {
      display: block;
    }

    span {
      font-size: 16px;
      white-space: pre-line;
      word-wrap: break-word;
      line-height: 25px;
    }

    .tagSpan_input {
      color: #1770df;
      padding: 4px 5px 4px 10px;
    }

    .note_span {
      padding: 4px 2px;
    }

    .float_view {
      position: absolute;
      bottom: 100%;
      left: 0;
      z-index: 1;
      display: none;

      ul {
        display: flex;
        padding: 3px;
        background: #fff;
        box-shadow: 0px 1px 5px #000;
        border-radius: 4px;
      }

      li {
        display: flex;
        align-items: center;
        flex: 1;
        width: 55px;
        justify-content: center;
        cursor: pointer;
        color: #248bff;

        img {
          height: 20px;
          flex-shrink: 0;
        }

        span {
          flex-shrink: 0;
        }

        & ~ li {
          border-left: 1px solid #ccc;
        }

        &:first-child {
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
          user-select: none;
        }
      }

      p {
        height: 5px;
        width: 100%;
        position: relative;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;

        &::after,
        &::before {
          position: absolute;
          left: 10px;
          display: block;
          content: '';
          height: 0;
          width: 0;
          border: 5px solid transparent;
        }
      }

      .bottom_arrows {
        &::after,
        &::before {
          border-top-color: #ccc;
          z-index: 1;
        }

        &::before {
          bottom: -4px;
          border-top-color: #fff;
          z-index: 2;
        }
      }

      .top_arrows {
        &::after,
        &::before {
          border-bottom-color: #ccc;
          z-index: 1;
          top: -5px;
        }

        &::before {
          top: -4px;
          border-bottom-color: #fff;
          z-index: 2;
        }
      }
    }

    .float_view_bottom {
      top: 100%;
      bottom: auto;
    }

    .close_i {
      position: absolute;
      top: -5px;
      left: -5px;
      z-index: 2;
      font-size: 16px;
      color: #f21919;
      cursor: pointer;
      display: none;
    }

    .note_icon {
      position: absolute;
      top: -5px;
      left: 10px;
      display: none;
      z-index: 2;
      color: #1770df;
    }

    &:hover {
      .close_i,
      .sort_i,
      .note_icon {
        display: block;
      }
    }
  }

  /deep/.define_disease {
    background: #eee;
    span {
      color: #333 !important;
    }
  }

  /deep/.el-select {
    position: absolute;
    width: 0;
    height: 0;
    opacity: 0;
  }
}

// 自定义下拉菜单样式
/deep/.disease-select-dropdown {
  max-height: 200px;
  min-width: 200px !important;

  .el-select-dropdown__wrap {
    max-height: 200px;
  }

  .el-select-dropdown__item {
    line-height: 30px;
    padding: 0 5px;
    font-size: 14px;

    &.selected,
    &.is-active {
      background-color: #1770df;
      color: #fff;
    }

    &:hover:not(.selected):not(.is-active) {
      background-color: #f5f7fa;
      color: #000;
    }
  }
}
</style>
