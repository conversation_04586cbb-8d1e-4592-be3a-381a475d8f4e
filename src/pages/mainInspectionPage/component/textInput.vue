<template>
  <el-input
    ref="text_Ref"
    class="text_dom"
    :disabled="isEdit"
    type="textarea"
    autosize
    v-focus
    placeholder="请输入内容"
    @blur="blur"
    @pointerdown.stop.native
    v-model="val"
  >
  </el-input>
</template>

<script>
export default {
  props: {
    isEdit: {
      type: Boolean,
      default: false
    },
    sugContent: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      val: this.sugContent
    };
  },
  methods: {
    blur() {
      this.$emit('blur', this.val);
    }
  },
  mounted() {
    this.$nextTick(() => {
      console.log(
        this.$refs.text_Ref.$el.parentNode,
        this.$refs.text_Ref.$el.scrollHeight
      );
      let el = this.$refs.text_Ref.$el.parentNode;
      el.style.height = this.$refs.text_Ref.$el.scrollHeight + 'px';
    });
  },
  watch: {
    sugContent: {
      handler(n, o) {
        this.val = n;
      },
      deep: true
    }
  }
};
</script>
