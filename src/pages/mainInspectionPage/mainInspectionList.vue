<template>
  <div class="mainInspection">
    <div class="topBtn">
      <div class="leftTop">
        <el-popover
          placement="bottom-start"
          width="100%"
          trigger="click"
          ref="listPopver"
          popper-class="popverClass"
        >
          <el-tabs v-model="activeMain3" @tab-click="mainClick3">
            <el-tab-pane label="待主检" name="main1">
              <Distribution
                :viewTableList="viewTableList"
                @getReportConclution="getReportConclution"
              ></Distribution>
            </el-tab-pane>
            <el-tab-pane label="已主检" name="main2">
              <Popover
                @getReportConclution="getReportConclution"
                ref="main2"
              ></Popover>
            </el-tab-pane>
            <el-tab-pane label="已审核" name="main3">
              <Distribution
                :viewTableList="viewTableList"
                @getReportConclution="getReportConclution"
              ></Distribution>
            </el-tab-pane>
          </el-tabs>
          <MainBtnCom
            :btnList="['表格数据']"
            @mainCheckList="mainCheckList"
            slot="reference"
          />
        </el-popover>
        <p>
          <label>体检号</label>
          <el-input
            size="small"
            placeholder="体检号"
            v-model="regNo"
            clearable
            @clear="clearSearch"
            @keyup.enter.native="regNoSearchs"
          ></el-input>
        </p>
      </div>

      <MainBtnCom
        :btnList="[
          '生成结论',
          '咨询列表',
          '历史报告',
          '主检',
          '取消主检',
          '暂存'
        ]"
        @createReport="createReport"
        @consultList="consultList"
        @historyReport="historyReport"
        @mainTest="mainTest"
        @cancelMainTest="cancelMainTest"
        @temporarySave="mainTest(false)"
        :isMain="isMain"
      />
    </div>
    <div class="topTitle">
      <el-collapse class="collapse">
        <el-collapse-item>
          <template slot="title">
            <ul class="type_head">
              <li>
                <div class="every_inp regNo_inp">
                  <label>体检号: </label>
                  <p>{{ reportConclution.patientInfo.regNo }}</p>
                </div>
                <div class="every_inp less_inp">
                  <label>姓名: </label>
                  <p>{{ reportConclution.patientInfo.name }}</p>
                </div>
                <div class="every_inp less_inp">
                  <label>性别: </label>
                  <p>{{ enum_sex[reportConclution.patientInfo.sex] }}</p>
                </div>
                <div class="every_inp less_inp">
                  <label>年龄: </label>
                  <p>
                    {{
                      reportConclution.patientInfo.ageUnit
                        ? reportConclution.patientInfo.age +
                          enum_ageUnit[reportConclution.patientInfo.ageUnit]
                        : reportConclution.patientInfo.age
                    }}
                  </p>
                </div>
                <div class="every_inp flex_inp">
                  <label>单位: </label>
                  <p
                    :title="reportConclution.patientInfo.companyName"
                    style="overflow-y: auto"
                  >
                    {{ reportConclution.patientInfo.companyName }}
                  </p>
                </div>
                <div class="every_inp cont_inp">
                  <label>套餐: </label>
                  <p
                    :title="reportConclution.patientInfo.clusterName"
                    style="
                      width: 100%;
                      overflow: hidden;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                    "
                  >
                    {{ reportConclution.patientInfo.clusterName }}
                  </p>
                </div>
              </li>
            </ul>
          </template>
          <div>
            <ul class="type_head">
              <li>
                <div class="every_inp regNo_inp">
                  <label>联系电话: </label>
                  <p>{{ reportConclution.patientInfo.tel }}</p>
                </div>
                <div class="every_inp num_inp">
                  <label>身份证: </label>
                  <p>{{ reportConclution.patientInfo.cardNo }}</p>
                </div>
                <div class="every_inp less_inp">
                  <label>婚姻状况: </label>
                  <p>
                    {{
                      enum_marrStatus[reportConclution.patientInfo.marryStatus]
                    }}
                  </p>
                </div>
                <div class="every_inp cont_inp">
                  <label>联系地址: </label>
                  <p
                    :title="reportConclution.patientInfo.address"
                    style="
                      width: 100%;
                      overflow: hidden;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                    "
                  >
                    {{ reportConclution.patientInfo.address }}
                  </p>
                </div>
                <div class="every_inp flex_inp">
                  <label>体检类型: </label>
                  <p>
                    {{ enum_peCls[reportConclution.patientInfo.enum_peCls] }}
                  </p>
                </div>
              </li>
            </ul>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
    <div class="centerCont">
      <div class="con_wrap left_wrap">
        <p>
          项目结果：
          <i class="el-icon-s-unfold"></i>
        </p>
        <div class="bottom_wrap">
          <el-collapse
            v-model="activeNames"
            @change="handleChange"
            size="small"
          >
            <el-collapse-item
              :title="item.combName"
              :name="item.combCode"
              v-for="item in projectResult"
              :key="item.combCode"
            >
              <template slot="title">
                <div class="collapse_div">
                  <span>{{ item.combName }}</span>
                  <div style="display: flex; align-items: center">
                    <span class="operNameClass"
                      >体检医生:{{
                        item.doctorName ? item.doctorName : '无'
                      }}</span
                    >
                    <el-button
                      class="violet_btn btn"
                      size="mini"
                      icon="iconfont icon-zaixianzixun"
                      @click.stop="consultingClick(item)"
                    >
                      咨询
                    </el-button>
                  </div>
                </div>
              </template>
              <!-- 项目列表 -->
              <el-table
                size="small"
                :data="item.projects"
                style="width: 100%; font-size: 14px"
                :header-cell-style="headerCellStyle"
              >
                <el-table-column prop="itemName" label="项目名称" width="180">
                </el-table-column>
                <el-table-column prop="address" label="结果">
                  <template #default="scope">
                    <div
                      class="result_div"
                      :title="C_result(scope.row.results)"
                    >
                      <el-popover
                        placement="right"
                        popper-class="my_popover"
                        width="200"
                        trigger="hover"
                        @hide="onPopoverHide"
                      >
                        <ul class="popover_ul">
                          <li>
                            <label for="">参考值：</label>
                            <span>{{ disposeFKVal(scope.row) }}</span>
                          </li>
                          <!-- <li>
                            <label for="">异常程度：</label>
                            <span>{{scope.row.hint}}</span>
                          </li> -->
                        </ul>
                        <span
                          slot="reference"
                          :class="
                            scope.row.isError ? 'redClass' : 'defaultClass'
                          "
                          ><i class="popover_i">{{ scope.row.hint }}</i
                          >{{ C_result(scope.row.results) }}</span
                        >
                      </el-popover>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="参考值">
                  <template #default="scope">
                    {{ disposeFKVal(scope.row) }}
                  </template>
                </el-table-column>
                <el-table-column prop="unit" label="单位"></el-table-column>
              </el-table>
              <!-- 体检小结 -->
              <div class="project_result">
                <span>体检小结：</span>
                <div>
                  <el-tag
                    size="small"
                    :class="result.isError ? 'tags_red' : 'tags_sty'"
                    v-for="result in item.combinations"
                    :key="result.index"
                    >{{ result.combTag }}</el-tag
                  >
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
      <div class="right_wrap">
        <div class="center_wrap">
          <!-- 综述 -->
          <div class="con_wrap overview">
            <summaryCom
              :sumCombs="reportConclution.summary.sumCombs"
              :suggestions="suggestions"
              :isMain="isMain"
              :isExamine="isExamine"
              :patientInfo="reportConclution.patientInfo"
              ref="summaryCom"
            ></summaryCom>
          </div>
          <!-- 建议 -->
          <div class="con_wrap suggest">
            <!-- <summaryCom
                :sumCombs.sync="reportConclution.summary.sumCombs"
                :suggestions.sync="suggestions"
                :isSummary="!isSummary"
                mainFlag
                ref="summaryComs"
              ></summaryCom> -->

            <summaryCom
              :sumCombs="reportConclution.summary.sumCombs"
              :suggestions="suggestions"
              :isSummary="!isSummary"
              :patientInfo="reportConclution.patientInfo"
              mainFlag
              ref="summaryComs"
              :isMain="isMain"
              :isExamine="isExamine"
            ></summaryCom>
          </div>
        </div>
        <!-- 结论 -->
        <div class="conclusion_wrap">
          <div class="footerCont">
            <div class="footer1">
              <label>本次体检结论</label>
              <p>
                <el-select
                  placeholder="请选择"
                  size="small"
                  :disabled="isMain != 2"
                  filterable
                  allow-create
                  default-first-option
                  clearable
                  v-model="reportConclution.conclusion"
                >
                  <el-option
                    v-for="item in conclusionList"
                    :key="item.tempCode"
                    :label="item.result"
                    :value="item.result"
                  >
                  </el-option>
                </el-select>
              </p>
            </div>
            <div class="footer2">
              <div class="foot_inp">
                <label>主检医师</label>
                <p>
                  <el-input
                    size="small"
                    clearable
                    readonly
                    v-model="reportConclution.checkDoctorName"
                  ></el-input>
                </p>
              </div>
              <div class="foot_inp">
                <label>主检日期</label>
                <p>
                  <el-date-picker
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    :clearable="false"
                    size="small"
                    readonly
                    v-model="reportConclution.checkTime"
                  >
                  </el-date-picker>
                </p>
              </div>
              <div class="foot_inp">
                <label>审核医师</label>
                <p>
                  <el-input
                    size="small"
                    clearable
                    readonly
                    v-model="reportConclution.auditDoctorName"
                  ></el-input>
                </p>
              </div>
              <div class="foot_inp">
                <label>审核日期</label>
                <p>
                  <el-date-picker
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    :clearable="false"
                    size="small"
                    readonly
                    v-model="reportConclution.auditTime"
                  >
                  </el-date-picker>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-drawer
      title="医生咨询回复一览表"
      :visible.sync="consultDialog"
      :before-close="consultDialogClose"
      :wrapperClosable="false"
      size="90%"
      v-if="consultDialog"
    >
      <Consult />
    </el-drawer>
    <el-drawer
      title="历史报告"
      :visible.sync="hisDrawer"
      :before-close="hisDrawerClose"
      :wrapperClosable="false"
      size="90%"
      v-if="hisDrawer"
    >
      <HistoryReport :regNo="reportConclution.patientInfo.regNo" />
    </el-drawer>
    <el-dialog
      title="医生解锁列表"
      :visible.sync="unlockShow"
      :close-on-click-modal="false"
      width="80%"
    >
      <div style="height: 500px; overflow: auto">
        <Unlock ref="unlock" :tableData="tableData"></Unlock>
      </div>
    </el-dialog>
    <!-- 重大阳性按钮 -->
    <!-- <div class="positive_btn" @click="PositiveResultShow">
      <i class="el-icon-d-arrow-left"></i>
      重阳
    </div> -->
    <!-- 重大阳性抽屉 -->
    <el-drawer
      title="重大阳性结果"
      :visible.sync="positiveResultDialog"
      :wrapperClosable="false"
      size="90%"
    >
      <PositiveResult
        :patientInfo.sync="reportConclution"
        :P_projectResult="projectResult"
        v-if="positiveResultDialog"
      />
    </el-drawer>
    <!-- 咨询弹窗 -->
    <el-dialog
      :visible.sync="consultingShow"
      width="800px"
      top="12%"
      custom-class="consultingShow"
      :close-on-click-modal="false"
      @closed="cancel"
    >
      <div slot="title" class="dialog-title">咨询录入窗口</div>
      <el-input
        type="textarea"
        :rows="20"
        placeholder="请输入咨询内容"
        v-model="consultingInput"
        style="margin-top: 20px"
      >
      </el-input>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel" size="small">取消</el-button>
        <el-button class="blue_btn" @click="consultingSubmit" size="small"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import PublicTable from '../../components/publicTable.vue';
import Consult from './consult.vue';
import MainBtnCom from './mainBtnCom.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
import SummaryCom from './summaryCom.vue';
import Distribution from './distribution.vue';
import NoDistribution from './noDistribution.vue';
import ResultEntry from '../inspectIn/resultEntry.vue';
import Popover from './popover.vue';
import HistoryReport from '../inspectIn/components/doctorWorkStation/historyReport.vue';
import Unlock from './unlock.vue';
import backout from './mixins/backout';
import mainInspectionMiXins from './mixins/mainInspection';
import PositiveResult from './positiveResult.vue';
import consult from './mixins/consult';
export default {
  name: 'mainInspection',
  mixins: [backout, mainInspectionMiXins, consult],
  components: {
    PublicTable,
    MainBtnCom,
    Consult,
    SummaryCom,
    Distribution,
    NoDistribution,
    ResultEntry,
    Popover,
    HistoryReport,
    Unlock,
    PositiveResult
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_userInfo']),
    C_result() {
      return function (result) {
        let txt = '';
        (result || []).map((item, idx) => {
          txt += item.itemTag + (idx + 1 == result.length ? '' : ';');
        });
        return txt;
      };
    }
  },
  data() {
    return {
      // 表头样式
      headerCellStyle: {
        background: 'rgba(23,112,223,.2)',
        fontSize: '14px',
        color: '#2D3436'
      },
      isSummary: true,
      tabPosition: 'left',
      processFlag: false,
      mainInspectionFlag: false,
      activeMain3: 'main1',
      consultDialog: false,
      tjCls: '',
      activeName: 'first',
      viewTableList: [],
      isMain: 2, //2表示未检，3表示已主检
      conclusionList: [],
      enum_marrStatus: {
        0: '未知',
        1: '未婚',
        2: '已婚',
        3: '离异',
        4: '丧偶'
      },
      //性别 (查全部 = -1,通用 = 0, 男 = 1, 女 = 2)
      enum_sex: {
        '-1': '全部',
        0: '通用',
        1: '男',
        2: '女'
      },
      enum_ageUnit: {
        null: '',
        0: '岁',
        1: '月'
      },
      enum_peCls: {
        0: '入职',
        1: '司机年审',
        2: '单位体检',
        3: '个人健康体检',
        4: '复查',
        5: '外检',
        6: '单项体检',
        7: '入学',
        8: '职业病',
        9: '健康证'
      },
      regNo: '', //体检号,//220924000001
      reportConclution: {
        patientInfo: {}, //个人信息
        summary: {}, //综述
        suggestions: [] //建议
      },
      checkStatus: 1, //主检的各个状态（1：待主检；2：已主检；3：已审核）
      hisDrawer: false,
      isExamine: 1
    };
  },
  created() {},
  mounted() {
    this.getConclusion();
    this.getPatientList();
  },
  methods: {
    //获取主检列表弹出
    getPatientList() {
      let data = {
        operatorCode: this.G_userInfo.codeOper.operatorCode,
        checkStatus: this.checkStatus
      };
      this.$ajax.post(this.$apiUrls.GetPatientList4Allocate, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.viewTableList = returnData;
      });
    },

    //体检号查询
    async regNoSearch(regNo) {
      this.getReportConclution(regNo).then((r) => {
        if (this.activeName == 'third') {
          this.$refs.resultEntry_Ref.headerInfo = r.patientInfo;
          this.$refs.resultEntry_Ref.getNavList();
        }
      });
    },
    //页面体检号输入回车查询
    async regNoSearchs() {
      this.getReportConclution(this.regNo).then((r) => {
        this.regNo = '';
        if (this.activeName == 'third') {
          this.$refs.resultEntry_Ref.headerInfo = r.patientInfo;
          this.$refs.resultEntry_Ref.getNavList();
        }
      });
    },
    //清空操作
    clearSearch() {
      this.reportConclution = {
        patientInfo: {}, //个人信息
        summary: {}, //综述
        suggestions: [] //建议
      };
      this.projectResult = [];
      this.suggestions = [];
      this.$refs.summaryCom.newSumCombs = [];
    },
    //获取报告结论
    getReportConclution(regNo) {
      this.projectResult = [];
      this.suggestions = [];
      this.$refs.summaryCom.newSumCombs = [];
      return new Promise((resolve, reject) => {
        if (!regNo) {
          this.$message({
            message: '没有体检号无法查询!',
            type: 'warning',
            showClose: true
          });
          return;
        }
        //this.regNo = regNo;
        this.$refs.listPopver.doClose();
        this.GetSimpleItemResult(regNo);
        this.$ajax
          .post(this.$apiUrls.GetReportConclution, [], {
            query: { regNo: regNo, checkAudit: 0 }
          })
          .then((r) => {
            let { success, returnData } = r.data;
            if (!success) return;
            if (returnData.patientInfo.peStatus === 4) {
              this.isExamine = 2;
            } else if (returnData.patientInfo.peStatus === 2) {
              this.isExamine = 1;
            } else {
              this.isExamine = 0;
            }
            //this.reportConclution = returnData;
            this.suggestions = dataUtils.deepCopy(returnData.suggestions);
            this.$nextTick(() => {
              this.activeName = 'first';
              this.$refs.summaryCom.getRecordCombs(regNo);
            });
            this.isMain = returnData.patientInfo.peStatus;
            if (this.isMain === 2) {
              returnData.checkDoctorName = '';
              returnData.checkTime = '';
            }
            this.reportConclution = returnData;
            console.log('[ this.reportConclution ]-354', this.reportConclution);
            resolve(returnData);
          });
      });
    },
    getConclusion() {
      this.$ajax
        .post(this.$apiUrls.RD_CodeConclusionTemplate + '/Read', [])
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.conclusionList = returnData || [];
        });
    },
    //保存主检
    mainTest(isMainCheck = true) {
      if (!this.reportConclution.patientInfo.regNo) {
        this.$message({
          message: '请选择体检人!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      //主检医生
      this.reportConclution.checkDoctorCode =
        this.G_userInfo.codeOper.operatorCode;
      this.reportConclution.checkDoctorName = this.G_userInfo.codeOper.name;
      this.reportConclution.summary.sumCombs =
        this.$refs.summaryCom.newSumCombs;
      this.reportConclution.suggestions = this.$refs.summaryComs.newSuggestions;
      if (!this.reportConclution.conclusion) {
        this.reportConclution.conclusion = '';
      }
      this.reportConclution.patientInfo.isMainCheck = isMainCheck;
      console.log('[ this.reportConclution) ]-433', this.reportConclution);
      this.$ajax
        .post(this.$apiUrls.SaveReportConclusion, this.reportConclution)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$message({
            message: '主检保存成功!',
            type: 'success',
            showClose: true
          });
          this.isMain = 3;
          this.getReportConclution(this.reportConclution.patientInfo.regNo); //刷新数据
          this.getPatientList();
          this.$refs.main2.changeTime();
        });
    },
    // 取消保存主检
    cancelMainTest() {
      if (!this.reportConclution.patientInfo.regNo) {
        this.$message({
          message: '请先选择要取消保存的结论!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$ajax
        .post(this.$apiUrls.CancelReportConclusion, [], {
          query: { regNo: this.reportConclution.patientInfo.regNo }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$message({
            message: '取消主检成功!',
            type: 'success',
            showClose: true
          });
          this.isMain = 2;
          this.getReportConclution(this.reportConclution.patientInfo.regNo); //刷新数据
          this.getPatientList();
          this.$refs.main2.changeTime();
        });
    },
    mainCheckList() {},
    mainClick3(tab, event) {
      console.log('[ this.activeMain3 ]-520', this.activeMain3);
      console.log(tab, event);
      if (this.activeMain3 == 'main1') {
        this.checkStatus = 1;
      } else if (this.activeMain3 == 'main3') {
        this.checkStatus = 3;
      }
      this.getPatientList();
    },

    handleClick(tab, event) {
      console.log(tab, event);
      this.$refs.summaryComs.newSuggestions = this.reportConclution.suggestions;
      if (this.activeName == 'third') {
        this.$refs.resultEntry_Ref.headerInfo =
          this.reportConclution.patientInfo;
        this.$refs.resultEntry_Ref.getNavList();
      }
    },

    consultList() {
      this.consultDialog = true;
    },
    consultDialogClose() {
      this.consultDialog = false;
    },
    // 生成结论
    createReport() {
      if (!this.reportConclution.patientInfo.regNo) {
        this.$message({
          message: '没有体检号无法生成结论!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm('是否确定要生成结论?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.generateSummarySuggestions();
        })
        .catch(() => {});
    },
    //生成结论
    generateSummarySuggestions() {
      this.$ajax
        .post(this.$apiUrls.GenerateSummarySuggestions, [], {
          query: { regNo: this.reportConclution.patientInfo.regNo }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$refs.summaryComs.suggestions = returnData.suggestions;
          this.$refs.summaryCom.sumCombs = returnData.summary.sumCombs;
          this.$refs.summaryComs.newSuggestions = returnData.suggestions;
          this.$refs.summaryCom.newSumCombs = returnData.summary.sumCombs;
          this.$nextTick(() => {
            this.$refs.summaryCom.getRecordCombs(
              this.reportConclution.patientInfo.regNo
            );
          });
          this.$message({
            type: 'success',
            message: '已重新生成结论!'
          });
        });
    },
    //关闭历史报告弹出
    hisDrawerClose() {
      this.hisDrawer = false;
    },
    historyReport() {
      if (!this.reportConclution.patientInfo.regNo) {
        this.$message({
          message: '请先点击要查看历史报告的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.hisDrawer = true;
    },
    // 处理参考值
    disposeFKVal(row) {
      let lLimit = row.lowerLimit;
      let uLimit = row.upperLimit;
      if (lLimit && !uLimit) {
        return `${Number(lLimit) ? '≥' : ''}${lLimit}`;
      }
      if (uLimit && !lLimit) {
        return `${Number(uLimit) ? '≤' : ''}${uLimit}`;
      }
      if (lLimit && uLimit) {
        return `${lLimit}-${uLimit}`;
      }
    },
    // 销毁前的回调
    onPopoverHide() {
      let node = document.querySelector('body>.my_popover'); //隐藏时删除
      node.remove();
    }
  }
};
</script>
<style lang="less" scoped>
.mainInspection {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  display: flex;
  color: #2d3436;
  flex-direction: column;
  /deep/.el-table__row--striped td.el-table__cell,
  .vxe-table--render-default .vxe-body--row.row--stripe {
    background-color: #e7f0fb !important;
  }
  .topBtn {
    // height: 68px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    .leftTop {
      display: flex;
      align-items: center;
      p {
        // line-height: 68px;
        display: flex;
        align-items: center;
        margin-left: 20px;
        margin-right: 20px;
        width: 200px;
        label {
          width: 60px;
          font-size: 14px;
          font-weight: 600;
        }
      }
    }
  }
  /deep/.el-popover {
    height: calc(100% - 142px);
    height: 600px !important;
  }
  .topTitle {
    // height: 90px;
    background: #fef0d8;
    box-shadow: 0 2px 8px 0 rgba(186, 133, 37, 0.1);
    border-radius: 2px;
    border-radius: 2px;
  }
  .collapse /deep/.el-collapse-item__header {
    background: #fef0d8;
    height: 28px;
  }
  .type_head {
    padding: 0 10px 0 18px !important;
    li {
      height: 28px !important;
    }
  }
  /deep/.el-collapse-item__content {
    padding-bottom: 0;
  }
  .type_head {
    width: 100%;
    background: #fef0d8;
    border-radius: 4px;
    border-radius: 4px;
    padding: 10px 10px 10px 18px;
    &:last-child {
      padding-top: 0;
    }
    li {
      display: flex;
      height: 32px;
      font-family: PingFangSC-Medium;
      font-size: 14px;
      color: #2d3436;
    }

    .every_inp {
      display: flex;
      align-items: center;
      margin-right: 10px;

      label {
        margin-right: 10px;
        width: auto;
        text-align: right;
      }
      p {
        flex: 1;
        font-size: 14px;
      }
    }
    .regNo_inp {
      width: 180px;
    }
    .less_inp {
      width: 100px;
    }
    .cont_inp {
      width: 300px;
    }
    .num_inp {
      width: 210px;
    }
    .flex_inp {
      flex: 1;
    }
  }
  .centerCont {
    flex: 1;
    overflow: auto;
    background: #fff;
    padding: 0 5px 5px;
    display: flex;
    .el-table {
      color: #000;
    }
    .con_wrap {
      display: flex;
      flex-direction: column;
      margin-right: 5px;
      p {
        display: flex;
        justify-content: space-between;
        height: 41px;
        align-items: center;
        font-size: 16px;
        color: #2d3436;
      }
      .bottom_wrap {
        border: 1px solid #b2bec3;
        border-radius: 4px;
        flex: 1;
        flex-shrink: 0;
        overflow: auto;
        .result_div {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        /deep/.el-collapse-item__header {
          background: rgba(178, 190, 195, 0.1);
          padding: 0 5px;
          font-size: 18px;
          color: #2d3436;
          line-height: normal;
          font-weight: 600;
          height: auto;
          min-height: 48px;
        }
        .project_result {
          padding: 0 5px;
          margin-top: 5px;
          display: flex;
          span {
            font-size: 14px;
            color: #1770df;
          }
          & > div {
            flex: 1;
            flex-shrink: 0;
          }
          .tags_sty {
            margin-right: 5px;
            margin-bottom: 5px;
            white-space: normal;
            color: #1770df;
            height: auto;
            &:last-child {
              margin-right: 0;
            }
          }
          .tags_red {
            margin-right: 5px;
            margin-bottom: 5px;
            white-space: normal;
            height: auto;
            color: red;
            &:last-child {
              margin-right: 0;
            }
          }
        }
      }
      .redClass {
        color: red;
        font-weight: 600;
      }
      .defaultClass {
        color: #000;
        font-weight: 600;
      }
      /deep/.SummaryCom {
        flex: 1;
        flex-shrink: 0;
        overflow: auto;
      }
      /deep/.leftCont {
        overflow: auto;
      }
    }
    .left_wrap {
      width: 30%;
      flex-shrink: 0;
    }
    .right_wrap {
      flex: 1;
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      .center_wrap {
        flex: 1;
        flex-shrink: 0;
        display: flex;
        overflow: auto;
        /deep/.SummaryCom .title > span {
          font-size: 16px;
          font-weight: normal;
        }
        /deep/.SummaryCom .title {
          margin: 5px 0;
        }
        /deep/.SummaryCom .leftDiv .leftCont .contDiv .titleLeft {
          height: auto;
          padding: 5px;
          line-height: normal;
          span:first-child {
            flex: 1;
            flex-shrink: 0;
          }
        }
      }
      .overview {
        width: 40%;
        flex-shrink: 0;
      }
      .suggest {
        flex: 1;
        flex-shrink: 0;
        margin-right: 0;
      }
    }
  }
  .centerCont-tabs {
    /deep/.el-tabs__item {
      padding: 0 10px;
    }
  }
  .tab-pane {
    padding: 10px 10px 10px 0;
  }
  .footerCont {
    // height: 118px;
    width: 100%;
    background: #fff;
    .footer1 {
      display: flex;
      line-height: 32px;
      width: 100%;
      margin: 3px 0;
      label {
        padding-right: 10px;
        font-size: 18px;
        font-weight: 600;
      }

      p {
        flex: 1;
        & > div {
          width: 100%;
        }
        /deep/.el-input--small .el-input__inner {
          color: #000;
          font-weight: 600;
        }
      }
    }
    .footer2 {
      display: flex;
      line-height: 32px;
      .foot_inp {
        flex: 1;
        display: flex;
        align-items: center;
        margin-right: 28px;
        &:last-child {
          margin-right: 0;
        }
        overflow: auto;
        label {
          width: 70px;
          font-size: 14px;
          font-weight: 600;
        }
        p {
          flex: 1;
          overflow: hidden;
        }
        /deep/.el-input--small .el-input__inner {
          color: #000;
          font-weight: 600;
        }
      }
      .el-date-editor.el-input,
      .el-date-editor.el-input__inner {
        width: 100%;
      }
    }
  }
  /deep/.el-date-editor.el-input,
  /deep/.el-date-editor.el-input__inner {
    width: 100%;
  }
  /deep/.el-drawer__header {
    align-items: center;
    display: flex;
    background: rgba(23, 112, 223, 0.2);
    border-radius: 4px 4px 0 0;
    line-height: 42px;
    font-family: PingFangSC-Medium;
    font-size: 18px;
    padding: 0;
    padding-left: 20px;
    color: #2d3436;
    margin-bottom: 0;
  }
  /deep/.el-drawer__close-btn {
    font-size: 30px;
  }
  .firstPage {
    flex: 1;
    overflow: auto;
  }
  .el-tabs {
    height: 100%;
    display: flex;
    flex-direction: row;
  }
  /deep/.el-tabs__content {
    flex: 1;
  }
  .el-tab-pane {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  /deep/.btnHeader {
    padding: 0 !important;
    margin-bottom: 10px;
  }
  /deep/.rightcont {
    padding: 0 !important;
  }
  /deep/.rightBom {
    padding: 10px 0 0 !important;
  }
  /deep/.el-dialog__header {
    align-items: center;
    display: flex;
    background: rgba(23, 112, 223, 0.2);
    border-radius: 4px 4px 0 0;
    line-height: 42px;
    height: 42px;
    font-family: PingFangSC-Medium;
    font-size: 18px;
    padding: 0;
    padding-left: 20px;
    color: #2d3436;
    margin-bottom: 0;
  }
  /deep/.el-dialog__headerbtn {
    font-size: 30px;
    top: 5px;
  }
  /deep/.el-dialog__body {
    padding-top: 0;
  }
  .positive_btn {
    position: fixed;
    top: 20%;
    right: 0;
    background: #d63031;
    border-radius: 22px 0 0 22px;
    font-size: 14px;
    height: 44px;
    line-height: 44px;
    width: 60px;
    text-align: center;
    color: #fff;
    cursor: pointer;
  }
  .collapse_div {
    display: flex;
    flex: 1;
    justify-content: space-between;
    .operNameClass {
      margin-left: 10px;
      margin-right: 8px;
      text-align: right;
      font-weight: 400;
      font-size: 14px;
      width: -moz-max-content;
      width: max-content;
    }
  }
}
</style>

<style lang="less">
.popverClass {
  height: calc(100% - 200px);
  /* overflow: auto; */
  /* left: 20px !important; */
}
.popverClass .el-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.popverClass .el-tabs__content {
  flex: 1;
  overflow: auto;
}
.popverClass .el-tab-pane {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.popoverDiv {
  display: flex;
  flex-direction: row;
  height: 100%;
}
.popoverDiv .poDiv1 {
  margin-right: 20px;
  overflow: auto;
  flex: 1;
}
.popoverDiv .poDiv2 {
  overflow: auto;
  flex: 1;
}
.my_popover {
  background: #dae4f2;
  .popper__arrow::after {
    border-right-color: rgba(0, 0, 0, 0.6) !important;
  }
  li {
    color: #000;
  }
}
.popover_ul {
  font-size: 16px;
  display: flex;
  color: #fff;
  li {
    flex: 1;
    flex-shrink: 0;
    // &:last-child span{
    //   color:#d63031;
    // }
  }
}
.popover_i {
  font-style: normal;
  color: #d63031;
  font-size: 18px;
  margin-right: 5px;
}
</style>
