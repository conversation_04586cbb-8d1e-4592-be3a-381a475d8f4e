<template>
  <div class="unlock">
    <div class="headrCont">
      <!-- <el-button type="primary" plain size="mini" class="sure">确定</el-button> -->
      <!-- <span class="cell_red titleInfo">双击解锁按钮可依次进行解锁</span> -->
    </div>

    <div class="tableCont">
      <PublicTable
        :viewTableList.sync="tableData"
        :theads.sync="theads"
        :tableLoading.sync="loading"
        :isSortShow="false"
        :columnWidth="columnWidth"
        :cell_red="[
          'regNo',
          'questionerName',
          'questionTime',
          'replyContent',
          'replyTime',
          'combCode',
          'combName',
          'replyerName',
          'unlockTime',
          'questionContent'
        ]"
      >
        <template #columnRight>
          <el-table-column prop="" label="操作" width="120" fixed="left">
            <template slot-scope="scope">
              <el-button
                type="primary"
                plain
                size="mini"
                class="cell_red"
                @click="dbUnlock(scope.row)"
                >解锁</el-button
              >
            </template>
          </el-table-column>
        </template>
      </PublicTable>
    </div>
  </div>
</template>

<script>
import PublicTable from '../../components/publicTable.vue';
import MainBtnCom from './mainBtnCom.vue';
import { mapGetters } from 'vuex';
import backout from './mixins/backout';
export default {
  name: 'unlock',
  mixins: [backout],
  components: { PublicTable, MainBtnCom },
  props: {
    tableData: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      loading: false,
      // tableData: [], //表单数据
      theads: {
        regNo: '体检号',
        combCode: '组合代码',
        combName: '组合名称',
        questionTime: '咨询时间',
        questionerName: '咨询医生',
        questionContent: '咨询内容',
        replyTime: '回复时间',
        replyerName: '回复医生',
        replyContent: '回复内容',
        unlockTime: '解锁时间'
      },
      columnWidth: {
        regNo: 130,
        combName: 140,
        questionContent: 240,
        replyContent: 240,
        questionTime: 180,
        replyTime: 180,
        replyerName: 100,
        questionerName: 100
      },
      timerName: '' //定时器,
    };
  },
  mounted() {},
  computed: {
    ...mapGetters(['G_EnumList', 'G_userInfo'])
  },
  methods: {}
};
</script>

<style lang="less" scoped>
.unlock {
  display: flex;
  flex-direction: column;
  height: 100%;
  .headrCont {
    // border-radius: 4px;
    // padding: 15px;
    // height: 50px;
    margin-bottom: 20px;
  }
  .titleInfo {
    font-weight: 600;
    // margin-left: 50px;
    font-size: 18px;
  }
  .tableCont {
    flex: 1;
    overflow: auto;
    border: 1px solid #dde2e5;
    border-radius: 4px;
  }
}
</style>
