<template>
  <div class="btn_group">
    <slot name="preAdd"></slot>
    <el-button
      v-if="btnList.includes('表格数据')"
      @click="mainCheckList"
      class="white_btn btn"
      size="small"
      icon="iconfont icon-liebiao"
      >主检列表</el-button
    >
    <el-button
      v-if="btnList.includes('审核列表')"
      @click="examineList"
      class="white_btn btn"
      size="small"
      icon="iconfont icon-liebiao"
      >审核列表</el-button
    >
    <el-button
      v-if="btnList.includes('查询')"
      @click="search"
      class="blue_btn btn"
      size="small"
      icon="iconfont icon-search"
      >查询</el-button
    >
    <el-button
      v-if="btnList.includes('审核')"
      @click="examineClick"
      class="yellow_btn btn"
      size="small"
      icon="iconfont icon-shenhe1"
      :loading="auditLoading"
      >{{ isExamine === 2 ? '取消审核' : '审核' }}</el-button
    >
    <el-button
      v-if="btnList.includes('保存')"
      @click="saveClick"
      class="blue_btn btn"
      size="small"
      icon="iconfont icon-baocun"
      >保存</el-button
    >
    <el-button
      v-if="btnList.includes('暂存')"
      @click="temporarySave"
      class="blue_btn btn"
      size="small"
      icon="iconfont icon-baocun"
      >暂存</el-button
    >
    <el-button
      size="small"
      class="green_btn btn"
      v-if="btnList.includes('预览报告')"
      @click="previewReport"
      icon="iconfont icon-preview"
      >预览报告</el-button
    >
    <el-button
      v-if="btnList.includes('优先')"
      @click="precedence"
      class="violet_btn btn"
      size="small"
      icon="iconfont icon-VIP"
      >优先</el-button
    >
    <el-button
      v-if="btnList.includes('生成结论')"
      @click="createReport"
      class="blue_btn btn"
      size="small"
      icon="iconfont icon-shengchengbaobiao"
      >生成结论</el-button
    >
    <el-button
      v-if="btnList.includes('咨询列表')"
      @click="consultList"
      class="yellow_btn btn"
      size="small"
      icon="iconfont icon-changyonghuifu"
      >咨询列表</el-button
    >
    <el-button
      v-if="btnList.includes('历史报告')"
      @click="historyReport"
      class="violet_btn btn"
      size="small"
      icon="iconfont icon-a-lishijilubiaodanjishi"
      >历史报告</el-button
    >
    <el-button
      v-if="btnList.includes('主检') && !(isMain !== 2)"
      size="small"
      icon="iconfont icon-shenhe1"
      class="blue_btn btn"
      @click="mainTest"
      >主检</el-button
    >
    <el-button
      v-if="btnList.includes('主检回复')"
      size="small"
      icon="iconfont icon-changyonghuifu"
      class="blue_btn btn"
      @click="mainInspectionReply"
      >主检回复</el-button
    >
    <el-button
      v-if="btnList.includes('取消主检') && !(isMain == 2 || isExamine === 2)"
      size="small"
      icon="iconfont icon-quxiaoshenhe"
      class="red_btn btn"
      @click="cancelMainTest"
      >取消主检</el-button
    >
    <el-button
      v-if="btnList.includes('取消审核') && isExamine === 2"
      @click="cancelExamineClick"
      class="yellow_btn btn"
      size="small"
      icon="iconfont icon-shenhe1"
      >取消审核</el-button
    >
    <el-button
      v-if="btnList.includes('撤回主检')"
      size="small"
      icon="iconfont icon-quxiaoshenhe"
      class="red_btn btn"
      @click="withdrawMainTest"
      >撤回主检</el-button
    >
    <el-button
      size="small"
      class="blue_btn btn"
      v-if="btnList.includes('添加')"
      @click="addFun"
      :disabled="isAabled"
      icon="iconfont icon-xinjian"
      >添加</el-button
    >
    <el-button
      size="small"
      class="green_btn btn"
      v-if="btnList.includes('清空')"
      @click="emptys"
      icon="iconfont icon-qingkonghuancun"
      :disabled="isDisabledClear"
      >清空</el-button
    >
    <el-button
      size="small"
      class="violet_btn btn"
      v-if="btnList.includes('AI纠错')"
      @click="errorCorrection"
      icon="iconfont icon-AIzhineng"
      >AI纠错</el-button
    >
    <el-button
      size="small"
      class="yellow_btn btn"
      v-if="btnList.includes('撤回上一步')"
      @click="revocationBtn"
      :disabled="isRevocation"
      icon="iconfont icon-huifuxitongmoren"
      >撤回上一步</el-button
    >
    <slot name="footAdd"></slot>
  </div>
</template>

<script>
export default {
  name: 'btnGroup',
  props: {
    btnList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    // 是否主检
    isMain: {
      type: Number,
      default: 2
    },
    isRevocation: {
      type: Boolean,
      default: () => {
        return false;
      }
    },
    // 是否审核
    isExamine: {
      type: Number,
      default: () => {
        return 1;
      }
    },
    isAabled: {
      type: Boolean,
      default: () => {
        return false;
      }
    },
    // 是否禁用清空
    isDisabledClear: {
      type: Boolean,
      default: () => {
        return false;
      }
    },
    // 审核加载中状态
    auditLoading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isPreview: false
    };
  },
  methods: {
    //主检列表
    mainCheckList() {
      this.$emit('mainCheckList');
    },
    //审核列表
    examineList() {
      this.$emit('examineList');
    },
    // 查询
    search() {
      this.$emit('search');
    },
    // 审核
    examineClick() {
      this.$emit('examineClick');
    },
    // 取消审核
    cancelExamineClick() {
      this.$emit('cancelExamineClick');
    },
    // 保存
    saveClick() {
      this.$emit('saveClick');
    },
    // 暂存
    temporarySave() {
      this.$emit('temporarySave');
    },
    // 预览报告
    previewReport() {
      this.$emit('previewReport');
    },
    //优先
    precedence() {
      this.$emit('precedence');
    },
    //生成结论
    createReport() {
      this.$emit('createReport');
    },
    // 咨询列表
    consultList() {
      this.$emit('consultList');
    },
    //历史报告
    historyReport() {
      this.$emit('historyReport');
    },
    // 主检
    mainTest() {
      this.$emit('mainTest');
    },
    // 主检回复
    mainInspectionReply() {
      this.$emit('mainInspectionReply');
    },
    //取消主检
    cancelMainTest() {
      this.$emit('cancelMainTest');
    },
    //撤回主检
    withdrawMainTest() {
      this.$emit('withdrawMainTest');
    },
    //添加
    addFun() {
      this.$emit('addFun');
    },
    //清空
    emptys() {
      this.$emit('emptys');
    },
    //AI纠错
    errorCorrection() {
      this.$emit('errorCorrection');
    },
    // 撤销修改
    revocationBtn() {
      this.$emit('revocationBtn');
    }
  }
};
</script>
<style lang="less" scoped>
.btn_group {
  // height: 48px;
  display: flex;
  align-items: center;
  .btn {
    padding: 6.5px 10px;
  }
}
</style>
<style lang="less"></style>
