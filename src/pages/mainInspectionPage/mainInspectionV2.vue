<!--
 * @FilePath: \shenshan\KrPeis\src\pages\mainInspectionPage\mainInspectionV2.vue
 * @Description  :  主检不分配页面v2
 * <AUTHOR> justin
 * @Date         : 2024-07-16 17:04:31
 * @Version      : 0.0.1
 * @LastEditors: key
 * @LastEditTime: 2025-04-15 11:18:39
*
-->

<template>
  <div
    class="mainInspection mainInspection_media"
    @mouseover="anchorPointEnter(false)"
  >
    <div class="header-wrapper">
      <!-- 锚点显示的按钮 -->
      <div
        class="anchorPoint_btn"
        @mousemove="anchorPointEnter(true)"
        @mouseover.stop
      ></div>
      <div class="topBtn">
        <div class="leftTop">
          <el-popover
            placement="bottom-start"
            width="100%"
            trigger="click"
            ref="popoverRef"
            popper-class="popverClass"
            @show="getPatientList"
          >
            <el-tabs v-model="activeMain3" @tab-click="mainClick3">
              <el-tab-pane label="待主检" name="main1">
                <NoDistribution
                  :viewTableList="viewTableList1"
                  :queryTimeType.sync="noDistributionQueryTimeType1"
                  :loading="noDistributionLoading1"
                  @getReportConclution="getReportConclution"
                  @filterCls="filterCls"
                  @dateChange="getPatientList1"
                  @queryTimeTypeChange="getPatientList1"
                  v-model="dateVal1"
                  :occupationalParameters="true"
                  :harmList="harmList"
                  :jobStatus.sync="jobStatus1"
                  :hazardousCodes.sync="hazardousCodes1"
                ></NoDistribution>
              </el-tab-pane>
              <el-tab-pane label="已主检" name="main2">
                <NoDistribution
                  :viewTableList="viewTableList2"
                  :queryTimeType.sync="noDistributionQueryTimeType2"
                  :loading="noDistributionLoading2"
                  @getReportConclution="getReportConclution"
                  @filterCls="filterCls"
                  @dateChange="getPatientList2"
                  @queryTimeTypeChange="getPatientList2"
                  isFilterRadioShow
                  v-model="dateVal2"
                  :occupationalParameters="true"
                  :harmList="harmList"
                  :jobStatus.sync="jobStatus2"
                  :hazardousCodes.sync="hazardousCodes2"
                ></NoDistribution>
              </el-tab-pane>
              <el-tab-pane label="已审核" name="main3">
                <NoDistribution
                  :viewTableList="viewTableList3"
                  :queryTimeType.sync="noDistributionQueryTimeType3"
                  :loading="noDistributionLoading3"
                  @getReportConclution="getReportConclution"
                  @filterCls="filterCls"
                  @dateChange="getPatientList3"
                  @queryTimeTypeChange="getPatientList3"
                  isFilterRadioShow
                  v-model="dateVal3"
                  :occupationalParameters="true"
                  :harmList="harmList"
                  :jobStatus.sync="jobStatus3"
                  :hazardousCodes.sync="hazardousCodes3"
                ></NoDistribution>
              </el-tab-pane>
            </el-tabs>
            <MainBtnCom
              :btnList="['表格数据']"
              @mainCheckList="mainCheckList"
              slot="reference"
            />
          </el-popover>
          <el-popover
            placement="bottom-start"
            trigger="click"
            ref="popoverRef2"
            class="projectResult_btn"
            popper-class="popverClass projectResult_popover"
          >
            <div class="con_wrap">
              <div class="projectResult_header">
                项目结果
                <!-- <span>
                    <span> -->
                <p>
                  <el-popover
                    placement="right-start"
                    title="快速定位"
                    trigger="click"
                    popper-class="anchor_popover"
                  >
                    <!-- 锚点列表 -->
                    <div class="projectNav_wrap">
                      <a
                        v-for="item in projectResult"
                        :key="item.combCode"
                        @click="anchorPointClick('#nav_' + item.combCode)"
                        >{{ item.combName }}</a
                      >
                    </div>
                    <el-button
                      size="mini"
                      class="green_btn btn"
                      slot="reference"
                      >定位</el-button
                    >
                  </el-popover>
                  <el-button
                    size="mini"
                    class="green_btn btn"
                    v-if="ImgTextList.length != 0"
                    @click="imgTextClick"
                    >图文报告</el-button
                  >
                </p>

                <!-- </span> -->
                <!-- <i class="el-icon-s-unfold"></i> -->
                <!-- </span> -->
              </div>
              <div class="bottom_wrap">
                <el-collapse
                  v-model="activeNames"
                  @change="handleChange"
                  size="small"
                >
                  <el-collapse-item
                    :title="item.combName"
                    :name="item.combCode"
                    v-for="item in projectResult"
                    :key="item.combCode"
                    :id="'nav_' + item.combCode"
                  >
                    <template slot="title">
                      <div class="collapse_div">
                        <span>
                          {{ item.combName }}
                          <img
                            title="引用项目"
                            src="@/assets/img/yinyong.png"
                            v-if="item.isQuote"
                            alt=""
                          />
                        </span>
                        <div style="display: flex; align-items: center">
                          <span class="operNameClass"
                            >体检医生:{{
                              item.doctorName ? item.doctorName : '无'
                            }}</span
                          >
                          <i
                            class="el-icon-s-comment icon-green"
                            v-if="!item.isQuote"
                            @click.stop="consultingClick(item)"
                          ></i>
                        </div>
                      </div>
                    </template>
                    <!-- 项目列表 -->
                    <el-table
                      size="small"
                      :data="item.projects"
                      style="width: 100%; font-size: 14px"
                      :header-cell-style="headerCellStyle"
                    >
                      <el-table-column
                        prop="itemName"
                        label="项目名称"
                        width="180"
                      >
                        <template #default="scope">
                          <div
                            :class="{ redClass: scope.row.abnormalType > 0 }"
                          >
                            {{ scope.row.itemName }}
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column prop="address" label="结果">
                        <template #default="scope">
                          <div class="result_div">
                            <el-popover
                              placement="right"
                              popper-class="my_popover"
                              width="500"
                              trigger="hover"
                              @hide="onPopoverHide"
                            >
                              <ul class="popover_ul">
                                <li>
                                  <!-- <label for="">参考值：</label> -->
                                  <span>{{ C_result(scope.row.results) }}</span>
                                </li>
                                <!-- <li>
                                    <label for="">异常程度：</label>
                                    <span>{{scope.row.hint}}</span>
                                  </li> -->
                              </ul>
                              <div
                                slot="reference"
                                class="popover_div"
                                :class="
                                  scope.row.isError
                                    ? 'redClass'
                                    : 'defaultClass'
                                "
                              >
                                <i class="popover_i">{{ scope.row.hint }}</i
                                >{{ C_result(scope.row.results) }}
                              </div>
                            </el-popover>
                            <!-- <span class="result_span" v-for="tag in scope.row.results" :key="tag.index">{{
                                tag.itemTag
                              }}</span> -->
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column
                        label="参考值"
                        v-if="item.isShowReferenceRange"
                      >
                        <template #default="scope">
                          <span
                            :class="{ redClass: scope.row.abnormalType > 0 }"
                            >{{ disposeFKVal(scope.row) }}</span
                          >
                        </template>
                      </el-table-column>
                      <el-table-column
                        prop="unit"
                        label="单位"
                        v-if="item.isShowUnits"
                      >
                        <template #default="scope">
                          <span
                            :class="{ redClass: scope.row.abnormalType > 0 }"
                            >{{ scope.row.unit }}</span
                          >
                        </template>
                      </el-table-column>
                      <el-table-column prop="address" label="上次结果">
                        <template #default="scope">
                          <div class="result_div">
                            <el-popover
                              placement="right"
                              popper-class="my_popover"
                              width="500"
                              trigger="hover"
                              @hide="onPopoverHide"
                            >
                              <ul class="popover_ul">
                                <li>
                                  <span>{{ scope.row.lastResult }}</span>
                                </li>
                              </ul>
                              <div
                                slot="reference"
                                class="popover_div"
                                :class="
                                  scope.row.isError
                                    ? 'redClass'
                                    : 'defaultClass'
                                "
                              >
                                {{ scope.row.lastResult }}
                              </div>
                            </el-popover>
                          </div>
                        </template>
                      </el-table-column>
                    </el-table>
                    <!-- 体检小结 -->
                    <div class="project_result">
                      <span>体检小结：</span>
                      <div>
                        <el-tag
                          size="small"
                          :class="
                            result.abnormalType > 0 ? 'tags_red' : 'tags_sty'
                          "
                          v-for="result in item.combinations"
                          :key="result.index"
                          >{{ result.combTag }}</el-tag
                        >
                      </div>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
            <el-button
              style="padding: 6.5px 10px; font-size: 14px"
              slot="reference"
              size="mini"
              >项目结果</el-button
            >
          </el-popover>
          <p>
            <el-input
              size="small"
              placeholder="体检号/姓名"
              v-model="keyword"
              clearable
              @clear="clearSearch"
              @keyup.enter.native="searchByKeyword"
            ></el-input>
          </p>
        </div>
        <MainBtnCom
          :btnList="[
            '查询',
            '生成结论',
            '咨询列表',
            '历史报告',
            '主检',
            '取消主检',
            '暂存',
            '取消审核'
          ]"
          @mainCheckList="mainCheckList"
          @search="search"
          @precedence="precedence"
          @consultList="consultList"
          @mainTest="mainTest"
          @cancelMainTest="cancelMainTest"
          @createReport="createReport"
          @historyReport="historyReport"
          @temporarySave="temporarySave"
          @cancelExamineClick="cancelExamineClick"
          :isMain="isMain"
          :isExamine="isExamine"
        >
          <template #preAdd>
            <el-button
              size="small"
              class="green_btn btn"
              v-if="isMain >= 3"
              @click="reviewBtnClick"
              >复查</el-button
            >
          </template>
          <template #footAdd>
            <el-button
              class="yellow_btn btn"
              size="small"
              icon="el-icon-headset"
              v-if="PureToneAudiometryBtnShow"
              @click="showPureToneAudiometry = true"
              >纯音测听</el-button
            >
            <el-button
              class="green_btn btn"
              size="small"
              icon="el-icon-tickets"
              :class="{ 'questionnaire-disabled': questionnaireDisabled }"
              @click="questionnaire"
              >问卷调查</el-button
            >
            <el-button size="small" class="green_btn btn" @click="followUpClick"
              >添加随访</el-button
            >
            <el-button
              size="small"
              class="green_btn btn"
              @click="followUpRecodeClick"
              v-if="isHasFollowUp"
              >随访记录</el-button
            >
            <div
              class="positive_btn"
              :class="{ is_positive: isPositiveFlag }"
              @click="PositiveResultShow"
            >
              <!-- <i class="el-icon-d-arrow-left"></i> -->
              重阳
            </div>
          </template>
        </MainBtnCom>
      </div>
      <div class="topTitle">
        <!-- <el-collapse class="collapse" v-model="activeNamesOfPatient">
          <el-collapse-item name="patient">
            <template slot="title">
              <ul class="type_head">
                <li>
                  <div class="every_inp regNo_inp">
                    <label>体检号: </label>
                    <p>{{ reportConclution.patient.regNo }}</p>
                  </div>
                  <div class="every_inp less_inp">
                    <label>姓名: </label>
                    <p
                      :title="reportConclution.patient.name"
                      class="text-overflow-hidden"
                    >
                      {{ reportConclution.patient.name }}
                    </p>
                  </div>
                  <div class="every_inp less_inp">
                    <label>性别: </label>
                    <p>{{ G_EnumList["Sex"][reportConclution.patient.sex] }}</p>
                  </div>
                  <div class="every_inp less_inp">
                    <label>年龄: </label>
                    <p>
                      {{
                        (reportConclution.patient.age || "") +
                        (G_EnumList["AgeUnit"][
                          reportConclution.patient.ageUnit
                        ] || "")
                      }}
                    </p>
                  </div>
                  <div class="every_inp" style="width: 15%">
                    <label>单位: </label>
                    <p
                      :title="reportConclution.patient.companyName"
                      class="text-overflow-hidden"
                    >
                      {{ reportConclution.patient.companyName }}
                    </p>
                  </div>
                  <div class="every_inp" style="width: 15%">
                    <label>套餐: </label>
                    <p
                      :title="reportConclution.patient.clusterName"
                      class="text-overflow-hidden"
                    >
                      {{ reportConclution.patient.clusterName }}
                    </p>
                  </div>

                  <div class="every_inp" style="width: 20%">
                    <label>体检类型: </label>
                    <p class="text-overflow-hidden">
                      {{ G_EnumList["PeCls"][reportConclution.patient.peCls] }}
                    </p>
                  </div>
                </li>
              </ul>
            </template>

            <div :style="{ height: G_config.physicalMode.includes('职检') ? '4rem' : '100%' }">
              <el-image
                v-if="
                  G_config.physicalMode.includes('职检') &&
                  activeNamesOfPatient.includes('patient')
                "
                class="patient-photo"
                :src="reportConclution.patient.photoUrl"
                fit="contain"
              ></el-image>
              <ul class="type_head">
                <li>
                  <div class="every_inp regNo_inp">
                    <label>联系电话: </label>
                    <p>{{ reportConclution.patient.tel }}</p>
                  </div>
                  <div class="every_inp num_inp">
                    <label>身份证: </label>
                    <p>{{ reportConclution.patient.cardNo }}</p>
                  </div>
                  <div class="every_inp less_inp">
                    <label>婚姻状况: </label>
                    <p>
                      {{
                        G_EnumList["MarryStatus"][
                          reportConclution.patient.marryStatus
                        ]
                      }}
                    </p>
                  </div>
                  <div class="every_inp" style="width: 15%">
                    <label>联系地址: </label>
                    <p
                      :title="reportConclution.patient.address"
                      class="text-overflow-hidden"
                    >
                      {{ reportConclution.patient.address }}
                    </p>
                  </div>

                  <div v-if="G_config.physicalMode.includes('职检')" class="every_inp">
                    <label>岗位状态: </label>
                    <p>
                      {{
                        G_EnumList["CodeOccupationalPositionStatus"][
                          reportConclution.patient.jobstatus
                        ]
                      }}
                    </p>
                  </div>
                </li>
              </ul>
            </div>
          </el-collapse-item>
        </el-collapse> -->
        <el-collapse class="collapse">
          <el-collapse-item>
            <template slot="title">
              <ul class="type_head">
                <li>
                  <div class="every_inp regNo_inp">
                    <label>体检号: </label>
                    <p>{{ reportConclution.patient.regNo }}</p>
                  </div>
                  <div class="every_inp less_inp">
                    <label>姓名: </label>
                    <p
                      class="text-overflow-hidden"
                      :title="reportConclution.patient.name"
                    >
                      {{ reportConclution.patient.name }}
                    </p>
                  </div>
                  <div class="every_inp less_inp">
                    <label>性别: </label>
                    <p>{{ G_EnumList['Sex'][reportConclution.patient.sex] }}</p>
                  </div>
                  <div class="every_inp less_inp">
                    <label>年龄: </label>
                    <p>
                      {{
                        reportConclution.patient.ageUnit
                          ? reportConclution.patient.age +
                            G_EnumList['AgeUnit'][
                              reportConclution.patient.ageUnit
                            ]
                          : reportConclution.patient.age
                      }}
                    </p>
                  </div>
                  <div class="every_inp flex_inp">
                    <label>单位: </label>
                    <p
                      :title="reportConclution.patient.companyName"
                      style="overflow-y: auto"
                      class="text-overflow-hidden"
                    >
                      {{ reportConclution.patient.companyName }}
                    </p>
                  </div>
                  <div class="every_inp flex_inp">
                    <label>工龄: </label>
                    <p
                      :title="reportConclution.patient.totalYearsOfWork"
                      style="overflow-y: auto"
                      class="text-overflow-hidden"
                    >
                      {{ reportConclution.patient.totalYearsOfWork }}
                    </p>
                  </div>
                  <div class="every_inp flex_inp">
                    <label>在岗状态: </label>
                    <p
                      :title="reportConclution.patient.jobstatusName"
                      style="overflow-y: auto"
                      class="text-overflow-hidden"
                    >
                      {{ reportConclution.patient.jobstatusName }}
                    </p>
                  </div>
                  <div class="every_inp cont_inp">
                    <label>危害因素: </label>
                    <p
                      :title="reportConclution.patient.hazardousFators"
                      style="
                        width: 100%;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                      "
                    >
                      {{ reportConclution.patient.hazardousFators }}
                    </p>
                  </div>
                </li>
              </ul>
            </template>
            <div>
              <ul class="type_head">
                <li>
                  <div class="every_inp regNo_inp">
                    <label>联系电话: </label>
                    <p>{{ reportConclution.patient.tel }}</p>
                  </div>
                  <div class="every_inp num_inp">
                    <label>身份证: </label>
                    <p>{{ reportConclution.patient.cardNo }}</p>
                  </div>
                  <div class="every_inp less_inp">
                    <label>婚姻状况: </label>
                    <p>
                      {{
                        G_EnumList['MarryStatus'][
                          reportConclution.patient.marryStatus
                        ]
                      }}
                    </p>
                  </div>
                  <div class="every_inp cont_inp">
                    <label>联系地址: </label>
                    <p
                      :title="reportConclution.patient.address"
                      style="
                        width: 100%;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                      "
                    >
                      {{ reportConclution.patient.address }}
                    </p>
                  </div>
                  <div class="every_inp flex_inp">
                    <label>体检类型: </label>
                    <p>
                      {{ G_EnumList['PeCls'][reportConclution.patient.peCls] }}
                    </p>
                  </div>
                </li>
              </ul>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>

    <div class="centerCont">
      <div
        class="con_wrap left_wrap"
        ref="project_Ref"
        :class="{ left_wrap_fold: foldFlag }"
      >
        <header>
          <p v-if="!foldFlag">项目结果：</p>
          <div class="head_operation">
            <el-button
              v-if="!foldFlag && ImgTextList.length != 0"
              size="mini"
              class="green_btn btn"
              @click="imgTextClick"
              >图文报告</el-button
            >
            <el-dropdown placement="bottom" v-if="!foldFlag">
              <span class="el-dropdown-link">
                <i class="el-icon-tickets"></i> 目录
              </span>
              <el-dropdown-menu slot="dropdown" class="catalogue_dropdown">
                <el-dropdown-item
                  v-for="item in projectResult"
                  :key="item.combCode"
                  @click.native="anchorPointClick('#nav_' + item.combCode)"
                >
                  {{ item.combName }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <span @click="foldClick">
              <i :class="foldFlag ? 'el-icon-s-unfold' : 'el-icon-s-fold'"></i>
              收起
            </span>
          </div>
        </header>
        <!-- <p>
          项目结果
          <el-button
            size="mini"
            class="green_btn btn"
            @click="imgTextClick"
            v-if="ImgTextList.length != 0"
            >图文报告</el-button
          >
        </p> -->
        <!-- 收起的显示 -->
        <div class="fold_div" v-if="foldFlag">
          <div>项目结果</div>
        </div>
        <div class="bottom_wrap" v-if="!foldFlag">
          <!-- 锚点列表 -->
          <!-- <div
            class="projectNav_wrap"
            :class="anchorPointShow ? 'projectNav_wrap_active' : ''"
            @mouseover.stop
          >
            <a
              v-for="item in projectResult"
              :key="item.combCode"
              @click="anchorPointClick('#nav_' + item.combCode)"
              >{{ item.combName }}</a
            >
          </div> -->
          <el-collapse
            v-model="activeNames"
            @change="handleChange"
            size="small"
          >
            <el-collapse-item
              :title="item.combName"
              :name="item.combCode"
              v-for="item in projectResult"
              :key="item.combCode"
              :id="'nav_' + item.combCode"
            >
              <template slot="title">
                <div class="collapse_div">
                  <span>
                    {{ item.combName }}
                    <img
                      title="引用项目"
                      src="@/assets/img/yinyong.png"
                      v-if="item.isQuote"
                      alt=""
                    />
                  </span>
                  <div style="display: flex; align-items: center">
                    <span class="operNameClass"
                      >体检医生:{{
                        item.doctorName ? item.doctorName : '无'
                      }}</span
                    >
                    <i
                      class="el-icon-s-comment icon-green"
                      @click.stop="consultingClick(item)"
                      v-if="!item.isQuote"
                    ></i>
                  </div>
                </div>
              </template>
              <!-- 项目列表 -->
              <el-table
                size="small"
                :data="item.projects"
                style="width: 100%; font-size: 14px"
                :header-cell-style="headerCellStyle"
              >
                <el-table-column prop="itemName" label="项目名称" width="180">
                </el-table-column>
                <el-table-column prop="address" label="结果">
                  <template #default="scope">
                    <div class="result_div">
                      <el-popover
                        placement="right"
                        popper-class="my_popover"
                        width="500"
                        trigger="hover"
                        @hide="onPopoverHide"
                      >
                        <ul class="popover_ul">
                          <li>
                            <!-- <label for="">参考值：</label> -->
                            <span>{{ C_result(scope.row.results) }}</span>
                          </li>
                          <!-- <li>
                            <label for="">异常程度：</label>
                            <span>{{scope.row.hint}}</span>
                          </li> -->
                        </ul>
                        <div
                          slot="reference"
                          class="popover_div"
                          :class="
                            scope.row.isError ? 'redClass' : 'defaultClass'
                          "
                        >
                          <i class="popover_i">{{ scope.row.hint }}</i
                          >{{ C_result(scope.row.results) }}
                        </div>
                      </el-popover>
                      <!-- <span class="result_span" v-for="tag in scope.row.results" :key="tag.index">{{
                        tag.itemTag
                      }}</span> -->
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  label="参考值"
                  v-if="item.isShowReferenceRange"
                >
                  <template #default="scope">
                    <span :class="{ redClass: scope.row.abnormalType > 0 }">{{
                      disposeFKVal(scope.row)
                    }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="unit"
                  label="单位"
                  v-if="item.isShowUnits"
                >
                  <template #default="scope">
                    <span :class="{ redClass: scope.row.abnormalType > 0 }">{{
                      scope.row.unit
                    }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="address" label="上次结果">
                  <template #default="scope">
                    <div class="result_div">
                      <el-popover
                        placement="right"
                        popper-class="my_popover"
                        width="500"
                        trigger="hover"
                        @hide="onPopoverHide"
                      >
                        <ul class="popover_ul">
                          <li>
                            <span>{{ scope.row.lastResult }}</span>
                          </li>
                        </ul>
                        <div
                          slot="reference"
                          class="popover_div"
                          :class="
                            scope.row.isError ? 'redClass' : 'defaultClass'
                          "
                        >
                          {{ scope.row.lastResult }}
                        </div>
                      </el-popover>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <!-- 体检小结 -->
              <div class="project_result">
                <span>体检小结：</span>
                <div>
                  <el-tag
                    size="small"
                    :class="result.abnormalType > 0 ? 'tags_red' : 'tags_sty'"
                    v-for="result in item.combinations"
                    :key="result.index"
                    >{{ result.combTag }}</el-tag
                  >
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
      <div class="right_wrap">
        <!-- 综述和建议 -->
        <Summary
          ref="summaryCom"
          :isSummary="!isSummary"
          :patientInfo.sync="reportConclution.patient"
          mainFlag
          :isMain="isMain"
          :isExamine="isExamine"
          :recordCombItems="projectResult"
          v-model="harmSummary"
          :reportConclusionHazards.sync="
            reportConclution.reportConclusionHazards
          "
          :reportConclution="reportConclution"
          @reviewShow="reviewBtnShow"
        />
        <!-- 结论 -->
        <div class="conclusion_wrap" v-if="false">
          <div class="footerCont">
            <div class="footer1">
              <label>本次体检结论</label>
              <p>
                <el-input
                  size="small"
                  :disabled="isMain != 2"
                  v-model="reportConclution.conclusion"
                  placeholder="请输入内容"
                ></el-input>
              </p>
            </div>
            <div class="footer2">
              <div class="foot_inp">
                <label style="width: 40px">总结</label>
                <p>
                  <el-input
                    size="small"
                    clearable
                    readonly
                    v-model="harmSummary"
                  ></el-input>
                </p>
              </div>
              <div class="foot_inp">
                <label>主检医师</label>
                <p>
                  <el-input
                    size="small"
                    clearable
                    readonly
                    v-model="reportConclution.checkDoctorName"
                  ></el-input>
                </p>
              </div>
              <div class="foot_inp">
                <label>主检日期</label>
                <p>
                  <el-date-picker
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    :clearable="false"
                    size="small"
                    readonly
                    v-model="reportConclution.checkTime"
                  >
                  </el-date-picker>
                </p>
              </div>
              <div class="foot_inp">
                <label>审核医师</label>
                <p>
                  <el-input
                    size="small"
                    clearable
                    readonly
                    v-model="reportConclution.auditDoctorName"
                  ></el-input>
                </p>
              </div>
              <div class="foot_inp">
                <label>审核日期</label>
                <p>
                  <el-date-picker
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    :clearable="false"
                    size="small"
                    readonly
                    v-model="reportConclution.auditTime"
                  >
                  </el-date-picker>
                </p>
              </div>
              <!-- <div class="foot_inp">
                <label style="text-align: right">请于</label>
                <p>
                  <el-input size="mini" clearable readonly></el-input>
                </p>
                <label>天后复查</label>
              </div> -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <el-drawer
      title="查询"
      :visible.sync="searchDialog"
      :before-close="searchDialogClose"
      :wrapperClosable="false"
      size="90%"
      v-if="searchDialog"
    >
      <MainSearch @getReportConclution="getReportConclution" />
    </el-drawer>
    <el-drawer
      title="医生咨询回复一览表"
      :visible.sync="consultDialog"
      :before-close="consultDialogClose"
      :wrapperClosable="false"
      size="90%"
      v-if="consultDialog"
    >
      <Consult :conclusionList="conclusionList" />
    </el-drawer>
    <el-drawer
      title="历史报告"
      :visible.sync="hisDrawer"
      :before-close="hisDrawerClose"
      :wrapperClosable="false"
      size="90%"
      v-if="hisDrawer"
    >
      <HistoryReport :regNo="reportConclution.patient.regNo" />
    </el-drawer>

    <!-- 撤销主检的弹窗 -->
    <el-dialog
      title="撤销主检回复"
      :visible.sync="backoutShow"
      :close-on-click-modal="false"
      width="80%"
    >
      <div style="height: 500px; overflow: auto; margin-top: 20px">
        <PublicTable
          :theads="backoutThead"
          :viewTableList="backoutList"
          :columnWidth="backoutWidth"
        >
          <template #columnRight>
            <el-table-column width="60" label="操作">
              <template #default="props">
                <el-popover
                  placement="bottom-end"
                  width="400"
                  content="dsada"
                  v-model="props.row.backoutPopoverShow"
                  trigger="click"
                >
                  <div>
                    <el-input
                      type="textarea"
                      placeholder="请输入内容"
                      :autosize="{ minRows: 6, maxRows: 6 }"
                      v-model.trim="props.row.replyContent"
                    >
                    </el-input>
                    <p style="text-align: right; padding-top: 10px">
                      <el-button size="small">取消</el-button>
                      <el-button
                        size="small"
                        type="primary"
                        @click="reply(props.row)"
                        >确定</el-button
                      >
                    </p>
                  </div>
                  <el-button type="text" slot="reference">回复</el-button>
                </el-popover>
              </template>
            </el-table-column>
          </template>
        </PublicTable>
      </div>
    </el-dialog>
    <el-dialog
      title="医生解锁列表"
      :visible.sync="unlockShow"
      :close-on-click-modal="false"
      width="80%"
    >
      <div style="height: 500px; overflow: auto">
        <Unlock ref="unlock" :tableData="tableData"></Unlock>
      </div>
    </el-dialog>
    <!-- 重大阳性按钮 -->
    <!-- <div
      class="positive_btn"
      :class="{ is_positive: isPositiveFlag }"
      @click="PositiveResultShow"
    >
      <i class="el-icon-d-arrow-left"></i>
      重阳
    </div> -->
    <!-- 重大阳性抽屉 -->
    <el-drawer
      title="重大阳性结果"
      :visible.sync="positiveResultDialog"
      :wrapperClosable="false"
      size="90%"
    >
      <PositiveResult
        :patient.sync="reportConclution"
        :P_projectResult="projectResult"
        v-if="positiveResultDialog"
      />
    </el-drawer>
    <!-- 咨询弹窗 -->
    <el-dialog
      :visible.sync="consultingShow"
      width="800px"
      top="12%"
      custom-class="consultingShow"
      :close-on-click-modal="false"
      @closed="cancel"
    >
      <div slot="title" class="dialog-title">咨询录入窗口</div>
      <el-input
        type="textarea"
        :rows="20"
        placeholder="请输入咨询内容"
        v-model="consultingInput"
        style="margin-top: 20px"
      >
      </el-input>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel" size="small">取消</el-button>
        <el-button class="blue_btn" @click="consultingSubmit" size="small"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <!-- 一键审核 -->
    <el-dialog
      :visible.sync="processShow"
      width="500px"
      custom-class="consultingShow"
      :close-on-click-modal="false"
      @open="getOperatorList"
    >
      <div slot="title" class="dialog-title">是否立即审核</div>
      <el-form
        :model="processInfo"
        :rules="processInfoRule"
        ref="process_Ref"
        style="padding-top: 20px"
      >
        <el-form-item
          label="审核医生"
          label-width="80px"
          prop="auditDoctorCode"
        >
          <el-select
            v-model="processInfo.auditDoctorCode"
            placeholder="请选择审核医生"
          >
            <el-option
              :label="item.operatorName"
              :value="JSON.stringify(item)"
              v-for="item in processDocList"
              :key="item.operatorCode"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="processShow = false" size="small">取消</el-button>
        <el-button class="blue_btn" @click="processSubmit" size="small"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-drawer title="图文报告" size="90%" :visible.sync="ImgTextShow">
      <ImgText
        ref="ImgText_Ref"
        :printImgList.sync="ImgTextList"
        :headerInfo="reportConclution.patient"
        :checkComb.sync="checkComb"
        v-if="ImgTextShow"
      />
    </el-drawer>
    <!-- 添加随访 -->
    <FollowUp
      :regNo="reportConclution.patient.regNo"
      :followUpShow.sync="followUpShow"
      @followUpClose="followUpClose"
    />

    <!-- 体检号/姓名搜索列表内容 -->
    <choicePeReisterDialog
      :keyword="keyword"
      :peStatuss="[2, 3, 4]"
      :show.sync="showChoiceRegister"
      :useCheckStatusShow="true"
      @confirm="confrimChoiceRegDataHandle"
      @cancel="cancelChoiceRegDataHandle"
      @show-event="showChoiceRegisterHandle"
      ref="choicePeReisterDialog"
    />
    <!-- 复查 -->
    <el-dialog
      title="复查"
      :visible.sync="reviewDialogShow"
      :close-on-click-modal="false"
      width="50%"
    >
      <div class="review_wrap">
        <div class="review_head">
          <span>复查号：{{ recheckOrderNo }}</span>
          <p>
            <el-button
              size="small"
              @click="reviewSave"
              :disabled="!!recheckOrderNo"
              >提交复查</el-button
            >
            <el-button
              size="small"
              :disabled="!recheckOrderNo"
              @click="cancelReview"
              >取消复查</el-button
            >
          </p>
        </div>
        <div class="review_content">
          <PublicTable
            ref="reviewTable_Ref"
            :theads="reviewThead"
            :columnWidth="{ combCode: 100 }"
            :viewTableList="reviewList"
            v-model="checkReviewList"
            @selectionChange="reviewSelectionChange"
            isCheck
          ></PublicTable>
        </div>
      </div>
    </el-dialog>
    <el-dialog
      title="问卷调查表"
      :visible.sync="isShowdialogTable"
      class="questionnaire_dig"
    >
      <Questionnaire
        :formInfo="formInfo"
        @submitSuccess="submitSuccess"
        :questionnaireDisabled="questionnaireDisabled"
      ></Questionnaire>
    </el-dialog>
    <!-- 纯音测听组件 -->
    <el-dialog
      title="纯音测听"
      width="80%"
      top="0px"
      :visible.sync="showPureToneAudiometry"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="pure-tone-audiometry-dialog"
      :key="'pureToneAudiometryDialog_' + reportConclution.patient.regNo"
    >
      <PureToneAudiometry
        ref="pureToneAudiometry_Ref"
        :isReadonly="true"
        :patientInfo="reportConclution.patient"
      />
    </el-dialog>
  </div>
</template>
<script>
import PublicTable from '../../components/publicTable.vue';
import Consult from './consult.vue';
import MainBtnCom from './mainBtnCom.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
import MainSearch from './mainSearch.vue';
import SummaryCom from './summaryCom.vue';
import Distribution from './distribution.vue';
import NoDistribution from './noDistribution.vue';
import ResultEntry from '../inspectIn/resultEntry.vue';
import HistoryReport from '../inspectIn/components/doctorWorkStation/historyReport.vue';
import backout from './mixins/backout';
import Unlock from './unlock.vue';
import PositiveResult from './positiveResult.vue';
import mainInspectionMiXins from './mixins/mainInspectionV2';
import consult from './mixins/consult';
import ImgText from '../inspectIn/components/resultEntry/imgText.vue';
import imgTextJs from '../inspectIn/mixins/imgText';
import moment from 'moment';
import FollowUp from '@/components/followUp';
import Summary from './component/summaryV2.vue';
import choicePeReisterDialog from './component/choicePeReisterDialog';
import Questionnaire from '@/pages/register/components/questionnaire.vue';
import PureToneAudiometry from '../inspectIn/components/pureToneAudiometry.vue'; // 纯音测听

export default {
  name: 'mainInspectionV2',
  mixins: [backout, mainInspectionMiXins, consult, imgTextJs],
  components: {
    PublicTable,
    MainBtnCom,
    MainSearch,
    Consult,
    SummaryCom,
    Distribution,
    NoDistribution,
    ResultEntry,
    HistoryReport,
    Unlock,
    PositiveResult,
    ImgText,
    FollowUp,
    Summary,
    choicePeReisterDialog,
    Questionnaire,
    PureToneAudiometry
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_userInfo', 'G_peClsList', 'G_config']),
    C_result() {
      return function (result) {
        let txt = '';
        (result || []).map((item, idx) => {
          txt += item.itemTag + (idx + 1 == result.length ? '' : ';');
        });
        return txt;
      };
    }
  },
  data() {
    return {
      harmList: [],
      hazardousCodes1: [],
      jobStatus1: '',
      hazardousCodes2: [],
      jobStatus2: '',
      hazardousCodes3: [],
      jobStatus3: '',
      questionnaireDisabled: true,
      isShowdialogTable: false,
      formInfo: {
        regNo: '', //体检号
        name: '', //姓名
        sex: '', //性别（0通用1男2女）
        age: '', //年龄
        tel: '', //手机号
        familyMedicalHistory: '', //家族史
        pastMedicalHistory: '', //既往病史
        operationStatus: '', //手术状况
        smokingHabit: '', //吸烟习惯
        drinkingHabit: '', //喝酒习惯
        livingHabit: '', //生活习惯
        currentCondition: '', //现在病况
        questionnaireAnswer: '' //问卷答案
      },
      isReview: false,
      reviewDialogShow: false,
      harmSummary: '',
      HisPatInfo: {},
      processShow: false,
      processInfo: {
        auditDoctorCode: ''
      },
      processDocList: [],
      processInfoRule: {
        auditDoctorCode: [
          { required: true, message: '请选择审核医生', trigger: 'change' }
        ]
      },
      // 表头样式
      headerCellStyle: {
        background: 'rgba(23,112,223,.2)',
        fontSize: '14px',
        color: '#2D3436'
      },
      projectList: [],
      isSummary: true,
      tabPosition: 'left',
      mainInspectionFlag: false,
      activeMain3: 'main1',
      searchDialog: false,
      consultDialog: false,
      tjCls: '',
      activeName: 'first',
      viewTableList1: [],
      viewTableList2: [],
      viewTableList3: [],
      viewTableListCopy1: [],
      viewTableListCopy2: [],
      viewTableListCopy3: [],
      dateVal1: [],
      dateVal2: [],
      dateVal3: [],
      isMain: 2, //2表示未检，3表示已主检
      conclusionList: [],
      conclusionLists: [],
      keyword: '', //体检号/姓名,
      reportConclution: {
        patient: {}, //个人信息
        summary: {}, //综述
        suggestions: [], //建议
        isOccupation: true
      },
      checkStatus: 1, //主检的各个状态（1：待主检；2：已主检；3：已审核）
      hisDrawer: false,
      isExamine: 1,
      showChoiceRegister: false, // 是否显示关键字检索列表
      activeNamesOfPatient: [],
      reviewThead: {
        combCode: '组合代码',
        combName: '组合名称'
      },
      reviewList: [],
      recheckOrderNo: '', // 复查号
      checkReviewList: [],
      noDistributionQueryTimeType1: 2,
      noDistributionLoading1: false,
      noDistributionQueryTimeType2: 3,
      noDistributionLoading2: false,
      noDistributionQueryTimeType3: 4,
      noDistributionLoading3: false
    };
  },
  methods: {
    getHarmList() {
      let datas = {
        pageSize: 0,
        pageNumber: 0
      };
      this.$ajax
        .post(this.$apiUrls.ReadCodeOccupationalHazardousByPage, datas)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.harmList = returnData;
        });
    },
    submitSuccess() {
      this.isShowdialogTable = false;
      this.questionnaireDisabled = false;
    },
    //问卷调查
    questionnaire() {
      if (!this.reportConclution.patient.regNo) {
        this.$message({
          message: '请先选择需要问卷调查的体检信息！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.isShowdialogTable = true;
    },
    //通过体检号获取问卷数据
    GetQuestionDataByRegNo(userInfo) {
      this.formInfo = {
        regNo: userInfo.regNo, //体检号
        name: userInfo.name, //姓名
        sex: this.G_EnumList['Sex'][userInfo.sex], //性别（0通用1男2女）
        age: userInfo.age == '0' ? userInfo.age + '月' : userInfo.age + '岁', //年龄
        tel: userInfo.tel //手机号
      };
      this.$ajax
        .post(this.$apiUrls.GetQuestionDataByRegNo, '', {
          query: {
            regNo: userInfo.regNo
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          if (!returnData) {
            this.questionnaireDisabled = true;
            return;
          }
          this.questionnaireDisabled = false;
          let temp = Object.assign(this.formInfo, returnData);
          this.formInfo = { ...temp };
        });
    },
    handleChange(val) {},
    // 是否显示复查
    reviewBtnShow(flag) {
      this.isReview = flag;
    },
    getPatientList() {
      this.getPatientList1();
      this.getPatientList2();
      this.getPatientList3();
    },
    getPatientList1() {
      let data = {
        operatorCode: this.G_userInfo.codeOper.operatorCode,
        checkStatus: 1,
        beginDate: this.dateVal1[0],
        endDate: this.dateVal1[1],
        isOccupation: true,
        queryTimeType: this.noDistributionQueryTimeType1,
        hazardousCodes: this.hazardousCodes1,
        jobStatus: this.jobStatus1
      };
      this.noDistributionLoading1 = true;
      this.$ajax
        .post(this.$apiUrls.GetPatientList4NotAllocateV2, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.viewTableList1 = returnData;
          this.viewTableListCopy1 = returnData;
        })
        .finally((_) => {
          this.noDistributionLoading1 = false;
        });
    },
    getPatientList2() {
      let data = {
        operatorCode: this.G_userInfo.codeOper.operatorCode,
        checkStatus: 2,
        beginDate: this.dateVal2[0],
        endDate: this.dateVal2[1],
        isOccupation: true,
        queryTimeType: this.noDistributionQueryTimeType2,
        hazardousCodes: this.hazardousCodes2,
        jobStatus: this.jobStatus2
      };
      this.noDistributionLoading2 = true;
      this.$ajax
        .post(this.$apiUrls.GetPatientList4NotAllocateV2, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.viewTableList2 = returnData;
          this.viewTableListCopy2 = returnData;
        })
        .finally((_) => {
          this.noDistributionLoading2 = false;
        });
    },
    getPatientList3() {
      let data = {
        operatorCode: this.G_userInfo.codeOper.operatorCode,
        checkStatus: 3,
        beginDate: this.dateVal3[0],
        endDate: this.dateVal3[1],
        isOccupation: true,
        queryTimeType: this.noDistributionQueryTimeType3,
        hazardousCodes: this.hazardousCodes3,
        jobStatus: this.jobStatus3
      };
      this.noDistributionLoading3 = true;
      this.$ajax
        .post(this.$apiUrls.GetPatientList4NotAllocateV2, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.viewTableList3 = returnData;
          this.viewTableListCopy3 = returnData;
        })
        .finally((_) => {
          this.noDistributionLoading3 = false;
        });
    },
    //主检优先分配
    precedence() {
      if (!this.reportConclution.patient.regNo) {
        this.$message({
          message: '没有体检号无法优先分配!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$ajax
        .post(this.$apiUrls.PriorityAllocation, [], {
          query: {
            operatorCode: this.G_userInfo.codeOper.operatorCode,
            regNo: this.reportConclution.patient.regNo
          }
        })
        .then((r) => {
          let { success } = r.data;
          if (!success) return;
          this.$message({
            message: '主检优先分配成功!',
            type: 'success',
            showClose: true
          });
          this.getPatientList();
        });
    },

    /**
     * @author: justin
     * @description: 关键字查询
     * @return {*}
     */
    async searchByKeyword() {
      const that = this;
      if (dataUtils.isRegNo(that.keyword)) {
        that.searchByRegNo(that.keyword);
      } else {
        // 关键字查询列表
        that.$refs.choicePeReisterDialog.getReportPeRegisters(true);
      }
    },

    /**
     * @author: justin
     * @description: 根据体检号直接获取报告结论
     * @param {*} regNo 体检号
     * @return {*}
     */
    searchByRegNo(regNo) {
      // 直接获取报告结论
      this.getReportConclution(regNo).then((r) => {
        this.keyword = '';
        if (this.activeName == 'third') {
          this.$refs.resultEntry_Ref.headerInfo = r.patient;
          this.$refs.resultEntry_Ref.getNavList();
        }
      });
    },

    //清空操作
    clearSearch() {
      this.reportConclution = {
        patient: {}, //个人信息
        summary: {}, //综述
        suggestions: [] //建议
      };
      this.projectResult = [];
      this.suggestions = [];
      this.$refs.summaryCom.summaryList = [];
      this.ImgTextList = [];
    },
    //获取报告结论
    getReportConclution(regNo) {
      this.projectResult = [];
      this.suggestions = [];
      this.$refs.summaryCom.summaryList = [];
      this.$refs.summaryCom.newSuggestions = [];
      this.$refs.summaryCom.fixed_newSuggestions = [];
      this.$refs.summaryCom.mergeObj = {};
      return new Promise((resolve, reject) => {
        if (!regNo) {
          this.$message({
            message: '没有体检号无法查询!',
            type: 'warning',
            showClose: true
          });
          return;
        }
        this.$refs.popoverRef.doClose();
        this.searchDialog = false;
        //this.regNo = regNo; //双击列表把体检号赋值文本框
        this.GetSimpleItemResult(regNo);
        this.$ajax
          .post(this.$apiUrls.GetReportConclusionV2, [], {
            query: { regNo: regNo, oper: 0, isOccupation: true }
          })
          .then((r) => {
            this.$refs.summaryCom.recordFlag = true;
            this.$refs.summaryCom.record = [];
            let { success, returnData } = r.data;
            if (!success) return;
            if (returnData.patient.peStatus === 4) {
              this.isExamine = 2;
            } else if (returnData.patient.peStatus === 2) {
              this.isExamine = 1;
            } else {
              this.isExamine = 0;
            }

            this.suggestions = dataUtils.deepCopy(returnData.suggestions);
            this.$refs.summaryCom.newSuggestions = dataUtils.deepCopy(
              returnData.suggestions
            );
            this.$refs.summaryCom.fixed_newSuggestions = dataUtils.deepCopy(
              returnData.suggestions
            );
            this.activeName = 'first';
            this.isMain = returnData.patient.peStatus;
            if (this.isMain === 2) {
              returnData.checkDoctorName = '';
              returnData.checkTime = '';
            }
            this.reportConclution = returnData;
            this.$refs.summaryCom.summaryList = dataUtils.deepCopy(
              returnData.combSummarys
            );
            this.getAuditoryResult();
            this.getFollowUpState();
            this.GetQuestionDataByRegNo(this.reportConclution.patient);
            resolve(returnData);
          });
        this.getImgTextList(regNo);
      });
    },
    // 暂存
    temporarySave() {
      if (this.isMain >= 3) {
        this.$message({
          message: '已主检/已审核状态不能暂存！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (!this.reportConclution.patient.regNo) {
        this.$message({
          message: '请选择体检人！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$refs.summaryCom.cancelVerifyTableHazard();
      this.$nextTick(async () => {
        if (this.$refs.summaryCom.isHaveModify) {
          setTimeout(() => {
            this.temporarySave();
          }, 300);
          return;
        }
        await this.$refs.summaryCom.handleSort();
        //主检医生
        this.reportConclution.checkDoctorCode = '';
        this.reportConclution.checkDoctorName = '';
        (this.reportConclution.checkTime = null),
          (this.reportConclution.combSummarys =
            this.$refs.summaryCom.summaryList);
        this.reportConclution.suggestions =
          this.$refs.summaryCom.newSuggestions;

        if (!this.reportConclution.conclusion) {
          this.reportConclution.conclusion = '';
        }
        this.reportConclution.isOccupation = true;
        this.$ajax
          .post(this.$apiUrls.TempSaveReportConclusionV2, this.reportConclution)
          .then((r) => {
            let { success, returnData } = r.data;
            if (!success) return;
            this.$message({
              message: '暂存成功！',
              type: 'success',
              showClose: true
            });
          });
      });
    },
    //保存主检
    mainTest(isMainCheck = true) {
      if (!this.reportConclution.patient.regNo) {
        this.$message({
          message: '请选择体检人！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (!this.$refs.summaryCom.verifyTableHazard()) {
        this.$message({
          message: '请完善危害因素内容！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$nextTick(async () => {
        if (this.$refs.summaryCom.isHaveModify) {
          setTimeout(() => {
            this.mainTest(true);
          }, 300);
          return;
        }
        await this.$refs.summaryCom.handleSort();
        //主检医生
        this.reportConclution.checkDoctorCode =
          this.G_userInfo.codeOper.operatorCode;
        this.reportConclution.checkDoctorName = this.G_userInfo.codeOper.name;
        this.reportConclution.combSummarys = this.$refs.summaryCom.summaryList;
        this.reportConclution.suggestions =
          this.$refs.summaryCom.newSuggestions;

        if (!this.reportConclution.conclusion) {
          this.reportConclution.conclusion = '';
        }
        this.reportConclution.patient.isMainCheck = isMainCheck;
        this.reportConclution.isOccupation = true;
        // return;
        this.$ajax
          .post(
            this.$apiUrls.ConfirmReportConclusionV2,
            this.reportConclution,
            {
              query: { oper: 0 }
            }
          )
          .then((r) => {
            let { success, returnData } = r.data;
            if (!success) return;
            this.$message({
              message: '主检保存成功!',
              type: 'success',
              showClose: true
            });
            this.processShow = true;
            this.isMain = 3;
            // this.getReportConclution(this.reportConclution.patient.regNo); //刷新数据
            // this.getPatientList();
            this.getReportConclution(this.reportConclution.patient.regNo); //刷新数据
            this.getPatientList();
          });
      });
    },
    // 取消保存主检
    cancelMainTest() {
      if (!this.reportConclution.patient.regNo) {
        this.$message({
          message: '请先选择要取消保存的结论!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$ajax
        .post(this.$apiUrls.CancelReportConclusionNewV2, [], {
          query: {
            regNo: this.reportConclution.patient.regNo,
            doctorCode: this.G_userInfo.codeOper.operatorCode,
            oper: 0,
            isOccupation: true
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$message({
            message: '取消主检成功!',
            type: 'success',
            showClose: true
          });
          this.isMain = 2;
          this.getReportConclution(this.reportConclution.patient.regNo); //刷新数据
          this.getPatientList();
        });
    },
    mainCheckList() {},
    //主检列表点击
    mainClick3(tab, event) {
      // if (this.activeMain3 == "main1") {
      //   this.checkStatus = 1;
      // } else if (this.activeMain3 == "main2") {
      //   this.checkStatus = 2;
      // } else {
      //   this.checkStatus = 3;
      // }
      // this.getPatientList();
    },
    //体检分类过滤
    filterCls(peCls, filterCode) {
      if (this.activeMain3 == 'main1') {
        this.viewTableList1 = this.viewTableListCopy1.filter((item) => {
          return peCls === '' ? true : item.peCls == peCls;
        });
      } else if (this.activeMain3 == 'main2') {
        this.viewTableList2 = this.viewTableListCopy2.filter((item) => {
          // return peCls === "" ? true : item.peCls == peCls;
          if (peCls === '') {
            return filterCode === 0
              ? true
              : item.checkDoctorCode == this.G_userInfo.codeOper.operatorCode;
          } else {
            return filterCode === 0
              ? item.peCls == peCls
              : item.peCls == peCls &&
                  item.checkDoctorCode == this.G_userInfo.codeOper.operatorCode;
          }
        });
      } else {
        this.viewTableList3 = this.viewTableListCopy3.filter((item) => {
          // return peCls === "" ? true : item.peCls == peCls;
          if (peCls === '') {
            return filterCode === 0
              ? true
              : item.checkDoctorCode == this.G_userInfo.codeOper.operatorCode;
          } else {
            return filterCode === 0
              ? item.peCls == peCls
              : item.peCls == peCls &&
                  item.checkDoctorCode == this.G_userInfo.codeOper.operatorCode;
          }
        });
      }
    },
    handleClick(tab, event) {
      this.$refs.summaryCom.newSuggestions = this.reportConclution.suggestions;
      if (this.activeName == 'third') {
        this.$refs.resultEntry_Ref.headerInfo = this.reportConclution.patient;
        this.$refs.resultEntry_Ref.getNavList();
      }
    },
    search() {
      this.searchDialog = true;
    },
    searchDialogClose() {
      this.searchDialog = false;
    },
    // 咨询列表按钮
    consultList() {
      this.consultDialog = true;
      this.getQuestionList();
    },
    consultDialogClose() {
      this.consultDialog = false;
    },
    // 生成结论
    createReport() {
      if (!this.reportConclution.patient.regNo) {
        this.$message({
          message: '没有体检号无法生成结论!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.isMain !== 2) {
        this.$message({
          message: '已主检，无法重新生成结论!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm('是否确定要生成结论?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.generateSummarySuggestions();
        })
        .catch(() => {});
    },
    //生成结论
    generateSummarySuggestions() {
      this.suggestions = [];
      this.$refs.summaryCom.summaryList = [];
      this.$refs.summaryCom.newSuggestions = [];
      this.$refs.summaryCom.fixed_newSuggestions = [];
      this.$refs.summaryCom.mergeObj = {};
      this.$ajax
        .post(this.$apiUrls.GenerateSummarySuggestionFromRecordV2, [], {
          query: {
            regNo: this.reportConclution.patient.regNo,
            isOccupation: true
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$refs.summaryCom.newSuggestions = returnData.suggestions;
          this.$refs.summaryCom.fixed_newSuggestions = dataUtils.deepCopy(
            returnData.suggestions
          );
          this.$refs.summaryCom.summaryList = returnData.comSummarys;
          this.$message({
            type: 'success',
            message: '已重新生成结论!'
          });
        });
    },
    //关闭历史报告弹出
    hisDrawerClose() {
      this.hisDrawer = false;
    },
    historyReport() {
      if (!this.reportConclution.patient.regNo) {
        this.$message({
          message: '请先点击要查看历史报告的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.hisDrawer = true;
    },
    // 获取咨询列表
    getQuestionList() {
      this.$ajax
        .post(this.$apiUrls.ReadQuestionListByCheck, '', {
          query: { questionerCode: this.G_userInfo.codeOper.operatorCode }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.conclusionList = returnData || [];
        });
    },
    //本次体检结论列表
    getConclusion() {
      this.$ajax
        .post(this.$apiUrls.RD_CodeConclusionTemplate + '/Read', [])
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.conclusionLists = returnData || [];
        });
    },
    // 处理参考值
    disposeFKVal(row) {
      let lLimit = row.lowerLimit;
      let uLimit = row.upperLimit;
      if (lLimit && !uLimit) {
        return `${Number(lLimit) ? '≥' : ''}${lLimit}`;
      }
      if (uLimit && !lLimit) {
        return `${Number(uLimit) ? '≤' : ''}${uLimit}`;
      }
      if (lLimit && uLimit) {
        return `${lLimit}-${uLimit}`;
      }
    },
    // 销毁前的回调
    onPopoverHide() {
      let node = document.querySelector('body>.my_popover'); //隐藏时删除
      node?.remove();
    },
    // 队列的双击回调
    patientListDbClick(row) {
      this.getReportConclution(row.regNo);
    },
    // 立即审核
    processSubmit() {
      this.$refs['process_Ref'].validate((valid) => {
        if (valid) {
          let auditDoctor = JSON.parse(this.processInfo.auditDoctorCode);
          let datas = {
            regNo: this.reportConclution.patient.regNo,
            auditDoctorCode: auditDoctor.operatorCode,
            auditDoctorName: auditDoctor.operatorName,
            isOccupation: true
          };

          this.$ajax
            .post(this.$apiUrls.AuditReportConclusionV2, datas)
            .then((r) => {
              let { success, returnData } = r.data;
              if (!success) return;
              this.$message({
                message: '审核成功!',
                type: 'success',
                showClose: true
              });
              this.isExamine = 2;
              this.processShow = false;
              this.getReportConclution(this.reportConclution.patient.regNo); //刷新数据
              this.getPatientList();
            });
        } else {
          return false;
        }
      });
    },
    // 获取审核医生列表
    getOperatorList() {
      this.$ajax
        .paramsPost(this.$apiUrls.GetOperatorListByRoleCode, {
          roleCode: 'A04'
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.processDocList = returnData || [];
        });
    },

    /**
     * @author: justin
     * @description: 显示选择身份信息
     * @return {*}
     */
    showChoiceRegisterHandle() {
      this.showChoiceRegister = true;
    },

    /**
     * @author: justin
     * @description: 确认选择身份信息
     * @param {*} data 数据
     * @return {*}
     */
    confrimChoiceRegDataHandle(data) {
      if (!data) {
        this.$message.warning('请选择身份信息!');
        return;
      }

      this.cancelChoiceRegDataHandle();
      this.searchByRegNo(data.regNo);
    },

    /**
     * @author: justin
     * @description: 取消选择身份信息
     * @return {*}
     */
    cancelChoiceRegDataHandle() {
      this.showChoiceRegister = false;
      this.keyword = '';
    },
    // 取消审核
    cancelExamineClick() {
      if (!this.reportConclution.patient.regNo) {
        this.$message({
          message: '请先选择要取消保存的结论!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$ajax
        .post(this.$apiUrls.CancelReportConclusionNewV2, [], {
          query: {
            regNo: this.reportConclution.patient.regNo,
            doctorCode: this.G_userInfo.codeOper.operatorCode,
            oper: 1,
            isOccupation: true
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$message({
            message: '取消审核成功!',
            type: 'success',
            showClose: true
          });
          this.getReportConclution(this.reportConclution.patient.regNo); //刷新数据
          this.getPatientList();
        });
    },
    // 复查按钮点击的回调
    reviewBtnClick() {
      this.reviewDialogShow = true;
      this.getReviewList();
    },
    // 获取复查信息
    getReviewList() {
      this.$ajax
        .paramsPost(this.$apiUrls.ReadRecheckInfo, {
          regNo: this.reportConclution.patient.regNo
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.reviewList = returnData?.recheckCombs;
          this.recheckOrderNo = returnData?.recheckOrderNo;
          this.$nextTick(() => {
            let tableRef = this.$refs.reviewTable_Ref.$refs.tableCom_Ref;
            this.reviewList.forEach((item) => {
              if (item.isRecheck) {
                tableRef.toggleRowSelection(item);
              }
            });
          });
        });
    },
    // 复查项目选中的回调
    reviewSelectionChange(row, checkList) {
      this.checkReviewList = checkList;
    },
    // 提交复查
    reviewSave() {
      if (this.checkReviewList.length === 0) {
        this.$message({
          message: '请先选择复查项目!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      console.log(this.checkReviewList);
      let recheckCombs = this.checkReviewList.map((item) => item.combCode);
      let datas = {
        regNo: this.reportConclution.patient.regNo,
        recheckCombs: recheckCombs
      };
      this.$ajax.post(this.$apiUrls.CreateRecheckOrder, datas).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.$message({
          message: '提交复查成功!',
          type: 'success',
          showClose: true
        });
        this.reviewDialogShow = false;
      });
    },
    // 取消复查
    cancelReview() {
      this.$ajax
        .paramsPost(this.$apiUrls.CancelRecheckOrder, {
          regNo: this.reportConclution.patient.regNo
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$message({
            message: '取消复查成功!',
            type: 'success',
            showClose: true
          });
          this.reviewDialogShow = false;
        });
    }
  },
  watch: {
    'reportConclution.patient': {
      handler: function (n, o) {
        this.HisPatInfo = n;
      },
      deep: true
    }
  },
  created() {
    let startDate = moment().add(-7, 'd').format('YYYY-MM-DD');
    let endDate = moment().startOf('day').format('YYYY-MM-DD');
    this.dateVal1 = [startDate, endDate];
    this.dateVal2 = [startDate, endDate];
    this.dateVal3 = [startDate, endDate];
  },
  mounted() {
    // this.getPatientList();
    this.getConclusion();
    this.getHarmList();
  },
  activated() {
    let routeParmas = this.$route.params;

    if (JSON.stringify(routeParmas) != '{}') {
      this.searchByRegNo(routeParmas.regNo);
    }
  }
};
</script>
<style lang="less" scoped>
@custom-color-1: #089c66;
@custom-color-2: #1770df;
@custom-color-3: #d63031;

.mainInspection {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  display: flex;
  color: #2d3436;
  flex-direction: column;

  .header-wrapper {
    overflow-y: hidden;
    background: #fff;
    padding: 5px 10px;
    margin-bottom: 5px;
    width: 100%;

    .anchorPoint_btn {
      width: 10px;
      height: calc(100% - 164px);
      position: absolute;
      left: 0;
      z-index: 11;
      bottom: 0;
    }

    .topBtn {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 5px;

      .leftTop {
        display: flex;
        align-items: center;

        p {
          display: flex;
          align-items: center;
          margin-left: 20px;
          margin-right: 20px;
          width: 170px;

          label {
            font-family: PingFangSC-Medium;
            font-size: 14px;
            color: #2b3436;
            font-weight: 500;
          }
        }
      }
    }

    .topTitle {
      padding-left: 10px;

      .type_head {
        width: 90%;
        height: 100%;
        // margin-left: 100px;
        &:last-child {
          padding-top: 0;
        }

        li {
          display: flex;
          height: 32px;
          font-family: PingFangSC-Medium;
          font-size: 14px;
          color: #2d3436;
        }

        .every_inp {
          display: flex;
          align-items: center;
          margin-right: 10px;
          line-height: normal;

          label {
            margin-right: 10px;
            min-width: 37px;
            text-align: right;
            font-family: PingFangSC-Semibold;
            font-size: 14px;
            color: #2b3436;
            text-align: right;
            // font-weight: 600;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          p {
            flex: 1;
            flex-shrink: 0;
            font-family: PingFangSC-Regular;
            font-size: 14px;
            color: #2b3436;
            font-weight: 600;
          }
        }

        .regNo_inp {
          width: 180px;
        }
        .less_inp {
          width: 100px;
        }
        .cont_inp {
          width: 300px;
        }
        .num_inp {
          width: 210px;
        }
        .flex_inp {
          flex: 1;
          flex-shrink: 0;
        }
      }

      .patient-photo {
        width: 5.5rem;
        height: 5.5rem;
        border-radius: 5px;
        position: absolute;
        z-index: 1;
        margin-top: -25px;
      }

      /deep/.el-collapse {
        border-top: unset;
        border-bottom: unset;

        .el-collapse-item__header {
          border-bottom: unset;
        }

        .el-collapse-item__wrap {
          border-bottom: unset;
        }
      }
    }
  }

  /deep/.el-table__row--striped td.el-table__cell,
  .vxe-table--render-default .vxe-body--row.row--stripe {
    background-color: #e7f0fb !important;
  }

  // /deep/.el-popover {
  //   height: calc(100% - 142px);
  //   height: 600px !important;
  // }

  .collapse /deep/.el-collapse-item__header {
    background: #fff;
    height: 28px;
  }

  /deep/.el-collapse-item__content {
    padding-bottom: 0;
  }

  .centerCont {
    flex: 1;
    overflow: auto;
    background: #fff;
    padding: 0 5px 5px;
    display: flex;
    .el-table {
      color: #000;
    }
    .con_wrap {
      display: flex;
      flex-direction: column;
      margin-right: 5px;

      .bottom_wrap,
      .fold_div {
        border: 1px solid #b2bec3;
        border-radius: 4px;
        flex: 1;
        flex-shrink: 0;
        overflow: auto;

        .projectNav_wrap {
          height: 100%;
          overflow: auto;
          position: absolute;
          top: 0;
          left: -260px;
          border: 1px solid #b2bec3;
          border-radius: 4px;
          width: 250px;
          z-index: 12;
          background: #fff;
          transition: all 0.3s ease-in-out;
          a {
            display: block;
            padding: 10px;
            list-style: none;
            text-decoration: none;
            border-bottom: 1px solid #b2bec3;
            font-size: 16px;
            color: #000;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            &:hover {
              color: @custom-color-2;
            }
          }
        }
        .projectNav_wrap_active {
          transform: translateX(253px);
        }
        .result_div .popover_div {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        /deep/ .el-collapse-item {
          margin-bottom: 2px;
          .el-collapse-item__header {
            background: #eeeeee;
            padding: 0 5px;
            font-size: 18px;
            color: #2d3436;
            line-height: normal;
            font-weight: 600;
            height: auto;
            min-height: 48px;

            .el-icon-s-comment {
              font-size: 20px;
              margin-top: 5px;
            }

            .el-collapse-item__arrow {
              display: none;
            }
          }

          .el-table__cell {
            border-bottom: unset;
          }
        }

        .project_result {
          padding: 0 5px;
          margin-top: 5px;
          display: flex;

          span {
            font-size: 14px;
            color: @custom-color-2;
          }

          & > div {
            flex: 1;
            flex-shrink: 0;
          }

          .tags_sty {
            margin-right: 5px;
            margin-bottom: 5px;
            white-space: normal;
            height: auto;
            color: @custom-color-2;
            font-family: PingFangSC-Medium;
            &:last-child {
              margin-right: 0;
            }
          }
          .tags_red {
            margin-right: 5px;
            margin-bottom: 5px;
            white-space: normal;
            height: auto;
            color: red;
            &:last-child {
              margin-right: 0;
            }
          }
        }
      }
      .redClass {
        color: red;
        font-weight: 600;
      }
      .defaultClass {
        color: #000;
        font-weight: 600;
      }
      /deep/.SummaryCom {
        flex: 1;
        flex-shrink: 0;
        overflow: auto;
      }

      /deep/.leftCont {
        overflow: auto;
      }
    }

    .left_wrap {
      width: 30%;
      flex-shrink: 0;
      position: relative;
    }

    .right_wrap {
      flex: 1;
      flex-shrink: 0;
      display: flex;
      flex-direction: column;

      .center_wrap {
        flex: 1;
        flex-shrink: 0;
        display: flex;
        overflow: auto;

        /deep/.SummaryCom .title > span {
          font-size: 16px;
          font-weight: normal;
        }

        /deep/.SummaryCom .title {
          margin: 5px 0;
        }

        /deep/.SummaryCom .leftDiv .leftCont .contDiv .titleLeft {
          height: auto;
          padding: 5px;
          line-height: normal;

          span:first-child {
            flex: 1;
            flex-shrink: 0;
          }
        }
      }

      .overview {
        width: 40%;
        flex-shrink: 0;
      }

      .suggest {
        flex: 1;
        flex-shrink: 0;
        margin-right: 0;
      }
    }
  }

  .centerCont-tabs {
    /deep/.el-tabs__item {
      padding: 0 10px;
    }
  }

  .tab-pane {
    padding: 10px 10px 10px 0;
  }

  .footerCont {
    // height: 118px;
    width: 100%;
    background: #fff;

    .footer1 {
      display: flex;
      line-height: 32px;
      width: 100%;
      margin: 3px 0;

      label {
        padding-right: 10px;
        font-size: 18px;
        font-weight: 600;
      }

      p {
        flex: 1;

        & > div {
          width: 100%;
        }
        /deep/.el-input--small .el-input__inner {
          color: #000;
          font-weight: 600;
        }
      }
    }

    // .footer2 {
    //   display: flex;
    //   line-height: 32px;

    //   .foot_inp {
    //     // flex: 1;
    //     width: 190px;
    //     display: flex;
    //     align-items: center;
    //     margin-right: 20px;
    //     overflow: auto;
    //     &:nth-child(1){
    //       flex:1;
    //       flex-shrink: 0;
    //     }
    //     &:nth-child(even){
    //       /deep/input{
    //         padding-left: 5px;
    //       }
    //     }
    //     /deep/input{
    //       padding-right: 5px;
    //     }
    //     &:last-child {
    //       margin-right: 0;
    //     }
    //     label {
    //       width: 70px;
    //       font-size: 14px;
    //       font-weight: 600;
    //     }

    //     p {
    //       flex: 1;
    //       overflow: hidden;
    //     }
    //     /deep/.el-input--small .el-input__inner {
    //       color: #000;
    //       font-weight: 600;
    //     }
    //   }

    //   .el-date-editor.el-input,
    //   .el-date-editor.el-input__inner {
    //     width: 100%;
    //   }
    // }
  }

  /deep/.el-date-editor.el-input,
  /deep/.el-date-editor.el-input__inner {
    width: 100%;
  }

  /deep/.el-drawer__header {
    align-items: center;
    display: flex;
    background: rgba(23, 112, 223, 0.2);
    border-radius: 4px 4px 0 0;
    line-height: 42px;
    font-family: PingFangSC-Medium;
    font-size: 18px;
    padding: 0;
    padding-left: 20px;
    color: #2d3436;
    margin-bottom: 0;
  }

  /deep/.el-drawer__close-btn {
    font-size: 30px;
  }

  .firstPage {
    flex: 1;
    overflow: auto;
  }

  .el-tabs {
    height: 100%;
    display: flex;
    flex-direction: row;
  }

  /deep/.el-tabs__content {
    flex: 1;
  }

  .el-tab-pane {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /deep/.btnHeader {
    padding: 0 !important;
    margin-bottom: 10px;
  }

  /deep/.rightcont {
    padding: 0 !important;
  }

  /deep/.rightBom {
    padding: 10px 0 0 !important;
  }

  /deep/.el-dialog__header {
    align-items: center;
    display: flex;
    background: rgba(23, 112, 223, 0.2);
    border-radius: 4px 4px 0 0;
    line-height: 42px;
    height: 42px;
    font-family: PingFangSC-Medium;
    font-size: 18px;
    padding: 0;
    padding-left: 20px;
    color: #2d3436;
    margin-bottom: 0;
  }

  /deep/.el-dialog__headerbtn {
    font-size: 30px;
    top: 5px;
  }

  /deep/.el-dialog__body {
    padding-top: 0;
  }

  .positive_btn {
    margin-left: 10px;
    background: #c9c9c9;

    font-size: 14px;
    height: 29px;
    line-height: 29px;
    width: 60px;
    text-align: center;
    color: #fff;
    cursor: pointer;
  }
  .is_positive {
    background: #d63031;
  }
  .collapse_div {
    display: flex;
    flex: 1;
    justify-content: space-between;
    .operNameClass {
      margin-left: 10px;
      margin-right: 8px;
      text-align: right;
      font-weight: 400;
      font-size: 14px;
      width: -moz-max-content;
      width: max-content;
    }
  }

  .icon-green {
    color: @custom-color-1;
  }

  .icon-blue {
    color: @custom-color-2;
  }

  .icon-red {
    color: @custom-color-3;
  }

  .text-overflow-hidden {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .projectResult_btn {
    display: none;
  }
  .review_wrap {
    height: 500px;
    overflow: auto;
    display: flex;
    flex-direction: column;
    .review_head {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 5px 0;
      span {
        font-size: 20px;
        color: #1770df;
        font-weight: 500;
      }
    }
    .review_content {
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
    }
  }
  @media screen and (max-width: 1440px) {
    .left_wrap {
      display: none !important;
    }
    .projectResult_btn {
      display: block;
    }
  }
}
@import url(./css/mainInspectionMedia.less);
</style>

<style lang="less">
@custom-color-1: #089c66;
@custom-color-2: #1770df;
@custom-color-3: #d63031;

.popverClass {
  height: calc(100% - 200px);
  /* overflow: auto; */
  /* left: 20px !important; */
  .el-tabs__header {
    margin-bottom: 0;
  }
}

.popverClass .el-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popverClass .el-tabs__content {
  flex: 1;
  overflow: auto;
}

.popverClass .el-tab-pane {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popoverDiv {
  display: flex;
  flex-direction: row;
  height: 100%;
}

.popoverDiv .poDiv1 {
  margin-right: 20px;
}
.my_popover {
  background: #dae4f2;
  .popper__arrow::after {
    border-right-color: rgba(0, 0, 0, 0.6) !important;
  }
  li {
    color: #000;
  }
}
.popover_ul {
  font-size: 16px;
  display: flex;
  color: #fff;
  li {
    flex: 1;
    flex-shrink: 0;
    // &:last-child span{
    //   color:#d63031;
    // }
  }
}
.popover_i {
  font-style: normal;
  color: #d63031;
  font-size: 18px;
  margin-right: 5px;
}
.projectResult_popover {
  min-width: 670px;
  .con_wrap {
    height: 100%;
    overflow: auto;
    display: flex;
    flex-direction: column;
    .bottom_wrap {
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
      .icon-green {
        color: #089c66;
      }
    }
    .projectResult_header {
      display: flex;
      justify-content: space-between;
      height: 41px;
      align-items: center;
      margin-left: 20px;
      font-family: PingFangSC-Medium;
      font-size: 16px;
      color: #1770df;
      font-weight: 550;
    }
  }
  .el-collapse-item {
    margin-bottom: 2px;
    .el-collapse-item__header {
      background: #eeeeee;
      padding: 0 5px;
      font-size: 18px;
      color: #2d3436;
      line-height: normal;
      font-weight: 600;
      height: auto;
      min-height: 48px;

      .el-icon-s-comment {
        font-size: 20px;
        margin-top: 5px;
      }

      .el-collapse-item__arrow {
        display: none;
      }
    }

    .el-table__cell {
      border-bottom: unset;
    }
  }
  .collapse_div {
    display: flex;
    flex: 1;
    justify-content: space-between;
    .operNameClass {
      margin-left: 10px;
      margin-right: 8px;
      text-align: right;
      font-weight: 400;
      font-size: 14px;
      width: -moz-max-content;
      width: max-content;
    }
  }
  .project_result {
    padding: 0 5px;
    margin-top: 5px;
    display: flex;

    span {
      font-size: 14px;
      color: @custom-color-2;
    }

    & > div {
      flex: 1;
      flex-shrink: 0;
    }

    .tags_sty {
      margin-right: 5px;
      margin-bottom: 5px;
      white-space: normal;
      height: auto;
      color: @custom-color-2;
      font-family: PingFangSC-Medium;
      &:last-child {
        margin-right: 0;
      }
    }
    .tags_red {
      margin-right: 5px;
      margin-bottom: 5px;
      white-space: normal;
      height: auto;
      color: red;
      &:last-child {
        margin-right: 0;
      }
    }
  }
  .el-collapse-item__content {
    padding-bottom: 0;
  }
}
.anchor_popover {
  height: calc(100% - 200px);
  display: flex;
  flex-direction: column;
  .projectNav_wrap {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    border-top: 1px solid #b2bec3;
    width: 250px;
    background: #fff;
    a {
      display: block;
      cursor: pointer;
      padding: 10px;
      list-style: none;
      text-decoration: none;
      border-bottom: 1px solid #b2bec3;
      font-size: 16px;
      color: #000;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      &:hover {
        color: @custom-color-2;
      }
    }
  }
}
.questionnaire-disabled {
  opacity: 0.5;
}
@import url(./css/mainInspectionCommon.less);
</style>
