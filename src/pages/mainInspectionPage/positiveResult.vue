<template>
  <div class="positiveResult_page">
    <div class="con_wrap left_wrap">
      <p>
        关键字列表：
        <i class="el-icon-s-unfold"></i>
      </p>
      <div class="bottom_wrap">
        <!-- <el-table size="small" stripe :cell-style="cellStyleFun" :data="keywordList" style="width: 100%;font-size: 14px;">
                    <el-table-column prop="itemName" label="组合/项目"></el-table-column>

                    <el-table-column prop="result" label="小结"></el-table-column>
                    <el-table-column prop="keywordName" label="关键字"></el-table-column>
                    <el-table-column prop="type" label="类型"></el-table-column>
                </el-table> -->
        <el-collapse v-model="activeNames" size="small">
          <el-collapse-item
            :title="item.combName"
            :name="item.combCode"
            v-for="item in projectResult"
            :key="item.combCode"
          >
            <!-- 项目列表 -->
            <el-table
              size="small"
              :data="item.projects"
              style="width: 100%; font-size: 14px"
            >
              <el-table-column prop="itemName" label="项目名称" width="180">
              </el-table-column>
              <el-table-column prop="address" label="结果">
                <template #default="scope">
                  <div class="">
                    <span v-for="tag in scope.row.results" :key="tag.index">{{
                      tag.itemTag
                    }}</span>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <!-- 体检小结 -->
            <div class="project_result">
              <span>体检小结：</span>
              <div>
                <el-tag
                  size="small"
                  class="tags_sty"
                  v-for="result in item.combinations"
                  :key="result.index"
                  >{{ result.combTag }}</el-tag
                >
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
    <div class="con_wrap left_wrap">
      <p>
        已选重大阳性列表：
        <el-button type="primary" size="mini" @click="save">确认</el-button>
      </p>
      <div class="bottom_wrap">
        <el-table
          size="small"
          stripe
          :data="selectedData"
          style="width: 100%; font-size: 14px"
        >
          <el-table-column prop="positiveType" label="类别" width="60">
            <template #default="scope">
              {{ G_EnumList['PositiveType'][scope.row.positiveType] }}
            </template>
          </el-table-column>
          <el-table-column
            prop="positiveName"
            label="重大阳性名称"
          ></el-table-column>
          <el-table-column prop="itemName" label="删除" width="60">
            <template #default="scope">
              <span
                @click="del(scope.row)"
                class="icon_del iconfont icon-Rightxiangyou34"
              ></span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="con_wrap left_wrap">
      <p>
        可选重大阳性列表：
        <el-input
          v-model="searchVal"
          clearable
          @clear="search"
          @input="search"
          size="small"
          style="width: 200px"
          placeholder="请输入内容"
        ></el-input>
      </p>
      <div class="bottom_wrap">
        <el-table
          size="small"
          stripe
          :data="unselectedData"
          style="width: 100%; font-size: 14px"
        >
          <el-table-column prop="itemName" label="添加" width="60">
            <template #default="scope">
              <span
                @click="add(scope.row, scope.row.index)"
                class="icon_add iconfont icon-Leftxiangzuo35"
              ></span>
            </template>
          </el-table-column>
          <el-table-column prop="positiveType" label="类别" width="60">
            <template #default="scope">
              {{ G_EnumList['PositiveType'][scope.row.positiveType] }}
            </template>
          </el-table-column>
          <el-table-column
            prop="positiveName"
            label="重大阳性名称"
          ></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { dataUtils } from '../../common';
export default {
  components: {},
  name: 'positiveResult',
  props: {
    patientInfo: {
      type: Object,
      default: () => {
        return {};
      }
    },
    P_projectResult: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  computed: {
    ...mapGetters(['G_userInfo', 'G_EnumList'])
  },
  data() {
    return {
      keywordList: [], //关键字列表
      selectedData: [], //已选重大阳性列表
      unselectedData: [], //可选重大阳性列表
      fixed_unselectedData: [], //固定的可选重大阳性列表
      searchVal: '',
      projectResult: [],
      activeNames: []
    };
  },
  methods: {
    // 获取数据
    getListData() {
      console.log(this.patientInfo);
      let activeNames = [];
      this.$ajax
        .paramsPost(this.$apiUrls.PositiveResultReview, {
          regNo: this.patientInfo.patientInfo.regNo
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.keywordList = returnData.keywordData || [];
          this.selectedData = returnData.selectedData || [];
          this.fixed_unselectedData = returnData.unselectedData || [];
          this.unselectedData =
            dataUtils.deepCopy(returnData.unselectedData) || [];
          console.log(this.P_projectResult);
          let projectResult = this.P_projectResult.filter((item) => {
            if (returnData.combCodes.includes(item.combCode)) {
              activeNames.push(item.combCode);
            }
            return returnData.combCodes.includes(item.combCode);
          });
          this.activeNames = activeNames;
          this.projectResult = projectResult;
        });
    },
    cellStyleFun({ row, column, rowIndex, columnIndex }) {
      console.log(column);
      if (column.property == 'keywordName') {
        return {
          color: '#1770DF'
        };
      }
    },
    // 添加
    add(row, idx) {
      this.selectedData.unshift({
        ...row,
        regNo: this.patientInfo.patientInfo.regNo
      });
      this.unselectedData.splice(idx, 1);
      this.fixed_unselectedData.map((item, index, arr) => {
        if (item.positiveCode == row.positiveCode) {
          arr.splice(index, 1);
        }
      });
    },
    // 删除
    del(row) {
      console.log(row);
      this.unselectedData.unshift({
        positiveCode: row.positiveCode,
        positiveName: row.positiveName,
        positiveType: row.positiveType
      });
      this.fixed_unselectedData.unshift({
        positiveCode: row.positiveCode,
        positiveName: row.positiveName,
        positiveType: row.positiveType
      });
      this.selectedData.splice(row.index, 1);
    },
    // 保存
    save() {
      let datas = {
        regNo: this.patientInfo.patientInfo.regNo,
        name: this.patientInfo.patientInfo.name,
        sex: this.patientInfo.patientInfo.sex,
        age: this.patientInfo.patientInfo.age,
        tel: this.patientInfo.patientInfo.tel,
        creator: this.G_userInfo.codeOper.name,
        details: this.selectedData
      };
      console.log(datas);
      this.$ajax.post(this.$apiUrls.SavePositiveResult, datas).then((r) => {
        let { success, returnMsg } = r.data;
        if (!success) {
          this.$message({
            message: returnMsg,
            type: 'warning'
          });
          return;
        }
        this.$parent.$parent.positiveResultDialog = false;
        this.$message({
          message: returnMsg,
          type: 'success'
        });
      });
    },
    // 搜索可选重大阳性列表
    search() {
      let arr = this.fixed_unselectedData.filter((item) => {
        return item.positiveName.indexOf(this.searchVal) != -1;
      });
      console.log(arr);
      this.unselectedData = dataUtils.deepCopy(arr);
    }
  },
  created() {
    this.getListData();
  }
  // watch:{
  //     patientInfo:function(n,o){
  //         console.log(8888888989999999999999);
  //         this.getListData();
  //     }

  // }
};
</script>

<style lang="less" scoped>
.positiveResult_page {
  display: flex;
  height: 100%;
  padding: 5px;
  /deep/.el-table--striped .el-table__body tr.el-table__row--striped td {
    background: rgba(23, 112, 223, 0.1);
  }
  .icon_add {
    font-size: 20px;
    color: #1770df;
    cursor: pointer;
  }
  .icon_del {
    color: #d63031;
    font-size: 20px;
    cursor: pointer;
  }
  .con_wrap {
    display: flex;
    flex-direction: column;

    flex: 1;
    flex-shrink: 0;
    & + .con_wrap {
      margin-left: 5px;
    }
    p {
      display: flex;
      justify-content: space-between;
      height: 41px;
      align-items: center;
      font-size: 16px;
      color: #2d3436;
    }

    .bottom_wrap {
      border: 1px solid #b2bec3;
      border-radius: 4px;
      flex: 1;
      flex-shrink: 0;
      overflow: auto;

      .project_result {
        padding: 0 5px;
        margin-top: 5px;
        display: flex;

        span {
          font-size: 14px;
          color: #1770df;
        }

        & > div {
          flex: 1;
          flex-shrink: 0;
        }

        .tags_sty {
          margin-right: 5px;
          margin-bottom: 5px;

          &:last-child {
            margin-right: 0;
          }
        }
      }
    }
  }
}
</style>
