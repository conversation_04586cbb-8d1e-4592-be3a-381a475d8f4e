<template>
  <div class="mainSearch">
    <div class="headrCont">
      <div class="headerLeft">
        <el-form
          :model="searchForm"
          ref="form_ref"
          :rules="rules"
          label-width="80px"
        >
          <el-row>
            <el-col :span="4">
              <el-form-item label="体检号" prop="regNo ">
                <el-input
                  v-model.trim="searchForm.regNo"
                  size="small"
                  clearable
                  placeholder="请输入"
                  @keyup.enter.native="search"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="姓名" prop="name">
                <el-input
                  v-model.trim="searchForm.name"
                  size="small"
                  clearable
                  placeholder="请输入"
                  @keyup.enter.native="search"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="9">
              <el-form-item label="单位" prop="companyCode" class="company">
                <!-- <el-select
                  class="select"
                  v-model.trim="searchForm.companyCode"
                  placeholder="请选择"
                  size="small"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="(item, index) in companyList"
                    :key="index"
                    :label="item.companyName"
                    :value="item.companyCode"
                  ></el-option>
                </el-select> -->
                <el-cascader
                  ref="company_cascader_ref"
                  v-model="searchForm.companyCode"
                  :options="companyList"
                  :props="{ multiple: false }"
                  clearable
                  filterable
                  size="small"
                  collapse-tags
                  @change="companyChange"
                  :filter-method="filterMethod"
                >
                </el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="" class="formBtn">
                <MainBtnCom :btnList="['查询']" @search="search" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="4" v-if="G_config.physicalMode.includes('普检')">
              <el-form-item label="体检分类" prop="peCls">
                <el-select
                  class="select"
                  v-model.trim="searchForm.peCls"
                  placeholder="请选择"
                  size="small"
                  clearable
                  filterable
                >
                  <el-option
                    key="value"
                    label="全部"
                    :value="value"
                  ></el-option>
                  <el-option
                    v-for="(item, index) in G_peClsList"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="体检时间" prop="tjTime">
                <el-date-picker
                  :picker-options="{ shortcuts: G_datePickerShortcuts }"
                  size="small"
                  v-model.trim="tjTime"
                  type="daterange"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @change="search"
                  :clearable="false"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="14">
              <el-form-item class="peStatus" label="">
                <div class="titleRadio">
                  <el-radio-group
                    v-model.trim="searchForm.peStatus"
                    @change="search"
                  >
                    <!-- <el-radio
                      key="value"
                      label="全部"
                      :value="value"
                    ></el-radio>
                    <el-radio
                      v-for="(item, index) in G_peStatus"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-radio> -->
                    <el-radio :label="-1">全部</el-radio>
                    <el-radio :label="0">未检查</el-radio>
                    <el-radio :label="1">正在检查</el-radio>
                    <el-radio :label="2">已检完</el-radio>
                    <el-radio :label="3">已总检</el-radio>
                    <el-radio :label="4">已审核</el-radio>
                  </el-radio-group>
                </div>
                <!-- 查全部 = -1,未检查 = 0, 正在检查 = 1, 已检完 = 2, 已总检 = 3, 已审核 = 4) -->
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <div class="tableCont">
      <PublicTable
        :viewTableList.sync="tableData"
        :theads.sync="theads"
        :tableLoading.sync="loading"
        :columnWidth="columnWidth"
        @rowDblclick="rowDblclick"
        :columnSort="columnSort"
      >
        <template #sex="{ scope }">
          <div>
            {{ G_EnumList['Sex'][scope.row.sex] }}
          </div>
        </template>
        <template #peCls="{ scope }">
          <div>
            {{ G_EnumList['PeCls'][scope.row.peCls] }}
          </div>
        </template>
        <template #peStatus="{ scope }">
          <span v-if="scope.row.peStatus == 0" style="color: #fab63b">{{
            G_EnumList['PeStatus'][scope.row.peStatus]
          }}</span>
          <span v-else-if="scope.row.peStatus == 2" style="color: #1770df">{{
            G_EnumList['PeStatus'][scope.row.peStatus]
          }}</span>
          <span v-else-if="scope.row.peStatus == 4" style="color: #3cb34f">{{
            G_EnumList['PeStatus'][scope.row.peStatus]
          }}</span>
          <span v-else>{{ G_EnumList['PeStatus'][scope.row.peStatus] }}</span>
        </template>
        <template #isVIP="{ scope }">
          <div>
            <el-checkbox v-model.trim="scope.row.isVIP"></el-checkbox>
          </div>
        </template>
        <template #marryStatus="{ scope }">
          <div>
            {{ G_EnumList['MarryStatus'][scope.row.marryStatus] }}
          </div>
        </template>
      </PublicTable>
    </div>
  </div>
</template>

<script>
import PublicTable from '../../components/publicTable.vue';
import MainBtnCom from './mainBtnCom.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
export default {
  name: 'mainSearch',
  components: { PublicTable, MainBtnCom },
  props: {
    cancel: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      value: -1,
      loading: false,
      searchForm: {
        regNo: '',
        name: '',
        companyCode: '',
        peCls: -1,
        beginTime: '',
        endTime: '',
        peStatus: -1
      },
      tjTime: [dataUtils.getDate(), dataUtils.getDate()], //体检时间
      tableData: [], //表单数据
      columnSort: ['peCls', 'name', 'sex', 'age', 'companyName', 'commonTime'],
      theads: {
        regNo: '体检号',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        marryStatus: '婚姻状况',
        cardNo: '身份证',
        tel: '联系电话',
        peCls: '体检类型',
        activeTime: '体检日期',
        isVIP: 'VIP',
        peStatus: '状态',
        companyName: '工作单位'
      },
      columnWidth: {
        activeTime: 180,
        companyName: 180,
        peCls: 200,
        cardNo: 180,
        tel: 140,
        regNo: 140,
        companyName: 220
      },
      rules: {},
      companyList: [],
      radioVal: 1
    };
  },
  created() {},
  mounted() {
    this.getCompany();
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_peClsList',
      'G_userInfo',
      'G_shareSexList',
      'G_peStatus',
      'G_datePickerShortcuts',
      'G_config'
    ])
  },
  methods: {
    rowDblclick(row) {
      this.$emit('getReportConclution', row.regNo);
      console.log('[ row ]-121', row);
    },
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) return;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.tjTime = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.search();
    },
    //ReadCheckPerson
    search() {
      if (!this.tjTime) {
        this.searchForm.beginTime = '';
        this.searchForm.endTime = '';
      } else {
        this.searchForm.beginTime = this.tjTime[0] || '';
        this.searchForm.endTime = this.tjTime[1] || '';
      }
      let temp = JSON.parse(JSON.stringify(this.searchForm));
      temp.companyCode = temp.companyCode[0];
      let data = {
        ...temp,
        peCls: this.searchForm.peCls === '' ? -1 : this.searchForm.peCls
      };

      this.$ajax.post(this.$apiUrls.ReadCheckPerson, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.tableData = returnData || [];
        if (this.tableData.length < 1) {
          this.$message({
            message: '暂无数据!',
            type: 'success',
            showClose: true
          });
          return;
        }
        this.$message({
          message: '查询成功!',
          type: 'success',
          showClose: true
        });
      });
    }
  }
};
</script>

<style lang="less" scoped>
.mainSearch {
  display: flex;
  flex-direction: column;
  height: 100%;
  /deep/.formBtn .el-form-item__content {
    margin-left: 10px !important;
  }
  .headrCont {
    display: flex;
    flex-direction: row;
    // background: rgba(178, 190, 195, 0.2);
    border-radius: 4px;
    padding: 15px;
    .el-row {
      display: flex;
    }
    .el-form-item {
      margin-bottom: 10px;
      /deep/ .el-form-item__label,
      /deep/.el-form-item__content {
        height: 32px;
        line-height: 32px;
      }
    }
    .select {
      width: 100%;
    }
    .headerLeft {
      flex: 1;
      /deep/.el-input__inner {
        height: 32px;
        line-height: 32px;
      }

      .titleCont {
        line-height: 32px;
        display: flex;
        margin-left: 0 !important;
        padding-bottom: 10px;
        .titleRadio {
          padding-left: 20px;
          background: #fff;
          /deep/.el-radio {
            margin-right: 15px;
            /deep/.el-radio__inner {
              width: 18px;
              height: 18px;
            }
          }
        }
      }
    }
  }

  .tableCont {
    flex: 1;
    margin: 0 15px 15px 15px;
    overflow: auto;
    border: 1px solid #dde2e5;
    border-radius: 4px;
  }

  /deep/.el-checkbox__inner {
    width: 18px;
    height: 18px;
  }
  .peStatus {
    /deep/.el-form-item__content {
      margin-left: 40px !important;
    }
  }
}
</style>
