.mainInspection_media {
  .dropdown-select {
    max-height: 300px;
    border-radius: 4px;
    overflow: auto;
    background-color: #e7e4e4;
  }
  .list-item1 {
    padding: 2px 5px;
  }
  .list-item1:hover {
    background-color: #f5f7fa;
    cursor: pointer;
  }
}
@media screen and(max-width: 1440px) {
  .mainInspection_media {
    .topBtn {
      margin-bottom: 3px;
    }
    .collapse /deep/.el-collapse-item__header {
      height: 18px;
    }
    .centerCont {
      .con_wrap {
        header {
          height: 24px;
          margin: 1px 0;
        }
        // p{
        //   height: 24px;
        // }
        /deep/.el-collapse-item__header {
          min-height: 30px !important;
        }
      }
    }
    .footerCont {
      .footer1,
      .footer2 {
        line-height: 24px;
      }
    }
  }
}
