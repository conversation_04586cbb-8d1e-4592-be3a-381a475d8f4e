<template>
  <!-- 工作量统计 -->
  <div class="workloadStatistics">
    <div class="header-wrap">
      <h3>工作量统计：</h3>
      <div class="search-list">
        <div class="list-item">
          <span style="width: 56px">统计时间</span>
          <el-date-picker
            :picker-options="{ shortcuts: G_datePickerShortcuts }"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
            v-model="searchInfo.date"
            @change="statistics"
            :clearable="false"
          >
          </el-date-picker>
        </div>
        <div style="width: 246px">
          <el-radio-group
            v-model="searchInfo.personGroup"
            @change="radioChange"
          >
            <el-radio :label="0">所有</el-radio>
            <el-radio :label="1">个人</el-radio>
            <el-radio :label="2">团体</el-radio>
          </el-radio-group>
        </div>
        <div class="list-item">
          <span style="width: 36px">单位</span>
          <el-cascader
            ref="company_cascader_ref"
            v-model="searchInfo.companyCode"
            :filter-method="filterMethod"
            :options="companyList"
            :props="{ multiple: false }"
            clearable
            filterable
            size="small"
            collapse-tags
            @change="companyChange"
          >
          </el-cascader>
        </div>
        <el-button
          @click="statistics"
          class="blue_btn btn"
          size="small"
          icon="iconfont icon-tongji"
          >统计</el-button
        >
      </div>
    </div>
    <div class="main">
      <PopulationStatistics :basicData="basicData" :loading="loading" />
      <WorkloadPie
        :menAndWomenRatio="basicData.menAndWomenRatio"
        :personCompanyCount="basicData.personCompanyCount"
        :reportNum="basicData.reportNum"
        :loading="loading"
        ref="workloadPie_ref"
      />
      <MiddleStatistics
        :middleData="middleData"
        :loading="loading"
        ref="middleStatistics_ref"
      />
      <DepartmentStatistics
        :loading="loading"
        :departData="departData"
        ref="department_ref"
      />
      <RightStatistics
        ref="right_ref"
        :rightData="rightData"
        :loading="loading"
      />
    </div>
  </div>
</template>

<script>
import PopulationStatistics from './components/populationStatistics.vue';
import WorkloadPie from './components/workloadPie.vue';
import MiddleStatistics from './components/middleStatistics.vue';
import DepartmentStatistics from './components/departmentStatistics.vue';
import RightStatistics from './components/rightStatistics.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
export default {
  name: 'workloadStatistics',
  components: {
    PopulationStatistics,
    WorkloadPie,
    MiddleStatistics,
    DepartmentStatistics,
    RightStatistics
  },
  data() {
    return {
      loading: true,
      searchInfo: {
        date: [new Date(), new Date()],
        personGroup: 0,
        companyCode: '',
        itemClsCode: '',
        doctorName: '',
        deptCode: ''
      },
      companyList: [],
      // 基础信息统计数据
      basicData: {
        totalNum: 0,
        recordNum: 0,
        checkedNum: 0,
        auditedNum: 0,
        reportedNum: 0,
        menAndWomenRatio: {
          men: 0,
          women: 0,
          ratioOfMen: '',
          ratioOfWomen: ''
        },
        personCompanyCount: {
          companyCount: 0,
          personCount: 0
        },
        reportNum: {
          personNoReport: 0,
          personReport: 0,
          compnayNoReport: 0,
          compnayReport: 0
        }
      },
      // 登记员、激活、开单医生统计数据
      middleData: {
        activeTotal: 0,
        activeXData: [],
        activeYData: [],
        billingTotal: 0,
        billingXData: [],
        billingYData: [],
        registrarData: []
      },
      //科室统计数据
      departData: {
        xData: [],
        yPeopleData: [],
        yPriceData: []
      },
      // 医生、录入员、主检、审核统计数据
      rightData: {
        totalPeople: 0,
        totalPrice: 0,
        xData: [],
        yPeopleData: [],
        yPriceData: [],
        enterData: []
      }
    };
  },
  created() {
    this.getCompanyList();
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_sexList', 'G_datePickerShortcuts'])
  },
  methods: {
    // 获取单位下拉
    getCompanyList() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) return;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.searchInfo.date = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.statistics();
    },
    // 查询、统计
    async statistics() {
      let [beginDate, endDate] = this.searchInfo.date;
      let data = {
        beginDate: dataUtils.dateToString(beginDate),
        endDate: dataUtils.dateToString(endDate),
        personGroup: this.searchInfo.personGroup,
        companyCode: this.searchInfo.companyCode[0]
          ? this.searchInfo.companyCode[0]
          : '',
        itemClsCode: this.searchInfo.itemClsCode,
        doctorName: this.searchInfo.doctorName,
        deptCode: this.searchInfo.deptCode
      };
      await this.GetBasicStatistical(data);
      await this.GetRegistrarWorkload(data);
      await this.ActivateWorkloadReport(data);
      await this.GetApplicantWorkload(data);
      await this.GetDeptWorkload(data);
      await this.GetDoctorWorkload();
      await this.InputDoctorWorkloadReport();
      this.loading = false;
    },
    // 单选框切换
    async radioChange() {
      if (this.searchInfo.personGroup === 1) {
        this.searchInfo.companyCode = '';
      }
      await this.statistics();
    },
    // 基础信息
    GetBasicStatistical(data) {
      this.$ajax.post(this.$apiUrls.GetBasicStatistical, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.$nextTick(() => {
          this.basicData = returnData;
          this.$refs.workloadPie_ref.initChart();
        });
      });
    },
    // 登记员工作量
    GetRegistrarWorkload(data) {
      this.$ajax.post(this.$apiUrls.GetRegistrarWorkload, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        // 处理个人数据
        let personData = returnData.companyPatients.filter(
          (item) => !item.companyName
        );
        let titleList = [];
        let obj = {};
        let person = [];
        personData.map((item) => {
          titleList = item.patients.map((i) => {
            return i.registrar;
          });
          obj = titleList.reduce((prev, next) => {
            prev[next] = prev[next] + 1 || 1;
            return prev;
          }, {});
          for (let i in obj) {
            let newObj = {
              registrar: i,
              personNum: obj[i]
            };
            person.push(newObj);
          }
        });
        // 处理团体数据
        let companyData = returnData.companyPatients.filter(
          (item) => item.companyName
        );
        let company = [];
        let newArr = [];
        companyData.map((item) => {
          item.patients.map((i) => {
            newArr.push(i);
          });
        });
        titleList = newArr.map((item) => {
          return item.registrar;
        });
        obj = titleList.reduce((prev, next) => {
          prev[next] = prev[next] + 1 || 1;
          return prev;
        }, {});
        for (let i in obj) {
          let newObj = {
            registrar: i,
            companyNum: obj[i]
          };
          company.push(newObj);
        }
        let registrarData = [];
        registrarData = person.map((obj) => {
          const index = company.findIndex(
            (el) => el['registrar'] == obj['registrar']
          );
          const { companyNum } =
            index !== -1
              ? company[index]
              : {
                  ...obj,
                  companyNum: 0
                };
          return {
            ...obj,
            companyNum
          };
        });
        // 转成小数
        registrarData.map((item) => {
          item.personNum = item.personNum / 100;
          item.companyNum = item.companyNum / 100;
        });
        this.$nextTick(() => {
          this.middleData.registrarData = registrarData;
        });
      });
    },
    // 激活工作量
    ActivateWorkloadReport(data) {
      this.$ajax.post(this.$apiUrls.ActivateWorkloadReport, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.$nextTick(() => {
          this.middleData.activeTotal =
            returnData.populationStatistics.totalNum;
          this.middleData.activeXData = returnData.report.map((x) => {
            return this.G_EnumList['SysOperator'][x.operatorCode];
          });
          this.middleData.activeYData = returnData.report.map((y) => {
            return y.totalNum;
          });
          this.$refs.middleStatistics_ref.initChart();
        });
      });
    },
    // 开单医生工作量
    GetApplicantWorkload(data) {
      this.$ajax.post(this.$apiUrls.GetApplicantWorkload, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.$nextTick(() => {
          this.middleData.billingTotal =
            returnData.applicantPersonTimes.pop().personTime;
          let newData = returnData.applicantPersonTimes;
          this.middleData.billingXData = newData.map((x) => {
            return x.applicant;
          });
          this.middleData.billingYData = newData.map((y) => {
            return y.personTime;
          });
          this.$refs.middleStatistics_ref.initChart();
        });
      });
    },
    // 科室工作量
    GetDeptWorkload(data) {
      this.$ajax.post(this.$apiUrls.GetDeptWorkload, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.$nextTick(() => {
          this.departData.xData = returnData.deptCombs.map((x) => {
            return x.deptName;
          });
          returnData.deptCombs.map((y) => {
            let obj = {};
            obj = y.combStatisticses.pop();
            this.departData.yPeopleData.push(obj.personTime);
            this.departData.yPriceData.push(obj.amount);
          });
          this.$refs.department_ref.initChart();
        });
      });
    },
    // 医生工作量
    GetDoctorWorkload() {
      let [beginDate, endDate] = this.searchInfo.date;
      let data = {
        beginDate: dataUtils.dateToString(beginDate),
        endDate: dataUtils.dateToString(endDate),
        personGroup: this.searchInfo.personGroup,
        companyCode: this.searchInfo.companyCode[0]
          ? this.searchInfo.companyCode[0]
          : '',
        itemClsCode: this.$refs.right_ref.searchInfo.itemClsCode,
        doctorName: this.$refs.right_ref.searchInfo.doctorName,
        deptCode: this.$refs.right_ref.searchInfo.deptCode
      };
      this.rightData.xData = [];
      this.rightData.yPeopleData = [];
      this.rightData.yPriceData = [];
      this.$ajax.post(this.$apiUrls.GetDoctorWorkload, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.$nextTick(() => {
          this.rightData.totalPeople = returnData.personTime;
          this.rightData.totalPrice = returnData.amount;
          if (this.$refs.right_ref.searchInfo.doctorName) {
            let newArr = [];
            returnData.doctorCombs.map((y) => {
              newArr = y.combStatisticses.slice(0, -1);
              console.log('newArr: ', newArr);
              newArr.map((item) => {
                this.rightData.xData.push(item.combName);
                this.rightData.yPeopleData.push(item.personTime);
                this.rightData.yPriceData.push(item.amount);
              });
            });
            if (this.rightData.yPeopleData.length > 10) {
              this.$refs.right_ref.barWidth = '';
            } else {
              this.$refs.right_ref.barWidth = 10;
            }
          } else {
            this.rightData.xData = returnData.doctorCombs.map((y) => {
              return y.doctorName;
            });
            returnData.doctorCombs.map((y) => {
              let obj = {};
              obj = y.combStatisticses.pop();
              this.rightData.yPeopleData.push(obj.personTime);
              this.rightData.yPriceData.push(obj.amount);
            });
            this.$refs.right_ref.barWidth = 10;
          }
          this.$refs.right_ref.initChart();
        });
      });
    },
    // 录入员工作量
    InputDoctorWorkloadReport() {
      let [beginDate, endDate] = this.searchInfo.date;
      let data = {
        beginDate: dataUtils.dateToString(beginDate),
        endDate: dataUtils.dateToString(endDate),
        personGroup: this.searchInfo.personGroup,
        companyCode: this.searchInfo.companyCode[0]
          ? this.searchInfo.companyCode[0]
          : '',
        itemClsCode: this.searchInfo.itemClsCode,
        doctorName: this.searchInfo.doctorName,
        deptCode: this.searchInfo.deptCode
      };
      this.rightData.enterData = [];
      this.$ajax
        .post(this.$apiUrls.InputDoctorWorkloadReport, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$nextTick(() => {
            this.rightData.enterData = returnData.report;
          });
        });
    },
    // 主检工作量
    CheckDoctorWorkloadReport() {
      let [beginDate, endDate] = this.searchInfo.date;
      let data = {
        beginDate: dataUtils.dateToString(beginDate),
        endDate: dataUtils.dateToString(endDate),
        personGroup: this.searchInfo.personGroup,
        companyCode: this.searchInfo.companyCode[0]
          ? this.searchInfo.companyCode[0]
          : '',
        itemClsCode: this.searchInfo.itemClsCode,
        doctorName: this.searchInfo.doctorName,
        deptCode: this.searchInfo.deptCode
      };
      this.rightData.enterData = [];
      this.$ajax
        .post(this.$apiUrls.CheckDoctorWorkloadReport, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$nextTick(() => {
            this.rightData.enterData = returnData.dept;
          });
        });
    },
    // 审核工作量
    AuditDoctorWorkloadReport() {
      let [beginDate, endDate] = this.searchInfo.date;
      let data = {
        beginDate: dataUtils.dateToString(beginDate),
        endDate: dataUtils.dateToString(endDate),
        personGroup: this.searchInfo.personGroup,
        companyCode: this.searchInfo.companyCode[0]
          ? this.searchInfo.companyCode[0]
          : '',
        itemClsCode: this.searchInfo.itemClsCode,
        doctorName: this.searchInfo.doctorName,
        deptCode: this.searchInfo.deptCode
      };
      this.rightData.enterData = [];
      this.$ajax
        .post(this.$apiUrls.AuditDoctorWorkloadReport, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$nextTick(() => {
            this.rightData.enterData = returnData.dept;
          });
        });
    }
  }
};
</script>

<style lang="less" scoped>
.workloadStatistics {
  display: flex;
  flex-direction: column;
  color: #2d3436;
  overflow: auto;
  .header-wrap {
    background: #fff;
    padding: 18px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 4px;
    margin-bottom: 20px;
    h3 {
      font-size: 18px;
    }
  }
  .search-list {
    display: flex;
    align-items: center;
  }
  .list-item {
    display: flex;
    align-items: center;
    margin-right: 10px;
    span {
      font-size: 14px;
      font-weight: 600;
      margin-right: 10px;
    }
  }
  .input {
    width: 100%;
  }
  .btn {
    padding: 6.5px 10px;
  }
  .main {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    display: flex;
    flex-direction: column;
    // flex-direction: row;
  }
}
</style>
