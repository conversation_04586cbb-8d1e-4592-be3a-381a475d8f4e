<template>
  <div class="greffiertWorkload" id="greffiertWorkload">
    <div class="greffiertWorkload-wrap">
      <div class="header">
        <el-form :model="searchForm" label-width="60px">
          <el-form-item
            label="统计时间"
            prop="statisticalTime"
            label-width="70px"
          >
            <el-date-picker
              :picker-options="{ shortcuts: G_datePickerShortcuts }"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              v-model="statisticalTime"
              @change="search"
              :clearable="false"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="" prop="" class="radioForm">
            <div class="titleRadio">
              <el-radio-group
                v-model.trim="searchForm.personGroup"
                @change="radioChange"
              >
                <el-radio :label="0">所有</el-radio>
                <el-radio :label="1">个人</el-radio>
                <el-radio :label="2">团体</el-radio>
              </el-radio-group>
            </div>
          </el-form-item>
          <el-form-item label="单位" prop="companyCode">
            <el-cascader
              ref="company_cascader_ref"
              v-model="searchForm.companyCode"
              :options="companyList"
              :filter-method="filterMethod"
              :props="{ multiple: false }"
              clearable
              filterable
              size="small"
              collapse-tags
              @change="companyChange"
            >
            </el-cascader>
          </el-form-item>
        </el-form>
        <ButtonGroup
          :btnList="['统计', '导出']"
          @statistics="search"
        ></ButtonGroup>
      </div>
      <div id="printDiv">
        <div class="titlewrap">
          <p class="titleP">登记员工作量报表:</p>
          <p class="contP">
            <span class="otherSpsn"
              >登记总人数：{{
                allList.registerPopulationStatistics.totalNum
              }}</span
            >
            <span class="sexSpan"
              >男：{{ allList.registerPopulationStatistics.maleNum }}</span
            >
            <span class="sexSpan"
              >女：{{ allList.registerPopulationStatistics.femaleNum }}</span
            >
            <span class="otherSpsn"
              >个人：{{ allList.registerPopulationStatistics.personNum }}</span
            >
            <span class="otherSpsn"
              >团体：{{ allList.registerPopulationStatistics.companyNum }}</span
            >
            <span class="otherSpsn"
              >其中个体免费：{{
                allList.registerPopulationStatistics.personFreeNum
              }}</span
            >
          </p>
        </div>
        <div class="contDiv" id="contDiv">
          <div class="tableDiv">
            <div v-for="(item, index) in allList.companyPatients" :key="index">
              <p class="titleP">
                体检单位:{{ item.companyName ? item.companyName : '无单位' }}
              </p>
              <PublicTable
                :isSortShow="false"
                :viewTableList.sync="item.patients"
                :theads.sync="theads"
                :columnWidth="columnWidth"
              >
                <template #sex="{ scope }">
                  <div>
                    {{ G_EnumList['Sex'][scope.row.sex] }}
                  </div>
                </template>
                <template #age="{ scope }">
                  <div>
                    {{ scope.row.age + enum_ageUnit[scope.row.ageUnit] }}
                  </div>
                </template>
              </PublicTable>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import PublicTable from '@/components/publicTable.vue';
import ButtonGroup from './components/buttonGroup.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
export default {
  name: 'greffiertWorkload',
  components: { PublicTable, ButtonGroup },
  data() {
    return {
      searchForm: {
        beginDate: '',
        endDate: '',
        personGroup: 0,
        companyCode: '',
        itemClsCode: '',
        doctorName: '',
        deptCode: ''
      },
      statisticalTime: [dataUtils.getDate(), dataUtils.getDate()],
      companyList: [],
      columnWidth: {
        name: 100,
        sex: 80,
        age: 80
      },
      enum_ageUnit: {
        null: '',
        0: '岁',
        1: '月'
      },
      theads: {
        regNo: '体检号',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        cardNo: '身份证',
        registrar: '登记人',
        registertime: '登记时间'
      },
      allList: {
        registerPopulationStatistics: {
          totalNum: 0,
          maleNum: 0,
          femaleNum: 0,
          personNum: 0,
          companyNum: 0,
          personFreeNum: 0
        },
        companyPatients: [
          // {
          //   companyName: "string",
          //   patients: [
          //     {
          //       regNo: "string",
          //       name: "string",
          //       sex: 0,
          //       age: 0,
          //       ageUnit: 0,
          //       cardNo: "string",
          //       registrar: "string",
          //       registertime: "2022-12-22T07:49:45.073Z",
          //     },
          //   ],
          // },
        ]
      }
    };
  },
  mounted() {
    this.getCompany();
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_sexList', 'G_datePickerShortcuts'])
  },
  methods: {
    //统计查询
    search() {
      if (!this.statisticalTime) {
        this.searchForm.beginDate = '';
        this.searchForm.endDate = '';
      } else {
        this.searchForm.beginDate = this.statisticalTime[0];
        this.searchForm.endDate = this.statisticalTime[1];
      }
      let { companyCode, ...surplus } = this.searchForm;
      let parameter = {
        companyCode: companyCode[0],
        ...surplus
      };
      this.$ajax
        .post(this.$apiUrls.GetRegistrarWorkload, parameter)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          if (
            returnData.companyPatients.length === 0 &&
            returnData.registerPopulationStatistics === null
          ) {
            this.allList = {
              registerPopulationStatistics: {
                totalNum: 0,
                maleNum: 0,
                femaleNum: 0,
                personNum: 0,
                companyNum: 0,
                personFreeNum: 0
              },
              companyPatients: []
            };
            return;
          }
          this.allList = returnData;
          if (this.allList?.companyPatients.length >= 1) {
            this.$message({
              message: '成功!',
              type: 'success',
              showClose: true
            });
          } else {
            this.$message({
              message: '暂无数据!',
              type: 'success',
              showClose: true
            });
          }
        });
    },
    //获取单位下拉
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) return;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.statisticalTime = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.search();
    },
    // 单选框切换
    radioChange() {
      if (this.searchForm.personGroup === 1) {
        this.searchForm.companyCode = '';
      }
      this.search();
    }
  }
};
</script>
<style lang="less" scoped>
.greffiertWorkload {
  .greffiertWorkload-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 15px !important;
    font-family: PingFangSC-Semibold;
    font-size: 14px;
    color: #2d3436;
    background: #fff;
  }
  .header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    height: 40px;
    line-height: 40px;
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #2d3436;
    .el-form {
      display: flex;
      flex-direction: row;
      margin-right: 10px;
    }
    .el-form-item {
      margin-bottom: 0;
    }
    .radioForm {
      /deep/.el-form-item__content {
        margin-left: 20px !important;
        .titleRadio {
          width: 210px;
          .el-radio {
            margin-right: 16px;
          }
        }
      }
    }
  }
  #printDiv {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    .titlewrap {
      height: 60px;
      line-height: 60px;
      display: flex;
      flex-direction: row;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #2d3436;
      .titleP {
        font-family: PingFangSC-Medium;
        font-size: 18px;
        width: 224px;
        font-weight: 700;
      }
      .contP {
        flex: 1;
        display: flex;
        flex-direction: row;
        .otherSpsn {
          flex: 1;
        }
        .sexSpan {
          width: 110px;
        }
      }
    }
    .contDiv {
      flex: 1;
      overflow: auto;
      display: flex;
      flex-direction: column;
      .tableDiv {
        flex: 1;
        overflow: auto;
      }
      .titleP {
        font-family: PingFangSC-Regular;
        color: #1770df;
        line-height: 32px;
        font-weight: 700;
      }

      /deep/.el-table__footer-wrapper tbody td.el-table__cell,
      .el-table__header-wrapper tbody td.el-table__cell {
        font-family: PingFangSC-Regular;
        color: #1770df !important;
      }
    }
    /deep/.el-table--scrollable-y .el-table__body-wrapper {
      height: auto !important;
    }
    /deep/.el-table__body-wrapper {
      height: auto !important;
    }
    .publicTable_com {
      border: 1px solid #ccc;
      border-radius: 4px;
    }
    .spanDiv {
      width: 100%;
      display: flex;
      height: 38px;
      line-height: 38px;
      border-bottom: 1px solid #ccc;
      padding: 0 20px;
      & > span {
        flex: 1;
      }
    }
    .footerr {
      background: #cbddf5;
      padding-right: 30px;
      font-weight: 700;
    }
  }
}
</style>
