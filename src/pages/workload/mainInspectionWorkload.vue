<template>
  <!-- 主检员工作量报表 -->
  <div class="mainInspectionWorkload">
    <div class="main">
      <div class="searchBar">
        <el-form ref="searchForm" :inline="true" :model="searchInfo">
          <el-row justify="space-between" type="flex">
            <el-col :span="24">
              <el-form-item label="统计时间">
                <el-date-picker
                  :picker-options="{ shortcuts: G_datePickerShortcuts }"
                  type="daterange"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  size="small"
                  v-model="searchInfo.date"
                  @change="search"
                  :clearable="false"
                >
                </el-date-picker>
              </el-form-item>

              <el-form-item>
                <el-radio-group
                  v-model="searchInfo.personGroup"
                  @change="radioChange"
                >
                  <el-radio :label="0">所有</el-radio>
                  <el-radio :label="1">个人</el-radio>
                  <el-radio :label="2">团体</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="单位" label-width="60px">
                <el-cascader
                  ref="company_cascader_ref"
                  v-model="searchInfo.companyCode"
                  :filter-method="filterMethod"
                  :disabled="isPersonal"
                  :options="companyList"
                  :props="{ multiple: false }"
                  clearable
                  filterable
                  size="small"
                  collapse-tags
                  @change="companyChange"
                >
                </el-cascader>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row justify="space-between" type="flex">
            <el-col :span="24">
              <el-form-item>
                <el-radio-group v-model.trim="radioGroup" @change="search">
                  <el-radio :label="0">明细</el-radio>
                  <el-radio :label="1">汇总</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="主检医生" label-width="80px">
                <el-select
                  placeholder="请选择"
                  size="small"
                  filterable
                  clearable
                  v-model="searchInfo.doctorName"
                  @change="search"
                >
                  <el-option
                    v-for="item in G_sysOperator"
                    :key="item.label"
                    :label="item.label"
                    :value="item.label"
                  >
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item>
                <el-button
                  @click="search"
                  class="blue_btn btn"
                  size="small"
                  icon="iconfont icon-tongji"
                  >统计</el-button
                >
                <el-button
                  @click="exports"
                  size="small"
                  class="yellow_btn btn"
                  icon="iconfont icon-daochu"
                  >导出</el-button
                >
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="main-title">
        <h3>{{ headTitle }}:</h3>
      </div>

      <div class="main-table1" v-if="radioGroup == 0">
        <PublicTable
          :isSortShow="false"
          :theads="theads1"
          :viewTableList.sync="tableData1"
          :columnWidth="columnWidth"
          :tableLoading="tableLoading"
        >
          <template #summary="{ scope }">
            <el-tooltip
              placement="top"
              effect="light"
              popper-class="tooltip-width"
            >
              <template slot="content">
                <p style="max-width: 70vw">{{ scope.row.summary }}</p>
              </template>
              <span class="text-ellipsis">{{ scope.row.summary }}</span>
            </el-tooltip>
          </template>

          <template #suggestion="{ scope }">
            <el-tooltip
              placement="top"
              effect="light"
              popper-class="tooltip-width"
            >
              <template slot="content">
                <p style="max-width: 70vw">{{ scope.row.suggestion }}</p>
              </template>
              <span class="text-ellipsis">{{ scope.row.suggestion }}</span>
            </el-tooltip>
          </template>
        </PublicTable>
      </div>
      <div class="main-table" v-if="radioGroup == 1">
        <div class="table-list" v-loading="tableLoading">
          <template v-if="report && report.length > 0">
            <div
              class="table-item"
              v-for="(item, index) in report"
              :key="index"
            >
              <div class="cell_blue item-text">{{ item.deptName }}</div>
              <div class="publicTable">
                <PublicTable
                  :isSortShow="false"
                  :theads="theads2"
                  :viewTableList.sync="item.doctors"
                ></PublicTable>
              </div>
            </div>
          </template>

          <template v-else>
            <el-empty description="暂无记录"></el-empty>
          </template>
        </div>
        <div class="total">
          <span>总计：</span>
          <span>{{ total }}人</span>
          <span>{{ totalPrice }}元</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
const ExcelJS = require('exceljs');
import FileSaver from 'file-saver';
import SearchBar from './components/searchBar.vue';
import PublicTable from '@/components/publicTable.vue';
import { dataUtils } from '@/common';
import { export2Excel } from '@/common/excelUtil';
import moment from 'moment';
import { mapGetters } from 'vuex';

export default {
  name: 'mainInspectionWorkload',
  components: {
    SearchBar,
    PublicTable
  },
  data() {
    return {
      radioGroup: 0,
      searchInfo: {
        date: [new Date(), new Date()],
        personGroup: 0,
        companyCode: '',
        doctorName: ''
      },
      isPersonal: false,
      companyList: [],
      theads1: {
        name: '姓名',
        regNo: '体检号',
        checkTime: '日期',
        summary: '结论',
        suggestion: '建议',
        checkDoctorName: '主检医生',
        companyCode: '单位代码', //
        auditDoctorName: '审核医生'
      },
      tableData1: [],
      columnWidth: {
        name: 70,
        regNo: 120,
        checkTime: 140,
        checkDoctorName: 100,
        companyCode: 80, //
        auditDoctorName: 100
      },
      theads2: {
        doctorName: '检查医生',
        // deptName: "科室名称",
        totalNum: '人次',
        price: '金额'
      },
      tableData2: [],
      report: [],
      total: 0,
      totalPrice: 0,
      headTitle: '主检工作量报表明细报表',
      excelData: [],
      tableLoading: false
    };
  },
  created() {
    this.getCompanyList();
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_sysOperator', 'G_datePickerShortcuts'])
  },
  methods: {
    search() {
      if (this.tableLoading) return;
      if (this.radioGroup == 0) {
        this.headTitle = '主检工作量报表明细报表';
        this.CheckDoctorWorkloadDetailReport(); //明细表
      } else {
        this.headTitle = '主检工作量报表汇总报表';
        this.statistics(); //汇总表
      }
    },
    //明细
    CheckDoctorWorkloadDetailReport() {
      let [beginDate, endDate] = this.searchInfo.date;
      let data = {
        beginDate: dataUtils.dateToString(beginDate),
        endDate: dataUtils.dateToString(endDate),
        personGroup: this.searchInfo.personGroup,
        companyCode: this.searchInfo.companyCode[0]
          ? this.searchInfo.companyCode[0]
          : '',
        itemClsCode: '',
        doctorName: this.searchInfo.doctorName,
        deptCode: ''
      };
      this.tableLoading = true;
      this.$ajax
        .post(this.$apiUrls.CheckDoctorWorkloadDetailReport, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.tableData1 = returnData || [];
          this.excelData = dataUtils.deepCopy(returnData) || [];
        })
        .finally((_) => {
          this.tableLoading = false;
        });
    },
    // 汇总
    statistics() {
      let [beginDate, endDate] = this.searchInfo.date;
      let data = {
        beginDate: dataUtils.dateToString(beginDate),
        endDate: dataUtils.dateToString(endDate),
        personGroup: this.searchInfo.personGroup,
        companyCode: this.searchInfo.companyCode[0]
          ? this.searchInfo.companyCode[0]
          : '',
        itemClsCode: '',
        doctorName: this.searchInfo.doctorName,
        deptCode: ''
      };
      this.tableLoading = true;
      this.$ajax
        .post(this.$apiUrls.CheckDoctorWorkloadReport, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.report = returnData.dept.map((item) => {
            item.doctors.push({
              doctorName: '合计',
              deptName: null,
              totalNum: item.totalNum,
              price: item.totalPrice
            });
            return {
              ...item
            };
          });
          this.excelData = dataUtils.deepCopy(this.report) || [];
          this.excelData[this.excelData.length - 1].doctors.push(
            {
              doctorName: '',
              totalNum: '',
              price: ''
            },
            {
              doctorName: '总计',
              totalNum: returnData.totalNum + '人次',
              price: returnData.totalPrice + '元'
            }
          );
          this.total = returnData.totalNum;
          this.totalPrice = returnData.totalPrice;
        })
        .finally((_) => {
          this.tableLoading = false;
        });
    },
    // 单选框切换
    radioChange() {
      this.isPersonal = false;
      if (this.searchInfo.personGroup === 1) {
        this.searchInfo.companyCode = '';
        this.isPersonal = true;
        this.search();
      } else {
        this.isPersonal = false;
        this.search();
      }
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    // 获取单位下拉
    getCompanyList() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) return;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.searchInfo.date = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.search();
    },
    //导出
    exports() {
      if (this.excelData.length < 1) {
        this.$message({
          message: '没有可导出的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.radioGroup === 0) {
        this.exportTable();
      } else if (this.radioGroup === 1) {
        this.exportMoreTable();
      } else {
        this.$message({
          message: '请先选择明细或汇总数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
    },
    //明细excel
    exportTable() {
      this.$confirm('确定导出列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let columns = [];
        let theads = { index: '序号', ...this.theads1 };
        Object.keys(theads).map((item) => {
          columns.push({
            title: theads[item],
            key: item
          });
        });
        let data = [];
        data = dataUtils.deepCopy(this.excelData);
        data.map((item, i) => {
          item.index = i + 1;
        });
        let companyName = this.$refs.selectLabel.selected.label || '';
        const title =
          companyName + this.headTitle + moment().format('YYYY-MM-DD');
        this.$nextTick(() => {
          export2Excel(columns, data, title);
        });
      });
    },
    //单sheet页多表格导出Excel
    exportMoreTable() {
      this.$confirm('确定导出列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let workbook = new ExcelJS.Workbook();
        workbook.creator = '主检工作量报表汇总';
        workbook.lastModifiedBy = '主检工作量报表汇总';
        workbook.created = new Date();
        workbook.modified = new Date();
        workbook.properties.date1904 = true;
        const worksheet = workbook.addWorksheet('主检工作量报表汇总');
        worksheet.properties.defaultColWidth = 18;
        worksheet.properties.defaultRowHeight = 32;
        // 初始化样式
        const headerCellStyle = {
          font: { name: '宋体', family: 4, size: 14, bold: true },
          alignment: { vertical: 'middle', horizontal: 'center' }
        };
        const tableHeaderCellStyle = {
          font: { bold: true },
          fill: {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '95caf9' }
          },
          alignment: { vertical: 'middle', horizontal: 'center' }
        };
        let currentRowIndex = 1; // 当前行索引
        this.excelData.forEach((item, index) => {
          // 设置标题行
          const titleRow = worksheet.addRow({});
          titleRow.height = 50;
          const titleCell = titleRow.getCell(1);
          titleCell.value = ` 科室名称:${item.deptName}`;
          titleCell.style = headerCellStyle;
          worksheet.mergeCells(`A${currentRowIndex}:C${currentRowIndex}`);
          // 设置表头行
          const headerRow = worksheet.addRow({});

          const headerColumns = [
            { value: '检查医生', style: tableHeaderCellStyle },
            { value: '人次', style: tableHeaderCellStyle },
            { value: '金额', style: tableHeaderCellStyle }
          ];
          headerColumns.forEach(({ value, style }, colIndex) => {
            const cell = headerRow.getCell(colIndex + 1);
            cell.value = value;
            cell.style = style;
          });
          // 添加表格数据
          if (item.doctors.length > 0) {
            item.doctors.forEach((rowItems) => {
              const dataRow = worksheet.addRow([
                rowItems.doctorName,
                rowItems.totalNum,
                rowItems.price
              ]);
              dataRow.eachCell(
                (cell) =>
                  (cell.style = {
                    font: { name: '宋体' },
                    alignment: { vertical: 'middle', horizontal: 'center' }
                  })
              );
            });
          }
          // 跳过已添加的数据行，并为下一个表格预留一行空白
          currentRowIndex += item.doctors.length + 2;
        });
        let companyName = this.$refs.selectLabel.selected.label || '';
        this.$nextTick(() => {
          workbook.xlsx
            .writeBuffer()
            .then((buffer) => {
              FileSaver.saveAs(
                new Blob([buffer], {
                  type: 'application/octet-stream'
                }),
                `${companyName}主检工作量报表汇总报表.xlsx`
              );
            })
            .catch((err) => console.error('写入Excel文件时发生错误:', err));
        });
      });
    }
  }
};
</script>

<style lang="less" scoped>
.mainInspectionWorkload {
  color: #2d3436;
  .main {
    height: 100%;
    background: #fff;
    padding: 18px 10px;
    display: flex;
    flex-direction: column;
  }
  .searchBar {
    /deep/.el-form-item {
      margin-bottom: 5px;

      .el-form-item__label {
        color: #2d3436;
        font-weight: bold;
      }
    }
    .input {
      width: 100%;
    }
    .btn {
      padding: 6.5px 10px;
    }
  }
  .main-title {
    margin-bottom: 18px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    h3 {
      font-size: 18px;
    }
  }
  .title-wrap {
    display: flex;
    align-items: center;
    font-size: 14px;
    span {
      width: 180px;
    }
  }
  .main-table {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
  }
  .main-table1 {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    .el-table {
      display: flex;
      flex-direction: column;
    }
  }
  /deep/.el-table__body-wrapper {
    flex: 1;
    overflow: auto;
  }
  .table-list {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .table-item {
    margin-bottom: 18px;
  }
  .item-text {
    margin-bottom: 10px;
    font-size: 14px;
  }
  .publicTable {
    border: 1px solid rgba(178, 190, 195, 0.5);
    border-radius: 4px;
    overflow: auto;
  }
  .total {
    background: #cbddf5;
    font-size: 14px;
    font-weight: 600;
    padding: 10px;
    display: flex;
    align-items: center;
    border-radius: 4px;
    span {
      flex: 1;
    }
  }
  /deep/.main-table .el-table--scrollable-y .el-table__body-wrapper {
    height: auto !important;
  }
  /deep/.main-table .el-table__body-wrapper {
    height: auto !important;
  }

  .text-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 1;
    display: -webkit-box;
    -webkit-box-orient: vertical;
  }
}
</style>
