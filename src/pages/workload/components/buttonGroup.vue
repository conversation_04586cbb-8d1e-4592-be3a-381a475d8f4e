<template>
  <div class="btn_group">
    <el-button
      v-if="btnList.includes('统计')"
      @click="statistics"
      class="blue_btn btn"
      size="small"
      icon="iconfont icon-tongji"
      >统计</el-button
    >
    <el-button
      v-if="btnList.includes('打印')"
      @click="prints"
      size="small"
      class="green_btn btn"
      icon="iconfont icon-dayin-"
      >打印</el-button
    >
    <el-button
      v-if="btnList.includes('导出')"
      @click="exports"
      size="small"
      class="yellow_btn btn"
      icon="iconfont icon-daochu"
      >导出</el-button
    >
  </div>
</template>

<script>
export default {
  name: 'btnGroup',
  props: {
    btnList: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  data() {
    return {};
  },
  methods: {
    // 统计
    statistics() {
      this.$emit('statistics');
    },
    // 打印
    prints() {
      this.$emit('prints');
    },
    // 导出
    exports() {
      this.$emit('exports');
    }
  }
};
</script>
<style lang="less" scoped>
.btn_group {
  display: flex;
  align-items: center;
}
.btn {
  padding: 6.5px 10px;
}
</style>
