<template>
  <div class="workloadPie">
    <el-skeleton :loading="loading" animated :count="3">
      <template slot="template">
        <div class="pie-item">
          <el-skeleton-item
            variant="h3"
            style="width: 140px; margin-bottom: 10px"
          />
          <el-skeleton-item
            variant="image"
            style="width: 180px; height: 180px; margin: 0 auto"
          />
        </div>
      </template>
      <template>
        <div>
          <div class="pie-wrap">
            <div class="pie-item">
              <span>体检男女比例：</span>
              <div class="pie-text">
                <div id="sexRatio" class="sexRatio-chart"></div>
                <div>
                  <div class="male">
                    <span class="iconfont icon-nan icon-text"> 男</span>
                    <span>{{ menAndWomenRatio.men }}人</span>
                  </div>
                  <div class="female">
                    <span class="iconfont icon-nv icon-text"> 女</span>
                    <span>{{ menAndWomenRatio.women }}人</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="pie-item">
              <span>个人/团体分类：</span>
              <div class="chart">
                <div id="classify" class="classify-chart"></div>
              </div>
            </div>
            <div class="pie-item">
              <span>个人/团体已发报告数：</span>
              <div class="chart">
                <div id="report" class="classify-chart"></div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </el-skeleton>
  </div>
</template>

<script>
import * as echarts from 'echarts';
require('echarts/theme/macarons');
export default {
  name: 'workloadPie',
  props: {
    menAndWomenRatio: {
      type: Object,
      default: {}
    },
    personCompanyCount: {
      type: Object,
      default: {}
    },
    reportNum: {
      type: Object,
      default: {}
    },
    loading: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    // 初始化图表
    initChart() {
      this.$nextTick(() => {
        this.drawChartSexRatio();
        this.drawChartClassify();
        this.drawChartReport();
      });
    },
    // 男女比例统计图
    drawChartSexRatio() {
      let myChart = echarts.init(document.getElementById('sexRatio'));
      myChart.setOption({
        tooltip: {
          trigger: 'item'
        },
        color: ['#4A95F4', '#FF8384'],
        series: [
          {
            name: '体检男女比例',
            type: 'pie',
            radius: 70,
            label: {
              position: 'inner',
              fontSize: 14,
              color: '#fff',
              formatter: '{a|{b}}{a|\n{d}%}',
              rich: {
                a: {
                  fontSize: 14,
                  lineHeight: 20
                }
              }
            },
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 4
            },
            data: [
              { value: this.menAndWomenRatio.men, name: '男' },
              { value: this.menAndWomenRatio.women, name: '女' }
            ],
            labelLine: {
              show: false
            }
          }
        ]
      });
      // 浏览器大小调整echarts自适应
      window.addEventListener('resize', () => {
        if (myChart) {
          myChart.resize();
        }
      });
    },
    // 个人/团体分类统计图
    drawChartClassify() {
      let myChart = echarts.init(document.getElementById('classify'));
      myChart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        series: [
          {
            name: '个人/团体分类',
            type: 'pie',
            // selectedMode: "single",
            radius: [0, 40],
            color: ['#4A95F4', '#FAD73B'],
            label: {
              position: 'inner',
              fontSize: 14,
              color: '#fff'
            },
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 4
            },
            labelLine: {
              show: false
            },
            data: [
              { value: this.personCompanyCount.companyCount, name: '团体数' },
              { value: this.personCompanyCount.personCount, name: '个人数' }
            ]
          },
          {
            name: '个人/团体分类',
            type: 'pie',
            radius: [50, 70],
            color: ['#1770DF', '#FAB63B', '#FF8384'],

            // labelLine: {
            //   length: 5,
            //   length2: 0,
            //   maxSurfaceAngle: 100
            // },
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 4
            },
            label: {
              fontSize: 14,
              formatter: '{b}\n{c} 人',
              minMargin: 5,
              edgeDistance: 10,
              lineHeight: 20,
              color: 'inherit'
            },
            labelLayout: function (params) {
              const isLeft = params.labelRect.x < myChart.getWidth() / 2;
              const points = params.labelLinePoints;
              // Update the end point.
              points[2][0] = isLeft
                ? params.labelRect.x
                : params.labelRect.x + params.labelRect.width;
              return {
                labelLinePoints: points
              };
            },
            data: [
              {
                value: this.personCompanyCount.companyCount,
                name: '团体数'
              },
              {
                value: this.personCompanyCount.personCount,
                name: '个人数'
              }
              // { value: this.personCompanyPay.personFreeNum, name: "个人免费" }
            ]
          }
        ]
      });
      // 浏览器大小调整echarts自适应
      window.addEventListener('resize', () => {
        if (myChart) {
          myChart.resize();
        }
      });
    },
    // 个人/团体已发报告数统计图
    drawChartReport() {
      let company =
        this.reportNum.compnayNoReport + this.reportNum.compnayReport;
      let person = this.reportNum.personNoReport + this.reportNum.personReport;
      let myChart = echarts.init(document.getElementById('report'));
      myChart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        series: [
          {
            name: '个人/团体已发报告数',
            type: 'pie',
            radius: [0, 40],
            color: ['#3CB34F', '#FAB63B'],
            label: {
              position: 'inner',
              fontSize: 14,
              color: '#fff'
            },
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 4
            },
            labelLine: {
              show: false
            },
            data: [
              { value: company, name: '团体', selected: true },
              { value: person, name: '个人' }
            ]
          },
          {
            name: '个人/团体已发报告数',
            type: 'pie',
            radius: [50, 70],
            color: ['#3CB34F', '#FAB63B', '#9ED9A7', '#FDDB9D'],

            // labelLine: {
            //   length: 5,
            //   length2: 0,
            //   maxSurfaceAngle: 100
            // },
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 4
            },
            label: {
              fontSize: 14,
              formatter: '{b}\n{c} 人',
              minMargin: 5,
              edgeDistance: 10,
              lineHeight: 20,
              color: 'inherit'
            },
            labelLayout: function (params) {
              const isLeft = params.labelRect.x < myChart.getWidth() / 2;
              const points = params.labelLinePoints;
              // Update the end point.
              points[2][0] = isLeft
                ? params.labelRect.x
                : params.labelRect.x + params.labelRect.width;
              return {
                labelLinePoints: points
              };
            },
            data: [
              { value: this.reportNum.compnayReport, name: '团体已发报告' },
              { value: this.reportNum.personReport, name: '个人已发报告' },
              { value: this.reportNum.compnayNoReport, name: '团体未发报告' },
              { value: this.reportNum.personNoReport, name: '个人未发报告' }
            ]
          }
        ]
      });
      // 浏览器大小调整echarts自适应
      window.addEventListener('resize', () => {
        if (myChart) {
          myChart.resize();
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.workloadPie {
  display: flex;
  flex-direction: column;
  margin-bottom: 10px;
  .pie-wrap {
    display: flex;
    justify-content: space-between;
  }
  .pie-item {
    background: #fff;
    border-radius: 4px;
    padding: 10px;
    // width: 320px;
    height: 240px;
    margin-right: 10px;
    flex: 1;
    &:last-child {
      margin-right: 0;
    }
    span {
      font-size: 14px;
      font-weight: 600;
    }
  }
  .sexRatio-chart {
    width: 180px;
    height: 180px;
  }
  .pie-text {
    display: flex;
    justify-content: center;
    align-items: center;
    span {
      font-weight: normal;
    }
  }
  .male {
    color: #4a95f4;
    font-size: 14px;
    margin-bottom: 20px;
  }
  .female {
    color: #ff8384;
    font-size: 14px;
  }
  .icon-text {
    margin-right: 20px;
  }
  /deep/.el-skeleton {
    display: flex;
  }
  .classify-chart {
    width: 100%;
    height: 200px;
  }
}
</style>
