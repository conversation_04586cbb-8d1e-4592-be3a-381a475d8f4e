<template>
  <div class="departmentStatistics">
    <span class="department-title">科室工作量：</span>
    <el-skeleton :loading="loading" animated :count="3">
      <template slot="template">
        <el-skeleton-item variant="text" style="width: 700px" />
        <el-skeleton-item variant="text" style="width: 900px" />
        <el-skeleton-item variant="text" style="width: 900px" />
        <el-skeleton-item variant="text" style="width: 900px" />
        <el-skeleton-item
          variant="text"
          style="width: 780px; margin-bottom: 10px"
        />
      </template>
      <template>
        <div class="chart">
          <div id="department" class="department-chart"></div>
        </div>
      </template>
    </el-skeleton>
  </div>
</template>

<script>
import * as echarts from 'echarts';
require('echarts/theme/macarons');
export default {
  name: 'departmentStatistics',
  props: {
    loading: {
      type: Boolean,
      default: true
    },
    departData: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    // 初始化图表
    initChart() {
      this.$nextTick(() => {
        this.drawChartDepartment();
      });
    },
    // 科室工作量统计图
    drawChartDepartment() {
      const colors = ['#7364F4', '#FAB63B'];
      let myChart = echarts.init(document.getElementById('department'));
      myChart.setOption({
        color: colors,
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        grid: {
          left: '50',
          bottom: '100',
          top: '60',
          right: '50'
        },
        legend: {
          right: 0,
          data: ['人次', '金额']
        },
        xAxis: {
          type: 'category',
          axisLabel: {
            color: '#2D3436',
            formatter: function (value) {
              //x轴的文字改为竖版显示
              return `{a|${value.split('').join('\n')}}`;
            },
            rich: {
              a: {
                height: 14 // 设置字体行高
              }
            }
          },
          axisTick: {
            alignWithLabel: true,
            //y轴刻度线
            show: false
          },
          // prettier-ignore
          data: this.departData.xData
        },
        yAxis: [
          {
            type: 'value',
            name: '人次',
            position: 'left',
            alignTicks: true,
            axisLine: {
              show: true,
              lineStyle: {
                color: colors[0]
              }
            }
          },
          {
            type: 'value',
            name: '金额/元',
            position: 'right',
            alignTicks: true,
            axisLine: {
              show: true,
              lineStyle: {
                color: colors[1]
              }
            },
            splitLine: {
              // y轴虚线
              show: true,
              lineStyle: {
                type: 'dashed',
                color: '#B2BEC3'
              }
            }
          }
        ],
        series: [
          {
            name: '人次',
            type: 'bar',
            barWidth: 10,
            data: this.departData.yPeopleData
          },
          {
            name: '金额',
            type: 'line',
            smooth: true,
            yAxisIndex: 1,
            data: this.departData.yPriceData
          }
        ]
      });
      // 浏览器大小调整echarts自适应
      window.addEventListener('resize', () => {
        if (myChart) {
          myChart.resize();
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.departmentStatistics {
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
  .department-title {
    font-size: 14px;
    font-weight: 600;
  }
  .department-chart {
    width: 100%;
    height: 330px;
  }
}
</style>
