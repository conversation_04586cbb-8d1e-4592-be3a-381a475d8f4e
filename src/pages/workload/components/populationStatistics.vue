<template>
  <div class="populationStatistics">
    <el-skeleton :loading="loading" animated :count="5">
      <template slot="template">
        <div class="population-item">
          <el-skeleton-item
            class="population"
            variant="h3"
            style="width: 60%; height: 28px; margin-bottom: 22px"
          />
          <el-skeleton-item
            class="title"
            variant="text"
            style="width: 50%; height: 14px"
          />
        </div>
      </template>
      <template>
        <div>
          <div class="item-wrap">
            <div class="population-item">
              <span class="population">{{ basicData.totalNum }}人</span>
              <span class="title">体检总人数</span>
            </div>
            <div class="population-item">
              <span class="population">{{ basicData.recordNum }}人</span>
              <span class="title">已录入总人数</span>
            </div>
            <div class="population-item">
              <span class="population">{{ basicData.checkedNum }}人</span>
              <span class="title">已主检总人数</span>
            </div>
            <div class="population-item">
              <span class="population">{{ basicData.auditedNum }}人</span>
              <span class="title">已审核总人数</span>
            </div>
            <div class="population-item">
              <span class="population">{{ basicData.reportedNum }}人</span>
              <span class="title">已发报告人数</span>
            </div>
          </div>
        </div>
      </template>
    </el-skeleton>
  </div>
</template>

<script>
export default {
  name: 'populationStatistics',
  props: {
    basicData: {
      type: Object,
      default: {}
    },
    loading: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      chart: null
    };
  }
};
</script>

<style lang="less" scoped>
.populationStatistics {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
  .item-wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .population-item {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    border-radius: 4px;
    color: #fff;
    padding: 17px;
    // width: 180px;
    flex: 1;
    margin-right: 20px;
    &:nth-child(1) {
      background-image: linear-gradient(180deg, #1770df 38%, #65a9ff 100%);
    }
    &:nth-child(2) {
      background-image: linear-gradient(180deg, #d63031 37%, #ff8080 100%);
    }
    &:nth-child(3) {
      background-image: linear-gradient(180deg, #fab63b 43%, #ffcd73 100%);
    }
    &:nth-child(4) {
      background-image: linear-gradient(180deg, #7364f4 39%, #a89fff 100%);
    }
    &:nth-child(5) {
      background-image: linear-gradient(180deg, #3cb34f 41%, #7fe08e 100%);
      margin-right: 0;
    }
  }
  .population {
    font-size: 28px;
    margin-bottom: 8px;
  }
  .title {
    font-size: 14px;
  }
  /deep/.el-skeleton {
    display: flex;
  }
}
</style>
