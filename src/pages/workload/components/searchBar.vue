<template>
  <div class="searchBar">
    <div class="search-list">
      <div class="list-item">
        <span style="width: 56px">统计时间</span>
        <el-date-picker
          :picker-options="{ shortcuts: G_datePickerShortcuts }"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          v-model="searchInfo.date"
          @change="dateChange"
          :clearable="false"
        >
        </el-date-picker>
      </div>
      <div style="width: 246px">
        <el-radio-group v-model="searchInfo.personGroup" @change="radioChange">
          <el-radio :label="0">所有</el-radio>
          <el-radio :label="1">个人</el-radio>
          <el-radio :label="2">团体</el-radio>
        </el-radio-group>
      </div>
      <div class="list-item">
        <span style="width: 36px">单位</span>
        <el-cascader
          ref="company_cascader_ref"
          v-model="searchInfo.companyCode"
          :filter-method="filterMethod"
          :options="companyList"
          :props="{ multiple: false }"
          clearable
          filterable
          size="small"
          collapse-tags
          @change="companyChange"
        >
        </el-cascader>
      </div>
      <el-button
        @click="statistics"
        class="blue_btn btn"
        size="small"
        icon="iconfont icon-tongji"
        >统计</el-button
      >
      <!-- <el-button
        @click="prints"
        size="small"
        class="green_btn btn"
        icon="iconfont icon-dayin-"
        >打印</el-button
      > -->
      <el-button
        @click="exports"
        size="small"
        class="yellow_btn btn"
        icon="iconfont icon-daochu"
        >导出</el-button
      >
    </div>
  </div>
</template>

<script>
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
export default {
  name: 'searchBar',
  props: {
    searchInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      companyList: []
    };
  },
  computed: {
    ...mapGetters(['G_datePickerShortcuts'])
  },
  created() {
    this.getCompanyList();
  },
  methods: {
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    // 获取单位下拉
    getCompanyList() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) return;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.searchInfo.date = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.statistics();
    },
    // 统计
    statistics() {
      this.$emit('statistics');
    },
    // 打印
    prints() {
      this.$emit('prints');
    },
    // 导出
    exports() {
      this.$emit('exports');
    },
    // 日期切换
    dateChange() {
      this.$emit('dateChange');
    },
    // 单选框切换
    radioChange() {
      this.$emit('radioChange');
    },
    // 下拉框切换
    selectChange() {
      this.$emit('selectChange');
    }
  }
};
</script>

<style lang="less" scoped>
.searchBar {
  .search-list {
    display: flex;
    align-items: center;
  }
  .list-item {
    display: flex;
    align-items: center;
    margin-right: 10px;
    span {
      font-size: 14px;
      font-weight: 600;
      margin-right: 10px;
    }
  }
  .input {
    width: 100%;
  }
  .btn {
    padding: 6.5px 10px;
  }
}
</style>
