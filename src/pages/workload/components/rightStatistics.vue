<template>
  <div class="rightStatistics">
    <div class="doctor-workload">
      <div class="doctor-header">
        <span class="doctor-title">医生工作量:</span>
      </div>
      <div class="search-wrap">
        <div class="list-item">
          <span style="width: 75px">分类</span>
          <el-select
            placeholder="请选择"
            size="small"
            filterable
            clearable
            v-model="searchInfo.itemClsCode"
            class="input"
            @change="$parent.GetDoctorWorkload"
          >
            <el-option
              v-for="item in itemClsList"
              :key="item.clsCode"
              :label="item.clsName"
              :value="item.clsCode"
            >
            </el-option>
          </el-select>
        </div>
        <div class="list-item">
          <span style="width: 44px">医生</span>
          <el-select
            placeholder="请选择"
            size="small"
            filterable
            clearable
            v-model="searchInfo.doctorName"
            class="input"
            @change="$parent.GetDoctorWorkload"
          >
            <el-option
              v-for="item in G_sysOperator"
              :key="item.value"
              :label="item.label"
              :value="item.label"
            >
            </el-option>
          </el-select>
        </div>
        <div class="list-item">
          <span style="width: 84px">采集地点</span>
          <el-select
            placeholder="请选择"
            size="small"
            filterable
            clearable
            v-model="searchInfo.deptCode"
            class="input"
            @change="$parent.GetDoctorWorkload"
          >
            <el-option
              v-for="item in optionsExamRoom"
              :key="item.placeCode"
              :label="item.placeName"
              :value="item.placeCode"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="doctor-label">
        <span
          class="cell_blue"
          v-show="searchInfo.doctorName"
          style="width: 200px"
          >{{ searchInfo.doctorName }}：</span
        >
        <div class="label-num">
          <div class="num">
            总人数：<span class="cell_green"
              >{{ rightData.totalPeople }}人</span
            >
          </div>
          <div>
            总金额：<span class="cell_yellow"
              >{{ rightData.totalPrice }}元</span
            >
          </div>
        </div>
      </div>
      <el-skeleton :loading="loading" animated :count="4">
        <template slot="template">
          <el-skeleton-item variant="text" style="width: 500px" />
          <el-skeleton-item variant="text" style="width: 800px" />
          <el-skeleton-item variant="text" style="width: 800px" />
          <el-skeleton-item variant="text" style="width: 800px" />
          <el-skeleton-item
            variant="text"
            style="width: 700px; margin-bottom: 10px"
          />
        </template>
        <template>
          <div class="chart">
            <div id="doctor" class="doctor-chart"></div>
          </div>
        </template>
      </el-skeleton>
    </div>
    <div class="workload">
      <div class="workload-tabs">
        <div
          class="tabs-item"
          :class="activeIndex === item.id ? 'active' : ''"
          @click="tabsClick(item.id)"
          v-for="item in tabsList"
          :key="item.id"
        >
          {{ item.label }}
        </div>
      </div>
      <div class="tabs-table">
        <PublicTable
          :isSortShow="false"
          :theads="theads"
          :viewTableList.sync="rightData.enterData"
          :loading="loading"
          :cell_blue="['deptName', 'totalNum', 'totalPrice']"
          :expandAll="true"
        >
          <template #columnRight>
            <el-table-column type="expand">
              <template slot-scope="scope">
                <div
                  class="content"
                  v-for="(item, index) in scope.row.doctors"
                  :key="index"
                >
                  <span
                    :class="
                      activeIndex === 0 ? 'content-item' : 'contents-item'
                    "
                    >{{
                      activeIndex === 0 ? item.checkDoctorName : item.doctorName
                    }}</span
                  >
                  <span
                    :class="
                      activeIndex === 0 ? 'content-item' : 'contents-item'
                    "
                    >{{ item.totalNum }}</span
                  >
                  <span v-show="activeIndex !== 0" class="contents-item">{{
                    item.price
                  }}</span>
                </div>
              </template>
            </el-table-column>
          </template>
        </PublicTable>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
require('echarts/theme/macarons');
import PublicTable from '@/components/publicTable.vue';
import { mapGetters } from 'vuex';
export default {
  name: 'rightStatistics',
  components: {
    PublicTable
  },
  props: {
    loading: {
      type: Boolean,
      default: true
    },
    rightData: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      searchInfo: {
        itemClsCode: '',
        doctorName: '',
        deptCode: ''
      },
      activeIndex: 0,
      tabsList: [
        {
          id: 0,
          label: '录入员工作量'
        },
        {
          id: 1,
          label: '主检工作量'
        },
        {
          id: 2,
          label: '审核工作量'
        }
      ],
      theads: {
        deptName: '人员',
        totalNum: '人次'
      },
      tableData: [],
      // loading: true,
      optionsExamRoom: [],
      itemClsList: [],
      barWidth: 10
    };
  },
  mounted() {
    this.getItemCls();
    this.getCodeInspectPlace();
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_sysOperator'])
  },
  methods: {
    //分类
    getItemCls() {
      this.$ajax.post(this.$apiUrls.ItemCls).then((r) => {
        this.itemClsList = r.data.returnData;
      });
    },
    //获取检查地点
    getCodeInspectPlace() {
      this.$ajax
        .post(this.$apiUrls.RD_CodeGatherPlace + '/Read', [])
        .then((r) => {
          this.optionsExamRoom = r.data.returnData;
        });
    },
    // 标签页点击
    tabsClick(index) {
      this.activeIndex = index;
      if (index === 0) {
        this.theads = {
          deptName: '人员',
          totalNum: '人次'
        };
        this.$parent.InputDoctorWorkloadReport();
      } else if (index === 1) {
        this.theads = {
          deptName: '人员',
          totalNum: '人次',
          totalPrice: '金额'
        };
        this.$parent.CheckDoctorWorkloadReport();
      } else {
        this.theads = {
          deptName: '人员',
          totalNum: '人次',
          totalPrice: '金额'
        };
        this.$parent.AuditDoctorWorkloadReport();
      }
    },
    // 初始化图表
    initChart() {
      this.$nextTick(() => {
        this.drawChartDoctor();
      });
    },
    // 医生工作量统计图
    drawChartDoctor() {
      const colors = ['#3CB34F', '#FAB63B'];
      let myChart = echarts.init(document.getElementById('doctor'));
      myChart.setOption({
        color: colors,
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          data: ['人次', '金额'],
          right: 0
        },
        grid: {
          left: '50',
          bottom: '80',
          top: '60',
          right: '60'
          // containLabel: true
        },
        xAxis: {
          type: 'category',
          axisLabel: {
            color: '#2D3436',
            formatter: function (value) {
              //x轴的文字改为竖版显示
              return `{a|${value.split('').join('\n')}}`;
            },
            rich: {
              a: {
                height: 14 // 设置字体行高
              }
            }
          },
          axisTick: {
            alignWithLabel: true,
            //y轴刻度线
            show: false
          },
          // prettier-ignore
          data: this.rightData.xData
        },
        yAxis: [
          {
            type: 'value',
            name: '人次',
            position: 'left',
            alignTicks: true,
            axisLine: {
              show: true,
              lineStyle: {
                color: colors[0]
              }
            }
          },
          {
            type: 'value',
            name: '金额/元',
            position: 'right',
            alignTicks: true,
            axisLine: {
              show: true,
              lineStyle: {
                color: colors[1]
              }
            },
            splitLine: {
              // y轴虚线
              show: true,
              lineStyle: {
                type: 'dashed',
                color: '#B2BEC3'
              }
            }
          }
        ],
        series: [
          {
            name: '人次',
            type: 'bar',
            barWidth: this.barWidth,
            data: this.rightData.yPeopleData
          },
          {
            name: '金额',
            type: 'bar',
            barWidth: this.barWidth,
            yAxisIndex: 1,
            data: this.rightData.yPriceData
          }
        ]
      });
      // 浏览器大小调整echarts自适应
      window.addEventListener('resize', () => {
        if (myChart) {
          myChart.resize();
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.rightStatistics {
  display: flex;
  width: 100%;
  .doctor-workload {
    flex: 1;
    flex-shrink: 0;
    background: #fff;
    padding: 10px;
    border-radius: 4px;
    margin-right: 10px;
  }
  .doctor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    margin-bottom: 10px;
  }
  .doctor-title {
    font-weight: 600;
  }
  .doctor-label {
    // flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 14px;
    margin-bottom: 10px;
  }
  .label-num {
    display: flex;
    align-items: center;
    font-size: 14px;
  }
  .search-wrap {
    display: flex;
  }
  .list-item {
    display: flex;
    align-items: center;
    margin-right: 10px;
    margin-bottom: 10px;
    span {
      font-size: 14px;
      font-weight: 600;
      margin-right: 10px;
      text-align: right;
    }
  }
  .num {
    margin-right: 20px;
  }
  .doctor-chart {
    width: 100%;
    height: 460px;
  }
  .chart {
    width: 100%;
    height: 442px;
    overflow: auto;
  }
  .input {
    width: 100%;
  }
  .workload {
    // flex: 1;
    // height: 50%;
    // height: 661px;
    width: 330px;
    display: flex;
    flex-direction: column;
    border-radius: 4px;
    background: #fff;
    overflow: auto;
    padding-bottom: 10px;
  }
  .workload-tabs {
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex: 1;
    flex-shrink: 0;
    padding: 0 10px;
  }
  .tabs-item {
    padding: 0 10px 10px 10px;
    border-bottom: 2px solid #fff;
    cursor: pointer;
    &:last-child {
      margin-right: 0;
    }
    &.active {
      color: #1770df;
      border-bottom: 2px solid #1770df;
    }
  }
  .tabs-table {
    height: calc(100% - 61px);
    // flex: 1;
    overflow: auto;
  }
  .content {
    display: flex;
    padding: 6px 10px;
    border-bottom: 1px dashed rgba(178, 190, 195, 0.5);
    &:last-child {
      border-bottom: 1px dashed rgba(45, 52, 54, 0.6);
    }
  }
  .content-item {
    width: 136px;
  }
  .contents-item {
    width: 92px;
  }
  /deep/.el-table--striped
    .el-table__body
    tr.el-table__row--striped
    td.el-table__cell {
    background: #fff !important;
  }
}
</style>
