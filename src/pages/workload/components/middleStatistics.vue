<template>
  <div class="middleStatistics">
    <div class="registrar">
      <div class="registrar-title">
        <span class="registrar-text">登记员工作量：</span>
        <div>
          <span>个人<span class="personal"></span></span>
          <span>团体<span class="company"></span></span>
        </div>
      </div>
      <el-skeleton :loading="loading" animated :count="12">
        <template slot="template">
          <el-skeleton-item
            variant="text"
            style="width: 50px; margin-right: 10px; margin-bottom: 20px"
          />
          <el-skeleton-item
            variant="h3"
            style="width: 200px; margin-bottom: 20px"
          />
        </template>
        <template class="registrar-list">
          <div class="">
            <div
              class="list-item"
              v-for="(item, index) in middleData.registrarData"
              :key="index"
            >
              <div class="list-name">{{ item.registrar }}</div>
              <div class="list-progress">
                <div>
                  <el-progress
                    :percentage="item.personNum"
                    color="#fab63b"
                    :stroke-width="8"
                    :format="format"
                  ></el-progress>
                </div>
                <div>
                  <el-progress
                    :percentage="item.companyNum"
                    color="#1770df"
                    :stroke-width="8"
                    :format="format"
                  ></el-progress>
                </div>
              </div>
            </div>
          </div>
        </template>
      </el-skeleton>
    </div>
    <div class="right-wrap">
      <div class="active-wrap">
        <div class="active-header">
          <span class="active-title">激活工作量：</span>
          <span>总激活量：{{ middleData.activeTotal }}人</span>
        </div>
        <el-skeleton :loading="loading" animated :count="4">
          <template slot="template">
            <el-skeleton-item
              variant="text"
              style="width: 400px; margin-top: 6px"
            />
            <el-skeleton-item
              variant="text"
              style="width: 800px; margin-bottom: 20px"
            />
          </template>
          <template>
            <div class="chart">
              <div id="active" class="active-chart"></div>
            </div>
          </template>
        </el-skeleton>
      </div>
      <div class="active-wrap">
        <div class="active-header">
          <span class="active-title">开单医生工作量：</span>
          <span>医生开单总量：{{ middleData.billingTotal }}人</span>
        </div>
        <el-skeleton :loading="loading" animated :count="4">
          <template slot="template">
            <el-skeleton-item
              variant="text"
              style="width: 400px; margin-top: 5px"
            />
            <el-skeleton-item
              variant="text"
              style="width: 800px; margin-bottom: 20px"
            />
          </template>
          <template>
            <div class="chart">
              <div id="billing" class="active-chart"></div>
            </div>
          </template>
        </el-skeleton>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
require('echarts/theme/macarons');
export default {
  name: 'middleStatistics',
  props: {
    middleData: {
      type: Object,
      default: {}
    },
    loading: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    // 初始化图表
    initChart() {
      this.$nextTick(() => {
        this.drawChartActive();
        this.drawChartBilling();
      });
    },
    // 登记员工作量文字格式化
    format(percentage) {
      return (percentage * 100).toFixed(0) + ' 人';
    },
    // 激活工作量统计图
    drawChartActive() {
      let myChart = echarts.init(document.getElementById('active'));
      myChart.setOption({
        color: ['#D63031'],
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '42',
          bottom: '80',
          top: '20',
          right: '22'
        },
        xAxis: {
          data: this.middleData.activeXData,
          axisTick: {
            //y轴刻度线
            show: false
          },
          axisLabel: {
            color: '#2D3436',
            formatter: function (value) {
              //x轴的文字改为竖版显示
              return `{a|${value.split('').join('\n')}}`;
            },
            rich: {
              a: {
                height: 14 // 设置字体行高
              }
            }
          },
          axisLine: {
            //x轴
            lineStyle: {
              color: '#B2BEC3'
            }
          }
        },
        yAxis: {
          type: 'value',
          axisTick: {
            //y轴刻度线
            show: false
          },
          axisLine: {
            //y轴
            show: true
          },
          splitLine: {
            // y轴虚线
            show: true,
            lineStyle: {
              type: 'dashed',
              color: '#B2BEC3'
            }
          }
        },
        series: [
          {
            data: this.middleData.activeYData,
            type: 'line',
            smooth: true,
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  // 1代表上面
                  offset: 1,
                  color: 'rgba(214,48,49,0)'
                },
                {
                  offset: 0,
                  color: 'rgba(150,30,31,0.4)'
                }
              ]),
              opacity: 1 // 填充区域透明度
            }
          }
        ]
      });
      // 浏览器大小调整echarts自适应
      window.addEventListener('resize', () => {
        if (myChart) {
          myChart.resize();
        }
      });
    },
    // 开单医生工作量统计图
    drawChartBilling() {
      let myChart = echarts.init(document.getElementById('billing'));
      myChart.setOption({
        color: ['#1770DF'],
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '42',
          bottom: '80',
          top: '20',
          right: '22'
        },
        xAxis: {
          data: this.middleData.billingXData,
          axisTick: {
            //y轴刻度线
            show: false
          },
          axisLabel: {
            color: '#2D3436',
            formatter: function (value) {
              //x轴的文字改为竖版显示
              return `{a|${value.split('').join('\n')}}`;
            },
            rich: {
              a: {
                height: 14 // 设置字体行高
              }
            }
          },
          axisLine: {
            //x轴
            lineStyle: {
              color: '#B2BEC3'
            }
          }
        },
        yAxis: {
          type: 'value',
          axisTick: {
            //y轴刻度线
            show: false
          },
          axisLine: {
            //y轴
            show: true
          },
          splitLine: {
            // y轴虚线
            show: true,
            lineStyle: {
              type: 'dashed',
              color: '#B2BEC3'
            }
          }
        },
        series: [
          {
            data: this.middleData.billingYData,
            type: 'line',
            smooth: true,
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  // 1代表上面
                  offset: 1,
                  color: 'rgba(18,81,162,0)'
                },
                {
                  offset: 0,
                  color: 'rgba(18,81,162,0.4)'
                }
              ]),
              opacity: 1 // 填充区域透明度
            }
          }
        ]
      });
      // 浏览器大小调整echarts自适应
      window.addEventListener('resize', () => {
        if (myChart) {
          myChart.resize();
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.middleStatistics {
  // height: 570px;
  flex: 1;
  flex-shrink: 0;
  display: flex;
  flex-direction: row;
  margin-bottom: 10px;
  .registrar {
    background: #fff;
    padding: 10px;
    margin-right: 10px;
    display: flex;
    flex-direction: column;
    width: 320px;
    border-radius: 4px;
    overflow: auto;
  }
  .registrar-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    margin-bottom: 20px;
  }
  .registrar-text {
    font-weight: 600;
  }
  .personal {
    width: 32px;
    height: 8px;
    background: #fab63b;
    border-radius: 4px;
    display: inline-block;
    margin: 0 20px 0 10px;
  }
  .company {
    width: 32px;
    height: 8px;
    background: #1770df;
    border-radius: 4px;
    display: inline-block;
    margin: 0 20px 0 10px;
  }
  .registrar-list {
    flex: 1;
    overflow: auto;
  }
  .list-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .list-progress {
    flex: 1;
    margin-left: 10px;
  }
  .list-name {
    font-size: 14px;
    font-weight: 600;
    width: 70px;
    text-align: right;
  }
  /deep/.el-progress__text {
    font-size: 12px !important;
  }
  .right-wrap {
    flex: 1;
  }
  .active-wrap {
    background: #fff;
    border-radius: 4px;
    padding: 10px;
    font-size: 14px;
    // height: 280px;
    margin-bottom: 10px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .active-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .active-title {
    font-weight: 600;
  }
  .active-chart {
    width: 100%;
    height: 280px;
  }
}
</style>
