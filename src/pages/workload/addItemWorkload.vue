<template>
  <!-- 加项工作量报表 -->
  <div class="addItemWorkload">
    <div class="main">
      <SearchBar
        :searchInfo="searchInfo"
        @statistics="statistics"
        @prints="prints"
        @exports="exports"
        @dateChange="statistics"
        @radioChange="radioChange"
        @selectChange="statistics"
      />
      <div class="main-title">
        <h3>加项工作量报表:</h3>
        <div class="title-wrap">
          <span>本次体检人数：{{ dataInfo.totalNum }}</span>
          <span>男：{{ dataInfo.maleNum }}</span>
          <span>女：{{ dataInfo.femaleNum }}</span>
          <span>已发报告人数：{{ dataInfo.reportedNum }}</span>
          <span>团体检查人数：{{ dataInfo.companyNum }}</span>
          <span>团体已发报告人数：{{ dataInfo.companyReportedNum }}</span>
        </div>
      </div>
      <div class="main-table">
        <div class="table">
          <PublicTable
            :isSortShow="false"
            :theads="theads"
            :viewTableList.sync="tableData"
          ></PublicTable>
        </div>
        <div class="total">
          <span>总计：</span>
          <span>{{ dataInfo.totalNum }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SearchBar from './components/searchBar.vue';
import PublicTable from '@/components/publicTable.vue';
import { dataUtils } from '@/common';
export default {
  name: 'addItemWorkload',
  components: {
    SearchBar,
    PublicTable
  },
  data() {
    return {
      searchInfo: {
        date: [new Date(), new Date()],
        personGroup: 0,
        companyCode: ''
      },
      theads: {
        operatorCode: '操作员',
        totalNum: '人次'
      },
      tableData: [],
      dataInfo: {
        totalNum: 0,
        maleNum: 0,
        femaleNum: 0,
        reportedNum: 0,
        companyNum: 0,
        companyReportedNum: 0
      }
    };
  },
  methods: {
    // 查询获取数据
    statistics() {
      let [beginDate, endDate] = this.searchInfo.date;
      let data = {
        beginDate: dataUtils.dateToString(beginDate),
        endDate: dataUtils.dateToString(endDate),
        personGroup: this.searchInfo.personGroup,
        companyCode: this.searchInfo.companyCode[0],
        itemClsCode: '',
        doctorName: '',
        deptCode: ''
      };
      this.$ajax.post(this.$apiUrls.AddItemWorkloadReport, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.dataInfo = returnData.populationStatistics;
        this.tableData = returnData.report;
      });
    },
    // 单选框切换
    radioChange() {
      if (this.searchInfo.personGroup === 1) {
        this.searchInfo.companyCode = '';
      }
      this.statistics();
    },
    prints() {},
    exports() {}
  }
};
</script>

<style lang="less" scoped>
.addItemWorkload {
  color: #2d3436;
  .main {
    height: 100%;
    background: #fff;
    padding: 18px 10px;
    display: flex;
    flex-direction: column;
  }
  .main-title {
    margin: 18px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    h3 {
      font-size: 18px;
    }
  }
  .title-wrap {
    display: flex;
    align-items: center;
    font-size: 14px;
    span {
      width: 180px;
    }
  }
  .main-table {
    flex: 1;
    border: 1px solid rgba(178, 190, 195, 0.5);
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    overflow: auto;
    // padding-bottom: 38px;
  }
  .table {
    flex: 1;
    overflow: auto;
  }
  .total {
    background: #cbddf5;
    font-size: 14px;
    font-weight: 600;
    padding: 10px;
    display: flex;
    align-items: center;
    span {
      flex: 1;
    }
  }
}
</style>
