<template>
  <div class="doctorWorkload" id="doctorWorkload">
    <div class="doctorWorkload-wrap">
      <div class="header">
        <el-form :model="searchForm" inline>
          <!-- <el-row>
            <el-col :span="6"> -->
          <!-- </el-col>
            <el-col :span="6"> -->
          <el-form-item label="" prop="" class="radioForm">
            <div class="titleRadio">
              <el-radio-group
                v-model.trim="searchForm.personGroup"
                @change="radioChange"
              >
                <el-radio :label="0">所有</el-radio>
                <el-radio :label="1">个人</el-radio>
                <el-radio :label="2">团体</el-radio>
              </el-radio-group>
            </div>
          </el-form-item>
          <!-- </el-col>
            <el-col :span="6"> -->
          <el-form-item label="单位" prop="companyCode">
            <el-cascader
              ref="company_cascader_ref"
              v-model="searchForm.companyCode"
              :filter-method="filterMethod"
              :options="companyList"
              :props="{ multiple: false }"
              clearable
              filterable
              size="mini"
              collapse-tags
              @change="companyChange"
            >
            </el-cascader>
          </el-form-item>
          <!-- </el-col> -->
          <!-- <el-col :span="6">
            <el-form-item label="外检" prop="external">
              <el-select
                placeholder="请选择"
                size="mini"
                filterable
                clearable
                v-model="searchForm.external"
                @change="search"
              >
                <el-option
                  v-for="(item, key) in externalList"
                  :key="key"
                  :label="item"
                  :value="key"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
          <!-- </el-row> -->
          <!-- <el-row> -->
          <!-- <el-col :span="6"> -->
          <el-form-item label="分类" prop="itemClsCode">
            <el-select
              placeholder="请选择"
              size="mini"
              filterable
              clearable
              v-model="searchForm.itemClsCode"
              @change="search"
            >
              <el-option
                v-for="item in itemClsList"
                :key="item.clsCode"
                :label="item.clsName"
                :value="item.clsCode"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <!-- </el-col>
            <el-col :span="6"> -->
          <el-form-item label="检查科室" prop="deptCode">
            <el-select
              placeholder="请选择"
              size="mini"
              filterable
              clearable
              v-model="searchForm.deptCode"
              @change="search"
            >
              <el-option
                v-for="item in optionsExamRoom"
                :key="item.placeCode"
                :label="item.placeName"
                :value="item.placeCode"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <!-- </el-col>
            <el-col :span="6"> -->
          <el-form-item label="医生" prop="doctorName">
            <el-select
              placeholder="请选择"
              size="mini"
              filterable
              clearable
              v-model="searchForm.doctorName"
              @change="search"
            >
              <el-option
                v-for="item in G_sysOperator"
                :key="item.label"
                :label="item.label"
                :value="item.label"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="统计时间" prop="statisticalTime">
            <el-date-picker
              :picker-options="{ shortcuts: G_datePickerShortcuts }"
              style="width: 220px"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="mini"
              v-model="statisticalTime"
              @change="search"
              :clearable="false"
            >
            </el-date-picker>
          </el-form-item>
          <!-- </el-col>
            <el-col :span="6"> -->
          <el-form-item label="" prop="doctor" label-width="40px">
            <ButtonGroup
              style="margin-top: 5px"
              :btnList="['统计', '导出']"
              @statistics="search"
              @exports="exports"
            ></ButtonGroup>
          </el-form-item>
          <!-- </el-col>
          </el-row> -->
        </el-form>
      </div>
      <div id="printDiv">
        <div class="titlewrap">
          <p class="titleP">医生工作量报表:</p>
          <p class="contP">
            <span class="otherSpsn"
              >本次体检人数：{{ allList.populationStatistics.totalNum }}</span
            >
            <span class="sexSpan"
              >男：{{ allList.populationStatistics.maleNum }}</span
            >
            <span class="sexSpan"
              >女：{{ allList.populationStatistics.femaleNum }}</span
            >
            <span class="otherSpsn"
              >已发报告人数：{{
                allList.populationStatistics.reportedNum
              }}</span
            >
            <span class="otherSpsn"
              >团体检查人数:{{ allList.populationStatistics.companyNum }}</span
            >
            <span class="otherSpsn"
              >团体已发报告人数:{{
                allList.populationStatistics.companyReportedNum
              }}</span
            >
          </p>
        </div>
        <div class="contDiv" id="contDiv">
          <div class="tableDiv">
            <div v-for="(item, index) in allList.doctorCombs" :key="index">
              <p class="titleP">检查医生:{{ item.doctorName }}</p>
              <PublicTable
                :isSortShow="false"
                :viewTableList.sync="item.combStatisticses"
                :theads.sync="theads"
              >
              </PublicTable>
            </div>
          </div>

          <div class="spanDiv footerr">
            <span>总计:</span>
            <span>人次:{{ allList.personTime }}次</span>
            <span></span>
            <span>金额:{{ allList.amount }}元</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import PublicTable from '@/components/publicTable.vue';
import ButtonGroup from './components/buttonGroup.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
import Exceljs from '@/common/excel/groupDetailedExpenseReportExcel';
export default {
  name: 'doctorWorkload',
  mixins: [Exceljs],
  components: { PublicTable, ButtonGroup },
  data() {
    return {
      searchForm: {
        beginDate: '',
        endDate: '',
        personGroup: 0,
        companyCode: '',
        itemClsCode: '',
        doctorName: '',
        deptCode: ''
      },
      externalList: {
        0: '外检',
        1: '内检'
      },
      statisticalTime: [dataUtils.getDate(), dataUtils.getDate()],
      companyList: [],
      itemClsList: [],
      optionsExamRoom: [],
      theads: {
        combName: '项目名称',
        personTime: '人次',
        originalPrice: '原始单价',
        amount: '金额'
      },
      allList: {
        populationStatistics: {
          totalNum: 0,
          maleNum: 0,
          femaleNum: 0,
          reportedNum: 0,
          companyNum: 0,
          companyReportedNum: 0
        },
        doctorCombs: [
          // {
          //   doctorName: "",
          //   combStatisticses: [
          //     {
          //       combName: "string",
          //       personTime: 0,
          //       originalPrice: 0,
          //       amount: 0,
          //     },
          //   ],
          // },
        ],
        personTime: 0,
        amount: 0
      }
    };
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_sysOperator', 'G_datePickerShortcuts'])
  },
  mounted() {
    this.getCompany();
    this.getItemCls();
    this.getCodeInspectPlace();
  },

  methods: {
    //统计查询
    search() {
      if (!this.statisticalTime) {
        this.searchForm.beginDate = '';
        this.searchForm.endDate = '';
      } else {
        this.searchForm.beginDate = this.statisticalTime[0];
        this.searchForm.endDate = this.statisticalTime[1];
      }
      let { companyCode, ...surplus } = this.searchForm;
      let parameter = {
        companyCode: companyCode[0],
        ...surplus
      };
      this.$ajax.post(this.$apiUrls.GetDoctorWorkload, parameter).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.allList = returnData;
        if (this.allList?.deptCombs?.length >= 1) {
          this.$message({
            message: '成功!',
            type: 'success',
            showClose: true
          });
        } else {
          this.$message({
            message: '暂无数据!',
            type: 'success',
            showClose: true
          });
        }
      });
    },
    //获取单位下拉
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) return;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.statisticalTime = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.search();
    },
    //分类
    getItemCls() {
      this.$ajax.post(this.$apiUrls.ItemCls).then((r) => {
        this.itemClsList = r.data.returnData;
      });
    },
    //获取检查地点
    getCodeInspectPlace() {
      this.$ajax
        .post(this.$apiUrls.RD_CodeGatherPlace + '/Read', [])
        .then((r) => {
          //
          this.optionsExamRoom = r.data.returnData;
        });
    },
    // 单选框切换
    radioChange() {
      if (this.searchForm.personGroup === 1) {
        this.searchForm.companyCode = '';
      }
      this.search();
    },
    // 导出
    exports() {
      if (this.allList?.doctorCombs?.length == 0) {
        this.$message.warning('报表不能为空!');
        return;
      }
      this.exportExcel(
        this.theads,
        this.allList.doctorCombs,
        `已发报告人数：${this.allList.doctorCombs.length} 人`,
        '`检查医生：${item.doctorName}`',
        '医生工作量报表',
        'combStatisticses'
      );
    }
  }
};
</script>
<style lang="less" scoped>
.doctorWorkload {
  .doctorWorkload-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 15px !important;
    font-family: PingFangSC-Semibold;
    font-size: 14px;
    color: #2d3436;
    background: #fff;
  }
  .header {
    // display: flex;
    flex-direction: row;
    justify-content: space-between;
    // height: 40px;
    // line-height: 40px;
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #2d3436;
    .el-form-item {
      margin-bottom: 5px;
    }
    /deep/.el-date-editor--daterange.el-input,
    .el-date-editor--daterange.el-input__inner,
    .el-date-editor--timerange.el-input,
    .el-date-editor--timerange.el-input__inner {
      width: 100%;
    }
    .el-select {
      width: 100%;
    }
    .radioForm /deep/.el-form-item__content {
      margin: 0 10px !important;
      /deep/ .el-radio {
        margin-left: 15px;
      }
    }
  }
  #printDiv {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    .titlewrap {
      height: 60px;
      line-height: 60px;
      display: flex;
      flex-direction: row;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #2d3436;
      .titleP {
        font-family: PingFangSC-Medium;
        font-size: 18px;
        width: 224px;
        font-weight: 700;
      }
      .contP {
        flex: 1;
        display: flex;
        flex-direction: row;
        .otherSpsn {
          flex: 1;
        }
        .sexSpan {
          width: 110px;
        }
      }
    }
    .contDiv {
      flex: 1;
      overflow: auto;
      display: flex;
      flex-direction: column;
      .tableDiv {
        flex: 1;
        overflow: auto;
      }
      .titleP {
        font-family: PingFangSC-Regular;
        color: #1770df;
        line-height: 32px;
        font-weight: 700;
      }

      /deep/.el-table__footer-wrapper tbody td.el-table__cell,
      .el-table__header-wrapper tbody td.el-table__cell {
        font-family: PingFangSC-Regular;
        color: #1770df !important;
      }
    }
    /deep/.el-table--scrollable-y .el-table__body-wrapper {
      height: auto !important;
    }
    /deep/.el-table__body-wrapper {
      height: auto !important;
    }

    .publicTable_com {
      border: 1px solid #ccc;
      border-radius: 4px;
    }
    .spanDiv {
      width: 100%;
      display: flex;
      height: 38px;
      line-height: 38px;
      border-bottom: 1px solid #ccc;
      padding: 0 20px;
      & > span {
        flex: 1;
      }
    }
    .footerr {
      background: #cbddf5;
      padding-right: 30px;
      font-weight: 700;
    }
  }
}
</style>
