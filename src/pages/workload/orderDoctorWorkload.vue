<template>
  <div class="orderDoctorWorkload" id="orderDoctorWorkload">
    <div class="orderDoctorWorkload-wrap">
      <div class="header">
        <el-form :model="searchForm" label-width="60px">
          <el-form-item
            label="统计时间"
            prop="statisticalTime"
            label-width="70px"
          >
            <el-date-picker
              :picker-options="{ shortcuts: G_datePickerShortcuts }"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              v-model="statisticalTime"
              @change="search"
              :clearable="false"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="" prop="" class="radioForm">
            <div class="titleRadio">
              <el-radio-group
                v-model.trim="searchForm.personGroup"
                @change="radioChange"
              >
                <el-radio :label="0">所有</el-radio>
                <el-radio :label="1">个人</el-radio>
                <el-radio :label="2">团体</el-radio>
              </el-radio-group>
            </div>
          </el-form-item>
          <el-form-item label="单位" prop="companyCode">
            <el-cascader
              ref="company_cascader_ref"
              v-model="searchForm.companyCode"
              :filter-method="filterMethod"
              :options="companyList"
              :props="{ multiple: false }"
              clearable
              filterable
              size="small"
              collapse-tags
              @change="companyChange"
            >
            </el-cascader>
          </el-form-item>
        </el-form>
        <ButtonGroup
          :btnList="['统计', '导出']"
          @statistics="search"
          @exports="exports"
        ></ButtonGroup>
      </div>
      <div id="printDiv">
        <div class="titlewrap">
          <p class="titleP">开单医生工作量报表:</p>
          <p class="contP">
            <span class="otherSpsn"
              >本次体检人数：{{ allList.populationStatistics.totalNum }}</span
            >
            <span class="sexSpan"
              >男：{{ allList.populationStatistics.maleNum }}</span
            >
            <span class="sexSpan"
              >女：{{ allList.populationStatistics.femaleNum }}</span
            >
            <span class="otherSpsn"
              >已发报告人数：{{
                allList.populationStatistics.reportedNum
              }}</span
            >
            <span class="otherSpsn"
              >团体检查人数:{{ allList.populationStatistics.companyNum }}</span
            >
            <span class="otherSpsn"
              >团体已发报告人数:{{
                allList.populationStatistics.companyReportedNum
              }}</span
            >
          </p>
        </div>
        <div class="contDiv" id="contDiv">
          <PublicTable
            :isSortShow="false"
            :viewTableList.sync="allList.applicantPersonTimes"
            :theads.sync="theads"
          >
          </PublicTable>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import PublicTable from '@/components/publicTable.vue';
import ButtonGroup from './components/buttonGroup.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
export default {
  name: 'orderDoctorWorkload',
  components: { PublicTable, ButtonGroup },
  data() {
    return {
      searchForm: {
        beginDate: '',
        endDate: '',
        personGroup: 0,
        companyCode: '',
        itemClsCode: '',
        doctorName: '',
        deptCode: ''
      },
      statisticalTime: [dataUtils.getDate(), dataUtils.getDate()],
      companyList: [],
      theads: {
        applicant: '开单医生',
        personTime: '人次'
      },
      allList: {
        populationStatistics: {
          totalNum: 0,
          maleNum: 0,
          femaleNum: 0,
          reportedNum: 0,
          companyNum: 0,
          companyReportedNum: 0
        },
        applicantPersonTimes: [
          // {
          //   applicant: "string",
          //   personTime: 0,
          // },
        ]
      },
      exportData: []
    };
  },
  computed: {
    ...mapGetters(['G_datePickerShortcuts'])
  },
  mounted() {
    this.getCompany();
  },

  methods: {
    //统计查询
    search() {
      if (!this.statisticalTime) {
        this.searchForm.beginDate = '';
        this.searchForm.endDate = '';
      } else {
        this.searchForm.beginDate = this.statisticalTime[0];
        this.searchForm.endDate = this.statisticalTime[1];
      }
      let { companyCode, ...surplus } = this.searchForm;
      let parameter = {
        companyCode: companyCode[0],
        ...surplus
      };
      this.$ajax
        .post(this.$apiUrls.GetApplicantWorkload, parameter)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.allList = returnData;
          if (this.allList?.applicantPersonTimes.length >= 1) {
            this.$message({
              message: '成功!',
              type: 'success',
              showClose: true
            });
          } else {
            this.$message({
              message: '暂无数据!',
              type: 'success',
              showClose: true
            });
          }
        });
    },
    // 单选框切换
    radioChange() {
      if (this.searchForm.personGroup === 1) {
        this.searchForm.companyCode = '';
      }
      this.search();
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) return;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.statisticalTime = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.search();
    },
    //获取单位下拉
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    exports() {
      this.exportData = this.allList.applicantPersonTimes;
      if (this.exportData.length == 0) {
        return this.$message({
          message: '请选择至少一条数据进行操作！',
          type: 'warning',
          showClose: true
        });
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.exportData.map((item, i) => {
            item.index = i + 1;
          });
          this.$nextTick(function () {
            this.export2Excel();
          });
        })
        .catch(() => {});
    },
    // 数据写入excel
    export2Excel() {
      var that = this;
      require.ensure([], () => {
        const { export_json_to_excel } = require('@/utils/Export2Excel'); // 这里必须使用绝对路径，使用@/+存放export2Excel的路径
        const tHeader = Object.values(this.theads); // 导出的表头名信息
        const filterVal = Object.keys(this.theads); // 导出的表头字段名
        const list = that.exportData;
        const data = that.formatJson(filterVal, list);
        console.log(data);
        const name = '开单医生工作量报表' + dataUtils.getNowDateTiemNo();
        export_json_to_excel(tHeader, data, name);
      });
    },
    // 格式转换
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) => filterVal.map((j) => v[j]));
    }
  }
};
</script>
<style lang="less" scoped>
.orderDoctorWorkload {
  .orderDoctorWorkload-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 15px !important;
    font-family: PingFangSC-Semibold;
    font-size: 14px;
    color: #2d3436;
    background: #fff;
  }
  .header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    height: 40px;
    line-height: 40px;
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #2d3436;
    .el-form {
      display: flex;
      flex-direction: row;
      margin-right: 10px;
    }
    .el-form-item {
      margin-bottom: 0;
    }
    .radioForm {
      /deep/.el-form-item__content {
        margin-left: 20px !important;
        .titleRadio {
          width: 210px;
          .el-radio {
            margin-right: 16px;
          }
        }
      }
    }
  }
  #printDiv {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    .titlewrap {
      height: 60px;
      line-height: 60px;
      display: flex;
      flex-direction: row;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #2d3436;
      .titleP {
        font-family: PingFangSC-Medium;
        font-size: 18px;
        width: 224px;
        font-weight: 700;
      }
      .contP {
        flex: 1;
        display: flex;
        flex-direction: row;
        .otherSpsn {
          flex: 1;
        }
        .sexSpan {
          width: 110px;
        }
      }
    }
    /deep/.el-table__footer-wrapper tbody td.el-table__cell,
    .el-table__header-wrapper tbody td.el-table__cell {
      background: #cbddf5;
      border-radius: 4px;
      font-family: PingFangSC-Semibold;
      color: #2d3436;
    }
    .contDiv {
      flex: 1;
      overflow: auto;
    }
  }
}
</style>
