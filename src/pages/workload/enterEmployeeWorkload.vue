<template>
  <!-- 录入员工作量报表 -->
  <div class="enterEmployeeWorkload">
    <div class="main">
      <SearchBar
        :searchInfo="searchInfo"
        @statistics="statistics"
        @prints="prints"
        @exports="exports"
        @dateChange="statistics"
        @radioChange="radioChange"
        @selectChange="statistics"
      />
      <div class="main-title">
        <h3>录入员工作量报表:</h3>
        <div class="title-wrap">
          <span>本次体检人数：{{ dataInfo.totalNum }}</span>
          <span>男：{{ dataInfo.maleNum }}</span>
          <span>女：{{ dataInfo.femaleNum }}</span>
          <span>已发报告人数：{{ dataInfo.reportedNum }}</span>
          <span>团体检查人数：{{ dataInfo.companyNum }}</span>
          <span>团体已发报告人数：{{ dataInfo.companyReportedNum }}</span>
        </div>
      </div>
      <div class="main-table">
        <div class="table-list">
          <div class="table-item" v-for="(item, index) in report" :key="index">
            <div class="cell_blue item-text">{{ item.deptName }}</div>
            <div class="publicTable">
              <PublicTable
                :isSortShow="false"
                :theads="theads"
                :viewTableList.sync="item.doctors"
              ></PublicTable>
            </div>
          </div>
        </div>
        <div class="total">
          <span>总计：</span>
          <span>{{ total }}人</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SearchBar from './components/searchBar.vue';
import PublicTable from '@/components/publicTable.vue';
import { dataUtils } from '@/common';
export default {
  name: 'enterEmployeeWorkload',
  components: {
    SearchBar,
    PublicTable
  },
  data() {
    return {
      searchInfo: {
        date: [new Date(), new Date()],
        personGroup: 0,
        companyCode: ''
      },
      theads: {
        checkDoctorName: '检查医生',
        // deptName: "科室名称",
        totalNum: '人次'
      },
      tableData: [],
      dataInfo: {
        totalNum: 0,
        maleNum: 0,
        femaleNum: 0,
        reportedNum: 0,
        companyNum: 0,
        companyReportedNum: 0
      },
      report: [],
      total: 0
    };
  },
  methods: {
    // 查询统计
    statistics() {
      let [beginDate, endDate] = this.searchInfo.date;
      let data = {
        beginDate: dataUtils.dateToString(beginDate),
        endDate: dataUtils.dateToString(endDate),
        personGroup: this.searchInfo.personGroup,
        companyCode: this.searchInfo.companyCode[0],
        itemClsCode: '',
        doctorName: '',
        deptCode: ''
      };
      this.$ajax
        .post(this.$apiUrls.InputDoctorWorkloadReport, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.dataInfo = returnData.populationStatistics;
          this.report = returnData.report.map((item) => {
            item.doctors.push({
              checkDoctorName: '合计',
              deptName: null,
              totalNum: item.totalNum
            });
            return {
              ...item
            };
          });
          this.total = returnData.totalNum;
        });
    },
    // 单选框切换
    radioChange() {
      if (this.searchInfo.personGroup === 1) {
        this.searchInfo.companyCode = '';
      }
      this.statistics();
    },
    prints() {},
    exports() {}
  }
};
</script>

<style lang="less" scoped>
.enterEmployeeWorkload {
  color: #2d3436;
  .main {
    height: 100%;
    background: #fff;
    padding: 18px 10px;
    display: flex;
    flex-direction: column;
  }
  .main-title {
    margin: 18px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    h3 {
      font-size: 18px;
    }
  }
  .title-wrap {
    display: flex;
    align-items: center;
    font-size: 14px;
    span {
      width: 178px;
    }
  }
  .main-table {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
  }
  .table-list {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .table-item {
    margin-bottom: 18px;
  }
  .item-text {
    margin-bottom: 10px;
    font-size: 14px;
  }
  .publicTable {
    border: 1px solid rgba(178, 190, 195, 0.5);
    border-radius: 4px;
    overflow: auto;
  }
  .total {
    background: #cbddf5;
    font-size: 14px;
    font-weight: 600;
    padding: 10px;
    display: flex;
    align-items: center;
    border-radius: 4px;
    span {
      flex: 1;
    }
  }
  /deep/.el-table--scrollable-y .el-table__body-wrapper {
    height: auto !important;
  }
  /deep/.el-table__body-wrapper {
    height: auto !important;
  }
}
</style>
