<template>
  <div class="collectorWorkload" id="collectorWorkload">
    <div class="collectorWorkload-wrap">
      <div class="header">
        <el-form :model="searchForm" label-width="60px">
          <el-form-item
            label="统计时间"
            prop="statisticalTime"
            label-width="70px"
          >
            <el-date-picker
              :picker-options="{ shortcuts: G_datePickerShortcuts }"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              v-model="statisticalTime"
              @change="search"
              :clearable="false"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="" prop="" class="radioForm">
            <div class="titleRadio">
              <el-radio-group
                v-model.trim="searchForm.personGroup"
                @change="radioChange"
              >
                <el-radio :label="0">所有</el-radio>
                <el-radio :label="1">个人</el-radio>
                <el-radio :label="2">团体</el-radio>
              </el-radio-group>
            </div>
          </el-form-item>
          <el-form-item label="单位" prop="companyCode">
            <el-cascader
              ref="company_cascader_ref"
              v-model="searchForm.companyCode"
              :filter-method="filterMethod"
              :options="companyList"
              :props="{ multiple: false }"
              clearable
              filterable
              size="small"
              collapse-tags
              @change="companyChange"
            >
            </el-cascader>
          </el-form-item>
        </el-form>
        <ButtonGroup
          :btnList="['统计', '导出']"
          @statistics="search"
          @exports="exports"
        ></ButtonGroup>
      </div>
      <div id="printDiv">
        <div class="titlewrap">
          <p class="titleP">采集员工作量统计报表:</p>
          <p class="contP">
            <span class="otherSpsn"
              >本次体检人数：{{ allList.populationStatistics.totalNum }}</span
            >
            <span class="sexSpan"
              >男：{{ allList.populationStatistics.maleNum }}</span
            >
            <span class="sexSpan"
              >女：{{ allList.populationStatistics.femaleNum }}</span
            >
            <span class="otherSpsn"
              >已发报告人数：{{
                allList.populationStatistics.reportedNum
              }}</span
            >
            <span class="otherSpsn"
              >团体检查人数:{{ allList.populationStatistics.companyNum }}</span
            >
            <span class="otherSpsn"
              >团体已发报告人数:{{
                allList.populationStatistics.companyReportedNum
              }}</span
            >
          </p>
        </div>
        <div class="contDiv" id="contDiv">
          <PublicTable
            :isSortShow="false"
            :viewTableList.sync="tableData"
            :theads.sync="theads"
          >
          </PublicTable>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import PublicTable from '@/components/publicTable.vue';
import ButtonGroup from './components/buttonGroup.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
export default {
  name: 'collectorWorkload',
  components: { PublicTable, ButtonGroup },
  data() {
    return {
      regNo: '',

      searchForm: {
        beginDate: '',
        endDate: '',
        personGroup: 0,
        companyCode: '',
        itemClsCode: '',
        doctorName: '',
        deptCode: ''
      },
      statisticalTime: [dataUtils.getDate(), dataUtils.getDate()],
      companyList: [],
      theads: {},
      allList: {
        populationStatistics: {
          totalNum: 0,
          maleNum: 0,
          femaleNum: 0,
          reportedNum: 0,
          companyNum: 0,
          companyReportedNum: 0
        },
        samples: [],
        gatherOperatorSamples: [
          {
            gatherOperator: '',
            sampleNums: [],
            totalNum: 0
          }
        ]
      },
      tableData: []
    };
  },
  computed: {
    ...mapGetters(['G_datePickerShortcuts'])
  },
  mounted() {
    this.getCompany();
  },

  methods: {
    //获取单位下拉
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) return;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.statisticalTime = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.search();
    },
    //统计查询
    search() {
      if (!this.statisticalTime) {
        this.searchForm.beginDate = '';
        this.searchForm.endDate = '';
      } else {
        this.searchForm.beginDate = this.statisticalTime[0];
        this.searchForm.endDate = this.statisticalTime[1];
      }
      let { companyCode, ...surplus } = this.searchForm;
      let parameter = {
        companyCode: companyCode[0],
        ...surplus
      };
      this.$ajax
        .post(this.$apiUrls.GetGatherOperatorWorkload, parameter)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.allList = returnData;
          console.log('[  this.allList ]-154', this.allList);
          if (this.allList.gatherOperatorSamples.length >= 1) {
            var obj = {};
            var thead = {
              0: '采集员',
              totalNum: '总标本数'
            };
            //获取动态表头并拼接
            returnData.samples.forEach((item, key) => {
              obj[key + 1] = item;
            });
            this.theads = Object.assign(thead, obj);
            // console.log(obj);
            //把多维数组拼接成一维数组
            var newData = [];
            returnData.gatherOperatorSamples.forEach((item, index) => {
              var dataObj = {};
              var dataObj2 = {};
              dataObj = {
                0: item.gatherOperator,
                totalNum: item.totalNum
              };
              item.sampleNums.forEach((items, idx) => {
                dataObj2[idx + 1] = items;
              });
              newData.push(Object.assign(dataObj, dataObj2));
            });
            console.log(newData);
            this.tableData = newData;
            this.$message({
              message: '成功!',
              type: 'success',
              showClose: true
            });
          } else {
            this.theads = {};
            this.tableData = [];
            this.$message({
              message: '暂无数据!',
              type: 'success',
              showClose: true
            });
            return;
          }
        });
    },
    // 单选框切换
    radioChange() {
      if (this.searchForm.personGroup === 1) {
        this.searchForm.companyCode = '';
      }
      this.search();
    },
    exports() {
      if (this.tableData.length == 0) {
        return this.$message({
          message: '请选择至少一条数据进行操作！',
          type: 'warning',
          showClose: true
        });
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.tableData.map((item, i) => {
            item.index = i + 1;
          });
          this.$nextTick(function () {
            this.export2Excel();
          });
        })
        .catch(() => {});
    },
    // 数据写入excel
    export2Excel() {
      var that = this;
      require.ensure([], () => {
        const { export_json_to_excel } = require('@/utils/Export2Excel'); // 这里必须使用绝对路径，使用@/+存放export2Excel的路径
        const tHeader = Object.values(this.theads); // 导出的表头名信息
        const filterVal = Object.keys(this.theads); // 导出的表头字段名
        const list = that.tableData;
        const data = that.formatJson(filterVal, list);
        console.log(data);
        const name = '采集员工作量统计报表' + dataUtils.getNowDateTiemNo();
        export_json_to_excel(tHeader, data, name);
      });
    },
    // 格式转换
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) => filterVal.map((j) => v[j]));
    }
  }
};
</script>
<style lang="less" scoped>
.collectorWorkload {
  .collectorWorkload-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 15px !important;
    font-family: PingFangSC-Semibold;
    font-size: 14px;
    color: #2d3436;
    background: #fff;
  }
  .header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    height: 40px;
    line-height: 40px;
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #2d3436;
    .el-form {
      display: flex;
      flex-direction: row;
      margin-right: 10px;
    }
    .el-form-item {
      margin-bottom: 0;
    }
    .radioForm {
      /deep/.el-form-item__content {
        margin-left: 20px !important;
        .titleRadio {
          width: 210px;
          .el-radio {
            margin-right: 16px;
          }
        }
      }
    }
  }
  #printDiv {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    .titlewrap {
      height: 60px;
      line-height: 60px;
      display: flex;
      flex-direction: row;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #2d3436;
      .titleP {
        font-family: PingFangSC-Medium;
        font-size: 18px;
        width: 224px;
        font-weight: 700;
      }
      .contP {
        flex: 1;
        display: flex;
        flex-direction: row;
        .otherSpsn {
          flex: 1;
        }
        .sexSpan {
          width: 110px;
        }
      }
    }
    /deep/.el-table__footer-wrapper tbody td.el-table__cell,
    .el-table__header-wrapper tbody td.el-table__cell {
      background: #cbddf5;
      border-radius: 4px;
      font-family: PingFangSC-Semibold;
      color: #2d3436;
    }
    .contDiv {
      flex: 1;
      overflow: auto;
    }
  }
}
</style>
