<template>
  <!-- 审核员工作量报表 -->
  <div class="approveEmployeeWorkload">
    <div class="main">
      <div class="searchBar">
        <el-form ref="searchForm" :inline="true" :model="searchInfo">
          <el-row justify="space-between" type="flex">
            <el-col :span="24">
              <el-form-item label="统计时间">
                <el-date-picker
                  :picker-options="{ shortcuts: G_datePickerShortcuts }"
                  type="daterange"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  size="small"
                  v-model="searchInfo.date"
                  @change="search"
                  :clearable="false"
                >
                </el-date-picker>
              </el-form-item>

              <el-form-item>
                <el-radio-group
                  v-model="searchInfo.personGroup"
                  @change="radioChange"
                >
                  <el-radio :label="0">所有</el-radio>
                  <el-radio :label="1">个人</el-radio>
                  <el-radio :label="2">团体</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="单位" label-width="60px">
                <el-cascader
                  ref="company_cascader_ref"
                  v-model="searchInfo.companyCode"
                  :filter-method="filterMethod"
                  :disabled="isPersonal"
                  :options="companyList"
                  :props="{ multiple: false }"
                  clearable
                  filterable
                  size="small"
                  collapse-tags
                  @change="companyChange"
                >
                </el-cascader>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row justify="space-between" type="flex">
            <el-col :span="24">
              <el-form-item label="审核医生">
                <el-select
                  placeholder="请选择"
                  size="small"
                  filterable
                  clearable
                  v-model="searchInfo.doctorName"
                  @change="search"
                >
                  <el-option
                    v-for="item in G_sysOperator"
                    :key="item.label"
                    :label="item.label"
                    :value="item.label"
                  >
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item>
                <el-button
                  @click="search"
                  class="blue_btn btn"
                  size="small"
                  icon="iconfont icon-tongji"
                  >统计</el-button
                >
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="main-title">
        <h3>审核员工作量报表:</h3>
        <!-- <div class="title-wrap">
          <span>本次体检人数：{{ dataInfo.totalNum }}</span>
          <span>男：{{ dataInfo.maleNum }}</span>
          <span>女：{{ dataInfo.femaleNum }}</span>
          <span>已发报告人数：{{ dataInfo.reportedNum }}</span>
          <span>团体检查人数：{{ dataInfo.companyNum }}</span>
          <span>团体已发报告人数：{{ dataInfo.companyReportedNum }}</span>
        </div> -->
      </div>
      <div class="main-table">
        <div class="table-list" v-loading="loading">
          <template v-if="report && report.length > 0">
            <div
              class="table-item"
              v-for="(item, index) in report"
              :key="index"
            >
              <div class="cell_blue item-text">{{ item.deptName }}</div>
              <div class="publicTable">
                <PublicTable
                  :isSortShow="false"
                  :theads="theads"
                  :viewTableList.sync="item.doctors"
                ></PublicTable>
              </div>
            </div>
          </template>

          <template v-else>
            <el-empty description="暂无记录"></el-empty>
          </template>
        </div>
        <div class="total">
          <span>总计：</span>
          <span>{{ total }}人</span>
          <span>{{ totalPrice }}元</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SearchBar from './components/searchBar.vue';
import PublicTable from '@/components/publicTable.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';

export default {
  name: 'approveEmployeeWorkload',
  components: {
    SearchBar,
    PublicTable
  },
  data() {
    return {
      searchInfo: {
        date: [new Date(), new Date()],
        personGroup: 0,
        companyCode: '',
        doctorName: ''
      },
      theads: {
        doctorName: '审核医生',
        // deptName: "科室名称",
        totalNum: '人次',
        price: '金额'
      },
      tableData: [],
      report: [],
      total: 0,
      totalPrice: 0,
      isPersonal: false,
      companyList: [],
      loading: false
    };
  },
  created() {
    this.getCompanyList();
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_sysOperator', 'G_datePickerShortcuts'])
  },
  methods: {
    /**
     * @author: justin
     * @description: 获取单位下拉
     * @return {*}
     */
    getCompanyList() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) return;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.searchInfo.date = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.statistics();
    },
    search() {
      if (this.loading) return;
      this.statistics(); //汇总表
    },
    // 查询统计
    statistics() {
      let [beginDate, endDate] = this.searchInfo.date;
      let data = {
        beginDate: dataUtils.dateToString(beginDate),
        endDate: dataUtils.dateToString(endDate),
        personGroup: this.searchInfo.personGroup,
        companyCode: this.searchInfo.companyCode[0],
        itemClsCode: '',
        doctorName: this.searchInfo.doctorName,
        deptCode: ''
      };
      this.loading = true;
      this.$ajax
        .post(this.$apiUrls.AuditDoctorWorkloadReport, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.report = returnData.dept.map((item) => {
            item.doctors.push({
              doctorName: '合计',
              deptName: null,
              totalNum: item.totalNum,
              price: item.totalPrice
            });
            return {
              ...item
            };
          });
          this.total = returnData.totalNum;
          this.totalPrice = returnData.totalPrice;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 单选框切换
    radioChange() {
      if (this.searchInfo.personGroup === 1) {
        this.searchInfo.companyCode = '';
      }
      this.statistics();
    }
  }
};
</script>

<style lang="less" scoped>
.approveEmployeeWorkload {
  color: #2d3436;
  .main {
    height: 100%;
    background: #fff;
    padding: 18px 10px;
    display: flex;
    flex-direction: column;

    .searchBar {
      /deep/.el-form-item {
        margin-bottom: 5px;

        .el-form-item__label {
          color: #2d3436;
          font-weight: bold;
        }
      }
      .input {
        width: 100%;
      }
      .btn {
        padding: 6.5px 10px;
      }
    }
  }
  .main-title {
    margin-bottom: 18px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    h3 {
      font-size: 18px;
    }
  }
  .title-wrap {
    display: flex;
    align-items: center;
    font-size: 14px;
    span {
      width: 180px;
    }
  }
  .main-table {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
  }
  .table-list {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .table-item {
    margin-bottom: 18px;
  }
  .item-text {
    margin-bottom: 10px;
    font-size: 14px;
  }
  .publicTable {
    border: 1px solid rgba(178, 190, 195, 0.5);
    border-radius: 4px;
    overflow: auto;
  }
  .total {
    background: #cbddf5;
    font-size: 14px;
    font-weight: 600;
    padding: 10px;
    display: flex;
    align-items: center;
    border-radius: 4px;
    span {
      flex: 1;
    }
  }
  /deep/.el-table--scrollable-y .el-table__body-wrapper {
    height: auto !important;
  }
  /deep/.el-table__body-wrapper {
    height: auto !important;
  }
}
</style>
