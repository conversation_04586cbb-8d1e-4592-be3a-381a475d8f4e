<template>
  <div class="departWorkloadPressure" id="departWorkloadPressure">
    <div class="departWorkloadPressure-wrap">
      <div class="header">
        <el-form :model="searchForm" label-width="60px">
          <el-form-item
            label="统计时间"
            prop="statisticalTime"
            label-width="70px"
          >
            <el-date-picker
              :picker-options="{ shortcuts: G_datePickerShortcuts }"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              v-model="statisticalTime"
              @change="search"
              :clearable="false"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="" prop="" class="radioForm">
            <div class="titleRadio">
              <el-radio-group
                v-model.trim="searchForm.personGroup"
                @change="radioChange"
              >
                <el-radio :label="0">所有</el-radio>
                <el-radio :label="1">个人</el-radio>
                <el-radio :label="2">团体</el-radio>
              </el-radio-group>
            </div>
          </el-form-item>
          <el-form-item label="单位" prop="companyCode">
            <el-cascader
              ref="company_cascader_ref"
              v-model="searchForm.companyCode"
              :filter-method="filterMethod"
              :options="companyList"
              :props="{ multiple: false }"
              clearable
              filterable
              size="small"
              collapse-tags
              @change="companyChange"
            >
            </el-cascader>
          </el-form-item>
          <!-- <el-form-item label="外检" prop="external">
          <el-select
            placeholder="请选择"
            size="small"
            filterable
            clearable
            v-model="searchForm.external"
            @change="search"
          >
            <el-option
              v-for="(item, key) in externalList"
              :key="key"
              :label="item"
              :value="key"
            >
            </el-option>
          </el-select>
        </el-form-item> -->
        </el-form>
        <ButtonGroup
          :btnList="['统计', '导出']"
          @statistics="search"
          @exports="exports"
        ></ButtonGroup>
      </div>
      <div id="printDiv">
        <div class="titlewrap">
          <p class="titleP">体检科室压力工作量报表:</p>
          <p class="contP">
            <span class="otherSpsn"
              >本次体检人数：{{ allList.populationStatistics.totalNum }}</span
            >
            <span class="sexSpan"
              >男：{{ allList.populationStatistics.maleNum }}</span
            >
            <span class="sexSpan"
              >女：{{ allList.populationStatistics.femaleNum }}</span
            >
            <span class="otherSpsn"
              >已发报告人数：{{
                allList.populationStatistics.reportedNum
              }}</span
            >
            <span class="otherSpsn"
              >团体检查人数:{{ allList.populationStatistics.companyNum }}</span
            >
            <span class="otherSpsn"
              >团体已发报告人数:{{
                allList.populationStatistics.companyReportedNum
              }}</span
            >
          </p>
        </div>
        <div class="contDiv" id="contDiv">
          <div class="tableDiv">
            <div v-for="(item, index) in allList.deptCombs" :key="index">
              <p class="titleP">{{ item.deptName }}</p>
              <PublicTable
                :isSortShow="false"
                :viewTableList.sync="item.combStatisticses"
                :theads.sync="theads"
              >
                <template #doneRate="{ scope }">
                  <div>
                    {{ scope.row.doneRate + '%' }}
                  </div>
                </template>
              </PublicTable>
            </div>
          </div>

          <div class="spanDiv footerr">
            <span>总计:</span>
            <span>登记{{ allList.registerNum }}人</span>
            <span>完成{{ allList.doneNum }}人</span>
            <span>拒检{{ allList.discardNum }}人</span>
            <span>剩余{{ allList.remainNum }}人</span>
            <span></span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import PublicTable from '@/components/publicTable.vue';
import ButtonGroup from './components/buttonGroup.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
export default {
  name: 'departWorkloadPressure',
  components: { PublicTable, ButtonGroup },
  data() {
    return {
      searchForm: {
        beginDate: '',
        endDate: '',
        personGroup: 0,
        companyCode: '',
        itemClsCode: '',
        doctorName: '',
        deptCode: ''
      },
      externalList: {
        0: '外检',
        1: '内检'
      },
      statisticalTime: [dataUtils.getDate(), dataUtils.getDate()],
      companyList: [],
      theads: {
        combName: '项目名称',
        registerNum: '登记人数',
        doneNum: '完成人数',
        discardNum: '拒检人数',
        remainNum: '剩余人数',
        doneRate: '完成率（%）'
      },
      allList: {
        populationStatistics: {
          totalNum: 0,
          maleNum: 0,
          femaleNum: 0,
          reportedNum: 0,
          companyNum: 0,
          companyReportedNum: 0
        },
        deptCombs: [
          //   {
          //     deptName: "",
          //     combStatisticses: [
          //       {
          //         combName: "",
          //         registerNum: 0,
          //         doneNum: 0,
          //         discardNum: 0,
          //         remainNum: 0,
          //         doneRate: 0,
          //       },
          //     ],
          //   },
        ],
        registerNum: 0,
        doneNum: 0,
        discardNum: 0,
        remainNum: 0
      },
      exportData: []
    };
  },
  computed: {
    ...mapGetters(['G_datePickerShortcuts'])
  },
  mounted() {
    this.getCompany();
  },

  methods: {
    //获取单位下拉
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) return;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.statisticalTime = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.search();
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    //统计查询
    search() {
      if (!this.statisticalTime) {
        this.searchForm.beginDate = '';
        this.searchForm.endDate = '';
      } else {
        this.searchForm.beginDate = this.statisticalTime[0];
        this.searchForm.endDate = this.statisticalTime[1];
      }
      let { companyCode, ...surplus } = this.searchForm;
      let parameter = {
        companyCode: companyCode[0],
        ...surplus
      };
      this.$ajax.post(this.$apiUrls.GetDeptPressure, parameter).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.allList = returnData;
        //把多维数组拼接成一维数组
        var newData = [];
        returnData.deptCombs.forEach((item, index) => {
          item.combStatisticses.forEach((items, idx) => {
            newData.push({
              deptName: item.deptName,
              combName: items.combName,
              registerNum: items.registerNum,
              doneNum: items.doneNum,
              discardNum: items.discardNum,
              remainNum: items.remainNum,
              doneRate: items.doneRate + '%'
            });
          });
        });
        console.log(newData);
        this.exportData = newData;
        if (this.allList?.deptCombs.length >= 1) {
          this.$message({
            message: '成功!',
            type: 'success',
            showClose: true
          });
        } else {
          this.$message({
            message: '暂无数据!',
            type: 'success',
            showClose: true
          });
        }
      });
    },
    // 单选框切换
    radioChange() {
      if (this.searchForm.personGroup === 1) {
        this.searchForm.companyCode = '';
      }
      this.search();
    },
    //导出
    exports() {
      if (this.exportData.length == 0) {
        return this.$message({
          message: '请选择至少一条数据进行操作！',
          type: 'warning',
          showClose: true
        });
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.exportData.map((item, i) => {
            item.index = i + 1;
          });
          this.$nextTick(function () {
            this.export2Excel();
          });
        })
        .catch(() => {});
    },
    // 数据写入excel
    export2Excel() {
      let theads = {
        deptName: '科室',
        combName: '项目名称',
        registerNum: '登记人数',
        doneNum: '完成人数',
        discardNum: '拒检人数',
        remainNum: '剩余人数',
        doneRate: '完成率（%）'
      };
      var that = this;
      require.ensure([], () => {
        const { export_json_to_excel } = require('@/utils/Export2Excel'); // 这里必须使用绝对路径，使用@/+存放export2Excel的路径
        const tHeader = Object.values(theads); // 导出的表头名信息
        const filterVal = Object.keys(theads); // 导出的表头字段名
        const list = that.exportData;
        const data = that.formatJson(filterVal, list);
        console.log(data);
        const name = '体检科室压力工作量报表' + dataUtils.getNowDateTiemNo();
        export_json_to_excel(tHeader, data, name);
      });
    },
    // 格式转换
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) => filterVal.map((j) => v[j]));
    }
  }
};
</script>
<style lang="less" scoped>
.departWorkloadPressure {
  .departWorkloadPressure-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 15px !important;
    font-family: PingFangSC-Semibold;
    font-size: 14px;
    color: #2d3436;
    background: #fff;
  }
  .header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    height: 40px;
    line-height: 40px;
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #2d3436;
    .el-form {
      display: flex;
      flex-direction: row;
      margin-right: 10px;
    }
    .el-form-item {
      margin-bottom: 0;
    }
    .radioForm {
      /deep/.el-form-item__content {
        margin-left: 20px !important;
        .titleRadio {
          width: 210px;
          .el-radio {
            margin-right: 16px;
          }
        }
      }
    }
  }
  #printDiv {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;

    .titlewrap {
      height: 60px;
      line-height: 60px;
      display: flex;
      flex-direction: row;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #2d3436;
      .titleP {
        font-family: PingFangSC-Medium;
        font-size: 18px;
        width: 224px;
        font-weight: 700;
      }
      .contP {
        flex: 1;
        display: flex;
        flex-direction: row;
        .otherSpsn {
          flex: 1;
        }
        .sexSpan {
          width: 110px;
        }
      }
    }
    .contDiv {
      flex: 1;
      overflow: auto;
      display: flex;
      flex-direction: column;
      .tableDiv {
        flex: 1;
        overflow: auto;
      }
      .titleP {
        font-family: PingFangSC-Regular;
        color: #1770df;
        line-height: 32px;
        font-weight: 700;
      }

      /deep/.el-table__footer-wrapper tbody td.el-table__cell,
      .el-table__header-wrapper tbody td.el-table__cell {
        font-family: PingFangSC-Regular;
        color: #1770df !important;
      }
    }
    /deep/.el-table--scrollable-y .el-table__body-wrapper {
      height: auto !important;
    }
    /deep/.el-table__body-wrapper {
      height: auto !important;
    }
    .publicTable_com {
      border: 1px solid #ccc;
      border-radius: 4px;
    }
    .spanDiv {
      width: 100%;
      display: flex;
      height: 38px;
      line-height: 38px;
      border-bottom: 1px solid #ccc;
      padding: 0 20px;
      & > span {
        flex: 1;
      }
    }
    .footerr {
      background: #cbddf5;
      padding-right: 30px;
      font-weight: 700;
    }
  }
}
</style>
