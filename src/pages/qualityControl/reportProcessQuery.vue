<template>
  <!-- 报告进程查询 -->
  <div class="reportProcessQuery">
    <div class="selection-bar">
      <ul class="selection-tabs">
        <li
          @click="clickSelectionTabs(0)"
          :class="liActive === 0 ? 'li-active' : ''"
        >
          选择组合
        </li>
        <li
          @click="clickSelectionTabs(1)"
          :class="liActive === 1 ? 'li-active' : ''"
        >
          选择项目
        </li>
      </ul>
      <div class="operate-bar">
        <label style="width: 86px">项目分类</label>
        <el-select
          v-model="codeItemCls"
          placeholder="请选择"
          size="small"
          style="width: 100%"
          @change="clsChange"
          clearable
          filterable
          @clear="clsChange"
        >
          <el-option
            v-for="item in G_codeItemCls"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <!-- <ButtonGroup :btnList="['清空']" @clear="clear" /> -->
      </div>
      <div class="selection-table">
        <PublicTable
          v-if="liActive === 0"
          isCheck
          :isSortShow="false"
          :theads="theadItemComb"
          :viewTableList.sync="tableDataItemComb"
          ref="itemComb_ref"
          :columnWidth="columnWidth"
          @selectionChange="selectChange"
        >
        </PublicTable>
        <PublicTable
          v-else
          isCheck
          :isSortShow="false"
          :theads="theadItem"
          :viewTableList.sync="tableDataItem"
          ref="item_ref"
          :columnWidth="columnWidth"
          @selectionChange="selectChange"
        ></PublicTable>
      </div>
    </div>
    <div class="content-wrap">
      <div class="content-operate">
        <div class="operate-item">
          <label style="width: 152px">起止进程</label>
          <el-select
            v-model="infoData.beginProcess"
            placeholder="起始进程"
            size="small"
            class="width"
          >
            <el-option
              v-for="item in G_ProcessType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <span style="margin: 0 6px">-</span>
          <el-select
            v-model="infoData.endProcess"
            placeholder="终止进程"
            size="small"
            class="width"
          >
            <el-option
              v-for="item in G_ProcessType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <div class="operate-item">
          <label style="width: 84px">起止日期</label>
          <el-date-picker
            :picker-options="{ shortcuts: G_datePickerShortcuts }"
            v-model="infoData.date"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
            class="width"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </div>
        <div class="operate-item" v-if="G_config.physicalMode.includes('普检')">
          <label style="width: 94px">体检类型</label>
          <el-select
            v-model="infoData.peCls"
            placeholder="请选择"
            size="small"
            class="width"
            clearable
            filterable
          >
            <el-option
              v-for="item in G_peClsList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <!-- <div class="operate-item">
          <label style="width: 44px">外检</label>
          <el-select
            v-model="infoData.outsideUploaded"
            placeholder="请选择"
            size="small"
            class="width"
          >
            <el-option label="是" :value="true"> </el-option>
            <el-option label="否" :value="false"> </el-option>
          </el-select>
        </div> -->
      </div>
      <div class="content-operate">
        <!-- <div class="operate-item">
          <label style="width: 40px">院区</label>
          <el-select
            v-model="infoData.hospCode"
            placeholder="请选择"
            size="small"
            class="width"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div> -->
        <div class="operate-item">
          <el-radio-group v-model="infoData.peType" class="radio-group">
            <el-radio :label="0">所有</el-radio>
            <el-radio :label="1">个人</el-radio>
            <el-radio :label="2">团体</el-radio>
          </el-radio-group>
        </div>
        <div class="operate-item">
          <label style="width: 40px">单位</label>
          <el-cascader
            ref="company_cascader_ref"
            v-model="infoData.companyCode"
            :filter-method="filterMethod"
            :options="companyList"
            :props="{ multiple: false }"
            clearable
            filterable
            size="small"
            collapse-tags
            @change="companyChange"
          >
          </el-cascader>
        </div>
        <div class="operate-item">
          <span style="width: 40px">间隔≥</span>
          <el-input
            v-model="infoData.interval"
            size="small"
            style="width: 40px; margin-left: 10px"
          ></el-input>
          天
        </div>
        <div class="operate-item">
          <el-input
            v-model="infoData.keyword"
            size="small"
            style="width: 160px; margin-left: 10px"
            placeholder="体检号/姓名"
            clearable
          ></el-input>
        </div>
        <div class="operate-item">
          <ButtonGroup
            :btnList="['查询', '导出']"
            @search="search"
            @exports="exports"
          />
        </div>
      </div>
      <div class="content-tabs">
        <span
          @click="clickTabs(0)"
          :class="spanActive === 0 ? 'span-active' : ''"
          >体检报告进程统计表</span
        >
        <span
          @click="clickTabs(1)"
          :class="spanActive === 1 ? 'span-active' : ''"
          >体检报告进程分析表</span
        >
      </div>
      <div class="content-table">
        <PublicTable
          v-if="spanActive === 0"
          :theads="theads"
          :viewTableList.sync="tableData"
          :columnWidth="tableDataColumnWidth"
          :columnSort="['interval']"
          ref="view_Ref"
        >
          <template #sex="{ scope }">
            {{ G_EnumList['Sex'][scope.row.sex] }}
          </template>
        </PublicTable>
        <PublicTable
          v-else
          :isSortShow="false"
          :theads="theadsAnalysis"
          :viewTableList.sync="tableDataAnalysis"
          ref="view_Ref"
        >
        </PublicTable>
      </div>
    </div>
  </div>
</template>

<script>
import ButtonGroup from './components/buttonGroup.vue';
import PublicTable from '@/components/publicTable.vue';
import { mapGetters } from 'vuex';
import { dataUtils } from '../../common';
import ExportExcel from '@/common/excel/exportExcel';
export default {
  name: 'reportProcessQuery',
  mixins: [ExportExcel],
  components: {
    PublicTable,
    ButtonGroup
  },
  data() {
    return {
      infoData: {
        date: [dataUtils.getDate(), dataUtils.getDate()],
        peCls: '',
        peType: 0,
        companyCode: '',
        beginProcess: 0,
        endProcess: 5,
        hospCode: '',
        outsideUploaded: '',
        interval: 1,
        combItemArray: [],
        combItemType: 0,
        keyword: ''
      },
      options: [],
      theadItemComb: {
        combCode: '组合代码',
        combName: '组合名称',
        clsName: '项目分类'
      },
      theadItem: {
        itemCode: '项目代码',
        itemName: '项目名称',
        clsName: '项目分类'
      },
      tableDataItem: [],
      tableDataItemCopy: [],
      tableDataItemCombCopy: [],
      tableDataItemComb: [],
      liActive: 0,
      codeItemCls: '',
      spanActive: 0,
      processList: [],
      companyList: [],
      theads: {
        regNo: '体检号',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        cardNo: '证件号',
        auditDoctor: '操作者',
        interval: '时间间隔',
        beginDate: '开始时间',
        endDate: '结束时间',
        companyName: '单位名称',
        guidanceRecycleNote: '备注'
      },
      tableDataColumnWidth: {
        regNo: 120,
        name: 90,
        sex: 50,
        age: 50,
        cardNo: 165,
        auditDoctor: 90,
        interval: 100,
        beginDate: 160,
        endDate: 160,
        companyName: 200,
        guidanceRecycleNote: 200
      },
      theadsAnalysis: {
        countLessThanThree: '间隔小于3天数量',
        countThreeToSix: '间隔3-6天数量',
        countSixToNine: '间隔6-9天数量',
        countNineToTwelve: '间隔9-12天数量',
        countMoreThanTwelve: '间隔大于12天数量',
        countAllReport: '总数量',
        medianDays: '中位天数',
        ninetyPercentDays: '90分位天数',
        maxDays: '最大天数',
        minDays: '最小天数',
        avgDays: '平均天数'
      },
      tableData: [],
      columnWidth: {
        combName: 180,
        itemName: 160
      },
      selectData: [],
      tableDataAnalysis: []
    };
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_codeItemCls',
      'G_peClsList',
      'G_ProcessType',
      'G_datePickerShortcuts',
      'G_config'
    ])
  },
  created() {
    this.getItemComb();
    this.getCompany();
  },
  methods: {
    // 获取组合分类
    getItemComb() {
      this.$ajax.post(this.$apiUrls.ItemComb_ItemCls, []).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.tableDataItemComb = returnData || [];
        this.tableDataItemCombCopy = returnData || [];
      });
    },
    // 获取项目分类
    getItem() {
      this.$ajax.post(this.$apiUrls.Item_ItemCls, []).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.tableDataItem = returnData || [];
        this.tableDataItemCopy = returnData || [];
      });
    },
    // 获取单位
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) return;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.infoData.date = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.search();
    },
    // 组合、项目tabs切换
    clickSelectionTabs(val) {
      this.liActive = val;
      val === 0 ? this.getItemComb() : this.getItem();
      this.codeItemCls = '';
    },
    // 项目分类切换
    clsChange() {
      if (this.liActive === 0) {
        if (this.codeItemCls) {
          this.tableDataItemComb = this.tableDataItemCombCopy.filter((item) => {
            return item.clsCode === this.codeItemCls;
          });
        } else {
          this.tableDataItemComb = this.tableDataItemCombCopy;
        }
      } else {
        if (this.codeItemCls) {
          this.tableDataItem = this.tableDataItemCopy.filter((item) => {
            return item.clsCode === this.codeItemCls;
          });
        } else {
          this.tableDataItem = this.tableDataItemCopy;
        }
      }
    },
    // 表格勾选
    selectChange(val) {
      if (this.liActive === 0) {
        this.selectData = val.map((item) => {
          return item.combCode;
        });
      } else {
        this.selectData = val.map((item) => {
          return item.itemCode;
        });
      }
    },
    // 体检报告进程统计
    getReportProcessQuery() {
      console.log(this.infoData.peCls);
      let data = {
        peCls:
          this.infoData.peCls !== 0 && !this.infoData.peCls
            ? -1
            : this.infoData.peCls,
        peType: this.infoData.peType,
        companyCode: this.infoData.companyCode[0]
          ? this.infoData.companyCode[0]
          : '',
        beginProcess: this.infoData.beginProcess,
        endProcess: this.infoData.endProcess,
        hospCode: this.infoData.hospCode,
        outsideUploaded: this.infoData.outsideUploaded,
        interval: this.infoData.interval || 1,
        beginDate: this.infoData.date[0],
        endDate: this.infoData.date[1],
        combItemArray: this.selectData,
        combItemType: this.liActive,
        keyword: ((this.infoData.keyword ?? '') + '').trim()
      };
      console.log('this.infoData: ', data);
      this.$ajax.post(this.$apiUrls.PeReportProcessQuery, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.tableData = returnData || [];
      });
    },
    // 体检报告进程分析
    getPeReportProcessAnalyze() {
      this.tableDataAnalysis = [];
      let data = {
        peCls:
          this.infoData.peCls !== 0 && !this.infoData.peCls
            ? -1
            : this.infoData.peCls,
        peType: this.infoData.peType,
        companyCode: this.infoData.companyCode[0]
          ? this.infoData.companyCode[0]
          : '',
        beginProcess: this.infoData.beginProcess,
        endProcess: this.infoData.endProcess,
        hospCode: this.infoData.hospCode,
        outsideUploaded: this.infoData.outsideUploaded,
        interval: this.infoData.interval || 1,
        beginDate: this.infoData.date[0],
        endDate: this.infoData.date[1],
        combItemArray: this.selectData,
        combItemType: this.liActive,
        keyword: ((this.infoData.keyword ?? '') + '').trim()
      };
      this.$ajax.post(this.$apiUrls.PeReportProcessAnalize, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.tableDataAnalysis.push({
          ...returnData,
          countLessThanThree: this.toPoint(returnData.countLessThanThree),
          countThreeToSix: this.toPoint(returnData.countThreeToSix),
          countSixToNine: this.toPoint(returnData.countSixToNine),
          countNineToTwelve: this.toPoint(returnData.countNineToTwelve),
          countMoreThanTwelve: this.toPoint(returnData.countMoreThanTwelve)
        });
      });
    },
    // 处理百分数
    toPoint(val) {
      if (val) {
        return Number(val * 100).toFixed(2) + '%';
      }
    },
    // 查询
    search() {
      if (this.spanActive === 0) {
        this.getReportProcessQuery();
      } else {
        this.getPeReportProcessAnalyze();
      }
    },
    // 导出
    exports() {
      let thead, tableData, fileName;
      if (this.spanActive === 0) {
        thead = this.theads;
        fileName = '体检报告进程统计表';
        tableData = this.tableData;
      } else {
        thead = this.theadsAnalysis;
        fileName = '体检报告进程分析表';
        tableData = this.tableDataAnalysis;
      }
      if (tableData.length == 0) {
        return this.$message.error('没有数据可以导出');
      }
      this.exportExcel(fileName, fileName, thead, tableData, '');
    },
    // 进程统计、进程分析tabs切换
    clickTabs(val) {
      this.spanActive = val;
    },
    // 清空
    clear() {
      if (this.liActive === 0) {
        this.$nextTick(() => {
          this.$refs.itemComb_ref.$refs.tableCom_Ref.clearSelection();
        });
      } else {
        this.$nextTick(() => {
          this.$refs.item_ref.$refs.tableCom_Ref.clearSelection();
        });
      }
    }
  }
  // watch: {
  //   G_ProcessType:{
  //     handler(newVal){
  //       this.infoData.processList = [...newVal];
  //     },
  //     immediate:true
  //   }
  // }
};
</script>

<style lang="less" scoped>
.reportProcessQuery {
  display: flex;
  color: #2d3436;
  .selection-bar {
    width: 280px;
    background: #fff;
    border-radius: 4px;
    margin-right: 10px;
    padding-top: 10px;
    display: flex;
    flex-direction: column;
  }
  .content-wrap {
    flex: 1;
    overflow: auto;
    background: #fff;
    border-radius: 4px;
    padding: 10px;
    display: flex;
    flex-direction: column;
  }
  .selection-tabs {
    font-size: 14px;
    display: flex;
    justify-content: center;
    border-bottom: 1px solid #d9dfe2;
    padding: 0 10px;
    li {
      padding: 8px 24px;
      margin: 0 10px;
      cursor: pointer;
      &.li-active {
        color: #1770df;
        font-weight: 600;
        border-bottom: 2px solid #1770df;
      }
    }
  }
  .operate-bar {
    display: flex;
    align-items: center;
    padding: 18px 10px;
    font-size: 14px;
    font-weight: 600;
  }
  .select {
    width: 100%;
    margin-right: 10px;
  }
  .width {
    width: 100%;
  }
  .selection-table {
    flex: 1;
    overflow: auto;
    margin: 0 10px 10px 10px;
    border: 1px solid #d9dfe2;
    border-radius: 4px;
  }
  .content-operate {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 10px;
  }
  .operate-item {
    display: flex;
    align-items: center;
    margin-right: 10px;
    &:last-child {
      margin-right: 0;
    }
  }
  .radio-group {
    width: 100%;
    display: flex;
    align-items: center;
  }
  .content-tabs {
    font-size: 18px;
    margin-bottom: 12px;
    span {
      cursor: pointer;
      &:nth-child(1) {
        padding-right: 14px;
        border-right: 1px solid #2d3436;
        margin-right: 14px;
      }
      &.span-active {
        color: #1770df;
        font-weight: 600;
      }
    }
  }
  .content-table {
    border: 1px solid #d9dfe2;
    border-radius: 4px;
    flex: 1;
    overflow: auto;
  }
}
</style>
