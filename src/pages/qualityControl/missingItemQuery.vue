<template>
  <!-- 漏项查询 -->
  <div class="missingItemQuery">
    <div class="main-wrap">
      <div class="search-wrap">
        <h3>体检漏项统计表：</h3>
        <div class="search-list">
          <div class="list-item">
            <span style="width: 70px">统计时间</span>
            <el-date-picker
              :picker-options="{ shortcuts: G_datePickerShortcuts }"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              size="small"
              v-model="searchInfo.date"
              class="input"
              @change="getMissingItem"
            >
            </el-date-picker>
          </div>
          <div style="width: 312px">
            <el-radio-group v-model="searchInfo.peType" @change="radioChange">
              <el-radio :label="0">所有</el-radio>
              <el-radio :label="1">个人</el-radio>
              <el-radio :label="2">团体</el-radio>
            </el-radio-group>
          </div>
          <div class="list-item">
            <span style="width: 36px">单位</span>
            <el-cascader
              ref="company_cascader_ref"
              v-model="searchInfo.companyCode"
              :filter-method="filterMethod"
              :options="companyList"
              :props="{ multiple: false }"
              clearable
              filterable
              size="small"
              collapse-tags
              @change="companyChange"
            >
            </el-cascader>
          </div>
          <ButtonGroup
            :btnList="['查询', '打印', '导出']"
            @search="getMissingItem"
            @prints="prints"
            @exports="exports"
          />
        </div>
      </div>
      <div class="main-table">
        <el-table
          ref="tableCom_Ref"
          style="width: 100%; color: #2d3436; font-weight: 600; font-size: 14px"
          size="small"
          :data="tableData"
          v-el-table-infinite-scroll="load"
          :header-cell-style="{
            background: '#d1e2f9',
            fontSize: '14px',
            color: '#2d3436'
          }"
          height="100%"
          highlight-current-row
        >
          <el-table-column type="selection" width="45" fixed></el-table-column>
          <el-table-column
            prop="regNo"
            label="体检号"
            width="130"
            fixed
          ></el-table-column>
          <el-table-column prop="name" label="姓名" fixed></el-table-column>
          <el-table-column prop="sex" label="性别" fixed></el-table-column>
          <el-table-column prop="age" label="年龄" fixed></el-table-column>
          <el-table-column
            :prop="th"
            :label="thead[th]"
            v-for="th in Object.keys(thead)"
            :key="th"
            width="300"
          >
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import ButtonGroup from './components/buttonGroup.vue';
import PublicTable from '@/components/publicTable.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
export default {
  name: 'missingItemQuery',
  components: {
    ButtonGroup,
    PublicTable
  },
  data() {
    return {
      searchInfo: {
        date: [new Date(), new Date()],
        peType: 0,
        companyCode: ''
      },
      thead: {},
      tableData: [],
      companyList: [],
      excelData: [],
      pageSize: 50,
      currentPage: 1
    };
  },
  computed: {
    ...mapGetters(['G_datePickerShortcuts'])
  },
  created() {
    this.getCompanyList();
  },
  beforeUpdate() {
    this.$nextTick(() => {
      // 在数据加载完，重新渲染表格，解决表格抖动
      this.$refs.tableCom_Ref.doLayout();
    });
  },
  methods: {
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    // 获取单位下拉
    getCompanyList() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) return;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.searchInfo.date = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.getMissingItem();
    },
    // 查询
    getMissingItem() {
      let [startTime, endTime] = this.searchInfo.date;
      let data = {
        beginDate: startTime,
        endDate: endTime,
        peType: this.searchInfo.peType,
        companyCode: this.searchInfo.companyCode[0] || ''
      };
      this.$ajax.post(this.$apiUrls.PeMissingItemQuery, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        if (!returnData) return;
        this.thead = returnData.header;
        this.tableData = returnData.bodyData;
      });
    },
    // 单选框切换
    radioChange() {
      if (this.searchInfo.peType === 1) {
        this.searchInfo.companyCode = '';
      }
      this.getMissingItem();
    },
    // 打印
    prints() {},
    // 导出
    exports() {
      let tableCom_Ref = this.$refs.tableCom_Ref;
      if (tableCom_Ref.selection.length == 0) {
        return this.$message({
          message: '请选择至少一条数据进行操作！',
          type: 'warning',
          showClose: true
        });
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.excelData = tableCom_Ref.selection; // multipleSelection是一个数组，存储表格中选择的行的数据。
          this.excelData.map((item, i) => {
            item.index = i + 1;
          });
          this.$nextTick(function () {
            this.export2Excel();
          });
        })
        .catch(() => {});
    },
    // 数据写入excel
    export2Excel() {
      var that = this;
      require.ensure([], () => {
        const { export_json_to_excel } = require('@/utils/Export2Excel'); // 这里必须使用绝对路径，使用@/+存放export2Excel的路径
        that.thead = {
          regNo: '体检号',
          name: '姓名',
          sex: '性别',
          age: '年龄',
          ...that.thead
        };
        const tHeader = Object.values(that.thead); // 导出的表头名信息
        const filterVal = Object.keys(that.thead); // 导出的表头字段名
        const list = that.excelData;
        const data = that.formatJson(filterVal, list);
        const name = '体检漏项统计表';
        export_json_to_excel(tHeader, data, name);
      });
    },
    // 格式转换
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) => filterVal.map((j) => v[j]));
    },
    // 滚动加载
    load() {
      console.log('加载表格分页');
      this.currentPage++;
      // if (this.currentPage * this.pageSize >= this.viewTableList.length) return;
      let loadSize = this.currentPage * this.pageSize;
      if (loadSize - this.tableData.length >= this.pageSize) return;
      let start = (this.currentPage - 1) * this.pageSize;
      let end = this.currentPage * this.pageSize;
      let pageData = this.tableData.slice(start, end);
      console.log(pageData);
      this.tableData.push(...pageData);
      console.log(this.tableData);

      let tableRef = this.$refs.tableCom_Ref;
      console.log(tableRef.store.states.isAllSelected);

      this.defaultCheckRow(pageData);
      console.log(tableRef.selection);
      if (tableRef.store.states.isAllSelected) {
        tableRef.toggleAllSelection();
      }
    }
  },
  watch: {
    viewTableList(n, o) {
      this.tableData = [];
      this.currentPage = 1;
      this.$nextTick(() => {
        if (!this.isOpenPage) {
          this.tableData = n;
          return;
        }
        if (this.currentPage * this.pageSize >= n.length) {
          this.tableData.push(...n);
          return;
        }
        let start = (this.currentPage - 1) * this.pageSize;
        let end = this.currentPage * this.pageSize;
        let pageData = n.slice(start, end);
        console.log(pageData);
        this.tableData.push(...pageData);
      });
    }
  }
};
</script>

<style lang="less" scoped>
.missingItemQuery {
  color: #2d3436;
  .main-wrap {
    width: 100%;
    height: 100%;
    background: #fff;
    padding: 18px 10px;
    display: flex;
    flex-direction: column;
  }
  .search-wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 18px;
    h3 {
      font-size: 18px;
      width: 190px;
    }
  }
  .search-list {
    display: flex;
    align-items: center;
  }
  .list-item {
    display: flex;
    align-items: center;
    margin-right: 10px;
    span {
      font-size: 14px;
      font-weight: 600;
      margin-right: 10px;
    }
  }
  .input {
    width: 100%;
  }
  .main-table {
    flex: 1;
    border: 1px solid rgba(178, 190, 195, 0.5);
    border-radius: 4px;
    overflow: auto;
  }
}
</style>
