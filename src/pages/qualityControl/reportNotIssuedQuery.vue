<template>
  <!-- 报告未出查询 -->
  <div class="reportNotIssuedQuery">
    <div class="page-wrap">
      <div class="operate-wrap">
        <h3 class="title">体检报告未出统计表：</h3>
        <div class="operate-item">
          <label>统计时间</label>
          <el-date-picker
            :picker-options="{ shortcuts: G_datePickerShortcuts }"
            v-model="infoData.date"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
            class="width"
            style="width: 68%"
          >
          </el-date-picker>
        </div>
        <el-radio-group v-model="infoData.peType" class="radio-group">
          <el-radio :label="0">所有</el-radio>
          <el-radio :label="1">个人</el-radio>
          <el-radio :label="2">团体</el-radio>
        </el-radio-group>
        <div class="operate-item">
          <label style="width: 34px">单位</label>
          <el-cascader
            ref="company_cascader_ref"
            v-model="infoData.companyCode"
            :filter-method="filterMethod"
            :options="companyList"
            :props="{ multiple: false }"
            clearable
            filterable
            size="small"
            collapse-tags
            @change="companyChange"
          >
          </el-cascader>
        </div>
      </div>
      <div class="operate-wrap" v-if="G_config.physicalMode.includes('普检')">
        <div class="operate-item">
          <label style="width: 78px">体检类型</label>
          <el-select
            v-model="infoData.peCls"
            placeholder="请选择"
            size="small"
            class="width"
            filterable
            clearable
          >
            <el-option
              v-for="item in G_peClsList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <div class="operate-item" style="width: 43%">
          <el-input
            v-model="infoData.extralParm"
            size="small"
            class="width"
            placeholder="体检号/姓名/未齐项目名"
            style="width: 100%"
            clearable
          ></el-input>
        </div>
        <ButtonGroup
          :btnList="['查询', '打印', '导出']"
          @search="search"
          @exports="exports"
          @prints="prints"
        />
      </div>
      <div class="table">
        <PublicTable
          :theads="theads"
          :viewTableList.sync="tableData"
          ref="view_Ref"
          :columnWidth="columnWidth"
          @rowDblclick="handleClick"
        >
          <template #peCls="{ scope }">
            {{ G_EnumList['PeCls'][scope.row.peCls] }}
          </template>
          <template #companyCode="{ scope }">
            {{ companyEnum[scope.row.companyCode] }}
          </template>
          <template #sex="{ scope }">
            {{ G_EnumList['Sex'][scope.row.sex] }}
          </template>
          <template #activeTime="{ scope }">
            {{ formatDate(scope.row.activeTime) }}
          </template>
          <template #guidanceRecyclyTime="{ scope }">
            {{ formatDate(scope.row.guidanceRecyclyTime) }}
          </template>
          <template #isVIP="{ scope }">
            {{ scope.row.isVIP ? '是' : '否' }}
          </template>
          <template #isCheck="{ scope }">
            {{ scope.row.isCheck ? '是' : '否' }}
          </template>
          <template #isComfirm="{ scope }">
            {{ scope.row.isComfirm ? '是' : '否' }}
          </template>
          <template #isGuidanceRecycly="{ scope }">
            {{ scope.row.isGuidanceRecycly ? '是' : '否' }}
          </template>
          <template #peStatus="{ scope }">
            {{ formatPeStatus(scope.row.peStatus) }}
          </template>
        </PublicTable>
      </div>
      <el-drawer
        :visible.sync="drawer"
        size="25%"
        :wrapperClosable="false"
        :withHeader="false"
      >
        <ul class="user-info">
          <li>
            姓名：
            <span>{{ popupForm.name }}</span>
          </li>
          <li>
            性别：
            <span>{{ popupForm.sex }}</span>
          </li>
          <li>
            年龄：
            <span>{{ popupForm.age }}岁</span>
          </li>
        </ul>
        <el-form :model="popupForm" ref="ruleForms" label-width="125px">
          <el-form-item label="体检分类" prop="peCls">
            <el-input
              v-model.trim="popupForm.peCls"
              size="small"
              readonly
            ></el-input>
          </el-form-item>
          <el-form-item label="单位" prop="companyCode">
            <el-input
              v-model.trim="popupForm.companyCode"
              size="small"
              readonly
            ></el-input>
          </el-form-item>
          <el-form-item label="电话" prop="tel">
            <el-input
              v-model.trim="popupForm.tel"
              size="small"
              readonly
            ></el-input>
          </el-form-item>
          <el-form-item label="激活时间" prop="activeTime">
            <el-input
              v-model.trim="popupForm.activeTime"
              size="small"
              readonly
            ></el-input>
          </el-form-item>
          <el-form-item label="VIP" prop="isVIP">
            <el-input
              v-model.trim="popupForm.isVIP"
              size="small"
              readonly
            ></el-input>
          </el-form-item>
          <el-form-item label="审核" prop="isComfirm">
            <el-input
              v-model.trim="popupForm.isComfirm"
              size="small"
              readonly
            ></el-input>
          </el-form-item>
          <el-form-item label="主检" prop="isCheck">
            <el-input
              v-model.trim="popupForm.isCheck"
              size="small"
              readonly
            ></el-input>
          </el-form-item>
          <el-form-item label="是否交表" prop="isGuidanceRecycly">
            <el-input
              v-model.trim="popupForm.isGuidanceRecycly"
              size="small"
              readonly
            ></el-input>
          </el-form-item>
          <el-form-item label="交表时间" prop="guidanceRecyclyTime">
            <el-input
              v-model.trim="popupForm.guidanceRecyclyTime"
              size="small"
              readonly
            ></el-input>
          </el-form-item>
          <el-form-item label="结果未齐" prop="peStatus">
            <el-input
              v-model.trim="popupForm.peStatus"
              size="small"
              readonly
            ></el-input>
          </el-form-item>
          <el-form-item label="未齐项目" prop="noCheckCombList">
            <el-input
              v-model.trim="popupForm.noCheckCombList"
              size="small"
              readonly
              type="textarea"
              :rows="4"
            ></el-input>
          </el-form-item>
          <el-form-item label="电话联系" prop="recordContactList">
            <el-input
              v-model.trim="popupForm.recordContactList"
              size="small"
              readonly
              type="textarea"
              :rows="4"
            ></el-input>
          </el-form-item>
          <el-form-item label="问题单修改医生" prop="replyDcotor">
            <el-input
              v-model.trim="popupForm.replyDcotor"
              size="small"
              readonly
            ></el-input>
          </el-form-item>
          <el-form-item label="主检分配医生" prop="checkDcotor">
            <el-input
              v-model.trim="popupForm.checkDcotor"
              size="small"
              readonly
            ></el-input>
          </el-form-item>
        </el-form>
        <div class="dialog-footer">
          <el-button @click="cancel" size="small" class="search-btn blue_btn"
            >关闭</el-button
          >
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import ButtonGroup from './components/buttonGroup.vue';
import PublicTable from '@/components/publicTable.vue';
import { mapGetters } from 'vuex';
import { dataUtils } from '@/common';
export default {
  name: 'reportNotIssuedQuery',
  components: {
    PublicTable,
    ButtonGroup
  },
  data() {
    return {
      value: '',
      radio: '',
      options: [],
      theads: {
        peCls: '体检分类',
        companyCode: '单位',
        regNo: '体检号',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        tel: '电话',
        activeTime: '激活时间',
        isVIP: 'VIP',
        isCheck: '主检',
        isComfirm: '审核',
        isGuidanceRecycly: '是否交表',
        guidanceRecyclyTime: '交表时间',
        noCheckCombList: '未齐项目',
        peStatus: '结果未齐',
        checkDcotor: '主检分配医生',
        replyDcotor: '回复医生',
        recordContactList: '电话联系'
      },
      tableData: [],
      infoData: {
        date: [dataUtils.getDate(), dataUtils.getDate()],
        peType: 0,
        peCls: '',
        companyCode: '',
        extralParm: ''
      },
      companyList: [],
      columnWidth: {
        regNo: 130,
        checkDcotor: 120,
        activeTime: 110,
        guidanceRecyclyTime: 110,
        noCheckCombList: 1000,
        tel: 120,
        recordContactList: 400,
        companyCode: 200
      },
      drawer: false,
      popupForm: {
        peCls: '',
        companyCode: '',
        regNo: '',
        name: '',
        sex: '',
        age: '',
        tel: '',
        activeTime: '',
        isVIP: '',
        isCheck: '',
        isComfirm: '',
        isGuidanceRecycly: '',
        guidanceRecyclyTime: '',
        noCheckCombList: '',
        peStatus: '',
        checkDcotor: '',
        replyDcotor: '',
        recordContactList: ''
      },
      companyEnum: {}
    };
  },
  computed: {
    ...mapGetters([
      'G_peClsList',
      'G_EnumList',
      'G_datePickerShortcuts',
      'G_config'
    ])
  },
  created() {
    this.getCompany();
  },
  methods: {
    // 获取单位
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        let newObj = {};
        returnData.map((item) => {
          newObj[item.companyCode] = item.companyName;
        });
        this.companyEnum = newObj;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) return;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.infoData.date = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.search();
    },
    // 查询
    search() {
      let data = {
        beginDate: this.infoData.date[0],
        endDate: this.infoData.date[1],
        peType: this.infoData.peType,
        peCls:
          this.infoData.peCls !== 0 && !this.infoData.peCls
            ? -1
            : this.infoData.peCls,
        companyCode: this.infoData.companyCode[0],
        extralParm: this.infoData.extralParm
      };
      this.$ajax.post(this.$apiUrls.PeNoReportQuery, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.tableData = returnData || [];
      });
    },
    // 抽屉关闭
    cancel() {
      this.drawer = false;
      this.$nextTick(() => {
        this.$refs.ruleForms.resetFields();
      });
    },
    // 表格双击
    handleClick(row) {
      this.drawer = true;
      row = {
        ...row,
        sex: this.G_EnumList['Sex'][row.sex],
        peCls: this.G_EnumList['PeCls'][row.peCls],
        companyCode: this.companyEnum[row.companyCode],
        isVIP: row.isVIP ? '是' : '否',
        isCheck: row.isCheck ? '是' : '否',
        isComfirm: row.isComfirm ? '是' : '否',
        isGuidanceRecycly: row.isGuidanceRecycly ? '是' : '否',
        peStatus: this.formatPeStatus(row.peStatus)
      };
      this.$nextTick(() => {
        this.popupForm = dataUtils.deepCopy(row);
        this.$refs.ruleForms.resetFields();
      });
    },
    // 结果录入格式化
    formatPeStatus(val) {
      let text = '';
      switch (val) {
        case 0:
          text = '未录';
          break;
        case 1:
          text = '未齐';
          break;
        case 2:
          text = '已录齐';
          break;
      }
      return text;
    },
    // 时间格式化
    formatDate(date) {
      if (!date) return;
      return dataUtils.subBlankDate(date);
    },
    // 导出
    exports() {},
    // 打印
    prints() {}
  }
};
</script>

<style lang="less" scoped>
.reportNotIssuedQuery {
  .page-wrap {
    width: 100%;
    height: 100%;
    background: #fff;
    padding: 10px;
    color: #2d3436;
    display: flex;
    flex-direction: column;
  }
  .title {
    font-size: 18px;
  }
  .title-wrap {
    display: flex;
    // justify-content: space-between;
    align-items: center;
  }
  .operate-wrap {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    label {
      font-size: 14px;
      margin-right: 10px;
      font-weight: 600;
    }
  }
  .width {
    width: 100%;
  }
  .operate-item {
    margin-right: 18px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    &:last-child {
      margin-right: 0;
    }
  }
  .table {
    flex: 1;
    overflow: auto;
    border: 1px solid #d9dfe2;
    border-radius: 4px;
  }
  .user-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    font-size: 16px;
    span {
      font-size: 20px;
      font-weight: 600;
    }
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  /deep/.el-drawer__body {
    padding: 20px;
  }
  /deep/.el-form-item {
    margin-bottom: 10px;
  }
  /deep/.el-form-item__label {
    font-weight: 600;
    color: #2d3436;
  }
}
</style>
