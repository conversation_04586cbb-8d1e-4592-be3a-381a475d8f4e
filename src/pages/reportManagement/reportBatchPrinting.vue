<template>
  <!-- 报告批打印 -->
  <div class="reportBatchPrinting">
    <div class="search-header">
      <div class="search-item">
        <label style="width: 60px">体检时间</label>
        <el-date-picker
          :picker-options="{ shortcuts: G_datePickerShortcuts }"
          v-model.trim="searchInfo.date"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          class="date-picker"
          @change="getReportBatchPrintList"
        >
        </el-date-picker>
      </div>
      <div class="search-item">
        <label style="width: 34px">单位</label>
        <el-cascader
          ref="company_cascader_ref"
          v-model="searchInfo.companyCode"
          :options="companyList"
          :filter-method="filterMethod"
          :props="{ multiple: false }"
          clearable
          filterable
          size="small"
          collapse-tags
          @change="companyChange"
        >
        </el-cascader>
      </div>
      <div class="search-item">
        <label style="width: 80px">单位部门</label>
        <el-select
          v-model.trim="searchInfo.companyDeptCode"
          placeholder="请选择"
          size="small"
          class="input"
          filterable
          clearable
          @change="getReportBatchPrintList"
        >
          <el-option
            :label="item.deptName"
            :value="item.deptCode"
            v-for="item in companyDeptList"
            :key="item.deptCode"
          >
          </el-option>
        </el-select>
      </div>
      <ButtonGroup
        :btnList="['查询', '导出']"
        @search="getReportBatchPrintList"
        @prints="prints"
        @exports="exports"
      >
        <template #footAdd>
          <el-button type="primary" size="mini" @click="batchDownloadPDF"
            >批量下载</el-button
          >
        </template>
      </ButtonGroup>
    </div>
    <div class="main">
      <div class="btn-item">
        <label>状态</label>
        <el-select
          v-model.trim="status"
          placeholder="请选择"
          size="small"
          filterable
          @change="sortFun"
          style="margin: 0 10px"
        >
          <el-option
            :label="item.label"
            :value="item.value"
            v-for="item in statusList"
            :key="item.value"
          >
          </el-option>
        </el-select>

        <label>报告类型</label>
        <el-select
          style="width: 200px; margin: 0 10px"
          v-model.trim="reportType"
          placeholder="请选择"
          size="small"
          class="input"
          filterable
          :disabled="!lockingReportType"
        >
          <el-option
            :label="item.label"
            :value="item.value"
            v-for="item in G_codeReportType"
            :key="item.value"
          >
          </el-option>
        </el-select>
        <el-checkbox v-model="lockingReportType" class="checkbox"
          >锁定报告类型</el-checkbox
        >
        <el-select
          size="small"
          v-model="reportPrintVal"
          placeholder="请选择打印机"
          style="margin-left: 40px"
        >
          <el-option
            v-for="item in G_printerList"
            :key="item"
            :label="item"
            :value="item"
          >
          </el-option>
        </el-select>
        <ButtonGroup
          :btnList="['打印']"
          @prints="prints"
          style="display: inline-block; margin-left: 10px"
        >
        </ButtonGroup>
      </div>
      <div class="main-table">
        <PublicTable
          ref="view_Ref"
          isCheck
          :isStripe="false"
          :viewTableList.sync="tableData"
          :tableCellClassName="stateRow"
          :theads.sync="theads"
          :columnWidth="{
            activeTime: 110,
            regNo: 130,
            companyName: 230,
            companyDeptName: 230,
            reportPrinted: 90
          }"
          v-model="selection"
          :tableLoading="tableLoading"
          @selectionChange="selectionChange"
        >
          <template #activeTime="{ scope }">
            <div>
              {{ dateFormat(scope.row.activeTime) }}
            </div>
          </template>
          <template #sex="{ scope }">
            <div>
              {{ G_EnumList['Sex'][scope.row.sex] }}
            </div>
          </template>
          <template #reportPrinted="{ scope }">
            <div>
              {{ scope.row.reportPrinted ? '是' : '否' }}
            </div>
          </template>
        </PublicTable>
      </div>
    </div>
    <!-- <PrintPreview ref="PrintPreview_ref" v-model="previewShow" :batchPrintList="selectRowList" :dataInfo.sync="checkUser" :printerTypeList="printerTypeList" /> -->
    <!-- 打印进度 -->
    <BatchPrint ref="BatchPrint_ref" v-if="batchPrintShow" />
    <ReportPrint
      ref="print_ref"
      v-model="previewShow"
      :dataInfo.sync="dataInfo"
      :printerTypeList="printerTypeList"
      :batchPrintList="selectRowList"
    />
    <report-loading
      :reportLoadingShow="reportLoadingShow"
      :percentage="percentage"
      :message="reportPrintSchedule"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import ButtonGroup from './components/buttonGroup.vue';
import PublicTable from '@/components/publicTable';
import { dataUtils } from '@/common';
import BatchPrint from '@/components/batchPrint';
import printMixins from '@/components/newPrintMixins';
import ReportPrint from '@/components/reportPrint';
import { batchDownloadFile, newBatchDownloadFile } from '@/common/zipUtil';
import { storage } from '@/common';
import FileSaver from 'file-saver';

export default {
  name: 'reportBatchPrinting',
  mixins: [printMixins],
  components: {
    ButtonGroup,
    PublicTable,
    BatchPrint,
    ReportPrint
  },
  data() {
    return {
      statusList: [
        {
          value: null,
          label: '全部'
        },
        {
          value: false,
          label: '未打印'
        },
        {
          value: true,
          label: '已打印'
        }
      ],
      status: '',
      reportType: '',
      lockingReportType: false,
      previewShow: false,
      printerTypeList: [
        {
          printerType: 0, //打印机类型 0 普通打印机，1 条码打印机
          label: '报告',
          checked: true
        }
      ],
      checkUser: {},
      // 选中的人
      selectRowList: [],
      // 打印机
      printSetInfo: {
        PrintTimes: 1,
        PrinterName: ''
      },
      formLabelWidth: '100px',
      batchPrintShow: false,

      searchInfo: {
        date: [new Date(), new Date()],
        companyCode: '',
        companyDeptCode: ''
      },
      tableData: [],
      copyTableData: [],
      theads: {
        activeTime: '体检日期',
        regNo: '体检号',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        reportPrinted: '是否已打印',
        companyName: '工作单位',
        companyDeptName: '单位部门'
      },
      companyList: [],
      companyDeptList: [],
      excelData: [],
      dataInfo: {},
      selection: [],
      tableLoading: false
    };
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_peClsList',
      'G_codeReportType',
      'G_datePickerShortcuts'
    ])
  },
  created() {
    this.getCompanyList();
    this.getReportBatchPrintList();
    this.connectPrint((r) => {
      let dataInfo = JSON.parse(r.data);

      this.M_printerList(dataInfo.Data);
      const print = storage.local.get('print');

      this.reportPrintVal = print?.printer || dataInfo.Data[0];
    });
  },
  methods: {
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    //手动打印状态过滤左边表格
    sortFun() {
      this.selection = [];
      if (this.status == null) {
        this.tableData = this.copyTableData;
      } else {
        this.tableData = this.copyTableData.filter((item) => {
          return item.reportPrinted == this.status;
        });
      }
    },
    // 根据报告是否打印变化行颜色
    stateRow({ row }) {
      if (row.reportPrinted) {
        return 'background: rgb(38 ,126, 235, 0.5);';
      }
    },
    printFinishInit() {
      this.getReportBatchPrintList();
      this.selection = [];
    },
    selectionChange(val, checkList) {
      this.selectRowList = checkList;
      this.checkUser = checkList[0] || {};
    },
    batchPrintConfirm() {
      this.batchPrintShow = true;
      this.$nextTick(() => {
        this.$refs.BatchPrint_ref.batchPrint(this.selectRowList);
      });
    },
    /**
     * @description: 单位下拉变化
     * @param {*} data
     * @return {*}
     * @author: key
     */
    companyChange(data) {
      this.getCompanyDeptList(data);
      if (!data || data.length === 0) return;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        ?.children?.find((item) => item.value === data[1]).item;
      this.searchInfo.date = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.getReportBatchPrintList();
    },
    // 获取单位下拉
    getCompanyList() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    // 获取单位部门下拉
    getCompanyDeptList(companyCode) {
      this.searchInfo.companyDeptCode = '';
      let data = {
        // deptCode: "string",
        // deptName: "string",
        companyCode: companyCode?.[0] || ''
      };
      this.$ajax.post(this.$apiUrls.R_CodeCompanyDepartment, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyDeptList = returnData;
      });
    },
    // 查询/获取批打印列表
    getReportBatchPrintList() {
      let [beginDate, endDate] = this.searchInfo.date;
      this.status = null;
      let data = {
        beginDate: dataUtils.dateToString(beginDate),
        endDate: dataUtils.dateToString(endDate),
        companyCode: this.searchInfo.companyCode[0],
        companyDeptCode: this.searchInfo.companyDeptCode
      };
      this.tableLoading = true;
      this.$ajax
        .post(this.$apiUrls.GetReportBatchPrintAndExportList, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.copyTableData = returnData;
          this.tableData = dataUtils.deepCopy(returnData);
        })
        .finally((_) => {
          this.tableLoading = false;
        });
    },
    // 日期格式化
    dateFormat(date) {
      return dataUtils.subBlankDate(date);
    },
    // 打印
    prints() {
      if (this.selectRowList?.length == 0) {
        this.$message({
          message: '请选择需要打印的报告！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.lockingReportType && this.reportType == '') {
        this.$message({
          message: '请选择报告类型',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.dataInfo = {
        guidanceType: this.lockingReportType
          ? this.reportType
          : this.selectRowList[0]?.reportType,
        regNo: this.selectRowList[0]?.regNo,
        lockingReportType: this.lockingReportType
      };
      this.reportLoadingShow = true;
      this.percentage = 0;
      this.currentNum = 0;
      this.reportPrint(this.selectRowList);
    },
    // 导出
    exports() {
      let tableCom_Ref = this.$refs.view_Ref.$refs.tableCom_Ref;
      if (tableCom_Ref.selection.length == 0) {
        return this.$message({
          message: '请选择至少一条数据进行操作！',
          type: 'warning',
          showClose: true
        });
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.excelData = tableCom_Ref.selection.map((item, i) => {
            return {
              ...item,
              index: i + 1,
              sex: this.G_EnumList['Sex'][item.sex],
              reportPrinted: item.reportPrinted ? '是' : '否'
            };
          });
          this.$nextTick(function () {
            this.export2Excel();
          });
        })
        .catch(() => {});
    },
    // 数据写入excel
    export2Excel() {
      var that = this;
      require.ensure([], () => {
        const { export_json_to_excel } = require('@/utils/Export2Excel'); // 这里必须使用绝对路径，使用@/+存放export2Excel的路径
        const tHeader = ['序号'].concat(Object.values(this.theads)); // 导出的表头名信息
        const filterVal = ['index'].concat(Object.keys(this.theads)); // 导出的表头字段名
        const list = that.excelData;
        const data = that.formatJson(filterVal, list);
        console.log(data);
        const name = that.excelName;
        export_json_to_excel(tHeader, data, name);
      });
    },

    // 格式转换
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) => filterVal.map((j) => v[j]));
    },

    /**
     * @author: justin
     * @description: 批量下载PDF
     * @return {*}
     */
    batchDownloadPDF() {
      let tableCom_Ref = this.$refs.view_Ref;
      if (tableCom_Ref.selection.length < 1) {
        return this.$message.warning('请选择至少一条数据进行操作！');
      }
      this.$confirm('确定批量下载PDF报告文件？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const fileList = tableCom_Ref.selection
          .filter((x) => x.reportPDFUrl && x.reportPDFUrl.length > 0)
          .map((x) => {
            return {
              url: x.reportPDFUrl,
              name: `${x.name}(${x.regNo}).pdf`
            };
          });
        if (fileList.length === 0) {
          return this.$message.warning('没有有效的PDF文件链接可以下载！');
        }
        const loading = this.$loading({
          lock: true,
          text: '批量下载中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        try {
          for (let item of fileList) {
            try {
              const response = await this.$ajax.get(item.url, {
                responseType: 'blob'
              });
              FileSaver.saveAs(response.data, item.name);
            } catch (err) {
              console.error(`下载文件 ${item.name} 失败:`, err);
              this.$message.error(`下载文件 ${item.name} 失败，请稍后重试！`);
            }
          }
        } catch (err) {
          console.error('批量下载过程中出现错误:', err);
          this.$message.error('批量下载过程中出现错误，请稍后重试！');
        } finally {
          loading.close();
        }
        this.$message.success('所有文件下载完成！');
      });
    }
  }
};
</script>

<style lang="less" scoped>
.reportBatchPrinting {
  display: flex;
  flex-direction: column;
  overflow: auto;
  .search-header {
    padding: 5px;
    background: #fff;
    border-radius: 4px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
  .search-item {
    display: flex;
    align-items: center;
    margin-right: 18px;
    label {
      text-align: right;
      margin-right: 10px;
      font-size: 14px;
      font-weight: 600;
    }
  }
  .input {
    width: 100%;
  }
  .main {
    flex: 1;
    padding: 5px;
    background: #fff;
    border-radius: 4px;
    overflow: auto;
    display: flex;
    flex-direction: column;
    .btn-item {
      margin-bottom: 5px;
      label {
        width: 80px;
      }
    }
  }
  .main-table {
    flex: 1;
    flex-shrink: 0;
    border: 1px solid rgba(178, 190, 195, 0.5);
    border-radius: 4px;
  }
}
</style>
