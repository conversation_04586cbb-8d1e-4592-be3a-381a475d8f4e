<template>
  <!-- 报告邮寄 -->
  <div class="reportMailing">
    <div class="mailed-header">
      <div class="tabs">
        <div
          class="tabs-item"
          :class="tabsIndex === item.id ? 'active' : ''"
          @click="tabsClick(item)"
          v-for="item in tabsList"
          :key="item.id"
        >
          {{ item.title }}
        </div>
      </div>
    </div>
    <div class="main">
      <keep-alive>
        <component :is="view"></component>
      </keep-alive>
    </div>
  </div>
</template>

<script>
import NotMail from './components/notMail.vue';
import MailPage from './components/mailPage.vue';
import SelfOrSubstitution from './components/selfOrSubstitution.vue';
export default {
  name: 'reportMailing',
  components: {
    NotMail,
    MailPage,
    SelfOrSubstitution
  },
  data() {
    return {
      tabsIndex: 1,
      tabsList: [
        {
          id: 1,
          title: '未邮寄',
          view: 'NotMail'
        },
        {
          id: 2,
          title: '已邮寄',
          view: 'MailPage'
        },
        {
          id: 3,
          title: '本人/代取',
          view: 'SelfOrSubstitution'
        }
      ],
      view: 'NotMail'
    };
  },
  computed: {},
  methods: {
    // 标签页点击
    tabsClick(item) {
      this.tabsIndex = item.id;
      this.view = item.view;
    }
  }
};
</script>

<style lang="less" scoped>
.reportMailing {
  color: #2d3436;
  display: flex;
  flex-direction: column;
  overflow: auto;
  .mailed-header {
    background: #fff;
    padding: 18px 18px 0 18px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
  }
  .tabs {
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(178, 190, 195, 0.4);
  }
  .tabs-item {
    width: 100px;
    text-align: center;
    padding-bottom: 18px;
    border-bottom: 2px solid #fff;
    margin-right: 18px;
    cursor: pointer;
    &.active {
      color: #1770df;
      padding-bottom: 18px;
      border-bottom: 2px solid #1770df;
    }
  }
  .main {
    flex: 1;
    overflow: auto;
  }
}
</style>
<style lang="less">
.reportMailing {
  .mailShow {
    .el-dialog__body {
      padding-right: 28px;
      padding-bottom: 8px;
    }
    .el-dialog__footer {
      padding-top: 0;
      padding-right: 28px;
    }
  }
}
</style>
