<template>
  <div class="labelPrinting">
    <div class="headrCont">
      <el-form :model="searchForm" ref="form_ref">
        <el-form-item label="体检号" label-width="65px">
          <el-input
            v-model.trim="searchForm.regNo"
            size="small"
            placeholder="请输入"
            clearable
            @keyup.enter.native="search"
          ></el-input>
        </el-form-item>

        <el-form-item label="姓名" label-width="60px">
          <el-input
            v-model.trim="searchForm.name"
            size="small"
            placeholder="请输入"
            clearable
            @keyup.enter.native="search"
          ></el-input>
        </el-form-item>

        <el-form-item label="登记时间" label-width="70px">
          <el-date-picker
            :picker-options="{ shortcuts: G_datePickerShortcuts }"
            size="small"
            v-model="regTime"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            clearable
            @change="search"
          >
          </el-date-picker>
        </el-form-item>

        <el-button
          size="mini"
          class="blue_btn btn"
          @click="search"
          style="margin-left: 12px"
          >查询</el-button
        >
        <!-- <el-button @click="print" class="green_btn btn" size="mini"
          >打印</el-button
        > -->
        <el-button @click="extSystemPrint" class="green_btn btn" size="mini"
          >贴标打印</el-button
        >
      </el-form>
    </div>

    <div class="tableCont">
      <PublicTable
        ref="record_Ref"
        :viewTableList.sync="tableData"
        :theads.sync="theads"
        :tableLoading.sync="loading"
        :columnWidth="columnWidth"
        @selectionChange="handleSelRow"
        @currentChange="currentChange"
        @select="handleSelectionChange"
        @selectAll="selectAll"
        v-model="activeInfo.regNoArray"
        isCheck
      >
        <template #age="{ scope }">
          <div>
            {{ scope.row.age + enum_ageUnit[scope.row.ageUnit] }}
          </div>
        </template>
        <template #peCls="{ scope }">
          <div>
            {{ G_EnumList['PeCls'][scope.row.peCls] }}
          </div>
        </template>
        <template #peStatus="{ scope }">
          <div>
            {{ G_EnumList['PeStatus'][scope.row.peStatus] }}
          </div>
        </template>
        <template #sex="{ scope }">
          <div>
            {{ G_EnumList['Sex'][scope.row.sex] }}
          </div>
        </template>
      </PublicTable>
    </div>
    <div class="my_mask" v-if="G_activeProgressShow">
      <el-progress
        type="circle"
        :percentage="G_activeMsgData.currentProgressPercentage"
      ></el-progress>
      <p class="tips_p">{{ G_activeMsgData.message }}</p>
    </div>
    <!-- <PrintPreview
      v-model="previewShow"
      :dataInfo.sync="dataInfo"
      :printerTypeList="printerTypeList"
    /> -->
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import PublicTable from '@/components/publicTable';
import { dataUtils } from '@/common';
import PrintPreview from '@/components/printPreview';
import PushMixinsAct from '@/components/pushMixinsAct';
export default {
  name: 'labelPrinting',
  mixins: [PushMixinsAct],
  components: { PublicTable, PrintPreview },
  props: {
    cancel: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      extSystemPrintIdx: 0,
      printVal: ['采血条码'],
      previewShow: false,
      dataInfo: {},
      printerTypeList: [],
      currentRow: {},
      loading: false,
      regTime: [dataUtils.getDate(), dataUtils.getDate()],
      searchForm: {
        regNo: '',
        name: '',
        regStartTime: '',
        regEndTime: ''
      }, //查询条件
      tableData: [], //表单数据
      theads: {
        regNo: '体检号',
        peStatus: '状态',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        peCls: '体检分类',
        companyName: '工作单位'
      },
      enum_ageUnit: {
        null: '',
        0: '岁',
        1: '月'
      },
      columnWidth: {
        peStatus: 100,
        name: 100,
        sex: 65,
        age: 75
      },
      activeInfo: {
        regNoArray: [], //体检号数组
        activeTime: dataUtils.getDate(), //"2022-07-25T03:41:51.684Z"
        activator: ''
      },
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() > Date.now();
        }
      }
    };
  },
  created() {
    this.connectPrint();
  },
  mounted: function () {
    this.$nextTick(() => {
      this.search(); //进入页面默认查询当天
    });
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_userInfo',
      'G_activeProgressShow',
      'G_activeMsgData',
      'G_datePickerShortcuts'
    ])
  },
  methods: {
    format(percentage) {
      return `${this.G_activeMsgData.completedCount}/${this.G_activeMsgData.totalCount}`;
    },
    handleCheckedValChange(value) {
      console.log(value);
    },

    //查询
    search() {
      if (!this.regTime) {
        this.searchForm.regStartTime = '';
        this.searchForm.regEndTime = '';
      } else {
        this.searchForm.regStartTime = this.regTime[0] || '';
        this.searchForm.regEndTime = this.regTime[1] || '';
      }

      this.tableData = [];
      console.log('[ this.searchForm ]-314', this.searchForm);
      this.$ajax
        .post(this.$apiUrls.GetActiveRecord, this.searchForm)
        .then((r) => {
          console.log('r', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.tableData = returnData || [];
          if (this.searchForm.regNo && this.tableData.length > 0) {
            this.$nextTick(() => {
              this.$refs.record_Ref.$refs.tableCom_Ref.setCurrentRow(
                this.tableData[0]
              );
              this.$refs.record_Ref.$refs.tableCom_Ref.toggleRowSelection(
                this.tableData[0]
              );
            });
          }
        });
    },
    //勾选行
    handleSelRow: function (val, checkList) {
      console.log('[ val ]-326', val);
      this.activeInfo.regNoArray = [];
      checkList.forEach((item) => {
        if (item.regNo !== '') {
          this.activeInfo.regNoArray.push(item.regNo);
        }
      });
      if (val.length > 0 && checkList.length < 1) {
        this.activeInfo.regNoArray.push(val[0].regNo);
      }
      console.log('[ this.activeInfo ]-328', this.activeInfo.regNoArray);
    },
    // 选中列表高亮
    currentChange(row) {
      console.log(row);
      this.currentRow = row;
    },
    //勾选默认点击行
    handleSelectionChange(selection, row) {
      this.currentRow = row;
      this.$refs.record_Ref.$refs.tableCom_Ref.setCurrentRow(row);
    },
    //全选判定只有一条数据时默认点击操作
    selectAll(selection) {
      if (selection.length < 1) {
        return;
      }
      this.checkList = selection;
      this.$refs.record_Ref.$refs.tableCom_Ref.setCurrentRow(selection[0]);
    },
    // 打印
    print() {
      console.log('[ this.currentRow ]-282', this.currentRow);
      if (!this.currentRow?.regNo) {
        this.$message({
          message: '请先选择需要打印的体检信息！',
          type: 'warning',
          showClose: true
        });
        return;
      }

      this.printerTypeList = [];
      this.printVal?.map((item) => {
        let printerType = 0;
        let type = '';
        switch (item) {
          case '指引单':
            printerType = 0;
            break;

          case '体检标签':
            printerType = 1;
            break;
          case '采血条码':
            printerType = 1;
            type = 1;
            break;
          case '非采血条码':
            printerType = 1;
            type = 2;
            break;
          default:
            printerType = 0;
            break;
        }
        this.printerTypeList.push({
          printerType: printerType,
          label: item,
          checked: true,
          type
        });
      });
      this.dataInfo = this.currentRow;
      this.previewShow = true;
    },
    // 外部打印
    extSystemPrint() {
      if (this.activeInfo.regNoArray.length === 0) {
        this.$message({
          message: '请先选择需要推送打印的体检人!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      // this.connectPrint();
      this.extSystemPrintFun();
    },
    // 外部打印递归回调
    extSystemPrintFun() {
      console.log(this.extSystemPrintIdx);
      if (this.extSystemPrintIdx >= this.activeInfo.regNoArray.length) {
        this.$message({
          message: '完成操作!',
          type: 'success',
          showClose: true
        });
        this.extSystemPrintIdx = 0;
        return;
      }
      console.log(this.activeInfo.regNoArray[this.extSystemPrintIdx]);
      this.$ajax
        .paramsPost(this.$apiUrls.GetSampleDataToMachine, {
          regNo: this.activeInfo.regNoArray[this.extSystemPrintIdx]
        })
        .then((r) => {
          this.extSystemPrintIdx += 1;
          let { success, returnData } = r.data;
          let datas = "{'Action':'sample','Data':'" + returnData + "'}";
          console.log(datas);
          this.$sample_ws?.sendSock(datas);
          setTimeout(() => {
            this.extSystemPrintFun();
          }, 300);
        })
        .catch((e) => {
          this.extSystemPrintIdx += 1;
          setTimeout(() => {
            this.extSystemPrintFun();
          }, 300);
        });
    }
  }
};
</script>
<style lang="less" scoped>
.labelPrinting {
  display: flex;
  flex-direction: column;
  position: relative;
  height: 100%;
  .headrCont {
    background: #fff;
    border-radius: 4px;
    padding: 5px;
    .el-form {
      display: flex;
      .el-form-item {
        margin-bottom: 0px;
        /deep/.el-date-editor.el-input,
        .el-date-editor.el-input__inner {
          width: 220px;
        }

        .btn {
          padding: 6.5px 10px;
        }
        /deep/ .el-form-item__label,
        /deep/.el-form-item__content {
          height: 32px;
          line-height: 32px;
          padding-right: 8px;
        }
      }
    }
  }
  .tableCont {
    height: calc(100% - 200px);
    background: #fff;
    flex: 1;
    padding: 5px;
  }
}
</style>
