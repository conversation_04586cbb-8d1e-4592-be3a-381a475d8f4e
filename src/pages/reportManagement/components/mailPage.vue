<template>
  <div class="mailPage">
    <div class="hearer-wrap">
      <div class="search-wrap">
        <div class="search-item">
          <label>快递公司</label>
          <el-input
            v-model.trim="searchInfo.expressCompany"
            size="small"
            placeholder="请输入"
            clearable
            class="input"
          ></el-input>
        </div>
        <div class="search-item">
          <label>快递单号</label>
          <el-input
            v-model.trim="searchInfo.expressNo"
            size="small"
            placeholder="请输入"
            clearable
            class="input"
          ></el-input>
        </div>
        <div class="search-item">
          <label style="width: 110px">体检号/姓名</label>
          <el-input
            v-model.trim="searchInfo.keyWord"
            size="small"
            placeholder="请输入"
            clearable
            class="input"
            @keyup.enter.native="getReportMailList"
          ></el-input>
        </div>
        <ButtonGroup
          :btnList="['查询', '导出']"
          @search="getReportMailList"
          @exports="exports"
        />
      </div>
    </div>
    <div class="mailed-table">
      <div class="wrap-table">
        <PublicTable
          ref="view_Ref"
          isCheck
          :isSortShow="false"
          :viewTableList.sync="mailedTableData"
          :theads.sync="mailedTheads"
          :columnWidth="columnWidth"
          @selectionChange="selectionChange"
          @currentChange="rowClick"
        >
          <template #peCls="{ scope }">
            <div>
              {{ G_EnumList['PeCls'][scope.row.peCls] }}
            </div>
          </template>
        </PublicTable>
      </div>
    </div>
  </div>
</template>

<script>
import ButtonGroup from '../components/buttonGroup.vue';
import PublicTable from '@/components/publicTable';
import { mapGetters } from 'vuex';
import { dataUtils } from '@/common';
export default {
  name: 'mailPage',
  components: {
    ButtonGroup,
    PublicTable
  },
  data() {
    return {
      tabsIndex: 2,
      searchInfo: {
        radio: 3
      },
      mailedTheads: {
        name: '体检人',
        cardNo: '身份证',
        mailAddress: '邮寄地址',
        tel: '联系电话',
        peCls: '体检分类',
        clusName: '体检套餐',
        regNo: '体检号',
        companyName: '体检单位',
        applicant: '申请人',
        applicationTime: '申请时间'
      },
      mailedTableData: [],
      columnWidth: {
        cardNo: 180,
        mailAddress: 200,
        tel: 120,
        peCls: 120,
        clusName: 160,
        regNo: 130,
        companyName: 200,
        applicationTime: 170
      },
      selectRow: {},
      selection: [],
      regNo: [],
      excelData: []
    };
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_peClsList', 'G_userInfo'])
  },
  methods: {
    // 查询/获取邮寄列表
    getReportMailList(keyWord) {
      let data = {
        keyWord: keyWord,
        expressCompany: this.searchInfo.expressCompany,
        expressNo: this.searchInfo.expressNo,
        type: this.tabsIndex
      };
      this.$ajax.post(this.$apiUrls.GetReportMailList, data).then((r) => {
        console.log('GetReportMailList: ', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.mailedTableData = returnData;
      });
    },
    // 行点击
    rowClick(row) {
      let regNo = [];
      if (row) {
        this.selectRow = row;
        regNo.push(row.regNo);
        this.regNo = regNo;
      }
    },
    // 表格多选
    selectionChange(rows) {
      console.log('rows: ', rows);
      this.regNo = rows.map((item) => item.regNo);
      this.selection = rows;
    },
    // 重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    // 导出
    exports() {
      let tableCom_Ref = this.$refs.view_Ref.$refs.tableCom_Ref;
      if (tableCom_Ref.selection.length == 0) {
        return this.$message({
          message: '请选择至少一条数据进行操作！',
          type: 'warning',
          showClose: true
        });
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.excelData = tableCom_Ref.selection.map((item) => {
            return {
              name: item.name,
              cardNo: item.cardNo,
              mailAddress: item.mailAddress,
              tel: item.tel,
              peCls: this.G_EnumList['PeCls'][item.peCls],
              clusName: item.clusName,
              regNo: item.regNo,
              companyName: item.companyName,
              applicant: item.applicant,
              applicationTime: item.applicationTime
            };
          }); // multipleSelection是一个数组，存储表格中选择的行的数据。
          this.excelData.map((item, i) => {
            item.index = i + 1;
          });
          this.$nextTick(function () {
            this.export2Excel();
          });
        })
        .catch(() => {});
    },
    // 数据写入excel
    export2Excel() {
      var that = this;
      require.ensure([], () => {
        const { export_json_to_excel } = require('@/utils/Export2Excel'); // 这里必须使用绝对路径，使用@/+存放export2Excel的路径
        const tHeader = Object.values(this.theads); // 导出的表头名信息
        const filterVal = Object.keys(this.theads); // 导出的表头字段名
        const list = that.excelData;
        const data = that.formatJson(filterVal, list);
        console.log(data);
        const name = that.excelName;
        export_json_to_excel(tHeader, data, name);
      });
    }
  }
};
</script>

<style lang="less" scoped>
.mailPage {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
  .hearer-wrap {
    background: #fff;
    padding: 18px;
    margin-bottom: 10px;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
  }
  .search-wrap {
    display: flex;
    align-items: center;
  }
  .search-item {
    display: flex;
    align-items: center;
    margin-right: 18px;
    label {
      width: 80px;
      text-align: right;
      margin-right: 10px;
      font-size: 14px;
      font-weight: 600;
    }
  }
  .radio {
    margin-bottom: 18px;
  }
  .mailed-table {
    flex: 1;
    background: #fff;
    border-radius: 4px;
    padding: 10px;
    overflow: auto;
  }
  .wrap-table {
    height: 100%;
    border: 1px solid rgba(178, 190, 195, 0.5);
    border-radius: 4px;
    overflow: auto;
  }
  .dialog-title {
    background: #d1e2f9;
    font-size: 18px;
    padding: 12px 18px;
    font-weight: 600;
  }
  /deep/.el-dialog__header {
    padding: 0;
  }
  /deep/.el-form-item__label {
    font-size: 14px;
    color: #2d3436;
    font-weight: 600;
  }
  /deep/.el-form-item {
    margin-bottom: 16px;
  }
}
</style>
