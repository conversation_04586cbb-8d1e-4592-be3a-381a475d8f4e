<template>
  <div class="btn_group">
    <el-button
      v-if="btnList.includes('查询')"
      @click="search"
      class="blue_btn btn"
      size="small"
      icon="iconfont icon-search"
      >查询</el-button
    >

    <el-button
      v-if="btnList.includes('导出')"
      @click="exports"
      size="small"
      class="yellow_btn btn"
      icon="iconfont icon-daochu"
      >导出</el-button
    >
    <el-button
      v-if="btnList.includes('邮寄')"
      @click="mailClick"
      class="violet_btn btn"
      size="small"
      icon="iconfont icon-a-xinxiyoujixiaoxi"
      >邮寄</el-button
    >
    <el-button
      v-if="btnList.includes('取消邮寄')"
      @click="cancelMailing"
      class="red_btn btn"
      size="small"
      icon="iconfont icon-a-xinxiyoujixiaoxi"
      >取消邮寄</el-button
    >
    <el-button
      v-if="btnList.includes('领取')"
      @click="receiveClick"
      class="violet_btn btn"
      size="small"
      icon="iconfont icon-zhanghulingqu"
      >领取</el-button
    >
    <el-button
      v-if="btnList.includes('打印')"
      @click="prints"
      size="small"
      class="green_btn btn"
      icon="iconfont icon-dayin-"
      >打印</el-button
    >
    <el-button
      size="small"
      class="yellow_btn btn"
      v-if="btnList.includes('身份证')"
      @click="idCardClick"
      icon="iconfont icon-shenfenzheng"
      >身份证</el-button
    >
    <el-button
      size="small"
      class="yellow_btn btn"
      v-if="btnList.includes('作废')"
      @click="cancelClick"
      icon="iconfont icon-zuofei"
      >作废</el-button
    >
    <el-button
      v-if="btnList.includes('修改')"
      @click="modifyClick"
      class="blue_btn btn"
      size="small"
      icon="iconfont icon-bianji"
      >修改</el-button
    >
    <slot name="footAdd"></slot>
  </div>
</template>

<script>
export default {
  name: 'btnGroup',
  props: {
    btnList: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  data() {
    return {};
  },
  methods: {
    // 查询
    search() {
      this.$emit('search');
    },
    // 打印
    prints() {
      this.$emit('prints');
    },
    // 导出
    exports() {
      this.$emit('exports');
    },
    //领取
    receiveClick() {
      this.$emit('receiveClick');
    },
    //身份证
    idCardClick() {
      this.$emit('idCardClick');
    },
    //邮寄
    mailClick() {
      this.$emit('mailClick');
    },
    // 取消邮寄
    cancelMailing() {
      this.$emit('cancelMailing');
    },
    // 作废
    cancelClick() {
      this.$emit('cancelClick');
    },
    // 修改
    modifyClick() {
      this.$emit('modifyClick');
    }
  }
};
</script>
<style lang="less" scoped>
.btn_group {
  display: flex;
  align-items: center;
}
.btn {
  padding: 6.5px 10px;
}
</style>
