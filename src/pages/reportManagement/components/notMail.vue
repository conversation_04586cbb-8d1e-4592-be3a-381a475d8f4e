<template>
  <div class="notMail">
    <div class="hearer-wrap">
      <div class="search-wrap">
        <div class="search-item">
          <label>快递公司</label>
          <el-input
            v-model.trim="searchInfo.expressCompany"
            size="small"
            placeholder="请输入"
            clearable
            class="input"
          ></el-input>
        </div>
        <div class="search-item">
          <label>快递单号</label>
          <el-input
            v-model.trim="searchInfo.expressNo"
            size="small"
            placeholder="请输入"
            clearable
            class="input"
          ></el-input>
        </div>
        <div class="search-item">
          <label style="width: 110px">体检号/姓名</label>
          <el-input
            v-model.trim="searchInfo.keyWord"
            size="small"
            placeholder="请输入"
            clearable
            class="input"
            @keyup.enter.native="getReportMailList"
          ></el-input>
        </div>
        <ButtonGroup
          :btnList="['查询', '导出', '邮寄', '作废']"
          @search="getReportMailList"
          @cancelClick="cancelClick"
          @mailClick="mailClick"
          @exports="exports"
        />
      </div>
      <!-- <div class="scan-mail">
        <h3>团体报告扫描邮寄：</h3>
        <div class="search-item">
          <label>邮寄地址</label>
          <el-input
            v-model.trim="searchInfo.mailAddress"
            size="small"
            placeholder="请输入"
            clearable
            class="input"
          ></el-input>
        </div>
        <div class="search-item">
          <label>联系电话</label>
          <el-input
            v-model.trim="searchInfo.contactTel"
            size="small"
            placeholder="请输入"
            clearable
            class="input"
          ></el-input>
        </div>
        <div class="search-item">
          <label>体检号</label>
          <el-input
            v-model.trim="searchInfo.regNo"
            size="small"
            placeholder="请输入"
            clearable
            class="input"
          ></el-input>
        </div>
      </div> -->
    </div>
    <div class="mailed-table">
      <div class="wrap-table">
        <PublicTable
          ref="view_Ref"
          isCheck
          :isSortShow="false"
          :viewTableList.sync="mailedTableData"
          :theads.sync="mailedTheads"
          :columnWidth="columnWidth"
          @selectionChange="selectionChange"
          @currentChange="rowClick"
          @rowDblclick="rowDblclick"
        >
          <template #peCls="{ scope }">
            <div>
              {{ G_EnumList['PeCls'][scope.row.peCls] }}
            </div>
          </template>
        </PublicTable>
      </div>
    </div>
    <el-dialog
      :visible.sync="mailShow"
      width="376px"
      top="20%"
      custom-class="mailShow"
      :close-on-click-modal="false"
      :before-close="cancel"
    >
      <div slot="title" class="dialog-title">修改邮寄信息</div>
      <el-form
        ref="mailForm"
        :model="mailForm"
        label-width="80px"
        :rules="rules"
      >
        <el-form-item label="快递单号" prop="expressNo">
          <el-input
            v-model.trim="mailForm.expressNo"
            size="small"
            class="medium-input"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="快递公司" prop="expressCompany">
          <el-input
            v-model.trim="mailForm.expressCompany"
            size="small"
            class="medium-input"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="邮寄地址" prop="mailAddress">
          <el-input
            v-model.trim="mailForm.mailAddress"
            size="small"
            class="medium-input"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="联系电话" prop="contactTel">
          <el-input
            v-model.trim="mailForm.contactTel"
            size="small"
            class="medium-input"
            placeholder="请输入"
            :maxlength="11"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel" size="small">取消</el-button>
        <el-button class="blue_btn" @click="mailSubmit" size="small"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ButtonGroup from '../components/buttonGroup.vue';
import PublicTable from '@/components/publicTable';
import { mapGetters } from 'vuex';
import { dataUtils } from '@/common';
export default {
  name: 'notMail',
  components: {
    ButtonGroup,
    PublicTable
  },
  data() {
    return {
      tabsIndex: 1,
      searchInfo: {
        radio: 3
      },
      mailedTheads: {
        name: '体检人',
        cardNo: '身份证',
        mailAddress: '邮寄地址',
        tel: '联系电话',
        peCls: '体检分类',
        clusName: '体检套餐',
        regNo: '体检号',
        companyName: '体检单位',
        applicant: '申请人',
        applicationTime: '申请时间'
      },
      mailedTableData: [],
      columnWidth: {
        cardNo: 180,
        mailAddress: 200,
        tel: 120,
        peCls: 120,
        clusName: 160,
        regNo: 130,
        companyName: 200,
        applicationTime: 170
      },
      selectRow: {},
      mailShow: false,
      mailForm: {
        expressNo: '',
        expressCompany: '',
        mailAddress: '',
        contactTel: ''
      },
      selection: [],
      regNo: [],
      rules: {
        expressNo: [
          { required: true, message: '请输入快递单号', trigger: 'blur' }
        ],
        expressCompany: [
          { required: true, message: '请输入快递公司', trigger: 'blur' }
        ],
        mailAddress: [
          { required: true, message: '请输入邮寄地址', trigger: 'blur' }
        ],
        contactTel: [
          { required: true, message: '请输入联系电话', trigger: 'blur' }
        ]
      },
      excelData: []
    };
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_peClsList', 'G_userInfo'])
  },
  methods: {
    // 查询/获取邮寄列表
    getReportMailList() {
      let data = {
        keyWord: this.searchInfo.keyWord,
        expressCompany: this.searchInfo.expressCompany,
        expressNo: this.searchInfo.expressNo,
        type: this.tabsIndex
      };
      this.$ajax.post(this.$apiUrls.GetReportMailList, data).then((r) => {
        console.log('GetReportMailList: ', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.mailedTableData = returnData;
      });
    },
    // 行点击
    rowClick(row) {
      let regNo = [];
      if (row) {
        this.selectRow = row;
        regNo.push(row.regNo);
        this.regNo = regNo;
      }
    },
    // 作废
    cancelClick() {
      if (this.regNo.length === 0) {
        this.$message({
          message: '请选择报告作废人员!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm('是否作废该人员的报告?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax
            .post(this.$apiUrls.DeleteReportReceiveMode, '', {
              query: {
                regNo: this.regNo
              }
            })
            .then((r) => {
              console.log('DeleteReportReceiveMode: ', r);
              let { success, returnData } = r.data;
              if (!success) return;
              this.$message({
                message: '报告作废成功!',
                type: 'success',
                showClose: true
              });
              this.getReportMailList();
              this.regNo = [];
            });
        })
        .catch(() => {});
    },
    // 邮寄
    mailClick() {
      if (this.regNo.length === 0) {
        this.$message({
          message: '请选择需要邮寄报告人!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$ajax
        .post(this.$apiUrls.UnMailedSendReport, this.regNo)
        .then((r) => {
          console.log('UnMailedSendReport: ', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.$message({
            message: '邮寄成功!',
            type: 'success',
            showClose: true
          });
          this.mailShow = false;
          this.getReportMailList();
        });
    },
    // 双击行
    rowDblclick() {
      this.mailShow = true;
      this.$nextTick(() => {
        this.resetForm('mailForm');
      });
      this.mailForm = {
        expressNo: this.selectRow.expressNo,
        expressCompany: this.selectRow.expressCompany,
        mailAddress: this.selectRow.mailAddress,
        contactTel: this.selectRow.contactTel
      };
    },
    // 修改邮寄
    mailSubmit() {
      this.$refs.mailForm.validate((valid) => {
        if (valid) {
          let data = {
            regNos: this.regNo,
            applicant: this.G_userInfo.codeOper.name,
            ...this.mailForm
          };
          this.$ajax.post(this.$apiUrls.UpdateReportMail, data).then((r) => {
            console.log('UpdateReportMail: ', r);
            let { success, returnData } = r.data;
            if (!success) return;
            this.$message({
              message: '修改成功!',
              type: 'success',
              showClose: true
            });
            this.mailShow = false;
            this.getReportMailList();
          });
        }
      });
    },
    // 取消
    cancel() {
      this.mailShow = false;
    },
    // 表格多选
    selectionChange(rows) {
      console.log('rows: ', rows);
      this.regNo = rows.map((item) => item.regNo);
      this.selection = rows;
    },
    // 重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    // 导出
    exports() {
      let tableCom_Ref = this.$refs.view_Ref.$refs.tableCom_Ref;
      if (tableCom_Ref.selection.length == 0) {
        return this.$message({
          message: '请选择至少一条数据进行操作！',
          type: 'warning',
          showClose: true
        });
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.excelData = tableCom_Ref.selection.map((item) => {
            return {
              name: item.name,
              cardNo: item.cardNo,
              mailAddress: item.mailAddress,
              tel: item.tel,
              peCls: this.G_EnumList['PeCls'][item.peCls],
              clusName: item.clusName,
              regNo: item.regNo,
              companyName: item.companyName,
              applicant: item.applicant,
              applicationTime: item.applicationTime
            };
          }); // multipleSelection是一个数组，存储表格中选择的行的数据。
          this.excelData.map((item, i) => {
            item.index = i + 1;
          });
          this.$nextTick(function () {
            this.export2Excel();
          });
        })
        .catch(() => {});
    },
    // 数据写入excel
    export2Excel() {
      var that = this;
      require.ensure([], () => {
        const { export_json_to_excel } = require('@/utils/Export2Excel'); // 这里必须使用绝对路径，使用@/+存放export2Excel的路径
        const tHeader = Object.values(this.theads); // 导出的表头名信息
        const filterVal = Object.keys(this.theads); // 导出的表头字段名
        const list = that.excelData;
        const data = that.formatJson(filterVal, list);
        console.log(data);
        const name = that.excelName;
        export_json_to_excel(tHeader, data, name);
      });
    }
  }
};
</script>

<style lang="less" scoped>
.notMail {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
  .hearer-wrap {
    background: #fff;
    padding: 18px;
    margin-bottom: 10px;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
  }
  .search-wrap {
    display: flex;
    align-items: center;
  }
  .search-item {
    display: flex;
    align-items: center;
    margin-right: 18px;
    label {
      width: 80px;
      text-align: right;
      margin-right: 10px;
      font-size: 14px;
      font-weight: 600;
    }
  }
  .scan-mail {
    background: rgba(23, 112, 223, 0.1);
    border-radius: 4px;
    padding: 12px 0 12px 28px;
    display: flex;
    align-items: center;
    margin-top: 18px;
    h3 {
      font-size: 18px;
      margin-right: 62px;
    }
  }
  .radio {
    margin-bottom: 18px;
  }
  .mailed-table {
    flex: 1;
    background: #fff;
    border-radius: 4px;
    padding: 10px;
    overflow: auto;
  }
  .wrap-table {
    height: 100%;
    border: 1px solid rgba(178, 190, 195, 0.5);
    border-radius: 4px;
    overflow: auto;
  }
  .dialog-title {
    background: #d1e2f9;
    font-size: 18px;
    padding: 12px 18px;
    font-weight: 600;
  }
  /deep/.el-dialog__header {
    padding: 0;
  }
  /deep/.el-form-item__label {
    font-size: 14px;
    color: #2d3436;
    font-weight: 600;
  }
  /deep/.el-form-item {
    margin-bottom: 16px;
  }
}
</style>
