<!--
 * @FilePath: \shenshan\KrPeis\src\pages\reportManagement\reportPrinting.vue
 * @Description:  报告打印
 * @Author: 
 * @Date: 2024-03-19 17:23:58
 * @Version: 0.0.1
 * @LastEditors: key
 * @LastEditTime: 2025-04-17 11:42:24
*
-->
<template>
  <!-- 报告打印 -->
  <div class="reportPrinting">
    <div class="main">
      <div class="main-left">
        <div class="header">
          <!-- <div class="search-wrap">
        <div class="search-item">
          <el-input
            v-model.trim="searchInfo.name"
            size="mini"
            placeholder="姓名"
            clearable
            class="input"
            @keyup.enter.native="getReportPrintList"
          ></el-input>
        </div>
      </div> -->
          <div class="search-wrap">
            <div class="search-item" style="flex: 1; margin-right: 5px">
              <el-input
                v-model.trim="searchInfo.keyword"
                style="width: 100%"
                size="mini"
                placeholder="体检号/身份证/姓名"
                clearable
                class="input"
                @keyup.enter.native="getReportPrintList"
              >
              </el-input>
            </div>
            <div
              class="search-item"
              style="width: 200px"
              v-if="C_physicalMode.includes('普检')"
            >
              <el-select
                v-model.trim="searchInfo.peCls"
                placeholder="体检分类"
                size="mini"
                class="input"
                clearable
                @change="getReportPrintList"
              >
                <el-option
                  :label="item.label"
                  :value="item.value"
                  v-for="item in G_peClsList"
                  :key="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="search-wrap">
            <div class="search-item" style="flex: 1">
              <el-cascader
                ref="company_cascader_ref"
                placeholder="单位"
                style="width: 100%"
                v-model="searchInfo.companyCode"
                :options="companyList"
                :props="{ multiple: false }"
                clearable
                filterable
                size="mini"
                collapse-tags
                @change="companyChange"
                :filter-method="filterMethod"
              >
              </el-cascader>
            </div>
            <div class="search-item" style="width: 200px; margin-left: 5px">
              <el-select
                style="width: 100%"
                class="select"
                v-model.trim="searchInfo.companyDeptCode"
                placeholder="部门"
                size="mini"
                filterable
                clearable
                :disabled="isHavue"
                @change="getReportPrintList"
              >
                <el-option
                  v-for="(item, index) in companyDeptList"
                  :key="index"
                  :label="item.deptName"
                  :value="item.deptCode"
                ></el-option>
              </el-select>
            </div>
          </div>
          <div
            class="search-wrap"
            style="justify-content: space-between; flex-wrap: wrap"
          >
            <div class="search-item" style="width: 100px; margin-right: 5px">
              <el-select
                v-model.trim="status"
                placeholder="状态"
                size="mini"
                filterable
                @change="sortFun"
              >
                <el-option
                  :label="item.label"
                  :value="item.value"
                  v-for="item in statusList"
                  :key="item.value"
                >
                </el-option>
              </el-select>
            </div>
            <div
              class="search-item"
              :class="C_isOccupation ? 'occupationa' : 'occupationb'"
            >
              <el-date-picker
                :picker-options="{ shortcuts: G_datePickerShortcuts }"
                v-model.trim="searchInfo.date"
                style="width: 100%"
                type="daterange"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="mini"
                class="date-picker"
                @change="getReportPrintList"
              >
              </el-date-picker>
            </div>
            <div class="search-item" v-if="C_isOccupation">
              <el-radio-group
                v-model="searchInfo.isOccupation"
                @change="getReportPrintList"
              >
                <el-radio :label="false" v-if="C_physicalMode.includes('普检')"
                  >健康体检</el-radio
                >
                <el-radio :label="true" v-if="C_physicalMode.includes('职检')"
                  >职业体检</el-radio
                >
              </el-radio-group>
            </div>
            <div style="margin-left: 5px">
              <ButtonGroup :btnList="['查询']" @search="getReportPrintList">
              </ButtonGroup>
            </div>
          </div>
        </div>
        <div class="left-table">
          <PublicTable
            ref="view_Ref"
            isCheck
            :isSortShow="false"
            :viewTableList.sync="tableData"
            :theads.sync="theads"
            :columnWidth="{
              peCls: 104,
              regNo: 124,
              name: 76,
              reportPrinted: 150,
              reportPrintedTime: 110,
              reportType: 180
            }"
            :headerCellStyle="headerCellStyle"
            v-model="selection"
            @currentChange="rowClick"
            @select="select"
            @selectAll="selectAll"
            :tableCellClassName="stateRow"
            :isStripe="false"
            :columnSort="columnSort"
            :tableLoading="tableLoading"
          >
            <template #peCls="{ scope }">
              <div>
                {{ G_EnumList['PeCls'][scope.row.peCls] }}
              </div>
            </template>
            <template #reportPrinted="{ scope }">
              <div>
                {{ scope.row.reportPrinted ? '是' : '否' }}
              </div>
            </template>
            <template #reportPrintedTime="{ scope }">
              <div>
                {{
                  scope.row.reportPrintedTime === null
                    ? scope.row.reportPrintedTime
                    : dateFormat(scope.row.reportPrintedTime)
                }}
              </div>
            </template>
            <template #reportType="{ scope }">
              <div>
                {{ G_EnumList['CodeReportType'][scope.row.reportType] }}
              </div>
            </template>
          </PublicTable>
        </div>
      </div>
      <div class="main-right">
        <div class="right-btn">
          <div class="search-item" style="width: 180px; margin-right: 5px">
            <el-input
              v-model.trim="searchInfo.regNo"
              size="mini"
              placeholder="体检号"
              clearable
              class="input"
              @keyup.enter.native="regNoEnter"
            ></el-input>
          </div>
          <!-- <div class="btn-item">
            <label>状态</label>
            <el-select
              v-model.trim="status"
              placeholder="请选择"
              size="mini"
              filterable
              @change="sortFun"
            >
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in statusList"
                :key="item.value"
              >
              </el-option>
            </el-select>
          </div> -->
          <!-- <div class="btn-item">
            <label>显示比例</label>
            <el-select
              v-model.trim="searchInfo.barcode"
              placeholder="请选择"
              size="mini"
              filterable
              clearable
            >
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in G_peClsList"
                :key="item.value"
              >
              </el-option>
            </el-select>
          </div> -->
          <!-- 职业病报告格式 -->
          <div>
            <el-checkbox v-model="lockingReportType" class="checkbox"
              >锁定报告类型</el-checkbox
            >
            <!-- <el-checkbox v-model="isPrintOriginalReport"
              >是否打印原始报告</el-checkbox
            > -->
          </div>
          <div class="btn-item" v-if="searchInfo.isOccupation">
            <!-- <label>报告类型</label> -->
            <el-select
              v-model.trim="searchInfo.reportType"
              placeholder="请选择"
              size="mini"
              class="input"
              filterable
              @change="getPDF"
              :disabled="!lockingReportType"
            >
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in G_CodeOccupationalReportType"
                :key="item.value"
              >
              </el-option>
            </el-select>
          </div>
          <!-- 健康体检报告格式 -->
          <div class="btn-item" v-else>
            <!-- <label>报告类型</label> -->
            <el-select
              v-model.trim="searchInfo.reportType"
              placeholder="请选择"
              size="mini"
              class="input"
              filterable
              @change="getPDF"
              :disabled="!lockingReportType"
            >
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in G_codeReportType"
                :key="item.value"
              >
              </el-option>
            </el-select>
          </div>
          <div style="margin-right: 10px">
            <el-select
              size="mini"
              v-model="reportPrintVal"
              placeholder="请选择打印机"
            >
              <el-option
                v-for="item in G_printerList"
                :key="item"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
          </div>
          <div style="margin-right: 10px; min-width: 135px">
            <el-radio-group v-model="pagePrintShow" size="mini">
              <el-radio
                :label="i.value"
                v-for="i in printModel"
                :key="i.value"
                >{{ i.label }}</el-radio
              >
            </el-radio-group>
          </div>
          <div>
            <ButtonGroup>
              <template #footAdd>
                <el-button
                  v-if="pagePrintShow === 2"
                  icon="iconfont icon-dayin-"
                  size="mini"
                  @click="getCheckPagePdf(true, true)"
                  >打印</el-button
                >
                <el-button
                  v-if="pagePrintShow === 1"
                  icon="iconfont icon-dayin-"
                  size="mini"
                  @click="printsClick"
                  >打印</el-button
                >
                <el-button
                  v-if="pagePrintShow === 2"
                  icon="el-icon-download"
                  size="mini"
                  @click="getCheckPagePdf(false, true)"
                  >下载</el-button
                >
                <el-button
                  v-if="pagePrintShow === 1"
                  icon="el-icon-download"
                  size="mini"
                  @click="batchDownloadPDF"
                  >下载</el-button
                >
                <el-popover placement="bottom" trigger="hover">
                  <el-button
                    @click="mailClick"
                    class="violet_btn btn"
                    size="mini"
                    icon="iconfont icon-a-xinxiyoujixiaoxi"
                    >邮寄</el-button
                  >
                  <el-button
                    @click="receiveClick"
                    class="violet_btn btn"
                    size="mini"
                    icon="iconfont icon-zhanghulingqu"
                    >领取</el-button
                  >
                  <el-button
                    slot="reference"
                    icon="el-icon-s-operation"
                    size="mini"
                    style="margin-left: 10px"
                    >更多</el-button
                  >
                </el-popover>
              </template>
            </ButtonGroup>
          </div>
        </div>
        <div class="right-content">
          <div v-if="loading" class="loading">正在加载中,请稍候...</div>
          <embed
            type="application/pdf"
            width="100%"
            :src="pdfSrc"
            height="100%"
            v-else-if="!loading && pagePrintShow == 1"
          />
          <!-- 分页打印 -->
          <div class="page_print" v-else-if="!loading && pagePrintShow == 2">
            <!-- 页码 -->
            <ul class="pageNum_wrap" ref="pdfNav">
              <li
                v-for="i in pageTotalNum"
                :key="i.pageNum"
                :class="{ active_page: activePage == i.pageNum }"
              >
                <el-checkbox
                  @change="checkPageNumChange(i)"
                  v-model="i.checked"
                  class="page_checkbox"
                  size="medium"
                  removeAriaHidden
                ></el-checkbox>
                <a
                  @click="anchorPointClick('#pageNum_' + i.pageNum, i.pageNum)"
                >
                  <div class="pdf_wrap">
                    <pdf
                      :ref="'myPdfComponent_' + i.pageNum"
                      :src="pdfSrc"
                      :page="i.pageNum"
                    ></pdf>
                  </div>
                  <div class="pageNum">第 {{ i.pageNum }} 页</div>
                </a>
              </li>
              <el-empty
                image=" "
                :image-size="200"
                v-if="pageTotalNum.length == 0"
              ></el-empty>
            </ul>
            <!-- PDF显示区域 -->
            <ul
              class="preview_pdf"
              ref="pdfWrap"
              v-scroll="handleScroll"
              @mouseover="scrollFlag = true"
            >
              <li
                v-for="i in pageTotalNum"
                :key="i.pageNum"
                :id="'pageNum_' + i.pageNum"
              >
                <pdf
                  :ref="'pdf_' + i.pageNum"
                  :src="pdfSrc"
                  :page="i.pageNum"
                ></pdf>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      :visible.sync="mailShow"
      width="376px"
      top="20%"
      custom-class="mailShow"
      :close-on-click-modal="false"
      :before-close="cancel"
    >
      <div slot="title" class="dialog-title">邮寄</div>
      <el-form
        ref="mailForm"
        :model="mailForm"
        label-width="80px"
        :rules="mailRules"
      >
        <el-form-item label="快递单号" prop="expressNo">
          <el-input
            v-model.trim="mailForm.expressNo"
            size="small"
            class="medium-input"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="快递公司" prop="expressCompany">
          <el-input
            v-model.trim="mailForm.expressCompany"
            size="small"
            class="medium-input"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="邮寄地址" prop="mailAddress">
          <el-input
            v-model.trim="mailForm.mailAddress"
            size="small"
            class="medium-input"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="联系电话" prop="contactTel">
          <el-input
            v-model.trim="mailForm.contactTel"
            size="small"
            class="medium-input"
            placeholder="请输入"
            :maxlength="11"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel" size="small">取消</el-button>
        <el-button class="blue_btn" @click="mailSubmit" size="small"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="receiveShow"
      width="376px"
      top="20%"
      custom-class="mailShow"
      :close-on-click-modal="false"
      :before-close="cancel"
    >
      <div slot="title" class="dialog-title">领取模式</div>
      <el-form
        ref="receiveForm"
        :rules="rules"
        :model="receiveForm"
        label-width="80px"
      >
        <el-form-item label="" prop="radio">
          <el-radio-group v-model="receiveForm.radio">
            <el-radio :label="1">本人</el-radio>
            <el-radio :label="2">代取</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="代取人"
          prop="substitution"
          v-if="receiveForm.radio === 2"
        >
          <el-input
            v-model.trim="receiveForm.substitution"
            size="small"
            :maxlength="10"
            class="medium-input"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel" size="small">取消</el-button>
        <el-button class="blue_btn" @click="receiveSubmit" size="small"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <ReportPrint
      ref="print_ref"
      v-model="previewShow"
      :dataInfo.sync="dataInfo"
      :printerTypeList="printerTypeList"
      :batchPrintList="selection"
    />
    <report-loading
      :reportLoadingShow="reportLoadingShow"
      :percentage="percentage"
      :message="reportPrintSchedule"
    />
  </div>
</template>

<script>
import ButtonGroup from './components/buttonGroup.vue';
import PublicTable from '@/components/publicTable';
import { mapGetters, mapMutations } from 'vuex';
import { dataUtils } from '@/common';
import printMixins from '@/components/newPrintMixins';
import ReportPrint from '@/components/reportPrint.vue';
import { batchDownloadFile, newBatchDownloadFile } from '@/common/zipUtil';
import { storage } from '@/common';
import pagePrintJS from '@/components/print/pagePrint';

export default {
  name: 'reportPrinting',
  mixins: [printMixins, pagePrintJS],
  components: {
    ButtonGroup,
    PublicTable,
    ReportPrint
  },
  data() {
    return {
      headerCellStyle: {
        height: '20px',
        background: 'rgba(23,112,223,.2)',
        fontSize: '14px',
        color: '#2D3436',
        padding: '0 '
      },
      printerTypeList: [
        {
          printerType: 0, //打印机类型 0 普通打印机，1 条码打印机
          label: '报告',
          checked: true
        }
      ],
      searchInfo: {
        regNo: '',
        // name: "",
        // cardNo: "",
        keyword: '',
        peCls: '',
        companyCode: '',
        companyDeptCode: '',
        date: [new Date(), new Date()],
        reportType: '',
        isOccupation: false
      },
      isHavue: true,
      companyDeptList: [],
      tableData: [],
      theads: {
        regNo: '体检号',
        name: '姓名',
        peCls: '体检分类',
        reportPrinted: '报告是否打印',
        reportPrintedTime: '报告打印时间',
        reportType: '报告类型'
      },
      checked: false,
      mailShow: false,
      mailForm: {
        expressNo: '',
        expressCompany: '',
        mailAddress: '',
        contactTel: ''
      },
      receiveShow: false,
      receiveForm: {
        radio: 1,
        substitution: ''
      },
      rules: {
        substitution: [
          { required: true, message: '请输入代取人', trigger: 'blur' }
        ]
      },
      mailRules: {
        expressNo: [
          { required: true, message: '请输入快递单号', trigger: 'blur' }
        ],
        expressCompany: [
          { required: true, message: '请输入快递公司', trigger: 'blur' }
        ],
        mailAddress: [
          { required: true, message: '请输入邮寄地址', trigger: 'blur' }
        ],
        contactTel: [
          { required: true, message: '请输入联系电话', trigger: 'blur' }
        ]
      },
      companyList: [],
      statusList: [
        {
          value: null,
          label: '全部'
        },
        {
          value: false,
          label: '未打印'
        },
        {
          value: true,
          label: '已打印'
        }
      ],
      selectRow: {},
      lockingReportType: false,
      isPrintOriginalReport: false,
      selection: [],
      regNo: [],
      pdfSrc: '',
      fileStream: '', //文件流
      excelData: [],
      previewShow: false,
      dataInfo: {},
      columnSort: ['reportPrinted'],
      status: null,
      copyTableData: [],
      loading: false,
      isSelectAllFlag: false,
      tableLoading: false
    };
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_peClsList',
      'G_codeReportType',
      'G_userInfo',
      'G_CodeOccupationalReportType',
      'G_datePickerShortcuts',
      'G_config'
    ]),
    C_isOccupation() {
      return this.G_config.physicalMode.includes('职检');
    },
    C_physicalMode() {
      return this.G_config?.physicalMode;
    }
  },
  created() {
    this.getCompanyList();
    this.getReportPrintList();
    this.connectPrint((r) => {
      const dataInfo = JSON.parse(r.data);

      this.M_printerList(dataInfo.Data);
      const print = storage.local.get('print');

      this.reportPrintVal = print?.printer || dataInfo.Data[0];
    });
  },
  mounted() {
    this.searchInfo.isOccupation = this.G_config?.physicalMode.includes('普检')
      ? false
      : true;
  },
  methods: {
    ...mapMutations(['M_printerList']),
    // 打印完成的回调
    printFinishInit() {
      this.getReportPrintList();
      this.selection = [];
    },
    // 获取单位下拉
    getCompanyList() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    //查询公司部门信息
    getDepartList(companyCode) {
      this.searchInfo.companyDeptCode = '';
      this.isHavue = true;
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanyDepartment, {
          companyCode: companyCode?.[0] || '',
          deptCode: '',
          deptName: ''
        })
        .then((r) => {
          this.isHavue = false;
          let { success, returnData } = r.data;
          if (!success) return;
          this.companyDeptList = returnData || [];
        });
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    //单位下拉变化
    companyChange(data) {
      this.getDepartList(data);
      if (!data || data.length === 0) return;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.searchInfo.date = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];

      this.getReportPrintList();
    },
    //体检号回车
    regNoEnter() {
      this.getReportPrintList();
      this.searchInfo.regNo = '';
    },
    // 查询/获取报告打印列表
    getReportPrintList() {
      this.checkPageNum = [];
      this.pageTotalNum = [];
      this.activePage = 0;
      let data = {
        regNo: this.searchInfo.regNo,
        keyword: this.searchInfo.keyword,
        peCls: parseInt(this.searchInfo.peCls),
        companyCode: this.searchInfo.companyCode[0],
        companyDeptCode: this.searchInfo.companyDeptCode,
        beginDate: this.searchInfo?.date?.[0] || '',
        endDate: this.searchInfo?.date?.[1] || '',
        isOccupation: this.searchInfo.isOccupation
      };
      data.peCls = data.peCls >= 0 ? data.peCls : null;
      if (!this.checkSearchData(data)) return;

      this.status = null;
      this.pdfSrc = ''; //每次获取PDF先清空pdf
      // this.searchInfo.reportType = "";
      this.tableLoading = true;
      this.$ajax
        .post(this.$apiUrls.NewGetReportPrintList, data)
        .then((r) => {
          console.log('GetReportPrintList: ', r);
          this.tableLoading = false;
          let { success, returnData } = r.data;
          if (!success) return;
          this.tableData = returnData;
          this.copyTableData = returnData;
          this.selectRow = {};
          if (returnData.length == 1) {
            this.rowClick(returnData[0]);
          }
        })
        .catch((r) => {
          this.tableLoading = false;
        });
    },
    //手动打印状态过滤左边表格
    sortFun() {
      this.pdfSrc = '';
      this.searchInfo.reportType = '';
      if (this.status == null) {
        this.tableData = this.copyTableData;
      } else {
        this.tableData = this.copyTableData.filter((item) => {
          return item.reportPrinted == this.status;
        });
      }
    },
    // 日期格式化
    dateFormat(date) {
      return dataUtils.subBlankDate(date);
    },
    idCardClick() {},
    // 邮寄
    mailClick() {
      if (this.regNo.length === 0 && JSON.stringify(this.selectRow) === '{}') {
        this.$message({
          message: '请选择邮寄报告的体检人!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.mailShow = true;
      this.$nextTick(() => {
        this.resetForm('mailForm');
      });
    },
    // 领取
    receiveClick() {
      if (this.regNo.length === 0 && JSON.stringify(this.selectRow) === '{}') {
        this.$message({
          message: '请选择领取报告的体检人!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.receiveShow = true;
      this.$nextTick(() => {
        this.resetForm('receiveForm');
      });
    },
    // 表格单击选中
    async rowClick(row) {
      if (!row) return;
      // let regNo = [];
      this.selectRow = row;
      if (!this.lockingReportType) {
        this.searchInfo.reportType = row.reportType; //把点击行的报告类型赋值右边下拉的报告类型
      }
      if (row.reportPDFUrl && !this.lockingReportType) {
        this.loading = true;
        try {
          let res = await this.retrieveFileStream(
            row.regNo,
            false,
            row.reportPDFUrl
          );
          this.pdfSrc = res + (row.reportPrinted ? '' : '#toolbar=0');
          this.getNumPages(res);
        } finally {
          this.loading = false;
        }
        return;
      }
      this.getPDF();
    },
    /**
     * @description: 获取文件流
     * @param {*}
     * @return {*}
     * @author: key
     */
    async retrieveFileStream(regNo, reportType, src) {
      let url;
      if (reportType) {
        url = `/PrintFile/ReportPdf/${regNo}?reportCode=${reportType}&isOccupation=${this.searchInfo.isOccupation}`;
      } else {
        url = src + `&isOccupation=${this.searchInfo.isOccupation}`;
      }
      const res = await this.$ajax.get(url, { responseType: 'arraybuffer' });
      this.previewBuffer = res.data;
      const blob = new Blob([res.data], { type: 'application/pdf' });
      const pdfUrl = URL.createObjectURL(blob);
      return pdfUrl;
    },
    // 获取PDF
    async getPDF() {
      if (JSON.stringify(this.selectRow) === '{}') {
        this.$message({
          message: '请选择要预览的报告人员再切换报告类型!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.pdfSrc = ''; //每次获取PDF先清空pdf
      this.loading = true;
      ///api/PrintFile/ReportPdf/{regNo}  reportCode
      try {
        let res = await this.retrieveFileStream(
          this.selectRow.regNo,
          this.searchInfo.reportType
        );
        this.pdfSrc = res + (this.selectRow.reportPrinted ? '' : '#toolbar=0');
      } finally {
        this.loading = false;
      }
    },
    // 单个选中
    select(selection, row, checkList) {
      this.regNo = checkList.map((item) => item.regNo);
      this.selection = checkList;
      console.log(this.selection, this.regNo);
    },
    // 全选
    selectAll(rows, checkList) {
      console.log(rows, checkList);
      this.selection = checkList;
      this.regNo = checkList.map((item) => item.regNo);
      console.log(this.selection, this.regNo);
    },
    // 邮寄弹窗提交
    mailSubmit() {
      let data = {
        regNos: this.regNo.length > 0 ? this.regNo : [this.selectRow.regNo],
        applicant: this.G_userInfo.codeOper.name,
        ...this.mailForm
      };
      this.$ajax.post(this.$apiUrls.UnPrintSendRepor, data).then((r) => {
        console.log('UnPrintSendRepor: ', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.$message({
          message: '报告邮寄成功!',
          type: 'success',
          showClose: true
        });
        this.mailShow = false;
        this.getReportPrintList();
      });
    },
    // 领取弹窗提交
    receiveSubmit() {
      this.$refs.receiveForm.validate((valid) => {
        if (valid) {
          let data = {
            regNos: this.regNo.length > 0 ? this.regNo : [this.selectRow.regNo],
            mode: this.receiveForm.radio,
            recipient: this.receiveForm.substitution,
            applicant: this.G_userInfo.codeOper.name
            // applicationTime: dataUtils.dateToString(new Date())
          };
          this.$ajax
            .post(this.$apiUrls.SaveReportReceiveMode, data)
            .then((r) => {
              console.log('SaveReportReceiveMode: ', r);
              let { success, returnData } = r.data;
              if (!success) return;
              this.$message({
                message: '报告领取成功!',
                type: 'success',
                showClose: true
              });
              this.receiveShow = false;
              this.getReportPrintList();
            });
        }
      });
    },
    // 取消
    cancel() {
      this.mailShow = false;
      this.receiveShow = false;
    },
    // 打印
    printsClick() {
      if (
        this.selection.length === 0 &&
        JSON.stringify(this.selectRow) === '{}'
      ) {
        this.$message({
          message: '请选择要打印报告人员!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      // console.log(this.lockingReportType, this.searchInfo.reportType);
      if (this.lockingReportType && this.searchInfo.reportType == '') {
        this.$message({
          message: '请选择报告类型!',
          type: 'warning',
          showClose: true
        });
        return;
      }

      this.dataInfo = {
        guidanceType: this.lockingReportType
          ? this.searchInfo.reportType
          : this.selection[0]?.reportType || this.selectRow.reportType,
        regNo: this.selection[0]?.regNo || this.selectRow.regNo,
        lockingReportType: this.lockingReportType
      };
      this.reportLoadingShow = true;
      this.percentage = 0;
      this.currentNum = 0;
      if (this.selection.length == 0) {
        this.reportPrint([this.selectRow], this.searchInfo.isOccupation);
      } else {
        this.reportPrint(this.selection, this.searchInfo.isOccupation);
      }
    },
    // 打印状态更新
    updatePrintFlag() {
      this.$ajax
        .post(this.$apiUrls.UpdateReportPrinted, '', {
          query: {
            regNo: this.regNo
          }
        })
        .then((r) => {
          console.log('UpdateReportPrinted: ', r);
          let { success, returnData } = r.data;
          if (!success) return;
        });
    },
    // 导出
    exports() {
      let tableCom_Ref = this.$refs.view_Ref.$refs.tableCom_Ref;
      if (tableCom_Ref.selection.length == 0) {
        return this.$message({
          message: '请选择至少一条数据进行操作！',
          type: 'warning',
          showClose: true
        });
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.excelData = tableCom_Ref.selection.map((item, i) => {
            return {
              ...item,
              index: i + 1,
              peCls: this.G_EnumList['PeCls'][item.peCls],
              reportPrinted: item.reportPrinted ? '是' : '否',
              reportType: this.G_EnumList['CodeReportType'][item.reportType]
            };
          }); // multipleSelection是一个数组，存储表格中选择的行的数据。
          this.$nextTick(function () {
            this.export2Excel();
          });
        })
        .catch(() => {});
    },
    // 数据写入excel
    export2Excel() {
      var that = this;
      require.ensure([], () => {
        const { export_json_to_excel } = require('@/utils/Export2Excel'); // 这里必须使用绝对路径，使用@/+存放export2Excel的路径
        const tHeader = ['序号'].concat(Object.values(this.theads)); // 导出的表头名信息
        const filterVal = ['index'].concat(Object.keys(this.theads)); // 导出的表头字段名
        const list = that.excelData;
        const data = that.formatJson(filterVal, list);
        const name = that.excelName;
        export_json_to_excel(tHeader, data, name);
      });
    },
    // 格式转换
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) => filterVal.map((j) => v[j]));
    },
    // 重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    // 根据报告是否打印变化行颜色
    stateRow({ row }) {
      if (row.reportPrinted) {
        return 'background: rgb(38 ,126, 235 , 0.5)';
      }
    },

    /**
     * @author: justin
     * @description: 检测检索表单
     * @param {*} data
     * @return {*}
     */
    checkSearchData(data) {
      const msg = '请至少填写一个检索条件！';
      if (!data || typeof data !== 'object') {
        this.$message.warning(msg);
        return false;
      }

      let emptyCount = 0;
      for (let key in data) {
        const value = data[key];
        if (value === null || value === undefined) {
          emptyCount++;
        } else if (typeof value === 'string' && value.trim().length === 0) {
          emptyCount++;
        }
      }

      if (emptyCount === Object.keys(data).length) {
        this.$message.warning(msg);
        return false;
      }

      return true;
    },

    /**
     * @author: justin
     * @description: 批量下载PDF
     * @return {*}
     */
    async batchDownloadPDF() {
      let tableCom_Ref = this.$refs.view_Ref;
      console.log(tableCom_Ref.selection);
      if (
        tableCom_Ref.selection.length == 0 &&
        JSON.stringify(this.selectRow) === '{}'
      ) {
        return this.$message.warning('请选择数据进行操作！');
      }
      if (this.lockingReportType && this.searchInfo.reportType == '') {
        return this.$message.warning('已锁定报告类型,请先选择报告类型！');
      }
      if (!(tableCom_Ref.selection.length > 1)) {
        let url =
          tableCom_Ref.selection[0]?.reportPDFUrl ||
          this.selectRow.reportPDFUrl;
        // if(this.lockingReportType){
        //   url = `${url}?reportCode=${this.searchInfo.reportType}`
        // }
        const res = await this.$ajax.get(url, { responseType: 'arraybuffer' });
        const blob = new Blob([res.data], { type: 'application/pdf' });
        const href = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = href;
        a.download =
          tableCom_Ref.selection.length == 0
            ? `${this.selectRow.name}_${this.selectRow.regNo}_报告.pdf`
            : `${tableCom_Ref.selection[0].name}_${tableCom_Ref.selection[0].regNo}_报告.pdf`;
        a.click();
        URL.revokeObjectURL(a.href);
        return;
      }
      this.$confirm('确定批量下载PDF报告文件？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const fileList = tableCom_Ref.selection
          .filter((x) => x.reportPDFUrl && x.reportPDFUrl.length > 0)
          .map((x) => {
            return {
              url:
                x.reportPDFUrl +
                (this.lockingReportType
                  ? `?reportCode=${this.searchInfo.reportType}`
                  : ''),
              name: `${x.name}_${x.regNo}_报告.pdf`
            };
          });
        newBatchDownloadFile(
          fileList,
          `PDF报告文件${dataUtils.getNowDateTiemNo()}`
        );
      });
    }
  },
  watch: {
    tableData: {
      handler(n, o) {
        this.isSelectAllFlag = false;
      },
      deep: true
    }
  }
  // destroyed() {
  //   if (JSON.stringify(this.ws) == "{}") return;
  //   this.ws.closeWebSocket && this.ws.closeWebSocket();
  //   this.ws.reConnect = () => {};
  //   this.ws.createWebSocket = () => {};
  //   this.ws.heartCheck.stop();
  // },
};
</script>

<style lang="less" scoped>
.reportPrinting {
  color: #2d3436;
  display: flex;
  flex-direction: column;
  overflow: auto;
  .header {
    // padding: 18px;
    padding-bottom: 0;
    background: #fff;
    border-radius: 4px;
    // margin-bottom: 10px;
  }
  .search-wrap {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
  }
  .search-item {
    display: flex;
    align-items: center;
  }
  .date-picker {
    width: calc(100% - 60px);
  }
  .input {
    width: 100%;
  }
  .search-btn {
    margin-left: 6px;
  }
  .main {
    flex: 1;
    display: flex;
    overflow: auto;
  }
  .main-left,
  .main-right {
    background: #fff;
    border-radius: 4px;
    padding: 10px;
    overflow: auto;
  }
  .main-left {
    display: flex;
    flex-direction: column;
    width: 400px;
    margin-right: 12px;
    overflow: hidden;
  }
  .main-right {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .left-table {
    border: 1px solid rgba(178, 190, 195, 0.5);
    border-radius: 4px;
    flex: 1;
  }
  .right-btn {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    // padding: 0 20px;
  }
  .btn-item {
    display: flex;
    align-items: center;
    margin-right: 10px;
    &:nth-child(1) {
      flex: 1;
    }
    &:nth-child(2) {
      flex: 2;
    }
    &:nth-child(3) {
      flex: 2;
    }
    &:nth-child(4) {
      flex: 3;
    }
    label {
      width: 82px;
      text-align: right;
      margin-right: 10px;
      white-space: nowrap;
      font-size: 14px;
      font-weight: 600;
    }
  }
  .right-content {
    flex: 1;
    background: rgba(23, 112, 223, 0.1);
    border-radius: 4px;
    display: flex;
    justify-content: center;
    overflow: auto;
    .loading {
      width: 100%;
      height: 100;
      justify-content: center;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .page_print {
      height: 100%;
      overflow: auto;
      display: flex;
      flex: 1;
      flex-shrink: 0;
      .pageNum_wrap {
        width: 250px;
        height: 100%;
        overflow: auto;
        li {
          text-align: center;
          border-radius: 5px;
          background: #ccc;
          padding: 10px;
          margin-bottom: 10px;
          position: relative;
          border: 3px solid #fff;
          .pageNum {
            padding-top: 5px;
          }
          .page_checkbox {
            position: absolute;
            top: 0;
            left: 0;
            /deep/ .el-checkbox__inner {
              height: 20px;
              width: 20px;
              &::after {
                top: 3px;
                left: 7px;
              }
            }
          }
        }
        .active_page {
          border-color: #1770df;
        }
      }
      .preview_pdf {
        flex: 1;
        flex-shrink: 0;
        overflow: auto;
        background: #000;
        padding: 10px;
        border-radius: 5px;
        li {
          width: 80%;
          margin: 0 auto;
          margin-bottom: 20px;
          min-width: 595px;
          // height: 842px;
        }
      }
    }
  }
  .checkbox {
    margin-right: 10px;
  }
  .dialog-title {
    background: #d1e2f9;
    font-size: 18px;
    padding: 12px 18px;
    font-weight: 600;
  }
  /deep/.el-dialog__header {
    padding: 0;
  }
  /deep/.el-form-item__label {
    font-size: 14px;
    color: #2d3436;
    font-weight: 600;
  }
  /deep/.el-form-item {
    margin-bottom: 16px;
  }
  /deep/ .el-table__cell {
    vertical-align: baseline;
  }
}
</style>
<style lang="less">
.reportPrinting {
  .mailShow {
    .el-dialog__body {
      padding-right: 28px;
      padding-bottom: 8px;
    }
    .el-dialog__footer {
      padding-top: 0;
      padding-right: 28px;
    }
  }
}
.occupationa {
  width: 275px;
}
.occupationb {
  display: flex;
  flex: 1;
}
</style>
