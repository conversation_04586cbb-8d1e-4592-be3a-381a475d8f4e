<template>
  <div class="logQuery">
    <div class="log-info">
      <el-form :inline="true" :model="formInline" class="demo-form-inline">
        <el-form-item label="统计时间">
          <el-date-picker
            :picker-options="{ shortcuts: G_datePickerShortcuts }"
            v-model.trim="formInline.logTime"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
            class="date-picker"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="日志类型">
          <el-select
            @change="searchClick"
            style="width: 100px"
            placeholder="请选择"
            size="mini"
            clearable
            v-model="formInline.logType"
            class="input"
          >
            <el-option
              v-for="(value, key) in this.logTypes"
              :key="key"
              :label="value"
              :value="key"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="体检号">
          <el-input
            v-model.trim="formInline.regNo"
            size="small"
            placeholder="请输入体检号"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            icon="iconfont icon-search"
            size="small"
            class="blue_btn"
            @click="searchClick"
            >搜索</el-button
          >
        </el-form-item>
      </el-form>
      <div class="log-table">
        <PublicTable
          ref="table"
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          :isSortShow="false"
          :columnWidth="columnWidth"
          :tableLoading="tableLoading"
        >
          <template #logType="{ scope }">
            <div>
              {{ G_EnumList['LogType'][scope.row.logType] }}
            </div>
          </template>
          <template #operateType="{ scope }">
            <div>
              {{ G_EnumList['OperateType'][scope.row.operateType] }}
            </div>
          </template>
          <!-- <template #columnRight>
            <el-table-column type="expand">
              <template slot-scope="scope">
                <p class="content cell_blue">{{ scope.row.message }}</p>
              </template>
              
            </el-table-column>
          </template> -->
        </PublicTable>
      </div>
    </div>
  </div>
</template>

<script>
import PublicTable from '@/components/publicTable';
import { mapGetters } from 'vuex';
import moment from 'moment';
export default {
  components: {
    PublicTable
  },
  data() {
    return {
      formInline: {
        logTime: [new Date(), new Date()],
        regNo: '',
        logType: null
      },
      tableLoading: false,
      theads: {
        operator: '操作员',
        regNo: '体检号',
        combName: '组合名称',
        logType: '日志类型',
        operateType: '操作',
        message: '说明',
        logTime: '操作时间',
        ip: '操作IP'
      },
      columnWidth: {
        operator: 100,
        regNo: 130,
        logTime: 180,
        logType: 80,
        operateType: 50,
        combName: 180
      },
      tableData: [],
      logTypes: []
    };
  },
  created() {
    this.getReadLogBusiness();
    this.loadTypes();
  },
  mounted() {},
  computed: {
    ...mapGetters(['G_userInfo', 'G_datePickerShortcuts', 'G_EnumList'])
  },
  methods: {
    // 获取日志信息
    getReadLogBusiness() {
      console.log(this.logTypes);
      this.tableLoading = true;
      if (this.formInline.logTime === null) {
        this.tableLoading = false;
        return;
      }
      let beginDate = this.formInline.logTime[0];
      let endDate = this.formInline.logTime[1];
      let data = {
        beginDate: moment(beginDate).format('YYYY-MM-DD'),
        endDate: moment(endDate).format('YYYY-MM-DD'),
        regNo: this.formInline.regNo ? this.formInline.regNo : '',
        logType:
          this.formInline.logType == null || this.formInline.logType == ''
            ? -1
            : Number(this.formInline.logType)
      };
      console.log('data: ', data);
      this.$ajax.post(this.$apiUrls.ReadLogBusiness, data).then((r) => {
        console.log('ReadLogBusiness: ', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.tableLoading = false;
        this.tableData = returnData;
      });
    },
    // 搜索日志
    searchClick() {
      this.getReadLogBusiness();
    },
    loadTypes() {
      this.logTypes = this.G_EnumList['LogType'];
      console.log(this.logTypes);
    }
  }
};
</script>

<style lang="less" scoped>
.logQuery {
  width: 100%;
  height: 100%;
  font-weight: 600;
  .log-info {
    height: 100%;
    background: #fff;
    display: flex;
    flex-direction: column;
    border-radius: 4px;
    padding: 18px;
  }
  .log-table {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
  }
  .content {
    padding-left: 238px;
    padding-right: 10px;
    line-height: 26px;
    word-break: break-all;
    overflow: auto;
    white-space: normal;
  }
  /deep/.cell {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
  /deep/.el-form-item__label {
    color: #2d3436;
    font-weight: 600;
  }
}
</style>
