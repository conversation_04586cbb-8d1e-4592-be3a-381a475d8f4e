<template>
  <div class="followUpReg">
    <div class="followUpReg-page">
      <div class="header">
        <p class="header-title">体检随访表：</p>
        <div class="pBtn">
          <div style="margin-right: 20px; margin-top: 3px">
            <el-radio-group
              v-model="peType"
              class="radio-group"
              @input="search"
            >
              <el-radio :label="0">所有</el-radio>
              <el-radio :label="1">个人</el-radio>
              <el-radio :label="2">团体</el-radio>
            </el-radio-group>
          </div>
          <div class="pBtn-time">
            <label style="width: 38px; white-space: nowrap">单位</label>
            <el-cascader
              style="margin-right: 10px"
              ref="company_cascader_ref"
              v-model="companyCode"
              :options="companyList"
              :props="{ multiple: false }"
              clearable
              filterable
              size="small"
              collapse-tags
              @change="companyChange"
              :filter-method="filterMethod"
            >
            </el-cascader>
          </div>
          <div class="pBtn-time">
            <label style="width: 38px; white-space: nowrap">部门</label>
            <el-select
              class="select"
              v-model.trim="companyDeptCode"
              placeholder="请选择"
              size="small"
              filterable
              clearable
              :disabled="isHave"
              @change="search"
            >
              <el-option
                v-for="(item, index) in companyDeptList"
                :key="index"
                :label="item.deptName"
                :value="item.deptCode"
              ></el-option>
            </el-select>
          </div>
          <div class="pBtn-time">
            <label>统计时间</label>
            <el-date-picker
              :picker-options="{ shortcuts: G_datePickerShortcuts }"
              v-model="date"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              class="width"
              :clearable="false"
              style="width: 220px"
              format="yyyy-MM-dd"
              @change="search"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </div>
          <div class="pBtn-time">
            <label style="width: 85px">是否后续随访</label>
            <el-select
              style="width: 60px"
              class="select"
              v-model.trim="needFinallyNotify"
              placeholder="请选择"
              size="small"
              filterable
              clearable
              @change="search"
            >
              <el-option label="是" :value="true"></el-option>
              <el-option label="否" :value="false"></el-option>
            </el-select>
          </div>
          <div class="pBtn-time">
            <label style="width: 85px">随访来源</label>
            <el-select
              class="select"
              v-model.trim="source"
              placeholder="请选择"
              size="small"
              filterable
              clearable
              @change="search"
            >
              <el-option
                v-for="(item, idx) in G_FollowUpSource"
                :key="idx"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>
          <div class="pBtn-time">
            <el-input
              size="small"
              placeholder="体检号/姓名"
              clearable
              v-model="keyWord"
              class="input"
              style="width: 150px"
              @keyup.enter.native="search"
              @clear="search"
            ></el-input>
          </div>
          <ButtonGroup
            :btnList="['查询', '删除', '导出', '短信发送']"
            @deletes="deletes"
            @search="search"
            @exports="exports"
            @SMS_sending="SMS_sending"
          >
            <template #footAdd>
              <el-button
                size="mini"
                class="violet_btn"
                icon="iconfont icon-huifuxitongmoren"
                @click="regainRecord"
                >恢复记录</el-button
              >
            </template>
          </ButtonGroup>
        </div>
      </div>
      <div class="tableDiv" v-loading="tabLoading">
        <PublicTable
          ref="followUpReg_table"
          class="followUpReg_table"
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          :columnWidth="columnWidth"
          @currentChange="rowClick"
          @rowDblclick="edit"
          border
          isCheck
          @selectionChange="selectionChanges"
          @select="handleSelectionChanges"
          :columnSort="['activeTime', 'createTime', 'afterFollowUp']"
        >
          <template #sex="{ scope }">
            <div>
              {{ G_EnumList['Sex'][scope.row.sex] }}
            </div>
          </template>
          <template #needFinallyNotify="{ scope }">
            <div>
              <el-checkbox
                disabled
                v-model="scope.row.needFinallyNotify"
              ></el-checkbox>
            </div>
          </template>
          <template #source="{ scope }">
            <div>
              {{ G_EnumList['FollowUpSource'][scope.row.source] }}
            </div>
          </template>
          <!-- <template #columnRight>
            <el-table-column prop="" label="操作" width="158" fixed="right">
              <template slot-scope="scope">
                <el-button
                  type="primary"
                  plain
                  size="mini"
                  class="view"
                  @click="edit(scope.row)"
                  >编辑</el-button
                >
                <el-button
                  type="primary"
                  plain
                  size="mini"
                  class="view"
                  @click="SMSSending(scope.row)"
                  >短信发送</el-button
                >
              </template>
            </el-table-column>
          </template> -->
        </PublicTable>
      </div>
      <el-drawer
        :title="`${name}随访登记编辑`"
        :visible.sync="detailsShow"
        @close="detailsClose"
        :wrapperClosable="false"
        size="60%"
        v-if="detailsShow"
      >
        <div class="drawer-form">
          <el-form
            :model="popupForm"
            ref="form_ref"
            :rules="rules"
            label-width="130px"
          >
            <div class="leftDiv">
              <el-form-item label="体检号" prop="regNo">
                <el-input
                  v-model.trim="popupForm.regNo"
                  size="small"
                  placeholder="请输入"
                  disabled="disabled"
                ></el-input>
              </el-form-item>
              <el-form-item label="姓名" prop="name">
                <el-input
                  ref="drawer_name"
                  v-model.trim="popupForm.name"
                  size="small"
                  placeholder=""
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item label="性别" prop="sex">
                <el-select
                  v-model.trim="popupForm.sex"
                  ref="drawer_sex"
                  placeholder="请选择"
                  size="small"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="(item, index) in G_sexList"
                    :key="index"
                    :value="item.value"
                    :label="item.label"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="年龄" prop="age">
                <el-input
                  v-model.trim="popupForm.age"
                  size="small"
                  ref="drawer_age"
                  placeholder=""
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item label="电话" prop="tel">
                <el-input
                  v-model.trim="popupForm.tel"
                  ref="drawer_tel"
                  size="small"
                  placeholder=""
                ></el-input>
              </el-form-item>

              <el-form-item label="体检日期" prop="activeTime">
                <el-date-picker
                  v-model.trim="popupForm.activeTime"
                  type="date"
                  placeholder="请选择日期"
                  size="small"
                  class="date"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  disabled="disabled"
                >
                </el-date-picker>
              </el-form-item>

              <el-form-item label="审核日期" prop="auditTime">
                <el-date-picker
                  v-model.trim="popupForm.auditTime"
                  type="date"
                  placeholder="请选择日期"
                  size="small"
                  class="date"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  disabled="disabled"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="个人/单位" prop="peType">
                <el-input
                  v-model.trim="popupForm.peType"
                  size="small"
                  disabled="disabled"
                ></el-input>
              </el-form-item>
              <el-form-item label="单位/地址" prop="address">
                <el-input
                  type="textarea"
                  v-model.trim="popupForm.address"
                  size="small"
                  :autosize="{ minRows: 3 }"
                  placeholder=""
                  clearable
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="记录日期" prop="createTime">
                <el-date-picker
                  v-model.trim="popupForm.createTime"
                  type="date"
                  placeholder="请选择日期"
                  size="small"
                  class="date"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  disabled="disabled"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="是否需要后续随访" prop="needFinallyNotify">
                <el-checkbox
                  v-model="popupForm.needFinallyNotify"
                ></el-checkbox>
              </el-form-item>
              <el-form-item
                label="后续随访情况"
                prop="finallyAfterFollowUp"
                v-if="popupForm.needFinallyNotify"
              >
                <el-input
                  type="textarea"
                  v-model.trim="popupForm.finallyAfterFollowUp"
                  size="small"
                  placeholder=""
                ></el-input>
              </el-form-item>
            </div>
            <div class="rightDiv">
              <el-form-item label="发现问题" prop="findProblems">
                <el-input
                  type="textarea"
                  v-model.trim="popupForm.findProblems"
                  ref="drawer_findProblems"
                  size="small"
                  :rows="3"
                  placeholder=""
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item label="建议" prop="diagnosis">
                <el-input
                  type="textarea"
                  v-model.trim="popupForm.diagnosis"
                  ref="drawer_diagnosis"
                  size="small"
                  :rows="3"
                  placeholder=""
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item label="发现问题科室" prop="findProblemsDept">
                <el-select
                  v-model.trim="popupForm.findProblemsDept"
                  ref="drawer_findProblemsDept"
                  placeholder="请选择"
                  size="small"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="items in departList"
                    :key="items.deptCode"
                    :label="items.deptName"
                    :value="items.deptCode"
                  ></el-option>
                </el-select>
              </el-form-item>

              <!-- <el-form-item label="建议" prop="suggestion">
                <el-input
                  type="textarea"
                  v-model.trim="popupForm.suggestion"
                  size="small"
                  :rows="3"
                  placeholder=""
                  disabled="disabled"
                ></el-input>
              </el-form-item>
              <el-form-item label="主检医生" prop="checkDoctorCode">
                <el-input
                  v-model.trim="popupForm.checkDoctorCode"
                  size="small"
                  disabled="disabled"
                ></el-input>
              </el-form-item> -->
              <el-form-item label="登记人" prop="recorder">
                <el-input
                  v-model.trim="popupForm.recorder"
                  ref="drawer_recorder"
                  size="small"
                ></el-input>
              </el-form-item>
              <el-form-item label="随访人" prop="notifier">
                <el-input
                  v-model.trim="popupForm.notifier"
                  ref="drawer_notifier"
                  size="small"
                  placeholder=""
                ></el-input>
              </el-form-item>
              <!-- <el-form-item label="被通知人" prop="notifiedPerson">
                <el-input
                  v-model.trim="popupForm.notifiedPerson"
                  size="small"
                  placeholder=""
                ></el-input>
              </el-form-item> -->
              <el-form-item label="随访情况" prop="afterFollowUp">
                <div class="textarea_wrap">
                  <el-input
                    type="textarea"
                    v-model.trim="popupForm.afterFollowUp"
                    ref="drawer_afterFollowUp"
                    size="small"
                    :autosize="{ minRows: 3 }"
                    placeholder=""
                    clearable
                  ></el-input>
                  <el-dropdown
                    class="textarea_dropdown"
                    placement="top-start"
                    @command="followUpListChange"
                  >
                    <span class="el-dropdown-link">
                      <i class="el-icon-document text_icon"></i>
                    </span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item
                        v-for="item in followUpList"
                        :key="item"
                        :command="item"
                        >{{ item }}</el-dropdown-item
                      >
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
              </el-form-item>
              <el-form-item label="随访时间" prop="afterFollowUpTime">
                <el-date-picker
                  v-model.trim="popupForm.afterFollowUpTime"
                  type="date"
                  placeholder="请选择日期"
                  size="small"
                  class="date"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="二次随访人" prop="secondaryNotifier">
                <el-input
                  v-model.trim="popupForm.secondaryNotifier"
                  ref="drawer_secondaryNotifier"
                  size="small"
                  placeholder=""
                ></el-input>
              </el-form-item>
              <!-- <el-form-item label="被通知人" prop="notifiedPerson">
                <el-input
                  v-model.trim="popupForm.notifiedPerson"
                  size="small"
                  placeholder=""
                ></el-input>
              </el-form-item> -->
              <el-form-item label="二次随访情况" prop="secondaryAfterFollowUp">
                <div class="textarea_wrap">
                  <el-input
                    type="textarea"
                    v-model.trim="popupForm.secondaryAfterFollowUp"
                    ref="drawer_secondaryAfterFollowUp"
                    size="small"
                    :autosize="{ minRows: 3 }"
                    placeholder=""
                    clearable
                  ></el-input>
                  <el-dropdown
                    class="textarea_dropdown"
                    placement="top-start"
                    @command="twiceVisitListChange"
                  >
                    <span class="el-dropdown-link">
                      <i class="el-icon-document text_icon"></i>
                    </span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item
                        v-for="item in twiceVisitList"
                        :key="item"
                        :command="item"
                        >{{ item }}</el-dropdown-item
                      >
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
              </el-form-item>
              <el-form-item
                label="二次随访时间"
                prop="secondaryAfterFollowUpTime"
              >
                <el-date-picker
                  v-model.trim="popupForm.secondaryAfterFollowUpTime"
                  type="date"
                  placeholder="请选择日期"
                  size="small"
                  class="date"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item
                label="后续随访员"
                prop="finallyNotifier"
                v-if="popupForm.needFinallyNotify"
              >
                <el-input
                  v-model.trim="popupForm.finallyNotifier"
                  size="small"
                  placeholder=""
                ></el-input>
              </el-form-item>
            </div>
          </el-form>
        </div>
        <div class="footer">
          <el-button @click="detailsClose" size="small" class="search-btn"
            >取消</el-button
          >
          <el-button @click="save" size="small" class="blue_btn"
            >保存</el-button
          >
        </div>
      </el-drawer>
      <el-dialog
        title="体检短信发送"
        :before-close="smsCancalClick"
        :visible.sync="dialogVisible"
        width="80%"
        :close-on-click-modal="false"
      >
        <div class="bodyDiv">
          <div class="headerDiv">
            <div class="searchDiv">
              <span class="searchSpan"
                ><el-radio-group
                  v-model="searchInfo.isSent"
                  @change="smssearch"
                >
                  <el-radio :label="null">全部</el-radio>
                  <el-radio :label="false">未发送</el-radio>
                  <el-radio :label="true">已发送</el-radio>
                </el-radio-group></span
              >
              <span class="searchSpan">
                <span class="searchSpan">编辑时间:</span>
                <el-date-picker
                  size="mini"
                  type="daterange"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  v-model.trim="timeRange"
                  @change="smssearch"
                  :clearable="false"
                >
                </el-date-picker
              ></span>
              <span class="searchSpan">
                <span class="searchSpan">体检号/姓名:</span>
                <el-input
                  class="iptSpan"
                  size="mini"
                  placeholder="体检号/姓名"
                  type="text"
                  v-model="searchInfo.keyword"
                  @keyup.enter.native="smssearch"
                  @clear="smssearch"
                  autocomplete="off"
                  clearable
                ></el-input
              ></span>
            </div>
            <div class="searchDiv">
              <el-button
                class="blue_btn btn"
                size="mini"
                icon="iconfont icon-search"
                @click="smssearch"
                >查询</el-button
              >
              <el-button
                class="blue_btn btn"
                size="mini"
                icon="iconfont icon-bianji"
                @click="smseditMore"
                >批量编辑</el-button
              >
              <el-button
                class="blue_btn btn"
                size="mini"
                icon="iconfont icon-baocun"
                @click="smssave"
                >保存</el-button
              >
              <el-button
                class="blue_btn btn"
                size="mini"
                icon="iconfont icon-shenhe1"
                @click="smsaudit"
                :loading="loadingAudit"
                >{{ loadingAudit ? '正在审核中' : '审核' }}</el-button
              >

              <el-button
                class="blue_btn btn"
                size="mini"
                icon="iconfont icon-liebiao"
                @click="smssend"
                :loading="loadingSend"
                >{{ loadingSend ? '正在发送中' : '发送' }}</el-button
              >
            </div>
          </div>

          <div class="tableDiv">
            <PublicTable
              ref="table_ref"
              :viewTableList.sync="smstableData"
              :theads.sync="smstheads"
              isCheck
              @currentChange="smsrowClick"
              @rowDblclick="smsrowDblclick"
              :columnWidth="smscolumnWidth"
              @selectionChange="selectionChange"
              @select="handleSelectionChange"
              :tableRowClassName="rowStyle"
              @selectAll="selectAll"
              :tableLoading="smsTableLoading"
            >
            </PublicTable>
          </div>
        </div>
      </el-dialog>
      <el-dialog
        top="29vh"
        width="476px"
        title="短信编辑"
        :visible.sync="smseditIsshow"
        :close-on-click-modal="false"
        :before-close="smshandleClose"
        class="smsDialog"
        v-if="smseditIsshow"
      >
        <div class="bodydiv">
          <div class="head1">
            <el-form :model="smspopupForm" ref="ruleForm" :rules="smsrules">
              <el-form-item
                label="模板类型"
                :label-width="formLabelWidth"
                prop="shortMsgType"
              >
                <el-select
                  v-model.trim="smspopupForm.shortMsgType"
                  placeholder="请选择模板类型"
                  size="small"
                  style="width: 100%"
                  clearable
                  @change="shortMsgTypeChange"
                >
                  <el-option
                    v-for="item in typeList"
                    :key="item.type"
                    :label="item.typeName"
                    :value="item.type"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                label="模板"
                :label-width="formLabelWidth"
                prop="shortMsgCode"
              >
                <el-select
                  v-model.trim="smspopupForm.shortMsgCode"
                  @change="shortMsgCodeChange"
                  placeholder="请选择模板号"
                  size="small"
                  style="width: 100%"
                  clearable
                >
                  <el-option
                    v-for="item in msmList"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                label="短信内容"
                :label-width="formLabelWidth"
                prop="shortMsgContent"
              >
                <el-input
                  v-model.trim="smspopupForm.shortMsgContent"
                  size="small"
                  type="textarea"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </el-form-item>
            </el-form>
          </div>
        </div>

        <div class="btn" style="text-align: center">
          <el-button size="mini" class="cancel" @click="smshandleClose">
            取消</el-button
          >
          <el-button size="mini" class="sure" @click="smsconfirmLogin">
            确定</el-button
          >
        </div>
      </el-dialog>
    </div>
    <!-- 记录恢复 -->
    <el-drawer
      title="恢复记录"
      :visible.sync="drawer"
      @close="drawerClose"
      @opened="drawerOpen"
      size="90%"
    >
      <FollowRecordRecovery
        ref="ref_followRecordRecovery"
        :companyList="companyList"
      />
    </el-drawer>
  </div>
</template>
<script>
import PublicTable from '@/components/publicTable.vue';
import { mapGetters } from 'vuex';
import ButtonGroup from './components/buttonGroup.vue';
import { dataUtils } from '@/common';
import moment from 'moment';
import FollowRecordRecovery from './components/followRecordRecovery.vue';

export default {
  name: 'followUpReg',
  components: {
    PublicTable,
    ButtonGroup,
    FollowRecordRecovery
  },
  data() {
    return {
      drawer: false,
      tabLoading: false,
      peType: 0,
      companyCode: '',
      companyDeptCode: '',
      companyDeptList: '',
      isHave: true,
      companyList: [],
      departList: [],
      keyWord: '', //220924000001
      tableData: [],
      theads: {
        regNo: '体检号',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        tel: '电话',
        activeTime: '体检日期',
        source: '随访来源',
        peType: '个人/单位',
        // address: "单位/地址",
        createTime: '记录日期',
        findProblems: '发现问题',
        diagnosis: '建议',
        recorder: '登记人',
        notifier: '随访人',
        afterFollowUp: '随访情况',
        afterFollowUpTime: '随访时间',
        secondaryNotifier: '二次随访人',
        secondaryAfterFollowUp: '二次随访情况',
        secondaryAfterFollowUpTime: '二次随访时间',
        needFinallyNotify: '是否需要二次通知',
        finallyNotifier: '后续随访员',
        finallyAfterFollowUp: '后续随访情况'
        // findProblemsDept: "发现问题的科室",
        // suggestion: "建议",
        // checkDoctorCode: "主检医生",
        // notifier: "通知人",
        // notifiedPerson: "被通知人"
      },
      columnWidth: {
        regNo: 92,
        age: 40,
        sex: 40,
        activeTime: 125,
        auditTime: 125,
        createTime: 125,
        source: 80,
        tel: 85,
        address: 160,
        findProblems: 200,
        diagnosis: 160,
        peType: 80,
        notifier: 90,
        secondaryNotifier: 90,
        secondaryAfterFollowUp: 160,
        needFinallyNotify: 130,
        finallyNotifier: 110,
        finallyAfterFollowUp: 130,
        recorder: 90,
        afterFollowUp: 160,
        afterFollowUpTime: 160,
        secondaryAfterFollowUpTime: 160
      },
      name: '',
      followUpId: '',
      detailsShow: false,
      rules: {
        recorder: [
          { required: true, message: '请输入登记人', trigger: 'blur' }
        ],
        findProblems: [
          { required: true, message: '请输入发现问题', trigger: 'blur' }
        ]
        // notifier: [
        //   { required: true, message: "请输入通知人", trigger: "blur" },
        // ],
        // afterFollowUp: [
        //   { required: true, message: "请输入后续随访情况", trigger: "blur" },
        // ],
      },
      popupForm: {
        regNo: '',
        name: '',
        sex: null,
        age: null,
        tel: '',
        activeTime: '',
        auditTime: '',
        peType: '',
        address: '',
        createTime: '',
        diagnosis: '',
        findProblems: '',
        findProblemsDept: '',
        suggestion: '',
        checkDoctorCode: '',
        notifier: '',
        notifiedPerson: '',
        afterFollowUp: '',
        recorder: '',
        secondaryNotifier: '',
        secondaryAfterFollowUp: '',
        needFinallyNotify: false,
        finallyNotifier: '',
        finallyAfterFollowUp: ''
      },
      addList: {
        regNo: '',
        itemName: '',
        itemResult: '',
        deptCode: ''
      },
      date: [],
      dialogVisible: false,
      smstheads: {
        patientName: '姓名',
        sexName: '性别',
        phoneNumber: '电话',
        regNo: '体检号',
        approvedName: '审核人',
        approvedTime: '审核时间',
        isSentName: '标志',
        sentTimes: '次数',
        shortMsgContent: '短信内容'
      },
      smstableData: [],
      smscolumnWidth: {
        patientName: 90,
        sexName: 65,
        phoneNumber: 120,
        regNo: 120,
        approvedName: 100,
        approvedTime: 180,
        isSentName: 65,
        sentTimes: 65,
        shortMsgContent: 400
      },
      timeRange: [dataUtils.getDate(), dataUtils.getDate()],
      searchInfo: {
        pageSize: 0,
        pageNumber: 0,
        keyword: '',
        isSent: null,
        startCreatedTime: '',
        endCreatedTime: ''
      },
      needFinallyNotify: '',
      source: '',
      formLabelWidth: '120px',
      smspopupForm: {
        regNo: '',
        shortMsgCode: '',
        shortMsgType: '',
        shortMsgContent: ''
      },
      smsrules: {
        shortMsgCode: [
          { required: true, message: '请选择模板号', trigger: 'blur' },
          { required: true, message: '请选择模板号', trigger: 'change' }
        ],
        shortMsgType: [
          { required: true, message: '请选择模板类型', trigger: 'blur' },
          { required: true, message: '请选择模板类型', trigger: 'change' }
        ],
        shortMsgContent: [
          { required: true, message: '请输入短信内容', trigger: 'blur' }
        ]
      },
      smsRow: {},
      smseditIsshow: false,
      typeList: [],
      msmList: [],
      smsselList: [],
      loadingAudit: false,
      loadingSend: false,
      msmListCopy: [],
      index: 0,
      checkRow: {},
      selList: [],
      editSelList: [],
      isMore: true,
      firstActivate: true, //首次激活时
      dbClickCheckRow: {},
      smsTableLoading: true,
      followUpList: ['已通知', '短信通知', '空号', '备注'], //后续随访情况模板下拉
      twiceVisitList: ['在外院专科就诊', '在本院专科就诊', '失访', '未就诊'] //二次随访情况模板下拉
    };
  },
  created() {
    this.getDepartment();
    this.getCompanyList();
    this.GetShortMsgTemplateTypes();
    this.GetShortMsgTemplates();
  },
  mounted() {
    this.date = [
      moment().startOf('day').format('YYYY-MM-DD'),
      // moment().add(-7, "d").format("YYYY-MM-DD"),
      moment().startOf('day').format('YYYY-MM-DD')
    ];
    let regNo = this.$route.query.regNo;
    console.log('mounted');
    if (regNo) {
      this.keyWord = regNo;
    }
    this.search();
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_userInfo',
      'G_sexList',
      'G_datePickerShortcuts',
      'G_FollowUpSource'
    ])
  },
  activated() {
    if (this.firstActivate) {
      this.firstActivate = false;
      return;
    }
    let regNo = this.$route.query.regNo;
    console.log('activated');
    if (regNo) {
      this.keyWord = regNo;
    }
    this.search();
  },
  methods: {
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    // 获取科室列表
    getDepartment() {
      this.$ajax.post(this.$apiUrls.Department).then((r) => {
        let { returnData, success } = r.data;
        if (!success) {
          return;
        }
        this.departList = returnData || [];
      });
    },
    search() {
      let data = {
        startTime: this.date[0],
        endTime: this.date[1],
        keyWord: this.keyWord,
        companyCode: this.companyCode?.[0] || '',
        companyDeptCode: this.companyDeptCode,
        peType: this.peType,
        needFinallyNotify:
          this.needFinallyNotify == '' ? null : this.needFinallyNotify,
        source: this.source === '' ? -1 : this.source
      };
      //console.log("data: ", data);
      this.tabLoading = true;
      this.checkRow = {};
      this.$ajax
        .post(this.$apiUrls.PeFollowUpQuery, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.tableData = returnData || [];
          if (this.tableData.length < 1) {
            this.$message({
              message: '暂无数据!',
              type: 'success',
              showClose: true
            });
          }
        })
        .finally(() => {
          this.tabLoading = false;
        });
    },
    rowClick(row) {
      if (!row) return;
      this.name = row.name;
      this.followUpId = row.followUpId;
      this.checkRow = dataUtils.deepCopy(row);
      this.addList = {
        regNo: row.regNo,
        itemName: row.itemName,
        itemResult: row.itemResult,
        deptCode: this.G_userInfo.codeOper.deptCode
      };
    },
    //保存
    saves() {
      this.$ajax
        .post(this.$apiUrls.InsertPeFollowUp, this.addList)
        .then((r) => {
          //
          let { success } = r.data;
          if (!success) return;
          this.$message({
            message: '保存成功!',
            type: 'success',
            showClose: true
          });
          this.$nextTick(() => {
            this.search();
          });
        });
    },
    //删除
    deletes() {
      //console.log("[ this.followUpId ]-422", this.followUpId);
      if (!this.followUpId) {
        this.$message({
          message: '请先选中再删除!',
          type: 'warning',
          showClose: true
        });
        return;
      }

      this.$confirm(`是否确定删除?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax
            .post(this.$apiUrls.DeletePeFollowUp, '', {
              query: {
                followUpId: this.followUpId
              }
            })
            .then((r) => {
              //
              let { success } = r.data;
              if (!success) return;
              this.$message({
                message: '删除成功',
                type: 'success'
              });
              this.$nextTick(() => {
                this.search();
              });
            });
        })
        .catch(() => {});
    },
    edit(row, column) {
      // console.log("[ this.selectRow ]-341", this.selectRow);
      console.log(column);
      this.dbClickCheckRow = row;
      this.popupForm = dataUtils.deepCopy(row);
      this.detailsShow = true;
      this.$nextTick(() => {
        if (!this.$refs[`drawer_${column.property}`]) return;
        this.$refs[`drawer_${column.property}`].focus();
      });
    },
    detailsClose() {
      this.detailsShow = false;
    },
    //体检随访表勾选
    selectionChanges(val) {
      this.selList = [];
      val.forEach((item) => {
        this.selList.push(item.regNo);
      });
    },
    handleSelectionChanges(selection, row) {
      this.$refs.followUpReg_table.$refs.tableCom_Ref.setCurrentRow(row);
    },
    //编辑保存
    save() {
      this.$refs.form_ref.validate((valid) => {
        if (valid) {
          this.$ajax
            .post(this.$apiUrls.SavePeFollowUp, this.popupForm)
            .then((r) => {
              //
              let { success } = r.data;
              if (!success) return;
              this.$message({
                message: '保存成功!',
                type: 'success',
                showClose: true
              });
              this.detailsShow = false;
              this.$nextTick(() => {
                Object.keys(this.popupForm).map((item) => {
                  this.dbClickCheckRow[item] = this.popupForm[item];
                });
              });
            });
        } else {
          return false;
        }
      });
    },
    //打开短信编辑页面
    SMS_sending() {
      this.dialogVisible = true;
      if (this.selList.length > 0) {
        this.BatchNewPeSendShortMsgRecord(this.selList);
      } else if (this.checkRow?.regNo && this.selList.length < 1) {
        this.BatchNewPeSendShortMsgRecord([this.checkRow.regNo]);
      } else {
        this.timeRange = [dataUtils.getDate(), dataUtils.getDate()];
        this.searchInfo.keyword = '';
        this.smssearch();
      }
    },
    //打开短信编辑页面并新建新建短信模板
    // SMSSending(row) {
    //   this.dialogVisible = true;
    //   this.timeRange = [dataUtils.getDate(), dataUtils.getDate()];
    //   this.searchInfo.keyword = "";
    //   this.BatchNewPeSendShortMsgRecord(row.regNo);
    //   this.selList=[row.regNo]
    // },
    //短信模板
    BatchNewPeSendShortMsgRecord(data) {
      this.smsTableLoading = true;
      this.$ajax
        .post(this.$apiUrls.BatchNewPeSendShortMsgRecord, data)
        .then((r) => {
          let { returnData, success, returnMsg } = r.data;
          if (!success) {
            return;
          }
          this.smstableData = returnData || [];
          //默认全选
          this.$nextTick(() => {
            if (this.smstableData.length > 0) {
              this.smstableData.forEach((item) => {
                this.$refs.table_ref.$refs.tableCom_Ref.toggleRowSelection(
                  item
                );
              });
              this.$refs.table_ref.$refs.tableCom_Ref.setCurrentRow(
                this.smstableData[0]
              );
            }
          });
          //console.log("[ this.smstableData ]-726", this.smstableData);
          // this.$message({
          //   message: returnMsg,
          //   type: "success",
          //   showClose: true,
          // });
          // return;
        })
        .finally((_) => {
          this.smsTableLoading = false;
        });
    },
    //点击行
    smsrowClick(row) {
      //this.smsRow = row;
    },
    //双击编辑短信
    smsrowDblclick(row) {
      this.isMore = false;
      this.smsRow = row;
      this.smsedit(true);
    },
    //勾选
    selectionChange(val) {
      //console.log("[ val ]-933", val);
      this.editSelList = val;
    },
    //手动勾选数据行的,默认加点击行事件
    handleSelectionChange(selection, row) {
      this.$refs.table_ref.$refs.tableCom_Ref.setCurrentRow(row);
    },
    //全选判定只有一条数据时默认点击操作
    selectAll(selection) {
      //console.log("[ selection ]-948", selection);
      if (selection.length < 1) {
        return;
      }
      this.editSelList = selection;
      this.$refs.table_ref.$refs.tableCom_Ref.setCurrentRow(selection[0]);

      this.$nextTick(() => {
        this.$refs.table_ref.$refs.tableCom_Ref.bodyWrapper.scrollTop =
          this.$refs.table_ref.$refs.tableCom_Ref.bodyWrapper.scrollHeight;
      });
    },
    //短信编辑页查询
    smssearch() {
      this.searchInfo.startCreatedTime = dataUtils.hourMinSeStart(
        this.timeRange[0]
      );
      this.searchInfo.endCreatedTime = dataUtils.hourMinSeEnd(
        this.timeRange[1]
      );
      this.smstableData = [];
      //console.log("[ this.searchInfo ]-850", this.searchInfo);
      this.smsTableLoading = true;
      this.$ajax
        .post(this.$apiUrls.GetPeSendShortMsgRecords, this.searchInfo)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.smstableData = returnData || [];
          if (this.smstableData.length < 1) {
            this.$message({
              message: '暂无数据!',
              type: 'success',
              showClose: true
            });
          }
        })
        .finally((_) => {
          this.smsTableLoading = false;
        });
    },
    //多条短信编辑
    smseditMore() {
      this.isMore = true;
      this.smsedit(false);
    },
    //编辑短信
    smsedit(isDbclick = false) {
      if (!this.smsRow?.regNo && this.editSelList.length < 1) {
        this.$message({
          message: '请先选中至少一行数据再编辑',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.index = this.smsRow.index;
      //console.log("[ this.editSelList ]-992", this.editSelList);
      let isHave = false;
      if (isDbclick) {
        if (this.smsRow?.status >= 20) {
          isHave = true;
        }
      } else {
        this.editSelList.forEach((item) => {
          if (item.status >= 20) {
            isHave = true;
            //console.log("[ item.status ]-983", item.status);
          }
        });
      }

      if (isHave) {
        this.$message({
          message: '存在记录已审核或发送不能再编辑',
          type: 'warning',
          showClose: true
        });
        return;
      }
      const { regNo, shortMsgCode, shortMsgType, shortMsgContent } = this.isMore
        ? this.editSelList.length === 1
          ? this.editSelList[0]
          : {}
        : this.smsRow;
      this.smspopupForm = {
        regNo: regNo,
        shortMsgCode: shortMsgCode,
        shortMsgType: shortMsgType || this.typeList[0].type,
        shortMsgContent: shortMsgContent
      };
      if (!shortMsgContent) {
        this.shortMsgTypeChange(true, shortMsgCode);
      } else {
        this.shortMsgTypeChange(false, shortMsgCode);
      }

      this.smseditIsshow = true;
    },
    //获取短信记录信息
    GetPeSendShortMsgRecord() {
      this.$ajax
        .post(this.$apiUrls.GetPeSendShortMsgRecord, '', {
          query: {
            id: this.smsRow.id,
            regNo: this.smsRow.regNo
          }
        })
        .then((r) => {
          let { returnData, success, returnMsg } = r.data;
          if (!success) {
            return;
          }
          const { regNo, shortMsgCode, shortMsgType, shortMsgContent } =
            returnData;
          this.smspopupForm = {
            regNo: regNo,
            shortMsgCode: shortMsgCode,
            shortMsgType: shortMsgType,
            shortMsgContent: shortMsgContent
          };
        });
    },
    //关闭
    smsCancalClick() {
      this.dialogVisible = false;
      this.searchInfo.keyword = '';
      this.loadingAudit = false;
      this.loadingSend = false;
      this.smstableData = [];
      this.smsRow = {};
      this.editSelList = [];
      this.isMore = true;
    },
    //确定编辑
    smsconfirmLogin() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          //console.log("[  ]-963", 4444);
          //把编辑的信息赋值给编辑行
          const { regNo, shortMsgCode, shortMsgType, shortMsgContent } =
            this.smspopupForm;
          if (!this.isMore) {
            const item = this.smstableData.find(
              (item) => item.index === this.index
            );
            if (item) {
              item.shortMsgCode = shortMsgCode;
              item.shortMsgType = shortMsgType;
              item.shortMsgContent = this.replaceShortMsgContent(
                shortMsgContent,
                item
              );
              this.smsRow.shortMsgCode = shortMsgCode;
              this.smsRow.shortMsgType = shortMsgType;
              this.smsRow.shortMsgContent = item.shortMsgContent;
            }
          } else {
            const updatedData = this.smstableData.map((item) => {
              const smssel = this.editSelList.find(
                (s) => s.index === item.index
              );
              if (smssel) {
                //console.log("[ smssel ]-1045", smssel);
                item.shortMsgCode = shortMsgCode;
                item.shortMsgType = shortMsgType;
                item.shortMsgContent = item.shortMsgContent =
                  this.replaceShortMsgContent(shortMsgContent, item);
                smssel.shortMsgCode = shortMsgCode;
                smssel.shortMsgType = shortMsgType;
                smssel.shortMsgContent = item.shortMsgContent;
                this.$refs.table_ref.$refs.tableCom_Ref.setCurrentRow(item);
              }
              return item;
            });
            // 更新this.smstableData数组
            this.smstableData = updatedData;
            this.$nextTick(() => {
              this.smstableData.forEach((item) => {
                const matchingItem = this.editSelList.find(
                  (items) => items.index === item.index
                );
                if (matchingItem) {
                  this.$refs.table_ref.$refs.tableCom_Ref.toggleRowSelection(
                    item
                  );
                  this.$refs.table_ref.$refs.tableCom_Ref.setCurrentRow(item);
                }
              });
            });
          }

          this.smseditIsshow = false;
          this.smsRow = {};
        } else {
          return false;
        }
      });
    },
    //关闭编辑
    smshandleClose() {
      this.smseditIsshow = false;
      this.smsRow = {};
    },
    //保存编辑
    smssave() {
      //console.log("[ this.editSelList ]-1133", this.editSelList);
      //console.log("[  this.smsRow]-914", this.smsRow);
      // if (!this.smsRow.shortMsgContent) {
      //   this.$message({
      //     message: "请先去编辑该条数据短信内容再保存!",
      //     type: "warning",
      //     showClose: true,
      //   });
      //   return;
      // }
      let data = this.editSelList.length > 0 ? this.editSelList : [this.smsRow];

      if (!data) {
        this.$message({
          message: '没有可保存的数据',
          type: 'warning',
          showClose: true
        });
        return;
      }
      for (let index = 0; index < data.length; index++) {
        const item = data[index];
        if (!item.shortMsgContent) {
          //console.log("[ item.status ]-983", item.status);
          this.$message({
            message: '存在未编辑记录不能保存',
            type: 'warning',
            showClose: true
          });
          return;
        }
      }

      this.$ajax
        .post(this.$apiUrls.BatchSavePeSendShortMsgRecord, data)
        .then((r) => {
          //
          let { success, returnMsg, returnData } = r.data;
          if (!success) {
            return;
          }

          //console.log("[  this.editSelList]-1159", this.editSelList);
          if (this.editSelList.length > 0) {
            returnData.map((items, idx) => {
              this.editSelList[idx].id = items.id;
            });

            //console.log('[ this.editSelList  ]-1166',this.editSelList  );
            this.smstableData.forEach((item, i) => {
              const index = this.editSelList.findIndex(
                (editItem) => editItem.index === item.index
              );
              if (index !== -1) {
                this.$set(this.smstableData, i, this.editSelList[index]);
              }
            });
          }
          if (this.smsRow?.regNo && this.editSelList.length < 1) {
            this.smsRow = returnData[0];
            this.$set(this.smstableData, 0, returnData[0]);
          }
          //console.log("[this.smstableData  ]-1175", this.smstableData);
          this.$nextTick(() => {
            this.smstableData.forEach((item) => {
              const matchingItem = this.editSelList.find(
                (items) => items.index === item.index
              );
              if (matchingItem) {
                this.$refs.table_ref.$refs.tableCom_Ref.toggleRowSelection(
                  item
                );
                this.$refs.table_ref.$refs.tableCom_Ref.setCurrentRow(item);
              }
            });
          });
          this.$message({
            message: returnMsg,
            type: 'success',
            showClose: true
          });
          return;
        });
    },
    //批量审核短信记录
    smsaudit() {
      if (this.editSelList.length === 0) {
        this.$message({
          message: '请先勾选数据再审核',
          type: 'warning',
          showClose: true
        });
        return;
      }
      const smsselList = [];
      this.$confirm(`是否确定审核选中的记录?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          if (this.editSelList.some((item) => item.id == 0)) {
            this.$message({
              message: '存在未保存的数据,请先保存后审核!',
              type: 'warning',
              showClose: true
            });
            return;
          }
          this.editSelList.forEach((item) => {
            smsselList.push({
              id: item.id,
              regNo: item.regNo
            });
          });
          this.loadingAudit = true;
          this.$ajax
            .post(this.$apiUrls.BatchAuditPeSendShortMsgRecord, smsselList)
            .then((r) => {
              this.loadingAudit = false;
              //
              let { success, returnData, returnMsg } = r.data;
              if (!success) {
                return;
              }
              // this.smssearch();
              //回显审核后的数据
              for (let i = 0; i < this.smstableData.length; i++) {
                const index = returnData.findIndex(
                  (item) => item.id === this.smstableData[i].id
                );
                if (index != -1) {
                  //this.smstableData[i] = (returnData[index]);
                  this.$set(this.smstableData, i, returnData[index]);
                }
              }
              for (let i = 0; i < this.editSelList.length; i++) {
                const index = returnData.findIndex(
                  (item) => item.id === this.editSelList[i].id
                );
                if (index != -1) {
                  this.editSelList[i] = returnData[index];
                }
              }
              this.$nextTick(() => {
                this.smstableData.forEach((item) => {
                  const matchingItem = this.editSelList.find(
                    (items) => items.index === item.index
                  );
                  if (matchingItem) {
                    this.$refs.table_ref.$refs.tableCom_Ref.toggleRowSelection(
                      item
                    );
                    this.$refs.table_ref.$refs.tableCom_Ref.setCurrentRow(item);
                  }
                });
              });
              this.$message({
                message: returnMsg,
                type: 'success',
                showClose: true
              });
              return;
            });
        })
        .catch(() => {
          this.loadingAudit = false;
        });
    },
    //批量根据短信内容发送短信
    smssend() {
      if (this.editSelList.length === 0) {
        this.$message({
          message: '请先勾选数据再发送短信',
          type: 'warning',
          showClose: true
        });
        return;
      }
      const smsselList = [];
      this.$confirm(`是否确定发送选中的记录?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          if (this.editSelList.some((item) => item.id == 0)) {
            this.$message({
              message: '存在未保存的数据,请先保存再进行后续操作!',
              type: 'warning',
              showClose: true
            });
            return;
          }
          this.editSelList.forEach((item) => {
            smsselList.push({
              id: item.id,
              regNo: item.regNo
            });
          });
          this.loadingSend = true;
          this.$ajax
            .post(this.$apiUrls.BatchSendShortMsgByPeShortMsgRecord, smsselList)
            .then((r) => {
              this.loadingSend = false;
              //
              let { success, returnData, returnMsg } = r.data;
              if (!success) {
                return;
              }
              //this.smssearch();
              //回显发送短信后的数据
              for (let i = 0; i < this.smstableData.length; i++) {
                const index = returnData.findIndex(
                  (item) => item.id === this.smstableData[i].id
                );
                if (index !== -1) {
                  this.$set(this.smstableData, i, returnData[index]);
                }
              }

              //console.log("[ this.smstableData ]-1293", this.smstableData);
              for (let i = 0; i < this.editSelList.length; i++) {
                const index = returnData.findIndex(
                  (item) => item.id === this.editSelList[i].id
                );
                if (index !== -1) {
                  this.$set(this.editSelList, i, returnData[index]);
                }
              }
              this.$message({
                message: returnMsg,
                type: 'success',
                showClose: true
              });
              return;
            });
        })
        .catch(() => {
          this.loadingSend = false;
        });
    },
    // 获取短信模板类型
    GetShortMsgTemplateTypes() {
      this.$ajax.post(this.$apiUrls.GetShortMsgTemplateTypes).then((r) => {
        //
        let { success, returnData } = r.data;
        if (!success) {
          return;
        }
        this.typeList = returnData || [];
      });
    },
    //获取模板号
    GetShortMsgTemplates() {
      let data = {
        pageSize: 0,
        pageNumber: 0,
        keyword: ''
      };
      this.$ajax.post(this.$apiUrls.GetShortMsgTemplates, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.msmList = returnData || [];
        this.msmListCopy = returnData || [];
      });
    },
    // 短信模板类型改变
    shortMsgTypeChange(flag = true, shortMsgCode) {
      let newListData = [];

      this.msmListCopy.filter((item) => {
        if (item.type === this.smspopupForm.shortMsgType) {
          newListData.push(item);
        }
      });
      this.msmList = newListData;
      if (!shortMsgCode) {
        this.smspopupForm.shortMsgCode = this.msmList[0].code;
      }

      if (flag) this.shortMsgCodeChange();
    },

    shortMsgCodeChange() {
      this.msmList.filter((item) => {
        if (item.code === this.smspopupForm.shortMsgCode) {
          this.smspopupForm.shortMsgContent = this.isMore
            ? this.editSelList.length === 1
              ? this.replaceShortMsgContent(item.content, this.editSelList[0])
              : item.content
            : this.replaceShortMsgContent(item.content, this.smsRow);
        }
      });
    },
    exports() {
      if (this.tableData.length == 0) {
        return this.$message({
          message: '请选择至少一条数据进行操作！',
          type: 'warning',
          showClose: true
        });
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          let tableData = dataUtils.deepCopy(this.tableData);
          tableData.map((item, i) => {
            item.index = i + 1;
            item.sex = this.G_EnumList['Sex'][item.sex];
          });
          this.$nextTick(function () {
            this.export2Excel(tableData);
          });
        })
        .catch(() => {});
    },
    rowStyle({ row, rowIndex }) {
      row.index = rowIndex;
    },
    //没有保存的数据不能勾选
    // selectable(row, index) {
    // if (row.id != 0) {
    //   return true;
    // }
    //},
    // 数据写入excel
    export2Excel(tableData) {
      var that = this;
      require.ensure([], () => {
        const { export_json_to_excel } = require('@/utils/Export2Excel'); // 这里必须使用绝对路径，使用@/+存放export2Excel的路径
        const tHeader = Object.values(this.theads); // 导出的表头名信息
        const filterVal = Object.keys(this.theads); // 导出的表头字段名
        const list = tableData;
        const data = that.formatJson(filterVal, list);
        //console.log(data);
        const name = '体检随访表' + dataUtils.getNowDateTiemNo();
        export_json_to_excel(tHeader, data, name);
      });
    },
    // 格式转换
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) => filterVal.map((j) => v[j]));
    },
    // 获取单位下拉
    getCompanyList() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) {
        this.isHave = true;
        this.companyDeptCode = '';
        this.date = [dataUtils.getDate(), dataUtils.getDate()];
        return;
      }
      this.getDepartList(data);
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.date = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.search();
    },
    //获取部门数据
    getDepartList(companyCode) {
      this.companyDeptCode = '';
      this.isHave = true;
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanyDepartment, {
          companyCode: companyCode?.[0] || '',
          deptCode: '',
          deptName: ''
        })
        .then((r) => {
          this.isHave = false;
          //console.log("r", r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.companyDeptList = returnData || [];
        });
    },
    /**
     * @author: justin
     * @description: 替换短信模板内容
     * @param {*} content
     * @param {*} item
     * @return {*}
     */
    replaceShortMsgContent(content, item) {
      if (!content || !item) return '';

      let nameRespect = `${item.patientName}${dataUtils.getSexNameRespect(
        item.sex
      )}`;

      return content.replace(/\{1\}/g, nameRespect);
    },
    //恢复记录按钮回调
    regainRecord() {
      this.drawer = true;
    },
    //恢复记录抽屉关闭回调
    drawerClose() {
      this.$refs.ref_followRecordRecovery.clearForm();
      this.search();
    },
    //恢复记录抽屉打开回调
    drawerOpen() {
      this.$refs.ref_followRecordRecovery.search();
    },
    // 后续随访情况选模板的回调
    followUpListChange(command) {
      this.popupForm.afterFollowUp = command;
    },
    twiceVisitListChange(command) {
      this.popupForm.secondaryAfterFollowUp = command;
    }
  }
};
</script>
<style lang="less" scoped>
label {
  white-space: nowrap;
}
.followUpReg {
  .radio-group {
    & > * {
      margin-right: 5px;
    }
    min-width: 172px;
  }
  .followUpReg-page {
    width: 100%;
    height: 100%;
    display: flex;
    font-size: 14px;
    color: #2d3436;
    flex-direction: column;
    background: #fff;
    padding: 10px;
    border-radius: 4px;
  }
  .header {
    display: flex;
    overflow-x: auto;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0 10px 0;
    .header-title {
      font-size: 18px;
      font-weight: 600;
      white-space: nowrap;
    }
    .pBtn {
      display: flex;
      align-items: center;
    }
    .el-input {
      margin-right: 10px;
    }
  }
  .pBtn-time {
    display: flex;
    align-items: center;
    font-weight: 600;
    margin-right: 10px;
    label {
      width: 64px;
    }
  }
  .tableDiv {
    flex: 1;
    overflow: auto;
    border: 1px solid #d9dfe2;
    border-radius: 4px;
  }
  .followUpReg_table {
    /deep/ .table_dom {
      font-size: 12px !important;
      .cell {
        padding-left: 5px;
        padding-right: 5px;
      }
    }
  }
  /deep/.el-drawer__body {
    display: flex;
    flex-direction: column;
  }
  .footer {
    height: 80px;
    text-align: right;
    margin-right: 50px;
  }
  /deep/.el-drawer__header {
    line-height: 40px;
    align-items: center;
    display: flex;
    margin-bottom: 0px;
    padding: 5px 20px;
    background: rgba(23, 112, 223, 0.2);
    border-radius: 4px 4px 0 0;
    color: #000;
    font-size: 16px;
  }
  .el-form-item {
    margin-bottom: 20px;
  }
  .drawer-form {
    flex: 1;
    overflow: auto;
    display: flex;
    width: 100%;
    padding-top: 20px;
    flex-direction: column;
    /deep/.el-date-editor.el-input,
    .el-date-editor.el-input__inner {
      width: 100%;
    }
    .el-form {
      display: flex;
      flex-direction: row;
      padding-right: 20px;
    }
    .el-select {
      width: 100%;
    }
    .leftDiv {
      width: 50%;
    }
    .rightDiv {
      flex: 1;
    }
    .textarea_wrap {
      position: relative;
      .textarea_dropdown {
        position: absolute;
        bottom: 0;
        right: 0;
        line-height: normal;
      }
      .text_icon {
        font-size: 16px;
      }
    }
  }
  /deep/.el-dialog {
    margin-top: 50px !important;
    height: calc(100% - 100px);
  }

  /deep/.el-dialog__body {
    height: calc(100% - 50px);
    padding: 5px 10px;
  }

  .login-dialog-content {
    .content-title {
      font-size: 16px;
      color: #000000;
      font-weight: 600;
      margin-bottom: 13px;
    }
  }

  /deep/.el-dialog__header {
    background-color: #079c66 !important;
    height: 40px;
    line-height: 40px;
    padding: 0;

    .el-dialog__title {
      display: inline-block;
      padding: 0 16px;
      font-size: 16px;
      color: #ffffff;
      letter-spacing: -0.02px;
    }

    .el-dialog__headerbtn .el-dialog__close {
      color: #ffffff;
    }

    .el-dialog__headerbtn {
      position: absolute;
      top: 12px;
      right: 16px;
      padding: 0;
      background: 0 0;
      border: none;
      outline: 0;
      cursor: pointer;
      font-size: 18px;
    }
  }
  .bodyDiv {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .headerDiv {
    height: 80px;
  }
  .searchDiv {
    height: 40px;
    line-height: 40px;
    .searchSpan {
      margin-right: 20px;
    }
    .iptSpan {
      width: 140px;
    }
    /deep/.el-radio__inner {
      width: 20px;
      height: 20px;
    }
    /deep/.el-date-editor--daterange.el-input,
    /deep/.el-date-editor--daterange.el-input__inner,
    /deep/.el-date-editor--timerange.el-input,
    /deep/.el-date-editor--timerange.el-input__inner {
      width: 200px !important;
    }
  }
  .smsDialog {
    /deep/.el-dialog {
      height: 500px;
    }
    /deep/.el-dialog__body {
      display: flex;
      flex-direction: column;
    }

    .bodydiv {
      flex: 1;
      overflow: auto;
      /deep/.el-textarea__inner {
        min-height: 200px !important;
      }
    }
  }
}
</style>
