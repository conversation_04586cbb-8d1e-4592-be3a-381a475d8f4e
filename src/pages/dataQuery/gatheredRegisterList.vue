<template>
  <div class="gathered-register-list-container">
    <el-row :gutter="20" type="flex" align="center" class="header-wrapper">
      <el-col :span="24">
        <el-form
          ref="searchForm_ref"
          :model="searchForm"
          :rules="rules"
          :inline="true"
          class="search-form-box"
        >
          <el-form-item label="采集日期" prop="gatherDate">
            <el-date-picker
              v-model="searchForm.gatherDate"
              type="date"
              placeholder="请选择采集日期"
              size="small"
              @change="search"
            >
            </el-date-picker>
          </el-form-item>

          <el-form-item class="btn-box">
            <btn-group
              :btnList="['查询', '导出']"
              @search="search"
              @exports="export2Excel"
            >
            </btn-group>
          </el-form-item>
        </el-form>

        <div class="summary-box">
          <el-tag size="medium"> 总数：{{ totalNumber }} </el-tag>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="20" type="flex" align="center" class="body-wrapper">
      <el-col :span="24">
        <PublicTable
          ref="publicTable_Ref"
          :theads="theads"
          :url="$apiUrls.GetGatheredRegisterList"
          :params="searchForm"
          rowKey="regNo"
          remoteByPage
          :default-sort="{
            prop: 'regNo',
            order: 'ascending'
          }"
          @request-success="
            (data) => {
              this.totalNumber = data.totalNumber;
            }
          "
        >
        </PublicTable>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import PublicTable from '@/components/publicTable2.vue';
import BtnGroup from './components/buttonGroup.vue';
import { dataUtils } from '@/common';

export default {
  name: 'gatheredRegisterList',
  components: {
    PublicTable,
    BtnGroup
  },
  data() {
    return {
      searchForm: {
        gatherDate: new Date()
      },
      rules: {
        gatherDate: [
          {
            type: 'date',
            required: true,
            message: '请选择采集日期',
            trigger: 'change'
          }
        ]
      },
      theads: [
        {
          prop: 'name',
          label: '姓名',
          align: '',
          width: '150px',
          sortable: true
        },
        {
          prop: 'regNo',
          label: '体检号',
          align: '',
          width: '150px',
          sortable: true
        },
        {
          prop: 'age',
          label: '年龄',
          align: '',
          width: '100px',
          sortable: true
        },
        {
          prop: 'companyName',
          label: '单位',
          align: '',
          width: '',
          sortable: true,
          showOverflowTooltip: true
        },
        {
          prop: 'gatherTime',
          label: '首采集时间',
          align: '',
          width: '250px',
          sortable: true
        }
      ],
      totalNumber: 0
    };
  },
  mounted() {
    this.search();
  },
  methods: {
    search() {
      this.$refs['searchForm_ref'].validate((valid) => {
        if (valid) {
          this.searchForm.startTime = dataUtils.dateToStrStart(
            this.searchForm.gatherDate
          );
          this.searchForm.endTime = dataUtils.dateToStrEnd(
            this.searchForm.gatherDate
          );
          this.$refs.publicTable_Ref.loadData();
        }
      });
    },

    export2Excel() {
      this.$refs.publicTable_Ref.exportToExcel({
        fileName: '当日体检人员列表'
      });
    }
  }
};
</script>

<style lang="less" scoped>
.gathered-register-list-container {
  display: flex;
  flex-flow: column;
  height: 100%;
  overflow: hidden !important;

  .header-wrapper {
    background: #fff;
    margin-bottom: 5px;
    padding: 10px 0;

    /deep/ .search-form-box {
      display: flex;
      align-items: center;
      .el-form-item {
        margin-bottom: unset;
      }
    }

    .summary-box {
      padding: 5px 0 0 5px;
    }
  }
  .body-wrapper {
    flex: 1;
  }
}
</style>
