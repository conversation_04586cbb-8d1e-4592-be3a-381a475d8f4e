<template>
  <!-- 检验结果查询 -->
  <div class="inspectionResultQuery">
    <div class="main-wrap">
      <div>
        <div class="header">
          <el-input
            size="small"
            placeholder="体检号"
            clearable
            v-model="regNo"
            class="input"
            @keyup.enter.native="InspectionDataQuery"
          ></el-input>
          <ButtonGroup :btnList="['查询']" @search="InspectionDataQuery" />
        </div>
        <div class="main-info">
          <span>体检号：{{ infoData.regNo }}</span>
          <span>姓名：{{ infoData.name }}</span>
          <span>性别：{{ G_EnumList['Sex'][infoData.sex] }}</span>
          <span>年龄：{{ infoData.age }}岁</span>
          <span>单位：{{ infoData.companyName }}</span>
          <span>套餐：{{ infoData.clusName }}</span>
          <span>状态：{{ G_EnumList['PeStatus'][infoData.peStatus] }}</span>
        </div>
      </div>
      <div class="main-table">
        <div class="physical-table">
          <h3>体检项目：</h3>
          <div class="table-item">
            <PublicTable
              :isSortShow="false"
              :theads="itemsTheads"
              :viewTableList.sync="infoData.peItems"
            ></PublicTable>
          </div>
        </div>
        <div class="examine-table">
          <h3>检验项目：</h3>
          <div class="table-item">
            <PublicTable
              :isSortShow="false"
              :theads="inspectTheads"
              :viewTableList.sync="infoData.inspectItems"
            ></PublicTable>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ButtonGroup from './components/buttonGroup.vue';
import PublicTable from '@/components/publicTable.vue';
import { mapGetters } from 'vuex';
export default {
  name: 'inspectionResultQuery',
  components: {
    ButtonGroup,
    PublicTable
  },
  data() {
    return {
      regNo: '',
      itemsTheads: {
        combCode: '组合代码',
        combName: '组合名称',
        itemCode: '项目代码',
        itemName: '项目名称',
        itemResult: '结果',
        lowerLimit: '下限',
        upperLimit: '上限'
      },
      inspectTheads: {
        itemCode: '项目代码',
        itemName: '项目名称',
        itemResult: '结果',
        lowerLimit: '下限',
        upperLimit: '上限'
      },
      infoData: {
        regNo: '',
        name: '',
        sex: '',
        age: '',
        companyName: '',
        clusName: '',
        peStatus: '',
        peItems: [],
        inspectItems: []
      }
    };
  },
  computed: {
    ...mapGetters(['G_EnumList'])
  },
  methods: {
    // 查询
    InspectionDataQuery() {
      this.$ajax
        .post(this.$apiUrls.InspectionDataQuery, '', {
          query: {
            regNo: this.regNo
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.infoData = returnData;
        });
    }
  }
};
</script>

<style lang="less" scoped>
.inspectionResultQuery {
  .main-wrap {
    height: 100%;
    background: #fff;
    padding: 18px 10px;
    border-radius: 4px;
    color: #2d3436;
  }
  .header {
    width: 358px;
    display: flex;
  }
  .input {
    margin-right: 10px;
  }
  .main-info {
    margin-top: 18px;
    margin-bottom: 10px;
    padding: 6px 18px;
    font-size: 14px;
    background: rgba(250, 182, 59, 0.2);
    border-radius: 4px;
    span {
      margin-right: 48px;
    }
  }
  .main-table {
    height: calc(100% - 92px);
    display: flex;
  }
  .physical-table {
    margin-right: 10px;
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    h3 {
      font-size: 18px;
      margin-bottom: 10px;
    }
  }
  .examine-table {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    h3 {
      font-size: 18px;
      margin-bottom: 10px;
    }
  }
  .table-item {
    height: calc(100% - 34px);
    border: 1px solid rgba(178, 190, 195, 0.5);
    border-radius: 4px;
    overflow: auto;
  }
}
</style>
