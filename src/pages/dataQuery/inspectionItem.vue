<template>
  <div class="comprehensiveQuery">
    <div class="header-wrap">
      <div class="header-search">
        <div class="search-item">
          <span style="width: 60px">体检号</span>
          <el-input
            size="small"
            placeholder="请输入"
            clearable
            @clear="search"
            v-model="searchInfo.regNo"
            class="input"
            @keyup.enter.native="search"
          ></el-input>
        </div>
        <div class="search-item">
          <span style="width: 80px">档案卡号</span>
          <el-input
            size="small"
            placeholder="请输入"
            clearable
            @clear="search"
            v-model="searchInfo.patCode"
            @keyup.enter.native="search"
            class="input"
          ></el-input>
        </div>
        <div class="search-item">
          <div class="item-input">
            <span style="width: 44px">姓名</span>
            <el-input
              size="small"
              placeholder="请输入"
              clearable
              v-model="searchInfo.name"
              class="input"
            ></el-input>
          </div>
          <div class="item-input">
            <span style="width: 44px">性别</span>
            <el-select
              placeholder="请选择"
              size="small"
              clearable
              v-model="searchInfo.sex"
              class="input"
              @change="search"
            >
              <el-option
                v-for="item in G_sexList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="search-item">
          <span style="width: 60px">身份证</span>
          <el-input
            size="small"
            placeholder="请输入"
            clearable
            @clear="search"
            v-model="searchInfo.cardNo"
            class="input"
            @keyup.enter.native="search"
          ></el-input>
        </div>
        <div class="search-item" v-if="G_config.physicalMode.includes('普检')">
          <span>分类</span>
          <el-select
            placeholder="请选择"
            size="small"
            clearable
            v-model="searchInfo.peCls"
            class="input"
            @change="search"
          >
            <el-option
              v-for="item in G_peClsList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <div class="search-item">
          <span>状态</span>
          <el-select
            placeholder="请选择"
            size="small"
            clearable
            v-model="searchInfo.peStatus"
            class="input"
            @change="search"
          >
            <el-option
              v-for="item in G_peStatus"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <div class="search-item">
          <span style="width: 100px">登记时间</span>
          <el-date-picker
            :picker-options="{ shortcuts: G_datePickerShortcuts }"
            @change="search"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
            class="time-picker"
            :clearable="false"
            v-model.trim="time"
          >
          </el-date-picker>
        </div>
        <div class="search-item">
          <span>单位</span>
          <el-cascader
            ref="company_cascader_ref"
            v-model="companyCasList"
            :filter-method="filterMethod"
            :options="companyList"
            :props="{ multiple: false }"
            clearable
            filterable
            size="small"
            collapse-tags
            @change="companyChange"
          >
          </el-cascader>
        </div>
        <div class="search-item">
          <span>套餐</span>
          <el-select
            placeholder="请选择"
            size="small"
            filterable
            clearable
            v-model="searchInfo.clusterCode"
            class="input"
            @change="search"
          >
            <el-option
              v-for="item in clusterList"
              :key="item.clusCode"
              :label="item.clusName"
              :value="item.clusCode"
            >
            </el-option>
          </el-select>
        </div>
        <div class="search-item">
          <el-radio-group v-model="searchInfo.PersonCompany" @change="search">
            <el-radio :label="0">所有</el-radio>
            <el-radio :label="1">个人</el-radio>
            <el-radio :label="2">团体</el-radio>
          </el-radio-group>
        </div>
        <div class="">
          <el-radio-group v-model="searchInfo.CheckCls" @change="search">
            <el-radio :label="-1">全部</el-radio>
            <el-radio
              :label="item.value"
              v-for="item in G_checkCls"
              :key="item.value"
              >{{ item.label }}</el-radio
            >
          </el-radio-group>
        </div>
      </div>
      <div style="display: flex; align-items: center">
        <div class="status-wrap">
          <span>记录数：{{ recordStatus.totalCount }}</span>
          <span class="cell_red"
            >未激活数：{{ recordStatus.inActiveCount }}</span
          >
          <span class="cell_yellow"
            >未检查数：{{ recordStatus.unCheckedCount }}</span
          >
          <span class="cell_blue"
            >正在检查数：{{ recordStatus.isCheckingCount }}</span
          >
          <span class="cell_green"
            >已检完数：{{ recordStatus.checkedCount }}</span
          >
          <span class="cell_violet"
            >已审核数：{{ recordStatus.approvedCount }}</span
          >
          <span>已发报告数：{{ recordStatus.issuedReportCount }}</span>
        </div>
        <div class="search-btn" style="margin-left: auto">
          <span class="cell_blue"></span>
          <ButtonGroup
            :btnList="['查询', '导出']"
            @search="search"
            @exports="exports"
          >
          </ButtonGroup>
        </div>
      </div>
    </div>
    <div class="main-wrap">
      <div class="main-table">
        <PublicTable
          ref="main_table_ref"
          @request-success="requestSuccess"
          :theads="theads"
          :url="$apiUrls.ReadExamItemEntryReport"
          :params="params"
          rowKey="regNo"
          remoteByPage
          :excelDataMap="excelDataMap"
          :elStyle="{
            stripe: false,
            'show-selection': false,
            border: true
          }"
          :tableDataMap="
            (data) => {
              return data.examItemEntryReports;
            }
          "
        >
          <template #bookType="{ scope }">
            <div>
              {{ G_EnumList['BookType'][scope.row.bookType] }}
            </div>
          </template>
          <template #peStatus="{ scope }">
            <div
              slot="reference"
              :class="peStatusFormat(scope.row.peStatus)"
              style="cursor: pointer"
            >
              {{ G_EnumList['PeStatus'][scope.row.peStatus] }}
            </div>
          </template>
          <template #isActive="{ scope }">
            <el-checkbox v-model="scope.row.isActive" disabled></el-checkbox>
          </template>
          <template #isCompanyCheck="{ scope }">
            <el-checkbox
              v-model="scope.row.isCompanyCheck"
              disabled
            ></el-checkbox>
          </template>

          <template #peCls="{ scope }">
            <div
              style="
                display: flex;
                align-items: center;
                justify-content: center;
              "
            >
              {{ G_EnumList['PeCls'][scope.row.peCls] }}
            </div>
          </template>
          <template #sex="{ scope }">
            <div>
              {{ G_EnumList['Sex'][scope.row.sex] }}
            </div>
          </template>
          <template #reportPrinted="{ scope }">
            <div>
              <el-checkbox
                v-model="scope.row.reportPrinted"
                disabled
              ></el-checkbox>
            </div>
          </template>
          <template #price="{ scope }">
            {{ handlePrice(scope.row.price) }}
          </template>
        </PublicTable>
      </div>
    </div>
  </div>
</template>

<script>
import ButtonGroup from './components/buttonGroup.vue';
import PublicTable from '@/components/publicTable2.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';

export default {
  name: 'inspectionItem',
  components: {
    ButtonGroup,
    PublicTable
  },
  data() {
    return {
      recordStatus: {
        totalCount: 0,
        inActiveCount: 0,
        unCheckedCount: 0,
        isCheckingCount: 0,
        checkedCount: 0,
        approvedCount: 0,
        issuedReportCount: 0
      },
      searchInfo: {
        regNo: '', //体检号
        patCode: '', //档案卡号
        name: '', //姓名
        sex: '', //性别
        cardNo: '', //证证件号
        companyCode: '', //单位代码
        companyTimes: 0, //单位次数
        clusterCode: '', //套餐代码
        CheckCls: -1, //组合的检查类型
        PersonCompany: 0, //个人/团体/所有
        peCls: '', //体检分类
        peStatus: '', //体检状态
        beginTime: '', //开始时间
        endTime: '' //结束时间
      },
      theads: [
        {
          prop: 'bookType',
          label: '预约类型',
          align: 'center',
          width: '80px',
          sortable: false
        },
        {
          prop: 'peStatus',
          label: '体检状态',
          align: 'center',
          width: '100px',
          sortable: false
        },
        {
          prop: 'name',
          label: '姓名',
          align: 'center',
          width: '80px',
          sortable: false
        },
        {
          prop: 'regNo',
          label: '体检号',
          align: 'center',
          width: '130px',
          sortable: false
        },

        {
          prop: 'sex',
          label: '性别',
          align: 'center',
          width: '60px',
          sortable: false
        },
        {
          prop: 'age',
          label: '年龄',
          align: 'center',
          width: '60px',
          sortable: false
        },
        {
          prop: 'cardNo',
          label: '证件号',
          align: 'center',
          width: '180px',
          sortable: false
        },
        {
          prop: 'peCls',
          label: '体检分类',
          align: 'center',
          width: '120px',
          sortable: false
        },
        {
          prop: 'clusterName',
          label: '套餐名称',
          align: 'center',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'registerCombs',
          label: '项目明细',
          align: 'center',
          sortable: false,
          showOverflowTooltip: true
        }
      ],
      time: [
        new Date().toISOString().slice(0, 10),
        new Date().toISOString().slice(0, 10)
      ],
      clusterList: [], //套餐下拉
      fixed_clusterList: [], //套餐下拉
      companyList: [], //单位下拉
      companyCasList: [], //选中的单位及次数
      params: {} //请求参数
    };
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_sexList',
      'G_peClsList',
      'G_peStatus',
      'G_checkCls',
      'G_datePickerShortcuts',
      'G_config'
    ])
  },
  created() {
    this.getClusterList();
    this.getCompanyList();
  },
  methods: {
    // 处理价格
    handlePrice(price) {
      return dataUtils.handlePrice(price);
    },
    // 获取单位下拉
    getCompanyList() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },

    // 获取套餐下拉
    getClusterList() {
      this.$ajax.post(this.$apiUrls.Cluster).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.clusterList = returnData;
        this.fixed_clusterList = returnData;
      });
    },
    // 单位改变更新套餐
    companyTimesChange(data) {
      this.searchInfo.clusterCode = '';
      if (!data || data.length === 0) {
        this.clusterList = this.fixed_clusterList;
        return;
      }
      this.$ajax
        .post(this.$apiUrls.ReadCompanyCandidateCluster, '', {
          query: {
            companyCode: data[0],
            companyTimes: data[1]
          }
        })
        .then(async (r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.clusterList = returnData || [];
        });
    },
    // 体检状态格式化
    peStatusFormat(key) {
      let className = '';
      switch (key) {
        case 0:
          className = 'cell_yellow';
          break;
        case 1:
          className = 'cell_blue';
          break;
        case 2:
          className = 'cell_green';
          break;
        case 3:
          className = '';
          break;
        case 4:
          className = 'cell_violet';
          break;
      }
      return className;
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    //单位下拉变化
    companyChange(data) {
      this.companyTimesChange(data);
      if (!data || data.length === 0) {
        this.time = [dataUtils.getDate(), dataUtils.getDate()];
        this.search();
        return;
      }
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.time = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.search();
    },
    //查询
    search() {
      this.params = dataUtils.deepCopy(this.searchInfo);
      this.params.companyCode =
        this.params.companyCode[this.params.companyCode.length - 1];
      this.params.peCls =
        this.params.peCls === '' ? -1 : (this.params.peCls = this.params.peCls);
      this.params.peStatus =
        this.params.peStatus === '' ? -1 : this.params.peStatus;
      this.params.companyCode = this.companyCasList[0];
      this.params.companyTimes = this.companyCasList[1];
      this.params.beginTime = this.time[0];
      this.params.endTime = this.time[1];
      this.params.sex = this.params.sex === '' ? -1 : this.params.sex;
      this.$refs.main_table_ref.loadData();
    },
    //查询回调
    requestSuccess(res) {
      this.recordStatus = res?.returnData?.recordStatus || {
        totalCount: 0,
        inActiveCount: 0,
        unCheckedCount: 0,
        isCheckingCount: 0,
        checkedCount: 0,
        approvedCount: 0,
        issuedReportCount: 0
      };
    },
    //导出
    exports() {
      this.$refs.main_table_ref.exportToExcelAll();
    },
    //导出excel数据处理
    excelDataMap(data) {
      if (!data) return [];
      data.forEach((item, i) => {
        item.bookType = this.G_EnumList['BookType'][item.bookType]; //预约类型
        item.sex = this.G_EnumList['Sex'][item.sex]; //性别
        item.peStatus = this.G_EnumList['PeStatus'][item.peStatus]; //状态
        item.reportPrinted = item.reportPrinted ? '是' : '否'; //报告是否打印
        item.isActive = item.isActive ? '是' : '否'; //激活标志
        item.peCls = this.G_EnumList['PeCls'][item.peCls]; // 体检分类
      });
      return data;
    }
  }
};
</script>

<style lang="less" scoped>
.comprehensiveQuery {
  color: #2d3436;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  overflow: auto;
  .header-wrap {
    background: #fff;
    padding: 18px 18px 0 18px;
  }
  .header-search {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .search-item {
    width: 280px;
    display: flex;
    align-items: center;
    margin-right: 16px;
    &:last-child {
      margin-right: 0;
    }
    span {
      width: 40px;
      font-weight: 600;
      font-size: 14px;
      margin-right: 10px;
    }
  }
  .item-input {
    display: flex;
    align-items: center;
    margin-right: 10px;
    &:last-child {
      margin-right: 0;
    }
  }
  .input {
    width: 100%;
  }
  .search-select {
    display: flex;
    margin-right: 20px;
  }
  .status-wrap {
    flex: 1;
    font-weight: 600;
    font-size: 14px;
    span {
      margin-right: 22px;
      &:last-child {
        margin-right: 0;
      }
    }
  }
  .main-wrap {
    background: #fff;
    padding: 0 18px 18px 18px;
    flex: 1;
    overflow: auto;
  }
  .search-btn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    span {
      font-weight: 600;
      font-size: 14px;
    }
  }
  .main-table {
    height: 100%;
    border: 1px solid rgba(178, 190, 195, 0.5);
    border-radius: 4px;
    overflow: auto;

    /deep/.row-printed {
      background: rgba(38, 126, 235, 0.5) !important;
    }
  }
  /deep/.el-drawer__header {
    align-items: center;
    display: flex;
    background: rgba(23, 112, 223, 0.2);
    line-height: 42px;
    font-size: 18px;
    padding: 0;
    padding-left: 20px;
    color: #2d3436;
    margin-bottom: 0;
    font-weight: 600;
  }
  /deep/.el-drawer__close-btn {
    font-size: 30px;
  }
  .drawer-main {
    height: 100%;
    padding: 18px;
    display: flex;
  }
  .tabs {
    width: 140px;
    background: rgba(178, 190, 195, 0.1);
    border-radius: 4px;
    margin-right: 18px;
    padding: 20px 10px;
    font-weight: 600;
    li {
      color: #1770df;
      border: 1px solid #1770df;
      border-radius: 4px;
      text-align: center;
      height: 38px;
      line-height: 38px;
      margin-bottom: 30px;
      background: #fff;
      cursor: pointer;
      &:last-child {
        margin-bottom: 0;
      }
      &.active {
        background: #1770df;
        color: #fff;
      }
    }
  }
  .tabs-main {
    flex: 1;
    overflow: auto;
    h3 {
      font-size: 18px;
      margin-bottom: 20px;
    }
  }
  .tabs-component {
    height: calc(100% - 48px);
  }
  /deep/.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    background-color: #409eff;
    border-color: #409eff;
  }
  /deep/.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
    border-color: #fff;
  }
}
.step-popover {
  .step {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    position: relative;
  }
  .step-item {
    flex: 1;
    .step-default {
      color: #969799;
      position: relative;
    }
    .step-active {
      color: #1770df;
      position: relative;
      .icon {
        font-size: 14px;
      }
    }
    .step-finish {
      color: #1770df;
      position: relative;
      .step-circle {
        background: #1770df;
      }
      .step-line {
        background: #1770df;
      }
    }
    .step-title {
      margin-bottom: 6px;
      margin-left: 14px;
      transform: translateX(-50%);
    }
    .step-circle-container {
      background: #fff;
      position: absolute;
      top: 30px;
      left: -14px;
      z-index: 1;
      padding: 0 8px;
      background-color: #fff;
      transform: translateY(-50%);
    }
    .step-circle {
      width: 10px;
      height: 10px;
      background: #969799;
      border-radius: 50%;
    }
    .step-line {
      position: absolute;
      top: 30px;
      left: 0;
      width: 100%;
      height: 1px;
      background: #ebedf0;
      transition: background-color 0.3s;
    }
    &:first-child {
      .step-title {
        margin-left: 0;
        transform: none;
      }
      .step-circle-container {
        right: auto;
        left: -9px;
      }
    }
    &:last-child {
      position: absolute;
      right: 1px;
      width: auto;
      .step-title {
        margin-left: 0;
        transform: none;
      }
      .step-circle-container {
        right: -9px;
        left: auto;
      }
      .step-line {
        width: 0;
      }
    }
  }
}
.status-wrap {
  flex: 1;
  font-weight: 600;
  font-size: 14px;
  span {
    margin-right: 22px;
    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
