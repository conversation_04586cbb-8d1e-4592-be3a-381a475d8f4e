<template>
  <div class="majorAnomalyList">
    <div class="main">
      <div class="header-wrap">
        <div class="header-search">
          <h3>重大阳性列表:</h3>
          <div class="search-item">
            <span>统计时间</span>
            <el-date-picker
              :picker-options="{ shortcuts: G_datePickerShortcuts }"
              v-model="timeVal"
              type="daterange"
              size="mini"
              style="width: 250px"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </div>
          <div class="search-item">
            <span>单位</span>
            <el-cascader
              ref="company_cascader_ref"
              v-model="searchInfo.companyCode"
              :options="unitList"
              :props="{ multiple: false }"
              clearable
              filterable
              size="mini"
              collapse-tags
              @change="companyChange"
              placeholder="请选择单位"
            >
            </el-cascader>
          </div>
          <div class="search-item">
            <span>部门</span>
            <el-select
              class="select"
              v-model.trim="searchInfo.companyDeptCode"
              placeholder="请选择"
              size="mini"
              filterable
              clearable
              :disabled="isHavue"
              @change="search"
            >
              <el-option
                v-for="(item, index) in companyDeptList"
                :key="index"
                :label="item.deptName"
                :value="item.deptCode"
              ></el-option>
            </el-select>
          </div>

          <div class="search-item">
            <span>重大阳性类型</span>
            <el-select
              @change="search"
              style="width: 100px"
              placeholder="请选择"
              size="mini"
              clearable
              v-model="searchInfo.positiveType"
              class="input"
            >
              <el-option
                v-for="item in G_PositiveType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
          <div class="search-item">
            <span>重大阳性</span>
            <el-select
              @change="search"
              style="margin-right: 10px"
              v-model="searchInfo.majorCode"
              size="mini"
              filterable
              clearable
              reserve-keyword
              placeholder="重大阳性"
              :remote-method="remoteMethod"
            >
              <el-option
                v-for="item in this.zdyxList"
                :key="item.value"
                :label="item.label"
                :value="item.label"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="header-search" style="margin-bottom: 10px">
          <span>体检号</span>
          <el-input
            style="width: 150px"
            size="mini"
            placeholder="体检号"
            clearable
            v-model="searchInfo.regNo"
            class="input"
            @keyup.enter.native="search"
          ></el-input>
          <label>姓名</label>
          <el-input
            style="width: 150px"
            size="mini"
            placeholder="姓名"
            clearable
            v-model="searchInfo.name"
            class="input"
            @keyup.enter.native="search"
          ></el-input>

          <div class="search-item">
            <span>已确认</span>
            <el-select
              @change="search"
              style="width: 100px"
              placeholder="请选择"
              size="mini"
              v-model="searchInfo.isConfirm"
              class="input"
            >
              <el-option
                v-for="(item, index) in guidancePrint"
                :key="index"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
          <div class="search-item">
            <span>已随访</span>
            <el-select
              @change="search"
              style="width: 100px"
              placeholder="请选择"
              size="mini"
              v-model="searchInfo.hasFollowUp"
              class="input"
            >
              <el-option
                v-for="(item, index) in guidancePrint"
                :key="index"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>

          <ButtonGroup
            style="margin-left: 10px"
            :btnList="['查询', '删除']"
            @search="search"
            @deletes="deletes"
          ></ButtonGroup>
          <el-button
            size="mini"
            class="violet_btn"
            style="margin-left: 10px"
            icon="iconfont icon-huifuxitongmoren"
            @click="regainRecord"
            >恢复记录</el-button
          >
          <el-button @click="exports" size="mini" icon="iconfont icon-daochu"
            >导出</el-button
          >
        </div>
      </div>
      <div class="contDiv">
        <PublicTable
          showOverflowTooltip
          isCheck
          :isSortShow="false"
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          @selectionChange="selectionChange"
          v-model="checkList"
          :columnWidth="columnWidth"
          @currentChange="rowClick"
          :cell_red="['details']"
        >
          <template #sex="{ scope }">
            <div>
              {{ G_EnumList['Sex'][scope.row.sex] }}
            </div>
          </template>
          <template #positiveType="{ scope }">
            <div>
              {{ G_EnumList['PositiveType'][scope.row.positiveType] }}
            </div>
          </template>
          <template #isConfirm="{ scope }">
            <el-checkbox v-model="scope.row.isConfirm" disabled></el-checkbox>
          </template>
          <template #columnRight>
            <el-table-column prop="" label="操作" width="180" fixed="right">
              <template slot-scope="scope">
                <el-button
                  type="primary"
                  plain
                  size="mini"
                  @click="jump(scope.row)"
                  :disabled="!scope.row.isConfirm"
                  >随访登记</el-button
                >
                <el-button
                  type="primary"
                  plain
                  size="mini"
                  v-if="!scope.row.isConfirm"
                  @click="confirmMajorPositive(scope.row)"
                  >确认</el-button
                >
                <el-button
                  type="primary"
                  plain
                  size="mini"
                  v-if="scope.row.isConfirm"
                  @click="cancelConfirmMajorPositive(scope.row)"
                  >撤销</el-button
                >
              </template>
            </el-table-column>
          </template>
        </PublicTable>
      </div>
    </div>
    <el-dialog
      :visible.sync="showList"
      width="1313px"
      top="4%"
      custom-class="showList"
      :close-on-click-modal="false"
    >
      <div slot="title" class="dialog-title">删除记录查询窗口</div>
      <div class="dialog-btn">
        <label>统计时间</label>
        <el-date-picker
          @change="search"
          :picker-options="{ shortcuts: G_datePickerShortcuts }"
          v-model="timeVal2"
          type="daterange"
          size="mini"
          style="width: 250px; margin-right: 10px"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
        <div class="search-item">
          <span>单位</span>
          <el-cascader
            ref="company_cascader_ref"
            style="margin-right: 10px"
            v-model="searchInfo2.companyCode"
            :options="unitList"
            :props="{ multiple: false }"
            clearable
            filterable
            size="mini"
            collapse-tags
            @change="companyChange"
            placeholder="请选择单位"
          >
          </el-cascader>
        </div>
        <div class="search-item">
          <span>部门</span>
          <el-select
            class="select"
            v-model.trim="searchInfo2.companyDeptCode"
            placeholder="请选择"
            size="mini"
            filterable
            clearable
            :disabled="isHavue"
            @change="search"
          >
            <el-option
              v-for="(item, index) in companyDeptList"
              :key="index"
              :label="item.deptName"
              :value="item.deptCode"
            ></el-option>
          </el-select>
        </div>
        <label>体检号</label>
        <el-input
          style="width: 150px"
          size="mini"
          placeholder="体检号"
          clearable
          v-model="searchInfo2.regNo"
          class="input"
          @keyup.enter.native="search"
        ></el-input>
        <span>姓名</span>
        <el-input
          style="width: 150px"
          size="mini"
          placeholder="姓名"
          clearable
          v-model="searchInfo2.name"
          class="input"
          @keyup.enter.native="search"
        ></el-input>
      </div>
      <div class="contDiv">
        <PublicTable
          showOverflowTooltip
          :isSortShow="false"
          :viewTableList.sync="tableData2"
          :theads.sync="theads"
          :columnWidth="columnWidth"
          @currentChange="rowClick"
          :cell_red="['details']"
        >
          <template #sex="{ scope }">
            <div>
              {{ G_EnumList['Sex'][scope.row.sex] }}
            </div>
          </template>
          <template #positiveType="{ scope }">
            <div>
              {{ G_EnumList['PositiveType'][scope.row.positiveType] }}
            </div>
          </template>
          <template #isConfirm="{ scope }">
            <el-checkbox v-model="scope.row.isConfirm" disabled></el-checkbox>
          </template>
          <template #columnRight>
            <el-table-column prop="" label="操作" width="110" fixed="right">
              <template slot-scope="scope">
                <el-button
                  type="primary"
                  plain
                  size="mini"
                  @click="restoreMajorPositive(scope.row)"
                  >恢复</el-button
                >
              </template>
            </el-table-column>
          </template>
        </PublicTable>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import PublicTable from '@/components/publicTable.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
import ButtonGroup from './components/buttonGroup.vue';
import moment from 'moment';
import ExportExcel from '@/common/excel/exportExcel';
export default {
  name: 'majorAnomalyList',
  mixins: [ExportExcel],
  components: {
    PublicTable,
    ButtonGroup
  },
  data() {
    return {
      radio: '',
      unitList: [],
      fixed_unitList: [],
      zdyxList: [],
      companyDeptList: [],
      timeVal: [],
      timeVal2: [],
      isHavue: true,
      searchInfo: {
        beginDate: '',
        endDate: '',
        majorCode: '',
        companyCode: '',
        companyDeptCode: '',
        regNo: '',
        name: '',
        isConfirm: null,
        hasFollowUp: null,
        positiveType: null
      },
      searchInfo2: {
        beginDate: '',
        endDate: '',
        majorCode: '',
        companyCode: '',
        companyDeptCode: '',
        regNo: '',
        name: '',
        isConfirm: null,
        hasFollowUp: null,
        positiveType: null
      },
      showList: false,
      keyWord: '',
      tableData: [],
      tableData2: [],
      theads: {
        regNo: '体检号',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        tel: '电话',
        // companyName: "单位名称",
        positiveType: '重大阳性类型',
        positiveCode: '重大阳性代码',
        positiveName: '重大阳性名称',
        isConfirm: '是否确认',
        confirmOperator: '确认人',
        confirmTime: '确认时间',
        afterFollowUpTime: '处理时间',
        notifier: '随访人',
        afterFollowUp: '随访情况',
        secondaryNotifier: '二次随访人',
        secondaryAfterFollowUp: '二次随访情况',
        finallyNotifier: '后续随访员',
        finallyAfterFollowUp: '后续随访情况',
        recordTime: '生成时间'
      },
      columnWidth: {
        regNo: 130,
        // details: 400,
        createTime: 170,
        age: 60,
        sex: 60,
        tel: 120,
        creator: 90,
        name: 90,
        positiveType: 110,
        positiveCode: 110,
        positiveName: 210,
        confirmTime: 160,
        afterFollowUpTime: 160,
        recordTime: 160,
        // companyName:210,
        afterFollowUp: 210,
        secondaryNotifier: 90,
        secondaryAfterFollowUp: 160,
        finallyNotifier: 110,
        finallyAfterFollowUp: 130
      },
      regNo: '',
      majorPositiveId: '',
      addList: {
        regNo: '',
        followContent: '',
        deptCode: '',
        majorPositiveId: ''
      },
      guidancePrint: [
        {
          value: null,
          label: '全部'
        },
        {
          value: true,
          label: '是'
        },
        {
          value: false,
          label: '否'
        }
      ],
      checkList: []
    };
  },
  created() {
    this.getCompany();
    this.getMajorDict();
  },
  mounted() {
    this.timeVal = [
      moment().add(-7, 'd').format('YYYY-MM-DD'),
      moment().startOf('day').format('YYYY-MM-DD')
    ];
    this.timeVal2 = [
      moment().add(-7, 'd').format('YYYY-MM-DD'),
      moment().startOf('day').format('YYYY-MM-DD')
    ];
    if (this.$route.query?.regNo) {
      this.searchInfo.regNo = this.$route.query.regNo;
    }
    this.search();
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_userInfo',
      'G_datePickerShortcuts',
      'G_PositiveType'
    ])
  },
  methods: {
    search() {
      if (this.timeVal.length == 0) {
        this.searchInfo.beginDate = '';
        this.searchInfo.endDate = '';
      } else {
        this.searchInfo.beginDate = this.timeVal[0];
        this.searchInfo.endDate = this.timeVal[1];
      }
      console.log(this.searchInfo);
      let temp = JSON.parse(JSON.stringify(this.searchInfo));
      temp.companyCode = this.searchInfo.companyCode[0];
      temp.positiveType =
        temp.positiveType == null || temp.positiveType == ''
          ? 0
          : temp.positiveType;
      this.$ajax.post(this.$apiUrls.MajorPositiveQuery, temp).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.tableData = returnData || [];
        if (this.tableData.length >= 1) {
          this.$message({
            message: '加载成功!',
            type: 'success',
            showClose: true
          });
        } else {
          this.$message({
            message: '暂无数据!',
            type: 'success',
            showClose: true
          });
        }
      });
    },
    // 获取单位列表
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.unitList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    //查询公司部门信息
    getDepartList(companyCode) {
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanyDepartment, {
          companyCode: companyCode?.[0] || '',
          deptCode: '',
          deptName: ''
        })
        .then((r) => {
          //console.log("r", r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.companyDeptList = returnData || [];
          this.searchInfo.companyDeptCode = null;
        });
    },
    getMajorDict() {
      this.$ajax
        .post(this.$apiUrls.RD_MajorPositive + '/Read', [])
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.zdyxList = returnData.map((item) => {
            return {
              value: item.positiveCode,
              label: item.positiveName
            };
          });
        });
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) {
        this.isHavue = true;
        this.searchInfo.companyDeptCode = null;
      }
      const currentlySelected = this.unitList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.timeVal = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.getDepartList(data);
      this.isHavue = false;
      // this.search();
    },
    rowClick(row) {
      console.log('[ row ]-373', row);
      this.majorPositiveId = row.majorPositiveId;
    },

    //删除
    deletes() {
      console.log('[ this.majorPositiveId ]-422', this.majorPositiveId);
      if (!this.majorPositiveId && this.checkList.length == 0) {
        this.$message({
          message: '请先选中再删除!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let checkList = [];
      if (this.checkList.length == 0) {
        checkList = [this.majorPositiveId];
      } else {
        checkList = this.checkList.map((item) => item.majorPositiveId);
      }
      console.log(checkList);
      this.$confirm(`是否确定删除?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax
            .post(this.$apiUrls.BatchDeleteMajorPositive, checkList)
            .then((r) => {
              let { success } = r.data;
              if (!success) return;
              this.majorPositiveId = '';
              this.$nextTick(() => {
                this.search();
              });
            });
        })
        .catch(() => {});
    },
    //随访登记跳转到随访登记页面
    jump(row) {
      this.addList = {
        regNo: row.regNo,
        followContent: row.positiveName,
        deptCode: this.G_userInfo.codeOper.deptCode,
        recorder: this.G_userInfo.codeOper.name,
        majorPositiveId: row.majorPositiveId,
        abnormalType: 2
      };
      this.$ajax
        .post(this.$apiUrls.InsertPeFollowUp, this.addList)
        .then((r) => {
          let { success } = r.data;
          if (!success) return;
          this.$message({
            message: '成功!',
            type: 'success',
            showClose: true
          });
          this.$nextTick(() => {
            this.$router.push({
              path: '/dataQuery/followUpReg',
              name: 'followUpReg',
              query: {
                regNo: row.regNo
              }
            });
          });
        });
    },
    remoteMethod(query) {
      if (query !== '') {
        setTimeout(() => {
          this.unitList = this.fixed_unitList.filter((item) => {
            return (
              item.companyName.toLowerCase().indexOf(query.toLowerCase()) > -1
            );
          });
        }, 200);
      } else {
        this.unitList = this.fixed_unitList.slice(0, 200);
      }
    },
    confirmMajorPositive(row) {
      this.$confirm('是否确认此记录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax
            .paramsPost(this.$apiUrls.ConfirmMajorPositive, {
              majorPositiveId: row.majorPositiveId
            })
            .then((r) => {
              let { success } = r.data;
              if (!success) return;
              this.$message({
                message: '成功',
                type: 'success'
              });
              this.majorPositiveId = '';
              this.$nextTick(() => {
                this.search();
              });
            });
        })
        .catch(() => {});
    },
    cancelConfirmMajorPositive(row) {
      this.$confirm('是否撤销确认此记录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax
            .paramsPost(this.$apiUrls.CancelConfirmMajorPositive, {
              majorPositiveId: row.majorPositiveId
            })
            .then((r) => {
              let { success } = r.data;
              if (!success) return;
              this.$message({
                message: '成功',
                type: 'success'
              });
              this.majorPositiveId = '';
              this.$nextTick(() => {
                this.search();
              });
            });
        })
        .catch(() => {});
    },
    restoreMajorPositive(row) {
      this.$confirm('是否恢复此记录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax
            .paramsPost(this.$apiUrls.RestoreMajorPositive, {
              majorPositiveId: row.majorPositiveId
            })
            .then((r) => {
              let { success } = r.data;
              if (!success) return;
              this.$message({
                message: '成功',
                type: 'success'
              });
              this.majorPositiveId = '';
              this.$nextTick(() => {
                this.queryDeleteMajorPositiveList();
              });
            });
        })
        .catch(() => {});
    },
    regainRecord() {
      this.showList = !this.showList;
      this.queryDeleteMajorPositiveList();
    },
    queryDeleteMajorPositiveList() {
      if (this.timeVal2.length == 0) {
        this.searchInfo2.beginDate = '';
        this.searchInfo2.endDate = '';
      } else {
        this.searchInfo2.beginDate = this.timeVal2[0];
        this.searchInfo2.endDate = this.timeVal2[1];
      }
      console.log(this.searchInfo2);
      let temp = JSON.parse(JSON.stringify(this.searchInfo2));
      temp.companyCode = this.searchInfo2.companyCode[0];
      temp.positiveType = temp.positiveType == null ? 0 : temp.positiveType;
      this.$ajax
        .post(this.$apiUrls.QueryDeleteMajorPositiveList, temp)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.tableData2 = returnData || [];
          if (this.tableData2.length >= 1) {
            this.$message({
              message: '成功!',
              type: 'success',
              showClose: true
            });
          } else {
            this.$message({
              message: '暂无数据!',
              type: 'success',
              showClose: true
            });
          }
        });
    },
    // 重大阳性列表的选中回调
    selectionChange(val, checkList) {
      this.checkList = checkList;
    },
    // 导出excel
    exports() {
      if (this.checkList.length == 0) {
        this.$message({
          message: '请先选择需要导出的名单！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let checkList = dataUtils.deepCopy(this.checkList).map((item) => {
        item.positiveType = this.G_EnumList['PositiveType'][item.positiveType];
        item.isConfirm = item.isConfirm ? '是' : '否';
        return item;
      });
      let fileName = '重大阳性' + dataUtils.getNowDateTiemNo();
      let countText = `总人数：${this.checkList.length} 人`;
      this.exportExcel(
        fileName,
        '重大阳性列表',
        this.theads,
        checkList,
        countText
      );
    }
  },
  activated() {}
};
</script>
<style lang="less" scoped>
.majorAnomalyList {
  color: #2d3436;
  display: flex;
  .main {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 18px 10px;
    font-size: 14px;
    background: #fff;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    margin-bottom: 18px;

    .pBtn {
      display: flex;
      flex-direction: row;
      align-items: center;

      & > .el-input {
        margin-right: 20px;
      }
    }
  }

  .contDiv {
    // padding: 0 18px 18px 18px;
    flex: 1;
    height: 500px;
    overflow: auto;
    border: 1px solid rgba(178, 190, 195, 0.5);
    border-radius: 4px;
  }

  .el-radio {
    margin-right: 10px;
  }

  .header-search {
    display: flex;
    align-items: center;
    margin-bottom: 5px;

    &:last-child {
      margin-bottom: 0;
    }

    .pBtn {
      display: flex;
      flex-direction: row;
      align-items: center;

      & > .el-input {
        margin-right: 20px;
      }
    }
  }

  .header-wrap {
    background: #fff;
    // padding: 18px 18px 0 18px;
    // margin-bottom: 18px;
  }

  .showList {
    height: 85vh;
    /deep/.el-dialog__body {
      padding: 18px;
      .search-swtich-personal-box {
        margin-right: 30px;

        label {
          width: 40px;
        }
      }

      .search-swtich-collected-box {
        label {
          width: 60px;
        }
      }
    }
  }
  .dialog-title {
    background: #d1e2f9;
    font-size: 18px;
    padding: 18px;
    font-weight: 600;
  }
  .dialog-btn {
    display: flex;
    align-items: center;
    color: #2d3436;
    margin-bottom: 18px;
    .label {
      font-weight: 600;
      margin-right: 10px;
    }
    .time {
      margin-left: 48px;
      margin-right: 10px;
    }
  }
  .dialog-table {
    height: calc(100% - 50px);
    overflow: auto;
    border: 1px solid #d8dee1;
    border-radius: 4px;
  }
}
span {
  white-space: nowrap;
  margin: 0 5px 0 10px;
}
h3 {
  white-space: nowrap;
}
.search-item {
  display: flex;
  align-items: center;
}
</style>
