<template>
  <div class="comprehensiveQuery">
    <div class="header-wrap">
      <div class="header-search">
        <div class="search-item">
          <span>体检号</span>
          <el-input
            size="mini"
            style="width: 130px"
            placeholder="请输入"
            clearable
            v-model.trim="searchInfo.regNo"
            class="input"
            @keyup.enter.native="search"
          ></el-input>
        </div>
        <div class="search-item">
          <span>档案卡号</span>
          <el-input
            size="mini"
            style="width: 130px"
            placeholder="请输入"
            clearable
            v-model.trim="searchInfo.patCode"
            @keyup.enter.native="search"
            class="input"
          ></el-input>
        </div>
        <div class="search-item">
          <div class="item-input">
            <span>姓名</span>
            <el-input
              style="width: 130px"
              size="mini"
              placeholder="请输入"
              clearable
              v-model.trim="searchInfo.name"
              @keyup.enter.native="search"
              class="input"
            ></el-input>
          </div>
        </div>
        <div class="search-item">
          <div class="item-input">
            <span>性别</span>
            <el-select
              style="width: 84px"
              placeholder="请选择"
              size="mini"
              clearable
              v-model="searchInfo.sex"
              class="input"
            >
              <el-option
                v-for="item in G_sexList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="search-item">
          <span>身份证</span>
          <el-input
            style="width: 150px"
            size="mini"
            placeholder="请输入"
            clearable
            v-model.trim="searchInfo.cardNo"
            class="input"
            @keyup.enter.native="search"
          ></el-input>
        </div>
        <!-- <div class="search-item">
          <span>体检次数</span>
          <el-input-number 
            style="width:65px"
            size="mini"
            placeholder="请输入"
            :controls="false"
            :step="1"
            :min="0"
            clearable
            v-model.trim="searchInfo.registerTimes"
            class="input"
            @keyup.enter.native="search"
          ></el-input-number>
        </div> -->
      </div>
      <div class="header-search">
        <div class="search-item">
          <el-radio-group v-model="searchInfo.peType" @change="radioChange">
            <el-radio :label="0">所有</el-radio>
            <el-radio :label="1">个人</el-radio>
            <el-radio :label="2">团体</el-radio>
          </el-radio-group>
        </div>
        <div class="search-item">
          <span>单位</span>
          <!-- <el-cascader
            ref="company_cascader_ref"
            v-model="searchInfo.companyList"
            :options="companyOptions"
            :props="companyProps"
            clearable
            filterable
            size="mini"
            placeholder="请选择"
            style="width: 16rem"
            @change="
              () => {
                // 手动关闭下拉框
                this.$refs.company_cascader_ref.dropDownVisible = false;
              }
            "
          ></el-cascader> -->
          <el-cascader
            style="width: 330px"
            ref="company_cascader_ref"
            v-model="searchInfo.companyList"
            :options="companyList"
            :props="{ multiple: false }"
            clearable
            filterable
            size="mini"
            collapse-tags
            :filter-method="filterMethod"
            @change="companyChange"
          >
          </el-cascader>
        </div>
        <div class="search-item">
          <span>部门</span>
          <el-select
            class="select"
            v-model.trim="searchInfo.companyDeptCode"
            placeholder="请选择"
            size="mini"
            filterable
            clearable
            :disabled="isHavue"
            @change="search"
          >
            <el-option
              v-for="(item, index) in companyDeptList"
              :key="index"
              :label="item.deptName"
              :value="item.deptCode"
            ></el-option>
          </el-select>
        </div>
        <div class="search-item">
          <span>套餐</span>
          <el-select
            placeholder="请选择"
            size="mini"
            filterable
            clearable
            v-model="searchInfo.clusCode"
            class="input"
            @change="search"
          >
            <el-option
              v-for="item in clusterList"
              :key="item.clusCode"
              :label="item.clusName"
              :value="item.clusCode"
            >
            </el-option>
          </el-select>
        </div>
        <div class="search-item" v-if="C_physicalMode.includes('普检')">
          <span>分类</span>
          <el-select
            style="width: 100px"
            placeholder="请选择"
            size="mini"
            clearable
            v-model="searchInfo.peCls"
            class="input"
            @change="search"
          >
            <el-option
              v-for="item in G_peClsList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <div class="search-item">
          <span>状态</span>
          <el-select
            style="width: 100px"
            placeholder="请选择"
            size="mini"
            clearable
            v-model="searchInfo.peStatus"
            class="input"
            @change="search"
          >
            <el-option
              v-for="item in G_peStatus"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <div class="search-item">
          <span>指引单状态</span>
          <el-select
            @change="search"
            style="width: 100px"
            placeholder="请选择"
            size="mini"
            clearable
            v-model="searchInfo.guidanceStatus"
            class="input"
          >
            <el-option
              v-for="(item, index) in guidancePrint"
              :key="index"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="header-search">
        <div class="search-item">
          <el-radio-group v-model.trim="searchInfo.isActive" @change="search">
            <el-radio :label="null">全部</el-radio>
            <el-radio :label="true">已激活</el-radio>
            <el-radio :label="false">未激活</el-radio>
          </el-radio-group>
        </div>
        <div class="search-select">
          <el-select
            placeholder="请选择"
            size="mini"
            v-model="searchInfo.queryType"
            style="width: 80px; margin-right: 10px"
            @change="search"
          >
            <el-option
              v-for="item in queryTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-date-picker
            :picker-options="{ shortcuts: G_datePickerShortcuts }"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="mini"
            v-model="searchInfo.date"
            @change="search"
            style="width: 260px"
          >
          </el-date-picker>
        </div>
        <div class="search-item">
          <span>自费类型</span>
          <el-select
            @change="search"
            style="width: 100px"
            placeholder="请选择"
            size="mini"
            clearable
            v-model="searchInfo.combChargeMode"
            class="input"
          >
            <el-option
              v-for="(item, index) in settlement"
              :key="index"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <div class="status-wrap">
          <span>记录数：{{ recordStatus.totalCount }}</span>
          <span class="cell_red"
            >未激活数：{{ recordStatus.inActiveCount }}</span
          >
          <span class="cell_yellow"
            >未检查数：{{ recordStatus.unCheckedCount }}</span
          >
          <span class="cell_blue"
            >正在检查数：{{ recordStatus.isCheckingCount }}</span
          >
          <span class="cell_green"
            >已检完数：{{ recordStatus.checkedCount }}</span
          >
          <span class="cell_violet"
            >已审核数：{{ recordStatus.approvedCount }}</span
          >
          <span>已发报告数：{{ recordStatus.issuedReportCount }}</span>
        </div>
      </div>
      <div class="search-btn">
        <span class="cell_blue">已选人数：{{ selection.length }}</span>
        <ButtonGroup
          :btnList="['查询', '详情', '导出']"
          @search="search"
          @detailsClick="detailsClick"
          @exports="exports"
        >
          <template #footAdd>
            <el-button
              @click="activeClick"
              class="blue_btn btn"
              size="mini"
              icon="iconfont icon-jihuo"
              >激活 F1</el-button
            >
            <el-button
              size="mini"
              class="yellow_btn"
              icon="iconfont icon-jihuo"
              @click="cancelAct"
              >取消激活 F2</el-button
            >
            <el-button
              size="mini"
              class="violet_btn"
              icon="iconfont icon-huifuxitongmoren"
              @click="regainRecord"
              >恢复记录</el-button
            >
            <el-button
              size="mini"
              class="red_btn"
              icon="iconfont icon-shanchu"
              @click="delRecord"
              >删除 F4</el-button
            >
            <el-button
              size="mini"
              class="green_btn"
              icon="iconfont icon-preview"
              @click="preview"
              >预览</el-button
            >
            <el-button
              size="mini"
              class="red_btn"
              icon="el-icon-printer"
              @click="print"
              >打印</el-button
            >
          </template>
        </ButtonGroup>
      </div>
    </div>
    <div class="main-wrap">
      <div class="main-table">
        <PublicTable
          ref="main_table_ref"
          :theads="theads"
          :url="$apiUrls.PeComprehensiveQuery"
          :params="params"
          rowKey="regNo"
          remoteByPage
          :elStyle="{
            stripe: false
          }"
          :tableDataMap="
            (data) => {
              return data.comprehensiveData;
            }
          "
          @request-success="requestSuccess"
          @selectionChange="selectionChange"
          @currentChange="rowClick"
          @rowDblclick="rowDblclick"
          :rowClassName="setRowClassName"
          :excelDataMap="excelDataMap"
        >
          <template #bookType="{ scope }">
            <div>
              {{ G_EnumList['BookType'][scope.row.bookType] }}
            </div>
          </template>
          <template #peStatus="{ scope }">
            <el-popover
              placement="top"
              width="200"
              trigger="hover"
              popper-class="step-popover"
              @show="stepShow(scope.row.peStatus)"
            >
              <!-- 状态进程条 -->
              <div class="step">
                <div
                  class="step-item"
                  v-for="(item, index) in stepList"
                  :key="index"
                >
                  <div :class="item.className">
                    <div class="step-title">
                      {{ G_EnumList['PeStatus'][item.peStatus] }}
                    </div>
                    <div class="step-circle-container">
                      <div :class="item.icon"></div>
                    </div>
                    <div class="step-line"></div>
                  </div>
                </div>
              </div>
              <div
                slot="reference"
                :class="peStatusFormat(scope.row.peStatus)"
                style="cursor: pointer"
              >
                {{ G_EnumList['PeStatus'][scope.row.peStatus] }}
              </div>
            </el-popover>
          </template>
          <template #guidancePrinted="{ scope }">
            <div>
              <el-checkbox
                v-model.trim="scope.row.guidancePrinted"
                disabled
              ></el-checkbox>
            </div>
          </template>
          <template #isActive="{ scope }">
            <el-checkbox v-model="scope.row.isActive" disabled></el-checkbox>
          </template>
          <template #isRecheck="{ scope }">
            <el-checkbox v-model="scope.row.isRecheck" disabled></el-checkbox>
          </template>
          <template #isCompanyCheck="{ scope }">
            <el-checkbox
              v-model="scope.row.isCompanyCheck"
              disabled
            ></el-checkbox>
          </template>
          <template #isVIP="{ scope }">
            <el-checkbox v-model="scope.row.isVIP" disabled></el-checkbox>
          </template>
          <template #peCls="{ scope }">
            <div
              style="
                display: flex;
                align-items: center;
                justify-content: flex-end;
              "
            >
              <span>{{ G_EnumList['PeCls'][scope.row.peCls] }}</span>
              <i
                class="el-icon-download"
                @click="downLoadXml(scope.row)"
                v-if="scope.row.peCls == '9'"
                title="生成XML"
                style="font-size: 20px; cursor: pointer; color: #409eff"
              ></i>
            </div>
          </template>
          <template #sex="{ scope }">
            <div>
              {{ G_EnumList['Sex'][scope.row.sex] }}
            </div>
          </template>
          <!-- <template #marryStatus="{ scope }">
            <div>
              {{ G_EnumList["MarryStatus"][scope.row.marryStatus] }}
            </div>
          </template> -->
          <template #reportPrinted="{ scope }">
            <div>
              <el-checkbox
                v-model="scope.row.reportPrinted"
                disabled
              ></el-checkbox>
            </div>
          </template>
          <template #price="{ scope }">
            {{ handlePrice(scope.row.price) }}
          </template>
          <!-- <template #isPaid="{scope}">
            {{ scope.row.isPaid?'已缴费':'未缴费' }}
          </template> -->
        </PublicTable>
      </div>
    </div>
    <el-drawer
      :title="`综合查询—${name}详情`"
      :visible.sync="detailsShow"
      @close="detailsClose"
      :wrapperClosable="false"
      size="90%"
      :modal="false"
    >
      <div class="drawer-main">
        <ul class="tabs">
          <li
            :class="activeIndex === index ? 'active' : ''"
            @click="tabsClick(item, index)"
            v-for="(item, index) in tabsList"
            :key="index"
          >
            {{ item.title }}
          </li>
        </ul>
        <div class="tabs-main">
          <h3>{{ activeTitle }}：</h3>
          <component
            :is="componentName"
            class="tabs-component"
            ref="component_ref"
            :mainInspectionFlag="false"
            :dataQueryFlag="false"
            :selectRow="selectRow"
            :pdfSrc="pdfSrc"
            :isConsultationReplyFlag="false"
            :P_isModify="false"
          ></component>
        </div>
      </div>
    </el-drawer>
    <!-- 打印 -->
    <PrintSelection
      ref="PrintSelection_Ref"
      :defaultCheck="true"
      :displaySwitches.sync="printDisplay"
    />
    <!-- 预览 -->
    <RegisterForThePreview
      ref="RegisterForThePreview_Ref"
      :displaySwitches.sync="previewDisplay"
    />

    <!-- 恢复记录 -->
    <el-drawer
      :title="`${isGroup ? '团体' : '个人'}恢复记录`"
      :visible.sync="recordDrawer"
      :before-close="handleClose"
      :wrapperClosable="false"
      size="70%"
      v-if="recordDrawer"
    >
      <RecoveryRecordDrawer :isGroup="isGroup"></RecoveryRecordDrawer>
    </el-drawer>
  </div>
</template>

<script>
import ButtonGroup from './components/buttonGroup.vue';
import PublicTable from '@/components/publicTable2.vue';
import BasicInfo from './components/basicInfo.vue';
import Items from './components/items.vue';
import ExpenseList from './components/expenseList.vue';
import Specimen from './components/specimen.vue';
import PastRecords from './components/pastRecords.vue';
import AbnormalDanger from './components/abnormalDanger.vue';
import LogQuery from './components/logQuery.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
import ResultEntry from '../inspectIn/resultEntryV2.vue';
import Suggest from './components/suggest';
import Report from './components/report';
import RegisterForThePreview from '@/components/registerForThePreviewV2.vue';
import PrintSelection from '@/components/printSelection.vue';
import shortcut from '@/common/shortcut';
import RecoveryRecordDrawer from '../register/components/recoveryRecordDrawer.vue';
import GraphicAndTextualReport from './components/graphicAndTextualReport.vue';
let __that;
export default {
  name: 'comprehensiveQuery',
  mixins: [shortcut],
  components: {
    ButtonGroup,
    PublicTable,
    BasicInfo,
    Items,
    ExpenseList,
    Specimen,
    PastRecords,
    AbnormalDanger,
    LogQuery,
    ResultEntry,
    Suggest,
    Report,
    RegisterForThePreview,
    PrintSelection,
    RecoveryRecordDrawer,
    GraphicAndTextualReport
  },
  data() {
    return {
      recordDrawer: false,
      isGroup: true,
      shortcutList: {
        112: this.activeClick,
        113: this.cancelAct,
        115: this.delRecord
      },
      previewDisplay: false,
      printDisplay: false,
      isHavue: true,
      pdfSrc: '',
      companyOptions: [],
      companyProps: {
        checkStrictly: true,
        expandTrigger: 'click',
        lazy: true,
        lazyLoad(node, resolve) {
          __that.getCompanyOptions(node, resolve);
        }
      },
      guidancePrint: [
        {
          value: null,
          label: '全部'
        },
        {
          value: true,
          label: '已打印'
        },
        {
          value: false,
          label: '未打印'
        }
      ],
      settlement: [
        {
          value: 1,
          label: '全部自费'
        },
        {
          value: 2,
          label: '部分自费'
        },
        {
          value: 3,
          label: '团体记账'
        }
      ],
      searchInfo: {
        regNo: '',
        patCode: '',
        name: '',
        sex: '',
        cardNo: '',
        hospCode: '',
        peType: 0,
        clusCode: '',
        peCls: '',
        peStatus: '',
        queryType: 1,
        date: [new Date(), new Date()],
        companyList: [],
        companyDeptCode: '',
        isActive: null,
        guidanceStatus: null,
        combChargeMode: '',
        registerTimes: undefined
      },
      radio: '',
      searchTime: '',
      theads: [
        {
          prop: 'bookType',
          label: '预约类型',
          align: '',
          width: '100px',
          sortable: true
        },
        {
          prop: 'peStatus',
          label: '体检状态',
          align: '',
          width: '100px',
          sortable: true
        },
        {
          prop: 'name',
          label: '姓名',
          align: '',
          width: '80px',
          sortable: true,
          showOverflowTooltip: true
        },
        {
          prop: 'regNo',
          label: '体检号',
          align: '',
          width: '130px',
          sortable: true
        },
        {
          prop: 'patCode',
          label: '档案号',
          align: '',
          width: '130px',
          sortable: true
        },
        {
          prop: 'reportPrinted',
          label: '报告',
          align: 'center',
          width: '55px',
          sortable: false
        },
        {
          prop: 'reportPrintedTime',
          label: '报告打印时间',
          align: '',
          width: '160px',
          sortable: true
        },
        {
          prop: 'guidancePrinted',
          label: '指引单',
          align: '',
          width: '65',
          sortable: false
        },
        {
          prop: 'guidancePrintTime',
          label: '指引单打印时间',
          align: '',
          width: '155',
          sortable: true
        },
        {
          prop: 'isActive',
          label: '激活',
          align: '',
          width: '75px',
          sortable: true
        },
        {
          prop: 'activeTime',
          label: '激活时间',
          align: '',
          width: '160px',
          sortable: true
        },
        {
          prop: 'recheckNo',
          label: '复查号',
          align: '',
          width: '130px',
          sortable: true
        },
        {
          prop: 'isRecheck',
          label: '是否复查',
          align: 'center',
          width: '120px',
          sortable: true
        },
        {
          prop: 'registerTimes',
          label: '体检次数',
          align: 'center',
          width: '120px',
          sortable: true
        },
        {
          prop: 'isCompanyCheck',
          label: '团体',
          align: 'center',
          width: '75px',
          sortable: true
        },
        {
          prop: 'isVIP',
          label: 'VIP',
          align: 'center',
          width: '70px',
          sortable: true
        },
        {
          prop: 'peCls',
          label: '体检分类',
          align: 'right',
          width: '120px',
          sortable: true
        },
        {
          prop: 'combChargeMode',
          label: '自费类型',
          align: 'right',
          width: '120px',
          sortable: false
        },
        {
          prop: 'clusterName',
          label: '套餐名称',
          align: '',
          width: '160px',
          sortable: true,
          showOverflowTooltip: true
        },
        {
          prop: 'price',
          label: '金额(元)',
          align: 'right',
          width: '100px',
          sortable: true,
          cellClassName: 'cell_red'
        },
        {
          prop: 'sex',
          label: '性别',
          align: '',
          width: '80px',
          sortable: true
        },
        {
          prop: 'age',
          label: '年龄',
          align: '',
          width: '80px',
          sortable: true
        },
        {
          prop: 'cardNo',
          label: '证件号',
          align: '',
          width: '180px',
          sortable: true
        },
        {
          prop: 'tel',
          label: '电话号码',
          align: '',
          width: '130px',
          sortable: true
        },
        {
          prop: 'companyName',
          label: '工作单位',
          align: '',
          width: '210px',
          sortable: true,
          showOverflowTooltip: true
        },
        {
          prop: 'deptName',
          label: '部门',
          align: '',
          width: '80px',
          sortable: true,
          showOverflowTooltip: true
        },
        {
          prop: 'checkDoctorName',
          label: ' 主检医生',
          align: '',
          width: '100px',
          sortable: true
        },
        {
          prop: 'auditDoctorName',
          label: '审核医生',
          align: '',
          width: '100px',
          sortable: true
        }
      ],
      params: {},
      detailsShow: false,
      tabsList: [
        {
          title: '基本信息',
          component: 'BasicInfo'
        },
        {
          title: '项目',
          component: 'Items'
        },
        {
          title: '费用清单',
          component: 'ExpenseList'
        },
        {
          title: '标本',
          component: 'Specimen'
        },
        {
          title: '结果',
          component: 'ResultEntry'
        },
        {
          title: '综述建议',
          component: 'Suggest'
        },
        {
          title: '报告',
          component: 'Report'
        },
        {
          title: '历史',
          component: 'PastRecords'
        },
        {
          title: '危急异常',
          component: 'AbnormalDanger'
        },
        {
          title: '日志',
          component: 'LogQuery'
        },
        {
          title: '图文报告',
          component: 'GraphicAndTextualReport'
        }
        // {
        //   title: "病理图",
        //   component: "",
        // },
      ],
      activeIndex: 0,
      componentName: 'BasicInfo',
      activeTitle: '基本信息',
      companyList: [],
      companyDeptList: [],
      clusterList: [],
      fixed_clusterList: [],
      queryTypeList: [
        {
          value: 1,
          label: '登记'
        },
        {
          value: 2,
          label: '体检'
        },
        {
          value: 3,
          label: '预约'
        }
      ],
      recordStatus: {
        totalCount: 0,
        inActiveCount: 0,
        unCheckedCount: 0,
        isCheckingCount: 0,
        checkedCount: 0,
        approvedCount: 0,
        issuedReportCount: 0
      },
      selection: [],
      selectRow: {},
      name: '',
      stepList: [],
      excelData: []
    };
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_userInfo',
      'G_sexList',
      'G_peClsList',
      'G_peStatus',
      'G_userInfo',
      'G_datePickerShortcuts',
      'G_config'
    ]),
    C_physicalMode() {
      return this.G_config?.physicalMode;
    }
  },
  created() {
    __that = this;
    this.getCompanyList();
    this.getClusterList();
  },
  methods: {
    // 处理价格
    handlePrice(price) {
      return dataUtils.handlePrice(price);
    },
    // 下载Xml
    downLoadXml(row) {
      let datas = {
        regNo: row.regNo
      };

      this.$ajax
        .paramsPost(this.$apiUrls.GenerateHealthCardXML, datas)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;

          var filename = row.name + '.xml';
          var pom = document.createElement('a');
          var bb = new Blob([returnData], { type: 'text/plain' });

          pom.setAttribute('href', window.URL.createObjectURL(bb));
          pom.setAttribute('download', filename);

          pom.dataset.downloadurl = ['text/plain', pom.download, pom.href].join(
            ':'
          );
          pom.draggable = true;
          pom.classList.add('dragout');
          pom.click();
        });
    },
    // 标签页点击
    tabsClick(item, index) {
      this.activeIndex = index;
      this.componentName = item.component;
      this.activeTitle = item.title;
      this.$nextTick(() => {
        if (item.component == 'ResultEntry') {
          this.$refs.component_ref.headerInfo = this.selectRow;
          this.$refs.component_ref.getNavList();
        }
        if (item.component == 'report') {
          this.getPDF(this.selectRow);
        }
      });
    },
    //获取单位下拉
    getCompanyList() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        //console.log('🚀 ~ this.$ajax.post ~ r:', r);

        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    //单位下拉变化
    companyChange(data) {
      this.getDepartList(data);
      if (!data || data.length === 0) {
        this.isHavue = true;
        this.companyTimesChange();
        this.searchInfo.date = [dataUtils.getDate(), dataUtils.getDate()];
        this.search();
        return;
      }
      this.companyTimesChange();
      this.getClusterList();
      this.isHavue = false;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.searchInfo.date = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.search();
    },
    //次数改变时
    companyTimesChange() {
      this.searchInfo.clusCode = '';
      if (!this.searchInfo.companyList[1]) {
        this.clusterList = this.fixed_clusterList;
        return;
      }
      this.$ajax
        .post(this.$apiUrls.ReadCompanyCandidateCluster, '', {
          query: {
            companyCode: this.searchInfo.companyList[0],
            companyTimes: this.searchInfo.companyList[1]
          }
        })
        .then(async (r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.clusterList = returnData || [];
        });
    },
    //查询公司部门信息
    getDepartList(companyCode) {
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanyDepartment, {
          companyCode: companyCode?.[0] || '',
          deptCode: '',
          deptName: ''
        })
        .then((r) => {
          //console.log("r", r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.companyDeptList = returnData || [];
        });
    },
    /**
     * @author: justin
     * @description: 获取公司联级列表
     * @param {*} node  节点数据
     * @param {*} resolve 回调函数
     * @param {*} keyword 关键字
     * @return {*}
     */
    async getCompanyOptions(node, resolve, keyword) {
      // node 节点数据 node.value => 当前节点的值
      // level: 层级
      const { level, value } = node;
      let nodes = [];
      switch (level) {
        case 0:
          await this.getCompanyList();
          nodes = this.companyList.map((item) => ({
            value: item.companyCode,
            label: item.companyName,
            leaf: false
          }));
          break;

        case 1:
          await this.$ajax
            .post(this.$apiUrls.R_CodeCompanyDepartment, {
              companyCode: value
            })
            .then((r) => {
              let { success, returnData } = r.data;
              if (!success) return;

              nodes = returnData.map((item) => ({
                value: item.deptCode,
                label: item.deptName,
                leaf: true
              }));
            });
          break;
      }

      resolve(nodes);
    },

    // 获取套餐下拉
    getClusterList() {
      this.$ajax.post(this.$apiUrls.Cluster).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.clusterList = returnData;
        this.fixed_clusterList = returnData;
      });
    },
    // 详情点击
    detailsClick() {
      if (!this.selectRow?.regNo) {
        this.$message({
          message: '请选择体检人！',
          type: 'warning',
          showClose: true
        });
      } else {
        if (this.selection.length > 1) {
          this.$message({
            message: '只能选择一位体检人查看详情！',
            type: 'warning',
            showClose: true
          });
          return;
        }
        this.detailsShow = true;
        this.name = this.selectRow.name;
        this.$nextTick(() => {
          this.$refs.component_ref.GetPatientBasicInfo();
        });
      }
    },

    /**
     * @author: justin
     * @description: 查询
     * @return {*}
     */
    search() {
      let startTime = '';
      let endTime = '';
      if (this.searchInfo.date) {
        [startTime, endTime] = this.searchInfo.date;
      }
      this.params = {
        regNo: this.searchInfo.regNo,
        patCode: this.searchInfo.patCode,
        name: this.searchInfo.name,
        sex: this.searchInfo.sex !== '' ? this.searchInfo.sex : -1,
        cardNo: this.searchInfo.cardNo,
        hospCode: this.searchInfo.hospCode,
        peType: this.searchInfo.peType,
        clusCode: this.searchInfo.clusCode,
        peCls: this.searchInfo.peCls !== '' ? this.searchInfo.peCls : -1,
        peStatus:
          this.searchInfo.peStatus !== '' ? this.searchInfo.peStatus : -1,
        queryType: this.searchInfo.queryType,
        startTime: dataUtils.dateToStrStart(startTime),
        endTime: dataUtils.dateToStrEnd(endTime),
        isActive: this.searchInfo.isActive,
        guidanceStatus: this.searchInfo.guidanceStatus,
        combChargeMode: this.searchInfo.combChargeMode || 0,
        registerTimes:
          this.searchInfo.registerTimes == ('' || undefined)
            ? null
            : this.searchInfo.registerTimes,
        companyCode:
          this.searchInfo.companyList.length >= 1
            ? this.searchInfo.companyList[0]
            : undefined,
        companyTimes:
          this.searchInfo.companyList.length >= 1
            ? this.searchInfo.companyList[1]
            : -1,
        companyDeptCode:
          this.searchInfo.companyList?.length >= 1
            ? this.searchInfo.companyDeptCode
            : undefined
      };
      this.$refs.main_table_ref.loadData();
      this.searchInfo.regNo = '';
    },

    /**
     * @author: justin
     * @description: 表格数据响应成功回调处理
     * @param {*} data
     * @return {*}
     */
    requestSuccess(data) {
      if (!data) return;

      this.recordStatus = data.returnData.recordStatus || {};
      this.recordStatus.totalCount = data.totalNumber || 0;
    },

    // 状态进程条
    stepShow(val) {
      this.stepList = [];
      let stepOne = {};
      let stepTwo = {};
      let stepThree = {};
      if (val === this.G_peStatus.length - 1) {
        stepOne.peStatus = val - 2;
        stepOne.className = 'step-finish';
        stepOne.icon = 'step-circle';

        stepTwo.peStatus = val - 1;
        stepTwo.className = 'step-finish';
        stepTwo.icon = 'step-circle';

        stepThree.peStatus = val;
        stepThree.className = 'step-active';
        stepThree.icon = 'iconfont icon-zhifuchenggong icon';
      } else if (val === 0) {
        stepOne.peStatus = val;
        stepOne.className = 'step-active';
        stepOne.icon = 'iconfont icon-zhifuchenggong icon';

        stepTwo.peStatus = val + 1;
        stepTwo.className = 'step-default';
        stepTwo.icon = 'step-circle';

        stepThree.peStatus = val + 2;
        stepThree.className = 'step-default';
        stepThree.icon = 'step-circle';
      } else {
        stepOne.peStatus = val - 1;
        stepOne.className = 'step-finish';
        stepOne.icon = 'step-circle';

        stepTwo.peStatus = val;
        stepTwo.className = 'step-active';
        stepTwo.icon = 'iconfont icon-zhifuchenggong icon';

        stepThree.peStatus = val + 1;
        stepThree.className = 'step-default';
        stepThree.icon = 'step-circle';
      }
      this.stepList.push(stepOne, stepTwo, stepThree);
    },
    // 单选框切换
    radioChange() {
      if (this.searchInfo.peType === 1) {
        this.searchInfo.companyCode = '';
      }
      this.search();
    },
    // 表格双击
    rowDblclick(row) {
      this.detailsShow = true;
      this.name = this.selectRow.name;
      this.$nextTick(() => {
        this.$refs.component_ref.GetPatientBasicInfo();
      });
    },
    // 表格勾选
    selectionChange(rows) {
      if (!rows) return;
      this.selection = rows;
      this.selectRow = rows[0];
      this.excelData = rows;
    },
    // 表格点击
    rowClick(row) {
      if (!row) return;

      this.selectRow = row;
    },
    // 关闭详情
    detailsClose() {
      this.detailsShow = false;
      this.componentName = 'BasicInfo';
      this.activeIndex = 0;
    },
    // 体检状态格式化
    peStatusFormat(key) {
      let className = '';
      switch (key) {
        case 0:
          className = 'cell_yellow';
          break;
        case 1:
          className = 'cell_blue';
          break;
        case 2:
          className = 'cell_green';
          break;
        case 3:
          className = '';
          break;
        case 4:
          className = 'cell_violet';
          break;
      }
      return className;
    },
    /**
     * @author: justin
     * @description: 设置行颜色
     * @param {*} row
     * @return {*}
     */
    setRowClassName(row) {
      // 打印加背景颜色
      if (row.reportPrinted) return 'row-printed';
      else return '';
    },
    // 激活
    activeClick() {
      console.log(this.selectRow);
      if (this.selection.length === 0 && !this.selectRow?.regNo) {
        this.$message({
          message: '请选择要激活的数据！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let arr;
      if (this.selection.length === 0) {
        arr = [this.selectRow.regNo];
      } else {
        arr = this.selection.map((item) => {
          return item.regNo;
        });
      }

      let data = {
        regNoArray: arr, //体检号数组
        activeTime: new Date(),
        activator: this.G_userInfo.codeOper.operatorCode
      };
      this.$ajax
        .post(this.$apiUrls.ActivationOrCancel + '/Active', data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$message({
            showClose: true,
            message: '激活成功!',
            type: 'success'
          });
          this.search();
        });
    },
    //取消激活
    cancelAct() {
      if (this.selection.length < 1 && !this.selectRow?.regNo) {
        this.$message({
          message: '请先选择要取消激活的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let arr;
      if (this.selection.length === 0) {
        arr = [this.selectRow.regNo];
      } else {
        arr = this.selection.map((item) => {
          return item.regNo;
        });
      }
      let data = {
        regNoArray: arr, //体检号数组
        activeTime: new Date(),
        activator: this.G_userInfo.codeOper.operatorCode
      };
      this.$ajax
        .post(this.$apiUrls.ActivationOrCancel + '/Deactive', data)
        .then((r) => {
          if (!r.data.success) {
            return;
          }
          this.search();
        });
    },
    // 获取报告
    getPDF(row) {
      this.pdfSrc = '';
      let datas = {
        reportCode: row.guidanceType,
        queryString: 'regNo=' + row.regNo
      };

      this.$ajax
        .post(`${this.$config.pdfFileUrl}Home/ExportToPdf`, datas, {
          responseType: 'arraybuffer'
        })
        .then((r) => {
          const blob = new Blob([r.data], { type: 'application/pdf' });
          let reader = new FileReader();
          reader.onload = (e) => {
            let data;
            if (typeof e.target.result === 'object') {
              data = window.URL.createObjectURL(e.target.result);
            } else {
              data = e.target.result;
            }
            this.pdfSrc = data + '#toolbar=0';
            this.fileStream = data;
          };
          //转化为base64
          reader.readAsDataURL(blob);
        });
    },

    /**
     * @author: justin
     * @description: 导出excel
     * @return {*}
     */
    exports() {
      this.$refs.main_table_ref.exportToExcel({ fileName: '综合查询' });
    },

    /**
     * @author: justin
     * @description: 导出excel数据处理
     * @param {*} data
     * @return {*}
     */
    excelDataMap(data) {
      if (!data) return [];

      data.forEach((item, i) => {
        item.bookType = this.G_EnumList['BookType'][item.bookType]; //预约类型
        item.sex = this.G_EnumList['Sex'][item.sex]; //性别
        item.peStatus = this.G_EnumList['PeStatus'][item.peStatus]; //状态
        item.reportPrinted = item.reportPrinted ? '是' : '否'; //报告是否打印
        item.marryStatus = this.G_EnumList['MarryStatus'][item.marryStatus]; //婚姻状况
        item.isActive = item.isActive ? '是' : '否'; //激活标志
        item.isCompanyCheck = item.isCompanyCheck ? '是' : '否'; //团体标识
        item.isVIP = item.isVIP ? '是' : '否'; //VIP
        item.peCls = this.G_EnumList['PeCls'][item.peCls]; // 体检分类
      });
      return data;
    },

    /**
     * @description: 预览
     * @return {*}
     * @author: key
     */
    preview() {
      if (!this.selectRow) {
        return this.$message.warning('请选择体检信息');
      }
      this.previewDisplay = true;
      this.$refs.RegisterForThePreview_Ref.setPrintTypeList(
        this.selectRow.regNo
      );
    },
    /**
     * @description: 打印
     * @return {*}
     * @author: key
     */
    print() {
      if (!this.excelData?.length >= 1 && !this.selectRow?.regNo) {
        this.$message({
          message: '请先选择需要打印的体检信息！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let printData;
      if (this.selection.length === 0) {
        printData = [
          {
            regNo: this.selectRow.regNo,
            isActive: this.selectRow.isActive
          }
        ];
      } else {
        printData = this.selection.map((item) => {
          return {
            regNo: item.regNo,
            isActive: item.isActive
          };
        });
      }

      this.printDisplay = true;

      this.$refs.PrintSelection_Ref.setPrintTypeList(printData);
    },
    //删除记录
    delRecord() {
      if (this.selection?.length > 0 || this.selectRow?.regNo) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            let regNoList;
            if (this.selection?.length > 0) {
              regNoList = this.selection.map((item) => item.regNo);
            } else {
              regNoList = [this.selectRow.regNo];
            }
            this.$ajax
              .post(this.$apiUrls.RecycleRegisterOrder + '/company', regNoList)
              .then((r) => {
                let { success } = r.data;
                if (!success) return;
                this.$message({
                  showClose: true,
                  message: '删除成功',
                  type: 'success'
                });
                this.search();
              });
          })
          .catch(() => {
            return;
          });
      } else {
        return this.$message({
          message: '请选择至少一条数据再进行操作！',
          type: 'warning',
          showClose: true
        });
      }
    },
    //恢复记录
    regainRecord() {
      if (this.searchInfo.peType == 0) {
        return this.$message({
          message: '请选择团体还是个人',
          type: 'warning',
          showClose: true
        });
      }
      console.log(this.searchInfo.peType);
      this.isGroup = !(this.searchInfo.peType === 1);
      this.recordDrawer = true;
    },
    //恢复记录关闭弹窗
    handleClose() {
      this.recordDrawer = false;
      this.search();
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    }
  }
};
</script>

<style lang="less" scoped>
/deep/.el-radio-group {
  margin-right: 20px;
  label {
    margin-right: 15px !important;
  }
}
.comprehensiveQuery {
  // min-width: fit-content;
  color: #2d3436;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  overflow: auto;
  .header-wrap {
    background: #fff;
    padding: 18px 18px 0 18px;
  }
  .header-search {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .search-item {
    // width: 280px;
    display: flex;
    align-items: center;
    margin-right: 10px;
    &:last-child {
      margin-right: 0;
    }
    span {
      // width: 40px;
      min-width: fit-content;
      font-weight: 600;
      font-size: 14px;
      margin-right: 5px;
      min-width: fit-content;
    }
  }
  .item-input {
    display: flex;
    align-items: center;
    margin-right: 10px;
    &:last-child {
      margin-right: 0;
    }
  }
  .input {
    width: 100%;
  }
  .search-select {
    display: flex;
    margin-right: 20px;
  }
  .status-wrap {
    flex: 1;
    font-weight: 600;
    font-size: 14px;
    span {
      margin-right: 22px;
      &:last-child {
        margin-right: 0;
      }
    }
  }
  .main-wrap {
    background: #fff;
    padding: 0 18px 18px 18px;
    flex: 1;
    overflow: auto;
  }
  .search-btn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    span {
      font-weight: 600;
      font-size: 14px;
    }
  }
  .main-table {
    height: 100%;
    border: 1px solid rgba(178, 190, 195, 0.5);
    border-radius: 4px;
    overflow: auto;

    /deep/.row-printed {
      background: rgba(38, 126, 235, 0.5) !important;
    }
  }
  /deep/.el-drawer__header {
    align-items: center;
    display: flex;
    background: rgba(23, 112, 223, 0.2);
    line-height: 42px;
    font-size: 18px;
    padding: 0;
    padding-left: 20px;
    color: #2d3436;
    margin-bottom: 0;
    font-weight: 600;
  }
  /deep/.el-drawer__close-btn {
    font-size: 30px;
  }
  .drawer-main {
    height: 100%;
    padding: 5px;
    display: flex;
  }
  .tabs {
    width: 140px;
    background: rgba(178, 190, 195, 0.1);
    border-radius: 4px;
    margin-right: 5px;
    padding: 5px;
    font-weight: 600;
    overflow: auto;
    li {
      color: #1770df;
      border: 1px solid #1770df;
      border-radius: 4px;
      text-align: center;
      height: 38px;
      line-height: 38px;
      margin-bottom: 30px;
      background: #fff;
      cursor: pointer;
      &:last-child {
        margin-bottom: 0;
      }
      &.active {
        background: #1770df;
        color: #fff;
      }
    }
  }
  .tabs-main {
    flex: 1;
    overflow: auto;
    h3 {
      font-size: 18px;
      margin-bottom: 5px;
    }
  }
  .tabs-component {
    height: calc(100% - 48px);
  }
  /deep/.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    background-color: #409eff;
    border-color: #409eff;
  }
  /deep/.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
    border-color: #fff;
  }
}
.step-popover {
  .step {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    position: relative;
  }
  .step-item {
    flex: 1;
    .step-default {
      color: #969799;
      position: relative;
    }
    .step-active {
      color: #1770df;
      position: relative;
      .icon {
        font-size: 14px;
      }
    }
    .step-finish {
      color: #1770df;
      position: relative;
      .step-circle {
        background: #1770df;
      }
      .step-line {
        background: #1770df;
      }
    }
    .step-title {
      margin-bottom: 6px;
      margin-left: 14px;
      transform: translateX(-50%);
    }
    .step-circle-container {
      background: #fff;
      position: absolute;
      top: 30px;
      left: -14px;
      z-index: 1;
      padding: 0 8px;
      background-color: #fff;
      transform: translateY(-50%);
    }
    .step-circle {
      width: 10px;
      height: 10px;
      background: #969799;
      border-radius: 50%;
    }
    .step-line {
      position: absolute;
      top: 30px;
      left: 0;
      width: 100%;
      height: 1px;
      background: #ebedf0;
      transition: background-color 0.3s;
    }
    &:first-child {
      .step-title {
        margin-left: 0;
        transform: none;
      }
      .step-circle-container {
        right: auto;
        left: -9px;
      }
    }
    &:last-child {
      position: absolute;
      right: 1px;
      width: auto;
      .step-title {
        margin-left: 0;
        transform: none;
      }
      .step-circle-container {
        right: -9px;
        left: auto;
      }
      .step-line {
        width: 0;
      }
    }
  }
}
</style>
