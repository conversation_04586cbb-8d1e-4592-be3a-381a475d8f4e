<template>
  <div class="suggest_page">
    <div class="content_wrap">
      <ul>
        <li v-for="(item, index) in summaryList" :key="index">
          <span>{{ index + 1 }}、</span>
          <div>
            <label>{{ item.title }}</label>
            <p>{{ item.value }}</p>
          </div>
        </li>
      </ul>
      <ul>
        <li v-for="(item, index) in suggestList" :key="index">
          <span>{{ index + 1 }}、</span>
          <div>
            <label>{{ item.title }}</label>
            <p>{{ item.value }}</p>
          </div>
        </li>
      </ul>
    </div>
    <div class="info_wrap">
      <div>
        <label>主检医师：{{ suggestInfo.checkDoctorName }}</label>
        <span></span>
      </div>
      <div>
        <label>主检日期：{{ suggestInfo.checkTime }}</label>
        <span></span>
      </div>
      <div>
        <label>审核医师：{{ suggestInfo.auditDoctorName }}</label>
        <span></span>
      </div>
      <div>
        <label>审核日期：{{ suggestInfo.auditTime }}</label>
        <span></span>
      </div>
    </div>
  </div>
</template>

<script>
import { title } from 'process';

export default {
  name: 'suggest',
  props: {
    selectRow: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      summaryList: [],
      suggestList: [],
      suggestInfo: {
        checkDoctorName: '',
        checkTime: '',
        auditDoctorName: '',
        auditTime: ''
      }
    };
  },
  methods: {
    //获取综述和建议
    getSuggest() {
      this.$ajax
        .post(this.$apiUrls.GetReportConclusion, [], {
          query: { regNo: this.selectRow.regNo, checkAudit: 0 }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          let summaryList = [];
          let suggestList = [];
          returnData.combSummarys.map((item) => {
            let content = '';
            item.summTags.map((twoItem, twoIdx) => {
              content += twoItem.tag + '；';
            });
            summaryList.push({
              title: item.combName,
              value: content
            });
          });

          returnData.suggestions?.map((item) => {
            let content = '';
            item.deaTags.map((twoItem, twoIdx) => {
              content += twoItem.diseaseName + '；';
            });
            suggestList.push({
              title: content,
              value: item.sugContent
            });
          });

          this.summaryList = summaryList;
          this.suggestList = suggestList;
          this.suggestInfo.checkDoctorName = returnData.checkDoctorName;
          this.suggestInfo.checkTime = returnData.checkTime;
          this.suggestInfo.auditDoctorName = returnData.auditDoctorName;
          this.suggestInfo.auditTime = returnData.auditTime;
        });
    }
  },
  created() {
    this.getSuggest();
  }
};
</script>

<style lang="less" scoped>
.suggest_page {
  display: flex;
  flex-direction: column;

  .content_wrap {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    display: flex;

    ul {
      border: 1px solid #b2bec3;
      border-radius: 4px;
      overflow: auto;
      flex: 1;
      flex-shrink: 0;
      padding: 10px;

      &:nth-child(2) {
        margin-left: 10px;
      }
    }

    li {
      margin-bottom: 10px;
      display: flex;

      div {
        flex: 1;
        flex-shrink: 0;
      }

      label {
        color: #1770df;
        margin-bottom: 4px;
      }
    }
  }
  .info_wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
  }
}
</style>
