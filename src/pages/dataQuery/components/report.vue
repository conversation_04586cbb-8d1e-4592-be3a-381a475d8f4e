<template>
  <div v-loading="loading">
    <embed
      v-if="pdfSrc"
      type="application/pdf"
      width="100%"
      :src="pdfSrc"
      height="100%"
    />
    <el-empty v-if="!pdfSrc" description="暂无报告"></el-empty>
  </div>
</template>

<script>
export default {
  props: {},
  data() {
    return {
      pdfSrc: '',
      loading: false
    };
  },
  methods: {
    async retrieveFileStream() {
      try {
        this.loading = true;
        const res = await this.$ajax.get(
          `/PrintFile/ReportPdf/${this.$parent.$parent.selectRow.regNo}`,
          { responseType: 'arraybuffer' }
        );
        this.previewBuffer = res.data;
        const blob = new Blob([res.data], { type: 'application/pdf' });
        const pdfUrl = URL.createObjectURL(blob);
        this.pdfSrc = pdfUrl;
      } finally {
        this.loading = false;
      }
    }
  },
  created() {
    if (this.$parent.$parent.selectRow.isActive) this.retrieveFileStream();
  },
  destroyed() {
    this.pdfSrc = '';
  }
};
</script>

<style></style>
