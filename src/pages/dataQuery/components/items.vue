<template>
  <!-- 项目 -->
  <div class="items">
    <div class="items-wrap">
      <div class="wrap-title">套餐分组：</div>
      <div class="wrap-box classify">
        <span class="cell_blue">{{ itemsDate.clusName }}</span>
      </div>
    </div>
    <div class="items-wrap">
      <div class="wrap-title">项目分类：</div>
      <div class="wrap-box classify">
        <div v-for="(cls, index) in itemsDate.itemCls" :key="index">
          {{ cls.clsCode + '.' }}
          <span class="cell_blue">{{ cls.clsName }}</span>
        </div>
      </div>
    </div>
    <div class="items-wrap">
      <div class="wrap-title">加项：</div>
      <div class="wrap-box classify">
        <div v-for="(add, index) in itemsDate.addItem" :key="index">
          {{ add.combCode + '.' }}
          <span class="cell_blue">{{ add.combName }}</span>
        </div>
      </div>
    </div>
    <div class="items-wrap">
      <div class="wrap-title">未检：</div>
      <div class="wrap-box classify">
        <div v-for="(noCheck, index) in itemsDate.noCheckItems" :key="index">
          {{ noCheck.combCode + '.' }}
          <span class="cell_blue">{{ noCheck.combName }}</span>
        </div>
      </div>
    </div>
    <div class="items-wrap">
      <div class="wrap-title">拒检：</div>
      <div class="wrap-box classify">
        <div v-for="(abandon, index) in itemsDate.abandonItems" :key="index">
          {{ abandon.combCode + '.' }}
          <span class="cell_blue">{{ abandon.combName }}</span>
        </div>
      </div>
    </div>
    <div class="items-wrap">
      <div class="wrap-title">单位支付：</div>
      <div class="wrap-box classify">
        <div
          v-for="(companyPay, index) in itemsDate.companyPayItems"
          :key="index"
        >
          {{ companyPay.combCode + '.' }}
          <span class="cell_blue">{{ companyPay.combName }}</span>
        </div>
      </div>
    </div>
    <div class="items-wrap">
      <div class="wrap-title">预约平台支付：</div>
      <div class="wrap-box classify">
        <div
          v-for="(platformPay, index) in itemsDate.platformPayItems"
          :key="index"
        >
          {{ platformPay.combCode + '.' }}
          <span class="cell_blue">{{ platformPay.combName }}</span>
        </div>
      </div>
    </div>
    <div class="items-wrap">
      <div class="wrap-title">个人支付：</div>
      <div class="wrap-box classify">
        <div v-for="(selfPay, index) in itemsDate.selfPayItems" :key="index">
          {{ selfPay.combCode + '.' }}
          <span class="cell_blue">{{ selfPay.combName }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'items',
  data() {
    return {
      itemsDate: {
        clusName: '',
        itemCls: [
          {
            clsCode: '',
            clsName: ''
          }
        ],
        addItem: [
          {
            combCode: '',
            combName: ''
          }
        ],
        noCheckItems: [
          {
            combCode: '',
            combName: ''
          }
        ],
        abandonItems: [
          {
            combCode: '',
            combName: ''
          }
        ],
        companyPayItems: [
          {
            combCode: '',
            combName: ''
          }
        ],
        platformPayItems: [
          {
            combCode: '',
            combName: ''
          }
        ],
        selfPayItems: [
          {
            combCode: '',
            combName: ''
          }
        ]
      }
    };
  },
  created() {
    this.GetItemsInfo();
  },
  methods: {
    GetItemsInfo() {
      this.$ajax
        .post(this.$apiUrls.GetItemsInfo, '', {
          query: { regNo: this.$parent.$parent.selectRow.regNo }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.itemsDate = returnData || {};
        });
    }
  }
};
</script>

<style lang="less" scoped>
.items {
  padding: 18px;
  background: rgba(178, 190, 195, 0.1);
  border: 1px solid rgba(178, 190, 195, 0.5);
  border-radius: 4px;
  overflow: auto;
  .items-wrap {
    margin-bottom: 18px;
    font-size: 14px;
    font-weight: 600;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .wrap-title {
    margin-bottom: 10px;
  }
  .wrap-box {
    padding: 18px;
    background: #fff;
    border-radius: 4px;
  }
  .classify {
    display: flex;
    flex-wrap: wrap;
    padding: 18px 18px 8px 18px;
    div {
      width: 166px;
      margin-right: 38px;
      margin-bottom: 10px;
    }
  }
}
</style>
