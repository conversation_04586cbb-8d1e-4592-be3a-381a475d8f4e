<template>
  <div class="abnormalDanger">
    <PublicTable
      :isSortShow="false"
      :viewTableList.sync="tableData"
      :theads.sync="theads"
      :columnWidth="columnWidth"
      :cell_red="[
        'itemName',
        'itemResult',
        'createTime',
        'creator',
        'confirmTime',
        'confirmer',
        'transportTime',
        'replyPerson',
        'replyContent'
      ]"
    >
      <template #creator="{ scope }">
        <div>
          {{ G_EnumList['SysOperator'][scope.row.creator] }}
        </div>
      </template>
      <template #confirmer="{ scope }">
        <div>
          {{ G_EnumList['SysOperator'][scope.row.confirmer] }}
        </div>
      </template>
      <template #replyPerson="{ scope }">
        <div>
          {{ G_EnumList['SysOperator'][scope.row.replyPerson] }}
        </div>
      </template>
    </PublicTable>
  </div>
</template>

<script>
import PublicTable from '@/components/publicTable.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
export default {
  name: 'abnormalDanger',
  components: {
    PublicTable
  },
  data() {
    return {
      tableData: [],
      theads: {
        itemName: '项目',
        itemResult: '项目结果',
        creator: '生成人',
        createTime: '生成时间',
        confirmer: '确认人',
        confirmTime: '确认时间',
        replyPerson: '回复人',
        replyTime: '回复时间',
        replyContent: '回复内容'
      },
      columnWidth: {
        itemName: 130,
        itemResult: 170,
        createTime: 170,
        confirmTime: 170,
        replyTime: 170,
        replyContent: 300
      }
    };
  },
  created() {
    this.GetSampleData();
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_userInfo'])
  },
  methods: {
    GetSampleData() {
      this.$ajax
        .post(this.$apiUrls.GetCriticalException, '', {
          query: { regNo: this.$parent.$parent.selectRow.regNo }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.tableData = returnData || [];
        });
    }
  }
};
</script>

<style lang="less" scoped>
.abnormalDanger {
  border: 1px solid rgba(178, 190, 195, 0.5);
  border-radius: 4px;
}
</style>
