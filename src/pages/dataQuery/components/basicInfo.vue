<template>
  <!-- 基本信息 -->
  <div class="basicInfo">
    <div class="info-wrap">
      <div class="info-img">
        <el-image class="img" :src="form.photoUrl"></el-image>
      </div>
      <div class="info-form">
        <el-form ref="form" :model="form" label-width="82px">
          <!-- 第1行 -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="体检号">
                <el-input v-model="form.regNo" size="small" readonly></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="档案号">
                <el-input
                  v-model="form.patCode"
                  size="small"
                  readonly
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="体检次数">
                <el-input
                  v-model="form.registerTimes"
                  size="small"
                  readonly
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 第2行 -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="姓名">
                <el-input v-model="form.name" size="small" readonly></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="性别">
                <el-input
                  v-model="G_EnumList['Sex'][form.sex]"
                  size="small"
                  readonly
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="体检分类">
                <el-input
                  v-model="G_EnumList['PeCls'][form.peCls]"
                  size="small"
                  readonly
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 第3行 -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="出生日期">
                <el-input
                  v-model="form.birthday"
                  size="small"
                  readonly
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="年龄">
                <el-input v-model="form.age" size="small" readonly></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="籍贯">
                <el-input
                  v-model="G_EnumList['CodeNativePlace'][form.nativePlace]"
                  size="small"
                  readonly
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 第4行 -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="证件类型">
                <el-input
                  v-model="G_EnumList['CardType'][form.cardType]"
                  size="small"
                  readonly
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="证件号">
                <el-input
                  v-model="form.cardNo"
                  size="small"
                  readonly
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="婚姻状况">
                <el-input
                  v-model="G_EnumList['MarryStatus'][form.marryStatus]"
                  size="small"
                  readonly
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 第5行 -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="联系电话">
                <el-input v-model="form.tel" size="small" readonly></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="职业">
                <el-input
                  v-model="form.jobName"
                  size="small"
                  readonly
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="职业史">
                <el-input
                  v-model="form.jobHistory"
                  size="small"
                  readonly
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 第6行 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="地址">
                <el-input
                  v-model="form.address"
                  size="small"
                  readonly
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 第7行 -->
          <el-row :gutter="20">
            <el-col :span="10">
              <el-form-item label="既往病史">
                <el-input
                  v-model="form.medicalHistory"
                  size="small"
                  readonly
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="14">
              <el-form-item label="工作单位">
                <el-input
                  v-model="form.companyName"
                  size="small"
                  readonly
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 第8行 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="备注">
                <el-input v-model="form.note" size="small" readonly></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 第9行 -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="部门">
                <el-input
                  v-model="form.deptName"
                  size="small"
                  readonly
                ></el-input>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="8">
              <el-form-item label="工号">
                <el-input v-model="form.note" size="small" readonly></el-input>
              </el-form-item>
            </el-col> -->
            <el-col :span="8">
              <el-form-item label="复查号">
                <el-input
                  v-model="form.recheckNo"
                  size="small"
                  readonly
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="团体联系人">
                <el-input
                  v-model="form.companyContact"
                  size="small"
                  readonly
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 第10行 -->
          <!-- <el-row :gutter="20"> -->
          <!-- <el-col :span="8">
              <el-form-item label="岗位状态">
                <el-input v-model="form.note" size="small" readonly></el-input>
              </el-form-item>
            </el-col> -->

          <!-- </el-row> -->
          <!-- 第11行 -->
          <el-row :gutter="20">
            <!-- <el-col :span="8">
              <el-form-item label="开单医生">
                <el-input  v-model="G_EnumList['SysOperator'][form.operatorCode]" size="small" readonly></el-input>
              </el-form-item>
            </el-col> -->
            <el-col :span="8">
              <el-form-item label="介绍人">
                <el-input
                  v-model="form.introducer"
                  size="small"
                  readonly
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="操作员">
                <el-input
                  v-model="G_EnumList['SysOperator'][form.operatorCode]"
                  size="small"
                  readonly
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="体检时间">
                <el-input
                  v-model="form.activeTime"
                  size="small"
                  readonly
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 第12行 -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="体检费">
                <el-input
                  v-model="form.pePrice"
                  size="small"
                  readonly
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="加项费">
                <el-input
                  v-model="form.addItemPrice"
                  size="small"
                  readonly
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="登记时间">
                <el-input
                  v-model="form.registerTime"
                  size="small"
                  readonly
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 第13行 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="发票名称">
                <el-input
                  v-model="form.invoiceNo"
                  size="small"
                  readonly
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="">
                <el-checkbox v-model="form.isLeader" disabled>领导</el-checkbox>
                <el-checkbox v-model="form.isRecheck" disabled
                  >复查</el-checkbox
                >
                <el-checkbox v-model="form.isVIP" disabled>VIP会员</el-checkbox>
                <el-checkbox v-model="form.isConstitution" disabled
                  >体质辨识</el-checkbox
                >
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
export default {
  name: 'basicInfo',
  data() {
    return {
      form: {
        photoUrl: '',
        regNo: '',
        patCode: '',
        companyTimes: '',
        name: '',
        sex: '',
        peCls: '',
        birthday: '',
        age: '',
        nativePlace: '',
        cardType: '',
        cardNo: '',
        marryStatus: '',
        tel: '',
        jobName: '',
        jobHistory: '',
        address: '',
        medicalHistory: '',
        note: '',
        companyName: '',
        deptName: '',
        companyContact: '',
        introducer: '',
        operatorCode: '',
        recheckNo: '',
        pePrice: '',
        addItemPrice: '',
        registerTime: '',
        invoiceNo: '',
        activeTime: '',
        isLeader: false,
        isRecheck: false,
        isVIP: false,
        isConstitution: false
      }
    };
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_userInfo'])
  },
  created() {
    this.GetPatientBasicInfo();
  },
  methods: {
    GetPatientBasicInfo() {
      this.$ajax
        .post(this.$apiUrls.GetPatientBasicInfo, '', {
          query: { regNo: this.$parent.$parent.selectRow.regNo }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.form = {
            ...returnData,
            birthday: dataUtils.subBlankDate(returnData.birthday)
          };
        });
    }
  }
};
</script>

<style lang="less" scoped>
.basicInfo {
  height: 100%;
  display: flex;
  flex-direction: column;
  .info-wrap {
    flex: 1;
    padding: 18px;
    background: rgba(23, 112, 223, 0.2);
    border-radius: 4px;
    overflow: auto;
    display: flex;
  }
  .info-img {
    border: 1px solid rgba(178, 190, 195, 0.5);
    width: 140px;
    height: 178px;
    margin-right: 18px;
  }
  .img {
    width: 100%;
    height: 100%;
    border-radius: 4px;
  }
  .info-form {
    flex: 1;
    // overflow: auto;
  }
  /deep/.el-form-item__label {
    font-size: 14px;
    color: #2d3436;
    font-weight: 600;
  }
  /deep/.el-form-item {
    margin-bottom: 16px;
  }
  /deep/.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    background-color: #409eff;
    border-color: #409eff;
  }
  /deep/.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
    border-color: #fff;
  }
}
@media screen and(max-width: 1440px) {
  .basicInfo {
    .el-form-item {
      margin-bottom: 0;
    }
    .el-form-item__label,
    .el-form-item__content {
      line-height: 30px !important;
    }
  }
}
</style>
