<template>
  <div class="specimen">
    <PublicTable
      :isSortShow="false"
      :viewTableList.sync="tableData"
      :theads.sync="theads"
      :columnWidth="{}"
    >
      <template #status="{ scope }">
        <div>
          {{
            systemParams.value === '1'
              ? systemSample[scope.row.status]
              : prefabricateSample[scope.row.status]
          }}
        </div>
      </template>
      <template #gatherOperator="{ scope }">
        <div>
          {{ G_EnumList['SysOperator'][scope.row.gatherOperator] }}
        </div>
      </template>
      <template #gatherTime="{ scope }">
        <div>
          {{ dateFormat(scope.row.gatherTime) }}
        </div>
      </template>
      <template #transportTime="{ scope }">
        <div>
          {{ dateFormat(scope.row.transportTime) }}
        </div>
      </template>
    </PublicTable>
  </div>
</template>

<script>
import PublicTable from '@/components/publicTable.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
export default {
  name: 'specimen',
  components: {
    PublicTable
  },
  data() {
    return {
      tableData: [],
      theads: {
        sampleNo: '条码号',
        status: '状态',
        barcodeName: '条码类型',
        combName: '组合名称',
        gatherTime: '采集时间',
        gatherOperator: '采集人',
        transportTime: '包运送时间'
      },
      systemParams: {},
      // 系统生成条码模式：状态(0:全部 1:未打印 2:已打印 3:已运送) 预制条码模式 ：状态(0:全部 1:未配管 2:已配管 3:已运送)
      systemSample: {
        0: '全部',
        1: '未打印',
        2: '已打印',
        3: '已运送'
      },
      prefabricateSample: {
        0: '全部',
        1: '未配管',
        2: '已配管',
        3: '已运送'
      }
    };
  },
  created() {
    this.GetSampleData();
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_userInfo'])
  },
  methods: {
    // 获取系统参数
    getSystemParameters() {
      this.$ajax
        .post(this.$apiUrls.ReadSystemParameters, '', {
          query: { paramCode: 4 }
        })
        .then((r) => {
          console.log('ReadSystemParameters: ', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.systemParams = returnData;
          // 1:"系统条码" 2:"预制条码"
        });
    },
    GetSampleData() {
      this.$ajax
        .post(this.$apiUrls.GetSampleData, '', {
          query: { regNo: this.$parent.$parent.selectRow.regNo }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.tableData = returnData || [];
        });
    },
    dateFormat(date) {
      let newDate = date?.substr(0, 10);
      return newDate;
    }
  }
};
</script>

<style lang="less" scoped>
.specimen {
  border: 1px solid rgba(178, 190, 195, 0.5);
  border-radius: 4px;
}
</style>
