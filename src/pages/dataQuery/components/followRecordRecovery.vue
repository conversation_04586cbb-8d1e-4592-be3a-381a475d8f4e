<template>
  <!-- 恢复记录 -->
  <div class="recoveryRecordDrawer">
    <div class="headrCont">
      <div class="header-item">
        <el-input
          v-model.trim="recoveryForm.keyWord"
          @keyup.enter.native.stop="search"
          @clear="search"
          size="small"
          placeholder="姓名/体检号"
          style="width: 100%"
          clearable
        ></el-input>
      </div>
      <div class="header-item">
        <span>单位</span>
        <el-select
          class="select"
          v-model.trim="recoveryForm.companyCode"
          @change="companyChange"
          clearable
          placeholder="请选择"
          size="small"
          width="100%"
          filterable
        >
          <el-option
            v-for="(item, index) in companyList"
            :key="index"
            :value="item.value"
            :label="item.label"
          ></el-option>
        </el-select>
      </div>
      <div class="header-item">
        <span>部门</span>
        <el-select
          class="select"
          v-model.trim="recoveryForm.companyDeptCode"
          placeholder="请选择"
          size="small"
          filterable
          @change="search"
          clearable
          :disabled="isHave"
        >
          <el-option
            v-for="(item, index) in companyDeptList"
            :key="index"
            :label="item.deptName"
            :value="item.deptCode"
          ></el-option>
        </el-select>
      </div>
      <div class="header-item">
        <el-radio-group
          v-model="recoveryForm.peType"
          class="radio-group"
          @input="search"
        >
          <el-radio :label="0">所有</el-radio>
          <el-radio :label="1">个人</el-radio>
          <el-radio :label="2">团体</el-radio>
        </el-radio-group>
      </div>

      <div class="header-item">
        <span>删除时间</span>
        <el-date-picker
          style="width: 250px"
          @change="search"
          :picker-options="pickerOptions"
          v-model.trim="delTime"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          clearable
          size="small"
        >
        </el-date-picker>
      </div>
      <el-button
        size="small"
        icon="iconfont icon-search"
        class="blue_btn"
        @click="search"
        >查询</el-button
      >
      <el-button
        size="small"
        icon="iconfont icon-huifuxitongmoren"
        class="violet_btn"
        @click="regainRecord"
        >恢复</el-button
      >
      <el-button
        size="small"
        class="red_btn"
        icon="iconfont icon-shanchu"
        @click="delRecord"
        >彻底删除</el-button
      >
    </div>
    <div class="tableCont">
      <PublicTable
        :viewTableList.sync="tableData"
        :theads.sync="theads"
        :tableLoading.sync="loading"
        :columnWidth="columnWidth"
        @selectionChange="handleSelRow"
        showOverflowTooltip
        border
        isCheck
      >
        <template #name="{ scope }">
          <div class="sex_wrap">
            <i v-if="scope.row.sex == 1" class="el-icon-male"></i>
            <i v-if="scope.row.sex == 2" class="el-icon-female"></i>
            {{ scope.row.name }}
          </div>
        </template>
      </PublicTable>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { dataUtils } from '@/common';
import PublicTable from '@/components/publicTable.vue';
export default {
  name: 'followRecordRecovery',
  components: { PublicTable },
  props: {
    companyList: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      personOrCompany: '/person', //个人/团体
      loading: false,
      delTime: [dataUtils.getDate(), dataUtils.getDate()],
      companyDeptList: [],
      isHave: true,
      recoveryForm: {
        keyWord: '',
        startTime: '',
        endTime: '',
        peType: 0,
        companyCode: '',
        companyDeptCode: ''
      }, //查询条件
      tableData: [], //表单数据
      theads: {
        regNo: '体检号',
        name: '姓名',
        age: '年龄',
        tel: '电话',
        activeTime: '体检日期',
        deleteTime: '删除时间',
        address: '地址',
        diagnosis: '诊断',
        findProblems: '发现问题',
        suggestion: '建议',
        recorder: '登记人',
        notifier: '通知人',
        afterFollowUp: '处理情况',
        secondaryNotifier: '二次随访员',
        secondaryAfterFollowUp: '二次随访情况'
      },
      columnWidth: {
        regNo: 120,
        age: 50,
        name: 90,
        activeTime: 155,
        deleteTime: 155,
        suggestion: 160,
        auditTime: 125,
        createTime: 125,
        tel: 120,
        address: 160,
        findProblems: 250,
        diagnosis: 160,
        peType: 80,
        notifier: 90,
        secondaryNotifier: 120,
        secondaryAfterFollowUp: 160,
        recorder: 90,
        afterFollowUp: 160
      },
      selArr: [],
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() > Date.now();
        }
      }
    };
  },
  created() {},
  mounted: function () {
    this.$nextTick(() => {
      this.search();
    });
  },
  computed: {
    ...mapGetters(['G_EnumList'])
  },
  methods: {
    //查询
    search() {
      if (!this.delTime) {
        this.$message({
          message: '请先选择删除时间!',
          type: 'warning',
          showClose: true
        });
        this.recoveryForm.startTime = null;
        this.recoveryForm.endTime = null;
        return;
      } else {
        this.recoveryForm.startTime = this.delTime[0] || null;
        this.recoveryForm.endTime = this.delTime[1] || null;
      }
      this.tableData = [];
      this.$ajax
        .post(this.$apiUrls.QueryDeletePeFollowUp, this.recoveryForm)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.tableData = returnData || [];
        });
    },
    //勾选行
    handleSelRow: function (val) {
      this.selArr = [];
      val.map((item) => {
        if (item.followUpId != '') {
          this.selArr.push(item.followUpId);
        }
      });
      console.log('[ this.selArr ]-558', this.selArr);
    },
    //恢复记录
    regainRecord() {
      if (this.selArr.length < 1) {
        this.$message({
          message: '请先选择要恢复的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$ajax
        .post(this.$apiUrls.RestorePeFollowUp, this.selArr)
        .then((r) => {
          let { success } = r.data;
          if (!success) return;
          this.$message({
            message: '恢复成功!',
            type: 'success',
            showClose: true
          });
          this.search();
        });
    },
    //删除记录
    delRecord() {
      if (this.selArr.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.$ajax
              .post(this.$apiUrls.CompletelyDeletePeFollowUp, this.selArr)
              .then((r) => {
                let { success } = r.data;
                if (!success) return;
                this.$message({
                  showClose: true,
                  message: '删除成功',
                  type: 'success'
                });
                this.search();
              });
          })
          .catch(() => {
            return;
          });
      } else {
        return this.$message({
          message: '请先勾选至少一条数据再进行操作！',
          type: 'warning',
          showClose: true
        });
      }
    },
    //单位下拉变化
    companyChange(data) {
      this.search();
      if (!data) {
        this.isHave = true;
        this.companyDeptCode = '';
        this.companyDeptList = [];
        return;
      }
      this.getDepartList(data);
    },
    //获取部门数据
    getDepartList(companyCode) {
      this.recoveryForm.companyDeptCode = '';
      this.isHave = true;
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanyDepartment, {
          companyCode: companyCode || '',
          deptCode: '',
          deptName: ''
        })
        .then((r) => {
          this.isHave = false;
          let { success, returnData } = r.data;
          if (!success) return;
          this.companyDeptList = returnData || [];
        });
    },
    //清空表单
    clearForm() {
      this.tableData = [];
      this.companyDeptList = [];
      this.delTime = [dataUtils.getDate(), dataUtils.getDate()];
      this.isHave = true;
      this.selArr = [];
      this.recoveryForm = {
        keyWord: '',
        startTime: '',
        endTime: '',
        peType: 0,
        companyCode: '',
        companyDeptCode: ''
      };
    }
  }
};
</script>

<style lang="less" scoped>
.recoveryRecordDrawer {
  display: flex;
  flex-direction: column;
  height: 100%;
  /deep/.el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #409eff !important;
    border-color: #409eff !important;
  }
  .headrCont {
    display: flex;
    align-items: center;
    overflow-x: auto;
    overflow-y: hidden;
    background: rgba(178, 190, 195, 0.2);
    border-radius: 4px;
    padding: 15px;
    span {
      font-size: 14px;
      font-weight: 600;
      margin-right: 10px;
      white-space: nowrap;
    }
  }
  .header-item {
    display: flex;
    align-items: center;
    margin-right: 10px;
  }
  .selCol {
    display: flex;
    .el-select {
      width: 100px;
      padding-left: 20px;
    }
    .el-form-item {
      flex: 1;
    }
    /deep/.el-form-item__content {
      margin-left: 3px !important;
    }
  }
  /deep/.searchCol {
    .el-form-item__content {
      margin-left: 20px !important;
    }
  }
  /deep/.el-input__inner {
    height: 32px;
    line-height: 32px;
  }
  .tableCont {
    flex: 1;
    padding: 15px;
  }
  .el-button {
    height: 32px;
  }
  .sex_wrap {
    .el-icon-male {
      color: #037bff;
      background: none;
    }
    .el-icon-female {
      color: #fc73a7;
      background: none;
    }
  }
  .radio-group {
    & > * {
      margin-right: 5px;
    }
    min-width: 172px;
  }
}
</style>
