<template>
  <div class="btn_group">
    <el-button
      v-if="btnList.includes('查询')"
      @click="search"
      class="blue_btn btn"
      size="small"
      icon="iconfont icon-search"
      >查询</el-button
    >
    <el-button
      v-if="btnList.includes('详情')"
      @click="detailsClick"
      size="small"
      class="violet_btn btn"
      icon="iconfont icon-xiangqing-"
      >详情</el-button
    >
    <el-button
      v-if="btnList.includes('保存')"
      @click="saves"
      class="blue_btn btn"
      size="small"
      icon="iconfont icon-baocun"
      >保存</el-button
    >
    <el-button
      v-if="btnList.includes('删除')"
      @click="deletes"
      class="red_btn btn"
      size="small"
      icon="iconfont icon-shanchu"
      >删除</el-button
    >
    <el-button
      v-if="btnList.includes('打印')"
      @click="prints"
      size="small"
      class="green_btn btn"
      icon="iconfont icon-dayin-"
      >打印</el-button
    >
    <el-button
      v-if="btnList.includes('导出')"
      @click="exports"
      size="small"
      class="yellow_btn btn"
      icon="iconfont icon-daochu"
      >导出</el-button
    >
    <el-button
      v-if="btnList.includes('短信发送')"
      @click="SMS_sending"
      size="small"
      class="yellow_btn btn"
      icon="iconfont icon-daochu"
      >短信发送</el-button
    >
    <slot name="footAdd"></slot>
  </div>
</template>

<script>
export default {
  name: 'btnGroup',
  props: {
    btnList: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  data() {
    return {};
  },
  methods: {
    // 查询
    search() {
      this.$emit('search');
    },
    // 详情
    detailsClick() {
      this.$emit('detailsClick');
    },
    //保存
    saves() {
      this.$emit('saves');
    },
    // 删除
    deletes() {
      this.$emit('deletes');
    },
    // 打印
    prints() {
      this.$emit('prints');
    },
    // 导出
    exports() {
      this.$emit('exports');
    },
    //短信发送
    SMS_sending() {
      this.$emit('SMS_sending');
    }
  }
};
</script>
<style lang="less" scoped>
.btn_group {
  display: flex;
  align-items: center;
}
.btn {
  padding: 6.5px 10px;
}
</style>
