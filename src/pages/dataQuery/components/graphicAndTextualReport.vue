<template>
  <div class="printImg_page">
    <div class="right_wrap">
      <!-- <header>
        <ul>
          <li>
            <label>体检号：</label>
            <span>{{ headerInfo.regNo }}</span>
          </li>
          <li>
            <label>姓名：</label>
            <span>{{ headerInfo.name }}</span>
          </li>
          <li>
            <label>性别：</label>
            <span>{{ G_EnumList["Sex"][headerInfo.sex] }}</span>
          </li>
          <li>
            <label>年龄：</label>
            <span>{{ headerInfo.age }}</span>
          </li>
          <li>
            <label>单位：</label>
            <span>{{ headerInfo.companyName }}</span>
          </li>
        </ul>
      </header> -->
      <div class="print_content">
        <embed class="embed_dom" :src="imgSrc" type="application/pdf" />
      </div>
    </div>
    <div class="left_wrap">
      <!-- <h3>图文报告</h3> -->
      <el-menu
        ref="printImgMenu_Ref"
        :default-active="defaultActive"
        class="menu_dom"
        size="mini"
      >
        <el-menu-item
          :index="item.regCombId"
          v-for="(item, idx) in printImgList"
          :key="idx"
          @click="menuClick(item)"
        >
          <span slot="title">{{ item.combName }}</span>
        </el-menu-item>
      </el-menu>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  name: 'graphicAndTextualReport',
  props: {},
  computed: {
    ...mapGetters(['G_EnumList'])
  },
  data() {
    return {
      defaultActive: '',
      imgSrc: '',
      printImgList: []
    };
  },
  methods: {
    menuClick(menu) {
      this.imgSrc = menu.imagePath;
    },
    getImgTextList(regNo) {
      this.$ajax
        .paramsPost(this.$apiUrls.GetAllRecordImage, { regNo })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) {
            return;
          }
          this.printImgList = returnData || [];
          if (this.printImgList.length > 0) {
            this.defaultActive = this.printImgList[0].regCombId;
            this.imgSrc = this.printImgList[0].imagePath;
          }
        });
    }
  },
  created() {
    this.getImgTextList(this.$parent.$parent.selectRow.regNo);
  }
};
</script>

<style lang="less" scoped>
.printImg_page {
  height: 100%;
  overflow: auto;
  padding: 10px;
  display: flex;
  .left_wrap {
    width: 240px;
    display: flex;
    flex-direction: column;
    h3 {
      height: 65px;
      line-height: 65px;
      font-size: 18px;
      color: #2d3436;
      font-weight: normal;
    }
    .menu_dom {
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
      background: rgba(178, 190, 195, 0.1);
      border: 0;
      li {
        height: 40px;
        line-height: 40px;
      }
    }
  }
  .right_wrap {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-left: 10px;
    header {
      display: flex;
      height: 65px;
      align-items: center;
      justify-content: space-between;
      ul {
        display: flex;
        flex: 1;
        border-radius: 4px;
        flex-shrink: 0;
        justify-content: space-around;
        background: rgba(250, 182, 59, 0.2);
        height: 37px;
        align-items: center;
        margin-right: 15px;
      }
    }
    .print_content {
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
      .embed_dom {
        width: 100%;
        height: 100%;
        overflow: auto;
        body {
          margin: 0;
        }
      }
    }
  }
  .btn {
    padding: 6.5px 10px;
  }
}
</style>
