<template>
  <div class="pastRecords" id="pastRecords">
    <div class="leftDiv" id="leftDiv">
      <div class="divInfo">
        <p class="pCss">体检综述</p>
        <div class="contTxt">
          <div class="contDiv">
            <div class="contInfos">
              <p class="contP">{{ timeFormat(infoList.examTime) }}</p>
              <p class="contP">{{ infoList.examTimeLast }}</p>
            </div>

            <div class="contInfo">
              <div class="cont" style="white-space: pre-wrap">
                {{ infoList.summary.result }}
              </div>
              <div class="cont" style="white-space: pre-wrap">
                {{ infoList.summary.resultLast }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="divInfo">
        <p class="pCss">诊断建议</p>
        <div class="contTxt">
          <div class="contDiv">
            <div class="contInfos">
              <p class="contP">{{ timeFormat(infoList.examTime) }}</p>
              <p class="contP">{{ infoList.examTimeLast }}</p>
            </div>

            <div class="contInfo">
              <div class="cont" style="white-space: pre-wrap">
                {{ infoList.suggestion.result }}
              </div>
              <div class="cont" style="white-space: pre-wrap">
                {{ infoList.suggestion.resultLast }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="divInfo"
        v-for="(item, index) in infoList.examItems"
        :key="index"
        :id="'id' + index"
      >
        <p class="pCss">{{ item.combName }}</p>

        <div class="combDiv">
          <p>
            <span>项目名称</span><span>{{ timeFormat(infoList.examTime) }}</span
            ><span>{{ infoList.examTimeLast }}</span>
          </p>
          <p
            class="contDiv"
            v-for="(items, idx) in item.itemResults"
            :key="idx"
          >
            <span>{{ items.itemName }}</span
            ><span>{{ items.result }}</span
            ><span>{{ items.resultLast }}</span>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import PublicTable from '@/components/publicTable.vue';
export default {
  name: 'pastRecords',
  components: { PublicTable },
  data() {
    return {
      navList: [
        {
          id: 1,
          title: '体检综述'
        },
        {
          id: 2,
          title: '诊断建议'
        }
      ],
      infoList: {
        examTime: '',
        examTimeLast: '',
        summary: {
          result: '',
          resultLast: ''
        },
        suggestion: {
          result: '',
          resultLast: ''
        },
        examItems: []
      }
    };
  },
  mounted() {
    this.getHistoryReport();
  },

  methods: {
    getHistoryReport() {
      this.navList = [
        {
          id: 1,
          title: '体检综述'
        },
        {
          id: 2,
          title: '诊断建议'
        }
      ];
      this.$ajax
        .post(this.$apiUrls.GetHistoryReport, '', {
          query: {
            regNo: this.$parent.$parent.selectRow.regNo
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;

          if (returnData.examItems) {
            returnData.examItems.map((item, index) => {
              this.navList.push({
                id: index + 3,
                title: item.combName
              });
            });
            this.infoList = returnData || [];
          } else {
            this.$message({
              message: '没有历史报告的数据!',
              type: 'warning',
              showClose: true
            });
            return;
          }
          console.log('[ this.infoList ]-234', this.infoList);
        });
    },
    // 格式化时间
    timeFormat(date) {
      if (date) {
        let newDate = /\d{4}-\d{1,2}-\d{1,2}/g.exec(date);
        return newDate[0];
      } else {
        return '';
      }
    }
  }
};
</script>

<style lang="less" scoped>
.pastRecords {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  //   padding: 20px;
  font-family: PingFangSC-Semibold;
  font-size: 14px;
  color: #2d3436;
  .leftDiv {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    .divInfo {
      width: 100%;
      min-height: 300px;
      display: flex;
      flex-direction: column;
      border: 1px solid #eee;
      margin-bottom: 18px;
      &:last-child {
        margin-bottom: 0;
      }
      .pCss {
        background: rgba(23, 112, 223, 0.2);
        border-radius: 1px;
        border-radius: 1px;
        line-height: 38px;
        font-family: PingFangSC-Medium;
        font-size: 18px;
        // color: #ffffff;
        padding: 0 20px;
      }
      .contInfos {
        display: flex;
        flex-direction: row;
        border-bottom: 1px solid #ccc;
        .contP {
          line-height: 32px;
          border-bottom: 1px solid #eee;
          padding: 0 20px;
          flex: 1;
          border-right: 1px solid #ccc;
        }
      }
      .contTxt {
        flex: 1;
        display: flex;
        flex-direction: row;
        overflow: auto;
        .contDiv {
          flex: 1;
          flex-shrink: 0;
          border: 1px solid #eee;
          display: flex;
          flex-direction: column;
          .contInfo {
            flex: 1;
            display: flex;
            flex-direction: row;

            .contP {
              line-height: 32px;
              border-bottom: 1px solid #eee;
              padding: 0 20px;
              flex: 1;
              border-right: 1px solid #ccc;
            }
            .cont {
              padding: 0 20px;
              flex: 1;
              flex-shrink: 0;
              overflow: auto;
              border-right: 1px solid #ccc;
            }
          }
        }
      }
      .combDiv {
        flex: 1;
        overflow: auto;
        p {
          display: flex;
          flex-direction: row;
          line-height: 32px;
          border-bottom: 1px dashed #eee;
          padding: 0 20px;
          border-bottom: 1px solid #ccc;
          & > span {
            flex: 1;
          }
        }
      }
    }
  }
}
</style>
