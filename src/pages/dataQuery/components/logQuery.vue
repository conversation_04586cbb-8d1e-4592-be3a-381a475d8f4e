<template>
  <div class="logQuery">
    <PublicTable
      :isSortShow="false"
      :viewTableList.sync="tableData"
      :theads.sync="theads"
      :columnWidth="columnWidth"
    >
    </PublicTable>
  </div>
</template>

<script>
import PublicTable from '@/components/publicTable.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
export default {
  name: 'logQuery',
  components: {
    PublicTable
  },
  data() {
    return {
      tableData: [],
      theads: {
        operatorName: '操作员',
        logTime: '操作时间',
        message: '操作内容'
      },
      columnWidth: {
        operatorName: 100,
        logTime: 170
      }
    };
  },
  created() {
    this.GetSampleData();
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_userInfo'])
  },
  methods: {
    GetSampleData() {
      this.$ajax
        .post(this.$apiUrls.GetLogData, '', {
          query: { regNo: this.$parent.$parent.selectRow.regNo }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.tableData = returnData || [];
        });
    }
  }
};
</script>

<style lang="less" scoped>
.logQuery {
  border: 1px solid rgba(178, 190, 195, 0.5);
  border-radius: 4px;
}
</style>
