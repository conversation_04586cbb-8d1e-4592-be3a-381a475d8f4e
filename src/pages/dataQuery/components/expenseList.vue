<template>
  <!-- 费用清单 -->
  <div class="expenseList">
    <div class="list-wrap">
      <div class="list-info">
        <span>姓名：{{ personalInfo.name }}</span>
        <span>性别：{{ G_EnumList['Sex'][personalInfo.sex] }}</span>
        <span>年龄：{{ personalInfo.age }}岁</span>
        <span>体检号：{{ personalInfo.regNo }}</span>
      </div>
      <div class="table-wrap">
        <div
          class="table-item"
          v-for="(item, index) in personalInfo.itemFeeCls"
          :key="index"
        >
          <div class="table">
            <PublicTable
              :theads.sync="theads"
              :isSortShow="false"
              :viewTableList.sync="item.items"
            ></PublicTable>
          </div>
          <div class="subtotal">
            <span class="total">小计</span>
            <div class="subtotal-item">
              <span>{{ item.originalPrice }}</span>
              <span>{{ item.price }}</span>
              <span>{{ item.selfPayPrice }}</span>
              <span>{{ item.companyPayPrice }}</span>
              <span>{{ item.platformPayPrice }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="subtotal">
        <span class="total">合计</span>
        <div class="subtotal-item">
          <span>{{ personalInfo.originalPrice }}</span>
          <span>{{ personalInfo.price }}</span>
          <span>{{ personalInfo.selfPayPrice }}</span>
          <span>{{ personalInfo.companyPayPrice }}</span>
          <span>{{ personalInfo.platformPayPrice }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import PublicTable from '@/components/publicTable.vue';
export default {
  name: 'expenseList',
  components: {
    PublicTable
  },
  data() {
    return {
      personalInfo: {
        name: '',
        sex: '',
        age: '',
        regNo: '',
        originalPrice: '',
        price: '',
        selfPayPrice: '',
        companyPayPrice: '',
        platformPayPrice: '',
        itemFeeCls: [
          {
            items: [
              {
                feeClsName: '',
                combName: '',
                originalPrice: '',
                price: '',
                selfPayPrice: '',
                companyPayPrice: '',
                platformPayPrice: ''
              }
            ],
            originalPrice: '',
            price: '',
            selfPayPrice: '',
            companyPayPrice: '',
            platformPayPrice: ''
          }
        ]
      },
      theads: {
        feeClsName: '类别',
        combName: '体检项目（名称）',
        originalPrice: '原始单价',
        price: '计价金额',
        selfPayPrice: '个人交费',
        companyPayPrice: '团体交费',
        platformPayPrice: '预约平台'
      }
    };
  },
  computed: {
    ...mapGetters(['G_EnumList'])
  },
  created() {
    this.GetExpenseList();
  },
  methods: {
    // 获取费用清单
    GetExpenseList() {
      this.$ajax
        .post(this.$apiUrls.GetExpenseList, '', {
          query: {
            regNo: this.$parent.$parent.selectRow.regNo
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.personalInfo = returnData;
        });
    }
  }
};
</script>

<style lang="less" scoped>
.expenseList {
  background: rgba(178, 190, 195, 0.1);
  border: 1px solid rgba(178, 190, 195, 0.5);
  border-radius: 4px;
  .list-wrap {
    // margin: 10px;
    padding: 20px;
    background: #fff;
    font-size: 14px;
    height: 100%;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .list-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // border-bottom: 1px solid #2d3436;
    margin-bottom: 10px;
  }
  .list-thead {
    padding: 10px;
    border-bottom: 1px solid #2d3436;
    font-weight: 600;
  }
  .list-item {
    padding: 10px;
    border-bottom: 1px dashed rgba(45, 52, 54, 0.4);
  }
  .table-wrap {
    flex: 1;
    overflow: auto;
  }
  .table-item {
    display: flex;
    flex-direction: column;
  }
  .table {
    flex: 1;
    overflow: auto;
    /deep/.el-table--scrollable-y .el-table__body-wrapper {
      height: auto !important;
    }
    /deep/.el-table__body-wrapper {
      height: auto !important;
    }
  }
  .subtotal {
    padding: 10px;
    border-bottom: 1px solid #2d3436;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .total {
    width: 29%;
  }
  .subtotal-item {
    flex: 1;
    display: flex;
    justify-content: space-between;
    span {
      display: inline-block;
      width: 20%;
      &:last-child {
        width: 19%;
      }
    }
  }
}
</style>
