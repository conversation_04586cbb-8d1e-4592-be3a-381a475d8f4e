<template>
  <div class="analyse_page pages" @click="pageClick">
    <div class="analyse_wrap">
      <ul class="search_ul">
        <li>
          <div class="every_inp">
            <label style="width: 60px">体检号</label>
            <p>
              <el-input
                v-model.trim="searchInfo.regNo"
                size="small"
                placeholder="请输入"
              ></el-input>
            </p>
          </div>
          <div class="every_inp">
            <label style="width: 60px">档案卡号</label>
            <p>
              <el-input
                v-model.trim="searchInfo.patCode"
                size="small"
                placeholder="请输入"
              ></el-input>
            </p>
          </div>
        </li>
        <li>
          <div class="every_inp">
            <label>姓名</label>
            <p>
              <el-input
                v-model.trim="searchInfo.name"
                size="small"
                placeholder="请输入"
              ></el-input>
            </p>
          </div>
          <div class="every_inp">
            <label>性别</label>
            <p>
              <el-select
                v-model="searchInfo.sex"
                placeholder="请选择"
                size="small"
              >
                <el-option key="1" label="全部" :value="-1"></el-option>
                <el-option key="2" label="男" :value="1"></el-option>
                <el-option key="3" label="女" :value="2"></el-option>
              </el-select>
            </p>
          </div>
        </li>
        <li>
          <div class="every_inp">
            <label>身份证</label>
            <p>
              <el-input
                v-model.trim="searchInfo.cardNo"
                size="small"
                placeholder="请输入"
              ></el-input>
            </p>
          </div>
          <div class="every_inp">
            <el-button
              size="mini"
              class="yellow_btn"
              icon="iconfont icon-shenfenzheng"
              >身份证</el-button
            >
          </div>
        </li>
        <li>
          <div class="every_inp">
            <el-radio-group v-model="searchInfo.personCompany">
              <el-radio :label="0" class="type_radio">所有</el-radio>
              <el-radio :label="1" class="type_radio">个人</el-radio>
              <el-radio :label="2">团体</el-radio>
            </el-radio-group>
          </div>
          <div class="every_inp">
            <label style="width: 60px">单位</label>
            <p>
              <el-cascader
                ref="company_cascader_ref"
                v-model="searchInfo.companyCode"
                :options="companyList"
                :props="{ multiple: false }"
                clearable
                filterable
                size="small"
                collapse-tags
                @change="companyChange"
                :filter-method="filterMethod"
              >
              </el-cascader>
            </p>
          </div>
        </li>
        <!-- <li>
                    <div class="every_inp">
                        <label>套餐</label>
                        <p>
                            <el-select style="width:100%" placeholder="请选择" size="small" filterable clearable v-model="searchInfo.meal"
                                class="input">
                                <el-option v-for="item in clusterList" :key="item.clusCode" :label="item.clusName"
                                    :value="item.clusCode">
                                </el-option>
                            </el-select>
                        </p>
                    </div>
                </li> -->
        <li v-if="G_config.physicalMode.includes('普检')">
          <div class="every_inp">
            <label>分类</label>
            <p>
              <el-select
                clearable
                v-model="searchInfo.peCls"
                placeholder="请选择"
                size="small"
                style="width: 100%"
              >
                <el-option
                  v-for="item in G_peClsList"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                ></el-option>
              </el-select>
            </p>
          </div>
        </li>
        <li>
          <div class="every_inp">
            <el-select
              v-model="searchInfo.timeType"
              placeholder="请选择"
              size="small"
              style="width: 100px; margin-right: 10px"
            >
              <el-option :key="1" label="登记时间" :value="1"></el-option>
              <el-option :key="2" label="体检时间" :value="2"></el-option>
            </el-select>
            <p>
              <el-date-picker
                :picker-options="{ shortcuts: G_datePickerShortcuts }"
                v-model="dateArr"
                :clearable="false"
                type="daterange"
                range-separator="-"
                style="width: 100%"
                value-format="yyyy-MM-dd"
                start-placeholder="开始日期"
                size="small"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </p>
          </div>
        </li>
        <!-- <li>
                    <div class="every_inp">
                        <label>院区</label>
                        <p>
                            <el-select v-model="searchInfo.area" placeholder="请选择" size="small" style="width:100%">

                            </el-select>
                        </p>
                    </div>
                </li> -->
        <li>
          <div class="every_inp" style="justify-content: space-between">
            <el-button
              size="mini"
              class="blue_btn"
              icon="iconfont icon-search"
              @click="searchFun"
              >查询</el-button
            >
            <el-button
              size="mini"
              @click="addTempClick"
              class="blue_btn"
              icon="iconfont icon-baocun"
              >保存模板</el-button
            >
            <el-button
              size="mini"
              @click="loadTemp"
              class="violet_btn"
              icon="iconfont icon-moban"
              >加载模板</el-button
            >
            <el-button
              size="mini"
              class="yellow_btn"
              @click="exportClick"
              icon="iconfont icon-daochu"
              >导出</el-button
            >
          </div>
        </li>
      </ul>
      <!-- 内容 -->
      <div class="content_wrap">
        <div class="left_wrap">
          <ul class="selection-tabs">
            <li
              :class="liActive === 1 ? 'li-active' : ''"
              @click="queryTypeClick(1)"
            >
              选择组合
            </li>
            <li
              :class="liActive === 2 ? 'li-active' : ''"
              @click="queryTypeClick(2)"
            >
              选择项目
            </li>
          </ul>
          <div class="bottom_wrap" v-if="liActive === 1">
            <div class="add_btn">
              <el-button
                type="primary"
                size="mini"
                style="flex: 1; margin-right: 5px"
                @click="addCondition"
                >添加</el-button
              >
              <el-select
                v-model="searchInfo.operator"
                size="small"
                style="width: 50px"
                placeholder="请选择"
              >
                <el-option value="and" label="且"></el-option>
                <el-option value="or" label="或"></el-option>
              </el-select>
            </div>
            <div
              class="every_wrap"
              v-for="(item, idx) in searchInfo.expList"
              :key="idx"
            >
              <span
                class="project_span"
                :title="item.name"
                @click.stop="projectClick($event, item)"
                >{{ item.name }}</span
              >
              <el-select
                size="small"
                style="width: 60px"
                v-model="item.operator"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in symbolList"
                  :key="item"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-select>
              <el-input
                style="width: 50px"
                size="small"
                v-model="item.codeValue"
                placeholder=""
              ></el-input>
              <el-button
                type="danger"
                size="mini"
                icon="el-icon-delete"
                @click="delCondition(idx)"
              ></el-button>
            </div>
          </div>
          <div class="bottom_wrap" v-if="liActive === 2">
            <div class="add_btn">
              <el-button
                type="primary"
                size="mini"
                style="flex: 1; margin-right: 5px"
                @click="addCondition"
                >添加</el-button
              >
              <el-select
                v-model="searchInfo.operator"
                size="small"
                style="width: 50px"
                placeholder="请选择"
              >
                <el-option value="and" label="且"></el-option>
                <el-option value="or" label="或"></el-option>
              </el-select>
            </div>
            <div
              class="every_wrap"
              v-for="(item, idx) in searchInfo.expList"
              :key="idx"
            >
              <span
                class="project_span"
                :title="item.name"
                @click.stop="projectClick($event, item)"
                >{{ item.name }}</span
              >
              <el-select
                size="small"
                style="width: 60px"
                v-model="item.operator"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in symbolList"
                  :key="item"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-select>
              <el-input
                style="width: 50px"
                size="small"
                v-model="item.codeValue"
                placeholder=""
              ></el-input>
              <el-button
                type="danger"
                size="mini"
                icon="el-icon-delete"
                @click="delCondition(idx)"
              ></el-button>
            </div>
          </div>
        </div>
        <div class="right_wrap">
          <PublicTable
            :viewTableList.sync="resultList"
            :theads="resultTheads"
            :columnWidth="resultWidth"
          >
            <template #columnRight>
              <el-table-column
                v-for="item in Object.keys(resulTheader)"
                :key="item.index"
                :prop="item"
                :label="resulTheader[item]"
              ></el-table-column>
            </template>
          </PublicTable>
        </div>
      </div>
    </div>
    <!-- 选项目的浮窗 -->
    <div
      class="project_popup"
      v-if="popupShow"
      @click.stop
      @mouseleave="popupMouseleave"
      :style="{ top: top + 'px', left: left + 'px' }"
    >
      <div class="popup_search">
        <el-input
          @keypress.enter.native.stop="popupSearch"
          @clear="popupSearch"
          clearable
          v-model="popupSearchVal"
          style="flex: 1; margin-right: 5px"
          size="small"
          placeholder="请输入内容"
        ></el-input>
        <el-button type="primary" size="mini" @click="popupSearch"
          >查询</el-button
        >
      </div>
      <div class="popup_table">
        <PublicTable
          v-if="liActive === 1"
          :isSortShow="false"
          :theads="theadItemComb"
          :viewTableList.sync="tableDataItemComb"
          ref="itemComb_ref"
          :columnWidth="columnWidth"
          @rowDblclick="theadItemCombDBclick"
        >
        </PublicTable>

        <PublicTable
          v-else
          :isSortShow="false"
          :theads="theadItem"
          :viewTableList.sync="tableDataItem"
          ref="item_ref"
          :columnWidth="theadItemWidth"
          @rowDblclick="theadItemDBclick"
        ></PublicTable>
      </div>
    </div>
    <!-- 保存模板 -->
    <el-dialog title="保存模板" :visible.sync="addTempShow">
      <el-form ref="addTemp_Ref" :model="tempInfo" :rules="tempRules">
        <el-form-item label="模板代码" label-width="100px" prop="templateCode">
          <el-input
            v-model="tempInfo.templateCode"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="模板名称" label-width="100px" prop="templateName">
          <el-input
            v-model="tempInfo.templateName"
            autocomplete="off"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="addTempShow = false">取 消</el-button>
        <el-button size="small" type="primary" @click="addTemp"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <!-- 加载模板 -->
    <el-dialog
      title="结果查询模板"
      :visible.sync="tempDialogShow"
      width="60%"
      class="temp_dialog"
    >
      <div class="dialog_content" style="height: 400px">
        <!-- <div class="temp_btn">
                    <el-button size="small" @click="tempDialogShow = false">取 消</el-button>
                    <el-button size="small" @click="tempDialogShow = false">取 消</el-button>
                </div> -->
        <div class="temp_table">
          <PublicTable
            :isSortShow="false"
            :theads="tempThead"
            :viewTableList.sync="tempList"
            ref="itemComb_ref"
            :columnWidth="tempWidth"
            @rowDblclick="tempDbclick"
          >
            <template #operator="{ scope }">
              {{ operator_enum[scope.row.operator] }}
            </template>
            <template #queryType="{ scope }">
              {{ queryType_enum[scope.row.queryType] }}
            </template>
            <template #columnRight>
              <el-table-column width="80" label="操作">
                <template #default="scope">
                  <el-button
                    type="danger"
                    plain
                    size="mini"
                    @click="tempDel(scope.row)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </template>
          </PublicTable>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="tempDialogShow = false"
          >取 消</el-button
        >
        <el-button size="small" type="primary" @click="tempDialogShow = false"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import PublicTable from '@/components/publicTable';
import { mapGetters } from 'vuex';
import moment from 'moment';
import { dataUtils } from '../../common';
import { export2Excel } from '@/common/excelUtil';
export default {
  name: 'analyse',
  components: {
    PublicTable
  },
  computed: {
    ...mapGetters([
      'G_peClsList',
      'G_userInfo',
      'G_datePickerShortcuts',
      'G_config'
    ])
  },
  data() {
    return {
      tempDialogShow: false,
      operator_enum: {
        and: '并',
        or: '或'
      },
      queryType_enum: {
        1: '组合',
        2: '项目'
      },
      dateArr: [
        moment().add(-1, 'd').format('YYYY-MM-DD'),
        moment().startOf('day').format('YYYY-MM-DD')
      ],
      searchInfo: {
        timeType: 1,
        regNo: '',
        patCode: '',
        name: '',
        sex: -1,
        cardNo: '',
        personCompany: 0,
        companyCode: '',
        peCls: '',
        queryType: 1,
        operator: 'and',
        expList: []
      },
      liActive: 1,
      symbolList: ['=', '<', '>', '<=', '>=', '<>', 'like'],
      top: 0,
      left: 0,
      popupShow: false,
      popupSearchVal: '',
      companyList: [],
      clusterList: [],
      fixed_tableDataItemComb: [],
      tableDataItemComb: [],
      theadItemComb: {
        combCode: '组合代码',
        combName: '组合名称',
        clsName: '项目分类'
      },
      columnWidth: {
        combCode: 100,
        clsName: 100
      },
      fixed_tableDataItem: [],
      tableDataItem: [],
      theadItem: {
        itemCode: '项目代码',
        itemName: '项目名称',
        clsName: '项目分类'
      },
      theadItemWidth: {
        itemCode: 100,
        clsName: 100
      },
      leftActive: {},
      resulTheader: {},
      resultList: [],
      resultWidth: {
        regNo: 125,
        sex: 50,
        age: 50,
        tel: 120,
        cardNo: 178,
        activeDate: 100
      },
      resultTheads: {
        companyName: '单位',
        deptName: '部门',
        regNo: '体检号',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        tel: '电话',
        cardNo: '身份证',
        activeDate: '体检日期',
        doctorName: '采集员'
      },
      tempList: [],
      tempThead: {
        templateCode: '模板代码',
        templateName: '模板名称',
        operator: '异或',
        creator: '创建人',
        createTime: '创建时间',
        queryType: '类型'
      },
      tempWidth: {
        templateCode: 80,
        operator: 60,
        creator: 100,
        // createTime:'创建时间',
        queryType: 100
      },
      addTempShow: false,
      tempInfo: {
        templateCode: '',
        templateName: ''
      },
      tempRules: {
        templateName: [
          { required: true, message: '模板名字', trigger: 'blur' }
        ],
        templateCode: [{ required: true, message: '模板代码', trigger: 'blur' }]
      }
    };
  },
  methods: {
    // 查询
    searchFun() {
      let peCls = this.searchInfo.peCls;
      if (peCls === '') {
        peCls = -1;
      }
      let { companyCode, ...restSearchInfo } = this.searchInfo;
      let datas = {
        beinDate: this.dateArr[0],
        endDate: this.dateArr[1],
        ...restSearchInfo,
        companyCode: companyCode[0],
        peCls
      };
      console.log(datas);
      this.$ajax.post(this.$apiUrls.ResearchAnalysis, datas).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.resultList = returnData.bodyData || [];
        this.resulTheader = returnData.header || {};
      });
    },
    // 项目的点击回调
    projectClick(e, item) {
      this.popupShow = true;
      this.leftActive = item;
      this.popupSearchVal = '';
      console.log(e.target.getBoundingClientRect());
      let coord = e.target.getBoundingClientRect();
      this.top = coord.top + coord.height;
      this.left = coord.left;
      if (this.liActive == 1) {
        this.getItemComb();
      } else {
        this.getItem();
      }
    },
    // 鼠标移开弹窗
    popupMouseleave() {
      this.popupShow = false;
    },
    // 页面点击关闭弹窗
    pageClick() {
      this.popupShow = false;
    },
    // 添加条件
    addCondition() {
      this.searchInfo.expList.push({
        code: '',
        name: '',
        operator: '',
        codeValue: ''
      });
    },
    // 删除条件
    delCondition(idx) {
      this.searchInfo.expList.splice(idx, 1);
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    // 获取单位下拉
    getCompanyList() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    companyChange(data) {
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.dateArr = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.searchFun();
    },
    // 获取套餐下拉
    getClusterList() {
      this.$ajax.post(this.$apiUrls.Cluster).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.clusterList = returnData;
      });
    },
    // 查询类型的切换回调
    queryTypeClick(num) {
      this.liActive = num;
      this.searchInfo.queryType = num;
      this.searchInfo.expList = [];
    },
    // 获取组合分类
    getItemComb() {
      this.$ajax.post(this.$apiUrls.ItemComb_ItemCls, []).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.fixed_tableDataItemComb = returnData || [];
        this.tableDataItemComb = dataUtils.deepCopy(
          this.fixed_tableDataItemComb
        );
      });
    },
    // 获取项目分类
    getItem() {
      this.$ajax.post(this.$apiUrls.Item_ItemCls, []).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.fixed_tableDataItem = returnData || [];
        this.tableDataItem = dataUtils.deepCopy(this.fixed_tableDataItem);
      });
    },
    // 组合的双击回调
    theadItemCombDBclick(row) {
      this.leftActive.name = row.combName;
      this.leftActive.code = row.combCode;
    },
    // 项目的双击回调
    theadItemDBclick(row) {
      this.leftActive.name = row.itemName;
      this.leftActive.code = row.itemCode;
    },
    // 搜索组合
    searchComb() {
      this.tableDataItemComb = this.fixed_tableDataItemComb.filter((item) => {
        return item.combName.indexOf(this.popupSearchVal) != -1;
      });
    },
    // 搜索项目
    searchItem() {
      this.tableDataItem = this.fixed_tableDataItem.filter((item) => {
        return item.itemName.indexOf(this.popupSearchVal) != -1;
      });
    },
    // 组合和项目弹窗的搜索
    popupSearch() {
      if (this.liActive == 1) {
        this.searchComb();
      } else {
        this.searchItem();
      }
    },
    // 保存模板按钮的点击回调
    addTempClick() {
      if (this.searchInfo.expList.length == 0) {
        this.$message({
          message: '请先选择组合或者项目的查询条件！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.addTempShow = true;
      this.$nextTick(() => {
        this.resetForm();
      });
    },
    // 保存模板
    addTemp() {
      this.$refs.addTemp_Ref.validate((valid) => {
        if (valid) {
          this.searchInfo.expList.map((item) => {
            item.templateCode = this.tempInfo.templateCode;
          });
          let datas = {
            ...this.tempInfo,
            operator: this.searchInfo.operator,
            creator: this.G_userInfo.codeOper.operatorCode,
            createTime: moment().format('YYYY-MM-DD HH:mm:ss'),
            queryType: this.searchInfo.queryType,
            templateDetail: this.searchInfo.expList
          };
          console.log(datas);
          this.$ajax
            .post(this.$apiUrls.CU_ResearchAnalysisTemplate + '/Create', datas)
            .then((r) => {
              let { success } = r.data;
              if (!success) return;
              this.addTempShow = false;
              this.$message({
                message: '保存成功！',
                type: 'success'
              });
            });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    // 重置表单
    resetForm() {
      this.$refs.addTemp_Ref.resetFields();
    },
    // 加载模板按钮的点击回调
    loadTemp() {
      this.tempDialogShow = true;
      this.$ajax
        .post(this.$apiUrls.ReadResearchAnalysisTemplate, {
          creator: this.G_userInfo.codeOper.operatorCode
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.tempList = returnData;
        });
    },
    // 删除模板
    tempDel(row) {
      this.$confirm('是否删除该模板?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax
            .post(this.$apiUrls.DeleteResearchAnalysisTemplate, [
              row.templateCode
            ])
            .then((r) => {
              let { success } = r.data;
              if (!success) return;
              this.$message({
                message: '删除成功！',
                type: 'success'
              });
              this.loadTemp();
            });
        })
        .catch(() => {});
    },
    // 模板的双击回调
    tempDbclick(row) {
      this.searchInfo.expList = dataUtils.deepCopy(row.templateDetail);
      this.tempDialogShow = false;
      this.liActive = row.queryType;
    },
    // 导出
    exportClick() {
      if (this.resultList.length == 0) {
        this.$message({
          message: '请先查询科研分析！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let columns = [];
        let theads = {
          ...this.resultTheads,
          ...this.resulTheader
        };
        Object.keys(theads).map((item) => {
          columns.push({
            title: theads[item],
            key: item
          });
        });
        const title = '科研分析' + moment().format('YYYY-MM-DD HH:mm:ss');
        this.$nextTick(() => {
          export2Excel(columns, this.resultList, title);
        });
      });
    }
  },
  created() {
    let arr = [
      {
        key: 'name',
        text: '姓名'
      },
      {
        key: 'age',
        text: '年龄'
      }
    ];
  },
  mounted() {
    this.getCompanyList();
    // this.getClusterList();
  },
  deactivated() {
    this.pageClick();
  }
};
</script>

<style lang="less" scoped>
.analyse_page {
  .analyse_wrap {
    background: #fff;
    height: 100%;
    padding: 10px;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
  }

  .search_ul {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-end;

    li {
      flex-basis: 33%;
      flex-shrink: 0;
      display: flex;
      margin-bottom: 10px;
      overflow: hidden;
    }

    .every_inp {
      flex: 1;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      padding-right: 10px;

      label {
        width: 50px;
        text-align: right;
        font-size: 14px;
        color: #2d3436;
        margin-right: 5px;
      }

      p {
        flex: 1;
      }

      .type_radio {
        margin-right: 10px;
      }
    }
  }

  .content_wrap {
    flex: 1;
    flex-shrink: 0;
    overflow: hidden;
    display: flex;
    .left_wrap {
      width: 300px;
      border: 1px solid #ccc;
      margin-right: 10px;
      border-radius: 4px;
      display: flex;
      flex-direction: column;

      .selection-tabs {
        font-size: 14px;
        display: flex;
        justify-content: center;
        border-bottom: 1px solid #d9dfe2;
        padding: 0 10px;

        li {
          padding: 8px 24px;
          margin: 0 10px;
          cursor: pointer;

          &.li-active {
            color: #1770df;
            font-weight: 600;
            border-bottom: 2px solid #1770df;
          }
        }
      }

      .bottom_wrap {
        flex: 1;
        flex-shrink: 0;
        overflow: auto;
        position: relative;
        padding: 46px 5px 5px;

        .add_btn {
          position: absolute;
          left: 5px;
          right: 5px;
          top: 5px;
          display: flex;
        }

        .every_wrap {
          display: flex;
          margin-bottom: 10px;
        }

        .project_span {
          overflow: hidden;
          flex: 1;
          flex-shrink: 0;
          border: 1px solid #dcdfe6;
          border-radius: 4px;
          padding: 5px;
          cursor: pointer;
          font-size: 14px;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        /deep/ input {
          padding: 0 5px !important;
        }
      }
    }

    .right_wrap {
      flex: 1;
      flex-shrink: 0;
      border: 1px solid #ccc;
      border-radius: 4px;
      overflow: auto;
    }
  }

  .project_popup {
    width: 500px;
    height: 500px;
    border: 1px solid #dcdfe6;
    border-radius: 3px;
    position: fixed;
    background: #fff;
    display: flex;
    flex-direction: column;

    .popup_search {
      display: flex;
      padding: 5px;
    }

    .popup_table {
      flex: 1;
      flex-shrink: 0;
    }
  }
  .temp_dialog {
    /deep/ .el-dialog__body {
      padding: 10px;
    }
  }
  .dialog_content {
    display: flex;
    flex-direction: column;
    overflow: auto;
    .temp_btn {
      text-align: right;
      margin-bottom: 10px;
    }
    .temp_table {
      flex: 1;
      flex-shrink: 0;
    }
  }
}
</style>
