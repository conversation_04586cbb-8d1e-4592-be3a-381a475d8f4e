<template>
  <div class="abnormalDangerList">
    <div class="main">
      <div class="header">
        <h3>危急值异常项目列表:</h3>
        <div class="pBtn">
          <div class="pBtn-time">
            <label>统计时间</label>
            <el-date-picker
              :picker-options="{ shortcuts: G_datePickerShortcuts }"
              v-model="date"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="mini"
              class="width"
              :clearable="true"
              style="width: 220px"
              format="yyyy-MM-dd"
              @change="search"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </div>
          <el-input
            size="mini"
            placeholder="体检号/姓名"
            clearable
            v-model.trim="keyWord"
            class="input"
            @keyup.enter.native="search"
          ></el-input>
          <ButtonGroup
            :btnList="['查询', '导出', '删除']"
            @search="search"
            @exports="exports"
            @deletes="deletes"
          ></ButtonGroup>
        </div>
      </div>
      <div class="contDiv" v-loading="tabLoading">
        <PublicTable
          :isSortShow="false"
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          :columnSort="columnSort"
          :columnWidth="columnWidth"
          @currentChange="rowClick"
          :cell_red="[
            'regNo',
            'name',
            'sex',
            'age',
            'tel',
            'itemName',
            'itemResult',
            'createTime',
            'creator',
            'confirmTime',
            'confirmer',
            'transportTime',
            'replyPerson',
            'replyContent'
          ]"
        >
          <template #sex="{ scope }">
            <div>
              {{ G_EnumList['Sex'][scope.row.sex] }}
            </div>
          </template>
          <template #hasFollowUp="{ scope }">
            <div>
              {{ scope.row.hasFollowUp ? '已随访' : '未随访' }}
            </div>
          </template>
          <!-- <template #creator="{ scope }">
            <div>
              {{ G_EnumList["SysOperator"][scope.row.creator] }}
            </div>
          </template> -->
          <!-- <template #confirmer="{ scope }">
            <div>
              {{ G_EnumList["SysOperator"][scope.row.confirmer] }}
            </div>
          </template> -->
          <template #replyPerson="{ scope }">
            <div>
              {{ G_EnumList['SysOperator'][scope.row.replyPerson] }}
            </div>
          </template>
          <template #columnRight>
            <el-table-column prop="" label="操作" width="110" fixed="right">
              <template slot-scope="scope">
                <el-button
                  type="primary"
                  plain
                  size="mini"
                  class="view"
                  @click="jump(scope.row)"
                  >随访登记</el-button
                >
              </template>
            </el-table-column>
          </template>
        </PublicTable>
      </div>
    </div>
  </div>
</template>

<script>
import PublicTable from '@/components/publicTable.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
import ButtonGroup from './components/buttonGroup.vue';
import ExportExcel from '@/common/excel/exportExcel.js';
export default {
  name: 'abnormalDangerList',
  components: {
    PublicTable,
    ButtonGroup
  },
  mixins: [ExportExcel],
  data() {
    return {
      tabLoading: false,
      keyWord: '',
      tableData: [],
      date: [],
      theads: {
        hasFollowUp: '随访状态',
        regNo: '体检号',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        tel: '电话',
        itemName: '项目',
        itemResult: '项目结果',
        creator: '生成人',
        createTime: '生成时间',
        confirmer: '确认人',
        confirmTime: '确认时间',
        replyPerson: '处理人',
        replyTime: '处理时间',
        replyContent: '处理内容',
        secondaryNotifier: '二次随访人',
        secondaryAfterFollowUp: '二次随访情况',
        finallyNotifier: '后续随访员',
        finallyAfterFollowUp: '后续随访情况'
      },
      columnSort: ['createTime', 'confirmTime', 'replyTime'],
      columnWidth: {
        hasFollowUp: 80,
        regNo: 130,
        itemResult: 170,
        createTime: 170,
        confirmTime: 170,
        replyTime: 170,
        replyContent: 300,
        tel: 120,
        secondaryNotifier: 90,
        secondaryAfterFollowUp: 160,
        finallyNotifier: 110,
        finallyAfterFollowUp: 130
      },
      id: null,
      addList: {
        regNo: '',
        followContent: '',
        recorder: '',
        deptCode: ''
      }
    };
  },
  mounted() {
    this.search();
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_userInfo', 'G_datePickerShortcuts'])
  },
  methods: {
    search() {
      this.tabLoading = true;
      this.$ajax
        .post(this.$apiUrls.CriticalExceptionQuery, {
          keyWord: this.keyWord,
          beginDate: this.date?.[0] || '',
          endDate: this.date?.[1] || ''
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          if (!returnData) return;
          this.tableData = returnData || [];
        })
        .finally(() => {
          this.tabLoading = false;
        });
    },
    // 导出
    exports() {
      if (this.tableData.length == 0) {
        this.$message({
          message: '危机值异常项目列表不能为空！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm(`确定导出 危机值异常项目列表 报表?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // let text = `  项目总数：${this.tableData.length} `
          let data = this.tableData.map((item) => {
            return {
              ...item,
              hasFollowUp: item.hasFollowUp ? '已随访' : '未随访'
            };
          });
          this.exportExcel(
            '危机值异常项目列表',
            '危机值异常项目列表',
            this.theads,
            data
          );
        })
        .catch(() => {});
    },
    rowClick(row) {
      console.log('[ row ]-373', row);
      if (row) {
        this.id = row.id;
      }
    },

    //删除
    deletes() {
      if (!this.id) {
        this.$message({
          message: '请先选中再删除!',
          type: 'warning',
          showClose: true
        });
        return;
      }

      this.$confirm(`是否确定删除?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax
            .post(this.$apiUrls.DeleteCriticalException, '', {
              query: {
                Id: this.id
              }
            })
            .then((r) => {
              let { success } = r.data;
              if (!success) return;
              this.$message({
                message: '删除成功!',
                type: 'success'
              });
              this.$nextTick(() => {
                this.search();
              });
            });
        })
        .catch(() => {});
    },
    //随访登记跳转到随访登记页面
    jump(row) {
      this.addList = {
        regNo: row.regNo,
        followContent: row.itemName + ':' + row.itemResult,
        deptCode: this.G_userInfo.codeOper.deptCode,
        recorder: this.G_userInfo.codeOper.name,
        abnormalType: 3,
        notifier: row.replyPerson,
        afterFollowUp: row.replyContent
      };
      this.$ajax
        .post(this.$apiUrls.InsertPeFollowUp, this.addList)
        .then((r) => {
          let { success } = r.data;
          if (!success) return;
          this.$nextTick(() => {
            this.$router.push({
              path: '/dataQuery/followUpReg',
              name: 'followUpReg',
              query: {
                regNo: row.regNo
              }
            });
          });
        });
    }
  }
};
</script>
<style lang="less" scoped>
.abnormalDangerList {
  color: #2d3436;
  .main {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 18px 10px;
    font-size: 14px;
    background: #fff;
  }
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    margin-bottom: 18px;
    .pBtn {
      display: flex;
      flex-direction: row;
      & > .el-input {
        margin-right: 20px;
      }
      .pBtn-time {
        display: flex;
        align-items: center;
        font-weight: 600;
        margin-right: 10px;
        label {
          width: 64px;
        }
      }
    }
  }
  .contDiv {
    flex: 1;
    overflow: auto;
    border: 1px solid rgba(178, 190, 195, 0.5);
    border-radius: 4px;
  }
}
</style>
