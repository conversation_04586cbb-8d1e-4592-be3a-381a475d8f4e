<template>
  <div class="historyResult" id="historyResult">
    <div class="historyResult-page">
      <div class="header">
        <p class="header-title">历史结果记录表:</p>
        <p class="pBtn">
          <el-input
            size="small"
            placeholder="请输入体检号"
            clearable
            v-model="regNo"
            class="input"
            @keyup.enter.native="search"
          ></el-input>
          <ButtonGroup
            :btnList="['查询', '导出']"
            @search="search"
          ></ButtonGroup>
        </p>
      </div>
      <!-- <div class="titlewrap">
        <span class="titleSpan1">体检号: {{ recordStatus.regNo }}</span>
        <span class="titleSpan">姓名：{{ recordStatus.name }}</span>
        <span class="titleSpan">性别：{{ recordStatus.sex }}</span>
        <span class="titleSpan">年龄：{{ recordStatus.age }}</span>
        <span class="titleSpan2">单位{{ recordStatus.companyName }}</span>
      </div> -->
      <div class="leftDiv" id="leftDiv">
        <div class="divInfo">
          <p class="pCss">体检综述</p>
          <div class="contTxt">
            <div class="contDiv">
              <div class="contInfos">
                <p class="contP">{{ infoList.examTime }}</p>
                <p class="contP">{{ infoList.examTimeLast }}</p>
              </div>

              <div class="contInfo">
                <div class="cont" style="white-space: pre-wrap">
                  {{ infoList.summary.result }}
                </div>
                <div class="cont" style="white-space: pre-wrap">
                  {{ infoList.summary.resultLast }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="divInfo">
          <p class="pCss">诊断建议</p>
          <div class="contTxt">
            <div class="contDiv">
              <div class="contInfos">
                <p class="contP">{{ infoList.examTime }}</p>
                <p class="contP">{{ infoList.examTimeLast }}</p>
              </div>

              <div class="contInfo">
                <div class="cont" style="white-space: pre-wrap">
                  {{ infoList.suggestion.result }}
                </div>
                <div class="cont" style="white-space: pre-wrap">
                  {{ infoList.suggestion.resultLast }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="divInfo"
          v-for="(item, index) in infoList.examItems"
          :key="index"
          :id="'id' + index"
        >
          <p class="pCss">{{ item.combName }}</p>

          <div class="combDiv">
            <p>
              <span>项目名称</span><span>{{ infoList.examTime }}</span
              ><span>{{ infoList.examTimeLast }}</span>
            </p>
            <p
              class="contDiv"
              v-for="(items, idx) in item.itemResults"
              :key="idx"
            >
              <span>{{ items.itemName }}</span
              ><span>{{ items.result }}</span
              ><span>{{ items.resultLast }}</span>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import PublicTable from '@/components/publicTable.vue';
import ButtonGroup from './components/buttonGroup.vue';
export default {
  name: 'historyResult',
  components: { PublicTable, ButtonGroup },
  data() {
    return {
      regNo: '',
      infoList: {
        examTime: '',
        examTimeLast: '',
        summary: {
          result: '',
          resultLast: ''
        },
        suggestion: {
          result: '',
          resultLast: ''
        },
        examItems: []
      }
    };
  },
  mounted() {},

  methods: {
    search() {
      this.infoList = {
        examTime: '',
        examTimeLast: '',
        summary: {
          result: '',
          resultLast: ''
        },
        suggestion: {
          result: '',
          resultLast: ''
        },
        examItems: []
      };

      if (!this.regNo) {
        this.$message({
          message: '请先输入体检号再查询!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$ajax
        .post(this.$apiUrls.GetHistoryReport, '', {
          query: {
            regNo: this.regNo
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;

          if (returnData.examItems) {
            this.infoList = returnData || {
              examTime: '',
              examTimeLast: '',
              summary: {
                result: '',
                resultLast: ''
              },
              suggestion: {
                result: '',
                resultLast: ''
              },
              examItems: []
            };
          } else {
            this.$message({
              message: '没有历史报告的数据!',
              type: 'warning',
              showClose: true
            });
            return;
          }
          console.log('[ this.infoList ]-234', this.infoList);
        });
    }
  }
};
</script>

<style lang="less" scoped>
.historyResult {
  .historyResult-page {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 10px;
    font-size: 14px;
    color: #2d3436;
    background: #fff;
    border-radius: 4px;
  }
  .header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    height: 60px;
    line-height: 60px;
    .pBtn {
      display: flex;
    }
    .el-input {
      margin-right: 20px;
    }
  }
  .header-title {
    font-size: 18px;
    font-weight: 600;
  }
  .titlewrap {
    display: flex;
    flex-direction: row;
    height: 40px;

    .titleSpan {
      width: 100px;
    }
    .titleSpan1 {
      width: 200px;
    }
    .titleSpan2 {
      flex: 1;
    }
  }
  .leftDiv {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    .divInfo {
      width: 100%;
      min-height: 300px;
      display: flex;
      flex-direction: column;
      border: 1px solid #eee;
      margin-bottom: 18px;
      &:last-child {
        margin-bottom: 0;
      }
      .pCss {
        background: #1770df;
        border-radius: 1px;
        border-radius: 1px;
        line-height: 38px;
        font-family: PingFangSC-Medium;
        font-size: 18px;
        color: #ffffff;
        padding: 0 20px;
      }
      .contInfos {
        display: flex;
        flex-direction: row;
        border-bottom: 1px solid #ccc;
        .contP {
          line-height: 32px;
          border-bottom: 1px solid #eee;
          padding: 0 20px;
          flex: 1;
          border-right: 1px solid #ccc;
        }
      }
      .contTxt {
        flex: 1;
        display: flex;
        flex-direction: row;
        overflow: auto;
        .contDiv {
          flex: 1;
          flex-shrink: 0;
          border: 1px solid #eee;
          display: flex;
          flex-direction: column;
          .contInfo {
            flex: 1;
            display: flex;
            flex-direction: row;

            .contP {
              line-height: 32px;
              border-bottom: 1px solid #eee;
              padding: 0 20px;
              flex: 1;
              border-right: 1px solid #ccc;
            }
            .cont {
              padding: 0 20px;
              flex: 1;
              flex-shrink: 0;
              overflow: auto;
              border-right: 1px solid #ccc;
            }
          }
        }
      }
      .combDiv {
        flex: 1;
        overflow: auto;
        p {
          display: flex;
          flex-direction: row;
          line-height: 32px;
          border-bottom: 1px dashed #eee;
          padding: 0 20px;
          border-bottom: 1px solid #ccc;
          & > span {
            flex: 1;
          }
        }
      }
    }
  }
}
</style>
