<template>
  <div class="batchAddDel">
    <div class="leftBody">
      <ul class="record_head">
        <li>
          <div class="every_inp" style="flex: 1">
            <label>单位</label>
            <p style="width: 100%">
              <el-cascader
                style="width: 100%"
                ref="company_cascader_ref"
                v-model="companyVal"
                :options="companyList"
                :props="{ multiple: false }"
                :filter-method="filterMethod"
                clearable
                filterable
                size="small"
                collapse-tags
                @change="companyChange"
              >
              </el-cascader>
            </p>
          </div>
          <div class="every_inp">
            <label>部门</label>
            <p>
              <el-select
                v-model.trim="companyDeptCode"
                placeholder="请选择"
                size="small"
                clearable
                filterable
                :disabled="isHavue"
                @change="search"
              >
                <el-option
                  v-for="(item, index) in companyDeptList"
                  :key="index"
                  :label="item.deptName"
                  :value="item.deptCode"
                ></el-option>
              </el-select>
            </p>
          </div>
        </li>
        <li>
          <div class="every_inp">
            <label>套餐</label>
            <p>
              <el-select
                style="width: 215px"
                v-model.trim="clusVal"
                placeholder="请选择"
                size="small"
                clearable
                filterable
                @change="search"
              >
                <el-option
                  v-for="(item, index) in ClusterList"
                  :key="index"
                  :value="item.clusterCode"
                  :label="item.clusterName"
                ></el-option>
              </el-select>
            </p>
          </div>
          <el-input
            size="small"
            placeholder="请输入体检号/姓名搜索"
            v-model="userSearchVal"
            @input="userSearchChange"
          ></el-input>
          <el-button
            size="mini"
            class="blue_btn"
            icon="iconfont icon-search"
            style="width: 90px"
            @click="search"
            >查询</el-button
          >
        </li>
      </ul>
      <div class="record_table">
        <PublicTable
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          :tableLoading.sync="loading"
          :columnWidth="columnWidth"
          isCheck
          @selectionChange="handleSelRow"
          v-model="addDelInfo.regNoArray"
          ref="userList_Ref"
        >
          <template #age="{ scope }">
            <div>
              {{ scope.row.age + enum_ageUnit[scope.row.ageUnit] }}
            </div>
          </template>
          <template #sex="{ scope }">
            <div>
              {{ G_EnumList['Sex'][scope.row.sex] }}
            </div>
          </template>
        </PublicTable>
      </div>
    </div>
    <div class="rightBody">
      <ul class="record_head">
        <li>
          <div class="every_inp">
            <p>
              <el-select
                v-model.trim="itemCls"
                placeholder="所有分类"
                size="small"
                clearable
                filterable
                @clear="getClsList"
              >
                <el-option
                  v-for="(item, index) in itemClsList"
                  :key="index"
                  :value="item.clsCode"
                  :label="item.clsName"
                  @click.native="getClsList(item.value)"
                ></el-option>
              </el-select>
            </p>
          </div>
          <div class="every_inp">
            <p>
              <el-input
                v-model.trim="searchVal"
                placeholder="组合名称/代码"
                size="small"
                clearable
                @keyup.enter.native="getClsList"
                @clear="getClsList"
              >
              </el-input>
            </p>
          </div>
          <div
            style="margin: 0 10px"
            v-if="G_config.physicalMode.includes('职检')"
          >
            <el-checkbox v-model="addDelInfo.isOccupation">职</el-checkbox>
          </div>

          <el-button
            size="mini"
            class="blue_btn"
            icon="iconfont icon-xinjian"
            style="width: 90px"
            @click="add"
            >添加</el-button
          >
          <el-button
            size="mini"
            class="red_btn"
            icon="iconfont icon-shanchu"
            style="width: 90px"
            @click="del"
            >删除</el-button
          >
        </li>
      </ul>
      <div class="combo_group">
        <PublicTable
          :viewTableList.sync="searchComboList"
          :theads.sync="comboTheads"
          :tableLoading.sync="loading"
          :columnWidth="comboColumnWidth"
          :tableRowClassName="tableRowClassName"
          isCheck
          :cell_red="comboCell_red"
          @selectionChange="selectionRow"
          ref="combTable_Ref"
          v-model="addDelInfo.combCodeArray"
        >
        </PublicTable>
      </div>
    </div>
    <div class="my_mask" v-if="G_BatchAddOrDeleteShow">
      <el-progress
        type="circle"
        :percentage="G_BatchAddOrDeleteData.currentProgressPercentage"
      ></el-progress>
      <p class="tips_p">{{ G_BatchAddOrDeleteData.message }}</p>
    </div>
  </div>
</template>

<script>
import PublicTable from '../../components/publicTable.vue';
import { mapGetters, mapMutations } from 'vuex';
import { dataUtils } from '../../common';
export default {
  name: 'batchAddDel',
  components: {
    PublicTable
  },
  data() {
    return {
      leftScrollTop: 0,
      rightScrollTop: 0,
      companyDeptCode: '',
      companyDeptList: [],
      isHavue: true,
      companyVal: '', //单位
      comTimesVal: null, //体检次数
      clusVal: '', //套餐
      comTimesList: [],
      ClusterList: [],
      companyList: [],
      // 表格数据
      tableData: [],
      fixed_tableDate: [],
      loading: false,
      theads: {
        registerTime: '体检日期',
        regNo: '体检号',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        companyName: '工作单位'
      },
      enum_ageUnit: {
        null: '',
        0: '岁',
        1: '月'
      },
      columnWidth: {
        registerTime: 170,
        regNo: 170,
        companyName: 220
      },
      // 组合
      itemClsList: [],
      itemCls: '',
      searchVal: '',
      comboTheads: {
        combCode: '组合代码',
        combName: '组合名称',
        clsName: '项目分类',
        price: '价格'
      },

      comboList: [], //组合列表
      searchComboList: [],
      comboCell_red: ['price'],
      comboColumnWidth: {
        combCode: '100',
        clsName: '100',
        price: '70'
      },
      //添加删除传参
      addDelInfo: {
        regNoArray: [],
        combCodeArray: [],
        isOccupation: false
      },
      userSearchVal: '',
      containComboCode: []
    };
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_BatchAddOrDeleteShow',
      'G_BatchAddOrDeleteData',
      'G_config'
    ]),
    ...mapMutations(['M_BatchAddOrDeleteShow'])
  },
  methods: {
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    //获取单位下拉
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    //获取部门数据
    getDepartList(companyCode) {
      this.companyDeptCode = '';
      this.isHavue = true;
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanyDepartment, {
          companyCode: companyCode || '',
          deptCode: '',
          deptName: ''
        })
        .then((r) => {
          this.isHavue = false;
          //console.log("r", r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.companyDeptList = returnData || [];
        });
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) {
        this.isHavue = true;
        this.clusVal = '';
        this.search();
        return;
      }
      this.getDepartList(data[0]);
      this.getCluster(data[0], data[1]);
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.comTimesVal = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.search();
    },
    //获取体检次数
    getSimpleCompanyTimes(companyCode) {
      this.comTimesList = [];
      this.comTimesVal = null;
      this.$ajax
        .post(this.$apiUrls.GetSimpleCompanyTimes, '', {
          query: { companyCode: companyCode }
        })
        .then((r) => {
          console.log('rrrr', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.$nextTick(function () {
            this.comTimesList = [];
            if (returnData.length > 0) {
              returnData.map((item) => {
                this.comTimesList.push(item.companyTimes);
              });
              this.comTimesVal = this.comTimesList[0];
              this.getCluster(this.companyVal?.[0] || '', this.comTimesVal); //
              console.log('[ this.comTimesVal ]-265', this.comTimesVal);
            }
          });
        });
    },
    getComTimes(val) {
      console.log('[ val ]-265', val);
      this.getSimpleCompanyTimes(val);
    },
    getCompanyCluster(val) {
      console.log('[ val ]-284', val);
      this.getCluster(this.companyVal?.[0] || '', val);
    },
    //获取套餐：
    getCluster(companyCode, companyTimes) {
      this.ClusterList = [];
      this.clusVal = '';
      this.$ajax
        .post(this.$apiUrls.GetCompanyCluster, '', {
          query: { companyCode: companyCode, companyTimes: companyTimes }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$nextTick(function () {
            this.ClusterList = returnData || [];
            // if (this.ClusterList.length > 0) {
            //   this.clusVal = this.ClusterList[0].clusterCode;
            // }
          });
        });
    },
    //获取所有分类
    getItemCls() {
      this.$ajax.post(this.$apiUrls.ItemCls).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.itemClsList = returnData || [];
      });
    },
    // 组合列表
    getComboList() {
      this.$ajax.post(this.$apiUrls.ItemComb_ItemCls).then((r) => {
        console.log('ReadCompanyClusAndCombs: ', r);
        let { success, returnData } = r.data;
        if (!success) return;
        let data = [];
        returnData.map((item) => {
          // if (item.reportShow==true) {
          data.push(item);
          // }
        });
        this.comboList = data || [];
        this.searchComboList = data || [];
      });
    },
    //查询左边列表
    search() {
      this.$ajax
        .post(this.$apiUrls.GetBatchAddOrDelete, {
          companyCode: this.companyVal?.[0] || '',
          companyTimes: this.companyVal?.[1] || -1,
          clusterCode: this.clusVal,
          companyDeptCode: this.companyDeptCode
        })
        .then((r) => {
          console.log('r', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.tableData = returnData || [];
          this.fixed_tableDate = returnData || [];
        });
    },
    //筛选右边组合列表
    getClsList() {
      this.searchIpt = this.searchVal; //获取组件文本框的值
      this.searchSel = this.itemCls; //下拉
      this.order = `item.combCode?.includes(searchVal)||
      item.combName?.includes(searchVal)`;
      this.orderSel = `item.clsCode?.includes(searchVal)`;
      console.log(this.searchIpt, this.searchSel);
      if (!this.searchIpt && !this.searchSel) {
        this.searchComboList = this.comboList;
        this.mealChange();
        return;
      }
      var newTableData = [];
      //有下拉有文本
      if (this.searchSel && this.searchIpt) {
        this.comboList.map((item) => {
          let flag = new Function(
            'item',
            'searchVal',
            `return ${this.orderSel}`
          )(item, this.searchSel);
          if (flag) {
            newTableData.push(item);
          }
        });
        var newData = [];
        newTableData.map((item) => {
          let flag = new Function('item', 'searchVal', `return ${this.order}`)(
            item,
            this.searchIpt
          );
          if (flag) {
            newData.push(item);
          }
        });
        this.searchComboList = newData;
        console.log('[ this.searchComboList ]-375', this.searchComboList);
      }
      //只有下拉
      if (this.searchSel && !this.searchIpt) {
        this.comboList.map((item) => {
          let flag = new Function(
            'item',
            'searchVal',
            `return ${this.orderSel}`
          )(item, this.searchSel);
          if (flag) {
            newTableData.push(item);
          }
        });
        this.searchComboList = newTableData;
        console.log('[ this.searchComboList ]-390', this.searchComboList);
      }
      //只有文本
      if (this.searchIpt && !this.searchSel) {
        newTableData = [];
        this.comboList.map((item) => {
          let flag = new Function('item', 'searchVal', `return ${this.order}`)(
            item,
            this.searchIpt
          );
          if (flag) {
            newTableData.push(item);
          }
        });
        this.searchComboList = newTableData;
        console.log('[ this.searchComboList ]-404', this.searchComboList);
        this.mealChange();
      }
    },
    //左边勾选行
    handleSelRow(val, checkList) {
      this.addDelInfo.regNoArray = [];
      checkList.map((item) => {
        if (item.regNo != '') {
          this.addDelInfo.regNoArray.push(item.regNo);
        }
      });
    },
    //右边勾选行
    selectionRow(val, checkList) {
      this.addDelInfo.combCodeArray = [];
      checkList.map((item) => {
        if (item.combCode != '') {
          this.addDelInfo.combCodeArray.push(item.combCode);
        }
      });
    },
    //添加
    add() {
      if (this.addDelInfo.regNoArray.length < 1) {
        this.$message({
          message: '请先勾选要批添加的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.addDelInfo.combCodeArray.length < 1) {
        this.$message({
          message: '请先勾选要批添加的组合!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      console.log('this.addDelInfo', this.addDelInfo);
      this.$ajax
        .post(this.$apiUrls.BatchAddOrDeleteNew + '/batchadd', this.addDelInfo)
        .then((r) => {
          //console.log("r", r);
          let { success } = r.data;
          if (!success) return;
          this.$refs.combTable_Ref.$refs.tableCom_Ref.clearSelection();
          // this.$message({
          //   message: "添加成功!",
          //   type: "success",
          //   showClose: true,
          // });
          // this.search();
          // this.getComboList();
        })
        .catch((e) => {
          this.M_BatchAddOrDeleteShow({ flag: false, msgData: {} });
        });
    },
    //删除
    del() {
      if (this.addDelInfo.regNoArray.length < 1) {
        this.$message({
          message: '请先勾选要批删除的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.addDelInfo.combCodeArray.length < 1) {
        this.$message({
          message: '请先勾选要批删除的组合!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      console.log('this.addDelInfo', this.addDelInfo);
      if (this.addDelInfo.regNoArray.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.$ajax
              .post(
                this.$apiUrls.BatchAddOrDeleteNew + '/batchdelete',
                this.addDelInfo
              )
              .then((r) => {
                let { success } = r.data;
                if (!success) return;
                this.$refs.combTable_Ref.$refs.tableCom_Ref.clearSelection();
                // this.$message({
                //   showClose: true,
                //   message: "删除成功",
                //   type: "success",
                // });
                // this.search();
                // this.getComboList();
              })
              .catch((e) => {
                this.M_BatchAddOrDeleteShow({ flag: false, msgData: {} });
              });
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    // 搜索体检人
    userSearchChange() {
      let tableData = this.fixed_tableDate.filter((item) => {
        return (
          item.regNo.includes(this.userSearchVal) ||
          item.name.includes(this.userSearchVal)
        );
      });
      this.tableData = tableData;
    },
    // 套餐改变的回调
    mealChange() {
      console.log(this.clusVal);
      this.$ajax
        .paramsPost(this.$apiUrls.ReadCompanyClusAndCombs, {
          clusterCode: this.clusVal
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;

          let combList = dataUtils.deepCopy(this.searchComboList);
          this.containComboCode = returnData.companyComb.map(
            (item) => item.combCode
          );
          console.log(this.containComboCode);
          let lenIdx = 0;
          let checkCombList = [];
          combList.some((item, idx) => {
            if (this.containComboCode.includes(item.combCode)) {
              lenIdx++;
              combList.splice(idx, 1);
              combList.unshift(item);
              checkCombList.push(item);
            }
            if (lenIdx >= this.containComboCode.length) return true;
          });
          this.searchComboList = combList;
        });
    },
    // 套餐内的组合标记
    tableRowClassName({ row, rowIndex }) {
      // console.log(row,rowIndex);
      if (this.containComboCode.includes(row.combCode.trim())) {
        return 'have_meal';
      } else {
        return 'no_meal';
      }
    }
  },
  watch: {
    clusVal: {
      handler(n, o) {
        this.mealChange();
      }
    }
  },
  mounted() {
    this.getCompany();
    this.getComboList();
    this.getItemCls();
    this.addDelInfo.isOccupation = this.G_config.physicalOccupationMode;
    this.$refs.userList_Ref.$refs.tableCom_Ref.bodyWrapper.addEventListener(
      'scroll',
      (res) => {
        this.leftScrollTop = res.target.scrollTop;
      },
      true
    );
    this.$refs.combTable_Ref.$refs.tableCom_Ref.bodyWrapper.addEventListener(
      'scroll',
      (res) => {
        this.rightScrollTop = res.target.scrollTop;
      },
      true
    );
  },
  activated() {
    this.$nextTick(() => {
      setTimeout(() => {
        this.$refs.userList_Ref.$refs.tableCom_Ref.bodyWrapper.scrollTop =
          this.leftScrollTop;
        this.$refs.combTable_Ref.$refs.tableCom_Ref.bodyWrapper.scrollTop =
          this.rightScrollTop;
      }, 200);
    });
  }
};
</script>
<style lang="less" scoped>
.batchAddDel {
  height: 100%;
  width: 100%;
  overflow: auto;
  border-radius: 4px;
  padding: 5px 15px 15px;
  display: flex;
  //   overflow: auto;
  .leftBody {
    background-color: #fff;
    width: 700px;
    height: 100%;
    display: flex;
    flex-direction: column;
    margin-right: 20px;
    padding: 15px;
  }
  .rightBody {
    background-color: #fff;
    padding: 15px;
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    .combo_group {
      flex: 1;
      height: calc(100% - 50px);
    }
    .tabs_wrap {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    .el-tab-pane {
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: auto;
    }
    .table_wrap {
      flex: 1;
      overflow: auto;
    }
    /deep/.el-tabs__content {
      flex: 1;
    }
  }
  .back_wrap {
    margin-bottom: 25px;

    span {
      font-size: 18px;
      font-weight: 600;
      border-left: 2px solid #001618;
      color: #001618;
      margin-left: 15px;
      padding-left: 15px;
      line-height: 19px;
      display: inline-block;
    }
  }
  /deep/.el-tabs--border-card > .el-tabs__header {
    background: rgba(23, 112, 223, 0.2);

    .is-active {
      color: #2d3436;
    }
  }

  /deep/.el-tabs__item {
    color: rgba(45, 52, 54, 0.6);
    font-size: 18px;
    font-weight: 500;
    height: 48px;
    line-height: 48px;
  }
  .record_head {
    li {
      display: flex;
      margin-bottom: 5px;
      align-items: center;
    }
    .every_inp:first-of-type {
      margin-right: 10px;
    }
    .every_inp {
      display: flex;
      align-items: center;
      width: 40%;

      label {
        margin-right: 10px;
        width: 40px;
        text-align: right;
      }

      p {
        flex: 1;
      }

      &:nth-child(4) {
        flex: 1;
      }
    }
    .record_li {
      height: 32px;
      align-items: center;
      // padding-left: 20px;
      span {
        margin-left: 28px;
        font-size: 14px;
        vertical-align: top;
        display: inline-block;
        font-weight: 600;
        margin-top: -5px;
        &:nth-child(1) {
          margin-left: 0px !important;
        }
        &:nth-child(3) {
          color: #d63031;
        }
        &:nth-child(4) {
          color: #fab63b;
        }
        &:nth-child(5) {
          color: #1770df;
        }
        &:nth-child(6) {
          color: #3cb34f;
        }
        &:nth-child(7) {
          color: #7364f4;
        }
        &:nth-child(8) {
          color: #3cb34f;
        }
      }
    }
    .operate_li {
      & > div {
        height: 48px;
        display: flex;
        align-items: center;
        margin-right: 28px;
        padding: 0 18px;
        border-radius: 2px;
        &:nth-child(1) {
          background: rgba(23, 112, 223, 0.1);
          width: 250px;
        }
        &:nth-child(3) {
          background: rgba(60, 179, 79, 0.1);
        }
        &:nth-child(4) {
          background: rgba(214, 48, 49, 0.1);
        }
      }
      .record_status {
        margin-left: 28px;
        font-size: 14px;
        vertical-align: top;
        display: inline-block;
        font-weight: 600;
        margin-top: -5px;
        &:nth-child(1) {
          margin-left: 0px !important;
        }
        &:nth-child(3) {
          color: #d63031;
        }
        &:nth-child(4) {
          color: #fab63b;
        }
        &:nth-child(5) {
          color: #1770df;
        }
        &:nth-child(6) {
          color: #3cb34f;
        }
        &:nth-child(7) {
          color: #7364f4;
        }
        &:nth-child(8) {
          color: #3cb34f;
        }
      }
    }
  }
  .record_table {
    flex: 1;
    flex-shrink: 0;
    height: calc(100% - 50px);
  }
}
</style>
