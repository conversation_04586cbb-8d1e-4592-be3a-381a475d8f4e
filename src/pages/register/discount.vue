<template>
  <div class="discount_page">
    <ul class="search_ul">
      <li>
        <label>体检号</label>
        <span>
          <el-input
            clearable
            @keyup.enter.native="search"
            v-model.trim="searchInfo.regNo"
            placeholder="请输入体检号"
            size="small"
          ></el-input>
        </span>
      </li>
      <li>
        <label>姓名</label>
        <span>
          <el-input
            v-focus
            clearable
            @keyup.enter.native="search"
            v-model.trim="searchInfo.name"
            placeholder="请输入姓名"
            size="small"
          ></el-input>
        </span>
      </li>
      <li>
        <label>身份证</label>
        <span>
          <el-input
            clearable
            @keyup.enter.native="search"
            v-model.trim="searchInfo.cardNo"
            placeholder="请输入身份证号"
            size="small"
          ></el-input>
        </span>
      </li>
      <el-button
        class="yellow_btn"
        size="mini"
        icon="iconfont icon-shenfenzheng"
        >身份证</el-button
      >
      <el-button
        class="blue_btn"
        size="mini"
        icon="iconfont icon-search"
        style="margin-left: 18px"
        @click="search"
        >查询</el-button
      >
    </ul>
    <!-- 个人信息 -->
    <div class="person_info">
      <el-image
        style="width: 112px; height: 140px; margin-right: 28px"
        :src="personInfo.photoUrl"
      >
      </el-image>
      <div class="info_column">
        <p>
          <label>体检号：</label>
          <span>{{ personInfo.regNo }}</span>
        </p>
        <p>
          <label>出生日期：</label>
          <span>{{ personInfo.birthday }}</span>
        </p>
        <p>
          <label>证件号码：</label>
          <span>{{ personInfo.cardNo }}</span>
        </p>
      </div>
      <div class="info_column">
        <p>
          <label>姓名：</label>
          <span>{{ personInfo.name }}</span>
        </p>
        <p>
          <label>籍贯：</label>
          <span>{{ personInfo.nativePlace }}</span>
        </p>
        <!-- <p>
                    <label>体检次数：</label>
                    <span>{{personInfo.regNo}}</span>
                </p> -->
        <p>
          <label>性别：</label>
          <span>{{
            G_EnumList['Sex'] ? G_EnumList['Sex'][personInfo.sex] : ''
          }}</span>
        </p>
      </div>
      <div class="info_column">
        <!-- <p>
                    <label>性别：</label>
                    <span>{{G_EnumList['Sex']?G_EnumList['Sex'][personInfo.sex]:'未知'}}</span>
                </p> -->
        <p>
          <label>婚姻状况：</label>
          <span>{{
            G_EnumList['MarryStatus']
              ? G_EnumList['MarryStatus'][personInfo.marryStatus]
              : ''
          }}</span>
        </p>
        <p>
          <label>体检套餐：</label>
          <span>{{ personInfo.clusName }}</span>
        </p>
      </div>
      <div class="info_column">
        <p>
          <label>年龄：</label>
          <span>{{ personInfo.age }}</span>
        </p>
        <p>
          <label>联系电话：</label>
          <span>{{ personInfo.tel }}</span>
        </p>
      </div>
    </div>
    <!-- 组合列表 -->
    <div class="combo_wrap">
      <ul>
        <li style="margin-right: 10px">
          <label>已选组合：</label>
          <span>{{ selectionComb.length }}</span>
        </li>
        <li>
          <el-input
            clearable
            @input="comboSearch"
            v-model.trim="searchVal"
            placeholder="请输入内容"
            size="small"
            style="margin-right: 10px; width: 200px"
          ></el-input>
          <el-button
            class="blue_btn"
            icon="iconfont icon-search"
            size="mini"
            style="margin-right: 18px"
            >搜索
          </el-button>
          <el-button
            @click="fixedPriceBtn"
            class="yellow_btn"
            icon="iconfont icon-jiagepaixu"
            size="mini"
            style="margin: 0 10px 0 0"
            >一口价
          </el-button>
          <el-button
            @click="cancelDiscount(null)"
            icon="iconfont icon-huifuxitongmoren"
            size="mini"
            style="margin: 0 10px 0 0"
            >取消优惠
          </el-button>
          <!-- <el-input placeholder="请输入内容" size="small" style="width: 120px;"></el-input> -->
        </li>
        <li class="price_li">
          <div>
            <p>
              项目价格：
              <i>{{ handlePrice(feeList.combTotal) }}</i>
            </p>
            <p>
              材料费：
              <i>{{ handlePrice(feeList.materialTotal) }}</i>
            </p>
          </div>
          <div>
            <p>
              未缴费：
              <i>{{ handlePrice(feeList.unpaidAmount) }}</i>
            </p>
            <p>
              <span
                >总价格：<i>{{ handlePrice(feeList.orderTotal) }}</i></span
              >
            </p>
          </div>
        </li>
        <!-- <li>
                    <el-button class="red_btn" icon="iconfont icon-search" size="mini">预计价</el-button>
                </li> -->
      </ul>
      <div class="table_div">
        <PublicTable
          ref="combTable_Ref"
          :columnAlign="{ price: 'right', originalPrice: 'right' }"
          :columnWidth="columnWidth"
          :reserveSelection="true"
          rowKey="combCode"
          @selectAll="selectAll"
          @select="select"
          :selectable="combSelectable"
          :theads="theads"
          :viewTableList="viewTableList"
          :tableRowClassName="tableRowClassName"
          :isStripe="false"
          :cell_red="cell_red"
          isCheck
        >
          <template #discountClickHeader="{ scope }">
            <div class="cell_blue discount_div" @click="batchDiscountBtn('')">
              打折
            </div>
          </template>
          <template #discountClick="{ scope }">
            <div
              class="cell_blue discount_div"
              @click="
                scope.row.payStatus == 0
                  ? !scope.row.discountOperName
                    ? batchDiscountBtn(scope.row)
                    : cancelDiscount(scope.row)
                  : ''
              "
            >
              {{
                scope.row.payStatus == 0
                  ? !scope.row.discountOperName
                    ? '打折'
                    : '取消打折'
                  : '-'
              }}
            </div>
          </template>
          <template #price="{ scope }">
            {{ handlePrice(scope.row.price) }}
          </template>
          <template #originalPrice="{ scope }">
            {{ handlePrice(scope.row.originalPrice) }}
          </template>
          <template #payStatus="{ scope }">
            <div
              :class="scope.row.payStatus == 0 ? 'noPay_text' : 'havePay_text'"
            >
              {{ G_EnumList['PayStatus'][scope.row.payStatus] }}
            </div>
          </template>
        </PublicTable>
      </div>
    </div>
    <!-- 搜索弹窗 -->
    <el-dialog title="提示" :visible.sync="dialogVisible" width="70%">
      <div class="search_table" style="height: 500px; overflow: auto">
        <PublicTable
          :theads="searchTheads"
          :viewTableList.sync="searchTableList"
          :columnWidth="searchColumnWidth"
          @rowDblclick="rowDblclick"
          @currentChange="currentChange"
        >
          <template #sex="{ scope }">
            <div>
              {{ G_EnumList['Sex'][scope.row.sex] }}
            </div>
          </template>
        </PublicTable>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="searchConfim">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 一口价和打折的登录 -->
    <el-dialog
      title="登录"
      :visible.sync="loginPropShow"
      top="20%"
      width="360px"
    >
      <el-form
        :model="loginInfo"
        :rules="rules"
        ref="ruleForm"
        label-width="60px"
      >
        <el-form-item label="账号" prop="operatorCode">
          <el-input
            size="small"
            @keyup.enter.native="login"
            v-focus
            v-model="loginInfo.operatorCode"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
            type="password"
            @keyup.enter.native="login"
            size="small"
            show-password
            v-model="loginInfo.password"
            autocomplete="off"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="loginPropShow = false">取 消</el-button>
        <el-button type="primary" @click="login">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 一口价弹窗 -->
    <el-dialog
      title="一口价管理"
      :close-on-click-modal="false"
      :visible.sync="fixedPriceShow"
      top="20%"
      width="360px"
    >
      <el-form
        :model="fixedInfo"
        :rules="fixedPriceRules"
        ref="fixedPrice_Ref"
        label-width="80px"
      >
        <div>
          该套餐的一口价设置范围为：<span
            class="cell_blue"
            style="font-weight: 600"
            >{{ C_fixedPrice }}</span
          >
        </div>
        <el-form-item label="一口价" prop="fixedPrice">
          <el-input
            size="small"
            v-focus
            @keyup.enter.native="fixedPriceConfirm"
            v-model.trim="fixedInfo.fixedPrice"
            autocomplete="off"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="fixedPriceShow = false">取 消</el-button>
        <el-button type="primary" @click="fixedPriceConfirm">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 打折弹窗 -->
    <el-dialog
      title="打折管理"
      :close-on-click-modal="false"
      :visible.sync="discountShow"
      top="20%"
      width="360px"
    >
      <el-form
        :model="fixedInfo"
        :rules="discountRules"
        ref="discount_Ref"
        label-width="60px"
      >
        <div>
          打折范围为：<span class="cell_blue" style="font-weight: 600">{{
            C_fixedPrice
          }}</span>
        </div>
        <el-form-item label="折扣" prop="discount">
          <el-input
            size="small"
            v-focus
            @keyup.enter.native="discountConfirm"
            v-model.trim="fixedInfo.discount"
            autocomplete="off"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="discountShow = false">取 消</el-button>
        <el-button type="primary" @click="discountConfirm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import PublicTable from '@/components/publicTable.vue';
import { mapGetters } from 'vuex';
import { dataUtils } from '@/common';
import { _debounce } from '@/common/throttle';

export default {
  name: 'discount',
  components: {
    PublicTable
  },
  computed: {
    ...mapGetters(['G_EnumList']),
    C_fixedPrice() {
      if (this.discountOrFixedPrice == 'discount') {
        // let discount = this.authInfo.maxDeductionPrice?this.authInfo.maxDeductionPrice.toString().split('.')[1]:'00';
        // return `${discount?.length<2?this.authInfo.maxDeductionPrice+'0':this.authInfo.maxDeductionPrice}-1.00`
        //单个打折取maxDiscountPrice,当maxDiscountPrice大于1时默认加0.00-0.maxDiscountPrice,等于1默认0.00-1.00
        if (this.authInfo.maxDiscountPrice > 1) {
          return `0.00-0.${this.authInfo.maxDiscountPrice}`;
        } else {
          return '0.00-1.00';
        }
      }
      let price = 0;
      // console.log(this.feeList,this.authInfo.maxDeductionPrice);
      console.log();
      let unpaidAmount = ~~(
        this.feeList.unpaidAmount * 100 -
        this.feeList.materialTotal * 100
      ); //未缴费减去材料费
      let maxFixedPrice = unpaidAmount - this.authInfo.maxDeductionPrice * 100; //最大折扣价为未缴总价减去最大折扣价
      if (maxFixedPrice <= 0) {
        price = `0.00-${this.feeList.unpaidAmount}`;
      } else {
        price = `${(maxFixedPrice / 100).toFixed(2)}-${(unpaidAmount / 100).toFixed(2)}`;
      }
      return price;
    }
  },
  data() {
    var fixedPriceRange = (rule, value, callback) => {
      let valArr = this.C_fixedPrice.split('-');
      let minVal = Number(valArr[0]);
      let maxVal = Number(valArr[1]);
      let valNum = Number(value);
      if (!valNum) {
        callback(new Error(`内容必须为数字`));
        return;
      }
      if (valNum >= minVal && valNum <= maxVal) {
        callback();
      } else {
        callback(new Error(`请输入${this.C_fixedPrice}范围内的值`));
      }
    };
    return {
      columnWidth: {
        price: '100',
        discountClick: '100',
        discount: '60',
        originalPrice: '100'
      },
      searchVal: '',
      searchInfo: {
        name: '',
        cardNo: '',
        regNo: ''
      },
      personInfo: {
        age: '',
        birthday: '',
        cardNo: '',
        clusName: '',
        marryStatus: '',
        name: '',
        nativePlace: '',
        photoUrl: '',
        regNo: '',
        sex: '',
        tel: ''
      },
      theads: {
        combCode: '组合代码',
        combName: '组合名称',
        discount: '折扣',
        discountClick: '打折',
        price: '单价',
        originalPrice: '原价',
        payStatus: '支付状态',
        discountOperName: '打折医生'
      },
      cell_red: ['price', 'originalPrice'],
      viewTableList: [],
      fixed_viewTableList: [],
      dialogVisible: false,
      searchTableList: [],
      searchTheads: {
        regNo: '体检号',
        name: '姓名',
        age: '年龄',
        sex: '性别',
        cardNo: '证件号',
        tel: '手机号码',
        clusName: '套餐名称'
      },
      searchColumnWidth: {
        sex: '50',
        age: '50',
        regNo: '130',
        cardNo: '190'
      },
      feeList: {
        combTotal: 0,
        materialTotal: 0,
        unpaidAmount: 0,
        orderTotal: 0
      },
      idxRow: {},
      loginPropShow: false,
      loginInfo: {
        operatorCode: '',
        password: ''
      },
      rules: {
        operatorCode: [
          { required: true, message: '请输入账号', trigger: 'blur' }
        ],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
      },
      // 一口价
      fixedPriceShow: false,
      fixedPriceRules: {
        fixedPrice: [
          { required: true, message: '请输入内容', trigger: 'blur' },
          ,
          { validator: fixedPriceRange, trigger: 'blur' }
        ]
      },
      fixedInfo: {
        fixedPrice: '',
        discount: ''
      },
      authInfo: {},
      // 打折
      discountRules: {
        discount: [
          { required: true, message: '请输入内容', trigger: 'blur' },
          { validator: fixedPriceRange, trigger: 'blur' }
        ]
      },
      discountShow: false,
      discountOrFixedPrice: 'fixedPrice',
      selectionComb: [],
      rowDiscount: {}
    };
  },
  methods: {
    handlePrice: dataUtils.handlePrice,
    tableRowClassName({ row, rowIndex }) {
      if (row.payStatus == 0) {
        return 'no_pay';
      } else {
        return 'have_pay';
      }
    },
    // 搜索
    search() {
      if (
        !this.searchInfo.regNo &&
        !this.searchInfo.name &&
        !this.searchInfo.cardNo
      ) {
        this.$message({
          message: '请输入搜索内容！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let datas = {};
      if (!this.searchInfo.regNo) {
        // 身份证和姓名搜索
        datas = {
          name: this.searchInfo.name,
          cardNo: this.searchInfo.cardNo
        };
        console.log(datas);
        this.$ajax
          .post(this.$apiUrls.GetPersonInfo, '', {
            query: datas
          })
          .then((r) => {
            let { success, returnData } = r.data;
            if (!success) return;
            console.log(returnData);
            this.dialogVisible = true;
            this.$nextTick(() => {
              this.searchTableList = returnData;
            });
          });
      } else {
        this.regNoSearch(this.searchInfo.regNo);
      }
    },
    // 体检号搜索
    regNoSearch(regNo) {
      // 体检号搜索
      let datas = {
        regNo: regNo
      };
      this.personInfo = {};
      console.log(datas);
      this.$ajax
        .post(this.$apiUrls.GetRegisterInfoAndComb, '', {
          query: datas
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.personInfo = returnData.registerInfo;
          this.viewTableList = returnData.combs;
          this.fixed_viewTableList = JSON.parse(
            JSON.stringify(this.viewTableList)
          );
          this.feeList = returnData.feeList;
          this.dialogVisible = false;
          this.idxRow = {};
          this.selectionComb = [];
          this.$refs.combTable_Ref.$refs.tableCom_Ref.clearSelection();
        });
    },
    // 搜索弹窗的表格双击回调
    rowDblclick(row) {
      this.regNoSearch(row.regNo);
    },
    // 搜索弹窗的表格当前行改变的回调
    currentChange(row) {
      console.log(row);
      this.idxRow = row;
    },
    // 搜索弹窗的确定
    searchConfim() {
      this.dialogVisible = false;
      this.regNoSearch(this.idxRow.regNo);
    },
    // 一口价按钮
    fixedPriceBtn() {
      // let selection = this.$refs.combTable_Ref.$refs.tableCom_Ref.selection
      if (!this.personInfo.regNo) {
        this.$message({
          message: '请先搜索订单！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.discountOrFixedPrice = 'fixedPrice';
      this.loginPropShow = true;
      this.$nextTick(() => {
        this.resetForm('ruleForm');
      });
    },
    // 登录
    login() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.$ajax
            .post(this.$apiUrls.GetOperatorDiscount, '', {
              query: this.loginInfo
            })
            .then((r) => {
              let { success, returnData } = r.data;
              if (!success) return;
              this.loginPropShow = false;
              this.authInfo = returnData;
              if (this.discountOrFixedPrice == 'discount') {
                this.discountShow = true;
                this.$nextTick(() => {
                  this.resetForm('discount_Ref');
                });
                return;
              }
              this.fixedPriceShow = true;
              this.$nextTick(() => {
                this.resetForm('fixedPrice_Ref');
              });
            });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    // 重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    // 一口价的确定按钮
    fixedPriceConfirm() {
      this.$refs['fixedPrice_Ref'].validate((valid) => {
        if (valid) {
          let datas = {
            regNo: this.personInfo.regNo,
            discountOperCode: this.authInfo.operatorCode,
            discountOperName: this.authInfo.name,
            fixedPrice: this.fixedInfo.fixedPrice
          };
          console.log(datas);
          this.$ajax.post(this.$apiUrls.FixedPrice, datas).then((r) => {
            let { success, returnData } = r.data;
            if (!success) return;
            this.$message({
              message: '优惠成功！',
              type: 'success',
              showClose: true
            });
            this.fixedPriceShow = false;
            this.authInfo = {};
            this.regNoSearch(this.personInfo.regNo);
          });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    // 根据row 判断是否单个或者批量打折
    batchDiscountBtn(row) {
      console.log(row);
      this.rowDiscount = row;
      if (!this.personInfo.regNo) {
        this.$message({
          message: '请先搜索订单！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.selectionComb?.length == 0 && !row) {
        this.$message({
          message: '请先选择需要打折的组合！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.discountOrFixedPrice = 'discount';
      this.loginPropShow = true;
      this.$nextTick(() => {
        this.resetForm('ruleForm');
      });
    },
    // 打折的确定按钮
    discountConfirm() {
      this.$refs['discount_Ref'].validate((valid) => {
        if (valid) {
          let ids = [];
          if (!this.rowDiscount) {
            this.selectionComb.map((item) => {
              if (item.payStatus == 0 && !item.discountOperName) {
                ids.push(item.id);
              }
            });
          } else {
            ids.push(this.rowDiscount.id);
          }

          let datas = {
            regNo: this.personInfo.regNo,
            discountOperCode: this.authInfo.operatorCode,
            discountOperName: this.authInfo.name,
            discount: this.fixedInfo.discount,
            ids: ids
          };
          console.log(datas);
          this.$ajax
            .post(this.$apiUrls.ReductioProjectsPrice, datas)
            .then((r) => {
              let { success, returnData } = r.data;
              if (!success) return;
              this.$message({
                message: '优惠成功！',
                type: 'success',
                showClose: true
              });
              this.discountShow = false;
              this.authInfo = {};
              this.regNoSearch(this.personInfo.regNo);
            });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    // 组合是否可选的回调
    combSelectable(row, index) {
      if (row.payStatus == 0 && !row.discountOperName) {
        return true;
      } else {
        return false;
      }
    },
    // 全选的回调
    selectAll(val) {
      console.log(val);
      let combTable_Ref = this.$refs.combTable_Ref.$refs.tableCom_Ref;
      let selection = combTable_Ref.selection;
      if (combTable_Ref.store.states.isAllSelected && selection?.length != 0) {
        selection = this.fixed_viewTableList;
      }
      this.selectionComb = selection;
    },
    // 单选的回调
    select(checkList, row) {
      let combTable_Ref = this.$refs.combTable_Ref.$refs.tableCom_Ref;
      let selection = combTable_Ref.selection;
      if (combTable_Ref.store.states.isAllSelected && selection?.length != 0) {
        selection = this.fixed_viewTableList;
      }
      this.selectionComb = selection;
    },
    // 取消打折
    cancelDiscount(row) {
      console.log(row);
      if (!this.personInfo.regNo) {
        this.$message({
          message: '请先搜索订单！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let text = !row ? '是否恢复原价?' : `是否取消${row.combName}的折扣?`;
      this.$confirm(text, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          let ids = [];
          if (!row) {
            this.fixed_viewTableList.map((item) => {
              if (item.payStatus == 0) {
                ids.push(item.id);
              }
            });
          } else {
            ids.push(row.id);
          }
          let datas = {
            regNo: this.personInfo.regNo,
            ids: ids
          };
          console.log(datas);
          this.$ajax.post(this.$apiUrls.RecoverCombPrice, datas).then((r) => {
            let { success, returnData } = r.data;
            if (!success) return;
            this.regNoSearch(this.personInfo.regNo);
            this.$message({
              message: '恢复原价成功！',
              type: 'success',
              showClose: true
            });
          });
        })
        .catch(() => {});
    }
  },
  created() {
    // 组合搜索
    this.comboSearch = _debounce(() => {
      let viewTableList = this.fixed_viewTableList.filter((item) => {
        return this.searchVal
          ? item.combName.includes(this.searchVal) ||
              item.combCode.includes(this.searchVal)
          : true;
      });
      console.log(viewTableList);
      this.viewTableList = viewTableList;
    }, 500);
  }
};
</script>
<style lang="less" scoped>
.discount_page {
  display: flex;
  flex-direction: column;
  overflow: auto;

  .search_ul {
    display: flex;
    height: 68px;
    align-items: center;
    background-color: #fff;
    border-radius: 4px;
    padding: 0 28px;
    margin-bottom: 18px;

    li {
      display: flex;
      align-items: center;
      margin-right: 28px;

      &:nth-child(3) {
        margin-right: 18px;
      }

      label {
        margin-right: 10px;
      }
    }
  }

  .person_info {
    display: flex;
    overflow: auto;
    background-color: #fff;
    border-radius: 4px;
    padding: 18px;
    margin-bottom: 18px;

    .info_column {
      padding-top: 10px;
      flex-basis: 20%;
      flex-shrink: 0;
      padding-right: 10px;

      p {
        margin-bottom: 18px;
        font-size: 14px;
        color: #2d3436;

        label {
          font-weight: 600;
        }
      }
    }
  }

  .combo_wrap {
    border-radius: 4px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    flex: 1;
    flex-shrink: 0;
    padding: 0 18px 18px;
    overflow: auto;

    ul {
      display: flex;
      align-items: center;
      padding: 18px 0;

      li {
        display: flex;
        align-items: center;
      }

      .price_li {
        margin: 0 20px;
        flex: 1;
        flex-shrink: 0;
        justify-content: space-between;
        padding-left: 20px;

        p {
          font-size: 14px;
          color: #d63031;
          display: flex;
          justify-content: space-between;
          min-width: 80px;
          margin-right: 20px;

          i {
            display: inline-block;
            font-style: normal;
            text-align: right;
          }
        }

        span {
          font-size: 18px;
          color: #d63031;
        }
      }
    }

    .table_div {
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
    }
  }

  .discount_div {
    cursor: pointer;
  }
}
</style>
