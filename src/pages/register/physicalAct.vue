<template>
  <div class="physicalAct">
    <div class="headrCont">
      <el-form :model="searchForm" ref="form_ref" label-width="80px">
        <el-row>
          <el-col>
            <el-form-item label="体检号">
              <el-input
                v-model.trim="searchForm.regNo"
                size="small"
                placeholder="请输入"
                clearable
                @keyup.enter.native="activate"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="姓名">
              <el-input
                v-model.trim="searchForm.name"
                size="small"
                placeholder="请输入"
                clearable
                @keyup.enter.native="searchAct"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col>
            <el-form-item label="单位">
              <!-- <el-select
                class="select"
                v-model.trim="searchForm.companyCode"
                placeholder="请选择"
                size="small"
                clearable
                filterable
                @change="getComTimes"
              >
                <el-option
                  v-for="(item, index) in companyList"
                  :key="index"
                  :label="item.companyName"
                  :value="item.companyCode"
                ></el-option>
              </el-select> -->
              <el-cascader
                ref="company_cascader_ref"
                v-model="searchForm.companyCode"
                :options="companyList"
                :props="{ multiple: false }"
                clearable
                filterable
                size="small"
                collapse-tags
                @change="companyChange"
                :filter-method="filterMethod"
              >
              </el-cascader>
            </el-form-item>
          </el-col>
          <!-- <el-col>
            <el-form-item label="单位次数">
              <el-select
                v-model.trim="companyTimes"
                placeholder="请选择"
                size="small"
                clearable
              >
                <el-option
                  v-for="(item, index) in comTimesList"
                  :key="index"
                  :value="item"
                  :label="item"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="档案卡号">
              <el-input
                v-model.trim="searchForm.patCode"
                size="small"
                clearable
                placeholder="请输入"
                @keyup.enter.native="searchAct"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="身份证">
              <el-input
                v-model.trim="searchForm.cardNo"
                size="small"
                placeholder="请输入"
                clearable
                @keyup.enter.native="searchAct"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="登记时间">
              <el-date-picker
                :picker-options="{ shortcuts: G_datePickerShortcuts }"
                size="small"
                v-model="regTime"
                type="daterange"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
                @change="searchAct"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-button
              size="small"
              icon="iconfont icon-search"
              class="blue_btn btn"
              @click="searchAct"
              style="margin-left: 12px"
              >查询</el-button
            >
          </el-col>
        </el-row>
        <el-row class="titleCont">
          <el-col class="checkbox">
            <el-form-item label="">
              <!-- <el-checkbox
                label="自动更新相片及信息"
                v-model.trim="isAutoUpdate"
              ></el-checkbox> -->
              <el-checkbox
                label="自动激活"
                v-model.trim="isAutoAct"
              ></el-checkbox>
              <!-- <el-checkbox
                label="激活插入导检"
                v-model.trim="isActCheck"
              ></el-checkbox>
              <el-checkbox
                label="自动打印指引单"
                v-model.trim="isAutoPrintCard"
              ></el-checkbox>
              <el-checkbox
                label="自动打印检验标签"
                v-model.trim="isAutoPrintType"
              ></el-checkbox>
              <el-checkbox
                label="自动打印体检条码"
                v-model.trim="isAutoPrintBarcode"
              ></el-checkbox> -->
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6" style="margin-right: 20px">
            <el-form-item label="激活时间" prop="activeTime" class="regTime">
              <el-date-picker
                v-model.trim="activeInfo.activeTime"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                placeholder="激活体检时间"
                clearable
                size="small"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-button
              size="small"
              class="blue_btn btn"
              icon="iconfont icon-jihuo"
              @click="acting"
              >激活</el-button
            >
            <el-button
              size="small"
              class="yellow_btn btn"
              icon="iconfont icon-jihuo"
              @click="cancelAct"
              >取消激活</el-button
            >
          </el-col>
          <el-col :span="9">
            <div class="printBtn_wrap">
              <!-- <el-checkbox-group
                v-model.trim="printVal"
                style="margin-right: 20px"
              >
                <el-checkbox label="指引单"></el-checkbox>
                <el-checkbox label="采血条码"></el-checkbox>
                <el-checkbox label="非采血条码"></el-checkbox>
                <el-checkbox label="体检标签"></el-checkbox>
              </el-checkbox-group> -->
              <el-button
                @click="print"
                class="green_btn btn"
                size="small"
                icon="iconfont icon-dayin-"
                >打印</el-button
              >
              <!-- <el-button
                @click="extSystemPrint"
                class="green_btn btn"
                size="small"
                >贴标打印</el-button
              > -->
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="tableCont">
      <PublicTable
        ref="table"
        :viewTableList.sync="tableData"
        :theads.sync="theads"
        :tableLoading.sync="loading"
        :columnWidth="columnWidth"
        @selectionChange="handleSelRow"
        @currentChange="currentChange"
        v-model="activeInfo.regNoArray"
        @select="handleSelectionChange"
        @selectAll="selectAll"
        isCheck
      >
        <template #age="{ scope }">
          <div>
            {{ scope.row.age + enum_ageUnit[scope.row.ageUnit] }}
          </div>
        </template>
        <template #peCls="{ scope }">
          <div>
            {{ G_EnumList['PeCls'][scope.row.peCls] }}
          </div>
        </template>
        <template #peStatus="{ scope }">
          <div>
            {{ G_EnumList['PeStatus'][scope.row.peStatus] }}
          </div>
        </template>
        <template #sex="{ scope }">
          <div>
            {{ G_EnumList['Sex'][scope.row.sex] }}
          </div>
        </template>
        <template #isActive="{ scope }">
          <div>
            <el-checkbox
              v-model.trim="scope.row.isActive"
              disabled
            ></el-checkbox>
          </div>
        </template>
        <template #group="{ scope }">
          <div>
            <el-checkbox v-model.trim="scope.row.group" disabled></el-checkbox>
          </div>
        </template>
        <template #isVIP="{ scope }">
          <div>
            <el-checkbox v-model.trim="scope.row.isVIP" disabled></el-checkbox>
          </div>
        </template>
        <template #isCompanyCheck="{ scope }">
          <div>
            <el-checkbox
              v-model.trim="scope.row.isCompanyCheck"
              disabled
            ></el-checkbox>
          </div>
        </template>
        <template #marryStatus="{ scope }">
          <div>
            {{ G_EnumList['MarryStatus'][scope.row.marryStatus] }}
          </div>
        </template>
      </PublicTable>
    </div>
    <div class="my_mask" v-if="G_activeProgressShow">
      <el-progress
        type="circle"
        :percentage="G_activeMsgData.currentProgressPercentage"
      ></el-progress>
      <p class="tips_p">{{ G_activeMsgData.message }}</p>
    </div>
    <PrintPreview
      v-model="previewShow"
      :dataInfo.sync="dataInfo"
      :printerTypeList="printerTypeList"
      :batchPrintList="checkRow"
    />
    <!-- 打印 -->
    <PrintSelection
      ref="PrintSelection_Ref"
      :defaultCheck="true"
      :displaySwitches.sync="printDisplay"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import PublicTable from '../../components/publicTable.vue';
import { dataUtils } from '@/common';
import PrintPreview from '@/components/printPreview';
// import PushMixinsAct from "@/components/pushMixinsAct";
import PrintSelection from '@/components/printSelection.vue';
import IdCardMixins from '@/components/IdCardMixins';
export default {
  name: 'physicalAct',
  mixins: [IdCardMixins],
  components: { PublicTable, PrintPreview, PrintSelection },
  props: {
    cancel: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      printDisplay: false,
      extSystemPrintIdx: 0,
      printVal: [],
      previewShow: false,
      dataInfo: {},
      printerTypeList: [],
      currentRow: {},

      isAutoAct: false,
      isActCheck: false,
      // isAutoUpdate: false,
      isAutoPrintCard: false,
      isAutoPrintType: false,
      isAutoPrintBarcode: false,
      loading: false,
      companyList: [], //单位
      comTimesList: [], //單位次數
      regTime: [dataUtils.getDate(), dataUtils.getDate()],
      companyTimes: null,
      searchForm: {
        regNo: '',
        name: '',
        companyCode: '',
        patCode: '',
        cardNo: '',
        regStartTime: '',
        regEndTime: '',
        companyTimes: -1
      }, //查询条件
      isEnabled: '',
      tableData: [], //表单数据
      theads: {
        peStatus: '状态',
        isActive: '激活',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        marryStatus: '婚姻状况',
        cardNo: '身份证',
        isCompanyCheck: '团体',
        isVIP: 'VIP',
        peCls: '体检分类',
        companyName: '工作单位',
        deptName: '部门',
        regNo: '体检号'
      },
      enum_ageUnit: {
        null: '',
        0: '岁',
        1: '月'
      },
      columnWidth: {
        cardNo: 180,
        companyName: 220,
        deptName: 180,
        regNo: 150
      },
      activeInfo: {
        regNoArray: [], //体检号数组
        activeTime: dataUtils.getDate(), //"2022-07-25T03:41:51.684Z"
        activator: ''
      },
      checkRow: [],
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() > Date.now();
        }
      }
    };
  },
  created() {
    // this.connectPrint();
  },
  mounted: function () {
    this.getCompany();
    this.$nextTick(() => {
      this.searchAct(); //进入页面默认查询当天
    });
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_userInfo',
      'G_activeProgressShow',
      'G_activeMsgData',
      'G_datePickerShortcuts'
    ])
  },
  methods: {
    format(percentage) {
      return `${this.G_activeMsgData.completedCount}/${this.G_activeMsgData.totalCount}`;
    },
    handleCheckedValChange(value) {},
    //获取单位下拉
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) return;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.regTime = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.searchForm.companyTimes = data[1];
      this.searchAct();
    },
    //获取体检次数
    getSimpleCompanyTimes(companyCode) {
      this.comTimesList = [];
      this.companyTimes = null;
      this.$ajax
        .post(this.$apiUrls.GetSimpleCompanyTimes, '', {
          query: { companyCode: companyCode }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$nextTick(function () {
            this.comTimesList = [];
            if (returnData.length > 0) {
              returnData.map((item) => {
                this.comTimesList.push(item.companyTimes);
              });
            }
          });
        });
    },
    getComTimes(val) {
      this.getSimpleCompanyTimes(val);
    },
    //查询
    searchAct() {
      if (!this.regTime) {
        this.searchForm.regStartTime = '';
        this.searchForm.regEndTime = '';
      } else {
        this.searchForm.regStartTime = this.regTime[0] || '';
        this.searchForm.regEndTime = this.regTime[1] || '';
      }
      // if (this.searchForm.companyCode) {
      //   if (this.companyTimes == null || this.companyTimes == -1) {
      //     this.$message({
      //       message: "查询单位时,单位跟单位次数必须有值!",
      //       type: "warning",
      //       showClose: true,
      //     });
      //     return;
      //   }
      // }
      this.searchForm.companyTimes = this.searchForm.companyCode?.[1] || -1;
      this.tableData = [];
      let temp = JSON.parse(JSON.stringify(this.searchForm));
      temp.companyCode = this.searchForm.companyCode[0];
      this.$ajax.post(this.$apiUrls.GetActiveRecordNew, temp).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.tableData = returnData || [];
        if (this.searchForm.regNo && this.tableData.length > 0) {
          this.$nextTick(() => {
            this.$refs.table.$refs.tableCom_Ref.setCurrentRow(
              this.tableData[0]
            );
            this.$refs.table.$refs.tableCom_Ref.toggleRowSelection(
              this.tableData[0]
            );
          });
        }
      });
    },
    //勾选行
    handleSelRow: function (val, checkList) {
      this.activeInfo.regNoArray = [];
      this.checkRow = checkList;
      checkList.forEach((item) => {
        if (item.regNo !== '') {
          this.activeInfo.regNoArray.push(item.regNo);
        }
      });
      if (val.length > 0 && checkList.length < 1) {
        this.activeInfo.regNoArray.push(val[0].regNo);
      }
    },
    // 选中列表高亮
    currentChange(row) {
      this.currentRow = row;
    },
    //勾选默认点击行
    handleSelectionChange(selection, row) {
      this.currentRow = row;
      this.$refs.table.$refs.tableCom_Ref.setCurrentRow(row);
    },
    //全选判定只有一条数据时默认点击操作
    selectAll(selection) {
      if (selection.length < 1) {
        return;
      }
      this.activeInfo.regNoArray = [];
      selection.map((item) => {
        if (item.regNo != '') {
          this.activeInfo.regNoArray.push(item.regNo);
        }
      });
      this.$refs.table.$refs.tableCom_Ref.setCurrentRow(selection[0]);
    },
    activate() {
      if (this.searchForm.regNo) {
        if (this.isAutoAct) {
          let activeInfo = {
            regNoArray: [this.searchForm.regNo], //体检号数组
            activeTime: this.activeInfo.activeTime, //"2022-07-25T03:41:51.684Z"
            activator: this.G_userInfo.codeOper.operatorCode
          };
          this.$ajax
            .post(this.$apiUrls.ActivationOrCancel + '/Active', activeInfo)
            .then((r) => {
              if (!r.data.success) {
                return;
              }
              this.$message({
                showClose: true,
                message: '激活成功!',
                type: 'success'
              });
              this.searchAct();
            });
        } else {
          this.searchAct();
        }
      }
    },
    //激活
    acting() {
      if (this.activeInfo.regNoArray.length < 1) {
        this.$message({
          message: '请先选择要激活的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.activeInfo.activator = this.G_userInfo.codeOper.operatorCode;

      this.$ajax
        .post(this.$apiUrls.ActivationOrCancel + '/Active', this.activeInfo)
        .then((r) => {
          if (!r.data.success) {
            return;
          }
          if (this.activeInfo.regNoArray.length == 1) {
            this.$message({
              showClose: true,
              message: '操作成功!',
              type: 'success'
            });
          }
          this.tableData.forEach((item) => {
            if (this.activeInfo.regNoArray.includes(item.regNo))
              item.isActive = true;
          });
        });
    },
    //取消激活
    cancelAct() {
      if (this.activeInfo.regNoArray.length < 1) {
        this.$message({
          message: '请先选择要取消激活的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.activeInfo.activator = this.G_userInfo.codeOper.operatorCode;
      this.$ajax
        .post(this.$apiUrls.ActivationOrCancel + '/Deactive', this.activeInfo)
        .then((r) => {
          if (!r.data.success) {
            return;
          }
          if (this.activeInfo.regNoArray.length == 1) {
            this.$message({
              message: '操作成功!',
              type: 'success',
              showClose: true
            });
          }
          this.tableData.forEach((item) => {
            if (this.activeInfo.regNoArray.includes(item.regNo))
              item.isActive = false;
          });
        });
    },
    // print() {
    //   if (!this.currentRow?.regNo) {
    //     this.$message({
    //       message: "请先选择需要打印的体检信息！",
    //       type: "warning",
    //       showClose: true,
    //     });
    //     return;
    //   }
    //   if (this.printVal?.length == 0) {
    //     this.$message({
    //       message: "请先选择打印的类型",
    //       type: "warning",
    //       showClose: true,
    //     });
    //     return;
    //   }
    //   this.printerTypeList = [];
    //   this.printVal?.map((item) => {
    //     let printerType = 0;
    //     let type = "";
    //     switch (item) {
    //       case "指引单":
    //         printerType = 0;
    //         break;

    //       case "体检标签":
    //         printerType = 1;
    //         break;
    //       case "采血条码":
    //         printerType = 1;
    //         type = 1;
    //         break;
    //       case "非采血条码":
    //         printerType = 1;
    //         type = 2;
    //         break;
    //       default:
    //         printerType = 0;
    //         break;
    //     }
    //     this.printerTypeList.push({
    //       printerType: printerType,
    //       label: item,
    //       checked: true,
    //       type,
    //     });
    //   });
    //   this.dataInfo = this.currentRow;
    //   this.previewShow = true;
    // },
    //打印
    print() {
      if (!this.checkRow?.length > 0 && !this.currentRow?.regNo) {
        this.$message({
          message: '请先选择需要打印的体检信息！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let regNoList;
      if (!this.checkRow?.length > 0) {
        regNoList = [
          {
            regNo: this.currentRow.regNo,
            isActive: this.currentRow.isActive
          }
        ];
      } else {
        regNoList = this.checkRow.map((item) => {
          return {
            regNo: item.regNo,
            isActive: item.isActive
          };
        });
      }

      this.$refs.PrintSelection_Ref.setPrintTypeList(regNoList);
      this.printDisplay = true;
    },
    // 外部打印
    extSystemPrint() {
      if (this.activeInfo.regNoArray.length === 0) {
        this.$message({
          message: '请先选择需要推送打印的体检人!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.connectPrint();
      this.extSystemPrintFun();
    },
    // 外部打印递归回调
    extSystemPrintFun() {
      if (this.extSystemPrintIdx >= this.activeInfo.regNoArray.length) {
        this.$message({
          message: '完成操作!',
          type: 'success',
          showClose: true
        });
        this.extSystemPrintIdx = 0;
        return;
      }
      this.$ajax
        .paramsPost(this.$apiUrls.GetSampleDataToMachine, {
          regNo: this.activeInfo.regNoArray[this.extSystemPrintIdx]
        })
        .then((r) => {
          this.extSystemPrintIdx += 1;
          let { success, returnData } = r.data;
          let datas = "{'Action':'sample','Data':'" + returnData + "'}";
          this.$sample_ws?.sendSock(datas);
          setTimeout(() => {
            this.extSystemPrintFun();
          }, 200);
        })
        .catch((e) => {
          this.extSystemPrintIdx += 1;
          setTimeout(() => {
            this.extSystemPrintFun();
          }, 200);
        });
    },
    connectInit() {
      this.connectIdcard((r) => {
        try {
          let datas = JSON.parse(r.data);
          if (datas.Data) {
            this.searchForm.cardNo = datas.Data.IDCardNo;
            this.searchForm.name = datas.Data.Name;
            this.$nextTick(() => {
              this.searchAct();
            });
          }
        } catch (error) {}
      });
    }
  }
};
</script>

<style lang="less" scoped>
.physicalAct {
  display: flex;
  flex-direction: column;
  position: relative;
  height: 100%;
  /deep/.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    background-color: #409eff;
    border-color: #409eff;
  }
  .headrCont {
    // display: flex;
    // flex-direction: row;
    background: #fff;
    border-radius: 4px;
    padding: 5px;
    // /deep/.el-input__icon {
    //   line-height: 32px;
    // }
    .el-row {
      display: flex;
    }
    .el-form-item {
      margin-bottom: 10px;
      /deep/ .el-form-item__label,
      /deep/.el-form-item__content {
        height: 32px;
        line-height: 32px;
      }
    }

    .titleCont {
      height: 32px;
      margin-bottom: 10px;
      /deep/.el-form-item__content {
        padding-left: 20px;
        background: rgba(23, 112, 223, 0.1);
      }
    }
  }
  .centerCont {
    display: flex;
    flex-direction: row;
    width: 100%;
    height: 48px;
    line-height: 48px;
    border-radius: 4px;

    .titleCheckbox {
      display: flex;
      background: rgba(60, 179, 79, 0.1);
      border-radius: 2px;
      padding-left: 20px;
      .print-btn {
        margin: 0 20px;
        height: 32px;
        margin-top: 8px;
      }
    }
    .amount {
      margin: 0 20px 0 10px;
      height: 32px;
      width: 76px;
    }
    .btnTxt {
      background: rgba(214, 48, 49, 0.1);
      padding: 0 20px;
      border-radius: 2px;
      border-radius: 2px;
      margin-left: 40px;
      .del-btn {
        margin-right: 30px;
      }
    }
  }
  .tableCont {
    height: calc(100% - 200px);
    background: #fff;
    flex: 1;
    padding: 5px;
  }
  .select {
    width: 100%;
  }
  /deep/.el-checkbox__inner {
    width: 18px;
    height: 18px;
  }
  /deep/.el-date-editor.el-input,
  .el-date-editor.el-input__inner {
    width: 100%;
    // height: 32px;
  }
  // /deep/.el-input__icon,
  // /deep/.el-input__inner,
  // .el-button {
  //   height: 32px;
  // }
  .btn {
    padding: 6.5px 10px;
  }
  .printBtn_wrap {
    display: flex;
    align-items: center;
  }
}
</style>
