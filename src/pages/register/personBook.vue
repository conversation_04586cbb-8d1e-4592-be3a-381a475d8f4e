<template>
  <div class="personBook">
    <div class="left">
      <p>预约列表</p>
      <div class="left-table">
        <PublicTable
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          :isSortShow="false"
          @rowClick="currentChangeInfo"
          :columnWidth="columnWidth"
          ref="patientTable"
        ></PublicTable>
      </div>
    </div>
    <div class="right">
      <BookBtn
        :btnList="['新建', '保存', '删除']"
        @create="createClick"
        @deletes="deletes"
        @save="save"
        ref="orderBtn_Ref"
      />
      <div class="right-info">
        <div class="title-text">预约登记表</div>
        <div class="numb-box">
          预约编号：<span class="numb">{{ bookNo }}</span>
        </div>
      </div>
      <div class="right-form">
        <el-form ref="ruleForm" :model="form" :rules="rules" label-width="79px">
          <el-row :gutter="20">
            <el-col :span="6" v-if="G_config.physicalMode.includes('普检')">
              <el-form-item label="体检分类" prop="peCls">
                <el-select
                  class="select"
                  v-model.trim="form.peCls"
                  placeholder="请选择"
                  size="small"
                >
                  <el-option
                    :label="item.label"
                    :value="item.value"
                    v-for="item in G_peClsList"
                    :key="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="体检号" prop="regNo">
                <el-input
                  v-model.trim="form.regNo"
                  size="small"
                  placeholder="请输入体检号"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="姓名" prop="name">
                <el-input
                  v-model.trim="form.name"
                  size="small"
                  placeholder="请输入姓名"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="性别" prop="sex">
                <el-select
                  class="select"
                  v-model.trim="form.sex"
                  placeholder="请选择性别"
                  size="small"
                  @change="searchClus"
                >
                  <el-option
                    v-for="item in G_sexList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="年龄" required>
                <div class="age-input">
                  <el-form-item prop="age" style="margin-bottom: 0; flex: 1">
                    <el-input
                      class="select"
                      :maxlength="3"
                      v-model.number.trim="form.age"
                      size="small"
                      placeholder="请输入"
                      oninput="value=value.replace(/[^\d]/g,'')"
                      clearable
                      @blur="ageBlur"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    prop="ageUnit"
                    style="margin-bottom: 0; flex: 1"
                  >
                    <el-select
                      class="select"
                      v-model.trim="form.ageUnit"
                      @change="ageUnitChange"
                      placeholder="请选择年龄单位"
                      size="small"
                    >
                      <el-option
                        :label="item.label"
                        :value="item.value"
                        v-for="item in G_ageUnit"
                        :key="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="婚姻状况" prop="marryStatus">
                <el-select
                  class="select"
                  v-model.trim="form.marryStatus"
                  placeholder="请选择"
                  size="small"
                  clearable
                >
                  <el-option
                    :label="item.label"
                    :value="item.value"
                    v-for="item in G_marriageStatus"
                    :key="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="籍贯" prop="nativePlace">
                <el-select
                  filterable
                  class="select"
                  v-model.trim="form.nativePlace"
                  placeholder="请选择籍贯"
                  size="small"
                  clearable
                >
                  <el-option
                    :label="item.label"
                    :value="item.value"
                    v-for="item in G_codeNativePlace"
                    :key="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="联系电话" prop="tel">
                <el-input
                  :maxlength="11"
                  v-model.trim="form.tel"
                  size="small"
                  placeholder="请输入"
                  clearable
                  oninput="value=value.replace(/[^\d]/g,'')"
                  @clear="telClear"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="证件类型" prop="cardType">
                <el-select
                  filterable
                  class="select"
                  v-model.trim="form.cardType"
                  placeholder="请选择"
                  size="small"
                >
                  <el-option
                    :label="item.label"
                    :value="item.value"
                    v-for="item in G_cardType"
                    :key="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="证件号" prop="cardNo">
                <el-input
                  v-model.trim="form.cardNo"
                  size="small"
                  placeholder="请输入证件号"
                  clearable
                  @clear="cardNoClear"
                  @blur="cardNoBlur"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="联系地址" prop="address">
                <el-input
                  v-model.trim="form.address"
                  size="small"
                  placeholder="请输入联系地址"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="工作单位" prop="companyCode">
                <el-select
                  filterable
                  clearable
                  class="select"
                  v-model.trim="form.companyCode"
                  placeholder="请选择工作单位"
                  :filter-method="filterMethod"
                  @visible-change="companyVisibleChange"
                  size="small"
                >
                  <el-option
                    :label="item.companyName"
                    :value="item.companyCode"
                    v-for="item in companyList"
                    :key="item.companyCode"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="预约类型" prop="bookType">
                <el-select
                  class="select"
                  v-model.trim="form.bookType"
                  placeholder="请选择"
                  size="small"
                >
                  <el-option
                    :label="item.label"
                    :value="item.value"
                    v-for="item in G_bookType"
                    :key="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="预约日期" required>
                <div class="date-box">
                  <el-form-item prop="bookBeginTime" style="margin-bottom: 0">
                    <el-date-picker
                      v-model="form.bookBeginTime"
                      type="date"
                      placeholder="选择日期"
                      value-format="yyyy-MM-dd"
                      size="small"
                      class="date-picker"
                      :picker-options="pickerOptions"
                    >
                    </el-date-picker>
                  </el-form-item>
                  <el-form-item
                    prop="bookEndTime"
                    style="margin-bottom: 0; width: 90%"
                  >
                    <el-time-picker
                      is-range
                      v-model="form.bookEndTime"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      placeholder="选择时间范围"
                      format="HH:mm"
                      value-format="HH:mm:ss"
                      size="small"
                      class="date-picker time-picker"
                    >
                    </el-time-picker>
                  </el-form-item>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="登记员" prop="operatorCode">
                <el-input
                  v-model.trim="form.operatorCode"
                  size="small"
                  placeholder="请输入登记员"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="登记时间" prop="registerTime">
                <el-date-picker
                  class="select"
                  v-model.trim="form.registerTime"
                  type="datetime"
                  size="small"
                  format="yyyy-MM-dd HH:mm"
                  placeholder="请选择登记时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  disabled
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="info-table">
        <el-row :gutter="20" class="table-row">
          <el-col :span="12" class="table-col">
            <div class="table-letf">
              <div class="letf-title">
                <h4>体检套餐列表</h4>
              </div>
              <div class="tables">
                <PublicTable
                  ref="singleTable"
                  :viewTableList.sync="searchClusterList"
                  :theads.sync="leftTheads"
                  :showHeader="false"
                  :isSortShow="false"
                  :columnWidth="columnWidth"
                  @rowClick="handleCurrentChange"
                ></PublicTable>
              </div>
            </div>
          </el-col>
          <el-col :span="12" class="table-col">
            <div class="table-letf">
              <div class="letf-title">
                <h4>体检组合列表</h4>
                <h4>已选择：{{ multiple.length }}个</h4>
              </div>
              <div class="tables">
                <PublicTable
                  ref="multipleTable"
                  :viewTableList.sync="comboList"
                  :theads.sync="rightTheads"
                  :showHeader="false"
                  :isSortShow="false"
                  :columnWidth="columnWidth"
                  :checkRowCode.sync="checkRowCode"
                  rowID="combCode"
                  isCheck
                  @select="selectionChange"
                ></PublicTable>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import BookBtn from './components/bookBtn.vue';
import PublicTable from '@/components/publicTable';
import { mapGetters } from 'vuex';
import moment from 'moment';
import { dataUtils } from '@/common';
export default {
  name: 'personBook',
  components: {
    BookBtn,
    PublicTable
  },
  data() {
    // 年龄校验
    const checkAge = (rule, value, callback) => {
      if (value === '' || value === null) {
        return callback(new Error('请输入年龄'));
      } else {
        if (this.form.ageUnit === 0) {
          let ageReg = /^((\d{1,2})|(1[0-4]\d)|(150))$/;
          if (!ageReg.test(value)) {
            callback(new Error('年龄格式不正确'));
          } else {
            callback();
          }
        } else {
          let ageReg = /^(1[0-2]|[1-9])$/;
          if (!ageReg.test(value)) {
            callback(new Error('月份格式不正确'));
          } else {
            callback();
          }
        }
      }
    };
    // 年龄单位校验
    const checkAgeUnit = (rule, value, callback) => {
      if (this.form.age) {
        if (value === 0) {
          let ageReg = /^((\d{1,2})|(1[0-4]\d)|(150))$/;
          if (!ageReg.test(this.form.age)) {
            callback(new Error(' '));
          } else {
            callback();
          }
        } else {
          let ageReg = /^(1[0-2]|[1-9])$/;
          if (!ageReg.test(this.form.age)) {
            callback(new Error(' '));
          } else {
            callback();
          }
        }
      }
    };
    // 身份证校验
    const checkCardNo = (rule, value, callback) => {
      if (value === '') {
        callback();
      } else if (value && this.form.cardType === '1') {
        let cardNoReg =
          /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/;
        if (!cardNoReg.test(value)) {
          callback(new Error('身份证格式不正确'));
        } else {
          let { age, sex } = dataUtils.getByIdCard(value);
          this.form.age = age;
          this.form.sex = sex;
          this.searchClus(sex);
          callback();
        }
      }
    };
    // 手机号校验
    const checkTel = (rule, value, callback) => {
      if (value) {
        let telReg = /^[1][3,4,5,7,8][0-9]{9}$/;
        if (!telReg.test(value)) {
          callback(new Error('手机号格式不正确'));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    return {
      checkRowCode: [],
      form: {
        regNo: '',
        peCls: null,
        name: '',
        sex: null,
        age: null,
        ageUnit: 0,
        marryStatus: null,
        nativePlace: '',
        cardType: '1',
        cardNo: '',
        companyCode: '',
        jobCode: '',
        address: '',
        tel: '',
        bookBeginTime: null,
        bookEndTime: null,
        operatorCode: '',
        registerTime: null,
        bookType: null
      },
      theads: {
        bookNo: '编号',
        name: '姓名',
        bookBeginTime: '开始时间',
        bookEndTime: '结束时间'
      },
      leftTheads: {
        clusCode: '套餐编号',
        clusName: '套餐名称'
      },
      rightTheads: {
        combCode: '组合编号',
        combName: '组合名称'
      },
      tableData: [],
      columnWidth: {
        clusCode: 60,
        combCode: 80,
        bookNo: 60,
        name: 80,
        bookBeginTime: 170,
        bookEndTime: 170
      },
      rules: {
        peCls: [
          { required: true, message: '请选择体检分类', trigger: 'change' }
        ],
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
          { min: 2, message: '最少输入2个字符', trigger: 'blur' }
        ],
        sex: [{ required: true, message: '请选择性别', trigger: 'change' }],
        age: [{ validator: checkAge, trigger: 'blur' }],
        ageUnit: [{ validator: checkAgeUnit, trigger: 'change' }],
        cardNo: [{ validator: checkCardNo, trigger: 'blur' }],
        tel: [{ validator: checkTel, trigger: 'blur' }],
        bookType: [
          { required: true, message: '请选择预约类型', trigger: 'change' }
        ],
        bookBeginTime: [
          { required: true, message: '请选择预约日期', trigger: 'change' }
        ],
        bookEndTime: [
          { required: true, message: '请选择预约时间段', trigger: 'change' }
        ]
      },
      currentRow: {},
      companyList: [],
      companyListCopy: [],
      infoRow: {},
      singleTable: [],
      multipleTable: [],
      bookNo: '0',
      isUpdate: false,
      multiple: [],
      multipleCopy: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
        }
      },
      clusterList: [],
      searchClusterList: [],
      comboList: [],
      fixed_comboList: [],
      comboListCodeObj: {},
      removeCombCode: false
    };
  },
  computed: {
    ...mapGetters([
      'G_userInfo',
      'G_sexList',
      'G_peClsList',
      'G_marriageStatus',
      'G_cardType',
      'G_bookType',
      'G_codeNativePlace',
      'G_ageUnit',
      'G_config'
    ])
  },
  created() {
    this.getReadPatientList();
    this.getCompany();
    this.getClusterList();
    this.getCombList();
    console.log('this.G_userInfo: ', this.G_userInfo);
  },
  methods: {
    // 获取套餐列表
    getClusterList() {
      this.$ajax.post(this.$apiUrls.ReadCandidateCluster).then((r) => {
        console.log('ReadCandidateCluster: ', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.clusterList = returnData || [];
        this.searchClusterList = returnData || [];
      });
    },
    // 获取组合列表
    getCombList() {
      this.$ajax.post(this.$apiUrls.ReadCandidateComb).then((r) => {
        console.log('ReadCandidateComb: ', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.fixed_comboList = dataUtils.deepCopy(returnData || []);
        this.comboList = [];
        this.fixed_comboList.map((item) => {
          this.comboListCodeObj[item.combCode] = item;
        });
      });
    },
    // 获取个人预约信息列表
    getReadPatientList() {
      this.$ajax
        .post(this.$apiUrls.ReadPatientList, '', {
          query: { isCompanyCheck: false }
        })
        .then((r) => {
          console.log('ReadPatientList: ', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.tableData = returnData.map((item) => {
            return {
              bookBeginTime: moment(item.bookBeginTime).format(
                'YYYY-MM-DD HH:mm'
              ),
              bookEndTime: moment(item.bookEndTime).format('YYYY-MM-DD HH:mm'),
              bookNo: item.bookNo,
              name: item.name
            };
          });
        });
    },
    //获取个人预约信息
    getReadPeBook() {
      if (this.infoRow === null) {
        return;
      }
      this.checkRowCode = [];
      this.multiple = [];
      this.$ajax
        .post(this.$apiUrls.ReadPeBook, '', {
          query: { bookNo: this.infoRow.bookNo }
        })
        .then((r) => {
          console.log('ReadPeBook: ', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.isUpdate = true;
          let form = returnData.bookPatient;
          let beginTime = moment(form.bookBeginTime).format('HH:mm:ss');
          let endTime = moment(form.bookEndTime).format('HH:mm:ss');
          this.form = {
            ...form,
            bookBeginTime: moment(form.bookBeginTime).format('YYYY-MM-DD'),
            bookEndTime: [beginTime, endTime]
          };
          this.bookNo = returnData.bookPatient.bookNo;
          this.searchClus();
          // 匹配套餐
          if (returnData.bookCluster) {
            let row = this.clusterList.filter(
              (item) => returnData.bookCluster.clusCode === item.clusCode
            );
            this.currentRow = row[0];
            this.$nextTick(() => {
              this.$refs.singleTable.$refs.tableCom_Ref.setCurrentRow(row[0]);
            });
          }
          // 处理组合表格选中
          this.$nextTick(() => {
            this.comboList = this.fixed_comboList;
            returnData.bookCombs.map((item) => {
              this.checkRowCode.push(item.combCode);
              this.multiple.push(this.comboListCodeObj[item.combCode]);
              this.$refs.multipleTable.$refs.tableCom_Ref.toggleRowSelection(
                this.comboListCodeObj[item.combCode]
              );
            });
            // 处理勾选
            let checkArr = returnData.bookCombs.map((item) => item.combCode);
            const result = this.fixed_comboList.filter((item) =>
              checkArr.includes(item.combCode)
            );
            this.multipleCopy = result;
            // 处理未勾选
            let arr = returnData.bookCombs.map((item) => item.combCode);
            const results = this.fixed_comboList.filter(
              (item) => !arr.includes(item.combCode)
            );
            // 拼接勾选未勾选
            this.comboList = [...result, ...results];
          });
        });
    },
    //获取单位下拉
    getCompany() {
      this.$ajax.post(this.$apiUrls.Company).then((r) => {
        console.log('Company: ', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData;
        this.companyListCopy = dataUtils.deepCopy(this.companyList);
      });
    },
    filterMethod(val) {
      this.form.companyCode = val;
      if (val.trim() == '') {
        this.companyList = dataUtils.deepCopy(this.companyListCopy);
      } else {
        this.companyList = this.companyListCopy.filter(
          (item) =>
            item.companyName.includes(val) || item.companyCode.includes(val)
        );
      }
    },
    companyVisibleChange(data) {
      if (data)
        this.companyList = JSON.parse(JSON.stringify(this.companyListCopy));
    },
    // 预约列表当前行选择
    currentChangeInfo(row) {
      console.log('row: ', row);
      this.infoRow = row;
      this.$nextTick(() => {
        this.resetForm('ruleForm');
      });
      this.getReadPeBook();
    },
    // 体检组合列表勾选
    selectionChange(selection, row) {
      console.log('selection: ', selection, row);
      if (selection.includes(row)) {
        this.multiple.push(row);
        this.checkRowCode.push(row.combCode);
      } else {
        let idx = this.checkRowCode.indexOf(row.combCode);
        this.multiple.splice(idx, 1);
        this.checkRowCode.splice(idx, 1);
      }
      // 去掉套餐里的项目
      if (this.multipleCopy.includes(row)) {
        this.removeCombCode = true;
        this.$nextTick(() => {
          this.$refs.singleTable.$refs.tableCom_Ref.setCurrentRow();
        });
      }
    },
    // 左边套餐表格点击行
    handleCurrentChange(row) {
      console.log('currentRow: ', row);
      this.currentRow = row;
      this.multiple = [];
      this.checkRowCode = [];
      this.comboList = [];
      this.$nextTick(() => {
        this.comboList = this.fixed_comboList;
        row.bindCombs.map((item) => {
          this.checkRowCode.push(item);
          this.multiple.push(this.comboListCodeObj[item]);
          this.$refs.multipleTable.$refs.tableCom_Ref.toggleRowSelection(
            this.comboListCodeObj[item]
          );
        });
        let checkArr = row.bindCombs.map((item) => item);
        const result = this.fixed_comboList.filter((item) =>
          checkArr.includes(item.combCode)
        );
        this.multipleCopy = result;
        let arr = row.bindCombs.map((item) => item);
        const results = this.fixed_comboList.filter(
          (item) => !arr.includes(item.combCode)
        );
        this.comboList = [...result, ...results];
      });
    },
    // 套餐通过性别过滤
    searchClus() {
      let searchMealList = this.clusterList.filter((item) => {
        return !this.form.sex
          ? true
          : item.sex == this.form.sex || item.sex == 0;
      });
      console.log(searchMealList);
      this.comboList = [];
      this.searchClusterList = searchMealList;
    },
    //新增
    createClick() {
      this.isUpdate = false;
      this.getClusterList();
      this.bookNo = '0';
      this.comboList = [];
      this.multiple = [];
      this.multipleTable = [];
      this.$nextTick(() => {
        this.resetForm('ruleForm');
        this.form.bookEndTime = null;
        this.$refs.singleTable.$refs.tableCom_Ref.setCurrentRow();
        this.$refs.patientTable.$refs.tableCom_Ref.setCurrentRow();
      });
    },
    // 新建或修改请求
    async create() {
      let bookBeginTime = `${this.form.bookBeginTime} ${this.form.bookEndTime[0]}`;
      let bookEndTime = `${this.form.bookBeginTime} ${this.form.bookEndTime[1]}`;
      let multiple = this.multiple.map((item) => {
        return {
          combCode: item.combCode,
          combName: item.combName,
          examDeptCode: item.examDeptCode,
          checkCls: item.checkCls,
          clsCode: item.clsCode,
          price: item.price,
          applicantCode: this.G_userInfo.codeOper.operatorCode,
          applicantName: this.G_userInfo.codeOper.name
        };
      });
      let data = {
        bookPatient: {
          ...this.form,
          bookNo: this.bookNo,
          isCompanyCheck: false,
          bookBeginTime: bookBeginTime,
          bookEndTime: bookEndTime,
          operatorCode: this.G_userInfo.codeOper.operatorCode,
          operatorName: this.G_userInfo.codeOper.name,
          registerTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
          guidanceType: '',
          reportType: ''
        },
        bookCluster: this.removeCombCode
          ? {}
          : {
              clusCode: this.currentRow.clusCode,
              clusName: this.currentRow.clusName,
              price: this.currentRow.price
            },
        bookCombs: multiple
      };
      console.log('data: ', data);
      const result = await this.$ajax
        .post(`${this.$apiUrls.SaveBook}/person`, data)
        .then((r) => {
          console.log('r : ', r);
          let { success } = r.data;
          if (!success) return;
          this.$message({
            message: !this.isUpdate ? '新建成功!' : '修改成功!',
            type: 'success',
            showClose: true
          });
          this.getReadPatientList();
          this.createClick();
        });
      return result;
    },
    //保存
    save() {
      if (JSON.stringify(this.currentRow) === '{}' || !this.currentRow) {
        this.$message({
          message: '请选择体检套餐!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.create();
        }
      });
    },
    //删除
    deletes() {
      if (JSON.stringify(this.infoRow) === '{}') {
        this.$message({
          message: '请从预约列表选择一条要删除的信息!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm(`是否确定删除这条数据?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let bookBeginTime = `${this.form.bookBeginTime} ${this.form.bookEndTime[0]}`;
        let bookEndTime = `${this.form.bookBeginTime} ${this.form.bookEndTime[1]}`;
        console.log('this.multiple: ', this.multiple);
        let multiple = this.multiple.map((item) => {
          return {
            combCode: item.combCode,
            combName: item.combName,
            examDeptCode: item.examDeptCode,
            checkCls: item.checkCls,
            clsCode: item.clsCode,
            price: item.price,
            applicantCode: this.G_userInfo.codeOper.operatorCode,
            applicantName: this.G_userInfo.codeOper.name
          };
        });
        let data = {
          bookPatient: {
            ...this.form,
            bookNo: this.bookNo,
            isCompanyCheck: false,
            bookBeginTime: bookBeginTime,
            bookEndTime: bookEndTime,
            operatorCode: this.G_userInfo.codeOper.operatorCode,
            operatorName: this.G_userInfo.codeOper.name,
            registerTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
            guidanceType: '',
            reportType: ''
          },
          bookCluster: {
            clusCode: this.currentRow.clusCode,
            clusName: this.currentRow.clusName,
            price: this.currentRow.price
          },
          bookCombs: multiple
        };
        console.log('data: ', data);
        this.$ajax.post(this.$apiUrls.DeleteBook, data).then((r) => {
          let { success } = r.data;
          if (!success) return;
          this.$message({
            message: '删除成功!',
            type: 'success',
            showClose: true
          });
          this.getReadPatientList();
          this.createClick();
        });
      });
    },
    // 年龄校验
    ageBlur() {
      this.$refs.ruleForm.validateField('ageUnit');
    },
    // 年龄单位校验
    ageUnitChange() {
      this.$refs.ruleForm.validateField('age');
    },
    // 电话清空
    telClear() {
      this.$refs.ruleForm.validateField('tel');
    },
    // 身份证清空
    cardNoClear() {
      this.$refs.ruleForm.validateField('cardNo');
    },
    // 身份证校验
    cardNoBlur() {
      this.$refs.ruleForm.validateField('cardNo', (valid) => {
        if (!valid) {
          if (!this.form.cardNo) return;
          this.$nextTick(() => {
            this.ageBlur();
            this.ageUnitChange();
          });
        }
      });
    },
    // 重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields();
    }
  }
};
</script>
<style lang="less" scoped>
.personBook {
  display: flex;
  color: #2d3436;
  font-weight: 600;
  .left {
    width: 360px;
    background: white;
    display: flex;
    flex-direction: column;
    p {
      font-size: 16px;
      color: #2d3436;
      line-height: 48px;
      margin-left: 18px;
    }
    .left-table {
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    flex: 1;
    flex-shrink: 0;
    background: white;
    margin-left: 20px;
    padding: 16px;
    font-size: 14px;
  }
  .right-info {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  .title-text {
    font-size: 24px;
    font-weight: 600;
    flex: 1;
    flex-shrink: 0;
    text-align: center;
    padding-left: 124px;
  }
  .right-form {
    margin-bottom: 14px;
  }
  .bookNo {
    width: 60px;
  }
  .numb-box {
    padding-right: 30px;
  }
  .numb {
    color: #1770df;
  }
  .select {
    width: 100%;
  }
  .age {
    display: flex;
  }
  .age-input {
    display: flex;
    span {
      margin: 0 8px;
    }
  }
  .info-table {
    flex: 1;
    flex-shrink: 0;
    overflow: hidden;
    h4 {
      font-size: 18px;
      padding: 12px 18px;
    }
  }
  .table-letf {
    border: 1px solid #d8dee1;
    border-radius: 4px;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .letf-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(23, 112, 223, 0.2);
  }
  .table-row,
  .table-col {
    height: 100%;
  }
  .tables {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
  }
  .date-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .time-picker {
    padding: 2px 10px;
  }
  .date-picker {
    width: 100%;
    /deep/.el-input__inner {
      padding-right: 0px;
    }
  }
  /deep/.el-form-item__label {
    color: #2d3436;
    font-weight: 600;
  }
  .el-form-item {
    margin-bottom: 10px;
  }
}
</style>
