<template>
  <div class="bulkEditing">
    <div class="headrCont">
      <div class="headerLeft">
        <el-form
          :model="searchForm"
          ref="form_ref"
          :rules="rules"
          label-width="80px"
        >
          <el-row>
            <el-col>
              <el-form-item label="体检号" prop="regNo" class="haveCheck">
                <el-input
                  v-model.trim="searchForm.regNo"
                  size="small"
                  placeholder="请输入"
                  clearable
                  @keyup.enter.native="searchAct"
                  style="width: calc(100% - 64px); margin-right: 5px"
                ></el-input>
                <el-checkbox>自动</el-checkbox>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="姓名" prop="name">
                <el-input
                  v-model.trim="searchForm.name"
                  size="small"
                  placeholder="请输入"
                  clearable
                  @keyup.enter.native="searchAct"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="" prop="regTime" class="regTime">
                <el-select
                  v-model.trim="searchForm.queryType"
                  placeholder="请选择"
                  size="small"
                  clearable
                  @keyup.enter.native="searchAct"
                >
                  <el-option
                    v-for="item in timeType"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
                <el-date-picker
                  :picker-options="{ shortcuts: G_datePickerShortcuts }"
                  v-model.trim="regTime"
                  type="daterange"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  clearable
                  @keyup.enter.native="searchAct"
                  size="small"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-form-item label="档案卡号" prop="patCode">
                <el-input
                  v-model.trim="searchForm.patCode"
                  size="small"
                  placeholder="请输入"
                  class="patCode"
                  clearable
                  @keyup.enter.native="searchAct"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="单位" prop="companyCode">
                <el-cascader
                  style="width: 100%"
                  ref="company_cascader_ref"
                  v-model="searchForm.companyCode"
                  :options="companyList"
                  :filter-method="filterMethod"
                  :props="{ multiple: false }"
                  clearable
                  filterable
                  size="small"
                  collapse-tags
                  @change="companyChange"
                >
                </el-cascader>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="" class="checkForm">
                <el-checkbox>全选</el-checkbox>
                <el-button
                  size="small"
                  icon="iconfont icon-search"
                  class="blue_btn"
                  @click="searchAct"
                  >查询</el-button
                >
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <div class="content_wrap">
      <div class="left_wrap">
        <header>
          <h3>修改信息：</h3>
        </header>
        <div class="left_content">
          <el-form :model="changeInfo" ref="forms_ref" label-width="80px">
            <el-row>
              <el-col v-if="G_config.physicalMode.includes('普检')">
                <el-form-item label="修改分类">
                  <el-select
                    class="select"
                    v-model.trim="changeInfo.peCls"
                    placeholder="请选择"
                    size="small"
                    filterable
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in G_peClsList"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item label="">
                  <el-checkbox
                    v-model="isVIP"
                    false-label="null"
                    true-label="true"
                    >加上VIP</el-checkbox
                  >
                  <el-checkbox
                    v-model="isVIP"
                    false-label="null"
                    true-label="false"
                    >去除VIP</el-checkbox
                  >
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item label="">
                  <el-checkbox
                    v-model="isLeader"
                    false-label="null"
                    true-label="true"
                    >加上领导</el-checkbox
                  >
                  <el-checkbox
                    v-model="isLeader"
                    false-label="null"
                    true-label="false"
                    >去除领导</el-checkbox
                  >
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <footer style="text-align: center">
          <el-button
            size="small"
            icon="iconfont icon-xuanzhong"
            class="blue_btn"
            @click="confirm"
            >确定</el-button
          >
        </footer>
      </div>
      <div class="right_wrap">
        <PublicTable
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          :tableLoading.sync="loading"
          :columnWidth="columnWidth"
          @selectionChange="handleSelRow"
          isCheck
        >
          <template #age="{ scope }">
            <div>
              {{ scope.row.age + enum_ageUnit[scope.row.ageUnit] }}
            </div>
          </template>
          <template #peCls="{ scope }">
            <div>
              {{ G_EnumList['PeCls'][scope.row.peCls] }}
            </div>
          </template>
          <template #peStatus="{ scope }">
            <div>
              {{ G_EnumList['PeStatus'][scope.row.peStatus] }}
            </div>
          </template>
          <template #sex="{ scope }">
            <div>
              {{ G_EnumList['Sex'][scope.row.sex] }}
            </div>
          </template>
          <template #isActive="{ scope }">
            <div>
              <el-checkbox
                v-model.trim="scope.row.isActive"
                disabled
              ></el-checkbox>
            </div>
          </template>

          <template #isVIP="{ scope }">
            <div>
              <el-checkbox
                v-model.trim="scope.row.isVIP"
                disabled
              ></el-checkbox>
            </div>
          </template>
          <template #isLeader="{ scope }">
            <div>
              <el-checkbox
                v-model.trim="scope.row.isLeader"
                disabled
              ></el-checkbox>
            </div>
          </template>
          <template #marryStatus="{ scope }">
            <div>
              {{ enum_marrStatus[scope.row.marryStatus] }}
            </div>
          </template>
        </PublicTable>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import PublicTable from '../../components/publicTable.vue';
import { dataUtils } from '@/common';
export default {
  name: 'bulkEditing',
  components: { PublicTable },
  props: {
    cancel: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      timeType: [
        {
          value: 1,
          label: '登记时间'
        },
        { value: 2, label: '激活时间' }
      ],
      rules: {},
      loading: false,
      peCls: '',
      companyList: [], //单位
      checkClsList: [], //分类
      regTime: [dataUtils.getDate(), dataUtils.getDate()],
      searchForm: {
        regNo: '',
        name: '',
        queryType: 1,
        startTime: '',
        endTime: '',
        patCode: '',
        companyCode: ''
      },
      isEnabled: '',
      tableData: [], //表单数据
      theads: {
        peStatus: '状态',
        isActive: '激活',
        isVIP: 'VIP',
        isLeader: '领导标识',
        regNo: '体检号',
        patCode: '档案号',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        marryStatus: '婚姻状况',
        cardNo: '身份证',
        peCls: '体检分类'
      },
      enum_marrStatus: {
        0: '未知',
        1: '未婚',
        2: '已婚',
        3: '离异',
        4: '丧偶'
      },
      enum_ageUnit: {
        null: '',
        0: '岁',
        1: '月'
      },
      columnWidth: {
        cardNo: 180,
        regNo: 150,
        patCode: 150
      },
      isVIP: 'null',
      isLeader: 'null',
      changeInfo: {
        regNoArray: [],
        peCls: '',
        isVIP: false,
        isLeader: false
      },
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() > Date.now();
        }
      }
    };
  },
  created() {},
  mounted: function () {
    this.getCompany();
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_peClsList',
      'G_datePickerShortcuts',
      'G_config'
    ])
  },
  methods: {
    handleCheckedValChange(value) {
      console.log(value);
    },

    //获取单位下拉
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) return;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.regTime = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.searchAct();
    },

    //查询
    searchAct() {
      if (!this.regTime) {
        this.searchForm.startTime = '';
        this.searchForm.endTime = '';
      } else {
        this.searchForm.startTime = this.regTime[0] || '';
        this.searchForm.endTime = this.regTime[1] || '';
      }
      console.log('[ this.searchForm ]-314', this.searchForm);
      this.tableData = [];
      const temp = JSON.parse(JSON.stringify(this.searchForm));
      temp.companyCode = temp.companyCode?.[0] || '';
      this.$ajax.post(this.$apiUrls.GetBatchUpdate, temp).then((r) => {
        console.log('r', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.tableData = returnData || [];
      });
    },
    //勾选行
    handleSelRow: function (val) {
      console.log('[ val ]-326', val);
      this.changeInfo.regNoArray = [];
      val.map((item) => {
        if (item.regNo != '') {
          this.changeInfo.regNoArray.push(item.regNo);
        }
      });
      console.log('[ this.changeInfo ]-328', this.changeInfo);
    },
    //确定
    confirm() {
      // console.log("this.changeInfo", this.changeInfo);
      if (this.changeInfo.regNoArray.length < 1) {
        this.$message({
          message: '请先选择要批量修改的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      //修改分类不传默认传-1;
      if (this.changeInfo.peCls == '') {
        this.changeInfo.peCls = -1;
      }
      if (this.isVIP == 'null') {
        this.isVIP = null;
      } else if (this.isVIP == 'false') {
        this.isVIP = false;
      } else {
        this.isVIP = true;
      }
      if (this.isLeader == 'null') {
        this.isLeader = null;
      } else if (this.isLeader == 'false') {
        this.isLeader = false;
      } else {
        this.isLeader = true;
      }
      this.changeInfo.isVIP = this.isVIP;
      this.changeInfo.isLeader = this.isLeader;
      this.$ajax
        .post(this.$apiUrls.BatchUpdateByRegNo, this.changeInfo)
        .then((r) => {
          if (!r.data.success) {
            return;
          }
          this.$message({
            showClose: true,
            message: '批量修改成功!',
            type: 'success'
          });
          this.changeInfo.peCls = '';
          this.isVIP = 'null';
          this.isLeader = 'null';
          this.searchAct();
        });
    }
  }
};
</script>

<style lang="less" scoped>
.bulkEditing {
  display: flex;
  flex-direction: column;
  height: 100%;
  /deep/.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    background-color: #409eff;
    border-color: #409eff;
  }
  .haveCheck {
    /deep/.el-form-item__content {
      display: flex;
    }
  }
  /deep/.patCode {
    width: calc(100% - 65px) !important;
  }
  .headrCont {
    display: flex;
    flex-direction: row;

    border-radius: 4px;
    padding: 15px;
    display: flex;
    flex-direction: row;
    margin: 15px 0;
    padding: 15px;
    background: #fff;
    .regTime {
      /deep/.el-form-item__content {
        margin-left: 10px !important;
        display: flex;
      }
      /deep/.el-input--small {
        width: 100px;
      }
    }
    .headerLeft {
      flex: 1;
    }
    .el-row {
      display: flex;
    }
    .el-form-item {
      margin-bottom: 10px;
      /deep/ .el-form-item__label,
      /deep/.el-form-item__content {
        height: 32px;
        line-height: 32px;
      }
    }
    .checkForm {
      // width: 200px;
      /deep/.el-form-item__content {
        margin-left: 0 !important;
        .el-checkbox {
          margin: 0 20px;
        }
      }
    }
    .headerRight {
      width: 200px;
      display: flex;
    }
  }
  .centerCont {
    display: flex;
    flex-direction: row;
    width: 100%;
    height: 48px;
    line-height: 48px;
    border-radius: 4px;

    .titleCheckbox {
      display: flex;
      background: rgba(60, 179, 79, 0.1);
      border-radius: 2px;
      padding-left: 20px;
      .print-btn {
        margin: 0 20px;
        height: 32px;
        margin-top: 8px;
      }
    }
    .amount {
      margin: 0 20px 0 10px;
      height: 32px;
      width: 76px;
    }
    .btnTxt {
      background: rgba(214, 48, 49, 0.1);
      padding: 0 20px;
      border-radius: 2px;
      border-radius: 2px;
      margin-left: 40px;
      .del-btn {
        margin-right: 30px;
      }
    }
  }
  .content_wrap {
    display: flex;
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    .left_wrap {
      width: 360px;
      margin-right: 20px;
      background-color: #fff;
      border-radius: 5px;
      overflow: auto;
      position: relative;
      padding-top: 92px;
      padding: 15px;

      header {
        border-bottom: 0.5px solid rgba(178, 190, 195, 0.6);
        h3 {
          font-size: 16px;
          color: #2d3436;
          font-weight: 600;
          margin-bottom: 14px;
        }
      }
      .left_content {
        margin-top: 20px;
      }
    }

    .right_wrap {
      flex: 1;
      flex-shrink: 0;
      background-color: #fff;
      border-radius: 5px;
      overflow: auto;
      display: flex;
      padding: 18px;
      & > div {
        border: 1px solid #b2bec3;
        border-radius: 4px;
      }
    }
  }
  .select {
    width: 100%;
  }
  /deep/.el-checkbox__inner {
    width: 18px;
    height: 18px;
  }
  /deep/.el-date-editor.el-input,
  .el-date-editor.el-input__inner {
    width: 100%;
    height: 32px;
  }
  /deep/.el-input__icon,
  /deep/.el-input__inner,
  .el-button {
    height: 32px;
  }
}
</style>
