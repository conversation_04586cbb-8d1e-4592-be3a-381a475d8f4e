import { VueCropper } from 'vue-cropper';

export default {
  components: {
    VueCropper
  },
  data() {
    return {
      clipDrawerShow: false,
      videoShow: true, //显示拍照的视频
      isFoundCamer: true, //获取是否成功调取摄像头
      // ---------
      option: {
        img: '', //裁剪图片的地址
        outputSize: 1, //裁剪生成图片的质量(可选0.1 - 1)
        outputType: 'jpeg', //裁剪生成图片的格式（jpeg || png || webp）
        info: true, //图片大小信息
        canScale: true, //图片是否允许滚轮缩放
        autoCrop: true, //是否默认生成截图框
        autoCropWidth: 200, //默认生成截图框宽度
        autoCropHeight: 250, //默认生成截图框高度
        fixed: true, //是否开启截图框宽高固定比例
        fixedNumber: [1, 1.25], //截图框的宽高比例
        full: false, //false按原比例裁切图片，不失真
        fixedBox: true, //固定截图框大小，不允许改变
        canMove: true, //上传图片是否可以移动
        canMoveBox: true, //截图框能否拖动
        original: true, //上传图片按照原始比例渲染
        centerBox: true, //截图框是否被限制在图片里面
        height: true, //是否按照设备的dpr 输出等比例图片
        infoTrue: true, //true为展示真实输出图片宽高，false展示看到的截图框宽高
        maxImgSize: 3000, //限制图片最大宽度和高度
        enlarge: 1, //图片根据截图框输出比例倍数
        mode: 'auto auto' //图片默认渲染方式
      },
      previews: {},
      fileData: ''
    };
  },
  methods: {
    // 拍照函数
    take_a_picture_fun(canvasDom, videoDom, obj, w = 200, h = 250) {
      this.$nextTick(() => {
        let canvas = this.$refs[canvasDom];
        let video = this.$refs[videoDom];
        let ctx = canvas.getContext('2d');
        // ctx.setTransform(1, 0, 0, 1, 0, 0);
        // ctx.translate((canvas.width), 0);
        // ctx.scale(-1, 1);
        ctx.drawImage(video, 0, 0, w, h);
        this[obj].photoUrl = canvas.toDataURL('image/png');
        console.log(this[obj].photoUrl);
      });
    },
    async take_a_picture() {
      if (this.videoShow) {
        await this.take_a_picture_fun('canvas', 'video', 'searchForm');
        this.dataURLtoFile(this['searchForm'].photoUrl, 'image/jpeg');
        this.closeCamer('video');
      } else {
        this.openCamer('video');
        this.fileData = '';
      }
      this.videoShow = !this.videoShow;
    },
    // 将图片转换为文件格式
    dataURLtoFile(dataURI, type) {
      // console.log(dataURI.split(',')[1]);
      return new Promise((resolve, reject) => {
        if (!dataURI.split(',')[1]) {
          resolve();
          return;
        }
        let binary = atob(dataURI.split(',')[1]);
        // let binary = Buffer.from(dataURI.split(',')[1], 'base64').toString('ascii')
        if (!binary) return;
        console.log(binary);
        let array = [];
        for (let i = 0; i < binary.length; i++) {
          array.push(binary.charCodeAt(i));
        }
        this.fileData = new Blob([new Uint8Array(array)], { type: type });
        resolve(binary);
      });
    },
    // 打开摄像头
    openCamer(
      videoDom,
      options = {
        video: {
          width: 200,
          height: 250,
          facingMode: 'environment'
          //    facingMode: { exact: "environment" }
        }
      }
    ) {
      var that = this;
      try {
        //访问用户媒体设备的兼容方法
        function getUserMedia(constraints, success, error) {
          if (navigator.mediaDevices.getUserMedia) {
            //最新的标准API
            navigator.mediaDevices
              .getUserMedia(constraints)
              .then(success)
              .catch(error);
          } else if (navigator.webkitGetUserMedia) {
            //webkit核心浏览器
            navigator.webkitGetUserMedia(constraints, success, error);
          } else if (navigator.mozGetUserMedia) {
            //firfox浏览器
            navigator.mozGetUserMedia(constraints, success, error);
          } else if (navigator.getUserMedia) {
            //旧版API
            navigator.getUserMedia(constraints, success, error);
          }
        }

        let video = this.$refs[videoDom];
        function success(stream) {
          that.isFoundCamer = true;
          //兼容webkit核心浏览器
          let CompatibleURL = window.URL || window.webkitURL;
          //将视频流设置为video元素的源
          console.log(stream);

          //video.src = CompatibleURL.createObjectURL(stream);
          video.srcObject = stream;
          // this.stream=stream.getTracks()[1];
          video.play();
        }

        function error(error) {
          that.videoShow = false;
          console.log(`访问用户媒体设备失败${error.name}, ${error.message}`);
          that.isFoundCamer = false;
          that.$message({
            message: '没有找到摄像头！！',
            type: 'warning'
          });
        }

        if (
          navigator.mediaDevices.getUserMedia ||
          navigator.getUserMedia ||
          navigator.webkitGetUserMedia ||
          navigator.mozGetUserMedia
        ) {
          //调用用户媒体设备, 访问摄像头
          getUserMedia(options, success, error);
        } else {
          alert('不支持访问用户媒体');
        }
      } catch (error) {
        this.videoShow = false;
        console.log('启用摄像头失败，请用localhost或https');
      }
    },
    // 关闭摄像头
    closeCamer(videoDom) {
      var video = this.$refs[videoDom];
      if (video.srcObject) {
        video.srcObject.getTracks()[0].stop();
      }
      this.isFoundCamer = false;
    },
    // -------
    // 打开拍照截图的抽屉
    clipImg() {
      this.clipDrawerShow = true;
      this.videoShow = false;
      this.closeCamer('video');
      this.$nextTick(() => {
        this.option.img = this.searchForm.photoUrl;
        this.openCamer('drawerVideo', {
          video: {
            width: 450,
            height: 250,
            facingMode: 'environment'
            //    facingMode: { exact: "environment" }
          }
        });
      });
    },
    // 取消裁剪
    cancelClip() {
      this.closeCamer('drawerVideo');
      this.clipDrawerShow = false;
    },
    // 抽屉的拍照功能
    async drawer_take_a_picture() {
      await this.take_a_picture_fun(
        'drawerCanvas',
        'drawerVideo',
        'previews',
        450,
        250
      );
      this.option.img = this.previews.photoUrl;
    },
    // 确定裁剪图片
    confirmClip(type) {
      this.videoShow = false;
      this.$nextTick(() => {
        // 输出
        if (type === 'blob') {
          this.$refs.cropper.getCropBlob((data) => {
            console.log(data);
            this.fileData = data;
            var img = window.URL.createObjectURL(data);
            console.log(img);
            // this.model = true;
            this.searchForm.photoUrl = img;

            this.clipDrawerShow = false;
            this.closeCamer('drawerVideo');
          });
        } else {
          this.$refs.cropper.getCropData((data) => {
            this.searchForm.photoUrl = data;
            this.clipDrawerShow = false;
            this.closeCamer('drawerVideo');
          });
        }
      });

      // this.$nextTick(()=>{
      //     this.clipDrawerShow = false;
      //     this.closeCamer('drawerVideo');
      // })
    },
    // 上传图片
    uploadImg(fileNum) {
      if (this.fileData == '') return;
      let formData = new FormData();
      var file = new File([this.fileData], `${fileNum}.jpg`, {
        type: this.fileData.type,
        lastModified: Date.now()
      });
      console.log(file.name);
      formData.append('files', file);
      console.log(formData);
      this.$ajax
        .post(this.$apiUrls.UploadPhoto + '?regNo=' + fileNum, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: ''
          }
        })
        .then((r) => {
          this.fileData = '';
        });
    },
    //初始化函数
    imgLoad(msg) {
      console.log('工具初始化函数=====' + msg);
    },
    //图片缩放
    changeScale(num) {
      num = num || 1;
      this.$refs.cropper.changeScale(num);
    },
    //向左旋转
    rotateLeft() {
      this.$refs.cropper.rotateLeft();
    },
    //向右旋转
    rotateRight() {
      this.$refs.cropper.rotateRight();
    },
    //实时预览函数
    realTime(data) {
      console.log(data);
      let previews = {
        ...data,
        photoUrl: data.url
      };
      delete previews.url;
      this.previews = previews;
    },
    //选择图片
    selectImg(e) {
      let file = e.target.files[0];
      if (!/\.(jpg|jpeg|png|JPG|PNG)$/.test(e.target.value)) {
        this.$message({
          message: '图片类型要求：jpeg、jpg、png',
          type: 'error'
        });
        return false;
      }
      //转化为blob
      let reader = new FileReader();
      reader.onload = (e) => {
        let data;
        if (typeof e.target.result === 'object') {
          data = window.URL.createObjectURL(new Blob([e.target.result]));
        } else {
          data = e.target.result;
        }
        this.option.img = data;
      };
      //转化为base64
      reader.readAsDataURL(file);
    }
    //上传图片
    // uploadImg(type) {
    //     let _this = this;
    //     if (type === 'blob') {
    //         //获取截图的blob数据
    //         this.$refs.cropper.getCropBlob(async (data) => {
    //             console.log(data);
    //             let formData = new FormData();
    //             formData.append('file', data, "DX.jpg")
    //             //调用axios上传
    //             let { data: res } = await _this.$http.post('/api/file/imgUpload', formData)
    //             if (res.code === 200) {
    //                 _this.$message({
    //                     message: res.msg,
    //                     type: "success"
    //                 });
    //                 let data = res.data.replace('[', '').replace(']', '').split(',');
    //                 let imgInfo = {
    //                     name: _this.Name,
    //                     url: data[0]
    //                 };
    //                 _this.$emit('uploadImgSuccess', imgInfo);
    //             } else {
    //                 _this.$message({
    //                     message: '文件服务异常，请联系管理员！',
    //                     type: "error"
    //                 });
    //             }
    //         })
    //     }
    // },
  },
  mounted() {
    // requestAnimationFrame(this.openCamer);
    this.openCamer('video');
  }
};
