import { _debounce } from '@/common/throttle';
import { mapGetters } from 'vuex';
import { dataUtils } from '@/common';

export default {
  computed: {
    ...mapGetters(['G_userInfo', 'G_EnumList'])
  },
  data() {
    return {
      combDetailShow: false,
      combDetailList: [],
      combDetailTitle: '',
      navLiVal: 'info',
      mealSearchVal: '',
      comboSearchVal: '',
      mealListCode: [],
      mealListCodeObj: {},
      comboListCode: [],
      fixed_comboListCodeObj: {}, //不会变的组合字典
      comboListCodeObj: {}, //组合字典
      // 套餐
      fixed_mealList: [], // 固定的所有套餐列表
      fixed_mealListCodeObj: {}, //固定的所有套餐列表对象
      mealList: [], //套餐列表
      searchMealList: [],
      mealTheads: {
        //套餐的表格头
        clusCode: '编号',
        clusName: '入职套餐',
        price: '单价'
      },
      cell_red: ['price'],
      columnWidth: {
        clusCode: '80',
        price: '80'
      },
      // 组合
      fixed_comboList: [], //不变的组合列表
      comboList: [], //组合列表
      searchComboList: [],
      comboMealTheads: {
        //套餐的表格头
        // combCode:'组合代码',
        combName: '组合名称',
        price: '单价'
      },
      comboCell_red: ['price'],
      comboColumnWidth: {
        // combName: "160"
        price: 80
      },
      // 已选套餐
      checkMealList: [], //套餐列表
      checkSearchMealList: [],
      checkMealTheads: {
        //套餐的表格头
        clusName: '入职套餐',
        price: '单价'
      },
      checkCell_red: ['price'],
      checkColumnWidth: {
        clusName: '160'
      },
      activeCheckMeal: {},
      containCombo: [], // 选中的套餐包含的组合
      containComboCode: [], //选中的套餐包含的组合代码
      // 已选组合
      checkComboList: [], //组合列表
      checkSearchComboList: [],
      checkComboMealTheads: {
        //组合的表格头
        combCode: '组合代码',
        combName: '组合名称',
        // discount: "折扣",
        price: '单价',
        testTubes: '材料费',
        payStatus: '支付状态'
        // applicantName: "开单医生"
      },
      groupCheckComboTheads: {
        //团体组合的表格头
        combCode: '组合代码',
        combName: '组合名称',
        originalPrice: '原价',
        discount: '折扣',
        price: '单价',
        // isPayBySelf: "-",
        testTubes: '材料费',
        // payStatus: "支付状态",
        applicantName: '开单医生'
      },
      checkComboCell_red: ['price', 'testTubes', 'originalPrice'],
      checkComboColumnWidth: {
        combCode: '100',
        // applicantName: "100",
        // discount: "50",
        testTubes: '80',
        originalPrice: '100',
        price: '100',
        payStatus: '100'
      },
      selectCombo: [], // 双击选择组合的列表
      selectComboCode: [], // 双击选择组合的代码列表
      // 已选的套餐对象
      checkMealObj: {},
      checkMealCodeArr: [],
      // 已选的组合对象
      checkComboObj: {},
      // 根据checkComboList的顺序排的已选组合的代码数组
      checkComboCode: [],

      allPrice: 0, //总价格
      feeList: {
        combTotal: '0.00', //组合价格
        materialTotal: '0.00', //材料费
        orderTotal: '0.00', //总价格
        unpaidAmount: '0.00' //未缴费
      },
      // 组合分类
      combTypeList: [],
      combTypeVal: '',
      // 表格快捷键
      mealTableIdx: '',
      combTableIdx: '',
      tabActiveName: 'info',
      // 查询历史已保存到数据库的组合
      saveCombListObj: {},
      isCancelRefundsBtnShow: false
    };
  },
  methods: {
    // 切换登记信息和套餐组合模板
    navLiClick(val) {
      this.navLiVal = val;
    },
    // 获取组合分类
    getCombTypeList() {
      this.$ajax.post(this.$apiUrls.ItemCls).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.combTypeList = returnData || [];
      });
    },
    // 获取套餐列表
    getMealList() {
      this.mealListCode = [];
      this.mealListCodeObj = {};
      return this.$ajax.post(this.$apiUrls.ReadCandidateCluster).then((r) => {
        let { success, returnData } = r.data;
        //
        if (!success) return;
        this.fixed_mealList = dataUtils.deepCopy(returnData || []);
        this.mealList = returnData || [];
        this.searchMealList = returnData || [];
        this.mealList.map((item) => {
          this.mealListCode.push(item.clusCode);
          this.fixed_mealListCodeObj[item.clusCode] = dataUtils.deepCopy(item);
          this.mealListCodeObj[item.clusCode] = item;
        });
        //console.log(this.fixed_mealList);
        Promise.resolve(r);
      });
    },
    // 获取组合列表
    getComboList() {
      return this.$ajax.post(this.$apiUrls.ReadCandidateComb).then((r) => {
        let { success, returnData } = r.data;
        //
        if (!success) return;
        this.fixed_comboList = dataUtils.deepCopy(returnData || []);
        this.comboList = returnData || [];
        this.searchComboList = returnData || [];
        this.comboList.map((item) => {
          this.fixed_comboListCodeObj[item.combCode] = dataUtils.deepCopy(item);
          this.comboListCodeObj[item.combCode] = item;
        });
        Promise.resolve(r);
      });
    },
    // 出来价格保留两位小数
    handlePrice: dataUtils.handlePrice,
    // 套餐列表双击回调
    mealDBClick(row) {
      console.log(row);
      let txt = '';
      console.log(this.checkComboList);
      if (!row.isSelected) {
        this.checkComboList.map((item, idx) => {
          if (row.mutexCombs.includes(item.combCode)) {
            txt += idx == 0 ? item.combName : '，' + item.combName;
          }
        });
      }
      if (txt != '') {
        this.$message({
          message: `该套餐与${txt}组合存在互斥！`,
          type: 'warning',
          duration: 5000,
          showClose: true
        });
        return;
      }
      row.isSelected = !row.isSelected;
      if (row.isSelected) {
        this.checkMealList.push(row);
        this.checkMealObj[row.clusCode] = row?.bindCombs;
        this.checkMealCodeArr.push(row.clusCode);
        this.$refs.form_Ref.searchForm.peCls = row.peCls;
      } else {
        this.checkMealList.splice(
          this.checkMealCodeArr.indexOf(row.clusCode),
          1
        );
        delete this.checkMealObj[row.clusCode];
        this.checkMealCodeArr.splice(
          this.checkMealCodeArr.indexOf(row.clusCode),
          1
        );
      }
      // console.log(this.checkMealObj);
      this.handleCombo();
    },
    // 组合列表的双击回调
    comboDBClick(row) {
      console.log(row, this.containCombo);
      if (this.containComboCode.includes(row.combCode)) return;
      let txt = '';
      let mealTxt = '';
      console.log(this.checkMealList);
      if (!row.isSelected) {
        this.checkMealList.map((item, idx) => {
          if (item.mutexCombs.includes(row.combCode)) {
            mealTxt += idx == 0 ? item?.clusName : '，' + item?.clusName;
          }
        });
        this.checkComboList.map((item, idx) => {
          if (row.mutexCombs.includes(item.combCode)) {
            txt += idx == 0 ? item.combName : '，' + item.combName;
          }
        });
        if (row.sex !== 0 && this.$refs.form_Ref.searchForm.sex != row.sex) {
          this.$message({
            message: `该组合与体检人性别存在互斥！`,
            type: 'warning',
            duration: 5000,
            showClose: true
          });
          return;
        }
      }
      if (mealTxt != '') {
        this.$message({
          message: `该组合与${mealTxt}套餐存在互斥！`,
          type: 'warning',
          duration: 5000,
          showClose: true
        });
        return;
      }
      if (txt != '') {
        this.$message({
          message: `该组合与${txt}组合存在互斥！`,
          type: 'warning',
          duration: 5000,
          showClose: true
        });
        return;
      }
      row.isSelected = !row.isSelected;
      let combCode = row.combCode.trim();
      if (row.isSelected) {
        this.selectCombo.unshift({
          ...row,
          id: 0,
          discount: 1,
          payStatus: 0,
          isPayBySelf: this.isPayBySelf,
          applicantCode: this.G_userInfo.codeOper.operatorCode,
          applicantName: this.G_userInfo.codeOper.name,
          originalPrice: row.price
        });
        this.checkComboObj[combCode] = row;
        this.selectComboCode.unshift(combCode);
      } else {
        this.selectCombo.splice(this.selectComboCode.indexOf(combCode), 1);
        this.selectComboCode.splice(this.selectComboCode.indexOf(combCode), 1);
        delete this.checkComboObj[combCode];
      }
      console.log(this.checkComboObj);
      this.handleCombo();
    },
    // 套餐和组合列表双击选中处理组合列表的选中
    handleCombo() {
      this.comboList.map((item) => {
        item.isSelected = false;
      });
      this.containCombo = [];
      this.containComboCode = [];
      let allPrice = 0; //总价格
      let unpaidAmount = 0;
      let combTotal = 0;
      // console.log(this.checkMealList);
      let checkComboListObj = {};
      this.checkComboList.map((item) => {
        checkComboListObj[item.combCode] = item;
      });
      let mealContainCom = this.checkMealList.reduce((pre, cur, idx) => {
        // allPrice += ~~((cur.price*100).toFixed());
        // console.log(pre,cur);
        // if(cur.isRecord) return pre;
        cur?.bindCombs.map((item) => {
          let comb = this.comboListCodeObj[item.combCode];
          // console.log(comb);
          if (!comb) return;
          if (!this.containComboCode.includes(item.combCode)) {
            console.log(comb);
            comb.isSelected = true;
            let recordItem = this.saveCombListObj[item.combCode];
            let checkCombItem = checkComboListObj[item.combCode];
            let option = {
              price: '',
              discount: ''
            };
            switch (true) {
              case checkCombItem != undefined:
                option.price = checkCombItem.price;
                option.discount = checkCombItem.discount;
                break;

              case recordItem != undefined:
                option.price = recordItem.price;
                option.discount = recordItem.discount;
                break;

              default:
                option.price = item.price;
                option.discount = item.discount;
                break;
            }
            // console.log(recordItem);
            pre.push({
              ...comb,
              id: recordItem ? recordItem.id : 0,
              discount: option.discount,
              payStatus: recordItem ? recordItem.payStatus : 0,
              isPayBySelf: recordItem
                ? recordItem.isPayBySelf
                : this.isPayBySelf,
              applicantCode: recordItem
                ? recordItem.applicantCode
                : this.G_userInfo.codeOper.operatorCode,
              applicantName: recordItem
                ? recordItem.applicantName
                : this.G_userInfo.codeOper.name,
              testTubes: recordItem?.testTubes,
              price: option.price,
              originalPrice: item.originalPrice
            });
            this.containComboCode.push(item.combCode);
            this.containCombo.push(comb);
            // 过滤已收费的组合
            // console.log(comb, recordItem);
            if (
              comb.payStatus === 0 ||
              recordItem?.payStatus === 0 ||
              (!!comb && !comb.payStatus)
            ) {
              unpaidAmount += ~~(
                (recordItem?.price || comb.price) * 100
              ).toFixed();
            }
            combTotal += ~~((recordItem?.price || comb.price) * 100).toFixed();
            allPrice += ~~((recordItem?.price || comb.price) * 100).toFixed();
          }
          let combIdx = this.selectComboCode.indexOf(item);
          if (combIdx != -1) {
            this.selectCombo.splice(combIdx, 1);
            this.selectComboCode.splice(combIdx, 1);
            delete this.checkComboObj[item];
          }
        });
        // console.log(pre);
        return pre;
      }, []);
      // 筛选删除被套餐覆盖的单选的组合
      this.containComboCode.map((item) => {
        let idx = this.selectComboCode.indexOf(item);
        if (!this.selectComboCode.includes(item)) return;
        this.selectCombo.splice(idx, 1);
        this.selectComboCode.splice(idx, 1);
      });
      this.selectCombo.map((item) => {
        let recordItem =
          this.saveCombListObj[item.combCode] || this.saveCombListObj[item.id];
        // console.log(recordItem);
        if (recordItem) {
          (item.id = recordItem.id),
            (item.discount = recordItem.discount),
            (item.payStatus = recordItem.payStatus),
            (item.isPayBySelf = recordItem.isPayBySelf),
            (item.applicantCode = recordItem.applicantCode),
            (item.applicantName = recordItem.applicantName);
        }
        allPrice += ~~(item.price * 100).toFixed();
        let comb = this.comboListCodeObj[item.combCode];
        if (!comb) return;
        this.comboListCodeObj[item.combCode].isSelected = true;
        // 过滤已收费的组合
        if (item.payStatus === 0) {
          unpaidAmount += ~~(item.price * 100).toFixed();
        }
        combTotal += ~~(item.price * 100).toFixed();
      });
      // console.log(this.selectCombo,mealContainCom);
      this.checkComboList = [...this.selectCombo, ...mealContainCom];
      this.feeList.orderTotal = this.handlePrice(allPrice / 100);
      this.feeList.unpaidAmount = this.handlePrice(unpaidAmount / 100);
      this.feeList.combTotal = this.handlePrice(combTotal / 100);
      this.feeList.materialTotal = '0.00';
    },
    handleCombo_old() {
      this.containCombo = [];
      this.containComboCode = [];
      let bindCombsArr = Object.values(this.checkMealObj);
      let allCombo = [];
      let allPrice = 0; //总价格
      bindCombsArr.map((item) => {
        allCombo = [...allCombo, ...item];
      });
      // console.log(allCombo);
      allCombo.map((item) => {
        let comboCode = item.trim();
        if (!this.containComboCode.includes(comboCode)) {
          this.containComboCode.push(comboCode);
          this.containCombo.push(item);
        }
        // if(item.count > this.containCombo[this.containComboCode.indexOf(comboCode)].count){
        //     this.containCombo[this.containComboCode.indexOf(comboCode)].count = item.count
        // }
      });

      // console.log(this.containCombo);
      // 筛选删除被套餐覆盖的单选的组合
      this.containComboCode.map((item) => {
        let idx = this.selectComboCode.indexOf(item);
        if (!this.selectComboCode.includes(item)) return;
        this.selectCombo.splice(idx, 1);
        this.selectComboCode.splice(idx, 1);
      });
      let checkComboList = [];
      this.comboList.map((item) => {
        item.isSelected = false;
        let comboItem =
          this.containCombo[
            this.containComboCode.indexOf(item.combCode.trim())
          ];
        if (this.selectComboCode.includes(item.combCode.trim())) {
          item.isSelected = true;
          allPrice += ~~(item.price * 100);
        }
        if (this.containComboCode.includes(item.combCode.trim())) {
          item.isSelected = true;
          // console.log(this.G_EnumList['CodeItemCls'][item.clsCode]);
          checkComboList.push({
            count: comboItem.count,
            id: this.checkComboObj[item.combCode]?.id || 0,
            combCode: item.combCode,
            combName: item.combName,
            examDeptCode: item.examDeptCode,
            checkCls: item.checkCls,
            clsCode: item.clsCode,
            clsName: this.G_EnumList['CodeItemCls'][item.clsCode],
            // price: comboItem.price,
            // discount: comboItem.discount,
            price: this.checkComboObj[item.combCode]?.price || item.price,
            discount: this.checkComboObj[item.combCode]?.discount || 1,
            isPayBySelf: this.isPayBySelf,
            payStatus: 0,
            applicantCode: this.G_userInfo.codeOper.operatorCode,
            applicantName: this.G_userInfo.codeOper.name,
            DiscountOperCode: '',
            DiscountOperName: '',
            BeFrom: [],
            BeFromJson: '',
            testTubes: this.checkComboObj[item.combCode]?.testTubes
          });
        }
      });
      // 计算套餐的价钱
      this.checkMealList.map((item) => {
        allPrice += ~~(item.price * 100);
      });

      // console.log(checkComboList);
      this.checkComboList = [...this.selectCombo, ...checkComboList];
      this.feeList.orderTotal = this.handlePrice(allPrice / 100);
    },
    // 处理查询记录的数据
    async disposeRecordData() {
      let setData = dataUtils.deepCopy(this.$refs.form_Ref.setData);
      // console.log(setData);
      this.checkMealList = [];
      this.checkMealObj = {};
      this.checkMealCodeArr = [];
      // 处理套餐
      setData.clusters.map((item) => {
        let meal = this.mealListCodeObj[item.clusCode];
        this.checkMealList.push({ ...meal, isRecord: true });
        this.checkMealObj[item.clusCode] = meal?.bindCombs;
        this.checkMealCodeArr.push(item.clusCode);
        this.mealListCodeObj[item.clusCode].isSelected = true;
      });
      // console.log(this.checkMealObj);
      // 处理组合
      let comboIdObj = {};
      this.selectCombo = [];
      this.checkComboObj = {};
      this.selectComboCode = [];
      this.saveCombListObj = {};
      setData.combs.map((item) => {
        // console.log(item);
        if (item.payStatus == 2) {
          this.isCancelRefundsBtnShow = true;
        }
        let combCode = item.combCode.trim();
        item.testTubes = [];
        this.selectCombo.push(item);
        this.checkComboObj[combCode] = item;

        if (item.isExamComb) {
          this.saveCombListObj[combCode] = item;
        } else {
          this.saveCombListObj[item.id] = item;
        }
        this.selectComboCode.push(combCode);
        comboIdObj[item.id] = item;
        comboIdObj[item.id].testTubes = {
          testTubesPrice: 0,
          testTubes: []
        };
      });
      // 处理材料
      // let testTubes = [];
      setData.testTubes.map((item) => {
        // console.log(item.beFrom);
        let testTubes = comboIdObj[item.beFrom[0]].testTubes;
        // console.log(item.price);
        testTubes.testTubesPrice = Math.floor(
          (testTubes.testTubesPrice + item.price) * 100
        );
        testTubes.testTubes.push({
          combName: item.combName,
          discount: item.discount,
          price: item.price
        });
        testTubes.testTubesPrice = testTubes.testTubesPrice / 100;
        // console.log(testTubes.testTubesPrice);
        // comboIdObj[item.beFrom[0]].testTubes.push({
        //     combName:item.combName,
        //     discount:item.discount,
        //     price:item.price
        // })
      });
      // console.log(setData);
      await this.handleCombo();
      this.feeList = setData.feeList;
    },
    // 选中的菜单列表的单击回调
    checkMealClick(row) {
      this.activeCheckMeal = row;
    },
    // 删除套餐
    delMeal(activeCheckMeal = {}) {
      // console.log(this.activeCheckMeal);
      let checkMealCodeArr = Object.keys(this.checkMealObj);
      // console.log(checkMealCodeArr);
      let idx = this.mealListCode.indexOf(activeCheckMeal.clusCode);
      // console.log(this.mealListCode, this.mealList[idx]);
      this.mealList[idx].isSelected = false;
      this.checkMealList.splice(
        this.checkMealCodeArr.indexOf(activeCheckMeal.clusCode),
        1
      );
      this.checkMealCodeArr.splice(
        this.checkMealCodeArr.indexOf(activeCheckMeal.clusCode),
        1
      );
      delete this.checkMealObj[activeCheckMeal.clusCode];
      this.handleCombo();
    },
    // 删除团体组合
    delCombo() {
      let tableCom_Ref = this.$refs.checkCombo_Ref.$refs.tableCom_Ref;
      let checkRow = tableCom_Ref.selection;
      if (checkRow.length === 0) {
        this.$message({
          message: '请先选择需要删除的组合！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (tableCom_Ref.store.states.isAllSelected) {
        // 处理组合
        this.selectCombo = [];
        this.checkComboObj = {};
        this.selectComboCode = [];
        this.handleCombo();
        return;
      }

      checkRow.map((item) => {
        let idx = this.selectComboCode.indexOf(item.combCode.trim());
        if (idx != -1) {
          this.selectCombo.splice(idx, 1);
          this.selectComboCode.splice(idx, 1);
          delete this.checkComboObj[item.combCode.trim()];
        }
      });
      this.handleCombo();
    },
    // 删除个人组合
    personDelCombo() {
      let tableCom_Ref = this.$refs.checkCombo_Ref.$refs.tableCom_Ref;
      let checkRow = tableCom_Ref.selection;
      if (checkRow.length === 0) {
        this.$message({
          message: '请先选择需要删除的组合！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      // console.log(tableCom_Ref.store.states.isAllSelected);
      if (
        tableCom_Ref.store.states.isAllSelected &&
        checkRow.length == this.checkComboList.length
      ) {
        // 处理套餐
        this.checkMealCodeArr.map((item) => {
          this.mealListCodeObj[item].isSelected = false;
        });
        this.checkMealList = [];
        this.checkMealObj = {};
        this.checkMealCodeArr = [];
        // 处理组合
        this.selectCombo = [];
        this.checkComboObj = {};
        this.selectComboCode = [];
        this.handleCombo();
        return;
      }

      // let combValArr = Object.values(this.checkMealObj);
      // let mealCodeArr = Object.keys(this.checkMealObj);
      //console.log(checkRow);
      // console.log(combValArr,mealCodeArr);
      //console.log(this.selectComboCode);
      checkRow.map((item) => {
        //console.log(item);checkComboMealTheads
        let idx = this.selectComboCode.indexOf(item.combCode.trim());
        if (idx != -1) {
          this.selectCombo.splice(idx, 1);
          this.selectComboCode.splice(idx, 1);
          delete this.checkComboObj[item.combCode.trim()];
        }
        let combValArr = Object.values(this.checkMealObj);
        let mealCodeArr = Object.keys(this.checkMealObj);
        combValArr.map((combItem, combIndex) => {
          // let combIdx = combItem.indexOf(item.combCode);
          let combIdx = -1;
          combItem.some((childItem, childIdx) => {
            if (item.combCode == childItem.combCode) {
              combIdx = childIdx;
              return true;
            }
          });
          if (combIdx != -1) {
            let mealCode = mealCodeArr[combIndex];
            let delMealIdx = this.checkMealCodeArr.indexOf(mealCode);
            this.checkMealList.splice(delMealIdx, 1);
            delete this.checkMealObj[mealCode];
            this.checkMealCodeArr.splice(delMealIdx, 1);

            this.mealListCodeObj[mealCode].isSelected = false;

            combItem.map((threeItem) => {
              if (this.selectComboCode.includes(threeItem.combCode)) return;
              if (threeItem.combCode != item.combCode.trim()) {
                let rowData = this.comboListCodeObj[threeItem.combCode];
                this.selectCombo.push({
                  ...rowData,
                  id: 0,
                  discount: 1,
                  payStatus: 0,
                  applicantCode: this.G_userInfo.codeOper.operatorCode,
                  applicantName: this.G_userInfo.codeOper.name
                });
                this.checkComboObj[threeItem.combCode] = rowData;
                this.selectComboCode.push(threeItem.combCode);
              }
            });
          }
        });
        // //console.log(combIdx);
      });
      this.handleCombo();
    },
    // 团体已选组合是否可选
    selectable(row, index) {
      if (this.containComboCode.includes(row.combCode.trim())) {
        return false;
      } else {
        return true;
      }
    },
    // 个人已选组合是否可选
    personSelectable(row, index) {
      if (row.payStatus == 0 || row.payStatus == 3) {
        return true;
      } else {
        return false;
      }
    },
    combTypeChange() {
      //console.log(this.combTypeVal);
    },
    // 套餐当前行改变的回调
    mealCurrentChange(val) {
      this.mealTableIdx = this.searchMealList.indexOf(val);
    },
    // 组合当前行改变的回调
    combCurrentChange(val) {
      this.combTableIdx = this.searchComboList.indexOf(val);
    },
    // 键盘按下事件
    keyDown(e) {
      if (e.keyCode == 9) {
        e.preventDefault();
      }
    },
    // 表格的键盘事件
    keyUp(e) {
      e.preventDefault();
      e.stopPropagation();
      // //console.log(e.keyCode);
      if (this.tabActiveName == 'mealTab' || this.tabActiveName == 'info') {
        // //console.log("meal");
        this.keyEventFun(
          e,
          'searchMealList',
          'mealTable_Ref',
          'mealTableIdx',
          'mealDBClick'
        );
      } else if (this.tabActiveName == 'combTab') {
        //console.log("comb");
        this.keyEventFun(
          e,
          'searchComboList',
          'combTable_Ref',
          'combTableIdx',
          'comboDBClick'
        );
      }
      return false;
    },
    // 表格的键盘事件封装
    keyEventFun(e, searchList, tableRef, rowIdx, DBClickFun) {
      if (
        this[searchList].length == 0 ||
        !this.$refs[tableRef] ||
        !this.$refs[tableRef].$refs
      )
        return;
      // this.$nextTick(()=>{
      let mealTable_ref = this.$refs[tableRef].$refs.tableCom_Ref;
      // //console.log(mealTable_ref.bodyWrapper);
      let bodyWrapper = mealTable_ref.bodyWrapper;

      if (e.keyCode == 40) {
        // 下键
        if (this[rowIdx] === '') {
          this[rowIdx] = 0;
          mealTable_ref.setCurrentRow(this[searchList][this[rowIdx]]);
          return;
        }

        if (this[rowIdx] + 1 >= this[searchList].length) return;
        this[rowIdx] += 1;
        mealTable_ref.setCurrentRow(this[searchList][this[rowIdx]]);
      } else if (e.keyCode == 38) {
        // 上键
        if (this[rowIdx] === '') {
          this[rowIdx] = 0;
          mealTable_ref.setCurrentRow(this[searchList][this[rowIdx]]);
          return;
        }
        if (this[rowIdx] === 0) return;
        this[rowIdx] -= 1;
        mealTable_ref.setCurrentRow(this[searchList][this[rowIdx]]);
      } else if (e.keyCode == 13) {
        if (this[rowIdx] === '') return;
        this[DBClickFun](this[searchList][this[rowIdx]]);
      } else if (e.keyCode == 9) {
        if (this.tabActiveName == 'info') {
          this.tabActiveName = 'mealTab';
        } else if (this.tabActiveName == 'mealTab') {
          this.tabActiveName = 'combTab';
        } else {
          this.tabActiveName = 'info';
        }
      }
      this.$nextTick(() => {
        if (e.keyCode == 38 || e.keyCode == 40) {
          let currentRow = bodyWrapper.getElementsByClassName('current-row');
          //console.log(currentRow[0].offsetTop);
          //console.log(currentRow);
          //console.log(mealTable_ref);
          bodyWrapper.scrollTop = currentRow[0].offsetTop;
        }
      });

      // })
    },
    // 清空套餐和组合数据
    clearMealData() {
      this.mealList = dataUtils.deepCopy(this.fixed_mealList);
      this.searchMealList = this.mealList;
      this.mealListCodeObj = dataUtils.deepCopy(this.fixed_mealListCodeObj);

      this.comboList = dataUtils.deepCopy(this.fixed_comboList);
      // this.comboListCodeObj = dataUtils.deepCopy(this.fixed_comboListCodeObj);
      this.comboListCodeObj = {};
      this.comboList.map((item) => {
        this.comboListCodeObj[item.combCode] = item;
      });
      this.clearFun();
      // return Promise.all([this.getMealList(), this.getComboList()])
    },
    // 不改变套餐数量的清空
    clearcheckMeal() {
      this.checkMealList.map((item) => {
        this.mealListCodeObj[item.clusCode].isSelected = false;
      });
      this.checkComboList.map((item) => {
        let comb = this.comboListCodeObj[item.combCode];
        if (!comb) return;
        this.comboListCodeObj[item.combCode].isSelected = false;
      });
      this.clearFun();
    },
    // 清空的通用函数
    clearFun() {
      // 清空选中的套餐
      this.checkMealList = [];
      this.checkMealObj = {};
      this.checkMealCodeArr = [];
      // 清空选中的组合
      this.selectCombo = [];
      this.checkComboObj = {};
      this.selectComboCode = [];
      this.checkComboList = [];

      this.feeList = {
        combTotal: '0.00', //组合价格
        materialTotal: '0.00', //材料费
        orderTotal: '0.00', //总价格
        unpaidAmount: '0.00' //未缴费
      };
    },
    // 预计价
    budgetPrice() {
      if (this.checkComboList.length == 0) {
        this.$message({
          message: '请先选择套餐和组合！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.feeList = {
        orderTotal: this.handlePrice(
          ~~(
            this.feeList.orderTotal * 100 -
            this.feeList.materialTotal * 100
          ).toFixed() / 100
        ),
        unpaidAmount: this.handlePrice(
          ~~(
            this.feeList.unpaidAmount * 100 -
            this.feeList.materialTotal * 100
          ).toFixed() / 100
        ),
        combTotal: this.feeList.combTotal,
        materialTotal: this.handlePrice(this.feeList.materialTotal)
      };
      let datas = [];
      this.checkComboList.map((item) => {
        datas.push(item.combCode);
      });
      //console.log(datas);
      this.$ajax
        .post(this.$apiUrls.CalculateTestTubeMaterialFees, datas)
        .then((r) => {
          //
          let { success, returnData } = r.data;
          this.feeList = {
            orderTotal: this.handlePrice(
              ~~(this.feeList.orderTotal * 100 + returnData * 100).toFixed() /
                100
            ),
            unpaidAmount: this.handlePrice(
              ~~(this.feeList.unpaidAmount * 100 + returnData * 100).toFixed() /
                100
            ),
            combTotal: this.feeList.combTotal,
            materialTotal: this.handlePrice(returnData)
          };
        });
    },
    // 查看组合明细
    lookDetail(row) {
      //console.log(row);
      this.combDetailTitle = row.combName;
      this.combDetailShow = true;
      this.$ajax
        .paramsPost(this.$apiUrls.ReadCandidateCombItems, {
          combCode: row.combCode
        })
        .then((r) => {
          //
          let { success, returnData } = r.data;
          if (!success) {
            this.combDetailList = [];
            return;
          }
          this.combDetailList = returnData;
        });
    },
    // 折扣改变的回调
    discountChange(row) {
      let price = dataUtils.multiply(row.originalPrice, row.discount);
      console.log(row, price);
      row.price = price;
    }
  },
  watch: {
    tabActiveName(n, o) {
      //console.log(n, o);
      if (n == 'mealTab') {
        this.$nextTick(() => {
          this.$refs.mealSearch_Ref.$el.querySelector('input').focus();
        });
      } else if (n == 'combTab') {
        this.$nextTick(() => {
          this.$refs.combSearch_Ref.$el.querySelector('input').focus();
        });
      }
    }
  },
  created() {
    this.getMealList();
    this.getComboList();
    this.getCombTypeList();
    //console.log(this.mealList);
    // 套餐搜索
    this.mealSearch = _debounce(() => {
      let searchMealList = this.mealList.filter((item) => {
        let form_Ref = this.$refs.form_Ref;
        return (
          (!form_Ref.searchForm.sex
            ? true
            : item.sex == form_Ref.searchForm.sex || item.sex == 0) &&
          item?.clusName.includes(this.mealSearchVal)
        );
      });
      //console.log(searchMealList);
      this.searchMealList = searchMealList;
    }, 500);
    // 组合搜索
    this.comboSearch = _debounce(() => {
      let searchComboList = this.comboList.filter((item) => {
        return (
          (this.combTypeVal ? item.clsCode == this.combTypeVal : true) &&
          (item.combName.includes(this.comboSearchVal) ||
            item.combCode.includes(this.comboSearchVal))
        );
      });
      //console.log(searchComboList);
      this.searchComboList = searchComboList;
    }, 500);
    //console.log(this.G_userInfo);
  },
  mounted() {
    addEventListener('keyup', this.keyUp);
    addEventListener('keydown', this.keyDown);
  },
  activated() {
    if (this.recordShow) {
      this.$nextTick(() => {
        removeEventListener('keyup', this.keyUpFun);
        removeEventListener('keydown', this.keyDownFun);
      });
    }
    addEventListener('keyup', this.keyUp);
    addEventListener('keydown', this.keyDown);
  },
  beforeDestroy() {
    removeEventListener('keyup', this.keyUp);
    removeEventListener('keydown', this.keyDown);
  },
  deactivated() {
    removeEventListener('keyup', this.keyUp);
    removeEventListener('keydown', this.keyDown);
  }
};
