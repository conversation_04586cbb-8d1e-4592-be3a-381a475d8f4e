import { mapGetters } from 'vuex';
import { dataUtils } from '../../../common';
import Moment from 'moment';
import { _debounce } from '@/common/throttle';
import IdCardMixins from '@/components/IdCardMixins';

export default {
  mixins: [IdCardMixins],
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_peClsList',
      'G_cardType',
      'G_userInfo',
      'G_HazardousTypeList',
      'G_peStatus',
      'G_config'
    ]),
    //  已选组合列表中是否存在普通勾选
    checkCombs_c() {
      return this.checkCombs.some((item) => item.isOrdinary === true);
    },
    //已选组合列表中是否存在职业病勾选
    checkCombs_o() {
      return this.checkCombs.some((item) => item.isOccupation === true);
    },
    C_occupation() {
      return this.peCls?.includes('职业检');
    },
    C_ordinary() {
      return /\d/.test(this.peCls);
    },
    C_type() {
      if (this.peCls.length == 2) {
        return 1;
      } else {
        if (this.C_isOccupation) {
          return 3;
        } else {
          return 2;
        }
      }
    },
    isCancelRefundsBtnShow() {
      return this.checkCombs.some((item) => item.payStatus === 2);
    }
  },
  data() {
    return {
      questionnaireDisabled: true,
      discount: 1,
      isWideScreen: window.innerWidth,
      userPeStatus: 0,
      recordShow: false,
      tableMaxHeight: '100%',
      harmList: [],
      harmList_fixed: [],
      harmVal: [],
      // 危害因素
      checkHarmList: [],
      checkHarmObj: {},
      harmDialogShow: false,
      harmLoading: false,
      dialogChcekHarm: [],
      harmTypeVal: null,
      harmKeyword: '',
      harmTheads: [
        {
          label: '编号',
          prop: 'hazardousCode',
          width: 70
        },
        {
          label: '名称',
          prop: 'hazardousName'
        },
        {
          label: '开始接害日期',
          prop: 'date',
          width: 110
        },
        {
          label: '接害工龄',
          prop: 'age',
          width: 100
        }
      ],
      harmMealList: [], //所有候选危害因素套餐
      harmMealListObj: {},
      searchHarmMealList: [], // 搜索后的危害因素套餐列表--绑定视图
      searchHarmMealVal: '',
      mealDialogShow: false,
      mealActiveName: 'meal',
      searchMealVal: '',
      mealTheadsV1: {
        // clusCode: '编号',
        clusName: '套餐名称',
        price: '价格'
      },
      mealColumnWidth: {
        // clusCode: 80,
        price: 80
      },
      mealTheads: [
        {
          label: '套餐名称',
          prop: 'clusName'
        },
        {
          label: '价格',
          prop: 'price',
          width: 80,
          align: 'right'
        }
      ],
      combTypeVal: '',
      comboSearchVal: '',
      combTypeList: [],
      combsTheads: {
        // combCode: '编号',
        combName: 'combName',
        price: 'price'
      },
      comboColumnWidth: {
        // combCode: 100,
        price: 80
      },
      checkMealList: [], //已选套餐数据(用于判断互斥)
      commonMealList: [], //普通套餐
      commonMealListObj: {},
      mealList: [], // 所有的候选套餐列表
      mealListObj: {}, //
      searchMealList: [], //候选的套餐搜索后的列表--绑定视图的
      combList: [], // 所有的候选组合列表
      combListObj: {}, //候选组合码对应的组合信息对象
      searchCombList: [], //候选的组合搜索后的列表--绑定视图的
      candidateMealCheckList: [], //候选套餐的选中列表
      checkMeal: [], //已选的套餐列表
      candidateCombCheckList: [], //候选组合的选中列表
      checkCombs: [], //已选的组合列表
      candidateTestTubes: [], //候选的材料列表
      testTubes: [], //材料列表
      isCanClickconfirmBtn: true,
      oldHazardousCodes: [], //已选的危害因素代码
      regNoSearchVal: '',
      regNoSearchDialogShow: false,
      regNoSearchTheads: [
        {
          label: '状态',
          prop: 'peStatus',
          width: '85'
        },
        {
          label: '激活',
          prop: 'isActive',
          width: '50'
        },
        {
          label: '姓名',
          prop: 'name',
          width: '120',
          showOverflowTooltip: true
        },
        {
          label: '证件号',
          prop: 'cardNo',
          width: '200'
        },
        {
          label: '年龄',
          prop: 'age',
          width: '50'
        },
        {
          label: '体检号',
          prop: 'regNo',
          width: '115'
        },
        {
          label: '档案号',
          prop: 'patCode',
          width: '100'
        },
        {
          label: '体检分类',
          prop: 'peCls',
          width: '80'
        }
        // {
        //     label: '工作单位',
        //     prop: 'companyCode',
        //     width: '',
        //     showOverflowTooltip: true
        // },
        // {
        //     label: '部门',
        //     prop: 'companyDeptCode',
        //     width: '200',
        //     showOverflowTooltip: true
        // },
      ],
      regNoSearchParams: {},
      isShowdialogTable: false,
      formInfo: {
        regNo: '', //体检号
        name: '', //姓名
        sex: '', //性别（0通用1男2女）
        age: '', //年龄
        tel: '', //手机号
        familyMedicalHistory: '', //家族史
        pastMedicalHistory: '', //既往病史
        operationStatus: '', //手术状况
        smokingHabit: '', //吸烟习惯
        drinkingHabit: '', //喝酒习惯
        livingHabit: '', //生活习惯
        currentCondition: '', //现在病况
        questionnaireAnswer: '' //问卷答案
      },
      mealTableIdx: '',
      combTableIdx: '',
      harmMealTableIdx: '',
      combId: 0, //添加组合生成ID
      cell_red: ['price'],
      HisPatInfo: {},
      refundsList: [], //退费列表
      refundsDigShow: false, //退费显示开关
      refundRegNo: '', //退费体检号
      isSwipingCard: false, // 是否刷身份证
      harmDateVal: '',
      idcardInfo: {}, // 刷身份证时的身份信息；
      fixedPrice: 0, // 一口价
      totalOriginalPrice: 0, // 总原价
      batchYearsOfHazards: 0,
      batchMonthsOfHazards: 0
    };
  },
  methods: {
    //组合类型更改回调
    handleCheckboxChange(row, type) {
      if (!row.isOccupation && !row.isOrdinary) {
        row[type] = !row[type];
        this.$message({
          message: '职业勾选和普通勾选不能同时为空',
          type: 'warning',
          showClose: true
        });
      }
    },
    //危害因素鼠标双击编辑
    handleCellEnter(row, column, cell, event) {
      if (row.isOtherHazardous) {
        let itemInput = cell.querySelector('.item__input');
        let itemTxt = cell.querySelector('.item__txt');

        if (itemInput) {
          itemInput.style.display = 'block';
          itemInput.querySelector('input').focus();
        }
        if (itemTxt) {
          itemTxt.style.display = 'none';
        }
      }
    },
    //危害因素鼠标取消编辑
    handleBlur(e) {
      const container = e.target.closest('.hazar-item');
      const input = container.querySelector('.item__input');
      const text = container.querySelector('.item__txt');
      if (input) input.style.display = 'none';
      if (text) text.style.display = 'block';
    },
    //复制
    copyCombination() {
      if (!this.$refs.form_Ref.userInfo.sex) {
        return this.$message({
          message: '请先选择性别！',
          type: 'warning',
          showClose: true
        });
      }
      // if (this.C_isOccupation && !this.$refs.form_Ref.userInfo.jobStatus) {
      //     return this.$message({
      //         message: '请选择在岗状态!',
      //         type: "warning",
      //         showClose: true,
      //     });
      // }
      this.$refs.group_copy.regNo = this.$refs.form_Ref.userInfo.regNo;
      this.$refs.group_copy.centerDialogVisible = true;
    },
    //体检类型勾选去掉触发
    checktyPetype(isItAProfession) {
      // 获取已选套餐中所有组合数据
      // const getUniqueCombsByCode = (list, codeKey) => {
      //     const filteredList = list
      //       ?.filter((item) => this.checkMeal?.some((c) => c.clusCode === item.clusCode))
      //       .flatMap((item) => item.bindCombs?.flat() || []) || [];
      //     return Array.from(new Map(filteredList.map(item => [item[codeKey], item])).values());
      // };

      // const meal = getUniqueCombsByCode(this.mealList, 'combCode');
      // const harm = getUniqueCombsByCode(this.harmMealList, 'combCode');
      let editCombs;
      let checkMealCopy = dataUtils.deepCopy(this.checkMeal);
      if (isItAProfession) {
        editCombs = this.checkCombs.filter((item) => item.isOccupation);
        if (
          editCombs.some(
            (item) =>
              item?.payStatus == 1 || item?.payStatus == 2 || item?.isChecked
          )
        ) {
          return false;
        }
        // this.checkCombs.forEach(item => {
        //     if (item?.payStatus == 1 || item?.payStatus == 2) {
        //         item.isOrdinary = true;
        //     }
        //     item.isOccupation = false;
        // });
        this.checkHarmList = [];
        checkMealCopy = checkMealCopy.filter((i) => !i.isOccupation);
      } else {
        editCombs = this.checkCombs.filter((item) => item.isOrdinary);
        if (
          editCombs.some(
            (item) =>
              item?.payStatus == 1 || item?.payStatus == 2 || item?.isChecked
          )
        ) {
          return false;
        }
        // this.checkCombs.forEach(item => {
        //     if (item?.payStatus == 1 || item?.payStatus == 2) {
        //         item.isOccupation = true;
        //     }
        //     item.isOrdinary = false;
        // });
        checkMealCopy = checkMealCopy.filter((i) => i.isOccupation);
      }
      this.checkCombs = this.checkCombs.filter(
        (item) => !(item.isOccupation === false && item.isOrdinary === false)
      );
      this.checkMeal = checkMealCopy.filter((i) => {
        let bindCombs =
          this.mealList.find((m) => m.clusCode == i.clusCode)?.bindCombs || [];
        if (bindCombs.length === 0) {
          bindCombs =
            this.harmMealList.find((m) => m.clusCode == i.clusCode)
              ?.bindCombs || [];
        }
        return (
          bindCombs.length > 0 &&
          bindCombs.some((c) =>
            this.checkCombs.some((b) => b.combCode == c.combCode)
          )
        );
      });
      // console.log(this.checkMeal);
      return true;
    },
    // 获取套餐列表
    getMealList() {
      this.$ajax.post(this.$apiUrls.ReadCandidateClusterNew).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.mealList = returnData || [];
        if (!this.C_isOccupations) {
          this.mealSearch();
        }

        this.searchMealList = dataUtils.deepCopy(returnData) || [];
        this.commonMealList = dataUtils.deepCopy(returnData) || [];
        let mealListObj = {};

        this.mealList.map((item) => {
          mealListObj[item.clusCode] = item;
        });
        this.mealListObj = mealListObj;
        this.commonMealListObj = dataUtils.deepCopy(mealListObj);
      });
    },
    // 获取组合列表
    getCombList() {
      this.$ajax.post(this.$apiUrls.ReadCandidateCombNew).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.combList = returnData || [];
        if (!this.C_isOccupations) {
          this.comboSearch();
        }

        this.searchCombList = dataUtils.deepCopy(returnData) || [];
        let combListObj = {};
        this.combList.map((item) => {
          item.detailList = [];
          combListObj[item.combCode] = item;
        });
        this.searchCombList = dataUtils.deepCopy(this.combList) || [];
        this.combListObj = combListObj;
      });
    },
    // 获取组合分类
    getCombTypeList() {
      this.$ajax.post(this.$apiUrls.ItemCls).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.combTypeList = returnData || [];
      });
    },
    // 获取危害因素
    getHarmList() {
      let datas = {
        pageSize: 0,
        pageNumber: 0
      };
      this.$ajax
        .post(this.$apiUrls.ReadCodeOccupationalHazardousByPage, datas)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          // this.harmList = returnData;
          //isOtherHazardous
          this.harmList_fixed = returnData.map((item) => {
            if (item.isOtherHazardous) {
              item.hazardousAlias = item.hazardousName;
            }
            return item;
          });
        });
    },
    // 获取危害因素套餐
    getHarmMealList(jobStatus = '') {
      let datas = {
        jobStatus: jobStatus
      };
      this.$ajax
        .paramsPost(this.$apiUrls.ReadOccupationalCandidateCluster, datas)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.harmMealList = returnData || [];

          this.searchHarmMealList = dataUtils.deepCopy(returnData) || [];
          let harmMealListObj = {};
          this.harmMealList.map((item) => {
            harmMealListObj[item.clusCode] = item;
          });
          this.harmMealListObj = harmMealListObj;
        });
    },
    // 添加危害因素弹窗显示的回调
    harmDialogOpen() {
      this.harmList = dataUtils.deepCopy(this.harmList_fixed);
      this.dialogChcekHarm = dataUtils.deepCopy(this.checkHarmList);
      let checkHarmCode = this.dialogChcekHarm.map(
        (item) => item.hazardousCode
      );
      this.harmList.map((item) => {
        if (checkHarmCode.includes(item.hazardousCode)) {
          item.isSelected = true;
          if (item.isOtherHazardous) {
            const harmItem = this.checkHarmList.find(
              (h) => h.hazardousCode == item.hazardousCode
            );
            //修改搜索用的数据以及视图数据
            const idx = this.harmList_fixed.findIndex(
              (f) => f.hazardousCode == item.hazardousCode
            );
            this.harmList_fixed[idx].hazardousAlias = harmItem.hazardousName;
            item.hazardousAlias = harmItem.hazardousName;
          }
        }
      });
      this.harmKeyword = '';
      this.harmTypeVal = null;
      this.$nextTick(() => {
        this.$refs.vxeList_Ref?.refreshScroll();
      });
    },
    // 候选危害因素的双击回调
    harmListDblClick(row, e) {
      row.isSelected = !row.isSelected;
      let date = Moment()
        .subtract(this.$refs.form_Ref.userInfo.totalMonthsOfWork, 'month')
        .format('YYYY-MM-DD');
      date = Moment(date)
        .subtract(this.$refs.form_Ref.userInfo.totalYearsOfWork, 'years')
        .format('YYYY-MM-DD');
      if (row.isSelected) {
        this.dialogChcekHarm.push({
          hazardousCode: row.hazardousCode,
          hazardousName: row.hazardousName,
          startDateOfHazards: date,
          isOtherHazardous: row.isOtherHazardous,
          yearsOfHazards: this.$refs.form_Ref.userInfo.totalYearsOfWork,
          monthsOfHazards: this.$refs.form_Ref.userInfo.totalMonthsOfWork
        });
        this.$nextTick(() => {
          if (row.isOtherHazardous) {
            let listItem = e.target.closest('.my-list-item');
            let input = listItem?.querySelector('.hazardousAlias_input');
            input?.querySelector('input').focus();
          }
        });
      } else {
        if (row.isOtherHazardous) {
          row.hazardousAlias = row.hazardousName;
        }
        this.dialogChcekHarm = this.dialogChcekHarm.filter(
          (item) => item.hazardousCode != row.hazardousCode
        );
      }
    },
    // 确定添加危害因素
    harmConfirm() {
      if (!this.$refs.form_Ref.userInfo.isOccupation) {
        return this.$message({
          message: '体检类型未勾选职业检!',
          type: 'warning',
          showClose: true
        });
      }
      let oldHazardousCodes = this.checkHarmList.map(
        (item) => item.hazardousCode
      );
      let newHazardousCodes = this.dialogChcekHarm.map(
        (item) => item.hazardousCode
      );
      let selectedHarmList = dataUtils.deepCopy(this.dialogChcekHarm);
      this.checkHarmList = selectedHarmList.map((item) => {
        if (item.isOtherHazardous) {
          const harmItem = this.harmList.find(
            (h) => h.hazardousCode == item.hazardousCode
          );
          item.hazardousName = harmItem.hazardousAlias;
        }
        return item;
      });
      this.harmDialogShow = false;

      let deleteHazardousCodes = oldHazardousCodes.filter(
        (item) => !newHazardousCodes.includes(item)
      );
      let addHazardousCodes = newHazardousCodes.filter(
        (item) => !oldHazardousCodes.includes(item)
      );
      this.hazardOutCombination();
      //console.log(addHazardousCodes,deleteHazardousCodes);
    },
    // 已选的危害因素删除
    checkHarmDel() {
      let oldHazardousCodes = [];
      let checkHarmList = this.$refs.checkHarmList_Ref.selection.map(
        (item) => item.hazardousCode
      );
      if (checkHarmList.length === 0) {
        this.$message({
          message: '请先选择需要删除的危害因素！',
          type: 'warning',
          showClose: true
        });
        return;
      }

      this.checkHarmList = this.checkHarmList.filter((item) => {
        oldHazardousCodes.push(item.hazardousCode);
        return !checkHarmList.includes(item.hazardousCode);
      });
      this.hazardOutCombination();
      //console.log(oldHazardousCodes);
    },
    // 开始接害日期改变的回调
    harmDateChange(row) {
      let currentDate = Moment();
      let startDate = Moment(Moment(row.startDateOfHazards).format('YYYY-MM'));
      let duration = Moment.duration(currentDate.diff(startDate));
      let { _data } = duration;
      row.yearsOfHazards = _data.years;
      row.monthsOfHazards = _data.months;
      this.changeLengthOfService();
    },
    // 添加套餐按钮点击回调
    addMealBtn() {
      if (this.C_isOccupation) {
        console.log(this.$refs.form_Ref.userInfo.jobStatus);
        if (!this.$refs.form_Ref.userInfo.jobStatus) {
          return this.$message({
            message: '请选择在岗状态!',
            type: 'warning',
            showClose: true
          });
        }
      }
      if (this.$refs.form_Ref.userInfo.peCls.length === 0) {
        return this.$message({
          message: '请选择体检类型!',
          type: 'warning',
          showClose: true
        });
      }
      this.mealDialogShow = true;
    },
    // 添加套餐弹窗打开的回调
    async mealDialogOpen() {
      this.searchMealVal = '';
      this.combTypeVal = '';
      this.comboSearchVal = '';
      this.searchHarmMealVal = '';
      this.candidateMealCheckList = dataUtils.deepCopy(this.checkMeal);
      this.candidateCombCheckList = dataUtils.deepCopy(this.checkCombs);
      this.candidateTestTubes = dataUtils.deepCopy(this.testTubes);
      if (this.candidateCombCheckList.length != 0) {
        this.isCanClickconfirmBtn = true;
      }

      this.mealSearch();
      this.comboSearch();
      this.harmMealSearch();
    },
    // 候选套餐的单元格设置
    mealCellClassName(row, rowIndex, column, columnIndex) {
      if (column.property == 'price') return 'tr_red';
    },
    // 候选套餐的双击回调
    mealDBClick(row) {
      if (!this.checkDelCombination(row) && row.isSelected) {
        this.$message({
          message: '该套餐存在已缴费或已检查的组合!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.userPeStatus > 2) {
        return this.$message({
          message: '该体检信息已完成，无法进行操作!',
          type: 'warning',
          showClose: true
        });
      }
      if (!this.C_isOccupations) {
        if (this.$refs.form_Ref.userInfo.peCls.length === 0) {
          return this.$message({
            message: '请选择体检类型!',
            type: 'warning',
            showClose: true
          });
        }
      }
      if (this.checkMeal.length === 0 && !this.C_isOccupations) {
        this.mainPackage(row);
      }
      // 套餐包含的组合代码数组  倒序保持顺序不乱
      let rowBindCombs = row?.bindCombs.reverse();
      let includeCombs = rowBindCombs.map((item) => item.combCode);
      // 获取是否互斥
      let mutexText = '';
      let checkCombsCopy = this.C_isOccupations
        ? this.candidateCombCheckList
        : this.checkCombs;
      let checkCombsCodeArr = checkCombsCopy.map((item) => {
        if (row.mutexCombs.includes(item.combCode)) {
          mutexText += mutexText == '' ? item.combName : `;${item.combName}`;
        }
        includeCombs.map((twoItem) => {
          if (item.mutexCombs.includes(twoItem)) {
            mutexText += mutexText == '' ? item.combName : `;${item.combName}`;
          }
        });
        return item.combCode;
      });
      //console.log(checkCombsCodeArr);
      if (mutexText != '' && !row.isSelected) {
        this.$message({
          message: `该套餐与${mutexText}存在互斥！`,
          type: 'warning',
          showClose: true
        });
        return;
      }
      row.isSelected = !row.isSelected;
      // 获取已选套餐的code数组
      let checkMealCopy = this.C_isOccupations
        ? this.candidateMealCheckList
        : this.checkMeal;
      console.log(dataUtils.deepCopy(checkMealCopy));
      let checkMealCodeArr = checkMealCopy.map((item) => item.clusCode);
      let isOrdinaryMeal = row?.jobStatus === '' || row?.jobStatus === null;
      if (row.isSelected) {
        // let userInfo = this.$refs.form_Ref.userInfo;
        checkMealCopy.push({
          clusCode: row.clusCode,
          clusName: row.clusName,
          price: row.price,
          isOccupation: !isOrdinaryMeal
        });
        includeCombs.map((item, idx) => {
          if (checkCombsCopy.some((i) => i.combCode == item)) {
            let idx = checkCombsCopy.findIndex((i) => i.combCode == item);
            if (isOrdinaryMeal) checkCombsCopy[idx].isOrdinary = true;
            else checkCombsCopy[idx].isOccupation = true;
            return;
          }
          let bindComb =
            checkCombsCopy.find((c) => c.combCode === item) ||
            rowBindCombs[idx];
          let combItem = this.combListObj[item];
          checkCombsCopy.unshift({
            ...combItem,
            isGathered: false,
            isOrdinary: bindComb.isOrdinary || isOrdinaryMeal,
            isOccupation: bindComb.isOccupation || !isOrdinaryMeal,
            originalPrice: bindComb.originalPrice,
            price: bindComb.price,
            discount: bindComb.discount,
            payStatus: 0,
            isPayBySelf: bindComb.isPayBySelf
          });
        });

        // console.log(this.checkCombs);
        this.checkMealList.push(row);
      } else {
        this.checkMealList.splice(this.checkMealList.indexOf(row.clusCode), 1);
        let delMealIdx = checkMealCodeArr.indexOf(row.clusCode);
        checkMealCodeArr.splice(delMealIdx, 1);
        checkMealCopy.splice(delMealIdx, 1);
        let checkMealCombsArr = []; // 剩余已选套餐包含的组合代码数组；
        checkMealCodeArr.map((item) => {
          let codeArr = (
            this.mealListObj[item] || this.harmMealListObj[item]
          ).bindCombs.map((twoItem) => twoItem.combCode);
          checkMealCombsArr.push(...codeArr);
        });

        row.bindCombs.map((item) => {
          let combIdx = checkCombsCodeArr.indexOf(item.combCode);
          if (combIdx == -1 || checkMealCombsArr.includes(item.combCode)) {
            // 仅去掉 职/普同时勾选的组合 其中一项
            if (isOrdinaryMeal) {
              checkCombsCopy[combIdx].isOrdinary =
                !checkCombsCopy[combIdx].isOccupation;
            } else {
              checkCombsCopy[combIdx].isOccupation =
                !checkCombsCopy[combIdx].isOrdinary;
            }
            return;
          }
          checkCombsCodeArr.splice(combIdx, 1);
          checkCombsCopy.splice(combIdx, 1);
        });
      }
      if (!this.G_config.physicalMode.includes('职检')) {
        this.mealConfirm();
      }
    },
    // 获取带出组合的互斥
    getBinCombsMutexText(binCombsArr) {
      let checkCombsCopy = this.C_isOccupations
        ? this.candidateCombCheckList
        : this.checkCombs;
      let bindCombs = [];
      binCombsArr.map((item) => {
        let mutexText = '';
        let row = this.combListObj[item];
        let isSelect = false; // 是否已选；
        checkCombsCopy.map((twoItem) => {
          if (row.mutexCombs.includes(twoItem.combCode)) {
            mutexText +=
              mutexText == '' ? twoItem.combName : `;${twoItem.combName}`;
          }
          return twoItem.combCode;
        });
        this.checkMealList.map((twoItem) => {
          if (twoItem.mutexCombs.includes(row.combCode)) {
            mutexText +=
              mutexText == '' ? twoItem.clusName : `;${twoItem.clusName}`;
          }
          return twoItem.combCode;
        });
        // 获取是否已存在已选列表
        this.checkCombs.some((twoItem) => {
          if (twoItem.combCode == item) {
            isSelect = true;
            return true;
          }
        });
        if (!mutexText && !isSelect) {
          bindCombs.push(item);
        }
      });
      return bindCombs;
    },
    // 候选组合的双击回调
    combDbClick(row) {
      if (this.userPeStatus > 2) {
        return this.$message({
          message: '该体检信息已完成，无法进行操作!',
          type: 'warning',
          showClose: true
        });
      }
      const combination = this.checkCombs.filter(
        (item) =>
          (item.payStatus !== 0 && item.payStatus !== 3) || item.isChecked
      );
      if (combination.some((item) => item.combCode === row.combCode)) {
        return this.$message({
          message: '该组合已缴费或已检查!',
          type: 'warning',
          showClose: true
        });
      }
      if (!this.C_isOccupations) {
        if (this.$refs.form_Ref.userInfo.peCls.length === 0) {
          return this.$message({
            message: '请选择体检类型!',
            type: 'warning',
            showClose: true
          });
        }
      }
      console.log(row);
      // 获取是否互斥
      let mutexText = '';
      let checkCombsCopy = this.C_isOccupations
        ? this.candidateCombCheckList
        : this.checkCombs;
      let checkCombsCodeArr = checkCombsCopy.map((item) => {
        if (row.mutexCombs.includes(item.combCode)) {
          mutexText += mutexText == '' ? item.combName : `;${item.combName}`;
        }
        return item.combCode;
      });
      this.checkMealList.map((item) => {
        if (item.mutexCombs.includes(row.combCode)) {
          mutexText += mutexText == '' ? item.clusName : `;${item.clusName}`;
        }
        return item.combCode;
      });
      //console.log(checkCombsCodeArr);
      if (mutexText != '') {
        this.$message({
          message: `该组合与${mutexText}存在互斥！`,
          type: 'warning',
          showClose: true
        });
        return;
      }
      row.isSelected = !row.isSelected;
      let userInfo = this.$refs.form_Ref.userInfo;
      if (row.isSelected) {
        let checkCombs = [];
        checkCombs.push({
          ...row,
          isGathered: false,
          isOrdinary: !this.C_isOccupation,
          isOccupation: this.C_isOccupation,
          originalPrice: 0,
          discount: 1,
          payStatus: 0
        });
        this.getBinCombsMutexText(row.bindCombs).map((item) => {
          let itemRow = this.combListObj[item];
          console.log(itemRow);
          if (!itemRow) return;
          checkCombs.push({
            ...itemRow,
            isGathered: false,
            isOrdinary: !this.C_isOccupation,
            isOccupation: this.C_isOccupation,
            originalPrice: 0,
            discount: 1,
            payStatus: 0
          });
        });
        checkCombsCopy.unshift(...checkCombs);
        this.combsSelectStatu();
      } else {
        let checkMealCopy = this.C_isOccupations
          ? this.candidateMealCheckList
          : this.checkMeal;
        checkMealCopy = checkMealCopy.filter((item) => {
          //console.log(item);
          let isIncludes = false;
          (
            this.mealListObj[item.clusCode] ||
            this.harmMealListObj[item.clusCode]
          ).bindCombs.some((twoItem) => {
            if (twoItem.combCode == row.combCode) {
              isIncludes = true;
              return true;
            }
          });
          return !isIncludes;
        });
        let combIdx = checkCombsCodeArr.indexOf(row.combCode);
        checkCombsCopy.splice(combIdx, 1);
        if (this.containComboCode.includes(row.combCode.trim())) {
          this.$confirm('是否保留套餐名？', '提示', {
            confirmButtonText: '保留',
            cancelButtonText: '不保留',
            type: 'warning',
            closeOnClickModal: false,
            closeOnPressEscape: false,
            showClose: false
          })
            .then(() => {})
            .catch(() => {
              if (this.C_isOccupations) {
                this.candidateMealCheckList = checkMealCopy;
              } else {
                this.checkMeal = checkMealCopy;
              }
            });
        }
      }
      if (!this.G_config.physicalMode.includes('职检')) {
        this.mealConfirm();
      }
    },
    // 候选套餐/组合弹窗的确定回调
    async mealConfirm() {
      // let checkCombsCode = this.checkCombs.map(item => item.combCode);
      let checkMealCopy = this.C_isOccupations
        ? this.candidateMealCheckList
        : this.checkMeal;
      let checkCombsCopy = this.C_isOccupations
        ? this.candidateCombCheckList
        : this.checkCombs;
      let testTubesCopy = this.C_isOccupations
        ? this.candidateTestTubes
        : this.testTubes;
      let checkCombsCode = checkCombsCopy.map((item) => item.combCode);
      console.log(checkCombsCopy);
      checkCombsCopy.map((item) => {
        let idx = checkCombsCode.indexOf(item.combCode);
        item.discount = idx != -1 ? checkCombsCopy[idx].discount : 1;
        item.originalPrice = item.originalPrice || item.price;
        item.price = idx != -1 ? checkCombsCopy[idx].price : item.originalPrice;
        item.payStatus = idx != -1 ? checkCombsCopy[idx].payStatus : 0;
      });
      if (
        this.checkMeal.length === 0 &&
        this.C_isOccupations &&
        checkMealCopy.length > 0
      ) {
        let temp = this.mealList.find(
          (item) => item.clusCode === checkMealCopy[0].clusCode
        );
        if (!temp) {
          temp = this.harmMealList.find(
            (item) => item.clusCode === checkMealCopy[0].clusCode
          );
          temp.peCls = '职业检';
        }
        this.mainPackage(temp);
      }
      //套餐变化处理危害因素
      if (this.C_isOccupations) {
        this.editHarm(checkMealCopy, dataUtils.deepCopy(this.checkMeal));
      }
      this.checkMeal = dataUtils.deepCopy(checkMealCopy);
      this.checkCombs = dataUtils.deepCopy(checkCombsCopy);
      this.testTubes = dataUtils.deepCopy(testTubesCopy);
      //console.log(this.checkMeal,this.checkCombs,this.testTubes);

      //设置折扣,折后价,支付状态默认值
      // this.checkCombs.forEach(item => {
      //     item.discount = item.discount || 1;
      //     item.originalPrice = item.originalPrice || item.price;
      //     item.payStatus = item.payStatus || 0;
      // });

      await this.disposeTestTubes(true);
      // await this.getMaterial()
      this.$nextTick(() => {
        this.mealDialogShow = false;
      });
    },
    /**
     * 套餐变化修改危害因素
     * @param {*} n 新的已选套餐
     * @param {*} o 旧的已选套餐
     */
    editHarm(n, o) {
      const addList = n.filter(
        (item) => !o.some((i) => i.clusCode == item.clusCode)
      );
      const delList = o.filter(
        (item) => !n.some((i) => i.clusCode == item.clusCode)
      );
      if (addList && addList.length > 0) {
        const addMealHarm = this.backHarm(addList);
        let date = Moment()
          .subtract(this.$refs.form_Ref.userInfo.totalMonthsOfWork, 'month')
          .format('YYYY-MM-DD');
        date = Moment(date)
          .subtract(this.$refs.form_Ref.userInfo.totalYearsOfWork, 'years')
          .format('YYYY-MM-DD');
        Object.entries(addMealHarm).forEach(([key, value]) => {
          if (!this.checkHarmList.some((item) => item.hazardousCode == key))
            this.checkHarmList.push({
              hazardousCode: key,
              hazardousName: value,
              startDateOfHazards: date,
              isOtherHazardous: false,
              yearsOfHazards: this.$refs.form_Ref.userInfo.totalYearsOfWork,
              monthsOfHazards: this.$refs.form_Ref.userInfo.totalMonthsOfWork
            });
        });
      }
      if (delList && delList.length > 0) {
        const delMealHarm = Object.keys(this.backHarm(delList));
        this.harmTagDel(delMealHarm, n);
      }
    },
    // 处理材料费
    disposeTestTubes(requestOrNot) {
      let comboIdObj = {};
      this.checkCombs.map((item) => {
        comboIdObj[item.id] = item;
        comboIdObj[item.id].testTubes = {
          testTubesPrice: 0,
          testTubes: []
        };
      });
      // this.testTubes.map(item => {
      //     if (comboIdObj[item.beFrom[0]]) {
      //         let testTubes = comboIdObj[item.beFrom[0]].testTubes;
      //         testTubes.testTubesPrice = Math.floor((testTubes.testTubesPrice + item.price) * 100);
      //         testTubes.testTubes.push(item);
      //         testTubes.testTubesPrice = testTubes.testTubesPrice / 100;
      //     }
      // });
      this.getMaterial(requestOrNot);
    },
    //获取材料费数据
    async getMaterial(requestOrNot) {
      // console.log('🚀 ~ getMaterial ~ this.testTubes:', this.testTubes);

      this.checkCombs = this.checkCombs.map((item) => {
        return {
          id: item.id ? item.id : this.generateUniqueNumericId(),
          ...item
        };
      });
      let temp;
      if (!requestOrNot) {
        temp = dataUtils.deepCopy(this.testTubes);
        this.processingMaterialCosts(temp);
        return;
      }
      let data = {
        clusters: this.checkMeal,
        combs: this.checkCombs,
        testTubes: this.testTubes,
        isCompanyCheck: true
      };
      await this.$ajax
        .post(this.$apiUrls.RealtimeCalculateTestTubeNew, data)
        .then((r) => {
          // console.log("🚀 ~ awaitthis.$ajax.post ~ r:", r)
          let { success, returnData } = r.data;
          if (!success) return;
          this.testTubes = [...returnData];
          temp = dataUtils.deepCopy(this.testTubes);
          this.processingMaterialCosts(temp);
        });
    },
    processingMaterialCosts(temp) {
      temp.forEach((item) => {
        let index = this.checkCombs.findIndex((i) => i.id == item.beFrom[0]);
        if (index !== -1) {
          let newObj = {
            combName: item.combName,
            discount: item.discount,
            price: item.price
          };
          let exists = this.checkCombs[index].testTubes.testTubes.some(
            (tube) => tube.combName === newObj.combName
          );
          if (!exists) {
            this.checkCombs[index].testTubes.testTubes.push(newObj);
          }
        }
      });

      this.checkCombs.forEach((c) => {
        c.testTubes.testTubesPrice = c.testTubes.testTubes.reduce(
          (sum, testTube) => dataUtils.add(sum, testTube.price),
          0
        );
      });
    },
    //生成id
    generateUniqueNumericId() {
      // const timestamp = Date.now().toString();
      // const randomNum = Math.floor(Math.random() * 10000).toString();
      // return timestamp + randomNum;
      this.combId++;
      return this.combId;
    },
    // 候选套餐/组合tabs的tab-click回调
    mealTabsTabClick() {
      if (this.mealActiveName == 'meal') {
        this.mealSelectStatu();
      } else if (this.mealActiveName == 'comb') {
        this.combsSelectStatu();
      } else {
        this.harmMealStatu();
      }
    },
    // 套餐的选中状态
    mealSelectStatu() {
      let checkMealCodeArr = (
        this.C_isOccupations ? this.candidateMealCheckList : this.checkMeal
      ).map((item) => item.clusCode);
      //console.log(checkMealCodeArr);
      this.searchMealList.map((item) => {
        item.isSelected = false;
        if (checkMealCodeArr.includes(item.clusCode)) {
          item.isSelected = true;
        }
      });
    },
    // 组合的选中状态
    combsSelectStatu() {
      let checkCombCodeArr = (
        this.C_isOccupations ? this.candidateCombCheckList : this.checkCombs
      ).map((item) => item.combCode);
      this.searchCombList.map((item) => {
        item.isSelected = false;
        if (checkCombCodeArr.includes(item.combCode)) {
          item.isSelected = true;
        }
      });
    },
    // 危害因素套餐选中状态
    harmMealStatu() {
      let checkMealCodeArr = this.candidateMealCheckList.map(
        (item) => item.clusCode
      );
      //console.log(checkMealCodeArr);
      this.searchHarmMealList.map((item) => {
        item.isSelected = false;
        if (checkMealCodeArr.includes(item.clusCode)) {
          item.isSelected = true;
        }
      });
    },
    // 检查是否存在已缴费或已检查的组合
    checkDelCombination(mealCombination) {
      const delCombination = this.checkCombs.filter((item) =>
        mealCombination.bindCombs.some((i) => i.combCode == item.combCode)
      );
      if (
        delCombination.some(
          (item) =>
            (item.payStatus !== 0 && item.payStatus !== 3) || item.isChecked
        )
      ) {
        return false; // 返回 false 表示存在无法删除的组合
      }
      return true; // 返回 true 表示可以删除
    },
    // 已选套餐列表的删除回调
    mealTagDel(tag, idx) {
      let mealCombination = this.mealList.find(
        (item) => item.clusCode == tag.clusCode
      );
      if (mealCombination === undefined) {
        mealCombination = this.harmMealList.find(
          (item) => item.clusCode == tag.clusCode
        );
      }
      if (!this.checkDelCombination(mealCombination)) {
        this.$message({
          message: '该套餐存在已缴费或已检查的组合!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.checkMealList.splice(idx, 1);
      this.checkMeal.splice(idx, 1);
      this.candidateMealCheckList.splice(idx, 1);
      let checkMealCodeArr = this.checkMeal.map((item) => item.clusCode);
      let checkCombsCodeArr = this.checkCombs.map((item) => {
        return item.combCode;
      });
      console.log(checkMealCodeArr);
      let checkMealCombsArr = []; // 剩余已选套餐包含的组合代码数组；
      checkMealCodeArr.map((item) => {
        let codeArr = (
          this.mealListObj[item] || this.harmMealListObj[item]
        ).bindCombs.map((twoItem) => twoItem.combCode);
        checkMealCombsArr.push(...codeArr);
      });

      console.log(checkMealCombsArr);
      let isOrdinaryMeal =
        mealCombination?.jobStatus === '' ||
        mealCombination?.jobStatus === null;
      (
        this.mealListObj[tag.clusCode] || this.harmMealListObj[tag.clusCode]
      ).bindCombs.map((item) => {
        let combIdx = checkCombsCodeArr.indexOf(item.combCode);
        if (combIdx == -1 || checkMealCombsArr.includes(item.combCode)) {
          if (isOrdinaryMeal) {
            this.checkCombs[combIdx].isOrdinary =
              !this.checkCombs[combIdx].isOccupation;
          } else {
            this.checkCombs[combIdx].isOccupation =
              !this.checkCombs[combIdx].isOrdinary;
          }
          return;
        }
        checkCombsCodeArr.splice(combIdx, 1);
        this.checkCombs.splice(combIdx, 1);
      });
      let meal =
        this.mealList.find((item) => item.clusCode == tag.clusCode) ||
        this.harmMealList.find((item) => item.clusCode == tag.clusCode);
      if (
        meal?.bindHazardFactors &&
        Object.keys(meal.bindHazardFactors).length > 0
      ) {
        let hazardousCodes = Object.keys(meal.bindHazardFactors);
        this.harmTagDel(hazardousCodes);
      }
      this.$nextTick(() => {
        this.mealTabsTabClick();
        this.disposeTestTubes(true);
      });
    },
    /**
     * 已选危害因素删除
     * @param {*} data 需要删除的危害因素数组
     * @param {*} meal 已选择的套餐列表
     */
    harmTagDel(data, meal = this.checkMeal) {
      let backHarmReturnValue = Object.keys(this.backHarm(meal));
      //过滤掉已选套餐列表中包含的危害因素
      data = data.filter((item) => !backHarmReturnValue.includes(item));
      this.checkHarmList = this.checkHarmList.filter(
        (item) => !data.includes(item.hazardousCode)
      );
    },
    /**
     *
     * @param {*} mealList  套餐列表
     * @returns 返回参数中所包含的危害因素(去重)
     */
    backHarm(mealList) {
      let harmList = {};
      mealList.map((item) => {
        let meal =
          this.harmMealList.find((m) => m.clusCode == item.clusCode) ||
          this.mealList.find((m) => m.clusCode == item.clusCode);
        if (
          meal?.bindHazardFactors &&
          Object.keys(meal.bindHazardFactors).length > 0
        ) {
          for (const key in meal?.bindHazardFactors) {
            if (!(key in harmList)) {
              harmList[key] = meal?.bindHazardFactors[key];
            }
          }
        }
      });
      return harmList;
    },
    // 删除组合
    delCombs(delMealName) {
      let tableCom_Ref = this.$refs.combsTable_Ref;
      let checkRow = tableCom_Ref.selection;

      if (checkRow.length === 0)
        return this.$message({
          message: '请先选择需要删除的组合！',
          type: 'warning',
          showClose: true
        });
      let checkMealCopy = dataUtils.deepCopy(this.checkMeal);
      let checkCombsCopy = dataUtils.deepCopy(this.checkCombs);
      checkRow.forEach((item) => {
        let idx = this.checkCombs.findIndex(
          (c) => c.combCode === item.combCode
        );
        if (idx != -1) {
          this.candidateCombCheckList.splice(idx, 1);
          this.checkCombs.splice(idx, 1);
          // delete this.checkComboObj[item.combCode.trim()];
        }

        if (checkCombsCopy.length === checkRow.length) return;
        checkMealCopy.map((cm, cIdx) => {
          let c_index = this.mealList.findIndex(
            (m) => m.clusCode == cm.clusCode
          );
          if (c_index !== -1) {
            if (
              this.mealList[c_index].bindCombs.findIndex(
                (b) => b.combCode == item.combCode
              ) !== -1
            ) {
              checkMealCopy.splice(cIdx, 1);
              this.candidateMealCheckList.splice(cIdx, 1);
            }
          }
          c_index = this.harmMealList.findIndex(
            (m) => m.clusCode == cm.clusCode
          );
          if (c_index !== -1) {
            if (
              this.harmMealList[c_index].bindCombs.findIndex(
                (b) => b.combCode == item.combCode
              ) !== -1
            ) {
              checkMealCopy.splice(cIdx, 1);
              this.candidateMealCheckList.splice(cIdx, 1);
            }
          }
        });
      });
      // let notCheckedCombs = checkCombsCopy.filter(item => !item.isChecked);

      let mealAtones = [
        ...new Set([
          ...(this.mealList
            ?.filter((item) =>
              this.checkMeal?.some((c) => c.clusCode === item.clusCode)
            )
            .flatMap((item) => item.bindCombs?.flat() || []) || []),
          ...(this.harmMealList
            ?.filter((item) =>
              this.checkMeal?.some((c) => c.clusCode === item.clusCode)
            )
            .flatMap((item) => item.bindCombs?.flat() || []) || [])
        ])
      ];

      let uncheckedCombs = checkCombsCopy.filter(
        (item) => !checkRow.some((i) => i.combCode == item.combCode)
      );
      const selected = mealAtones.some((i) =>
        checkRow.some((c) => c.combCode == i.combCode)
      );
      const notSelected = mealAtones.some((i) =>
        uncheckedCombs.some((c) => c.combCode == i.combCode)
      );
      // return;
      switch (delMealName) {
        case 0:
          if (selected) {
            if (notSelected) {
              this.$confirm('是否保留套餐名？', '提示', {
                confirmButtonText: '保留',
                cancelButtonText: '不保留',
                type: 'warning',
                closeOnClickModal: false,
                closeOnPressEscape: false,
                showClose: false
              })
                .then(() => {
                  if (!this.C_isOccupations) {
                    this.mealTabsTabClick();
                  }
                  this.getMaterial();
                })
                .catch(() => {
                  this.checkMeal = checkMealCopy;
                  this.checkMealList = this.checkMealList.filter((item) =>
                    this.checkMeal.some((c) => c.clusCode == item.clusCode)
                  );
                  if (!this.C_isOccupations) {
                    this.mealTabsTabClick();
                  }
                  this.getMaterial();
                });
            } else {
              this.checkMeal = [];
              this.checkMealList = [];
              if (!this.C_isOccupations) {
                this.mealTabsTabClick();
              }
            }
          } else {
            if (!this.C_isOccupations) {
              this.mealTabsTabClick();
            }
            this.getMaterial();
          }
          break;
        case 1:
          this.checkMeal = checkMealCopy;
          this.checkMealList = this.checkMealList.filter((item) =>
            this.checkMeal.some((c) => c.clusCode == item.clusCode)
          );
          if (!this.C_isOccupations) {
            this.mealTabsTabClick();
          }
          this.getMaterial();
          break;
        case 2:
          if (!notSelected) {
            this.checkMeal = [];
            this.checkMealList = [];
          }
          if (!this.C_isOccupations) {
            this.mealTabsTabClick();
          }
          this.getMaterial();
      }
    },
    // 新建按钮的点击回调
    createBtn() {
      return new Promise((resolve, reject) => {
        this.clearMealAndCombs();
        this.checkHarmList = [];
        this.harmVal = [];
        this.userPeStatus = 0;
        this.questionnaireStatus = true;
        this.mealList = dataUtils.deepCopy(this.commonMealList);
        this.mealListObj = dataUtils.deepCopy(this.commonMealListObj);
        this.$refs.form_Ref.resetForm();
        this.$refs.form_Ref.peStatus = false;
        this.mealSearch();
        resolve();
      });
    },
    // 清空已选套餐、组合
    clearMealAndCombs() {
      this.checkMeal = [];
      this.checkCombs = [];
      this.testTubes = [];
      this.checkMealList = [];
      this.checkHarmList = [];
      this.combId = 0;
      //去掉候选组合中的勾选
      this.searchCombList.forEach((item) => {
        item.isSelected = false;
      });
    },
    // 新建按钮点击回调后根据配置文件是否开启摄像头
    openOrClose() {
      if (!this.G_config.isOpenCamera) {
        this.$refs.form_Ref.closeCamer('video');
        this.$refs.form_Ref.videoShow = false;
      } else {
        this.$refs.form_Ref.videoShow = true;
        this.$refs.form_Ref.openCamer('video');
      }
    },
    // 出来价格保留两位小数
    handlePrice: dataUtils.handlePrice,
    // 折扣改变的回调
    discountChange(row) {
      let price = dataUtils.multiply(row.originalPrice, row.discount);
      row.price = price;
    },
    // 危害因素的下拉框聚焦回调
    harmSelectVisible() {
      this.oldHazardousCodes = this.harmVal.map((item) => item.hazardousCode);
      //console.log(this.oldHazardousCodes);
    },
    //查询
    search(url) {
      if (!this.regNoSearchVal.trim()) {
        this.$message({
          showClose: true,
          message: '请输入搜索内容',
          type: 'warning'
        });
        return;
      }
      this.$ajax
        .post(this.$apiUrls.GetRegisterByQueryTypeNew + url, '', {
          query: {
            queryValue: this.regNoSearchVal
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          if (returnData.length == 1) {
            this.$nextTick(() => {
              this.userListRowDblclick(returnData[0]);
              this.$message({
                showClose: true,
                message: '查询订单数据成功',
                type: 'success'
              });
            });
          } else if (returnData.length > 1) {
            this.regNoSearchDialogShow = true;
            this.$nextTick(() => {
              this.$refs.regNoSearchTable_Ref.staticLoad(returnData);
            });
          } else {
            this.regNoSearchDialogShow = false;
            this.$message({
              showClose: true,
              message: '暂无数据!',
              type: 'success'
            });
          }
        })
        .finally(() => {
          this.regNoSearchVal = '';
        });
    },
    // 体检号搜索的表格双击回调
    regNoSearchRowDblclick(row) {
      //console.log(row);
      this.userListRowDblclick(row);
      this.regNoSearchDialogShow = false;
    },
    // 危害因素添加按钮的点击回调
    addHarmBtn() {
      if (!this.peCls.includes('职业检')) {
        this.$message({
          message: '体检分类需要选择职业检！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.$refs.form_Ref.userInfo.jobStatus == null) {
        this.$message({
          message: '请先选择需要在岗状态！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.harmDialogShow = true;
    },
    // 危害因素分类值改变的回调
    harmTypeChange() {
      let checkHarmCode = this.dialogChcekHarm.map(
        (item) => item.hazardousCode
      );
      let harmList_fixed = dataUtils.deepCopy(this.harmList_fixed);
      let harmList = harmList_fixed.filter((item) => {
        if (checkHarmCode.includes(item.hazardousCode)) {
          item.isSelected = true;
        }
        return (
          (!this.harmTypeVal ? true : this.harmTypeVal == item.hazardousType) &&
          (!this.harmKeyword
            ? true
            : item.hazardousName.includes(this.harmKeyword) ||
              item?.hazardousAlias?.includes(this.harmKeyword))
        );
      });
      this.harmList = harmList;
      this.$nextTick(() => {
        this.$refs.vxeList_Ref?.refreshScroll();
      });
    },
    //危害因素带出组合
    async hazardOutCombination() {
      let hazardOutCombinationList = [];
      this.harmList_fixed.forEach((item) => {
        let index = this.checkHarmList.find(
          (i) => i.hazardousCode === item.hazardousCode
        );
        if (index && Object.keys(item.combsByStatus).length !== 0)
          hazardOutCombinationList.push(item.combsByStatus);
      });

      hazardOutCombinationList = new Set(
        hazardOutCombinationList
          .map((obj) => obj[this.$refs?.form_Ref?.userInfo?.jobStatus] || [])
          .flat()
      );
      // this.checkMeal = [];
      // this.checkCombs = this.checkCombs.filter(item => item.isSelected && item.payStatus !== 0);
      let checkCombsCode = this.checkCombs.map((item) => item.combCode);
      console.log(checkCombsCode);
      this.combList.forEach((c) => {
        if (hazardOutCombinationList.has(c.combCode)) {
          let idx = checkCombsCode.findIndex((i) => i === c.combCode);
          if (idx === -1) {
            let newC = dataUtils.deepCopy(c);
            newC.isOccupation = true;
            newC.isOrdinary = false;
            this.checkCombs.unshift(newC);
          } else {
            this.checkCombs[idx].isOccupation = true;
          }
        }
      });
      //设置折扣,折后价,支付状态默认值
      this.checkCombs.forEach((item) => {
        item.discount ??= 1;
        item.originalPrice ||= item.price;
        item.payStatus = 0;
      });
      await this.disposeTestTubes(true);
    },
    //清空职业套餐
    clearOccupationPackages() {
      const o = dataUtils.deepCopy(this.checkMeal);
      const n = o?.filter((item) => !item.isOccupation);
      const c = o?.filter((item) => item.isOccupation);
      const c_combCode = this.getUniqueCombsByCode(c, 'combCode');

      // 判断是否会影响已缴费或者已检查的项目
      const hasImpact = this.checkCombs.some((item) => {
        if (c_combCode.some((c) => c.combCode == item.combCode)) {
          if (
            (item.payStatus == 1 || item.payStatus == 2 || item.isChecked) &&
            !item.isOrdinary
          ) {
            return true;
          }
        }
        return false;
      });
      if (hasImpact) {
        return false;
      }
      // 处理套餐
      this.checkMeal = dataUtils.deepCopy(n);
      // 处理组合
      console.log(this.checkCombs);
      this.checkCombs?.forEach((item) => {
        if (c_combCode.some((c) => c.combCode == item.combCode)) {
          item.isOccupation = false;
        }
        return item;
      });
      this.checkCombs = this.checkCombs?.filter(
        (item) => !(!item.isOccupation && !item.isOrdinary)
      );
      // 处理危害因素
      this.editHarm(n, o);
      return true;
    },
    //返回套餐中包含的组合数据
    getUniqueCombsByCode(list, codeKey) {
      const mealList = [
        ...dataUtils.deepCopy(this.mealList),
        ...dataUtils.deepCopy(this.harmMealList)
      ];
      const filteredList =
        mealList
          ?.filter((item) => list?.some((c) => c.clusCode === item.clusCode))
          .flatMap((item) => item.bindCombs?.flat() || []) || [];
      return Array.from(
        new Map(filteredList.map((item) => [item[codeKey], item])).values()
      );
    },
    // 套餐搜索
    mealSearch() {
      let form_Ref = this.$refs.form_Ref;
      let searchMealList = this.mealList.filter((item) => {
        let val = this.searchMealVal.toLowerCase();
        return (
          (!form_Ref?.userInfo.sex ||
            item.sex === form_Ref?.userInfo.sex ||
            item.sex === 0) &&
          (item?.clusName.toLowerCase().includes(val) ||
            item?.clusCode.includes(this.searchMealVal) ||
            item?.pinYinCode?.toLowerCase().includes(val))
        );
      });
      if (this.C_isOccupation) {
        // console.log(JSON.parse(JSON.stringify(searchMealList)));
        searchMealList = searchMealList.filter((item) => {
          if (item.isGroup) {
            if (
              item.jobStatus == form_Ref.userInfo.jobStatus ||
              !item.bindHazardFactors
            ) {
              return true;
            } else {
              return false;
            }
          }
          return true;
        });
      }
      this.searchMealList = dataUtils.deepCopy(searchMealList);
      //json格式转换
      // console.log(JSON.parse(JSON.stringify(this.searchMealList)));
      // (this.$refs.mealTable_Ref || this.$refs.dialogMealTable_Ref).staticLoad(this.searchMealList);
      this.mealSelectStatu();
    },
    // 组合搜索
    comboSearch() {
      let form_Ref = this.$refs.form_Ref;
      let searchCombList = this.combList.filter((item) => {
        let val = this.comboSearchVal.toLowerCase();
        return (
          (!form_Ref?.userInfo.sex ||
            item.sex === form_Ref?.userInfo.sex ||
            item.sex === 0) &&
          (this.combTypeVal ? item.clsCode == this.combTypeVal : true) &&
          (item.combName.toLowerCase().includes(val) ||
            item.combCode.includes(this.comboSearchVal) ||
            item?.pinYinCode?.toLowerCase().includes(val))
        );
      });
      this.searchCombList = dataUtils.deepCopy(searchCombList);

      // (this.$refs.combTable_Ref || this.$refs.dialogCombTable_Ref).staticLoad(this.searchCombList);
      this.combsSelectStatu();
    },
    // 危害因素套餐搜索
    harmMealSearch() {
      let form_Ref = this.$refs.form_Ref;
      let searchHarmMealList = this.harmMealList.filter((item) => {
        return (
          (!form_Ref.userInfo.sex
            ? true
            : item.sex == form_Ref.userInfo.sex || item.sex == 0) &&
          item?.clusName.includes(this.searchHarmMealVal)
        );
      });

      searchHarmMealList = searchHarmMealList.filter((item) => {
        if (
          item.jobStatus == form_Ref.userInfo.jobStatus ||
          form_Ref.userInfo.jobStatus == null ||
          form_Ref.userInfo.jobStatus == ''
        ) {
          return true;
        } else {
          return false;
        }
      });
      this.searchHarmMealList = dataUtils.deepCopy(searchHarmMealList);

      if (!this.$refs.harmMealTabel_Ref) return;
      this.$refs.harmMealTabel_Ref.staticLoad(this.searchHarmMealList);
      this.harmMealStatu();
    },
    //套用触发
    async applyDate(data, onlyCombination) {
      if (
        this.checkCombs.some(
          (item) =>
            (item.payStatus !== 0 && item.payStatus !== 3) || item.isChecked
        )
      ) {
        return this.$message({
          message: '已选组合中存在已缴费或已检查的项目!',
          type: 'warning',
          showClose: true
        });
      }
      // 组合处理
      if (data?.combs && Object.keys(data?.combs).length > 0) {
        const combKeys = Object.keys(data.combs);
        let applyCombinations = this.combList.filter((comb) =>
          combKeys.includes(comb.combCode)
        );
        if (
          applyCombinations.some(
            (item) =>
              item.sex !== this.$refs.form_Ref.userInfo.sex && item.sex !== 0
          )
        ) {
          return this.$message({
            message: '套用组合中存在与当前性别不符的项目!',
            type: 'warning',
            showClose: true
          });
        }
        this.candidateCombCheckList = [...applyCombinations];
        applyCombinations.forEach((comb) => {
          comb.discount ??= 1;
          comb.originalPrice ||= comb.price;
          comb.payStatus = 0;
          comb.isOccupation = this.childUserInfo.isOccupation;
          comb.isOrdinary = !this.childUserInfo.isOccupation;
        });
        this.checkCombs = dataUtils.deepCopy(applyCombinations);
        await this.disposeTestTubes(true);
      } else {
        this.checkCombs = [];
        this.candidateCombCheckList = [];
      }

      // 套餐处理
      if (
        data?.cluster &&
        Object.keys(data?.cluster).length > 0 &&
        onlyCombination
      ) {
        const mealKeys = Object.keys(data.cluster);
        this.checkMealList = this.mealList.filter((meal) =>
          mealKeys.includes(meal.clusCode)
        );
        this.checkMeal = [...this.checkMealList];
        this.candidateMealCheckList = [...this.checkMealList];
      } else {
        this.checkMealList = [];
        this.checkMeal = [];
        this.candidateMealCheckList = [];
      }

      //危害因素处理
      if (data?.hazards && Object.keys(data.hazards).length > 0) {
        let date = Moment()
          .subtract(this.$refs.form_Ref.userInfo.totalMonthsOfWork, 'month')
          .format('YYYY-MM-DD');
        date = Moment(date)
          .subtract(this.$refs.form_Ref.userInfo.totalYearsOfWork, 'years')
          .format('YYYY-MM-DD');
        let harmList = [];
        Object.entries(data.hazards).forEach(([key, value]) => {
          harmList.push({
            hazardousCode: key,
            hazardousName: value,
            startDateOfHazards: date,
            yearsOfHazards: this.$refs.form_Ref.userInfo.totalYearsOfWork,
            monthsOfHazards: this.$refs.form_Ref.userInfo.totalMonthsOfWork
          });
        });
        this.checkHarmList = dataUtils.deepCopy(harmList);
      } else {
        this.checkHarmList = [];
      }
      //在岗状态 职业代码 职业名称
      if (data?.jobStatus) {
        this.$refs.form_Ref.userInfo.jobStatus = data.jobStatus;
      }
      if (data?.jobName) {
        this.$refs.form_Ref.userInfo.jobName = data.jobName;
      }
      if (data?.jobType) {
        this.$refs.form_Ref.userInfo.jobType = data.jobType;
      }
      this.combsSelectStatu();
      this.mealSelectStatu();
    },
    submitSuccess() {
      this.isShowdialogTable = false;
      this.questionnaireDisabled = false;
    },
    //问卷调查
    questionnaire() {
      let userInfo = this.$refs.form_Ref.userInfo;
      if (!userInfo.regNo) {
        this.$message({
          message: '请先选择需要问卷调查的体检信息！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      // this.GetQuestionDataByRegNo(userInfo);
      this.isShowdialogTable = true;
    },
    //通过体检号获取问卷数据
    GetQuestionDataByRegNo(userInfo) {
      this.formInfo = {
        regNo: userInfo.regNo, //体检号
        name: userInfo.name, //姓名
        sex: this.G_EnumList['Sex'][userInfo.sex], //性别（0通用1男2女）
        age: userInfo.age == '0' ? userInfo.age + '月' : userInfo.age + '岁', //年龄
        tel: userInfo.tel //手机号
      };
      this.$ajax
        .post(this.$apiUrls.GetQuestionDataByRegNo, '', {
          query: {
            regNo: userInfo.regNo
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          if (!returnData) {
            this.questionnaireDisabled = true;
            return;
          }
          this.questionnaireDisabled = false;
          let temp = Object.assign(this.formInfo, returnData);
          this.formInfo = { ...temp };
        });
    },
    //获取体检次数
    getSimpleCompanyTimes(empty) {
      this.comTimesList = [];
      this.$ajax
        .post(this.$apiUrls.GetSimpleCompanyTimes, '', {
          query: { companyCode: this.$refs.form_Ref.userInfo.companyCode }
        })
        .then((r) => {
          console.log('rrrr', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.$refs.form_Ref.userInfo.companyTimes =
            returnData[returnData?.length - 1].companyTimes;
          this.getCompanyMeal().then((r) => {
            if (empty) this.clearMealAndCombs();
          });
        });
    },
    // 获取单位套餐
    getCompanyMeal() {
      let form_Ref = this.$refs.form_Ref;
      return new Promise((resolve, reject) => {
        if (!!form_Ref.userInfo.companyTimes) {
          this.$ajax
            .post(this.$apiUrls.ReadCompanyCandidateCluster, '', {
              query: {
                companyCode: form_Ref.userInfo.companyCode,
                companyTimes: Number(form_Ref.userInfo.companyTimes),
                type: this.C_type
              }
            })
            .then(async (r) => {
              let { success, returnData } = r.data;
              if (!success) return;
              if (!this.G_config.physicalMode.includes('普检')) {
                this.harmMealList = this.harmMealList.filter(
                  (item) => !item.isGroup
                );
                returnData?.forEach((item) => {
                  item.isGroup = true;
                  this.harmMealListObj[item.clusCode] = item;
                });
                this.harmMealList.unshift(...returnData);
              } else {
                this.mealList = dataUtils.deepCopy(this.commonMealList);
                this.mealListObj = dataUtils.deepCopy(this.commonMealListObj);
                returnData?.map((item) => {
                  item.isGroup = true;
                  this.mealListObj[item.clusCode] = item;
                });
                this.mealList.unshift(...returnData);
              }
              // this.searchMealList = dataUtils.deepCopy(this.mealList);
              this.mealSearch();
              resolve();
            });
        }
      });
    },
    // 获取组合明细
    combExpandChange(row) {
      console.log(row);
      this.$ajax
        .paramsPost(this.$apiUrls.ReadCandidateCombItems, {
          combCode: row.combCode
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) {
            return;
          }
          row.detailList = returnData;
        });
    },
    // 键盘按下事件
    keyDown(e) {
      // console.log(e.keyCode);
      // if (e.keyCode == 9) {
      //     e.preventDefault();
      // }
    },
    // 表格的键盘事件
    keyUp(e) {
      e.preventDefault();
      e.stopPropagation();
      if (this.$refs.form_Ref?.isFocused) {
        this.$refs.form_Ref.keyboardFun(e);
        return;
      } else if (this.mealActiveName == 'meal') {
        this.keyEventFun(
          e,
          'searchMealList',
          this.C_isOccupations ? 'dialogMealTable_Ref' : 'mealTable_Ref',
          'mealTableIdx',
          'mealDBClick'
        );
      } else if (this.mealActiveName == 'comb') {
        //console.log("comb");
        this.keyEventFun(
          e,
          'searchCombList',
          this.C_isOccupations ? 'dialogCombTable_Ref' : 'combTable_Ref',
          'combTableIdx',
          'combDbClick'
        );
      } else {
        this.keyEventFun(
          e,
          'searchHarmMealList',
          'harmMealTabel_Ref',
          'harmMealTableIdx',
          'mealDBClick'
        );
      }
      return false;
    },
    // 表格的键盘事件封装
    keyEventFun(e, searchList, tableRef, rowIdx, DBClickFun) {
      if (
        this[searchList].length == 0 ||
        !this.$refs[tableRef] ||
        !this.$refs[tableRef].$refs
      )
        return;
      let mealTable_ref = this.$refs[tableRef].$refs.tableCom_Ref;
      let bodyWrapper = mealTable_ref.bodyWrapper;

      // console.log(mealTable_ref);
      switch (e.keyCode) {
        case 40:
          // 下键
          console.log(this[rowIdx]);
          if (this[rowIdx] === '') {
            this[rowIdx] = 0;
            mealTable_ref.setCurrentRow(this[searchList][this[rowIdx]]);
            return;
          }
          this[rowIdx] += 1;
          if (this[rowIdx] >= this[searchList].length) {
            this[rowIdx] = this[rowIdx] - 1;
            return;
          }
          console.log(this[rowIdx], this[searchList][this[rowIdx]]);
          mealTable_ref.setCurrentRow(this[searchList][this[rowIdx]]);
          break;

        case 38:
          // 上键
          if (this[rowIdx] === '') {
            this[rowIdx] = 0;
            mealTable_ref.setCurrentRow(this[searchList][this[rowIdx]]);
            return;
          }
          this[rowIdx] -= 1;
          if (this[rowIdx] < 0) {
            this[rowIdx] = 0;
            return;
          }
          mealTable_ref.setCurrentRow(this[searchList][this[rowIdx]]);
          break;

        case 13:
          if (this[rowIdx] === '') return;
          this[DBClickFun](this[searchList][this[rowIdx]]);
          break;
      }
      this.$nextTick(() => {
        if (e.keyCode == 38 || e.keyCode == 40) {
          let currentRow = bodyWrapper.getElementsByClassName('current-row');
          bodyWrapper.scrollTop = currentRow[0].offsetTop;
        }
      });
    },
    // 套餐当前行改变的回调
    mealCurrentChange(val) {
      this.mealTableIdx = this.searchMealList.indexOf(val);
    },
    // 组合当前行改变的回调
    combCurrentChange(val) {
      this.combTableIdx = this.searchCombList.indexOf(val);
    },
    // 危害因素套餐当前行改变的回调
    harmbCurrentChange(val) {
      this.harmMealTableIdx = this.searchHarmMealList.indexOf(val);
    },
    // 已选组合是否可选
    combSelectable(row, index) {
      if ((row.payStatus == 0 || row.payStatus == 3) && !row.isChecked) {
        return true;
      } else {
        return false;
      }
    },
    //主套餐
    mainPackage(row) {
      console.log('🚀 ~ mainPackage ~ row:', row);
      let form = this.$refs.form_Ref.userInfo;
      form.reportType = row.reportType;
      form.guidanceType = row.guidanceType;
      if (!this.G_config.physicalMode.includes('职检')) {
        form.peCls = row.peCls;
      } else {
        let temp = [...new Set([...form.peCls, row.peCls])];
        const numberCount = temp.filter(
          (item) => typeof item === 'number'
        ).length;

        if (numberCount > 1) {
          const index = temp.findIndex((item) => typeof item === 'number');
          if (index !== -1) {
            temp.splice(index, 1);
          }
        }
        form.peCls = temp;
      }
    },
    // 更新信息
    updateInfo() {
      this.$refs.form_Ref.updateReg();
    },
    // 连接本地的身份证读卡器
    connectInit() {
      this.connectIdcard((r) => {
        // console.log(r.data);
        try {
          let datas = JSON.parse(r.data);
          if (datas.Data) {
            let userInfoCopy = dataUtils.deepCopy(this.$refs.form_Ref.userInfo);
            this.createBtn().then((r) => {
              setTimeout(() => {
                this.idcardInfo = datas.Data;
                this.$refs.form_Ref.userInfo = Object.assign(
                  this.$refs.form_Ref.userInfo,
                  {
                    name: this.idcardInfo.Name,
                    sex: this.idcardInfo.Sex == '男' ? 1 : 2,
                    photoUrl:
                      'data:image/jpeg;base64,' + this.idcardInfo.PhotoBase64,
                    cardNo: this.idcardInfo.IDCardNo,
                    age: dataUtils.getByIdCard(this.idcardInfo.IDCardNo).age,
                    birthday: Moment(
                      this.idcardInfo.Born,
                      'YYYY年MM月DD日'
                    ).format('YYYY-MM-DD'),
                    address: this.idcardInfo.Address,
                    peCls: userInfoCopy.peCls
                  }
                );
                this.$refs.form_Ref.searchCard();
                userInfoCopy = null;
              }, 100);
              this.isSwipingCard = true;
            });
          }
        } catch (error) {}
      });
    },
    handleResize() {
      if (window.innerWidth > 1500 && this.isWideScreen <= 1500) {
        this.$refs?.userTable_Ref.loadData();
      } else if (window.innerWidth < 1500 && this.isWideScreen > 1500) {
        this.$refs?.userTable_Refs.loadData();
      }
      this.isWideScreen = window.innerWidth;
    },
    //体检类型全选-全不选
    checkAll(ordinary, isChecked) {
      const condition = ordinary ? this.C_ordinary : this.C_occupation;
      const typeToSet = ordinary ? 'isOrdinary' : 'isOccupation';
      const oppositeType = ordinary ? 'isOccupation' : 'isOrdinary';

      if (!condition) return;

      this.checkCombs.forEach((item) => {
        if (!item.isChecked) {
          if (isChecked) {
            item[typeToSet] = true;
          } else if (item[oppositeType]) {
            item[typeToSet] = false;
          }
        }
      });
    },
    //批打折
    discountConfirm() {
      console.log(this.discount);
      if (!(this.discount >= 0)) {
        this.showDiscount();
        return this.$message.error('请输入正确的折扣！');
      }
      this.checkCombs.forEach((item) => {
        if (!item.isChecked && (item.payStatus == 0 || item.payStatus == 3)) {
          let price = dataUtils.multiply(item.originalPrice, this.discount);
          item.discount = this.discount;
          item.price = price;
        }
      });
    },
    //批打折隐藏回调
    closePopover() {
      this.discount = 1;
      this.fixedPrice = 0;
    },
    //批打折显示回调
    showDiscount() {
      this.$nextTick(() => {
        this.$refs.ref_input.focus();
      });
    },
    //设置退费列表类名
    tableRowClassName({ row, rowIndex }) {
      if (this.containComboCode.includes(row.combCode.trim())) {
        return 'have_meal';
      } else {
        return 'no_meal';
      }
    },
    // 撤销退款
    cancelRefundsBtn() {
      if (!this.$refs.form_Ref.userInfo.regNo) {
        this.$message({
          message: '请先选择需要撤销退款申请的用户',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm('是否确定撤销退款申请？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax
            .paramsPost(this.$apiUrls.CancelHisRefund, {
              regNo: this.$refs.form_Ref.userInfo.regNo
            })
            .then((r) => {
              let { success, returnData, returnMsg } = r.data;
              if (!success) return;
              this.$message({
                message: returnMsg,
                type: 'success'
              });
              this.userListRowDblclick(this.$refs.form_Ref.userInfo);
            });
        })
        .catch(() => {});
    },
    // 退费
    refundsBtn() {
      if (!this.$refs.form_Ref.userInfo.regNo) {
        this.$message({
          message: '请先选择需要退费的用户',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let refundsList = this.checkCombs.filter((item) => {
        return item.payStatus == 1;
      });
      this.refundsList = refundsList;
      if (refundsList.length == 0) {
        this.$message({
          message: '该用户暂未收费',
          type: 'warning'
        });
        return;
      }
      this.refundRegNo = this.$refs.form_Ref.userInfo.regNo;
      this.refundsDigShow = true;
    },
    //退费成功回调
    refundSuccess() {
      this.userListRowDblclick(this.$refs.form_Ref.userInfo);
    },
    // 接害日期改变同步总工龄
    changeLengthOfService() {
      this.harmList_fixed.map((item) => {
        if (item.isOtherHazardous) item.hazardousAlias = item.hazardousName;
      });
      let earliestDate = new Date(
        Math.min(
          ...this.checkHarmList.map((item) =>
            new Date(item.startDateOfHazards).getTime()
          )
        )
      );
      if (!earliestDate) return;

      let today = new Date();
      let years = today.getFullYear() - earliestDate.getFullYear();
      let months = today.getMonth() - earliestDate.getMonth();

      if (months < 0) {
        years--;
        months += 12;
      }

      if (years) this.$refs.form_Ref.userInfo.totalYearsOfWork = years;

      if (months) this.$refs.form_Ref.userInfo.totalMonthsOfWork = months;
    },
    // 确定修改全部已选的开始接害日期
    harmDateConfirm() {
      this.checkHarmList.map((item) => {
        item.startDateOfHazards = this.harmDateVal;
        this.harmDateChange(item);
      });
    },
    // 批量修改已选的开始接害日期
    harmYearsAndMonthsConfirm() {
      this.checkHarmList.map((item) => {
        item.yearsOfHazards = this.batchYearsOfHazards;
        item.monthsOfHazards = this.batchMonthsOfHazards;
      });
    },
    // 一口价的确定回调
    fixedPriceFun() {
      if (
        this.fixedPrice <
        this.subNum(
          this.C_totalMoney.totalOriginalPrice,
          this.G_userInfo.codeOper.maxDeductionPrice
        )
      ) {
        this.$message({
          message: '请输入范围内的一口价！',
          type: 'warning'
        });
        return;
      }
      let maxIdx;
      let maxItem = null;
      this.checkCombs.forEach((item, idx) => {
        if (!maxItem) {
          maxItem = item;
          maxIdx = idx;
          return;
        }
        if (maxItem.originalPrice < item.originalPrice) {
          maxItem = item;
          maxIdx = idx;
        }
      });
      maxItem = null;
      let discount = dataUtils.divide(
        this.fixedPrice,
        Number(this.totalOriginalPrice)
      );
      let materialsPrice = 0;
      let a = this.checkCombs.reduce((accumulator, currentValue, idx, arr) => {
        console.log(currentValue);

        if (currentValue.testTubes) {
          materialsPrice = dataUtils.add(
            materialsPrice,
            Number(currentValue.testTubes.testTubesPrice)
          );
        }
        if (idx == maxIdx) return Number(accumulator);
        currentValue.discount = discount;
        currentValue.price = dataUtils.multiply(
          currentValue.originalPrice,
          discount
        );

        return dataUtils.add(accumulator, Number(currentValue.price));
      }, 0);
      let num = this.fixedPrice - a - materialsPrice;

      let lastItem = this.checkCombs[maxIdx];
      num = num / lastItem.originalPrice;

      let price = dataUtils.multiply(lastItem.originalPrice, num);
      lastItem.discount = num;
      lastItem.price = price;
    },
    // 减法保留两位小数
    subNum(num1, num2) {
      return Number(dataUtils.subtract(num1, num2));
    }
  },
  watch: {
    mealActiveName: {
      handler(n, o) {
        this.mealTableIdx = '';
        this.combTableIdx = '';
        this.harmMealTableIdx = '';
        if (n == 'meal') {
          this.$nextTick(() => {
            this.$refs.mealInput_Ref?.$el.querySelector('input').focus();
          });
        } else if (n == 'comb') {
          this.$nextTick(() => {
            this.$refs.combInput_Ref?.$el.querySelector('input').focus();
          });
        } else {
          this.harmMealSearch();
          this.$nextTick(() => {
            this.$refs.harmMealInput_Ref?.$el.querySelector('input').focus();
          });
        }
      },
      immediate: true
    }
  },
  created() {
    //获取材料费
    this.getMaterial = _debounce(this.getMaterial, 1000);
    // 套餐搜索
    this.mealSearch = _debounce(this.mealSearch, 300);
    // 组合搜索
    this.comboSearch = _debounce(this.comboSearch, 300);
    // 危害因素的搜索
    this.harmMealSearch = _debounce(this.harmMealSearch, 300);
  },
  mounted() {
    this.getHarmList();
    this.getCombTypeList();
    this.getMealList();
    this.getCombList();
    this.getHarmMealList();
    addEventListener('keyup', this.keyUp);
    addEventListener('keydown', this.keyDown);
    //去掉控制台警告 单选框
    this.$refs.radio.$children.forEach((item) => {
      item.$refs.radio.removeAttribute('aria-hidden');
    });
    this.$refs.radios.$children.forEach((item) => {
      item.$refs.radio.removeAttribute('aria-hidden');
    });
    addEventListener('resize', this.handleResize);
    if (!this.G_config.physicalMode.includes('普检'))
      this.mealActiveName = 'harm';
  },
  activated() {
    if (this.recordShow) {
      this.$nextTick(() => {
        removeEventListener('keyup', this.keyUpFun);
        removeEventListener('keydown', this.keyDownFun);
      });
    }
    addEventListener('keyup', this.keyUp);
    addEventListener('keydown', this.keyDown);
    addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    removeEventListener('keyup', this.keyUp);
    removeEventListener('keydown', this.keyDown);
    removeEventListener('resize', this.handleResize);
  },
  deactivated() {
    removeEventListener('keyup', this.keyUp);
    removeEventListener('keydown', this.keyDown);
    removeEventListener('resize', this.handleResize);
  }
};
