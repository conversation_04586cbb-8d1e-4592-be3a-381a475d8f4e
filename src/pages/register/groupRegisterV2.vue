<template>
  <div class="group-register-page" @mouseover="showUserListFlag = false">
    <!-- 左侧人员列表区域 -->
    <div class="user-list-section" @mouseover.stop v-show="!recordShow">
      <el-card class="filter-card" shadow="never">
        <div slot="header" class="card-header">
          <span class="card-title">人员筛选</span>
        </div>

        <!-- 筛选表单区域 -->
        <div class="filter-form">
          <!-- 单位和部门筛选 -->
          <div class="form-row">
            <div class="form-item">
              <label class="form-label">单位</label>
              <el-cascader
                v-model="userQueue.companyCode"
                :options="userQueue.companyList"
                :props="{ multiple: false }"
                clearable
                filterable
                size="small"
                collapse-tags
                @change="companyChange"
                :filter-method="filterMethod"
                placeholder="请选择单位"
              />
            </div>
            <div class="form-item">
              <label class="form-label">部门</label>
              <el-select
                v-model.trim="userQueue.companyDeptCode"
                placeholder="请选择部门"
                size="small"
                filterable
                clearable
                :disabled="userQueue.isHavue"
                @change="getUserList"
              >
                <el-option
                  v-for="(item, index) in userQueue.companyDeptList"
                  :key="index"
                  :label="item.deptName"
                  :value="item.deptCode"
                />
              </el-select>
            </div>
          </div>

          <!-- 套餐和检查状态筛选 -->
          <div class="form-row">
            <div class="form-item">
              <label class="form-label">套餐</label>
              <el-select
                v-model.trim="userQueue.clusCode"
                placeholder="请选择套餐"
                size="small"
                filterable
                clearable
                @change="getUserList"
              >
                <el-option
                  v-for="(item, index) in userQueue.clusterList"
                  :key="index"
                  :value="item.clusCode"
                  :label="item.clusName"
                />
              </el-select>
            </div>
            <div class="form-item">
              <label class="form-label">检查状态</label>
              <el-select
                v-model="userQueue.peStatus"
                placeholder="请选择状态"
                size="small"
                clearable
                @change="getUserList"
              >
                <el-option
                  v-for="item in G_peStatus"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>

          <!-- 时间筛选 -->
          <div class="form-row">
            <div class="form-item time-filter">
              <label class="form-label">时间类型</label>
              <el-select
                v-model.trim="params.queryType"
                size="small"
                @change="getUserList"
                style="width: 100px"
              >
                <el-option
                  v-for="item in timeType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
            <div class="form-item date-range">
              <el-date-picker
                v-model="dateVal"
                type="daterange"
                size="small"
                :picker-options="{ shortcuts: G_datePickerShortcuts }"
                :clearable="false"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                @change="getUserList"
              />
            </div>
          </div>

          <!-- 激活状态和查询按钮 -->
          <div class="form-row">
            <div class="form-item">
              <label class="form-label">激活状态</label>
              <el-radio-group
                v-model.trim="userQueue.isActive"
                @change="getUserList"
                size="small"
              >
                <el-radio :label="null">全部</el-radio>
                <el-radio :label="true">已激活</el-radio>
                <el-radio :label="false">未激活</el-radio>
              </el-radio-group>
            </div>
            <div class="form-item">
              <el-button
                type="primary"
                size="small"
                icon="el-icon-search"
                @click="getUserList"
              >
                查询
              </el-button>
            </div>
          </div>

          <!-- 记录统计 -->
          <div class="form-row">
            <div class="record-count">
              <span class="count-label">记录数：</span>
              <span class="count-value">{{ userQueue.total }}</span>
            </div>
          </div>
        </div>

        <!-- 人员列表表格 -->
        <div class="user-table-container">
          <PublicTableV2
            ref="userTable_Ref"
            :params="params"
            :theads="userListTheads"
            :tableDataMap="(data) => data.record"
            :elStyle="{
              'show-selection': false,
              'header-cell-style': {
                height: '32px',
                background: '#409eff',
                fontSize: '14px',
                color: '#ffffff',
                fontWeight: '500'
              }
            }"
            rowKey="regNo"
            remoteByPage
            :url="$apiUrls.GetRegistersByMultipleFilterNew"
            @rowDblclick="userListRowDblclick"
            @request-success="successfulCallback"
          >
            <template #name="{ scope }">
              <div class="name-cell">
                <i
                  v-if="scope.row.sex == 1"
                  class="el-icon-male gender-icon"
                ></i>
                <i
                  v-if="scope.row.sex == 2"
                  class="el-icon-female gender-icon"
                ></i>
                <span class="name-text">{{ scope.row.name }}</span>
              </div>
            </template>
            <template #peStatus="{ scope }">
              <el-tag
                :type="getStatusTagType(scope.row.peStatus)"
                size="small"
                effect="light"
              >
                {{ G_EnumList['PeStatus'][scope.row.peStatus] }}
              </el-tag>
            </template>
          </PublicTableV2>
        </div>
      </el-card>
    </div>
    <!-- 右侧主要内容区域 -->
    <div class="main-content-section" v-show="!recordShow" v-loading="loading">
      <!-- 顶部操作栏 -->
      <el-card class="operation-card" shadow="never">
        <div class="operation-header">
          <!-- 搜索区域 -->
          <div class="search-area">
            <el-popover
              placement="bottom"
              popper-class="user-list-popover"
              @after-enter="userListShow"
              width="400"
              trigger="click"
            >
            <div class="left_wrap" @mouseover.stop v-show="!this.recordShow">
              <div class="left_tab">
                <ul>
                  <li>
                    <div>
                      <span>单位</span>
                      <el-cascader
                        style="width: 100%; margin: 0"
                        ref="company_cascader_ref"
                        v-model="userQueue.companyCode"
                        :options="userQueue.companyList"
                        :props="{ multiple: false }"
                        clearable
                        filterable
                        size="mini"
                        collapse-tags
                        :filter-method="filterMethod"
                        @change="companyChange"
                      >
                      </el-cascader>
                    </div>
                    <div style="width: 50%">
                      <span>部门</span>
                      <el-select
                        class="select"
                        v-model.trim="userQueue.companyDeptCode"
                        placeholder="请选择"
                        size="mini"
                        filterable
                        clearable
                        :disabled="userQueue.isHavue"
                        @change="getUserList"
                      >
                        <el-option
                          v-for="(item, index) in userQueue.companyDeptList"
                          :key="index"
                          :label="item.deptName"
                          :value="item.deptCode"
                        ></el-option>
                      </el-select>
                    </div>
                  </li>
                  <li>
                    <div>
                      <span>套餐</span>
                      <el-select
                        class="select"
                        v-model.trim="userQueue.clusCode"
                        placeholder="请选择"
                        size="mini"
                        filterable
                        clearable
                        @change="getUserList"
                      >
                        <el-option
                          v-for="(item, index) in userQueue.clusterList"
                          :key="index"
                          :value="item.clusCode"
                          :label="item.clusName"
                        ></el-option>
                      </el-select>
                    </div>
                    <div style="width: 50%">
                      <span>检查状态</span>
                      <el-select
                        style="width: 100%"
                        placeholder="请选择"
                        size="mini"
                        clearable
                        v-model="userQueue.peStatus"
                        class="input"
                        @change="getUserList"
                      >
                        <el-option
                          v-for="item in G_peStatus"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        >
                        </el-option>
                      </el-select>
                    </div>
                  </li>
                  <li>
                    <label>
                      <el-select
                        v-model.trim="params.queryType"
                        style="width: 100px"
                        placeholder="请选择"
                        size="mini"
                        @change="getUserList"
                      >
                        <el-option
                          v-for="item in timeType"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        >
                        </el-option>
                      </el-select>
                    </label>
                    <el-date-picker
                      :picker-options="{ shortcuts: G_datePickerShortcuts }"
                      style="flex: 1"
                      :clearable="false"
                      v-model="dateVal"
                      size="mini"
                      type="daterange"
                      range-separator="-"
                      @change="getUserList"
                      value-format="yyyy-MM-dd"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                    >
                    </el-date-picker>
                  </li>
                  <li>
                    <el-radio-group
                      style="
                        display: flex;
                        flex-wrap: wrap;
                        align-content: center;
                      "
                      v-model.trim="userQueue.isActive"
                      @change="getUserList"
                      class="radio_group"
                      ref="radios"
                      size="mini"
                    >
                      <el-radio :label="null">全部</el-radio>
                      <el-radio :label="true">已激活</el-radio>
                      <el-radio :label="false">未激活</el-radio>
                    </el-radio-group>
                    <el-button
                      class="blue_btn btn"
                      size="mini"
                      icon="iconfont icon-search"
                      @click="getUserList"
                      >查询</el-button
                    >
                  </li>
                  <li>
                    <span>记录数：{{ userQueue.total }}</span>
                  </li>
                </ul>
                <div style="flex: 1; flex-shrink: 0; overflow: auto">
                  <PublicTableV2
                    ref="userTable_Refs"
                    :params="params"
                    :theads="userListTheads"
                    :tableDataMap="
                      (data) => {
                        return data.record;
                      }
                    "
                    :elStyle="{
                      'show-selection': false,
                      'header-cell-style': {
                        height: '20px',
                        background: 'rgba(23,112,223,.2)',
                        fontSize: '14px',
                        color: '#2D3436',
                        padding: '0 '
                      }
                    }"
                    rowKey="regNo"
                    remoteByPage
                    :url="$apiUrls.GetRegistersByMultipleFilterNew"
                    @rowDblclick="userListRowDblclick"
                    @request-success="successfulCallback"
                  >
                    <template #name="{ scope }">
                      <div class="sex_wrap">
                        <i v-if="scope.row.sex == 1" class="el-icon-male"></i>
                        <i v-if="scope.row.sex == 2" class="el-icon-female"></i>
                        {{ scope.row.name }}
                      </div>
                    </template>
                    <template #peStatus="{ scope }">
                      <div>
                        {{ G_EnumList['PeStatus'][scope.row.peStatus] }}
                      </div>
                    </template>
                  </PublicTableV2>
                </div>
              </div>
            </div>
            <el-button
              class="blue_btn btn query_btn"
              size="small"
              icon="iconfont icon-liebiao"
              style="margin-right: 10px"
              slot="reference"
              >人员列表</el-button
            >
          </el-popover>
          <el-input
            v-model.trim="regNoSearchVal"
            size="medium"
            style="width: 245px"
            placeholder="体检号/档案号/姓名/证件号/电话"
            @keyup.enter.native.stop="search('/company')"
            clearable
          ></el-input>
        </div>
        <BtnGroup
          :btnList="['新建', '保存']"
          ref="btnGroup_Ref"
          @save="save"
          @create="
            createBtn().then(() => {
              openOrClose();
            })
          "
        >
          <template #head>
            <el-button
              size="small"
              icon="iconfont icon-yonghujiluchaxun"
              class="violet_btn"
              @click="recordSearchBtn"
              >记录查询 F4</el-button
            >
          </template>
          <template #footAdd>
            <el-button
              size="small"
              icon="iconfont icon-huifuxitongmoren"
              class="violet_btn"
              @click="updateInfo"
              >更新信息</el-button
            >
            <el-button
              class="green_btn"
              size="small"
              icon="el-icon-tickets"
              :class="{ 'questionnaire-disabled': questionnaireDisabled }"
              @click="questionnaire"
              >问卷调查</el-button
            >
            <el-button
              class="green_btn"
              size="small"
              icon="iconfont icon-dayin-"
              @click="print"
              >打印</el-button
            >
            <el-button
              class="green_btn"
              size="small"
              icon="el-icon-s-custom"
              @click="preview"
              >预览</el-button
            >
          </template>
        </BtnGroup>
      </header>
      <!-- 个人信息 -->
      <UerInfoForm ref="form_Ref"></UerInfoForm>
      <!-- 已选套餐和危害因素 -->
      <div class="selectMeal_wrap" ref="selectMeal">
        <!-- 候选套餐和组合 -->
        <!-- <div class="candidateCombs_wrap">

                </div> -->
        <!-- 危害因素 -->
        <div class="hagard_wrap" v-if="C_isOccupations">
          <header :class="userPeStatus > 2 ? 'prohibitClicking' : ''">
            <div class="left_div">
              <label>危害因素：</label>
            </div>
            <div class="right_div">
              <span
                type="text"
                style="color: #089c66; margin-left: 10px; cursor: pointer"
                icon="iconfont icon-shanchu"
                @click="
                  isCancelRefundsBtnShow
                    ? $message.warning('已选组合中存在退款申请!')
                    : addHarmBtn()
                "
              >
                <i class="el-icon-circle-plus-outline"></i>
                添加
              </span>
              <span
                type="text"
                style="color: #d63031; margin-left: 10px; cursor: pointer"
                icon="iconfont icon-shanchu"
                @click="checkHarmDel"
              >
                <i class="iconfont icon-shanchu"></i>
                删除
              </span>
            </div>
          </header>
          <!-- 已选的危害因素 -->
          <div class="table_wrap" ref="tableWrap_ref">
            <el-table
              :data="checkHarmList"
              ref="checkHarmList_Ref"
              size="small"
              height="100%"
              :max-height="tableMaxHeight"
              style="width: 100%"
              @cell-dblclick="handleCellEnter"
            >
              <el-table-column type="selection" width="45"> </el-table-column>
              <el-table-column prop="hazardousCode" label="编号" width="70">
              </el-table-column>
              <el-table-column prop="hazardousName" label="名称">
                <div class="hazar-item" slot-scope="scope">
                  <el-input
                    size="mini"
                    class="item__input"
                    v-model.trim="scope.row.hazardousName"
                    autofocus
                    @blur="handleBlur"
                  ></el-input>
                  <div class="item__txt">
                    <i
                      class="el-icon-edit"
                      style="color: #409eff"
                      v-show="scope.row.isOtherHazardous"
                    ></i>
                    {{ scope.row.hazardousName }}
                  </div>
                </div>
              </el-table-column>
              <el-table-column width="135" prop="date">
                <template #header>
                  <el-popover placement="top-start" trigger="click">
                    <div>
                      <el-date-picker
                        v-model="harmDateVal"
                        size="mini"
                        type="date"
                        placeholder="选择日期"
                        style="margin-right: 5px; width: 150px"
                      ></el-date-picker>
                      <el-button
                        :disabled="checkHarmList.length == 0"
                        type="primary"
                        @click="harmDateConfirm"
                        size="mini"
                        >确定</el-button
                      >
                    </div>
                    <span slot="reference" style="cursor: pointer">
                      <i class="el-icon-edit"></i> 开始接害日期</span
                    >
                  </el-popover>
                </template>
                <template slot-scope="scope">
                  <el-date-picker
                    class="harm_date"
                    style="width: 100%"
                    prefix-icon="null"
                    value-format="yyyy-MM-dd"
                    v-model="scope.row.startDateOfHazards"
                    @change="harmDateChange(scope.row)"
                    type="date"
                    size="small"
                    placeholder="选择日期"
                  >
                  </el-date-picker>
                </template>
              </el-table-column>
              <el-table-column label="接害工龄" width="100">
                <template #header>
                  <el-popover placement="top-start" trigger="click">
                    <div style="display: flex">
                      <div class="harmAge_div">
                        <el-input-number
                          v-model="batchYearsOfHazards"
                          style="width: 60px"
                          :controls="false"
                          :step="1"
                          :min="0"
                          :max="150"
                          :precision="0"
                          size="small"
                        ></el-input-number>
                        年
                        <el-input-number
                          v-model="batchMonthsOfHazards"
                          style="width: 60px"
                          :controls="false"
                          :step="1"
                          :min="0"
                          :max="11"
                          :precision="0"
                          size="small"
                        ></el-input-number>
                        月
                      </div>
                      <el-button
                        :disabled="checkHarmList.length == 0"
                        type="primary"
                        @click="harmYearsAndMonthsConfirm"
                        size="mini"
                        >确定</el-button
                      >
                    </div>
                    <span slot="reference" style="cursor: pointer">
                      <i class="el-icon-edit"></i> 接害工龄</span
                    >
                  </el-popover>
                </template>
                <template slot-scope="scope">
                  <div class="harmAge_div">
                    <el-input-number
                      v-model="scope.row.yearsOfHazards"
                      :controls="false"
                      :step="1"
                      :min="0"
                      :max="150"
                      :precision="0"
                      size="small"
                    ></el-input-number>
                    年
                    <el-input-number
                      v-model="scope.row.monthsOfHazards"
                      :controls="false"
                      :step="1"
                      :min="0"
                      :max="11"
                      :precision="0"
                      size="small"
                    ></el-input-number>
                    月
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <!-- 候选套餐/组合 -->
        <div class="meal-common" v-else>
          <el-tabs
            type="border-card"
            stretch
            style="height: 100%; display: flex; flex-direction: column"
            v-model="mealActiveName"
            @tab-click="mealTabsTabClick"
            :style="
              selectMealHeight !== 0 ? { height: selectMealHeight + 'px' } : {}
            "
          >
            <el-tab-pane label="体检套餐" stretch name="meal" class="bs-meal">
              <div style="box-sizing: border-box; margin: 0 10px">
                <el-input
                  v-model.trim="searchMealVal"
                  @input="mealSearch"
                  @keypress.enter.native="mealSearch"
                  size="small"
                  ref="mealInput_Ref"
                  placeholder="请输入套餐名称搜索"
                >
                </el-input>
              </div>

              <div
                class="dialog_table meal_dialog"
                outline="0"
                hidefocus="true"
                tabindex="-1"
                @blur="removeKeyboardEvent"
                @click="addTokeyboardEvents"
                style="outline: none"
              >
                <PublicTable
                  :key="mealActiveName"
                  :viewTableList="searchMealList"
                  :theads="mealTheadsV1"
                  :isSortShow="false"
                  :showHeader="false"
                  :cell_red="cell_red"
                  :columnWidth="mealColumnWidth"
                  @rowDblclick="groupMealDBClick"
                  @currentChange="mealCurrentChange"
                  ref="mealTable_Ref"
                  :highlight-current-row="highlight"
                >
                  <template #columnLeft>
                    <el-table-column width="40">
                      <template slot-scope="scope">
                        <i
                          class="iconfont icon-xuanzhong select_icon"
                          v-if="scope.row.isSelected"
                        ></i>
                        <span v-if="!scope.row.isSelected"></span>
                      </template>
                    </el-table-column>
                  </template>
                  <template #price="{ scope }">
                    <div style="text-align: right">
                      {{ handlePrice(scope.row.price) }}
                    </div>
                  </template>
                  <template #clusName="{ scope }">
                    <span
                      ><i class="group_sign" v-if="scope.row.isGroup">团</i
                      >{{
                        `${scope.row.clusCode} - ${scope.row.clusName}`
                      }}</span
                    >
                  </template>
                </PublicTable>
              </div>
            </el-tab-pane>
            <el-tab-pane
              label="体检组合"
              stretch
              name="comb"
              class="bs-combination"
            >
              <div class="combination-search">
                <el-select
                  clearable
                  v-model="combTypeVal"
                  @change="comboSearch"
                  size="small"
                  placeholder="请选择"
                  style="width: 100px; margin-right: 5px"
                >
                  <el-option
                    v-for="item in combTypeList"
                    :key="item.clsCode"
                    :label="item.clsName"
                    :value="item.clsCode"
                  >
                  </el-option>
                </el-select>
                <el-input
                  v-model.trim="comboSearchVal"
                  clearable
                  size="small"
                  ref="combInput_Ref"
                  style="flex: 1; flex-shrink: 0"
                  placeholder="请输入组合名称和代码搜索"
                  @input="comboSearch"
                  @keypress.enter.native="comboSearch"
                ></el-input>
              </div>
              <div
                class="dialog_table meal_dialog"
                style="outline: none"
                outline="0"
                hidefocus="true"
                tabindex="-1"
                @blur="removeKeyboardEvent"
                @click="addTokeyboardEvents"
              >
                <PublicTable
                  :key="mealActiveName"
                  :viewTableList="searchCombList"
                  :theads="combsTheads"
                  :showHeader="false"
                  :cell_red="cell_red"
                  :columnWidth="comboColumnWidth"
                  :isSortShow="false"
                  @rowDblclick="groupCombDbClick"
                  @currentChange="combCurrentChange"
                  ref="combTable_Ref"
                  :highlight-current-row="highlight"
                >
                  <template #columnLeft>
                    <el-table-column width="40">
                      <template slot-scope="scope">
                        <i
                          class="iconfont icon-xuanzhong select_icon"
                          v-if="scope.row.isSelected"
                        ></i>
                        <span v-if="!scope.row.isSelected"></span>
                      </template>
                    </el-table-column>
                  </template>
                  <template #combName="{ scope }">
                    <div>
                      {{ `${scope.row.combCode} - ${scope.row.combName}` }}
                    </div>
                  </template>
                  <template #price="{ scope }">
                    <div style="text-align: right">
                      {{ handlePrice(scope.row.price) }}
                    </div>
                  </template>
                  <template #columnRight>
                    <el-table-column width="50" label="明细">
                      <template slot-scope="scope">
                        <el-popover
                          placement="right"
                          width="400"
                          @show="combExpandChange(scope.row)"
                          trigger="click"
                        >
                          <el-table :data="scope.row.detailList" height="300px">
                            <el-table-column
                              width="80"
                              property="itemCode"
                              label="项目代码"
                            ></el-table-column>
                            <el-table-column
                              property="itemName"
                              label="项目名称"
                            ></el-table-column>
                          </el-table>
                          <span
                            slot="reference"
                            style="color: #1770df; cursor: pointer"
                            >查看</span
                          >
                        </el-popover>
                      </template>
                    </el-table-column>
                  </template>
                </PublicTable>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
        <!-- 已选套餐 -->
        <div class="meal_wrap">
          <header :class="userPeStatus > 2 ? 'prohibitClicking' : ''">
            <div class="left_div">
              <label style="min-width: fit-content">套餐：</label>
              <div class="select_meal">
                <el-tag
                  size="medium"
                  @close="mealTagDel(item, index)"
                  v-for="(item, index) in checkMeal"
                  :key="item.clusCode"
                  closable
                >
                  <i class="job_sign" v-if="item.isOccupation">职</i>
                  {{ item.clusName }}
                </el-tag>
              </div>
            </div>
            <div class="right_div" style="min-width: fit-content">
              <span
                v-if="C_isOccupations"
                type="text"
                style="color: #089c66; margin-left: 3px; cursor: pointer"
                icon="iconfont icon-shanchu"
                @click="groupAddMealBtn"
              >
                <i class="el-icon-circle-plus-outline"></i>
                添加
              </span>
              <el-popover
                placement="bottom"
                width="290"
                trigger="click"
                @after-enter="showDiscount"
                @after-leave="closePopover"
              >
                <div
                  style="display: flex; align-items: center; flex-wrap: wrap"
                >
                  批量折扣：
                  <el-input-number
                    ref="ref_input"
                    @keyup.enter.native.stop="discountConfirm"
                    size="mini"
                    v-model="discount"
                    controls-position="right"
                    :precision="2"
                    :min="G_userInfo.codeOper.maxDiscount"
                    :step="0.01"
                    :max="1"
                  ></el-input-number>
                  <el-button
                    type="primary"
                    style="margin-left: 3px"
                    size="mini"
                    @click="discountConfirm"
                    >确 定</el-button
                  >
                  <p style="width: 100%">
                    折扣范围：{{
                      G_userInfo.codeOper.maxDiscount === null
                        ? 1
                        : G_userInfo.codeOper.maxDiscount
                    }}
                    ~ 1
                  </p>
                </div>
                <div
                  style="
                    display: flex;
                    align-items: center;
                    margin-top: 5px;
                    flex-wrap: wrap;
                  "
                >
                  <span style="width: 80px">一口价：</span>
                  <el-input-number
                    style="width: 120px"
                    ref="ref_input"
                    @keyup.enter.native.stop="fixedPriceFun"
                    :controls="false"
                    size="mini"
                    v-model="fixedPrice"
                    :precision="2"
                    :min="
                      subNum(
                        C_totalMoney.totalMoney,
                        G_userInfo.codeOper.maxDeductionPrice
                      )
                    "
                    :max="Number(totalOriginalPrice)"
                  ></el-input-number>
                  <el-button
                    type="primary"
                    style="margin-left: 3px"
                    size="mini"
                    @click="fixedPriceFun"
                    >确 定</el-button
                  >
                  <p style="width: 100%">
                    一口价范围：{{
                      subNum(
                        C_totalMoney.totalOriginalPrice,
                        G_userInfo.codeOper.maxDeductionPrice
                      )
                    }}
                    ~ {{ C_totalMoney.totalOriginalPrice }}
                  </p>
                </div>
                <span
                  v-show="checkCombs.length > 0"
                  slot="reference"
                  type="text"
                  style="
                    color: #1770df;
                    margin-left: 3px;
                    cursor: pointer;
                    margin-right: 3px;
                  "
                  icon="iconfont icon-shanchu"
                >
                  <i class="el-icon-price-tag"></i>
                  打折
                </span>
              </el-popover>
              <span
                type="text"
                style="
                  color: #1770df;
                  margin-left: 3px;
                  cursor: pointer;
                  margin-right: 3px;
                "
                icon="iconfont icon-shanchu"
                @click="groupCopyCombination"
              >
                <i class="el-icon-document-copy"></i>
                复制
              </span>
              <el-popover placement="bottom" width="225" trigger="hover">
                <el-button type="primary" size="mini" @click="delCombs(1)"
                  >不保留套餐号</el-button
                >
                <el-button
                  type="primary"
                  style="margin-left: 3px"
                  size="mini"
                  @click="delCombs(2)"
                  >保留套餐号</el-button
                >
                <span
                  type="text"
                  slot="reference"
                  style="
                    color: #d63031;
                    margin-left: 3px;
                    cursor: pointer;
                    margin-right: 3px;
                  "
                  icon="iconfont icon-shanchu"
                  @click="delCombs(0)"
                >
                  <i class="iconfont icon-shanchu"></i>
                  删除
                </span>
              </el-popover>
              <el-button
                icon="iconfont icon-chexiao"
                class="blue_btn"
                size="mini"
                v-if="isCancelRefundsBtnShow"
                @click="cancelRefundsBtn"
                >撤销退款
              </el-button>
              <el-button
                :disabled="C_refundBtn"
                icon="iconfont icon-tuikuan"
                class="blue_btn"
                size="mini"
                @click="refundsBtn"
                >退款申请
              </el-button>
            </div>
          </header>
          <!-- 已选的组合 -->
          <div class="table_wrap">
            <el-table
              ref="combsTable_Ref"
              class="combs_table"
              :data="checkCombs"
              size="small"
              height="100%"
              :row-class-name="tableRowClassName"
              select
              :max-height="tableMaxHeight"
              style="width: 100%"
            >
              <el-table-column
                type="selection"
                :selectable="combSelectable"
                width="45"
              >
              </el-table-column>
              <el-table-column prop="combCode" label="组合代码" width="90">
              </el-table-column>
              <el-table-column prop="combName" label="组合名称">
              </el-table-column>
              <el-table-column
                label="体检类型"
                width="106"
                v-if="C_isOccupations"
              >
                <template slot="header">
                  <el-popover placement="top-start" trigger="hover">
                    <div>
                      <el-button
                        :disabled="checkCombs.length == 0"
                        type="primary"
                        @click="checkAll(true, true)"
                        size="mini"
                        >普-全选</el-button
                      >
                      <el-button
                        :disabled="checkCombs.length == 0"
                        type="primary"
                        @click="checkAll(true, false)"
                        size="mini"
                        >普-全不选</el-button
                      >
                    </div>
                    <div style="margin-top: 5px">
                      <el-button
                        :disabled="checkCombs.length == 0"
                        type="primary"
                        @click="checkAll(false, true)"
                        size="mini"
                        >职-全选</el-button
                      >
                      <el-button
                        :disabled="checkCombs.length == 0"
                        type="primary"
                        @click="checkAll(false, false)"
                        size="mini"
                        >职-全不选</el-button
                      >
                    </div>
                    <span slot="reference" style="cursor: pointer">
                      <i class="el-icon-info"></i> 体检类型</span
                    >
                  </el-popover>
                </template>
                <template slot-scope="scope">
                  <el-checkbox
                    v-model="scope.row.isOrdinary"
                    style="margin-right: 10px"
                    @change="handleCheckboxChange(scope.row, 'isOrdinary')"
                    :disabled="scope.row.isChecked || !C_ordinary"
                    >普</el-checkbox
                  >
                  <el-checkbox
                    v-model="scope.row.isOccupation"
                    @change="handleCheckboxChange(scope.row, 'isOccupation')"
                    :disabled="scope.row.isChecked || !C_occupation"
                    >职</el-checkbox
                  >
                </template>
              </el-table-column>
              <el-table-column
                prop="originalPrice"
                label="原价"
                width="70"
                align="right"
              >
              </el-table-column>
              <el-table-column prop="discount" label="折扣" width="100">
                <template slot-scope="scope">
                  <el-input-number
                    size="mini"
                    @change="discountChange(scope.row)"
                    v-model="scope.row.discount"
                    :disabled="
                      scope.row.payStatus == 1 ||
                      scope.row.payStatus == 2 ||
                      scope.row.isChecked ||
                      userPeStatus >= 2
                    "
                    controls-position="right"
                    :precision="2"
                    :min="0"
                    :step="0.01"
                    :max="1"
                  ></el-input-number>
                </template>
              </el-table-column>
              <el-table-column
                prop="price"
                label="折后价"
                width="70"
                align="right"
                class-name="price_color"
              >
              </el-table-column>
              <el-table-column label="材料费" width="70" align="right">
                <template slot-scope="scope">
                  <div class="testTubes_div" v-if="!!scope.row.testTubes">
                    <el-popover
                      placement="top-start"
                      width="200"
                      trigger="hover"
                    >
                      <div
                        v-for="(item, idx) in scope.row.testTubes.testTubes"
                        :key="idx"
                      >
                        {{ item.combName + ' *' + item.discount }}
                      </div>
                      <div slot="reference">
                        <span>
                          {{
                            scope.row.testTubes.testTubesPrice
                              ? handlePrice(scope.row.testTubes.testTubesPrice)
                              : ''
                          }}
                        </span>
                      </div>
                    </el-popover>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="isPayBySelf" label="自费" width="50">
                <template slot-scope="scope">
                  <el-checkbox
                    v-model="scope.row.isPayBySelf"
                    style="margin-right: 10px"
                    :disabled="
                      containComboCode.includes(scope.row.combCode.trim()) ||
                      scope.row.isChecked ||
                      scope.row.payStatus == 1 ||
                      scope.row.payStatus == 2
                    "
                  ></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column prop="payStatus" label="支付状态" width="85">
                <template slot-scope="scope">
                  <div
                    :class="
                      scope.row.payStatus == 0 ? 'noPay_text' : 'havePay_text'
                    "
                  >
                    {{ G_EnumList['PayStatus'][scope.row.payStatus] }}
                  </div>
                </template>
              </el-table-column>
              <!-- <template #append>

                            </template> -->
            </el-table>
          </div>
          <!-- 总金额 -->
          <div class="totalMoney_wrap">
            <ul>
              <li>
                <label>组合：</label>
                <span>{{ checkCombs.length }}个</span>
              </li>
              <li class="">
                <label>项目价格：</label>
                <span>{{ C_totalMoney.itemMoney }}元</span>
              </li>
              <li class="">
                <label>附加费+材料费：</label>
                <span>{{ C_totalMoney.testTubesMoney }}元</span>
              </li>
              <li>
                <label>未缴费：</label>
                <span>{{ C_totalMoney.unPayMoney }}元</span>
              </li>
              <li class="total_money money_color">
                <label>总价格：</label>
                <span>{{ C_totalMoney.totalMoney }}元</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <!-- 候选套餐和组合弹窗 -->
    <el-dialog
      title="添加套餐和组合"
      :visible.sync="mealDialogShow"
      width="50%"
      :close-on-click-modal="false"
      custom-class="meal_dialog"
      @opened="mealDialogOpen"
    >
      <div class="meal_content">
        <el-tabs v-model="mealActiveName" @tab-click="mealTabsTabClick">
          <el-tab-pane
            label="体检套餐"
            name="meal"
            v-if="G_config.physicalMode.includes('普检')"
          >
            <el-input
              v-model.trim="searchMealVal"
              @input="mealSearch"
              @keypress.enter.native="mealSearch"
              size="small"
              ref="mealInput_Ref"
              placeholder="请输入套餐名称搜索"
              style="width: 100%"
            >
            </el-input>
            <div class="dialog_table">
              <PublicTable
                :key="mealActiveName"
                :viewTableList="searchMealList"
                :theads="mealTheadsV1"
                :isSortShow="false"
                :showHeader="false"
                :cell_red="cell_red"
                :columnWidth="mealColumnWidth"
                @rowDblclick="mealDBClick"
                @currentChange="mealCurrentChange"
                ref="dialogMealTable_Ref"
              >
                <template #columnLeft>
                  <el-table-column width="40">
                    <template slot-scope="scope">
                      <i
                        class="iconfont icon-xuanzhong select_icon"
                        v-if="scope.row.isSelected"
                      ></i>
                      <span v-if="!scope.row.isSelected"></span>
                    </template>
                  </el-table-column>
                </template>
                <template #price="{ scope }">
                  <div style="text-align: right">
                    {{ handlePrice(scope.row.price) }}
                  </div>
                </template>
                <template #clusName="{ scope }">
                  <span
                    ><i class="group_sign" v-if="scope.row.isGroup">团</i
                    >{{ `${scope.row.clusCode} - ${scope.row.clusName}` }}</span
                  >
                </template>
              </PublicTable>
            </div>
          </el-tab-pane>
          <el-tab-pane label="职检套餐" name="harm" v-if="C_isOccupation">
            <el-input
              v-model.trim="searchHarmMealVal"
              @input="harmMealSearch"
              size="small"
              ref="harmMealInput_Ref"
              placeholder="请输入套餐名称搜索"
              style="width: 100%"
            >
            </el-input>
            <div class="dialog_table">
              <PublicTableV2
                :key="mealActiveName"
                ref="harmMealTabel_Ref"
                :theads="mealTheads"
                :elStyle="{
                  'show-selection': false,
                  'show-sort': false,
                  'header-cell-style': { background: '#fff' }
                }"
                rowKey="clusCode"
                :cellClassName="mealCellClassName"
                :url="$apiUrls.ReadCandidateClusterNew"
                :isScrollLoad="false"
                :isOpenPage="false"
                @rowDblclick="mealDBClick"
                @currentChange="harmbCurrentChange"
              >
                <template #columnLeft>
                  <el-table-column width="40">
                    <template slot-scope="scope">
                      <i
                        class="iconfont icon-xuanzhong select_icon"
                        v-if="scope.row.isSelected"
                      ></i>
                      <span v-if="!scope.row.isSelected"></span>
                    </template>
                  </el-table-column>
                </template>
                <template #clusName="{ scope }">
                  <span
                    ><i class="group_sign" v-if="scope.row.isGroup">团</i
                    >{{
                      `${scope.row.clusCode} -
                    ${scope.row.clusName}`
                    }}</span
                  >
                </template>
              </PublicTableV2>
            </div>
          </el-tab-pane>
          <el-tab-pane label="体检组合" name="comb">
            <div class="comb_search">
              <el-select
                clearable
                v-model="combTypeVal"
                @change="comboSearch"
                size="small"
                placeholder="请选择"
                style="width: 100px; margin-right: 5px"
              >
                <el-option
                  v-for="item in combTypeList"
                  :key="item.clsCode"
                  :label="item.clsName"
                  :value="item.clsCode"
                >
                </el-option>
              </el-select>
              <el-input
                v-model.trim="comboSearchVal"
                clearable
                size="small"
                ref="combInput_Ref"
                style="flex: 1; flex-shrink: 0"
                placeholder="请输入组合名称和代码搜索"
                @input="comboSearch"
                @keypress.enter.native="comboSearch"
              ></el-input>
            </div>
            <div class="dialog_table">
              <PublicTable
                :key="mealActiveName"
                :viewTableList="searchCombList"
                :theads="combsTheads"
                :showHeader="false"
                :cell_red="cell_red"
                :columnWidth="comboColumnWidth"
                :isSortShow="false"
                @rowDblclick="combDbClick"
                @currentChange="combCurrentChange"
                ref="dialogCombTable_Ref"
              >
                <template #columnLeft>
                  <el-table-column width="40">
                    <template slot-scope="scope">
                      <i
                        class="iconfont icon-xuanzhong select_icon"
                        v-if="scope.row.isSelected"
                      ></i>
                      <span v-if="!scope.row.isSelected"></span>
                    </template>
                  </el-table-column>
                </template>
                <template #combName="{ scope }">
                  <div>
                    {{ `${scope.row.combCode} - ${scope.row.combName}` }}
                  </div>
                </template>
                <template #price="{ scope }">
                  <div style="text-align: right">
                    {{ handlePrice(scope.row.price) }}
                  </div>
                </template>
                <template #columnRight>
                  <el-table-column width="50" label="明细">
                    <template slot-scope="scope">
                      <el-popover
                        placement="right"
                        width="400"
                        @show="combExpandChange(scope.row)"
                        trigger="click"
                      >
                        <el-table :data="scope.row.detailList" height="300px">
                          <el-table-column
                            width="80"
                            property="itemCode"
                            label="项目代码"
                          ></el-table-column>
                          <el-table-column
                            property="itemName"
                            label="项目名称"
                          ></el-table-column>
                        </el-table>
                        <span
                          slot="reference"
                          style="color: #1770df; cursor: pointer"
                          >查看</span
                        >
                      </el-popover>
                    </template>
                  </el-table-column>
                </template>
              </PublicTable>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="mealDialogShow = false" size="small"
          >取 消</el-button
        >
        <el-button
          type="primary"
          @click="mealConfirm"
          :disabled="!isCanClickconfirmBtn"
          size="small"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <!-- 体检号/档案号/姓名/证件号/电话 -->
    <el-dialog
      title="订单记录"
      :visible.sync="regNoSearchDialogShow"
      append-to-body
      width="890px"
      custom-class="dialogCss"
    >
      <PublicTableV2
        ref="regNoSearchTable_Ref"
        :params="regNoSearchParams"
        :theads="regNoSearchTheads"
        :elStyle="{ 'show-selection': false }"
        rowKey="regNo"
        :tableDataMap="(data) => data"
        :url="$apiUrls.GetRegisterByQueryTypeNew"
        style="height: 600px"
        @rowDblclick="regNoSearchRowDblclick"
      >
        <template #peCls="{ scope }">
          <div>
            {{ G_EnumList['PeCls'][scope.row.peCls] }}
          </div>
        </template>
        <template #peStatus="{ scope }">
          <div>
            {{ G_EnumList['PeStatus'][scope.row.peStatus] }}
          </div>
        </template>
        <template #sex="{ scope }">
          <div>
            {{ G_EnumList['Sex'][scope.row.sex] }}
          </div>
        </template>
        <template #isActive="{ scope }">
          <div>
            <el-checkbox
              v-model.trim="scope.row.isActive"
              disabled
            ></el-checkbox>
          </div>
        </template>
        <template #group="{ scope }">
          <div>
            <el-checkbox v-model.trim="scope.row.group" disabled></el-checkbox>
          </div>
        </template>
        <template #isVIP="{ scope }">
          <div>
            <el-checkbox v-model.trim="scope.row.isVIP" disabled></el-checkbox>
          </div>
        </template>
        <template #isCompanyCheck="{ scope }">
          <div>
            <el-checkbox
              v-model.trim="scope.row.isCompanyCheck"
              disabled
            ></el-checkbox>
          </div>
        </template>
      </PublicTableV2>
      <div slot="footer" class="dialog-footer">
        <el-button @click="regNoSearchDialogShow = false" size="small"
          >取 消</el-button
        >
      </div>
    </el-dialog>
    <!-- 添加危害因素 -->
    <el-dialog
      title="添加危害因素"
      :visible.sync="harmDialogShow"
      width="50%"
      :close-on-click-modal="false"
      custom-class="meal_dialog"
      @open="harmDialogOpen"
    >
      <div class="meal_content harm_content">
        <div class="comb_search">
          <el-select
            clearable
            v-model="harmTypeVal"
            @change="harmTypeChange"
            size="small"
            placeholder="请选择危害因素分类"
            style="width: 180px; margin-right: 5px"
          >
            <el-option
              v-for="item in G_HazardousTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-input
            v-model.trim="harmKeyword"
            clearable
            size="small"
            style="flex: 1; flex-shrink: 0"
            placeholder="请输入危害因素名称搜索"
            @input="harmTypeChange"
          ></el-input>
        </div>
        <div class="dialog_table no-select">
          <vxe-list
            ref="vxeList_Ref"
            height="468px"
            class="my-list"
            :loading="harmLoading"
            :data="harmList"
            :scroll-y="{ enabled: true }"
          >
            <template #default="{ items }">
              <div
                @dblclick="harmListDblClick(item, $event)"
                class="my-list-item"
                v-for="(item, index) in items"
                :key="index"
              >
                <!-- <span>{{index+1}}</span> -->
                <i
                  class="iconfont select_icon"
                  :class="{ 'icon-xuanzhong': item.isSelected }"
                ></i>
                <div>{{ item.hazardousName }}</div>
                <el-input
                  size="small"
                  class="hazardousAlias_input"
                  style="margin-left: auto; width: 50%"
                  v-show="item.isOtherHazardous && item.isSelected"
                  v-model.trim="item.hazardousAlias"
                  placeholder="请输入名称"
                ></el-input>
              </div>
            </template>
          </vxe-list>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="harmDialogShow = false" size="small"
          >取 消</el-button
        >
        <el-button type="primary" @click="harmConfirm" size="small"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <groupCopy
      ref="group_copy"
      :isCompanyCheck="true"
      :isOccupation.sync="C_isOccupation"
    ></groupCopy>
    <el-dialog
      title="问卷调查表"
      :visible.sync="isShowdialogTable"
      class="questionnaire_dig"
    >
      <Questionnaire
        :formInfo="formInfo"
        @submitSuccess="submitSuccess"
        :questionnaireDisabled="questionnaireDisabled"
      ></Questionnaire>
    </el-dialog>
    <!-- 退费 -->
    <Refund
      :refundsDigShow.sync="refundsDigShow"
      :refundsList="refundsList"
      :regNo="refundRegNo"
      :tableRowClassName="tableRowClassName"
      @refundSuccess="refundSuccess"
      :testTubes="testTubes"
    />

    <!-- 打印 -->
    <PrintSelection
      ref="PrintSelection_Ref"
      :defaultCheck="true"
      :displaySwitches.sync="printDisplay"
    />
    <!-- 预览 -->
    <RegisterForThePreview
      ref="RegisterForThePreview_Ref"
      :displaySwitches.sync="previewDisplay"
    />
    <RecordSearch
      ref="recordSearch_Ref"
      style="width: 100%"
      :isGroup="true"
      @rowDBclick="rowDBclick"
      v-show="recordShow"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick } from 'vue';
import { useStore } from 'vuex';
import PublicTableV2 from '@/components/publicTable2';
import PublicTable from '@/components/publicTable';
import BtnGroup from './components/btnGroup';
import moment from 'moment';
import UerInfoForm from './components/uerInfoForm';
import NewCombo from './mixins/newCombo';
import { dataUtils } from '../../common';
import groupCopy from './components/groupCopy';
import Questionnaire from './components/questionnaire.vue';
import PrintSelection from '@/components/printSelection.vue';
import RegisterForThePreview from '@/components/registerForThePreviewV2.vue';
import Shortcut from '@/common/shortcut';
import RecordSearch from '@/pages/register/components/recordSearch.vue';
import Refund from './components/refund.vue';
import { ajax as $ajax } from '@/common';
import { apiUrls as $apiUrls } from '@/common/apiUrls';

// 组件名称
defineOptions({
  name: 'GroupRegisterV2'
});

// Store
const store = useStore();

// 响应式数据
const showUserListFlag = ref(false);
const recordShow = ref(false);
const loading = ref(false);
const userListScrollTop = ref(0);
const userListIsGet = ref(true);
const highlight = ref(true);
const selectMealHeight = ref(0);

// 用户队列数据
const userQueue = ref({
  total: 0,
  companyCode: '',
  companyList: [],
  companyTimes: -1,
  clusCode: '',
  clusterList: [],
  clusterLists: [],
  companyDeptCode: '',
  companyDeptList: [],
  isHavue: true,
  isActive: null,
  peStatus: ''
});

// 时间相关
const dateVal = ref([
  moment().startOf('day').format('YYYY-MM-DD'),
  moment().startOf('day').format('YYYY-MM-DD')
]);

const timeType = ref([
  { value: 1, label: '登记时间' },
  { value: 2, label: '体检时间' },
  { value: 3, label: '预约时间' }
]);

// 表格相关
const userListTheads = ref([
  {
    label: '体检号',
    prop: 'regNo',
    width: 120,
    sortable: true,
    align: 'center'
  },
  {
    label: '姓名',
    prop: 'name',
    align: 'center'
  },
  {
    label: '检查状态',
    prop: 'peStatus',
    width: 80,
    align: 'center'
  }
]);

// 查询参数
const params = ref({
  patCode: '',
  regNo: '',
  name: '',
  sex: -1,
  cardNo: '',
  peStatus: -1,
  queryType: 2,
  startTime: '',
  endTime: '',
  companyCode: '',
  companyTimes: -1,
  clusCode: '',
  companyDeptCode: '',
  isCompanyCheck: true,
  bookType: -1,
  peCls: -1,
  keyword: '',
  isOccupation: false
});

// 其他状态
const printDisplay = ref(false);
const previewDisplay = ref(false);
const childUserInfo = ref({
  isOrdinary: true,
  isOccupation: false
});
const peCls = ref([]);
const isCompanyCheck = ref(true);
const isOccupation = ref(false);
const userSearchVal = ref('');
const regNoSearchVal = ref('');

// 计算属性
const G_config = computed(() => store.getters.G_config);
const G_datePickerShortcuts = computed(() => store.getters.G_datePickerShortcuts);
const G_peStatus = computed(() => store.getters.G_peStatus);
const G_EnumList = computed(() => store.getters.G_EnumList);
const G_userInfo = computed(() => store.getters.G_userInfo);

const C_isOccupation = computed(() => {
  return (
    G_config.value.physicalMode.includes('职检') &&
    peCls.value?.includes('职业检')
  );
});

const C_isOccupations = computed(() => {
  let flag = G_config.value.physicalMode.includes('职检');
  if (!flag) {
    mealActiveName.value = 'meal';
    mealSearch();
  }
  return flag;
});

// 状态标签类型
const getStatusTagType = (status: number) => {
  switch (status) {
    case 0: return '';
    case 1: return 'warning';
    case 2: return 'success';
    case 3: return 'info';
    default: return '';
  }
};
  computed: {
    ...mapGetters(['G_config', 'G_datePickerShortcuts']),
    C_isOccupation() {
      return (
        this.G_config.physicalMode.includes('职检') &&
        this.peCls?.includes('职业检')
      );
    },
    C_isOccupations() {
      let flag = this.G_config.physicalMode.includes('职检');
      if (!flag) {
        this.mealActiveName = 'meal';
        this.mealSearch();
      }
      (this.mealTableIdx = ''),
        (this.combTableIdx = ''),
        (this.harmMealTableIdx = '');
      return flag;
    },
    C_totalMoney() {
      let totalOriginalPrice = 0;
      let totalMoney = 0,
        unPayMoney = 0,
        itemMoney = 0,
        testTubesMoney = 0;
      this.checkCombs.map((item) => {
        // 未缴费
        if (item.payStatus === 0) {
          unPayMoney = dataUtils.add(item.price, unPayMoney);
          unPayMoney = dataUtils.add(item.testTubes.testTubesPrice, unPayMoney);
        }
        itemMoney = dataUtils.add(item.price, itemMoney); //项目价格
        totalMoney = dataUtils.add(item.price, totalMoney);
        totalMoney = dataUtils.add(item.testTubes.testTubesPrice, totalMoney); //总价格
        // 总原价
        totalOriginalPrice = dataUtils.add(
          item.originalPrice,
          totalOriginalPrice
        );
        totalOriginalPrice = dataUtils.add(
          item.testTubes.testTubesPrice,
          totalOriginalPrice
        );
        testTubesMoney = dataUtils.add(
          item.testTubes.testTubesPrice,
          testTubesMoney
        ); //材料费
      });
      this.totalOriginalPrice = totalOriginalPrice;
      return {
        totalMoney,
        totalOriginalPrice,
        unPayMoney,
        itemMoney,
        testTubesMoney
      };
    },
    containComboCode() {
      const getCombinationCodes = (list) =>
        list
          .filter((item) =>
            this.checkMeal.some((i) => i.clusCode === item.clusCode)
          )
          .flatMap(
            (item) => item?.bindCombs?.map((comb) => comb?.combCode) || []
          );

      const combinationCode = getCombinationCodes(this.mealList);
      const harmCombinationCode = getCombinationCodes(this.harmMealList);

      return [...combinationCode, ...harmCombinationCode];
    },
    isCancelRefundsBtnShow() {
      return this.checkCombs.some((item) => item.payStatus === 2);
    },
    //退费
    C_refundBtn() {
      let data = this.checkCombs.filter((item) => item.isPayBySelf);
      if (data.length == 0) {
        return true;
      }
      return data.some((item) => item.payStatus !== 1 && item.payStatus !== 2);
    }
  },
  data() {
    return {
      userListScrollTop: 0,
      userListIsGet: true,
      highlight: true,
      // questionnaireStatus:true,
      userQueue: {
        total: 0,
        companyCode: '',
        companyList: [],
        companyTimes: -1,
        clusCode: '',
        clusterList: [],
        clusterLists: [],
        companyDeptCode: '',
        companyDeptList: [],
        isHavue: true,
        isActive: null,
        peStatus: ''
      },
      loading: false,
      recordShow: false,
      shortcutList: {
        112: this.createBtn,
        113: this.save,
        115: this.recordSearchBtn
      },
      printDisplay: false,
      previewDisplay: false,
      childUserInfo: {
        isOrdinary: true,
        isOccupation: false
      },
      peCls: [],
      selectMealHeight: 0,
      timeType: [
        {
          value: 1,
          label: '登记时间'
        },
        { value: 2, label: '体检时间' },
        { value: 3, label: '预约时间' }
      ],
      isCompanyCheck: true,
      isOccupation: false,
      dateVal: [
        moment().startOf('day').format('YYYY-MM-DD'),
        moment().startOf('day').format('YYYY-MM-DD')
      ],
      userSearchVal: '',
      userListTheads: [
        // {
        //   label: "性别",
        //   prop: "sex",
        //   width: 70,
        // },
        {
          label: '体检号',
          prop: 'regNo',
          width: 120,
          sortable: true,
          align: 'center'
        },
        {
          label: '姓名',
          prop: 'name',
          align: 'center'
        },
        {
          label: '检查状态',
          prop: 'peStatus',
          width: 80,
          align: 'center'
        }
      ],
      userColumnWidth: {
        regNo: 120,
        sex: 70,
        age: 60
      },
      params: {
        patCode: '',
        regNo: '',
        name: '',
        sex: -1,
        cardNo: '',
        peStatus: -1,
        queryType: 2,
        startTime: '',
        endTime: '',
        companyCode: '',
        companyTimes: -1,
        clusCode: '',
        companyDeptCode: '',
        isCompanyCheck: true,
        bookType: -1,
        peCls: -1,
        keyword: '',
        isOccupation: false
      },
      showUserListFlag: false
    };
  },
  methods: {
    addTokeyboardEvents() {
      addEventListener('keyup', this.keyUp);
      addEventListener('keydown', this.keyDown);
      this.highlight = true;
    },
    removeKeyboardEvent() {
      this.highlight = false;
      removeEventListener('keyup', this.keyUp);
      removeEventListener('keydown', this.keyDown);
    },
    rowDBclick(row) {
      console.log(row);
      setTimeout(() => {
        this.userListRowDblclick(row);
      }, 100);
    },
    // 记录查询
    recordSearchBtn() {
      this.recordShow = true;
      this.$refs.recordSearch_Ref.search();
    },
    //打印
    print() {
      if (!this.$refs.form_Ref.userInfo.regNo) {
        return this.$message.warning('请选择体检信息');
      }
      const { regNo, isActive } = this.$refs.form_Ref.userInfo;
      this.printDisplay = true;

      this.$refs.PrintSelection_Ref.setPrintTypeList([{ regNo, isActive }]);
    },
    //预览
    preview() {
      if (!this.$refs.form_Ref.userInfo.regNo) {
        return this.$message.warning('请选择体检信息');
      }
      this.previewDisplay = true;

      this.$refs.RegisterForThePreview_Ref.setPrintTypeList(
        this.$refs.form_Ref.userInfo.regNo
      );
    },
    // 获取用户队列
    getUserList() {
      this.params.startTime = this.dateVal[0];
      this.params.endTime = this.dateVal[1];
      this.params.companyCode = this.userQueue.companyCode?.[0] || '';
      this.params.companyTimes = this.userQueue.companyCode?.[1] || -1;
      this.params.clusCode = this.userQueue.clusCode;
      this.params.companyDeptCode = this.userQueue.companyDeptCode;
      this.params.isActive = this.userQueue.isActive;
      this.params.peStatus = this.userQueue.peStatus || -1;

      if (window.innerWidth > 1500) {
        this.$refs?.userTable_Ref.loadData();
      } else {
        this.$refs?.userTable_Refs.loadData();
      }
    },
    // 用户队列的搜索实时输入的回调
    userListSearchInput() {
      if (this.params.keyword.trim() == '') {
        this.getUserList();
      }
    },
    // 用户队列的双击回调
    userListRowDblclick(row) {
      this.loading = true;
      this.$refs.form_Ref.$refs.ruleForm.clearValidate();
      this.createBtn();
      if (this.$refs.form_Ref.videoShow) {
        this.$refs.form_Ref.closeCamer('video');
        this.$refs.form_Ref.videoShow = false;
      }
      this.$ajax
        .paramsPost(this.$apiUrls.GetRegisterOrderNew, { regNo: row.regNo })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) {
            return;
          }
          // if(returnData.registerOrder.patient.bookType === 3){
          //   this.questionnaireStatus = false;
          // }

          let peCls = this.G_config.physicalMode.includes('职检')
            ? [returnData.registerOrder.patient.peCls]
            : returnData.registerOrder.patient.peCls;
          if (
            returnData.registerOrder.patient.isOccupation &&
            this.G_config.physicalMode.includes('职检')
          ) {
            peCls.push('职业检');
          }
          let userInfo = {
            ...returnData.occupationOrder,
            ...returnData.registerOrder.patient,
            peCls
          };
          this.$set(this.$refs.form_Ref, 'userInfo', userInfo);
          this.$refs.form_Ref.peStatus = returnData.registerOrder.patient
            .isActive
            ? returnData.registerOrder.patient.peStatus
            : false;
          this.userPeStatus = returnData.registerOrder.patient.peStatus;
          this.$set(this.$refs.form_Ref, 'judgeGender', false);
          this.GetQuestionDataByRegNo(userInfo);
          this.$nextTick(async () => {
            await this.getCompanyMeal();
            this.checkMeal = returnData.registerOrder.clusters;
            this.checkCombs = returnData.registerOrder.combs;
            this.checkHarmList = returnData.occupationOrder.hazardInfo || [];
            this.testTubes = returnData.registerOrder.testTubes || [];
            this.harmVal = returnData.occupationOrder.hazardInfo || [];
            this.testTubes = dataUtils.deepCopy(
              returnData.registerOrder.testTubes
            );
            this.disposeTestTubes(false);
            console.log(this.checkCombs);
          });
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 保存订单
    save() {
      const user = this.$refs.form_Ref;
      user.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          if (this.C_isOccupation && !this?.checkHarmList?.length > 0) {
            return this.$message.warning('职业检危害因素不能为空');
          }
          if (this.checkHarmList.some((item) => !item.hazardousName)) {
            return this.$message.warning('危害因素名称不能为空');
          }
          await user.dataURLtoFile(user.userInfo.photoUrl);
          await user.uploadImg();
          let userInfoCopy = dataUtils.deepCopy(user.userInfo);
          userInfoCopy.photoUrl =
            user.headPortraitImg || userInfoCopy.photoUrl || '';
          try {
            let photoUrl = new URL(userInfoCopy.photoUrl);
            userInfoCopy.photoUrl = photoUrl.pathname;
          } catch (error) {
            console.log(error);
          }
          userInfoCopy.jobStatus =
            userInfoCopy.jobStatus === null
              ? ''
              : userInfoCopy.jobStatus.toString();
          userInfoCopy.monitoringType =
            userInfoCopy.monitoringType == '' ? 0 : userInfoCopy.monitoringType;
          if (this.G_config.physicalMode.includes('职检')) {
            let peClsIdx = userInfoCopy.peCls.indexOf('职业检');
            if (peClsIdx != -1) {
              userInfoCopy.peCls.splice(
                userInfoCopy.peCls.indexOf('职业检'),
                1
              );
            }
            userInfoCopy.peCls = userInfoCopy.peCls[0];
          }
          if (this.G_config.register.unitCustomization) {
            if (
              !user.companyList.some(
                (item) => item.companyCode == userInfoCopy.companyCode
              )
            ) {
              userInfoCopy.companyName = userInfoCopy.companyCode;
              userInfoCopy.companyCode = '';
              userInfoCopy.companyTimes = null;
            }
            if (
              !user.companyDeptList.some(
                (item) => item.companyCode == userInfoCopy.companyDeptCode
              )
            ) {
              userInfoCopy.companyDeptName = userInfoCopy.companyDeptCode;
              userInfoCopy.companyDeptCode = '';
            }
          }
          const handleConfirm = () => {
            return new Promise((resolve, reject) => {
              if (!userInfoCopy.isActive) {
                this.$confirm('是否立即激活?', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                })
                  .then(() => {
                    userInfoCopy.isActive = true;
                    resolve();
                  })
                  .catch(() => {
                    resolve();
                  });
              } else {
                resolve();
              }
            });
          };
          handleConfirm().then(() => {
            let datas = {
              registerOrder: {
                patient: {
                  ...userInfoCopy,
                  operatorCode: this.G_userInfo.codeOper.operatorCode
                },
                clusters: this.checkMeal,
                combs: this.checkCombs,
                testTubes: this.testTubes
              },
              occupationOrder: {
                ...userInfoCopy,
                hazardInfo: this.checkHarmList
                // jobStatus: !jobStatus && jobStatus != 0? null : jobStatus
              }
            };
            if (!this.G_config?.physicalMode.includes('职检')) {
              delete datas.occupationOrder;
            }
            console.log(datas);
            this.$ajax
              .post(this.$apiUrls.SaveRegisterOrderNew + '/Company', datas)
              .then((r) => {
                let { success, returnData } = r.data;
                if (!success) return;
                this.$message({
                  message: '保存成功',
                  type: 'success',
                  showClose: true
                });
                // this.getUserList();
                if (this.G_config.register.unitCustomization) user.getCompany();
                this.userListRowDblclick(returnData);
              });
          });
        } else {
          return false;
        }
      });
    },
    // 显示用户队列
    showUserList() {
      this.showUserListFlag = true;
    },
    //设置tabs高度 防止高度异常
    setselectMealHeight() {
      this.selectMealHeight = this.$refs.selectMeal.clientHeight;
    },
    //获取单位下拉
    getCompany() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.userQueue.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    //清空单位时,清空次数,部门,初始化套餐
    clearAll() {
      this.userQueue.companyDeptList = [];
      this.userQueue.companyDeptCode = '';
      this.userQueue.clusterList = dataUtils.deepCopy(
        this.userQueue.clusterList
      );
    },
    //用户队列单位下拉变化
    companyChange(data) {
      this.companyTimesChange(data);
      if (!data || data.length === 0) {
        this.userQueue.isHavue = true;
        this.clearAll();
        this.getUserList();
        return;
      }
      this.getDepartList(data);
      this.getUserList();
      this.userQueue.isHavue = false;
    },
    //查询公司部门信息
    getDepartList(companyCode) {
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanyDepartment, {
          companyCode: companyCode?.[0] || '',
          deptCode: '',
          deptName: ''
        })
        .then((r) => {
          //console.log("r", r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.userQueue.companyDeptList = returnData || [];
        });
    },
    //获取套餐：
    getCluster() {
      this.$ajax.post(this.$apiUrls.Cluster).then((r) => {
        this.userQueue.clusterList = r?.data?.returnData || [];
        this.userQueue.clusterLists = r?.data?.returnData || [];
      });
    },
    //次数改变修改套餐
    companyTimesChange(data) {
      this.userQueue.clusCode = '';
      if (!data || data.length === 0) {
        this.userQueue.clusterList = dataUtils.deepCopy(
          this.userQueue.clusterLists
        );
        return;
      }
      this.$ajax
        .post(this.$apiUrls.ReadCompanyCandidateCluster, '', {
          query: {
            companyCode: data[0],
            companyTimes: data[1]
          }
        })
        .then(async (r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          let companyMeal = returnData || [];
          this.userQueue.clusterList = companyMeal;
        });
    },
    //用户队列请求成功回调
    successfulCallback(res) {
      this.userQueue.total = res?.totalNumber || 0;
    },
    tableRowClassName({ row, rowIndex }) {
      if (this.containComboCode.includes(row.combCode.trim())) {
        return 'have_meal';
      } else {
        return 'no_meal';
      }
    },
    userListShow() {
      if (this.userListIsGet) {
        this.getUserList();
        this.userListIsGet = false;
      }
      this.$refs.userTable_Refs.$refs.tableCom_Ref.bodyWrapper.scrollTop =
        this.userListScrollTop;
    },
    groupMealDBClick(row) {
      if (!this.$refs.form_Ref?.userInfo.companyCode) {
        return this.$message({
          message: '请选择单位!',
          type: 'warning',
          showClose: true
        });
      }
      this.isCancelRefundsBtnShow
        ? this.$message.warning('已选组合中存在退款申请!')
        : this.mealDBClick(row);
    },
    groupCombDbClick(row) {
      if (!this.$refs.form_Ref?.userInfo.companyCode) {
        return this.$message({
          message: '请选择单位!',
          type: 'warning',
          showClose: true
        });
      }
      this.isCancelRefundsBtnShow
        ? this.$message.warning('已选组合中存在退款申请!')
        : this.combDbClick(row);
    },
    groupCopyCombination() {
      if (!this.$refs.form_Ref?.userInfo.companyCode) {
        return this.$message({
          message: '请选择单位!',
          type: 'warning',
          showClose: true
        });
      }
      this.copyCombination();
    },
    groupAddMealBtn() {
      if (!this.$refs.form_Ref?.userInfo.companyCode) {
        return this.$message({
          message: '请选择单位!',
          type: 'warning',
          showClose: true
        });
      }
      this.isCancelRefundsBtnShow
        ? this.$message.warning('已选组合中存在退款申请!')
        : this.addMealBtn();
    }
  },
  mounted() {
    this.getUserList();
    this.mealDialogOpen();
    this.getCompany();
    this.getCluster();
    // if (!this.C_isOccupations) {
    //   this.setselectMealHeight()
    // window.addEventListener('resize', this.setselectMealHeight);
    // }
    this.$refs.userTable_Refs.$refs.tableCom_Ref.bodyWrapper.addEventListener(
      'scroll',
      (res) => {
        this.userListScrollTop = res.target.scrollTop;
      },
      true
    );
  },
  beforeDestroy() {
    // if (!this.C_isOccupations)
    // 组件销毁前移除事件监听
    // window.removeEventListener('resize', this.setselectMealHeight);
  },
  watch: {
    G_config: {
      handler(val) {
        this.params.isOccupation = val.physicalMode.includes('职检');
      },
      immediate: true,
      deep: true
    }
  }
};
</script>

<style scoped lang="less">
/* ===== 主页面布局 ===== */
.group-register-page {
  /* 使用CSS Grid布局 - 左右两栏结构 */
  display: grid;
  grid-template-columns: 400px 1fr; /* 左侧固定宽度，右侧自适应 */
  gap: 16px;
  height: 100vh;
  padding: 16px;
  background: #f5f7fa;

  /* 响应式设计 */
  @media (max-width: 1500px) {
    grid-template-columns: 1fr; /* 小屏幕改为单列 */

    .user-list-section {
      display: none; /* 隐藏左侧面板 */
    }
  }
}

/* ===== 左侧人员列表区域 ===== */
.user-list-section {
  display: grid;
  grid-template-rows: 1fr;
  overflow: hidden;

  .filter-card {
    display: grid;
    grid-template-rows: auto 1fr;
    height: 100%;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .card-header {
      padding: 16px 20px;
      border-bottom: 1px solid #e8eaec;
      background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
      border-radius: 8px 8px 0 0;

      .card-title {
        color: #ffffff;
        font-size: 16px;
        font-weight: 600;
      }
    }

    /* 筛选表单布局 */
    .filter-form {
      display: grid;
      grid-template-rows: repeat(auto-fit, auto);
      gap: 16px;
      padding: 20px;
      overflow-y: auto;

      .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
        align-items: end;

        &.time-filter {
          grid-template-columns: 100px 1fr;
        }

        .form-item {
          display: grid;
          grid-template-rows: auto 1fr;
          gap: 8px;

          .form-label {
            font-size: 14px;
            color: #606266;
            font-weight: 500;
            margin: 0;
          }

          &.date-range {
            grid-column: 1 / -1;
          }
        }
      }

      /* 记录统计样式 */
      .record-count {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        background: #f8f9fa;
        border-radius: 6px;
        border-left: 4px solid #409eff;

        .count-label {
          font-size: 14px;
          color: #606266;
          margin-right: 8px;
        }

        .count-value {
          font-size: 16px;
          color: #409eff;
          font-weight: 600;
        }
      }
    }

    /* 表格容器 */
    .user-table-container {
      display: grid;
      grid-template-rows: 1fr;
      overflow: hidden;
      margin-top: 16px;

      /* 表格内姓名单元格样式 */
      .name-cell {
        display: flex;
        align-items: center;
        gap: 8px;

        .gender-icon {
          border-radius: 50%;
          padding: 2px;
          font-size: 12px;

          &.el-icon-male {
            background: #409eff;
            color: #ffffff;
          }

          &.el-icon-female {
            background: #f56c6c;
            color: #ffffff;
          }
        }

        .name-text {
          font-weight: 500;
        }
      }
    }
  }
}
  .user_tab {
    li {
      display: flex;
      align-items: center;
      margin-bottom: 5px;
      label {
        margin-right: 10px;
      }
    }
  }
  .right_wrap {
    background: #fff;
    border-radius: 4px;
    flex: 1;
    flex-shrink: 0;
    padding: 0 5px 5px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .global_head {
      overflow: auto;
      .idCardBtn_wrap {
        min-width: fit-content;
      }
      background-color: #fff;
      // margin-bottom: 18px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .selectMeal_wrap {
      position: relative;
      z-index: 2;
      flex: 1;
      display: flex;
      background: #fff;
      overflow: auto;
      .candidateCombs_wrap {
        width: 490px;
        margin-right: 5px;
      }
      .hagard_wrap,
      .meal_wrap,
      .meal-common {
        border: 1px solid rgba(178, 190, 195, 1);
        border-radius: 4px;
        display: flex;
        flex-direction: column;
        overflow: auto;
        position: relative;
        height: 100%;
        header {
          background: #d1e2f9;
          padding: 5px;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .table_wrap {
          flex: 1;
          flex-shrink: 0;
          overflow: auto;
          .harm_date {
            /deep/input {
              padding: 0 5px;
              text-align: center;
            }
          }
          .harmAge_div {
            /deep/ .el-input__inner {
              padding: 0 0 !important;
              border: unset;
              // border-bottom: 1px solid #a3a4ab;
              border-bottom-left-radius: 0;
              border-bottom-right-radius: 0;
              text-align: center;
            }
            display: flex;
            align-items: center;
            border: 1px solid rgba(204, 204, 204, 1);
            border-radius: 2px;
            padding: 0 5px;
          }
          /deep/.price_color {
            color: red;
          }
          /deep/.combs_table {
            .el-table__body-wrapper {
              position: relative;
              .el-table__append-wrapper {
                position: sticky;
                left: 0;
                bottom: 0;
                width: 100%;
                z-index: 2;
              }
            }
            .el-input-number--mini {
              width: 100%;
            }
            .el-input__inner {
              padding-left: 0 !important;
              padding-right: 32px !important;
            }
          }
        }
      }
      .hagard_wrap {
        width: 40%;
        margin-right: 5px;
        header {
          height: 40px;
          overflow: hidden;
        }
      }

      .meal-common {
        width: 40%;
        margin-right: 5px;
        // padding: ;
        box-sizing: border-box;
        border-radius: 3px;
        /deep/.el-tabs__content {
          flex: 1 !important;
          //   overflow-y: auto;
        }
        /deep/.el-tabs__nav-scroll {
          background: #d1e2f9;
          height: 40px;
        }

        .bs-meal {
          height: 100%;
          display: flex;
          flex-direction: column;
        }
        .dialog_table {
          flex-grow: 1;
          overflow: auto;
        }
        .bs-combination {
          height: 100%;
          display: flex;
          flex-direction: column;
          .combination-search {
            display: flex;
            box-sizing: border-box;
            padding: 0 10px;
          }
        }
      }
      .meal_wrap {
        flex: 1;
        flex-shrink: 0;
        .left_div {
          display: flex;
          // align-items: center;
          height: 100%;
          overflow-y: auto;
          .job_sign {
            font-style: normal;
            border-radius: 100%;
            background: #1770df;
            width: 15px;
            height: 15px;
            color: #fff;
            display: inline-block;
            vertical-align: middle;
            line-height: 15px;
            text-align: center;
            font-size: 11px;
          }
        }
        header {
          height: 40px;
          overflow: hidden;
        }
      }
    }
    .totalMoney_wrap {
      background: #e7f0fb;
      padding: 10px;
      border-radius: 4px;
      position: sticky;
      bottom: 0;
      left: 0;
      width: 100%;
      ul {
        display: flex;
        font-size: 14px;
        color: #050505;
        font-weight: 600;
        flex-wrap: wrap;
        li + li {
          margin-left: 10px;
        }
        .money_color {
          color: #d63031;
        }
        .total_money {
          font-size: 18px;
          text-align: right;
          flex: 1;
          flex-shrink: 0;
        }
      }
    }
  }
  /deep/.meal_dialog {
    .select_icon {
      color: #1770df;
    }
    .group_sign {
      border-radius: 100%;
      height: 16px;
      width: 16px;
      display: inline-block;
      background: #1770df;
      font-style: normal;
      vertical-align: middle;
      text-align: center;
      line-height: 16px;
      color: #fff;
      font-size: 12px;
    }
    .meal_content {
      height: 500px;
      .el-tabs {
        height: 100%;
        display: flex;
        flex-direction: column;
      }
      .el-tabs__content {
        flex: 1;
        flex-shrink: 0;
      }
      .el-tab-pane {
        height: 100%;
        overflow: auto;
        display: flex;
        flex-direction: column;
      }
      .dialog_table {
        flex: 1;
        flex-shrink: 0;
        overflow: auto;
        .select_icon {
          color: #1770df;
        }
        .my-list-item {
          padding: 10px;
          font-size: 16px;
          border-bottom: 1px solid #dcdfe6;
          display: flex;
          align-items: center;
          cursor: pointer;
          span {
            width: 50px;
          }
          i {
            width: 30px;
          }
        }
      }
      .el-tabs__header {
        margin-bottom: 5px;
      }
      .comb_search {
        display: flex;
      }
    }
    .harm_content {
      display: flex;
      flex-direction: column;
      overflow: auto;
    }
    .el-dialog__body {
      padding: 10px 20px;
    }
    .el-tabs__item {
      font-size: 16px;
      // color: #2B3436;
      // font-weight: 600;
    }
  }
}
/deep/.el-tabs__item {
  font-size: 16px !important;
  font-weight: inherit !important;
}
/deep/ .have_meal {
  background: rgba(115, 100, 244, 0.1) !important;
}
/deep/ .no_meal {
  background: rgba(255, 255, 255) !important;
}
.prohibitClicking {
  pointer-events: none;
}
/deep/.radio_group {
  label {
    margin-right: 15px;
  }
}
.left_wrap {
  background: #fff;
  width: 400px;
  height: 100%;
  border-radius: 4px;
  margin-right: 5px;
  display: flex;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
  // flex-direction: column;
  .left_tab {
    .sex_wrap {
      .el-icon-male {
        color: #037bff;
        background: none;
      }
      .el-icon-female {
        color: #fc73a7;
        background: none;
      }
    }
    //   .open-btn{
    //     // display: block;
    //     position: absolute;
    //     cursor: pointer;
    //     left: 460px;
    //     top: 320px;
    //     box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
    //     border-radius: 3px;
    //     padding: 5px 5px 5px 0;
    // }
    overflow: auto;
    flex: 1;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding: 8px;
    width: 100%;
    li {
      display: flex;
      align-content: center;
      justify-content: space-between;
      margin-bottom: 5px;
      span {
        white-space: nowrap;
        margin-right: 7px;
        display: flex;
        align-items: center;
        font-size: 14px;
      }
      & > div:nth-child(2) {
        margin-left: 10px;
      }
      & > div {
        display: flex;
        align-content: center;
        justify-content: space-around;
      }
    }
  }
  /deep/.el-tab-pane {
    height: 100%;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  /deep/.el-tabs__header {
    background: rgba(23, 112, 223, 0.2);
    .is-active {
      color: #2d3436;
    }
  }
  /deep/.el-tabs__item {
    font-size: 18px;
    font-weight: 500;
    height: 48px;
    line-height: 48px;
  }
  /deep/.el-tabs__content {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    padding: 5px;
  }
}
.hazar-item {
  .item__input {
    display: none;
    // width: 100px;
  }
  .item__txt {
    box-sizing: border-box;
    line-height: 24px;
    padding: 0 9px;
  }
}
//修改禁用状态复选框 √ 颜色
/deep/ .el-checkbox .is-disabled .el-checkbox__inner::after {
  border-color: #409eff;
}
/deep/.dialogCss {
  .el-dialog__body {
    padding: 0 15px !important;
  }
}
@import url(./css/media.less);
.questionnaire-disabled {
  opacity: 0.5;
}
</style>
