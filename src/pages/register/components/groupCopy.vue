<template>
  <el-dialog
    title="复制"
    :visible.sync="centerDialogVisible"
    width="1080px"
    :close-on-click-modal="false"
    class="main"
  >
    <div class="head">
      <el-input
        size="small"
        placeholder="体检号"
        style="width: 200px"
        auto-complete
        v-model.trim="form.regNo"
      ></el-input>

      <el-select
        class="select"
        placeholder="体检单位"
        size="small"
        width="100%"
        filterable
        v-model="form.companyCode"
        @change="search"
        :filter-method="filterMethod"
        @visible-change="companyVisibleChange"
        clearable
      >
        <el-option
          v-for="(item, index) in companyCodeList"
          :key="index"
          :value="item.companyCode"
          :label="item.companyName"
        ></el-option>
      </el-select>
      <div class="time">
        <span>登记时间</span>
        <el-date-picker
          :picker-options="{ shortcuts: G_datePickerShortcuts }"
          @change="search"
          type="daterange"
          value-format="yyyy-MM-dd"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          class="time-picker"
          :clearable="false"
          v-model.trim="time"
        >
        </el-date-picker>
      </div>
      <el-button
        size="small"
        style="width: 80px"
        type="primary"
        icon="el-icon-search"
        @click="search"
        >搜索</el-button
      >
    </div>
    <div class="tab">
      <PublicTabel
        :theads="theads"
        :params="form"
        ref="copyTable_Ref"
        rowKey="regNo"
        remoteByPage
        :url="$apiUrls.ReadCopyPersonList"
        :elStyle="{ 'show-selection': false }"
        style="width: 100%"
        @request-finally="requestFinally"
      >
        <template #sex="{ scope }">
          <div class="sex_wrap">
            <img v-if="scope.row.sex == 1" src="@/assets/img/nan.png" alt="" />
            <img v-if="scope.row.sex == 2" src="@/assets/img/nv.png" alt="" />
            {{ G_EnumList['Sex'][scope.row.sex] }}
          </div>
        </template>
        <template #edit="{ scope }">
          <el-popover placement="left" trigger="hover">
            <el-button
              class=""
              type="text"
              style="padding: 10px"
              size="mini"
              @click="copyPaste(scope.row, true)"
              >套用（全部）</el-button
            >
            <el-button
              class=""
              type="text"
              style="padding: 10px"
              size="mini"
              @click="copyPaste(scope.row, false)"
              >套用（不包含套餐号）</el-button
            >
            <el-button
              class="custom-button"
              size="mini"
              type="text"
              slot="reference"
              >套用</el-button
            >
          </el-popover>

          <el-button
            class="custom-button"
            size="mini"
            style="margin-left: 10px"
            @click="check(scope.row)"
            type="text"
            >查看</el-button
          >
        </template>
      </PublicTabel>
    </div>

    <el-dialog
      width="50%"
      :title="title"
      :visible.sync="innerVisible"
      append-to-body
    >
      <div style="height: 400px; overflow: auto">
        <el-table :data="combDetailList" size="small">
          <el-table-column
            property="value"
            label="项目代码"
            width="100"
          ></el-table-column>
          <el-table-column property="label" label="项目名称"></el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex';
import PublicTabel from '@/components/publicTable2';
export default {
  name: 'groupCopy',
  props: {
    isOccupation: {
      type: Boolean,
      default: false
    },
    isCompanyCheck: {
      type: Boolean,
      default: false,
      required: true
    }
  },
  components: { PublicTabel },
  computed: {
    ...mapGetters(['G_EnumList', 'G_datePickerShortcuts'])
  },
  data() {
    return {
      innerVisible: false,
      centerDialogVisible: false,
      title: '',
      regNo: '',
      theads: [
        {
          label: '性别',
          prop: 'sex',
          width: 70
        },
        {
          label: '姓名',
          prop: 'name'
        },
        {
          label: '年龄',
          prop: 'age',
          width: 60
        },
        {
          label: '体检单位',
          prop: 'companyName'
        },
        {
          label: '体检套餐',
          prop: 'clusName'
        },
        {
          label: '操作',
          prop: 'edit',
          width: 90
        }
      ],
      form: {
        // regNo: "", //体检号
        // companyCode: "", //体检单位
        // startTime: "", //开始时间
        // endTime: "", //结束时间
        // sex: -1,
        // queryType: 2,

        // keyword: "",
        regNo: '',
        companyCode: '', //体检单位
        beginDate: '',
        endDate: '',
        isOccupation: false
      },
      time: [
        new Date().toISOString().slice(0, 10),
        new Date().toISOString().slice(0, 10)
      ],
      companyCodeList: [], //体检单位列表
      companyCodeListCopy: [],
      combDetailList: []
    };
  },
  methods: {
    filterMethod(val) {
      this.form.companyCode = val;
      if (val.trim() == '') {
        this.companyCodeList = JSON.parse(
          JSON.stringify(this.companyCodeListCopy)
        );
      } else {
        this.companyCodeList = this.companyCodeListCopy.filter(
          (item) =>
            item.companyName.includes(val) || item.companyCode.includes(val)
        );
      }
    },
    companyVisibleChange(data) {
      if (data)
        this.companyCodeList = JSON.parse(
          JSON.stringify(this.companyCodeListCopy)
        );
    },
    //查询
    search() {
      this.form.beginDate = this.time[0];
      this.form.endDate = this.time[1];
      this.form.isOccupation = this.isOccupation;
      this.form.isCompanyCheck = this.isCompanyCheck;
      this.$refs.copyTable_Ref.loadData();
    },
    //请求成功回调
    requestFinally() {
      this.$refs.copyTable_Ref.tableData =
        this.$refs.copyTable_Ref.tableData.filter(
          (item) => item.regNo != this.regNo
        );
    },
    //获取套餐组合数据
    async getPackageCombination(regNo) {
      const response = await this.$ajax.paramsPost(
        this.$apiUrls.ReadRegisterClusterComb,
        { regNo }
      );
      const { success, returnData } = response.data;
      if (success) {
        return returnData;
      }
      return null;
    },
    //套用
    async copyPaste(row, onlyCombination) {
      const res = await this.getPackageCombination(row.regNo);
      this.$parent.applyDate(res, onlyCombination);
      this.centerDialogVisible = false;
    },

    //查看
    async check(row) {
      const res = await this.getPackageCombination(row.regNo);
      this.combDetailList = [];
      if (res?.combs.length === 0 || !res?.combs) return;
      Object.keys(res.combs).map((item) => {
        this.combDetailList.push({
          label: res.combs[item],
          value: item
        });
      });
      this.title = row.name;
      this.innerVisible = true;
    }
  },
  created() {
    this.$bus.$on('companyDate', (data) => {
      this.companyCodeList = [...data];
      this.companyCodeListCopy = JSON.parse(JSON.stringify(data));
    });
  },
  mounted() {},
  beforeCreate() {
    this.$bus.$off('companyDate');
  },
  watch: {
    centerDialogVisible: {
      handler(newVal) {
        if (newVal)
          this.$nextTick(() => {
            this.search();
          });
      }
    }
  }
};
</script>
<style lang="less" scoped>
.main {
  // height: 700px;
  .head {
    display: flex;
    height: 32px;
    align-items: center;
    .time {
      .time-picker {
        width: 260px;
        margin: 0 10px;
      }
    }
    .select {
      margin: 0 10px;
      width: 350px;
    }
    & > {
      height: 32px;
    }
  }
  .tab {
    height: 408px;
    margin-top: 16px;
    .tab-table {
      border: 0.5px solid rgba(178, 190, 195, 1);
      border-radius: 4px;
      /deep/.el-table__body-wrapper.is-scrolling-none {
        height: 100%;
      }
    }

    .custom-button {
      color: #1770df !important; /* 设置文字颜色 */
      background: none !important; /* 设置背景为透明 */
    }
  }
}
</style>
