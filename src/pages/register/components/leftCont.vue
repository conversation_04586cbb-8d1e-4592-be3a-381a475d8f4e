<template>
  <div class="leftCont">
    <div class="left_header">
      <header>
        <h3>个人信息：</h3>
        <div class="left_search">
          <div class="left_search_div" style="display: flex">
            <el-input
              v-model.trim="leftSearchVal"
              size="small"
              placeholder="体检号/档案号/姓名/证件号/电话"
              @keyup.enter.native.stop="search"
              clearable
            ></el-input>
          </div>
          <el-button
            size="small"
            class="blue_btn"
            icon="iconfont icon-search"
            style="padding: 2px"
            @click="search"
            >记录搜索</el-button
          >
          <el-button
            size="small"
            class="violet_btn"
            icon="iconfont icon-huifuxitongmoren"
            style="padding: 2px"
            @click="updateReg"
            >更新</el-button
          >
        </div>
      </header>
    </div>
    <div class="right_form">
      <el-form ref="form" :model="searchForm" label-width="80px" :rules="rules">
        <el-row v-if="isShow">
          <el-col>
            <el-form-item
              label="工作单位"
              label-width="90px"
              prop="companyCode"
            >
              <el-select
                class="select"
                v-model.trim="searchForm.companyCode"
                :filter-method="filterMethod"
                @visible-change="companyVisibleChange"
                placeholder="请选择"
                size="small"
                filterable
                @change="getComTimes"
                @keyup.enter.native.stop
              >
                <el-option
                  v-for="(item, index) in companyList"
                  :key="index"
                  :label="item.companyName"
                  :value="item.companyCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row style="display: flex">
          <el-col :span="6" style="text-align: center">
            <div
              class="demo-image__preview"
              @click="clipImg"
              style="overflow: hidden; height: 140px; width: 112px"
            >
              <video
                v-show="videoShow"
                ref="video"
                id="video"
                width="112"
                height="140"
                x5-video-player-fullscreen="true"
                x5-video-orientation="portraint"
              ></video>
              <el-image
                style="width: 100%; height: 100%"
                :src="searchForm.photoUrl"
                v-show="!videoShow"
              >
              </el-image>
            </div>
            <div style="background: #1770df; width: 112px; cursor: pointer">
              <span
                type="text"
                size="small"
                class="search-btn"
                @click.stop="take_a_picture"
                style="
                  color: #fff;
                  text-align: center;
                  vertical-align: middle;
                  line-height: 24px;
                  padding: 0;
                "
                >拍照</span
              >
              <canvas
                ref="canvas"
                id="canvas"
                width="200"
                height="250"
                style="display: none"
              ></canvas>
            </div>
          </el-col>
          <el-col>
            <div>
              <el-row>
                <el-col v-if="isShow">
                  <el-form-item
                    label="单位次数"
                    label-width="90px"
                    prop="companyTimes"
                  >
                    <el-select
                      v-model.trim="searchForm.companyTimes"
                      placeholder="请选择"
                      size="small"
                      clearable
                      @change="timesChange"
                      filterable
                      @keyup.enter.native.stop
                    >
                      <el-option
                        v-for="(item, index) in comTimesList"
                        :key="index"
                        :value="item.value"
                        :label="item.label"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-form-item
                    label="体检分类"
                    label-width="90px"
                    prop="peCls"
                  >
                    <el-select
                      class="select"
                      v-model.trim="searchForm.peCls"
                      placeholder="请选择"
                      size="small"
                      width="100%"
                      clearable
                      filterable
                      :disabled="isShow"
                    >
                      <el-option
                        v-for="(item, index) in G_peClsList"
                        :key="index"
                        :value="item.value"
                        :label="item.label"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col>
                  <el-form-item
                    label="档案号"
                    label-width="90px"
                    prop="patCode"
                  >
                    <el-input
                      v-model.trim="searchForm.patCode"
                      size="small"
                      placeholder="请输入"
                      disabled
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col>
                  <el-form-item label="体检号" label-width="90px" prop="regNo">
                    <el-input
                      v-model.trim="searchForm.regNo"
                      size="small"
                      placeholder="请输入"
                      disabled
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col>
                  <el-form-item
                    label="诊疗卡号"
                    label-width="90px"
                    prop="hisCard"
                  >
                    <el-input
                      v-model.trim="searchForm.hisCard"
                      size="small"
                      placeholder="请输入"
                      @keypress.enter.native="hisCardEnter"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
        <!-- <el-row>
         <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input
                v-model.trim="searchForm.name"
                size="small"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="体检次数" prop="tjTimes">
              <el-input
                v-model.trim="searchForm.tjTimes"
                size="small"
                placeholder="请输入"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row> -->
        <el-row>
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input
                v-model.trim="searchForm.name"
                size="small"
                placeholder="请输入"
                clearable
                minlength="2"
                @keyup.enter.native.stop="searchName"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别" prop="sex">
              <el-select
                class="select"
                v-model.trim="searchForm.sex"
                @change="sexChange"
                placeholder="请选择"
                size="small"
                width="100%"
                @keyup.enter.native.stop
              >
                <el-option
                  v-for="(item, index) in G_sexList"
                  :key="index"
                  :value="item.value"
                  :label="item.label"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="ageForm">
          <el-col>
            <el-form-item label="年龄" prop="age">
              <el-input
                v-model.trim="searchForm.age"
                size="small"
                placeholder="请输入"
                onkeyup="this.value=this.value.replace(/\D|^/g,'')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="年龄单位" prop="ageUnit">
              <el-select
                class="select"
                v-model.trim="searchForm.ageUnit"
                placeholder="岁(月)"
                size="small"
                width="100%"
                @keyup.enter.native.stop
              >
                <el-option
                  v-for="(item, index) in G_ageUnit"
                  :key="index"
                  :value="item.value"
                  :label="item.label"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="出生日期" prop="birthday" label-width="90px">
              <el-date-picker
                v-model.trim="searchForm.birthday"
                type="date"
                placeholder="选择日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                @change="changeBirthday"
                size="small"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="籍贯" prop="nativePlace">
              <el-select
                class="select"
                v-model.trim="searchForm.nativePlace"
                placeholder="请选择"
                size="small"
                width="100%"
                clearable
                filterable
                @keyup.enter.native.stop
              >
                <el-option
                  v-for="(item, index) in nativePlaceList"
                  :key="index"
                  :value="item.natCode"
                  :label="item.natName"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="婚姻状况" prop="marryStatus">
              <el-select
                class="select"
                v-model.trim="searchForm.marryStatus"
                placeholder="请选择"
                size="small"
                width="100%"
                clearable
                @keyup.enter.native.stop
              >
                <el-option
                  v-for="(item, index) in G_marriageStatus"
                  :key="index"
                  :value="item.value"
                  :label="item.label"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="证件类型" prop="cardType">
              <el-select
                class="select"
                v-model.trim="searchForm.cardType"
                placeholder="请选择"
                size="small"
                width="100%"
                clearable
                filterable
                @keyup.enter.native.stop
              >
                <el-option
                  v-for="(item, index) in G_cardType"
                  :key="index"
                  :value="item.value"
                  :label="item.label"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="证件号码" prop="cardNo" label-width="90px">
              <el-input
                v-model.trim="searchForm.cardNo"
                size="small"
                placeholder="请输入"
                clearable
                @keyup.enter.native.stop="searchCard"
                @blur="blurCardNo"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="联系电话" prop="tel" label-width="90px">
              <el-input
                v-model.trim="searchForm.tel"
                size="small"
                placeholder="请输入"
                clearable
                onkeyup="this.value=this.value.replace(/\D|^/g,'')"
                @keyup.enter.native.stop="searchTel"
                :maxlength="11"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="联系地址" prop="address">
              <el-input
                v-model.trim="searchForm.address"
                size="small"
                placeholder="请输入"
                clearable
                @keyup.enter.native.stop
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="isShow">
          <el-col>
            <el-form-item label="部门" prop="companyDeptCode">
              <el-select
                class="select"
                v-model.trim="searchForm.companyDeptCode"
                placeholder="请选择"
                size="small"
                filterable
                clearable
              >
                <el-option
                  v-for="(item, index) in companyDeptList"
                  :key="index"
                  :label="item.deptName"
                  :value="item.deptCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="工种" prop="jobCode">
              <el-select
                class="select"
                v-model.trim="searchForm.jobCode"
                placeholder="请选择"
                size="small"
                width="100%"
                clearable
                filterable
                @keyup.enter.native.stop
              >
                <el-option
                  v-for="(item, index) in jobList"
                  :key="index"
                  :value="item.jobCode"
                  :label="item.jobName"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="介绍人" prop="introducer">
              <el-select
                class="select"
                v-model.trim="searchForm.introducer"
                placeholder="请选择"
                size="small"
                width="100%"
                clearable
                filterable
                @keyup.enter.native.stop
              >
                <el-option
                  v-for="(item, index) in operatorList"
                  :key="index"
                  :value="item.operatorCode"
                  :label="item.name"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="既往病史" prop="medicalHistory">
              <el-input
                v-model.trim="searchForm.medicalHistory"
                size="small"
                placeholder="请输入"
                clearable
                @keyup.enter.native.stop
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="操作员" prop="operatorCode">
              <el-input
                v-model.trim="searchForm.operatorCode"
                size="small"
                placeholder="请输入"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row style="margin-bottom: 10px">
          <el-col class="checkbox">
            <!-- <el-checkbox-group
              v-model.trim="checkedVal"
              @change="handleCheckedValChange"
            >
              <el-checkbox
                v-for="item in checkList"
                :label="item"
                :key="item"
                >{{ item }}</el-checkbox
              >
            </el-checkbox-group> -->
            <el-checkbox
              label="领导"
              v-model.trim="searchForm.isLeader"
            ></el-checkbox>
            <el-checkbox
              label="复查"
              v-model.trim="searchForm.isRecheck"
            ></el-checkbox>
            <el-checkbox
              label="VIP"
              v-model.trim="searchForm.isVIP"
            ></el-checkbox>
            <el-checkbox
              label="体质辨识"
              v-model.trim="searchForm.isConstitution"
            ></el-checkbox>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="复查号" prop="recheckNo">
              <el-input
                v-model.trim="searchForm.recheckNo"
                size="small"
                placeholder="请输入"
                clearable
                @keyup.enter.native.stop
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row>
          <el-col>
            <el-form-item label="体检费" prop="price">
              <el-input
                v-model.trim="searchForm.price"
                size="small"
                placeholder="请输入"
                class="redIpt"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="加项费" prop="surcharges">
              <el-input
                v-model.trim="searchForm.surcharges"
                size="small"
                placeholder="请输入"
                class="redIpt"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row> -->

        <el-row>
          <el-col>
            <el-form-item label="登记日期" prop="registerTime">
              <el-date-picker
                v-model.trim="searchForm.registerTime"
                type="date"
                placeholder="选择日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                disabled
                size="small"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row>
          <el-col>
            <el-form-item label="挂号号" prop="registrationNo">
              <el-input
                v-model.trim="searchForm.registrationNo"
                size="small"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row> -->
        <!-- <el-row>
          <el-col>
            <el-form-item label="生理周期" prop="physiological">
              <el-select
                class="select"
                v-model.trim="searchForm.physiological"
                placeholder="请选择"
                size="small"
                width="100%"
              >
                <el-option label="" value=""></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row> -->
        <el-row>
          <el-col>
            <el-form-item label="备注" prop="note">
              <el-input
                v-model.trim="searchForm.note"
                type="textarea"
                size="small"
                placeholder="请输入"
                @keyup.enter.native.stop
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row>
          <el-col class="isBlue">
            <el-checkbox v-model.trim="searchForm.checkStatus"
              >已询问流行病学史</el-checkbox
            >
          </el-col>
        </el-row> -->
      </el-form>
    </div>
    <!-- 拍照裁剪图片抽屉 -->
    <el-drawer
      title="照片编辑"
      :append-to-body="true"
      :visible.sync="clipDrawerShow"
      size="50%"
      @close="cancelClip"
      class="cropper_drawer"
    >
      <div style="padding: 10px; overflow: auto">
        <div class="preview_img" style="display: flex; overflow: auto">
          <div
            class="preview"
            style="
              margin-right: 10px;
              overflow: hidden;
              width: 200px;
              height: 250px;
              border: 1px solid #ccc;
            "
          >
            <el-image :src="previews.photoUrl" :style="previews.img"></el-image>
          </div>
          <div style="flex: 1; flex-shrink: 0">
            <video
              ref="drawerVideo"
              id="video"
              width="100%"
              height="250"
              x5-video-player-fullscreen="true"
              x5-video-orientation="portraint"
            ></video>
            <canvas
              ref="drawerCanvas"
              width="450"
              height="250"
              style="display: none"
            ></canvas>
          </div>
        </div>
        <div class="preview_btn" style="margin-bottom: 10px">
          <el-button
            size="small"
            class="blue_btn"
            icon="el-icon-camera-solid"
            @click="drawer_take_a_picture"
            >拍照</el-button
          >
          <el-button
            size="small"
            class="blue_btn"
            icon="iconfont icon-baocun"
            @click="confirmClip('blob')"
            >确定</el-button
          >
          <el-button size="small" @click="cancelClip">取消</el-button>
        </div>
        <div class="cropper">
          <vue-cropper
            ref="cropper"
            :img="option.img"
            :outputSize="option.outputSize"
            :outputType="option.outputType"
            :info="option.info"
            :canScale="option.canScale"
            :autoCrop="option.autoCrop"
            :autoCropWidth="option.autoCropWidth"
            :autoCropHeight="option.autoCropHeight"
            :fixed="option.fixed"
            :fixedNumber="option.fixedNumber"
            :full="option.full"
            :fixedBox="option.fixedBox"
            :canMove="option.canMove"
            :canMoveBox="option.canMoveBox"
            :original="option.original"
            :centerBox="option.centerBox"
            :height="option.height"
            :infoTrue="option.infoTrue"
            :maxImgSize="option.maxImgSize"
            :enlarge="option.enlarge"
            :mode="option.mode"
            @realTime="realTime"
            @imgLoad="imgLoad"
          >
          </vue-cropper>
        </div>
        <!--底部操作工具按钮-->
        <div class="footer-btn" style="margin-top: 10px">
          <div class="scope-btn">
            <label class="up_label blue_btn" for="uploads"
              ><i class="el-icon-upload"></i> 上传本地图片</label
            >
            <input
              type="file"
              id="uploads"
              ref="img_file"
              style="position: absolute; clip: rect(0 0 0 0)"
              accept="image/png, image/jpeg, image/gif, image/jpg"
              @change="selectImg($event)"
            />
            <el-button
              size="mini"
              type="danger"
              plain
              icon="el-icon-zoom-in"
              @click="changeScale(1)"
              >放大</el-button
            >
            <el-button
              size="mini"
              type="danger"
              plain
              icon="el-icon-zoom-out"
              @click="changeScale(-1)"
              >缩小</el-button
            >
            <el-button size="mini" type="danger" plain @click="rotateLeft"
              >↺ 左旋转</el-button
            >
            <el-button size="mini" type="danger" plain @click="rotateRight"
              >↻ 右旋转</el-button
            >
          </div>
          <!-- <div class="upload-btn">
            <el-button size="mini" type="success" @click="uploadImg('blob')">上传封面 <i class="el-icon-upload"></i></el-button>
          </div> -->
        </div>
      </div>
    </el-drawer>
    <el-dialog
      title="身份信息列表"
      :visible.sync="dialogTableVisible"
      append-to-body
      custom-class="dialogCss"
    >
      <PublicTable
        :viewTableList.sync="dialogList"
        :theads.sync="theads"
        :columnWidth="columnWidth"
        @currentChange="handleCurrentChange"
        :isSortShow="false"
        ref="patientTable"
        style="height: 320px"
        @rowDblclick="enterDbClick"
      >
        <template #sex="{ scope }">
          <div>
            {{ G_EnumList['Sex'][scope.row.sex] }}
          </div>
        </template>
        <template #marryStatus="{ scope }">
          <div>
            {{ G_EnumList['MarryStatus'][scope.row.marryStatus] }}
          </div>
        </template>
      </PublicTable>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogTableVisible = false" size="small"
          >取 消</el-button
        >
        <el-button type="primary" size="small" @click="saveName"
          >确 定</el-button
        >
      </div>
    </el-dialog>

    <!-- 搜索 -->
    <el-dialog
      title="身份信息列表"
      :visible.sync="dialogTable"
      append-to-body
      custom-class="dialogCss"
    >
      <PublicTable
        :viewTableList.sync="searchList"
        :theads.sync="dialogTheads"
        :columnWidth="columnWidth"
        @currentChange="handleCurrentChanges"
        @rowDblclick="searchDbClick"
        :isSortShow="false"
        ref="patientTable"
        style="height: 320px"
      >
        <template #peCls="{ scope }">
          <div>
            {{ G_EnumList['PeCls'][scope.row.peCls] }}
          </div>
        </template>
        <template #peStatus="{ scope }">
          <div>
            {{ G_EnumList['PeStatus'][scope.row.peStatus] }}
          </div>
        </template>
        <template #sex="{ scope }">
          <div>
            {{ G_EnumList['Sex'][scope.row.sex] }}
          </div>
        </template>
        <template #isActive="{ scope }">
          <div>
            <el-checkbox
              v-model.trim="scope.row.isActive"
              disabled
            ></el-checkbox>
          </div>
        </template>
        <template #group="{ scope }">
          <div>
            <el-checkbox v-model.trim="scope.row.group" disabled></el-checkbox>
          </div>
        </template>
        <template #isVIP="{ scope }">
          <div>
            <el-checkbox v-model.trim="scope.row.isVIP" disabled></el-checkbox>
          </div>
        </template>
        <template #isCompanyCheck="{ scope }">
          <div>
            <el-checkbox
              v-model.trim="scope.row.isCompanyCheck"
              disabled
            ></el-checkbox>
          </div>
        </template>
      </PublicTable>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogTable = false" size="small">取 消</el-button>
        <el-button type="primary" size="small" @click="saveSearch"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import Camera from '../mixins/camera';
import PublicTable from '../../../components/publicTable.vue';
import { dataUtils } from '../../../common';
export default {
  name: 'leftCont',
  mixins: [Camera],
  props: {
    // 是否显示工作单位
    isShow: {
      type: Boolean,
      default: () => {
        return false;
      }
    },
    disposeRecordData: {
      type: Function,
      default: null
    }
  },
  components: {
    PublicTable
  },
  data() {
    const checkAge = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入年龄'));
      } else {
        if (this.searchForm.ageUnit === 0) {
          let ageReg = /^((\d{1,2})|(1[0-4]\d)|(150))$/;
          if (!ageReg.test(value)) {
            callback(new Error('年龄格式不正确'));
          } else {
            callback();
          }
        } else {
          let ageReg = /^(1[0-2]|[1-9])$/;
          if (!ageReg.test(value)) {
            callback(new Error('月份格式不正确'));
          } else {
            callback();
          }
        }
      }
    };
    const checkNames = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入姓名'));
      } else {
        // this.searchName();
        callback();
        return;
      }
    };
    const checkSex = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请选择性别'));
      } else {
        callback();
        return;
      }
    };
    // 身份证校验
    const checkCardNo = (rule, value, callback) => {
      if (!value) {
        callback();
        return;
      } else if (value && this.searchForm.cardType === '1') {
        let cardNoReg =
          /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/;
        if (!cardNoReg.test(value)) {
          callback(new Error('身份证格式不正确'));
        } else {
          this.$nextTick(() => {
            // let { age, sex, birthday } = dataUtils.getByIdCard(value);
            // this.$set(this.searchForm, "age", age);
            // this.$set(this.searchForm, "sex", sex);
            // this.$set(this.searchForm, "birthday", birthday);
            //console.log(this.searchForm.sex, sex);
            // this.searchCard(); //查询
            callback();
            return;
          });
        }
      } else {
        callback();
        return;
      }
    };
    const checkCardTel = (rule, value, callback) => {
      if (!value) {
        callback();
        return;
      } else {
        let telReg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/;
        if (!telReg.test(value)) {
          callback(new Error('手机号格式不正确！'));
        } else {
          // this.searchTel();
          callback();
          return;
        }
      }
    };
    //通过生日获取年龄
    const checkB = (rule, value, callback) => {
      if (!value) {
        callback();
        return;
      } else {
        // var strBirthdayArr = dataUtils.interceptDate(value).split("-");
        // var birthYear = strBirthdayArr[0];
        // var d = new Date();
        // var nowYear = d.getFullYear();
        // if (nowYear == birthYear) {
        //   this.searchForm.ageUnit = 1;
        // } else {
        //   this.searchForm.ageUnit = 0;
        // }
        // this.$set(this.searchForm, "age", dataUtils.jsGetAge(value));
        callback();
      }
    };
    return {
      personOrCompany: '',
      leftSearchVal: '',
      dialogTable: false,
      isUpdate: false,
      tableInfo: '',
      dialogTableVisible: false,
      comTimesList: [], //单位次数组
      param: '',
      setData: '',
      theads: {
        patCode: '档案号',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        birthday: '出生日期',
        marryStatus: '婚姻状况',
        cardNo: '身份证',
        address: '地址',
        tel: '电话'
      },
      dialogList: [],
      dialogTheads: {
        peStatus: '状态',
        isActive: '激活',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        regNo: '体检号',
        patCode: '档案号',
        // cardNo: "身份证",
        isCompanyCheck: '团体',
        isVIP: 'VIP',
        peCls: '体检分类',
        companyCode: '工作单位',
        companyDeptCode: '部门'
      },
      searchList: [],
      columnWidth: {
        patCode: '160',
        cardNo: '200',
        address: '200',
        tel: '160',
        regNo: '160',
        companyCode: '200',
        companyDeptCode: '200',
        birthday: '170'
      },
      searchForm: {
        regNo: '',
        patCode: '',
        name: '',
        sex: null,
        age: '',
        ageUnit: 0,
        birthday: null,
        cardType: '1',
        cardNo: '',
        tel: '',
        nativePlace: '',
        address: '',
        marryStatus: null,
        photoUrl: '',
        registerTime: null,
        isActive: false,
        peCls: 3,
        guidanceType: '',
        reportType: '',
        companyCode: '',
        companyTimes: null, //单位次数
        companyDeptCode: '',
        // jobStatus: "",
        jobCode: '',
        jobHistory: '',
        medicalHistory: '',
        familyMedicalHistory: '',
        isVIP: false,
        isLeader: false,
        isConstitution: false,
        isRecheck: false,
        recheckNo: '',
        operatorCode: '',
        introducer: '',
        note: '',
        chargeModel: 1,
        hisCard: '' //诊疗卡号
        //price: "", //体检费暂无对应
        //surcharges: "", //加项费暂无对应
      },
      companyCode: '',
      companyList: [],
      companyListCopy: [],
      nativePlaceList: [],
      cardTypeList: [],
      jobList: [],
      operatorList: [],

      checkedVal: [],
      rules: {
        companyCode: [
          { required: true, message: '请选择工作单位', trigger: 'change' }
        ],
        companyTimes: [
          { required: true, message: '请选择单位次数', trigger: 'change' }
        ],
        peCls: [
          { required: true, message: '请输入体检分类', trigger: 'change' }
        ],
        name: [{ required: true, validator: checkNames, trigger: 'blur' }],
        sex: [{ required: true, validator: checkSex, trigger: 'change' }],
        age: [
          { required: true, validator: checkAge, trigger: 'blur' },
          { validator: checkAge, trigger: 'change' }
        ],
        birthday: [
          { required: true, message: '请选择出生日期', trigger: 'change' },
          { validator: checkB, trigger: 'change' }
        ],
        cardNo: [
          { required: true, message: '请输入证件号', trigger: 'blur' },
          { validator: checkCardNo, trigger: 'blur' }
        ],
        tel: [
          { required: true, message: '请输入联系电话', trigger: 'blur' },
          { validator: checkCardTel, trigger: 'blur' }
        ]
      },
      companyDeptList: []
    };
  },
  created() {},
  mounted: function () {
    this.getCompany();
    this.getNativePlace();
    this.getJob();
    this.getOperator();
  },
  watch: {
    'searchForm.sex': {
      deep: true,
      handler: function (newSex, oldSex) {
        if (newSex == oldSex) return;
        this.sexChange();
      }
    },
    'searchForm.regNo': {
      handler: function (n, o) {
        this.$parent.$parent.$parent.HisPatInfo = this.searchForm;
      },
      deep: true
    },
    'searchForm.companyCode': {
      handler(n, o) {
        this.getDepartList();
      },
      deep: true
    }
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_sexList',
      'G_peClsList',
      'G_marriageStatus',
      'G_cardType',
      'G_ageUnit'
    ])
  },
  methods: {
    filterMethod(val) {
      this.searchForm.companyCode = val;
      if (val.trim() == '') {
        this.companyList = dataUtils.deepCopy(this.companyListCopy);
      } else {
        this.companyList = this.companyListCopy.filter(
          (item) =>
            item.companyName.includes(val) || item.companyCode.includes(val)
        );
      }
    },
    companyVisibleChange(data) {
      if (data)
        this.companyList = JSON.parse(JSON.stringify(this.companyListCopy));
    },
    //查询公司部门信息
    getDepartList() {
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanyDepartment, {
          companyCode: this.searchForm.companyCode,
          deptCode: '',
          deptName: ''
        })
        .then((r) => {
          //console.log("r", r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.companyDeptList = returnData || [];
        });
    },
    //通过生日获取年龄
    changeBirthday() {
      var value = this.searchForm.birthday;
      var strBirthdayArr = dataUtils.interceptDate(value).split('-');
      var birthYear = strBirthdayArr[0];
      var d = new Date();
      var nowYear = d.getFullYear();
      if (nowYear == birthYear) {
        this.searchForm.ageUnit = 1;
      } else {
        this.searchForm.ageUnit = 0;
      }
      this.$set(this.searchForm, 'age', dataUtils.jsGetAge(value));
    },
    //身份证获取生日,年龄,性别
    blurCardNo() {
      var value = this.searchForm.cardNo;
      if (value && this.searchForm.cardType === '1') {
        let cardNoReg =
          /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/;
        if (!cardNoReg.test(value)) {
        } else {
          this.$nextTick(() => {
            let { age, sex, birthday } = dataUtils.getByIdCard(value);
            this.$set(this.searchForm, 'age', age);
            this.$set(this.searchForm, 'sex', sex);
            this.$set(this.searchForm, 'birthday', birthday);
            console.log(this.searchForm.sex, sex);
          });
        }
      }
    },
    //获取体检次数
    getSimpleCompanyTimes(companyCode, companyTimes) {
      console.log('[ companyTimes ]-1059', companyTimes);
      this.comTimesList = [];
      this.$ajax
        .post(this.$apiUrls.GetSimpleCompanyTimes, '', {
          query: { companyCode: companyCode }
        })
        .then((r) => {
          console.log('rrrr', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.$nextTick(function () {
            this.comTimesList = [];
            if (returnData.length > 0) {
              returnData.map((item) => {
                this.comTimesList.push({
                  label: item.companyTimes,
                  value: item.companyTimes
                });
              });
              if (companyTimes) {
                console.log('[ companyTimes ]-1080', companyTimes);
                this.$set(this.searchForm, 'companyTimes', companyTimes);
                // if(isShow){
                //   this.$parent.$parent.$parent.timesChange();
                // }
                // return
              } else {
                this.$set(
                  this.searchForm,
                  'companyTimes',
                  this.comTimesList[returnData?.length - 1].value
                );
              }

              if (this.isShow) {
                this.$parent.$parent.$parent.timesChange();
              }
            }
          });
        });
    },
    getComTimes(val) {
      console.log('[ val ]-265', val);
      this.$set(this.searchForm, 'companyTimes', null);
      this.getSimpleCompanyTimes(val);
    },
    //姓名回车
    searchName() {
      if (!this.searchForm.name) {
        this.$message({
          showClose: true,
          message: '请先输入姓名再回车',
          type: 'warning'
        });
        return;
      }
      // if (this.searchForm.patCode) {
      // this.$message({
      //   showClose: true,
      //   message: "姓名查询带档案号的数据不能回车获取历史记录",
      //   type: "warning",
      // });
      //   return;
      // }
      this.$ajax
        .post(this.$apiUrls.GetHistoryArchives, '', {
          query: {
            queryType: 'Name',
            queryValue: this.searchForm.name
          }
        })
        .then((r) => {
          console.log('r', r);
          let { success, returnData } = r.data;
          if (!success) return;
          // if (returnData.length == 1) {
          //   this.$nextTick(() => {
          //     this.$confirm("查询到一条历史数据,是否沿用?", "提示", {
          //       confirmButtonText: "确定",
          //       cancelButtonText: "取消",
          //       type: "warning",
          //     }).then(() => {
          //       this.getReturnInfo(returnData[0], null);
          //     });
          //   });
          // } else
          if (returnData.length >= 1) {
            this.dialogTableVisible = true;
            this.$nextTick(() => {
              this.dialogList = returnData;
            });
            console.log('[ this.dialogList ]-743', this.dialogList);
          } else {
            // this.$parent.createBtn();
            this.dialogTableVisible = false;
            this.$message({
              showClose: true,
              message: '该姓名暂无历史数据',
              type: 'success'
            });
          }
        });
    },
    saveName() {
      if (!this.param) {
        this.$message({
          showClose: true,
          message: '请先选中数据再确定',
          type: 'warning'
        });
      } else {
        this.getReturnInfo(this.param);
        this.dialogTableVisible = false;
      }
    },
    //姓名/身份证/电话双击确定
    enterDbClick(row) {
      this.$emit('initData');
      this.$nextTick(() => {
        this.getReturnInfo(row);
        this.dialogTableVisible = false;
      });
    },
    //身份证回车
    searchCard() {
      if (!this.searchForm.cardNo) {
        this.$message({
          showClose: true,
          message: '请先输入身份证再回车',
          type: 'warning'
        });
        return;
      }
      //if (this.searchForm.patCode) {
      // this.$message({
      //   showClose: true,
      //   message: "身份证查询带档案号的数据不能回车获取历史记录",
      //   type: "warning",
      // });
      // return;
      //}
      let cardNo = this.searchForm.cardNo;
      this.$ajax
        .post(this.$apiUrls.GetHistoryArchives, '', {
          query: {
            queryType: 'CardNo',
            queryValue: this.searchForm.cardNo
          }
        })
        .then((r) => {
          console.log('r', r);
          let { success, returnData } = r.data;
          if (!success) return;
          // if (!returnData) {
          // this.$parent.createBtn();

          //}
          // this.$nextTick(() => {
          //   this.$confirm("查询到一条历史数据,是否沿用?", "提示", {
          //     confirmButtonText: "确定",
          //     cancelButtonText: "取消",
          //     type: "warning",
          //   }).then(() => {
          //     this.getReturnInfo(returnData, this.searchForm.cardNo);
          //   });
          // });
          if (returnData.length >= 1) {
            this.dialogTableVisible = true;
            this.dialogList = [];
            this.$nextTick(() => {
              this.dialogList = returnData;
            });
          } else {
            this.$message({
              showClose: true,
              message: '该身份证暂无历史数据',
              type: 'success'
            });
            this.$nextTick(() => {
              if (this.searchForm.cardType == '1') {
                //验证身份证正确再赋值性别,年龄,生日
                let cardNoReg =
                  /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/;
                if (!cardNoReg.test(cardNo)) {
                  return;
                }
                let { age, sex, birthday } = dataUtils.getByIdCard(cardNo);
                this.$set(this.searchForm, 'age', age);
                this.$set(this.searchForm, 'sex', sex);
                this.$set(this.searchForm, 'birthday', birthday);
              }
            });
            return;
          }
        });
    },
    //电话回车
    searchTel() {
      if (!this.searchForm.tel) {
        this.$message({
          showClose: true,
          message: '请先输入电话再回车',
          type: 'warning'
        });
        return;
      }
      //if (this.searchForm.patCode) {
      // this.$message({
      //   showClose: true,
      //   message: "电话查询带档案号的数据不能回车获取历史记录",
      //   type: "warning",
      // });
      // return;
      //}
      this.$ajax
        .post(this.$apiUrls.GetHistoryArchives, '', {
          query: {
            queryType: 'Tel',
            queryValue: this.searchForm.tel
          }
        })
        .then((r) => {
          console.log('r', r);
          let { success, returnData } = r.data;
          if (!success) return;
          //this.searchForm = returnData || [];
          // if (returnData.length == 1) {
          //   this.$nextTick(() => {
          //     this.$confirm("查询到一条历史数据,是否沿用?", "提示", {
          //       confirmButtonText: "确定",
          //       cancelButtonText: "取消",
          //       type: "warning",
          //     }).then(() => {
          //       this.getReturnInfo(returnData[0], null);
          //     });
          //   });
          // } else
          if (returnData.length >= 1) {
            this.dialogTableVisible = true;
            this.$nextTick(() => {
              this.dialogList = returnData;
            });
            console.log('[ this.dialogList ]-743', this.dialogList);
          } else {
            this.dialogTableVisible = false;
            // this.$parent.createBtn();
            this.$message({
              showClose: true,
              message: '该电话暂无历史数据',
              type: 'success'
            });
          }
        });
    },

    //获取回车返回数据对应
    getReturnInfo(returnData, cardNo) {
      if (this.isShow) {
        returnData.peCls = 3;
      } else {
        returnData.peCls = 0;
      }
      //把数据对应赋值给searchForm
      let data = dataUtils.deepCopy(returnData);
      Object.keys(returnData).forEach((key) => {
        this.searchForm[key] = data[key];
      });
      //console.log(this.searchForm)
      //this.searchForm = dataUtils.deepCopy(returnData);
      this.$nextTick(() => {
        this.dialogTableVisible = false;
      });

      if (cardNo) {
        console.log('[ this.searchForm ]-940', this.searchForm);
        if (this.searchForm.cardType == '1') {
          let { age, sex, birthday } = dataUtils.getByIdCard(cardNo);
          this.$set(this.searchForm, 'age', age);
          this.$set(this.searchForm, 'sex', sex);
          this.$set(this.searchForm, 'birthday', birthday);
        }
      }

      this.isUpdate = true;
      this.$message({
        showClose: true,
        message: '查询历史数据成功!',
        type: 'success'
      });
    },
    //获取年龄
    getAge(birthday) {
      this.searchForm.birthday = birthday;
      var strBirthdayArr = dataUtils.interceptDate(birthday).split('-');
      var birthYear = strBirthdayArr[0];
      var d = new Date();
      var nowYear = d.getFullYear();
      if (nowYear == birthYear) {
        this.searchForm.ageUnit = 1;
      } else {
        this.searchForm.ageUnit = 0;
      }
      this.searchForm.age = dataUtils.jsGetAge(birthday);
    },
    handleCurrentChange(rows) {
      console.log(rows);
      this.param = rows;
    },
    //查询
    search() {
      // if (this.queryType == "PatNo") {
      //   this.searchRegNo(this.leftSearchVal);
      // } else {
      if (!this.leftSearchVal.trim()) {
        this.$message({
          showClose: true,
          message: '请输入搜索内容',
          type: 'warning'
        });
        return;
      }
      this.isShow == true
        ? (this.personOrCompany = '/company')
        : (this.personOrCompany = '/person');
      console.log('[ this.personOrCompany ]-1182', this.personOrCompany);
      this.$ajax
        .post(this.$apiUrls.GetRegisterByQueryType + this.personOrCompany, '', {
          query: {
            // queryType: this.queryType,
            queryValue: this.leftSearchVal
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          if (returnData.length == 1) {
            this.$nextTick(() => {
              // this.searchForm = returnData[0];
              this.searchRegNo(returnData[0].regNo);
              this.isUpdate = true;
              // this.$message({
              //   showClose: true,
              //   message: "查询订单数据成功",
              //   type: "success",
              // });
            });
          } else if (returnData.length > 1) {
            this.dialogTable = true;
            this.$nextTick(() => {
              this.searchList = returnData;
            });
            console.log('[ this.searchList ]-743', this.searchList);
          } else {
            this.dialogTable = false;
            this.$message({
              showClose: true,
              message: '暂无数据!',
              type: 'success'
            });
          }
        });
      // }
    },
    //体检号搜索
    async searchRegNo(regNo) {
      this.$parent.$parent.$parent.createBtn().then((r) => {
        console.log(this);
        if (!regNo.trim()) {
          this.$message({
            showClose: true,
            message: '请输入搜索内容',
            type: 'warning'
          });
          return;
        }
        this.$ajax
          .post(this.$apiUrls.GetRegisterOrder, '', {
            query: { regNo: regNo }
          })
          .then((r) => {
            let { success, returnData } = r.data;
            let test = JSON.parse(JSON.stringify(returnData));
            console.log(test);
            if (!success) return;
            this.setData = returnData;
            console.log('[ this.setData ]-1168', this.setData);
            this.searchForm = returnData.patient;
            console.log('[  this.searchForm ]-1170', this.searchForm);
            this.$message({
              showClose: true,
              message: '查询数据成功!',
              type: 'success'
            });
            this.blurCardNo();
            this.isUpdate = true;
            this.getSimpleCompanyTimes(
              this.searchForm.companyCode,
              returnData.patient.companyTimes
            );
            // 判断是否团体，团体为true；
            if (this.isShow) {
              console.log(returnData.patient);
              let datas = {
                companyCode: returnData.patient.companyCode,
                companyTimes: returnData.patient.companyTimes
              };
              console.log(datas);
              this.$ajax
                .post(this.$apiUrls.ReadCompanyCandidateCluster, '', {
                  query: datas
                })
                .then(async (r) => {
                  let { success, returnData } = r.data;
                  if (!success) return;
                  let companyMeal = returnData || [];
                  console.log(companyMeal);
                  // await this.$parent.clearMealData();
                  await this.sexChange();
                  setTimeout(() => {
                    let companyMealCode = [];
                    companyMeal.map((item) => {
                      companyMealCode.push(item.clusCode);
                      this.$parent.$parent.$parent.mealListCodeObj[
                        item.clusCode
                      ] = item;
                    });
                    this.$parent.$parent.$parent.mealListCode.unshift(
                      ...companyMealCode
                    );
                    this.$parent.$parent.$parent.mealList.unshift(
                      ...companyMeal
                    );
                    this.$nextTick(() => {
                      this.disposeRecordData();
                    });
                  }, 50);
                });
            } else {
              this.sexChange();
              this.$nextTick(() => {
                this.disposeRecordData();
              });
            }
          });
      });
    },

    handleCurrentChanges(row) {
      console.log(row);
      if (!row) return;
      this.tableInfo = row.regNo;
    },
    //确定
    saveSearch() {
      console.log(this.tableInfo);
      if (!this.tableInfo) {
        this.$message({
          showClose: true,
          message: '请先选中数据再确定',
          type: 'warning'
        });
      } else {
        this.searchRegNo(this.tableInfo);
        this.isUpdate = true;
        this.dialogTable = false;
      }
    },
    //双击确定
    searchDbClick(val) {
      this.searchRegNo(val.regNo);
      this.isUpdate = true;
      this.dialogTable = false;
    },
    //更新
    updateReg() {
      if (!this.searchForm.regNo) {
        this.$message({
          showClose: true,
          message: '只有搜索出带体检号的数据才能更新',
          type: 'warning'
        });
        return;
      }
      this.isShow == true
        ? (this.personOrCompany = '/company')
        : (this.personOrCompany = '/person');
      console.log('[ this.personOrCompany ]-1287', this.personOrCompany);
      let searchForm = this.searchForm;
      console.log('[ searchForm ]-515', searchForm);
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.uploadImg(this.searchForm.regNo);
          this.$ajax
            .post(
              this.$apiUrls.AlterRegisterPatient + this.personOrCompany,
              searchForm
            )
            .then((r) => {
              let { success } = r.data;
              if (!success) return;
              this.isUpdate = false;
              this.$refs.form.resetFields();
              this.$parent.$parent.$parent.createBtn();
              if (this.isShow) {
                this.searchForm.peCls = 3; //团体默认单位体检
              }
              this.$message({
                message: '更新成功',
                type: 'success',
                showClose: true
              });
            });
        } else {
          return false;
        }
      });
    },
    //获取单位下拉
    getCompany() {
      this.$ajax.post(this.$apiUrls.Company).then((r) => {
        this.companyList = r.data.returnData;
        this.companyListCopy = JSON.parse(JSON.stringify(this.companyList));
      });
    },

    //获取籍贯：
    getNativePlace() {
      this.$ajax.post(this.$apiUrls.NativePlace).then((r) => {
        this.nativePlaceList = r.data.returnData;
      });
    },

    //获取证件类型：
    // getCardType() {
    //   this.$ajax.post(this.$apiUrls.CardType).then((r) => {
    //
    //     this.cardTypeList = r.data.returnData;
    //   });
    // },
    //获取工种：
    getJob() {
      this.$ajax.post(this.$apiUrls.Job).then((r) => {
        this.jobList = r.data.returnData;
      });
    },
    //获取介绍人：
    getOperator() {
      this.$ajax.post(this.$apiUrls.Operator).then((r) => {
        this.operatorList = r.data.returnData;
      });
    },
    // 性别改变的回调
    async sexChange() {
      await this.$parent.$parent.$parent.clearcheckMeal();
      this.$parent.$parent.$parent.mealSearch();
    },
    // 次数改变的回调
    timesChange() {
      this.$emit('timesChange');
      this.sexChange();
    },
    // 诊疗卡号的enter回调
    hisCardEnter() {
      console.log(this.$options.data().searchForm);
      if (this.searchForm.hisCard.trim() === '') return;
      this.$parent.$parent.$parent.clearcheckMeal();
      this.searchForm = {
        ...this.$options.data().searchForm,
        hisCard: this.searchForm.hisCard
      };
      this.$ajax
        .paramsPost(this.$apiUrls.GetHistoryArchivesByHisCard, {
          hisCard: this.searchForm.hisCard
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.searchForm = {
            ...this.searchForm,
            ...returnData
          };
        });
    }
  }
};
</script>
<style lang="less" scoped>
.leftCont {
  display: flex;
  padding: 10px;
  height: 100%;
  // width: 360px;
  flex-direction: column;
  padding-top: 0;
  background-color: #fff;
  /deep/.el-dialog {
    height: 500px;
    width: 700px;
    display: flex;
    flex-direction: column;
    padding: 15px;
  }
  .ageForm {
    display: flex;
  }
  .left_header {
    background-color: #fff;
    border-radius: 5px;
    height: 92px;
  }
  header {
    padding: 10px 0;
    border-bottom: 0.5px solid rgba(178, 190, 195, 0.6);
    padding-right: 7px;

    h3 {
      font-size: 16px;
      color: #2d3436;
      font-weight: 600;
      margin-bottom: 14px;
    }

    .left_search {
      display: flex;
    }

    .left_search_div {
      flex: 1;
      margin-right: 10px;

      input {
        height: 36px;
      }
    }
  }
  .left_search {
    display: flex;
  }

  .left_search_div {
    flex: 1;
    margin-right: 10px;

    input {
      height: 36px;
    }
  }
  .right_form {
    flex: 1;
    height: calc(100% - 92px);
    overflow: auto;
    background-color: #fff;
    /deep/.el-form-item__label {
      font-weight: bold;
      font-size: 16px;
    }
  }
  /deep/.el-dialog__body {
    height: calc(100% - 120px);
  }
  /deep/.el-select {
    width: 100%;
  }
  /deep/.el-input__inner {
    height: 32px;
    line-height: 32px;
  }
  /deep/.el-date-editor.el-input,
  .el-date-editor.el-input__inner {
    width: 100%;
  }
  /deep/.el-form-item__label {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #2d3436;
    text-align: right;
  }
  .el-form-item {
    margin-bottom: 8px;
  }
  /deep/.isBlue .el-checkbox__label {
    color: #1770df !important;
  }
  /deep/.redIpt .el-input__inner {
    color: red;
  }
  .footer-btn {
    display: flex;
    justify-content: space-between;
  }
  //
  /deep/.checkbox .el-checkbox {
    margin-right: 18px;
  }
}
</style>
<style lang="less">
.dialogCss {
  height: 520px !important;
  width: 800px;
  display: flex;
  flex-direction: column;
  padding: 15px;
  /deep/.el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #409eff;
    border-color: #409eff;
  }
}
.cropper_drawer {
  .cropper {
    width: 100%;
    height: 300px;
  }
}
.cropper_drawer {
  .up_label {
    border: 1px solid #ccc;
    font-size: 12px;
    border-radius: 3px;
    line-height: 26px;
    padding: 0 15px;
    display: inline-block;
    vertical-align: middle;
    cursor: pointer;
    margin-right: 10px;
  }
}
</style>
