<template>
  <el-dialog
    :close-on-click-modal="false"
    :show-close="false"
    :close-on-press-escape="false"
    class="refunds_dig"
    width="60%"
    title="退费"
    :visible.sync="refundsDigShow"
  >
    <div style="height: 400px">
      <PublicTable
        :viewTableList="refundsList"
        :theads="checkComboMealTheads"
        :tableRowClassName="tableRowClassName"
        :columnWidth="checkComboColumnWidth"
        :columnAlign="{ price: 'right', testTubes: 'right' }"
        :cell_red="checkComboCell_red"
        :isStripe="false"
        :isSortShow="false"
        onlyId="combCode"
        isCheck
        :headerCellStyle="{
          background: '#fff',
          fontSize: '14px',
          color: 'rgba(45,52,54,.6)'
        }"
        @selectionChange="checkRefundsListChange"
        v-model="checkRefundsList"
      >
        <template #price="{ scope }">
          {{ handlePrice(scope.row.price) }}
        </template>
        <template #testTubes="{ scope }">
          <div class="testTubes_div" v-if="!!scope.row.testTubes">
            <el-popover placement="top-start" width="200" trigger="hover">
              <div
                v-for="(item, idx) in scope.row.testTubes.testTubes"
                :key="idx"
              >
                {{ item.combName + ' *' + item.discount }}
              </div>
              <div slot="reference">
                <span>
                  {{
                    scope.row.testTubes.testTubesPrice
                      ? handlePrice(scope.row.testTubes.testTubesPrice)
                      : ''
                  }}
                </span>
              </div>
            </el-popover>
          </div>
        </template>
        <template #payStatus="{ scope }">
          <div
            :class="scope.row.payStatus == 0 ? 'noPay_text' : 'havePay_text'"
          >
            {{ G_EnumList['PayStatus'][scope.row.payStatus] }}
          </div>
        </template>
      </PublicTable>
    </div>
    <div slot="footer" class="dialog-footer">
      <div class="totalMoney_wrap">
        <ul>
          <li>
            <label>组合：{{ total.quantity }}</label>
            <span>个</span>
          </li>
          <li class="">
            <label>项目价格：{{ total.itemMoney }}</label>
            <span>元</span>
          </li>
          <li class="">
            <label>附加费+材料费：{{ total.testTubesMoney }}</label>
            <span>元</span>
          </li>
          <li class="total_money money_color">
            <label>合计：{{ total.totalMoney }}</label>
            <span>元</span>
          </li>
        </ul>
      </div>
      <div>
        <el-button @click="$emit('update:refundsDigShow', false)"
          >取 消</el-button
        >
        <el-button type="primary" @click="refundsConfirm">确 定</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex';
import PublicTable from '@/components/publicTable';
import { dataUtils } from '@/common';
export default {
  name: 'refund',
  components: { PublicTable },
  props: {
    //显示开关
    refundsDigShow: {
      type: Boolean,
      default: false,
      required: true
    },
    //提款申请体检号
    regNo: {
      type: String | Number,
      default: '',
      required: true
    },
    //退费列表
    refundsList: {
      type: Array,
      default: [],
      required: true
    },
    //设置列 类名
    tableRowClassName: {
      type: Function,
      default: () => {},
      required: true
    },
    //材料费
    testTubes: {
      type: Array,
      default: [],
      required: true
    }
  },
  data() {
    return {
      checkComboColumnWidth: {
        combCode: '100',
        testTubes: '80',
        originalPrice: '100',
        price: '100',
        payStatus: '100'
      },
      checkComboMealTheads: {
        combCode: '组合代码',
        combName: '组合名称',
        price: '单价',
        testTubes: '材料费',
        payStatus: '支付状态'
      },
      checkComboCell_red: ['price', 'testTubes', 'originalPrice'],
      checkRefundsList: [],
      total: {
        quantity: 0,
        totalMoney: 0,
        itemMoney: 0,
        testTubesMoney: 0
      }
    };
  },
  computed: {
    ...mapGetters(['G_EnumList'])
  },
  methods: {
    handlePrice: dataUtils.handlePrice,
    // 确定退费
    refundsConfirm() {
      if (this.checkRefundsList.length == 0) {
        this.$message({
          message: '请选择需要退费的项目',
          type: 'warning'
        });
        return;
      }
      let combs = [];
      this.checkRefundsList.map((item) => {
        combs.push(item.id);
      });
      let datas = {
        regNo: this.regNo,
        combs: combs
      };
      console.log(datas);
      this.$confirm('是否确定退款选中的项目？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax.post(this.$apiUrls.HisRefund, datas).then((r) => {
            let { success, returnData } = r.data;
            if (!success) return;
            this.$message({
              message: '申请退款操作成功！',
              type: 'success'
            });
            //退款成功回调
            this.$emit('refundSuccess');
            this.$emit('update:refundsDigShow', false);
          });
        })
        .catch(() => {});
    },
    // 退费选中的回调
    checkRefundsListChange(selection, checkList) {
      this.checkRefundsList = checkList;
      this.computeTotalPrice();
    },
    // 计算总价格
    computeTotalPrice() {
      let itemMoney = 0,
        testTubesMoney = 0;
      //未勾选列表
      let noCheckList = this.refundsList.filter((item) => {
        return !this.checkRefundsList.some((checkItem) => {
          return checkItem.id === item.id;
        });
      });
      //计算材料费
      for (const key in this.testTubes) {
        let temp = this.testTubes[key].beFrom;
        if (
          !noCheckList.some((item) => temp.includes(item.id)) &&
          this.checkRefundsList.some((item) => temp.includes(item.id))
        ) {
          testTubesMoney = dataUtils.add(
            this.testTubes[key].price,
            testTubesMoney
          );
        }
      }
      //计算项目总价格
      this.checkRefundsList.map((item) => {
        itemMoney = dataUtils.add(itemMoney, item.price);
      });
      this.total.quantity = this.checkRefundsList.length;
      this.total.itemMoney = itemMoney;
      this.total.testTubesMoney = testTubesMoney;
      this.total.totalMoney = dataUtils.add(itemMoney, testTubesMoney);
    }
  }
};
</script>
<style lang="less" scoped>
.dialog-footer {
  text-align: none;
  display: flex;
  justify-content: space-between;
  align-content: center;
}

.totalMoney_wrap {
  align-content: center;
  border-radius: 4px;
  position: sticky;
  bottom: 0;
  left: 0;

  ul {
    display: flex;
    font-size: 14px;
    color: #050505;
    font-weight: 600;
    flex-wrap: wrap;

    li + li {
      margin-left: 10px;
    }

    .money_color {
      color: #d63031;
    }

    .total_money {
      font-size: 18px;
      text-align: right;
      flex: 1;
      flex-shrink: 0;
    }
  }
}
</style>
