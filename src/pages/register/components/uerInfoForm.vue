<script>
import { mapGetters } from 'vuex';
import { dataUtils } from '../../../common';
import PublicTable from '../../../components/publicTable.vue';
import Camera from '../mixins/newCamera';

export default {
  mixins: [Camera],
  components: {
    PublicTable
  },
  props: {
    personal: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_peClsList',
      'G_cardType',
      'G_sexList',
      'G_ageUnit',
      'G_marriageStatus',
      'G_CodeOccupationalPositionStatus',
      'G_config',
      'G_userInfo'
    ]),
    // 判断是否是职业检
    C_isOccupation() {
      return (
        this.G_config.physicalMode.includes('职检') &&
        this.userInfo.peCls?.includes('职业检')
      );
    },
    bookType() {
      return this.G_EnumList['BookType'][this.userInfo.bookType];
    },
    telFormValidation() {
      if (this.personal) {
        return this.G_EnumList['SysParCustom']?.TelRequiredOfPerson == '是';
      } else {
        return this.G_EnumList['SysParCustom']?.TelRequiredOfCompany == '是';
      }
    }
  },
  data() {
    const checkAge = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入年龄'));
      } else {
        if (this.userInfo.ageUnit === 0) {
          let ageReg = /^((\d{1,2})|(1[0-4]\d)|(150))$/;
          if (!ageReg.test(value)) {
            callback(new Error('年龄格式不正确'));
          } else {
            callback();
          }
        } else {
          let ageReg = /^(1[0-2]|[1-9])$/;
          if (!ageReg.test(value)) {
            callback(new Error('月份格式不正确'));
          } else {
            callback();
          }
        }
      }
    };
    const checkNames = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入姓名'));
      } else {
        // this.searchName();
        callback();
        return;
      }
    };
    const checkSex = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请选择性别'));
      } else {
        callback();
        return;
      }
    };
    // 身份证校验
    const checkCardNo = (rule, value, callback) => {
      if (!value) {
        callback();
        return;
      } else if (value && this.userInfo.cardType === '1') {
        let cardNoReg =
          /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/;
        if (!cardNoReg.test(value)) {
          callback(new Error('身份证格式不正确'));
        } else {
          this.$nextTick(() => {
            // let { age, sex, birthday } = dataUtils.getByIdCard(value);
            // this.$set(this.userInfo, "age", age);
            // this.$set(this.userInfo, "sex", sex);
            // this.$set(this.userInfo, "birthday", birthday);
            // this.searchCard(); //查询
            callback();
            return;
          });
        }
      } else {
        callback();
        return;
      }
    };
    const checkCardTel = (rule, value, callback) => {
      if (!value) {
        callback();
        return;
      } else {
        let telReg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/;
        if (!telReg.test(value)) {
          callback(new Error('手机号格式不正确！'));
        } else {
          // this.searchTel();
          callback();
          return;
        }
      }
    };
    //通过生日获取年龄
    const checkB = (rule, value, callback) => {
      if (!value) {
        callback();
        return;
      } else {
        // var strBirthdayArr = dataUtils.interceptDate(value).split("-");
        // var birthYear = strBirthdayArr[0];
        // var d = new Date();
        // var nowYear = d.getFullYear();
        // if (nowYear == birthYear) {
        //   this.userInfo.ageUnit = 1;
        // } else {
        //   this.userInfo.ageUnit = 0;
        // }
        // this.$set(this.userInfo, "age", dataUtils.jsGetAge(value));
        callback();
      }
    };
    return {
      jobKeyword: '',
      headPortraitImg: '',
      pullDownTrigger: false, //是否由下拉触发的体检分类监视
      jobStatusIsChange: false, //是否由下拉触发职业病监视,
      judgeGender: true, //性别改变时候是否判断组合与当前性别互斥
      peKey: 0,
      peStatus: false,
      jobItem: [], //工种懒加载列表
      pageData: {
        pageNumber: 1,
        pageSize: 500,
        keyword: ''
      },
      totalPage: 1,
      loading: false,
      userInfo: {
        regNo: '',
        patCode: '',
        name: '',
        sex: null,
        age: '',
        ageUnit: 0,
        birthday: null,
        cardType: '1',
        cardNo: '',
        tel: '',
        nativePlace: '',
        address: '',
        marryStatus: null,
        photoUrl: '',
        registerTime: null,
        isActive: false,
        peCls: [],
        guidanceType: '',
        reportType: '',
        companyCode: '',
        companyTimes: null, //单位次数
        companyDeptCode: '',
        jobCode: '',
        jobHistory: '',
        medicalHistory: '',
        familyMedicalHistory: '',
        isVIP: false,
        isLeader: false,
        isConstitution: false,
        isRecheck: false,
        recheckNo: '',
        operatorCode: '',
        introducer: '',
        note: '',
        chargeModel: 1,
        registerTimes: 0,
        hisCard: '', //诊疗卡号
        // 职业病字段
        monitoringType: 1,
        totalYearsOfWork: 0,
        totalMonthsOfWork: 0,
        jobStatus: null,
        isOrdinary: true,
        isOccupation: false,
        bookType: -1,
        jobName: '',
        jobType: null,
        jobId: ''
      },
      HisPatInfo: {},
      rules: {
        // companyCode: [
        //   { required: true, message: "请选择工作单位", trigger: "change" },
        // ],
        companyTimes: [
          { required: true, message: '请选择单位次数', trigger: 'change' }
        ],
        peCls: [
          { required: true, message: '请输入体检分类', trigger: 'change' }
        ],
        name: [{ required: true, validator: checkNames, trigger: 'blur' }],
        sex: [{ required: true, validator: checkSex, trigger: 'change' }],
        age: [
          { required: true, validator: checkAge, trigger: 'blur' },
          { validator: checkAge, trigger: 'change' }
        ],
        birthday: [
          { required: true, message: '请选择出生日期', trigger: 'change' },
          { validator: checkB, trigger: 'change' }
        ],
        cardNo: [
          { required: true, message: '请输入证件号', trigger: 'blur' },
          { validator: checkCardNo, trigger: 'blur' }
        ],
        jobId: [{ required: true, message: '请输入工号', trigger: 'blur' }],
        jobType: [
          { required: true, message: '请输入工种', trigger: 'change' },
          {
            validator: (_, value, callback) => {
              if (!value.trim()) {
                callback(new Error('请输入工种名'));
              } else {
                callback();
              }
            },
            trigger: 'change'
          }
        ],
        jobStatus: [
          {
            required: true,
            message: '请输入在岗状态',
            trigger: 'change'
          }
        ],
        totalYearsOfWork: [
          { required: true, message: '请输入工龄-年', trigger: 'blur' },
          {
            validator: this.validateTotalYearsOfWork,
            trigger: 'blur'
          }
        ],
        totalMonthsOfWork: [
          { required: true, message: '请输入工龄-月', trigger: 'blur' },
          {
            validator: this.validateTotalMonthsOfWork,
            trigger: 'blur'
          }
        ],
        monitoringType: [
          {
            required: true,
            message: '请选择检测类型',
            trigger: 'change'
          }
        ]
      },
      nativePlaceList: [], //籍贯列表
      jobList: [], //工种列表
      jobIndex: -1, //当前工种下拉hover索引
      operatorList: [], //介绍人列表
      companyList: [],
      companyListCopy: [],
      unfoldFlag: false,
      monitorTypeList: [
        {
          label: '常规监测',
          value: 1
        },
        {
          label: '主动监测',
          value: 2
        }
      ],
      guidanceTypeList: [],
      dialogTableVisible: false,
      dialogList: [],
      theads: {
        name: '姓名',
        cardNo: '身份证',
        age: '年龄',
        birthday: '出生日期',
        patCode: '档案号',
        address: '地址'
      },
      columnWidth: {
        age: '50',
        patCode: '160',
        name: '120',
        cardNo: '200',
        tel: '160',
        regNo: '160',
        companyCode: '200',
        companyDeptCode: '200',
        birthday: '95'
      },
      peClsOptions: [], //体检类型
      companyDeptList: [],
      isFocused: false //工种下拉搜索input是否聚焦
    };
  },
  methods: {
    validateTotalMonthsOfWork(rule, value, callback) {
      const reg = /^(1[01]|[0-9])$/; // 正则表达式匹配0-11的整数
      if (!reg.test(value)) {
        callback(new Error('只能输入0-11的整数'));
      } else {
        callback();
      }
    },
    validateTotalYearsOfWork(rule, value, callback) {
      const reg = /^(150|[1-9]?[0-9])$/; // 正则表达式匹配0和正整数
      if (!reg.test(value)) {
        callback(new Error('只能输入0-150的整数'));
      } else {
        callback();
      }
    },
    //查询公司部门信息
    getDepartList() {
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanyDepartment, {
          companyCode: this.userInfo.companyCode,
          deptCode: '',
          deptName: ''
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.companyDeptList = returnData || [];
        });
    },
    async getItems() {
      this.loading = true;
      const data = await this.loadCodeJobs();

      const jobs = data.map(({ jobName, jobCode }) => ({
        label: jobName,
        value: jobCode
      }));
      this.jobItem = JSON.parse(JSON.stringify(jobs));
      this.jobList = JSON.parse(JSON.stringify(jobs));
      this.loading = false;
    },
    async loadCodeJobs() {
      const { data } = await this.$ajax.post(
        this.$apiUrls.ReadCodeOccupationalJobByPage,
        this.pageData
      );

      const { success, returnData } = data;
      if (!success) throw new Error('获取工种列表失败');
      return returnData;
    },
    dateDispose(row) {
      return dataUtils.subBlankDate(row.birthday);
    },
    // 展开个人信息
    unfoldClick() {
      // if(!this.C_isOccupation) return;
      this.unfoldFlag = !this.unfoldFlag;
      // setTimeout(()=>{
      //     this.$parent.tableMaxHeight = this.$parent.$refs.tableWrap_ref.getBoundingClientRect().height + 'px';
      //     console.log(this.$parent.tableMaxHeight);
      // },210)
    },
    //获取籍贯：
    getNativePlace() {
      this.$ajax.post(this.$apiUrls.NativePlace).then((r) => {
        this.nativePlaceList = r.data.returnData;
      });
    },
    //获取介绍人：
    getOperator() {
      this.$ajax.post(this.$apiUrls.Operator).then((r) => {
        this.operatorList = r.data.returnData;
      });
    },
    //指引单样式
    getGuidanceType() {
      this.$ajax.post(this.$apiUrls.GetGuidanceType).then((res) => {
        let { success, returnData } = res.data;
        if (!success) return;
        returnData.forEach((item) => {
          this.guidanceTypeList.push({
            label: item.reportName,
            value: item.reportCode
          });
        });
        // this.operatorList = r.data.returnData;
      });
    },
    //获取单位下拉
    getCompany() {
      this.$ajax.post(this.$apiUrls.Company).then((r) => {
        this.companyListCopy = r.data.returnData;
        this.companyList = dataUtils.deepCopy(this.companyListCopy);
        this.$bus.$emit('companyDate', r.data.returnData);
      });
    },
    // 体检分类值改变的回调
    peClsChange(val) {
      if (val.value == null) {
        this.userInfo.peCls = [];
      }
      this.pullDownTrigger = true;
      if (val.value?.length === 2 && !this.userInfo.peCls.includes('职业检'))
        this.userInfo.peCls.splice(0, 1);
      if (val.value?.length > 2)
        this.userInfo.peCls[0] == '职业检'
          ? this.userInfo.peCls.splice(1, 1)
          : this.userInfo.peCls.splice(0, 1);
    },
    //通过生日获取年龄
    changeBirthday() {
      var value = this.userInfo.birthday;
      var strBirthdayArr = dataUtils.interceptDate(value).split('-');
      var birthYear = strBirthdayArr[0];
      var d = new Date();
      var nowYear = d.getFullYear();
      if (nowYear == birthYear) {
        this.userInfo.ageUnit = 1;
      } else {
        this.userInfo.ageUnit = 0;
      }
      this.$set(this.userInfo, 'age', dataUtils.jsGetAge(value));
    },
    //身份证获取生日,年龄,性别
    blurCardNo() {
      var value = this.userInfo.cardNo;
      if (value && this.userInfo.cardType === '1') {
        let cardNoReg =
          /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/;
        if (!cardNoReg.test(value)) {
        } else {
          this.$nextTick(() => {
            let { age, sex, birthday } = dataUtils.getByIdCard(value);
            this.$set(this.userInfo, 'age', age);
            this.$set(this.userInfo, 'sex', sex);
            this.$set(this.userInfo, 'birthday', birthday);
            // this.sexChange();
          });
        }
      }
    },
    //身份证输入触发
    handleCardNo(value) {
      this.userInfo.cardNo = value.replace(/[^a-zA-Z0-9]/g, '').toUpperCase();
    },
    // 重置表单
    resetForm() {
      this.companyDeptList = [];
      this.$refs['ruleForm'].resetFields();
      this.userInfo.isLeader = false;
      this.userInfo.isVIP = false;
      this.userInfo.isActive = false;
      this.userInfo.isOrdinary = true;
      this.userInfo.isOccupation = false;
      this.userInfo.ageUnit = 0;
      this.userInfo.registerTimes = 0;
      this.headPortraitImg = '';
      this.userInfo.photoUrl = '';
      this.userInfo.guidanceType = this.G_userInfo.systemParams.guidanceType;
      this.isFocused = false;
      this.resetOccupationForm();
      if (this.G_config.physicalMode.length === 1) {
        this.userInfo.peCls = this.G_config.physicalMode.includes('普检')
          ? 3
          : ['职业检'];
      } else {
        this.userInfo.peCls = [3];
      }
    },
    //重置有关职业检的表单
    resetOccupationForm() {
      this.userInfo.jobId = '';
      this.userInfo.jobType = this.getCodeJobFirst(this.G_EnumList['CodeJob']);
      this.userInfo.jobName = '';
      this.userInfo.jobStatus = null;
      this.userInfo.monitoringType = 1;
      this.userInfo.totalYearsOfWork = 0;
      this.userInfo.totalMonthsOfWork = 0;
      this.jobKeyword = '';
    },
    // 诊疗卡号的enter回调
    hisCardEnter() {
      if (this.userInfo.hisCard.trim() === '') return;
      let userInfoCopy = dataUtils.deepCopy(this.userInfo);
      this.$parent.createBtn();
      this.$ajax
        .paramsPost(this.$apiUrls.GetHistoryArchivesByHisCard, {
          hisCard: userInfoCopy.hisCard
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.userInfo = {
            ...this.userInfo,
            ...returnData
          };
        });
    },
    //姓名回车
    searchName() {
      if (!this.userInfo.name) {
        this.$message({
          showClose: true,
          message: '请先输入姓名再回车',
          type: 'warning'
        });
        return;
      }
      this.$ajax
        .post(this.$apiUrls.GetHistoryArchivesNew, '', {
          query: {
            queryType: 'Name',
            queryValue: this.userInfo.name
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          if (returnData.length == 1) {
            this.statusDblclick(returnData[0]);
            return;
          }
          if (returnData.length >= 1) {
            this.dialogTableVisible = true;
            this.$nextTick(() => {
              this.dialogList = returnData;
            });
          } else {
            // this.$parent.createBtn();
            this.dialogTableVisible = false;
            this.$message({
              showClose: true,
              message: '该姓名暂无历史数据',
              type: 'success'
            });
          }
        });
    },
    // 身份信息表格的双击回调
    statusDblclick(row) {
      this.dialogTableVisible = false;
      if (this.videoShow) {
        this.closeCamer('video');
        this.videoShow = false;
      }
      if (this.$parent.isSwipingCard) {
        this.userInfo.patCode = row.patCode;
        this.userInfo.tel = row.tel;
        this.$parent.isSwipingCard = false;
        return;
      }
      this.$parent.createBtn().then(() => {
        this.$nextTick(() => {
          let data = dataUtils.deepCopy(row);
          setTimeout(() => {
            this.$set(this, 'userInfo', { ...this.userInfo, ...data });
          }, 100);
        });
      });
    },
    // 档案记录弹窗关闭的回调
    recordDialogClose() {
      this.$parent.isSwipingCard = false;
    },
    // 性别改变的回调
    // sexChange() {
    //   this.$parent.clearMealAndCombs();
    //   this.$parent.mealSearch();
    // },
    //身份证回车
    searchCard() {
      this.judgeGender = false; //性别改变不触发互斥提醒
      if (!this.userInfo.cardNo) {
        this.$message({
          showClose: true,
          message: '请先输入身份证再回车',
          type: 'warning'
        });
        return;
      }
      let cardNo = this.userInfo.cardNo;
      this.$ajax
        .post(this.$apiUrls.GetHistoryArchivesNew, '', {
          query: {
            queryType: 'CardNo',
            queryValue: this.userInfo.cardNo
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          if (returnData.length == 1) {
            this.statusDblclick(returnData[0]);
            return;
          }
          if (returnData.length >= 1) {
            this.dialogTableVisible = true;
            this.dialogList = [];
            this.$nextTick(() => {
              this.dialogList = returnData;
            });
          } else {
            this.$message({
              showClose: true,
              message: '该身份证暂无历史数据',
              type: 'success'
            });
            this.$nextTick(() => {
              if (this.userInfo.cardType == '1') {
                //验证身份证正确再赋值性别,年龄,生日
                let cardNoReg =
                  /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/;
                if (!cardNoReg.test(cardNo)) {
                  return;
                }
                let { age, sex, birthday } = dataUtils.getByIdCard(cardNo);
                this.$set(this.userInfo, 'age', age);
                this.$set(this.userInfo, 'sex', sex);
                this.$set(this.userInfo, 'birthday', birthday);
              }
            });
            return;
          }
        });
    },
    // 更新
    updateReg() {
      if (!this.userInfo.regNo) {
        this.$message({
          showClose: true,
          message: '只有搜索出带体检号的数据才能更新',
          type: 'warning'
        });
        return;
      }

      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          await this.dataURLtoFile(this.userInfo.photoUrl);
          await this.uploadImg();
          let userInfoCopy = dataUtils.deepCopy(this.userInfo);
          userInfoCopy.photoUrl =
            this.headPortraitImg || userInfoCopy.photoUrl || '';
          if (this.G_config.physicalMode.includes('职检')) {
            let peClsIdx = userInfoCopy.peCls.indexOf('职业检');
            if (peClsIdx != -1) {
              userInfoCopy.peCls.splice(
                userInfoCopy.peCls.indexOf('职业检'),
                1
              );
            }
            userInfoCopy.peCls = userInfoCopy.peCls[0];
          }

          let datas = {
            registerPatient: userInfoCopy,
            occupationOrder: userInfoCopy
          };
          let url = this.personal ? '/person' : '/company';
          this.$ajax
            .post(this.$apiUrls.AlterRegisterPatientNew + url, datas)
            .then((r) => {
              let { success, returnData } = r.data;
              if (!success) return;
              this.$message({
                message: '更新成功',
                type: 'success',
                showClose: true
              });
              // this.dataURLtoFile(this.userInfo.photoUrl).then((r) => {
              //   this.uploadImg(returnData);
              // });
              this.$parent.getUserList();
            });
        } else {
          return false;
        }
      });
    },
    // 单位的change回调
    companyChange(val) {
      this.userInfo.companyDeptCode = '';
      this.$parent.getSimpleCompanyTimes(true);
    },
    //在岗状态的change回调
    jobStatusChange() {
      this.jobStatusIsChange = true;
    },
    //点击工种下拉
    focusEvent() {
      this.changeJobType();
      this.$refs.pulldownRef?.showPanel();
      this.$nextTick(() => {
        this.$refs.jobSearchInput?.$el.querySelector('input').focus();
      });
    },
    //工种下拉选择回调
    selectJobType(item, e) {
      this.userInfo.jobType = item.value;
      if (item.value != '999999') {
        this.$refs.pulldownRef?.hidePanel();
        this.jobKeyword = '';
      } else {
        this.$nextTick(() => {
          let listItem = e.target.closest('.my-list-item');
          let input = listItem?.querySelector('.jobType-input');
          input?.querySelector('input').focus();
        });
        if (
          !this.jobList.some((item) => item.label == this.userInfo.jobName) &&
          this.userInfo.jobName != ''
        ) {
          return;
        }
      }
      this.userInfo.jobName = item.label;
    },
    //工种下拉隐藏触发
    hidePulldown() {
      this.jobKeyword = '';
      this.jobIndex = -1;
      this.isFocused = false;
    },
    //工种过滤
    changeJobType(value) {
      let jobList = dataUtils.deepCopy(this.jobList);
      this.jobItem = jobList.filter((item) =>
        !value ? true : item.label.includes(value) || item.value.includes(value)
      );
    },
    //键盘事件
    keyboardFun(e) {
      let idx = this.jobItem.findIndex((item) => item.value == this.jobIndex);
      switch (e.keyCode) {
        case 40:
          //下键
          if (this.jobIndex == -1 || idx == this.jobItem.length - 1) {
            this.jobIndex = this.jobItem[0].value;
            idx = 0;
          } else {
            idx++;
            this.jobIndex = this.jobItem[idx].value;
          }
          break;
        case 38:
          //上建
          if (idx > 0) {
            idx--;
            this.jobIndex = this.jobItem[idx].value;
          }
          break;
        case 13:
          //回车
          if (idx !== -1) {
            let item = this.jobItem[idx];
            this.userInfo.jobType = item.value;
            if (item.value != '999999') {
              this.$refs.pulldownRef?.hidePanel();
              this.jobKeyword = '';
            } else {
              this.$nextTick(() => {
                let listItem = this.$refs.jobList.$el.querySelector('.other');
                let input = listItem?.querySelector('.jobType-input');
                input?.querySelector('input').focus();
              });
              if (
                !this.jobList.some(
                  (item) => item.label == this.userInfo.jobName
                ) &&
                this.userInfo.jobName != ''
              ) {
                return;
              }
            }
            this.userInfo.jobName = item.label;
          }
      }
      if (e.keyCode == 40 || e.keyCode == 38) {
        const jobList = this.$refs.jobList;
        jobList.scrollTo(0, idx * 34);
      }
    },
    filterMethod(val) {
      this.userInfo.companyCode = val;
      if (val.trim() == '') {
        this.companyList = dataUtils.deepCopy(this.companyListCopy);
      } else {
        this.companyList = this.companyListCopy.filter(
          (item) =>
            item.companyName.includes(val) || item.companyCode.includes(val)
        );
      }
    },
    companyVisibleChange(data) {
      if (data)
        this.companyList = JSON.parse(JSON.stringify(this.companyListCopy));
    },
    getCodeJobFirst(codeJobs) {
      if (codeJobs.length === 0) return null;
      let firstKey = null;
      for (const key in codeJobs) {
        if (codeJobs.hasOwnProperty(key)) {
          firstKey = key;
          break;
        }
      }
      return firstKey;
    }
  },
  watch: {
    G_peClsList: {
      handler(newVal) {
        if (this.G_config.physicalMode.includes('职检')) {
          this.peClsOptions = [
            {
              label: '职',
              options: [
                {
                  value: '职业检',
                  label: '职业检'
                }
              ]
            },
            {
              label: '普',
              options: [...newVal]
            }
          ];
        } else this.peClsOptions = newVal;
      },
      immediate: true
    },
    userInfo: {
      handler(n, o) {
        this.$parent.HisPatInfo = n;
        this.$parent.isOccupation = n.isOccupation;
        let jobStatus = n.jobStatus,
          totalYearsOfWork = n.totalYearsOfWork + ''.trim(),
          totalMonthsOfWork = n.totalMonthsOfWork + ''.trim();
        if (jobStatus && totalYearsOfWork && totalMonthsOfWork) {
          this.$parent.isCanSelectHarm = true;
        } else {
          this.$parent.isCanSelectHarm = false;
        }
        this.$parent.peCls = n.peCls;
      },
      deep: true
    },
    'userInfo.jobStatus': function (newVal, oldVal) {
      if (!this.jobStatusIsChange) return;
      this.jobStatusIsChange = false;
      if (!this.$parent?.clearOccupationPackages()) {
        this.userInfo.jobStatus = oldVal;
        return this.$message({
          message: '当前在岗状态下的套餐中存在已检查或已缴费项目!',
          type: 'warning',
          showClose: true
        });
      }
      if (
        this?.$parent?.checkHarmList.length > 0 &&
        typeof this.$parent.hazardOutCombination === 'function'
      )
        this.$parent.hazardOutCombination();
    },
    'userInfo.peCls': {
      handler(newVal, oldVal) {
        if (this.G_config.physicalMode.includes('职检')) {
          if (this.userInfo.companyCode && !this.personal)
            this.$parent.getSimpleCompanyTimes(false);
          if (newVal?.length == 0 && oldVal?.length == 2) {
            let condition =
              this.$parent.checktyPetype(true) &&
              this.$parent.checktyPetype(false);
            if (condition) {
              this.$parent.clearMealAndCombs();
            } else {
              this.$message({
                message: '已勾选的组合中存在已检查或已收费的项目!',
                type: 'warning',
                showClose: true
              });
              this.$nextTick(() => {
                this.userInfo.peCls = oldVal;
              });
            }
          } else if (newVal?.length < oldVal?.length && this.pullDownTrigger) {
            if (newVal[0] === '职业检') {
              //去掉普通勾选
              if (!this.$parent.checktyPetype(false)) {
                this.$message({
                  message: "已勾选'普通'的组合中存在已检查或已收费的项目!",
                  type: 'warning',
                  showClose: true
                });
                this.$nextTick(() => {
                  this.userInfo.peCls = oldVal;
                });
              }
            } else {
              this.resetOccupationForm();
              //去掉职业病勾选
              if (!this.$parent.checktyPetype(true)) {
                this.$message({
                  message: "已勾选'职业'的组合中存在已检查或已收费的的项目!",
                  type: 'warning',
                  showClose: true
                });
                this.$nextTick(() => {
                  this.userInfo.peCls = oldVal;
                });
              }
            }
          } else if (newVal.length === 0) {
            //清空已选套餐组合
            this.$parent.clearMealAndCombs();
          }
          this.userInfo.isOccupation = this.peClsOptions[0].options.some(
            (option) => newVal.includes(option.value)
          );

          if (this.G_peClsList.length === 0) {
            this.userInfo.isOrdinary = true;
          } else {
            this.userInfo.isOrdinary = this.peClsOptions[1].options.some(
              (option) => newVal.includes(option.value)
            );
          }
        } else {
          this.userInfo.isOrdinary = true;
          this.userInfo.isOccupation = false;
        }
        this.$parent.childUserInfo.isOccupation = this.userInfo.isOccupation;
        this.$parent.childUserInfo.isOrdinary = this.userInfo.isOrdinary;
        this.pullDownTrigger = false;
      }
    },
    'userInfo.companyCode': {
      handler(n, o) {
        this.getDepartList();
      },
      deep: true
    },
    'userInfo.sex': {
      handler(n, o) {
        if (n != o) {
          if (
            this.$parent.checkCombs.some(
              (item) =>
                item?.payStatus == 1 || item?.payStatus == 2 || item?.isChecked
            )
          ) {
            if (
              this.$parent.checkCombs.some(
                (item) =>
                  (item?.payStatus == 1 ||
                    item?.payStatus == 2 ||
                    item?.isChecked) &&
                  item?.sex != n &&
                  item?.sex != 0
              )
            ) {
              if (this.judgeGender) {
                this.$message({
                  message:
                    '已选组合中存在已缴费或已检查的项目且与当前性别互斥!',
                  type: 'warning',
                  showClose: true
                });
              }
            }
            this.judgeGender = true;
            return;
          }
          this.$parent.clearMealAndCombs();
          this.$parent.mealSearch();
          this.$parent.comboSearch();
        }
        this.judgeGender = true;
      },
      deep: true
    },
    'userInfo.regNo': {
      handler(n, o) {
        if (n != o) {
          this.headPortraitImg = '';
        }
      },
      deep: true
    },
    'userInfo.monitoringType': {
      handler(n, o) {
        if (n == 0) {
          this.userInfo.monitoringType = 1;
        }
      },
      deep: true
    }
  },
  async mounted() {
    this.getNativePlace();
    this.getOperator();
    this.getCompany();
    this.getGuidanceType();

    await this.getItems();

    this.userInfo.guidanceType = this.G_userInfo.systemParams.guidanceType;
    if (this.G_config.physicalMode.length === 1) {
      this.userInfo.peCls = this.G_config.physicalMode.includes('普检')
        ? 3
        : ['职业检'];
      this.userInfo.jobType = this.getCodeJobFirst(this.G_EnumList['CodeJob']);
    } else {
      this.peKey = Math.random();
      this.userInfo.peCls = [3];
    }
  }
};
</script>

<template>
  <el-form
    :model="userInfo"
    :rules="rules"
    ref="ruleForm"
    label-width="80px"
    class="demo-ruleForm"
  >
    <div class="info_bottom">
      <div class="userImg_wrap">
        <!-- <label v-if="personal">体检信息</label> -->
        <!-- <i
          v-if="personal"
          title="更新信息"
          class="iconfont icon-huifuxitongmoren"
          @click="updateReg"
          style="color: #089c66; font-size: 18px; cursor: pointer"
        ></i> -->
        <div class="portrait_div">
          <div
            class="demo-image__preview"
            @click="clipImg"
            style="overflow: hidden; height: 110px; width: 88px"
          >
            <video
              v-show="videoShow"
              ref="video"
              id="video"
              width="88"
              height="110"
              x5-video-player-fullscreen="true"
              x5-video-orientation="portraint"
            ></video>
            <el-image
              class="photo"
              style="width: 100%; height: 100%"
              :src="userInfo.photoUrl"
              v-if="!videoShow && userInfo.photoUrl"
            >
            </el-image>
            <el-image
              v-else
              v-show="!videoShow && !userInfo.photoUrl"
              style="width: 100%; height: 100%"
            >
              <div
                slot="error"
                class="image-slot"
                style="
                  width: 100%;
                  height: 100%;
                  align-content: center;
                  background-color: #f5f7fa;
                "
              >
                <span>暂未上传</span>
              </div>
            </el-image>
          </div>
          <div class="take_btn">
            <span
              type="text"
              size="small"
              class="search-btn"
              @click.stop="take_a_picture"
              style="
                color: #fff;
                text-align: center;
                vertical-align: middle;
                line-height: 24px;
                padding: 0;
              "
              >拍照</span
            >
            <canvas
              ref="canvas"
              id="canvas"
              width="200"
              height="250"
              style="display: none"
            ></canvas>
          </div>
        </div>
        <span
          class="activation-status"
          v-if="peStatus === false"
          style="color: #c0c0c0"
          >未激活</span
        >
        <span class="activation-status" v-else
          >{{ G_EnumList['PeStatus'][peStatus] }}
        </span>
      </div>
      <div class="formItem_wrap">
        <!-- 展开 -->
        <div class="unfold_wrap" style="flex-basis: 100%">
          <div class="" style="display: flex; align-items: center; flex: 1">
            <!-- <label>体检信息</label> -->
            <!-- <i
              title="更新信息"
              class="iconfont icon-huifuxitongmoren"
              @click="updateReg"
              style="color: #089c66; font-size: 18px; cursor: pointer"
            ></i> -->
            <div
              class="formItem_col unit"
              style="flex-basis: 42.4%; margin-left: 0px"
              v-if="!personal || G_config.register.personalCompany"
            >
              <el-form-item
                inline-message
                label="单位"
                prop="companyCode"
                :rules="[
                  personal
                    ? {}
                    : {
                        required: true,
                        message: '请选择单位',
                        trigger: 'change'
                      }
                ]"
              >
                <el-select
                  class="select"
                  v-model.trim="userInfo.companyCode"
                  @change="companyChange"
                  :placeholder="
                    G_config.register.unitCustomization
                      ? '请选择或创建单位'
                      : '请选择'
                  "
                  size="small"
                  width="100%"
                  filterable
                  :allow-create="G_config.register.unitCustomization"
                  :clearable="G_config.register.unitCustomization && personal"
                  :filter-method="filterMethod"
                  @visible-change="companyVisibleChange"
                  :disabled="$parent.userPeStatus !== 0"
                >
                  <el-option
                    v-for="(item, index) in companyList"
                    :key="index"
                    :value="item.companyCode"
                    :label="item.companyName"
                  ></el-option>
                </el-select>
              </el-form-item>
            </div>
            <!-- 部门 -->
            <div
              class="formItem_col unit"
              v-if="!personal || G_config.register.personalCompany"
              style="flex-basis: 38.8%"
            >
              <el-form-item label="部门" prop="companyDeptCode">
                <el-select
                  v-model.trim="userInfo.companyDeptCode"
                  :placeholder="
                    G_config.register.unitCustomization
                      ? '请选择或创建部门'
                      : '请选择'
                  "
                  size="small"
                  filterable
                  :allow-create="G_config.register.unitCustomization"
                  clearable
                >
                  <el-option
                    v-for="(item, index) in companyDeptList"
                    :key="index"
                    :label="item.deptName"
                    :value="item.deptCode"
                  ></el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>
          <span class="unfold_btn" @click="unfoldClick">
            展开信息
            <i
              :class="unfoldFlag ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
            ></i>
          </span>
        </div>
        <div class="formItem_col">
          <el-form-item inline-message label="体检分类" prop="peCls">
            <div v-if="!G_config.physicalMode.includes('普检')">
              <el-input size="small" disabled value="职业检"> </el-input>
            </div>
            <div style="display: flex" v-else>
              <vxe-select
                v-if="this.G_config.physicalMode.includes('职检')"
                :multi-char-overflow="6"
                class="select classification"
                style="width: 100%; height: 32px"
                v-model.trim="userInfo.peCls"
                placeholder="请选择"
                @change="peClsChange"
                multiple
                collapse-tags
                :key="peKey"
                size="small"
                :transfer="true"
                :option-groups="peClsOptions"
                clearable
              ></vxe-select>
              <el-select
                v-else
                v-model.trim="userInfo.peCls"
                filterable
                size="small"
                placeholder="请选择"
              >
                <el-option
                  v-for="(item, index) in peClsOptions"
                  :key="index"
                  :value="item.value"
                  :label="item.label"
                ></el-option>
              </el-select>
              <!-- <el-checkbox
                style="margin-right: 0; margin-left: 5px"
                v-model="userInfo.isOccupation"
                >职</el-checkbox
              > -->
            </div>
          </el-form-item>
        </div>
        <div class="formItem_col">
          <el-form-item inline-message label="档案号" prop="patCode">
            <el-input
              size="small"
              disabled
              v-model="userInfo.patCode"
            ></el-input>
          </el-form-item>
        </div>
        <div class="formItem_col">
          <el-form-item inline-message label="体检号" prop="regNo">
            <el-input size="small" disabled v-model="userInfo.regNo"></el-input>
          </el-form-item>
        </div>
        <div class="formItem_col">
          <el-form-item inline-message label="诊疗卡号" prop="hisCard">
            <el-input
              size="small"
              v-model="userInfo.hisCard"
              @keypress.enter.native="hisCardEnter"
            ></el-input>
          </el-form-item>
        </div>
        <div class="formItem_col">
          <el-form-item inline-message label="籍贯" prop="nativePlace">
            <el-select
              class="select"
              v-model.trim="userInfo.nativePlace"
              placeholder="请选择"
              size="small"
              width="100%"
              clearable
              filterable
              @keyup.enter.native.stop
            >
              <el-option
                v-for="(item, index) in nativePlaceList"
                :key="index"
                :value="item.natCode"
                :label="item.natName"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="formItem_col">
          <el-form-item inline-message label="姓名" prop="name">
            <el-input
              size="small"
              clearable
              v-model.trim="userInfo.name"
              minlength="2"
              @keyup.enter.native.stop="searchName"
            ></el-input>
          </el-form-item>
        </div>
        <div class="formItem_col">
          <el-form-item inline-message label="证件类型" prop="cardType">
            <el-select
              class="select"
              v-model.trim="userInfo.cardType"
              placeholder="请选择"
              size="small"
              width="100%"
              clearable
              filterable
              @keyup.enter.native.stop
            >
              <el-option
                v-for="(item, index) in G_cardType"
                :key="index"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="formItem_col form-item-minx">
          <el-form-item inline-message label="证件号码" prop="cardNo">
            <el-input
              size="small"
              v-model="userInfo.cardNo"
              @keyup.enter.native.stop="searchCard"
              @blur="blurCardNo"
              @input="handleCardNo"
            ></el-input>
          </el-form-item>
        </div>
        <div class="formItem_col">
          <el-form-item inline-message label="出生日期" prop="birthday">
            <el-date-picker
              class="width_100"
              v-model.trim="userInfo.birthday"
              type="date"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              @change="changeBirthday"
              size="small"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <div class="formItem_col">
          <el-form-item inline-message label="年龄" prop="age">
            <div style="display: flex">
              <el-input
                v-model.trim="userInfo.age"
                size="small"
                placeholder="请输入"
                onkeyup="this.value=this.value.replace(/\D|^/g,'')"
              ></el-input>
              <el-select
                class="select"
                v-model.trim="userInfo.ageUnit"
                placeholder="岁(月)"
                size="small"
                style="flex-basis: 50%; flex-shrink: 0"
                @keyup.enter.native.stop
              >
                <el-option
                  v-for="(item, index) in G_ageUnit"
                  :key="index"
                  :value="item.value"
                  :label="item.label"
                ></el-option>
              </el-select>
            </div>
          </el-form-item>
        </div>
        <div class="formItem_col">
          <el-form-item inline-message label="性别" prop="sex">
            <el-select
              class="select"
              v-model.trim="userInfo.sex"
              placeholder="请选择"
              size="small"
              width="100%"
              @keyup.enter.native.stop
            >
              <el-option
                v-for="(item, index) in G_sexList"
                :key="index"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="formItem_col">
          <el-form-item inline-message label="婚姻状况" prop="marryStatus">
            <el-select
              class="select"
              v-model.trim="userInfo.marryStatus"
              placeholder="请选择"
              size="small"
              width="100%"
              clearable
              @keyup.enter.native.stop
            >
              <el-option
                v-for="(item, index) in G_marriageStatus"
                :key="index"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="formItem_col">
          <el-form-item
            inline-message
            label="联系电话"
            prop="tel"
            :rules="
              telFormValidation
                ? { required: true, message: '请输入联系电话', trigger: 'blur' }
                : {}
            "
          >
            <el-input size="small" v-model="userInfo.tel"></el-input>
          </el-form-item>
        </div>
        <div class="formItem_col" style="flex-basis: 40%">
          <el-form-item inline-message label="联系地址" prop="address">
            <el-input size="small" v-model="userInfo.address"></el-input>
          </el-form-item>
        </div>
        <div class="formItem_col" v-if="C_isOccupation">
          <el-form-item inline-message label="工号">
            <el-input size="small" v-model="userInfo.jobId"></el-input>
          </el-form-item>
        </div>
        <div class="formItem_col" v-if="C_isOccupation">
          <el-form-item inline-message label="工种" prop="jobType">
            <el-select
              class="select"
              v-model="userInfo.jobType"
              placeholder="请选择"
              size="small"
              width="100%"
              filterable
              @keyup.enter.native.stop
            >
              <el-option
                v-for="item in Object.entries(G_EnumList['CodeJob']).map(
                  ([value, label]) => ({ value, label })
                )"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>

            <!-- <vxe-pulldown
              @hide-panel="hidePulldown"
              style="width: 100%"
              ref="pulldownRef"
              popup-class-name="jobType-dropdown"
              transfer
            >
              <template #default>
                <vxe-input
                  readonly
                  style="width: 100%"
                  v-model.trim="userInfo.jobName"
                  suffix-icon="vxe-icon-arrow-down"
                  placeholder="请选择或搜索"
                  @focus="focusEvent"
                ></vxe-input>
              </template>
              <template #header>
                <el-input
                  @input="changeJobType"
                  @focus="isFocused = true"
                  @blur="isFocused = false"
                  ref="jobSearchInput"
                  size="small"
                  v-model.trim="jobKeyword"
                  placeholder="搜索"
                >
                </el-input>
              </template>

              <template #dropdown>
                <div
                  v-if="!loading && jobItem.length === 0"
                  style="
                    width: 100%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                  "
                >
                  暂无数据
                </div>
                <div class="jobType-dropdown" style="width: 100%" v-else>
                  <vxe-list
                    ref="jobList"
                    :data="jobItem"
                    :scroll-y="{ enabled: true }"
                    style="height: 100%"
                    :loading="loading"
                  >
                    <template #default="{ items }">
                      <div
                        class="my-list-item"
                        :class="{
                          'list-hover': item.value == jobIndex,
                          other: item.value === '999999'
                        }"
                        @mouseenter="jobIndex = item.value"
                        v-for="(item, index) in items"
                        :key="index"
                        @click="selectJobType(item, $event)"
                      >
                        <div
                          :class="{
                            'is-active': item.value == userInfo.jobType
                          }"
                        >
                          {{ item.label }}
                        </div>
                        <el-input
                          v-show="
                            item.value === '999999' &&
                            item.value == userInfo.jobType
                          "
                          size="mini"
                          class="jobType-input"
                          style="margin-left: 20px; flex: 1"
                          v-model.trim="userInfo.jobName"
                          placeholder="请输入名称"
                        >
                        </el-input>
                      </div>
                    </template>
                  </vxe-list>
                </div>
              </template>
            </vxe-pulldown> -->
          </el-form-item>
        </div>
        <div class="formItem_col" v-if="C_isOccupation">
          <el-form-item inline-message label="在岗状态" prop="jobStatus">
            <el-select
              class="select"
              v-model.trim="userInfo.jobStatus"
              placeholder="请选择"
              size="small"
              width="100%"
              filterable
              @keyup.enter.native.stop
              @change="jobStatusChange"
              :disabled="$parent.userPeStatus >= 2"
            >
              <el-option
                v-for="(item, index) in G_CodeOccupationalPositionStatus"
                :key="index"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="formItem_col" v-if="C_isOccupation">
          <el-form-item inline-message label="总工龄" prop="totalYearsOfWork">
            <div style="display: flex">
              <el-input
                size="small"
                v-model="userInfo.totalYearsOfWork"
              ></el-input>
              年
              <el-form-item inline-message prop="totalMonthsOfWork">
                <el-input
                  size="small"
                  v-model="userInfo.totalMonthsOfWork"
                ></el-input>
              </el-form-item>
              月
            </div>
          </el-form-item>
        </div>
        <div class="formItem_col" v-if="C_isOccupation">
          <el-form-item inline-message label="监测类型" prop="monitoringType">
            <el-select
              class="select"
              v-model.trim="userInfo.monitoringType"
              placeholder="请选择"
              size="small"
              width="100%"
              filterable
              @keyup.enter.native.stop
            >
              <el-option
                v-for="(item, index) in monitorTypeList"
                :key="index"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="formItem_col" v-show="unfoldFlag">
          <el-form-item
            inline-message
            label=""
            style="display: flex"
            class="col_checkbox"
          >
            <el-checkbox
              label="领导"
              v-model.trim="userInfo.isLeader"
            ></el-checkbox>
            <!-- <el-checkbox
              label="复查"
              v-model.trim="userInfo.isRecheck"
            ></el-checkbox> -->
            <el-checkbox
              label="VIP"
              v-model.trim="userInfo.isVIP"
            ></el-checkbox>
          </el-form-item>
        </div>
        <div class="formItem_col" v-show="unfoldFlag">
          <el-form-item inline-message label="复查号" prop="recheckNo">
            <el-input size="small" v-model="userInfo.recheckNo"></el-input>
          </el-form-item>
        </div>
        <div class="formItem_col" v-show="unfoldFlag">
          <el-form-item inline-message label="登记日期" prop="registerTime">
            <el-date-picker
              class="width_100"
              v-model.trim="userInfo.registerTime"
              type="date"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              disabled
              size="small"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <div class="formItem_col" v-show="unfoldFlag" prop="registerTimes">
          <el-form-item inline-message label="登记次数">
            <el-input
              size="small"
              disabled
              v-model="userInfo.registerTimes"
            ></el-input>
          </el-form-item>
        </div>
        <div class="formItem_col" v-show="unfoldFlag">
          <el-form-item inline-message label="操作员">
            <!-- <el-input
              size="small"
              disabled
              v-model="userInfo.operatorCode"
            ></el-input> -->
            <el-select
              class="select"
              v-model.trim="userInfo.operatorCode"
              placeholder="请选择"
              size="small"
              width="100%"
              disabled
            >
              <el-option
                v-for="(item, index) in operatorList"
                :key="index"
                :value="item.operatorCode"
                :label="item.name"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="formItem_col" style="flex-basis: 20%" v-show="unfoldFlag">
          <el-form-item inline-message label="指引单" prop="guidanceType">
            <el-select
              class="select"
              v-model.trim="userInfo.guidanceType"
              placeholder="请选择"
              size="small"
              width="100%"
              filterable
              @keyup.enter.native.stop
            >
              <el-option
                v-for="(item, index) in guidanceTypeList"
                :key="index"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="formItem_col" v-show="unfoldFlag">
          <el-form-item inline-message label="预约类型" prop="bookType">
            <el-input size="small" disabled v-model="bookType"></el-input>
          </el-form-item>
        </div>
        <div class="formItem_col" style="flex-basis: 20%" v-show="unfoldFlag">
          <el-form-item inline-message label="介绍人" prop="introducer">
            <el-select
              class="select"
              v-model.trim="userInfo.introducer"
              placeholder="请选择"
              size="small"
              width="100%"
              clearable
              filterable
              @keyup.enter.native.stop
            >
              <el-option
                v-for="(item, index) in operatorList"
                :key="index"
                :value="item.operatorCode"
                :label="item.name"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="formItem_col" style="flex-basis: 40%" v-show="unfoldFlag">
          <el-form-item inline-message label="备注" prop="note">
            <el-input size="small" v-model="userInfo.note"></el-input>
          </el-form-item>
        </div>
      </div>
    </div>
    <el-dialog
      title="档案记录"
      :visible.sync="dialogTableVisible"
      append-to-body
      custom-class="dialogCss"
      width="1200px"
      @close="recordDialogClose"
    >
      <PublicTable
        :viewTableList.sync="dialogList"
        :theads.sync="theads"
        :columnWidth="columnWidth"
        :showOverflowTooltip="true"
        ref="patientTable"
        style="height: 600px"
        @rowDblclick="statusDblclick"
      >
        <template #name="{ scope }">
          <div class="sex_wrap">
            <i v-if="scope.row.sex == 1" class="el-icon-male"></i>
            <i v-if="scope.row.sex == 2" class="el-icon-female"></i>
            {{ scope.row.name }}
          </div>
        </template>
        <template #marryStatus="{ scope }">
          <div>
            {{ G_EnumList['MarryStatus'][scope.row.marryStatus] }}
          </div>
        </template>
        <template #birthday="{ scope }">
          <div>
            {{ dateDispose(scope.row) }}
          </div>
        </template>
      </PublicTable>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogTableVisible = false" size="small"
          >取 消</el-button
        >
      </div>
    </el-dialog>
    <!-- 拍照裁剪图片抽屉 -->
    <el-drawer
      title="照片编辑"
      :append-to-body="true"
      :visible.sync="clipDrawerShow"
      size="50%"
      @close="cancelClip"
      class="cropper_drawer"
    >
      <div style="padding: 10px; overflow: auto">
        <div class="preview_img" style="display: flex; overflow: auto">
          <div
            class="preview"
            style="
              margin-right: 10px;
              overflow: hidden;
              width: 200px;
              height: 250px;
              border: 1px solid #ccc;
            "
          >
            <el-image :src="previews.photoUrl" :style="previews.img"></el-image>
          </div>
          <div style="flex: 1; flex-shrink: 0">
            <video
              ref="drawerVideo"
              id="video"
              width="100%"
              height="250"
              x5-video-player-fullscreen="true"
              x5-video-orientation="portraint"
            ></video>
            <canvas
              ref="drawerCanvas"
              width="450"
              height="250"
              style="display: none"
            ></canvas>
          </div>
        </div>
        <div class="preview_btn" style="margin-bottom: 6px">
          <el-button
            size="small"
            class="blue_btn"
            icon="el-icon-camera-solid"
            @click="drawer_take_a_picture"
            >拍照</el-button
          >
          <el-button
            size="small"
            class="blue_btn"
            icon="iconfont icon-baocun"
            @click="confirmClip('blob')"
            >确定</el-button
          >
          <el-button size="small" @click="cancelClip">取消</el-button>
        </div>
        <div class="cropper">
          <vue-cropper
            ref="cropper"
            :img="option.img"
            :outputSize="option.outputSize"
            :outputType="option.outputType"
            :info="option.info"
            :canScale="option.canScale"
            :autoCrop="option.autoCrop"
            :autoCropWidth="option.autoCropWidth"
            :autoCropHeight="option.autoCropHeight"
            :fixed="option.fixed"
            :fixedNumber="option.fixedNumber"
            :full="option.full"
            :fixedBox="option.fixedBox"
            :canMove="option.canMove"
            :canMoveBox="option.canMoveBox"
            :original="option.original"
            :centerBox="option.centerBox"
            :height="option.height"
            :infoTrue="option.infoTrue"
            :maxImgSize="option.maxImgSize"
            :enlarge="option.enlarge"
            :mode="option.mode"
            @realTime="realTime"
            @imgLoad="imgLoad"
          >
          </vue-cropper>
        </div>
        <!--底部操作工具按钮-->
        <div class="footer-btn" style="margin-top: 10px">
          <div class="scope-btn">
            <label class="up_label blue_btn" for="uploads"
              ><i class="el-icon-upload"></i> 上传本地图片</label
            >
            <input
              type="file"
              id="uploads"
              ref="img_file"
              style="position: absolute; clip: rect(0 0 0 0)"
              accept="image/png, image/jpeg, image/gif, image/jpg"
              @change="selectImg($event)"
            />
            <el-button
              size="mini"
              type="danger"
              plain
              icon="el-icon-zoom-in"
              @click="changeScale(1)"
              >放大</el-button
            >
            <el-button
              size="mini"
              type="danger"
              plain
              icon="el-icon-zoom-out"
              @click="changeScale(-1)"
              >缩小</el-button
            >
            <el-button size="mini" type="danger" plain @click="rotateLeft"
              >↺ 左旋转</el-button
            >
            <el-button size="mini" type="danger" plain @click="rotateRight"
              >↻ 右旋转</el-button
            >
          </div>
          <!-- <div class="upload-btn">
            <el-button size="mini" type="success" @click="uploadImg('blob')">上传封面 <i class="el-icon-upload"></i></el-button>
          </div> -->
        </div>
      </div>
    </el-drawer>
  </el-form>
</template>

<style lang="less" scoped>
/deep/.my-select .el-select-dropdown__item {
  overflow: visible;
  display: block;
}
.unit {
  /deep/.el-select {
    width: 100%;
    // min-width: 450px;
  }
}
.demo-ruleForm {
  width: 100%;
  // max-height: 210px;
  // overflow: hidden;
  transition: all 0.2s ease-in-out;
  overflow-x: scroll;
  overflow-y: hidden;
  /deep/.el-form-item {
    margin-bottom: 0;
  }
  /deep/ .el-form-item__label {
    line-height: 35px;
  }
  /deep/ .el-form-item__content {
    line-height: 35px;
    .el-checkbox:nth-child(1) {
      margin-right: 10px;
    }
  }
  /deep/ .el-form-item__error {
    top: auto;
    left: -100px;
    bottom: -2px;
    width: 100px;
    text-align: right;
  }
  .info_bottom {
    display: flex;
    width: 100%;
    min-width: 1100px;
    .userImg_wrap {
      text-align: center;
      .portrait_div {
        // height: 140px;
        // width: 112px;
        border: 1px solid rgba(178, 190, 195, 1);
        border-radius: 4px;
        margin-bottom: 5px;
        position: relative;
        overflow: hidden;
        .take_btn {
          display: none;
          background: #1770df;
          width: 100%;
          cursor: pointer;
          position: absolute;
          bottom: 0;
          left: 0;
        }
        &:hover .take_btn {
          display: block;
        }
      }
      span {
        font-size: 14px;
        color: #089c66;
        font-weight: 500;
      }
    }
    .formItem_wrap {
      flex: 1;
      flex-shrink: 0;
      display: flex;
      flex-wrap: wrap;
    }
  }
  .formItem_col {
    /deep/.vxe-input.size--small {
      height: 32px;
    }
    /deep/.vxe-input--inner {
      height: 32px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    // transition: all 0.5s ease;
    flex-basis: 20%;
  }
  .form-item-minx {
    // min-width: 262px;
    // /deep/ .el-input__inner{
    //   padding: 5px;
    // }
  }
  .width_100 {
    width: 100%;
  }
  .unfold_wrap {
    display: flex;
    justify-content: space-between;
    label {
      margin-right: 10px;
      font-size: 16px;
      color: #2b3436;
      font-weight: 600;
    }
    .unfold_btn {
      color: #089c66;
      font-size: 14px;
      font-weight: 400;
      cursor: pointer;
    }
    .unfold_btn_show {
      opacity: 0;
    }
  }
}
.unfold_userInfo {
  // max-height: 300px;
}
.footer-btn {
  display: flex;
  justify-content: space-between;
}
/deep/.col_checkbox {
  .el-checkbox__label {
    padding-left: 3px;
  }
  /deep/label {
    margin-right: 0px !important;
  }
  /deep/.el-checkbox {
    margin-right: 5px;
    margin-right: 0 !important;
  }
}
/deep/.dialogCss {
  .el-dialog__body {
    padding: 0 15px !important;
  }
}

.photo {
  /deep/ .el-image__error {
    color: #40a5ff !important;
  }
}
.image-slot > span {
  color: #c0c4cc !important;
}
@media screen and(max-width: 1440px) {
  .demo-ruleForm {
    /deep/.el-form-item__label,
    /deep/.el-form-item__content {
      line-height: 26px !important;
    }
    .demo-image__preview {
      height: 80px !important;
      width: 64px !important;
    }
    .portrait_div {
      margin-bottom: 0 !important;
    }
  }
}
</style>
<style lang="less">
.cropper_drawer {
  .cropper {
    width: 100%;
    height: 300px;
  }
}
.cropper_drawer {
  .up_label {
    border: 1px solid #ccc;
    font-size: 12px;
    border-radius: 3px;
    line-height: 26px;
    padding: 0 15px;
    display: inline-block;
    vertical-align: middle;
    cursor: pointer;
    margin-right: 10px;
  }
}
/deep/ .classification {
  /deep/.el-select__input {
    margin: 0;
  }
}
.jobType-dropdown {
  background-color: #fff;
  box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.1);
  height: 267px;
  width: 240px;
  border-radius: 3px;
  .my-list-item {
    display: flex;
    height: 34px;
    font-size: 14px;
    padding: 0 15px;
    font-weight: 700px;
    line-height: 34px;
    cursor: pointer;
    // &:hover{
    //   background-color: #F5F7FA;
    // }
  }
  .list-hover {
    background-color: #f5f7fa;
  }
  .is-active {
    color: #409eff;
    font-weight: 700;
  }
  .vxe-list--virtual-wrapper {
    height: 100%;
  }
  .vxe-input--suffix-icon.vxe-icon-arrow-down {
    font-size: 12px;
  }
}
</style>
