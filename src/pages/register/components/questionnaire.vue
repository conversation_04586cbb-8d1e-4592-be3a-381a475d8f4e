<template>
  <!-- 个人登记问卷调查 -->
  <div class="questionnaire">
    <div class="top_form">
      <el-form ref="form" :model="formInfo" label-width="90px" :rules="rules">
        <el-row>
          <el-col :span="12">
            <el-form-item label="体检号" prop="regNo">
              <el-input
                v-model.trim="formInfo.regNo"
                size="small"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input
                v-model.trim="formInfo.name"
                size="small"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="性别" prop="sex">
              <el-input
                v-model.trim="formInfo.sex"
                size="small"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="年龄" prop="age">
              <el-input
                v-model.trim="formInfo.age"
                size="small"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="联系电话" prop="tel">
              <el-input
                v-model.trim="formInfo.tel"
                size="small"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="家族史" prop="familyMedicalHistory">
              <el-input
                type="textarea"
                v-model.trim="formInfo.familyMedicalHistory"
                size="small"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="既往病史" prop="pastMedicalHistory">
              <el-input
                type="textarea"
                v-model.trim="formInfo.pastMedicalHistory"
                size="small"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="手术状况" prop="operationStatus">
              <el-input
                type="textarea"
                v-model.trim="formInfo.operationStatus"
                size="small"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="吸烟习惯" prop="smokingHabit">
              <el-input
                type="textarea"
                v-model.trim="formInfo.smokingHabit"
                size="small"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="喝酒习惯" prop="drinkingHabit">
              <el-input
                type="textarea"
                v-model.trim="formInfo.drinkingHabit"
                size="small"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="生活习惯" prop="livingHabit">
              <el-input
                type="textarea"
                v-model.trim="formInfo.livingHabit"
                size="small"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="现在病况" prop="currentCondition">
              <el-input
                type="textarea"
                v-model.trim="formInfo.currentCondition"
                size="small"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="问卷答案" prop="questionnaireAnswer">
              <el-input
                v-model.trim="formInfo.questionnaireAnswer"
                type="textarea"
                size="small"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="end">
          <el-button @click="submitForm" style="margin-top: 10px">
            {{ questionnaireDisabled ? '提交' : '更新' }}
          </el-button>
        </el-row>
      </el-form>
    </div>
  </div>
</template>
<script>
export default {
  name: 'questionnaire',
  props: {
    formInfo: {
      type: Object,
      default: () => {
        return {};
      }
    },
    questionnaireDisabled: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      rules: {
        // tel: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
        // familyMedicalHistory: [{ required: true, message: '请输入家族史', trigger: 'blur' }],
        // pastMedicalHistory: [{ required: true, message: '请输入既往病史', trigger: 'change' }],
        // operationStatus: [{ required: true, message: '请输入手术状况', trigger: 'blur' }],
        // smokingHabit: [{ required: true, message: '请输入吸烟习惯', trigger: 'blur' }],
        // drinkingHabit: [{ required: true, message: '请输入喝酒习惯', trigger: 'blur' }],
        // livingHabit: [{ required: true, message: '请输入生活习惯', trigger: 'change' }],
        // currentCondition: [{ required: true, message: '请输入现在病况', trigger: 'blur' }],
        // questionnaireAnswer: [{ required: true, message: '请输入问卷答案', trigger: 'blur' }],
      }
    };
  },
  created() {},
  mounted() {},
  watch: {},
  computed: {},
  methods: {
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const {
            regNo,
            familyMedicalHistory,
            pastMedicalHistory,
            operationStatus,
            smokingHabit,
            drinkingHabit,
            livingHabit,
            currentCondition,
            questionnaireAnswer
          } = this.formInfo;

          const requestParams = {
            regNo,
            familyMedicalHistory,
            pastMedicalHistory,
            operationStatus,
            smokingHabit,
            drinkingHabit,
            livingHabit,
            currentCondition,
            questionnaireAnswer
          };
          this.$ajax
            .post(this.$apiUrls.UpdateQuestionData, requestParams)
            .then((r) => {
              let { success } = r.data;
              if (!success) {
                this.$message.error('提交失败，请稍后再试');
              } else {
                this.$message.success('提交成功');
                this.$emit('submitSuccess');
              }
            });
        } else {
          return false;
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
.questionnaire {
  .el-form-item {
    margin-bottom: 3px;
  }
  // /deep/.el-textarea.is-disabled .el-textarea__inner,
  // /deep/.el-input.is-disabled .el-input__inner {
  //   background-color: #fff;
  //   color: #000;
  // }
  // /deep/.el-form-item__label {
  //   color: #000;
  // }
}
</style>
