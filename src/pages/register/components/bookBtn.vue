<template>
  <div class="bookbtn">
    <div>
      <el-button
        size="small"
        class="yellow_btn"
        @click="imports"
        v-if="btnList.includes('导入文件')"
        icon="iconfont icon-daoru"
        >导入文件</el-button
      >
    </div>
    <div>
      <el-button
        size="small"
        class="yellow_btn"
        v-if="btnList.includes('正式登记')"
        icon="iconfont icon-shaojiandengji"
        @click="register"
        >正式登记</el-button
      >
      <el-button
        v-if="btnList.includes('新建')"
        size="small"
        icon="iconfont icon-xinjian"
        class="blue_btn"
        @click="create"
        >新建</el-button
      >
      <el-button
        size="small"
        class="red_btn"
        v-if="btnList.includes('删除')"
        icon="iconfont icon-shanchu"
        @click="deletes"
        >删除</el-button
      >
      <el-button
        size="small"
        class="blue_btn"
        v-if="btnList.includes('保存')"
        icon="iconfont icon-baocun"
        @click="save"
        >保存</el-button
      >
      <el-button
        size="small"
        class="green_btn"
        v-if="btnList.includes('打印')"
        icon="iconfont icon-dayin-"
        @click="prints"
        >打印</el-button
      >
    </div>
  </div>
</template>

<script>
export default {
  name: 'bookbtn',
  props: {
    btnList: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    //导入
    imports() {
      this.$emit('imports');
    },
    //删除
    deletes() {
      this.$emit('deletes');
    },
    //打印
    prints() {
      this.$emit('prints');
    },
    // 新建
    create() {
      this.$emit('create');
    },
    // 保存
    save() {
      this.$emit('save');
    },
    // 正式登记
    register() {
      this.$emit('register');
    }
  }
};
</script>
<style lang="less" scoped>
.bookbtn {
  display: flex;
  justify-content: space-between;
}
</style>
