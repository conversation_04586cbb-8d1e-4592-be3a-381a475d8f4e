<template>
  <div class="btn_group">
    <slot name="head"></slot>
    <el-button
      v-if="btnList.includes('身份证')"
      @click="idCard"
      class="yellow_btn"
      size="small"
      icon="iconfont icon-shenfenzheng"
      >身份证</el-button
    >
    <el-button
      v-if="btnList.includes('新建')"
      @click="create"
      class="blue_btn"
      size="small"
      icon="iconfont icon-xinjian"
      >新建 F1</el-button
    >
    <el-button
      v-if="btnList.includes('删除')"
      @click="del"
      class="red_btn"
      size="small"
      icon="iconfont icon-shanchu"
      >删除</el-button
    >
    <el-button
      v-if="btnList.includes('保存')"
      @click="save"
      class="blue_btn"
      size="small"
      icon="iconfont icon-baocun"
      >保存 F2</el-button
    >
    <el-button
      v-if="btnList.includes('问卷调查')"
      @click="questionnaire"
      class="blue_btn"
      size="small"
      icon="iconfont icon-baocun"
      >问卷调查</el-button
    >
    <el-button
      v-if="btnList.includes('导入文件')"
      @click="importBtn"
      class="yellow_btn"
      size="small"
      icon="iconfont icon-daoru"
      >导入文件</el-button
    >
    <el-button
      v-if="btnList.includes('打印')"
      @click="print"
      class="green_btn"
      size="small"
      icon="iconfont icon-dayin-"
      >打印</el-button
    >
    <el-checkbox
      v-if="btnList.includes('保存即预览')"
      v-model.trim="isPreview"
      style="margin-left: 18px"
      >保存即预览</el-checkbox
    >
    <slot name="footAdd"></slot>
  </div>
</template>

<script>
export default {
  name: 'btnGroup',
  props: {
    btnList: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  data() {
    return {
      isPreview: false
    };
  },
  methods: {
    //身份证
    idCard() {
      this.$emit('idCard');
    },
    // 新建
    create() {
      this.$emit('create');
    },
    // 删除
    del() {
      this.$emit('del');
    },
    // 保存
    save() {
      this.$emit('save', this.isPreview);
    },
    //问卷调查
    questionnaire() {
      this.$emit('questionnaire');
    },
    // 导入文件
    importBtn() {
      this.$emit('importBtn');
    },
    // 打印
    print() {
      this.$emit('print');
    },
    // 预览
    preview() {
      this.$emit('preview');
    }
  }
};
</script>
<style lang="less" scoped>
.btn_group {
  height: 48px;
  display: flex;
  align-items: center;
  .public_btn {
    color: #fff;
  }
}
@media screen and(max-width: 1440px) {
  .btn_group {
    height: 32px;
  }
}
</style>
<style lang="less"></style>
