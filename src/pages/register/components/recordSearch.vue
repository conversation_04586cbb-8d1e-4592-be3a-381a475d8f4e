<template>
  <div class="RecordSearch_page">
    <div class="back_wrap">
      <el-button
        @click="backBtn"
        type="text"
        icon="iconfont icon-fanhui"
        style="
          font-weight: 600;
          font-size: 18px;
          color: #1770df !important;
          background: #fff !important;
        "
        >返回
      </el-button>
      <span>记录查询</span>
    </div>
    <div style="overflow: scroll">
      <ul class="record_head" style="min-width: 1350px">
        <li class="head_li">
          <div class="every_inp">
            <label>体检号</label>
            <p style="width: 130px">
              <el-input
                @keyup.enter.native="search"
                v-model.trim="searchInfo.regNo"
                placeholder="请输入内容"
                size="mini"
                clearable
              ></el-input>
            </p>
          </div>
          <div class="every_inp">
            <label>档案卡号</label>
            <p style="width: 130px">
              <el-input
                @keyup.enter.native="search"
                v-model.trim="searchInfo.patCode"
                placeholder="请输入内容"
                size="mini"
                clearable
              ></el-input>
            </p>
          </div>
          <div class="every_inp user_eve">
            <div>
              <label>姓名</label>
              <p style="width: 130px">
                <el-input
                  @keyup.enter.native="search"
                  v-model.trim="searchInfo.name"
                  placeholder="请输入"
                  size="mini"
                  clearable
                ></el-input>
              </p>
            </div>
            <div>
              <label>性别</label>
              <p style="width: 84px">
                <el-select
                  @change="search"
                  v-model.trim="searchInfo.sex"
                  placeholder="请选择"
                  size="mini"
                  clearable
                >
                  <el-option
                    v-for="(item, index) in G_sexList"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </p>
            </div>
          </div>
          <div class="every_inp">
            <label>证件号</label>
            <p style="width: 150px">
              <el-input
                @keyup.enter.native="search"
                v-model.trim="searchInfo.cardNo"
                placeholder="请输入内容"
                size="mini"
                clearable
              ></el-input>
            </p>
          </div>
          <!-- </li>
        <li class="company_li"> -->

          <div class="every_inp" v-if="!isGroup">
            <label>单位</label>
            <p>
              <el-select
                @change="personalCompanyChange"
                v-model.trim="searchInfo.companyCode"
                :filter-method="filterMethodPerson"
                @visible-change="companyVisibleChange"
                placeholder="请选择"
                size="mini"
                clearable
                filterable
                style="width: 330px"
              >
                <el-option
                  v-for="item in companyList"
                  :key="item.companyCode"
                  :label="item.companyName"
                  :value="item.companyCode"
                >
                  <span style="float: left">{{ item.companyName }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{
                    item.companyCode
                  }}</span>
                </el-option>
              </el-select>
            </p>
          </div>
          <div class="every_inp" v-else>
            <label>单位</label>
            <p>
              <el-cascader
                style="width: 310px"
                ref="company_cascader_ref"
                v-model="searchInfo.companyCode"
                :options="companyList"
                :props="{ multiple: false }"
                clearable
                filterable
                size="mini"
                collapse-tags
                @change="companyChange"
                :filter-method="filterMethod"
              >
              </el-cascader>
            </p>
          </div>
          <div class="every_inp">
            <label style="min-width: 35px; margin-left: 8px">部门</label>
            <p>
              <el-select
                class="select"
                v-model.trim="companyDeptCode"
                placeholder="请选择"
                size="mini"
                filterable
                clearable
                :disabled="isHavue"
                @change="search"
                style="width: 100%"
              >
                <el-option
                  v-for="(item, index) in companyDeptList"
                  :key="index"
                  :label="item.deptName"
                  :value="item.deptCode"
                ></el-option>
              </el-select>
            </p>
          </div>
          <div class="every_inp">
            <label>套餐</label>
            <p>
              <el-select
                style="width: 100%; min-width: 150px"
                class="select"
                v-model.trim="clusCode"
                placeholder="请选择"
                size="mini"
                filterable
                clearable
                @change="search"
              >
                <el-option
                  v-for="(item, index) in clusterList"
                  :key="index"
                  :value="item.clusCode"
                  :label="item.clusName"
                ></el-option>
              </el-select>
            </p>
          </div>
          <div class="every_inp" v-if="G_config.physicalMode.includes('普检')">
            <label>体检分类</label>
            <p style="width: 95px">
              <el-select
                style="width: 100%"
                placeholder="请选择"
                size="mini"
                v-model="searchInfo.peCls"
                class="input"
                @change="search"
                clearable
              >
                <el-option
                  v-for="item in G_peClsList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </p>
          </div>
          <div class="every_inp">
            <label>预约类型</label>
            <p style="width: 95px">
              <el-select
                style="width: 100%"
                placeholder="请选择"
                size="mini"
                v-model="stateVal"
                class="input"
                @change="search"
              >
                <el-option label="全部" :value="-1"> </el-option>
                <el-option
                  v-for="item in G_bookType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </p>
          </div>
          <div class="every_inp">
            <label>检查状态</label>
            <p style="width: 100px">
              <el-select
                style="width: 100%"
                placeholder="请选择"
                size="mini"
                clearable
                v-model="searchInfo.peStatus"
                class="input"
                @change="search"
              >
                <el-option
                  v-for="item in G_peStatus"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </p>
          </div>
          <div class="every_inp">
            <label>指引单状态</label>
            <p style="width: 100px">
              <el-select
                @change="search"
                style="width: 100%"
                v-model.trim="searchInfo.guidanceStatus"
                placeholder="请选择"
                size="small"
              >
                <el-option
                  v-for="(item, index) in guidancePrint"
                  :key="index"
                  :label="item"
                  :value="index"
                >
                </el-option>
              </el-select>
            </p>
          </div>
          <div class="every_inp">
            <el-radio-group v-model.trim="searchInfo.isActive" @change="search">
              <el-radio :label="null">全部</el-radio>
              <el-radio :label="true">已激活</el-radio>
              <el-radio :label="false">未激活</el-radio>
            </el-radio-group>
          </div>

          <div class="every_inp">
            <!-- <label>登记时间</label> -->
            <label style="min-width: 110px">
              <el-select
                @change="search"
                v-model.trim="searchInfo.queryType"
                placeholder="请选择"
                size="mini"
                clearable
                style="width: 100px"
              >
                <el-option
                  v-for="item in timeType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option> </el-select
            ></label>
            <p>
              <el-date-picker
                :picker-options="{ shortcuts: G_datePickerShortcuts }"
                @change="search"
                v-model.trim="searchInfo.regTime"
                size="mini"
                clearable
                style="width: 260px"
                type="daterange"
                start-placeholder="开始日期"
                value-format="yyyy-MM-dd"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </p>
          </div>
          <el-button
            size="mini"
            class="blue_btn"
            icon="iconfont icon-search"
            style="width: 90px; margin-left: 30px"
            @click="search"
            >查询</el-button
          >
        </li>
        <!-- <li class="record_li">
          
          
        </li> -->
        <li class="operate_li">
          <div style="font-size: 14px">已选人数:{{ checkList.length }}</div>
          <div class="record_status">
            <label>记录数:{{ recordStatus.totalNumber }}</label>
            <label>未激活:{{ recordStatus.inActiveCount }}</label>
            <label>未检查:{{ recordStatus.unCheckedCount }}</label>
            <label>正在检查:{{ recordStatus.isCheckingCount }}</label>
            <label>已检:{{ recordStatus.checkedCount }}</label>
            <label>已审核:{{ recordStatus.approvedCount }}</label>
            <label>已发报告:{{ recordStatus.issuedReportCount }}</label>
          </div>
          <div>
            <el-button
              size="mini"
              class="blue_btn"
              icon="iconfont icon-jihuo"
              @click="activate"
              >激活</el-button
            >
            <el-button
              size="mini"
              class="yellow_btn"
              icon="iconfont icon-jihuo"
              @click="cancelAct"
              >取消激活</el-button
            >
            <el-button
              class="red_btn"
              size="mini"
              icon="iconfont icon-shanchu"
              @click="delBtnClick"
              >删除</el-button
            >
            <el-button
              class="violet_btn"
              size="mini"
              @click="recordDrawer = true"
              icon="iconfont icon-huifuxitongmoren"
              >恢复</el-button
            >
            <el-button
              class="violet_btn"
              size="mini"
              @click="exports"
              icon="iconfont icon-daochu"
              >导出</el-button
            >
            <el-button
              size="mini"
              class="red_btn"
              icon="el-icon-printer"
              @click="recordPrint"
              >打印</el-button
            >
            <el-button
              class="green_btn"
              size="mini"
              icon="el-icon-s-custom"
              @click="preview"
              >预览</el-button
            >
          </div>
        </li>
      </ul>
    </div>

    <div class="record_table">
      <PublicTable
        ref="record_Ref"
        :theads="theads"
        :url="$apiUrls.GetRegistersByMultipleFilterNew"
        :params="params"
        :elStyle="{
          border: true
        }"
        :tableDataMap="
          (data) => {
            return data.record;
          }
        "
        rowKey="regNo"
        remoteByPage
        :excelDataMap="excelDataMap"
        @request-success="responseDataForGetRegisters"
        @rowDblclick="backDbClick"
        @select="handleSelectionChange"
        @currentChange="currentChange"
        @selectionChange="selectionChange"
      >
        <template #peStatus="{ scope }">
          <el-popover
            placement="top"
            width="200"
            trigger="hover"
            popper-class="step-popover"
            @show="stepShow(scope.row.peStatus)"
          >
            <!-- 状态进程条 -->
            <div class="step">
              <div
                class="step-item"
                v-for="(item, index) in stepList"
                :key="index"
              >
                <div :class="item.className">
                  <div class="step-title">
                    {{ G_EnumList['PeStatus'][item.peStatus] }}
                  </div>
                  <div class="step-circle-container">
                    <div :class="item.icon"></div>
                  </div>
                  <div class="step-line"></div>
                </div>
              </div>
            </div>
            <div
              slot="reference"
              :class="peStatusFormat(scope.row.peStatus)"
              style="cursor: pointer"
            >
              {{ G_EnumList['PeStatus'][scope.row.peStatus] }}
            </div>
          </el-popover>
        </template>
        <template #sex="{ scope }">
          <div>
            {{ G_EnumList['Sex'][scope.row.sex] }}
          </div>
        </template>
        <template #transfer="{ scope }">
          <div>
            <el-checkbox
              v-model.trim="scope.row.transfer"
              disabled
            ></el-checkbox>
          </div>
        </template>
        <template #isActive="{ scope }">
          <div>
            <el-checkbox
              v-model.trim="scope.row.isActive"
              disabled
            ></el-checkbox>
          </div>
        </template>
        <template #isRecheck="{ scope }">
          <el-checkbox v-model="scope.row.isRecheck" disabled></el-checkbox>
        </template>
        <!-- <template #isCompanyCheck="{ scope }">
            <div>
              <el-checkbox v-model.trim="scope.row.isCompanyCheck" disabled></el-checkbox>
            </div>
          </template> -->
        <template #isVIP="{ scope }">
          <div>
            <el-checkbox v-model.trim="scope.row.isVIP" disabled></el-checkbox>
          </div>
        </template>
        <template #guidancePrinted="{ scope }">
          <div>
            <el-checkbox
              v-model.trim="scope.row.guidancePrinted"
              disabled
            ></el-checkbox>
          </div>
        </template>
        <template #peCls="{ scope }">
          <div>
            {{ G_EnumList['PeCls'][scope.row.peCls] }}
          </div>
        </template>
      </PublicTable>
    </div>
    <el-dialog
      title="选择激活时间"
      :close-on-click-modal="false"
      :visible.sync="displayActivation"
      width="300px"
      :show-close="false"
    >
      <!-- <div class=""> -->
      <!-- <label style="width: 100px">体检激活时间</label> -->
      <p style="margin: 0 10px">
        <el-date-picker
          v-model.trim="activeInfo.activeTime"
          placeholder="激活时间"
        >
        </el-date-picker>
      </p>
      <div slot="footer" class="dialog-footer">
        <el-button @click="displayActivation = false">取 消</el-button>
        <el-button type="primary" @click="activationDisplay">确 定</el-button>
      </div>
      <!-- </div> -->
    </el-dialog>
    <!-- 恢复记录抽屉 -->
    <el-drawer
      title="恢复记录"
      :visible.sync="recordDrawer"
      :wrapperClosable="false"
      :before-close="handleClose"
      size="70%"
    >
      <RecoveryRecordDrawer :isGroup="isGroup"></RecoveryRecordDrawer>
    </el-drawer>
    <!-- 打印 -->
    <PrintSelection
      ref="PrintSelection_Ref"
      :defaultCheck="true"
      :displaySwitches.sync="printDisplay"
    />
    <!-- 预览 -->
    <RegisterForThePreview
      ref="RegisterForThePreview_Ref"
      :displaySwitches.sync="previewDisplay"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import PublicTable from '../../../components/publicTable2.vue';
import RecoveryRecordDrawer from './recoveryRecordDrawer.vue';
import { dataUtils } from '../../../common';
import PrintSelection from '@/components/printSelection.vue';
import RegisterForThePreview from '@/components/registerForThePreviewV2.vue';

export default {
  name: 'RecordSearch',
  components: {
    PublicTable,
    RecoveryRecordDrawer,
    PrintSelection,
    RegisterForThePreview
  },
  computed: {
    ...mapGetters([
      'G_config',
      'G_EnumList',
      'G_sexList',
      'G_userInfo',
      'G_peStatus',
      'G_bookType',
      'G_peClsList',
      'G_datePickerShortcuts'
    ])
  },
  props: {
    isGroup: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      displayActivation: false,
      companyDeptList: [],
      companyDeptCode: '',
      companyDeptList: [],
      clusCode: '',
      clusterList: [],
      clusterLists: [],
      isHavue: true,
      previewDisplay: false,
      printDisplay: false,
      printerTypeList: [],
      recordDrawer: false,
      timeType: [
        {
          value: 1,
          label: '登记时间'
        },
        { value: 2, label: '体检时间' }
      ],
      guidancePrint: ['全部', '已打印', '未打印'],
      searchInfo: {
        regNo: '',
        patCode: '',
        companyCode: '',
        name: '',
        cardNo: '', //身份证
        sex: '',
        regTime: [dataUtils.getDate(), dataUtils.getDate()],
        queryType: 1,
        isActive: null,
        peStatus: '',
        peCls: '',
        guidanceStatus: 0
      },
      params: {},
      companyList: [],
      companyListCopy: [],
      companyVal: '',
      sexVal: '',
      registerDate: [],
      TJDate: [],
      stateVal: -1,
      printVal: [],
      theads: [
        {
          prop: 'bookType',
          label: '预约类型',
          align: '',
          width: '80',
          sortable: false
        },
        {
          prop: 'peStatus',
          label: '体检状态',
          align: '',
          width: '102',
          sortable: true
        },
        {
          prop: 'regNo',
          label: '体检号',
          align: '',
          width: '130',
          sortable: true
        },
        {
          prop: 'name',
          label: '姓名',
          align: '',
          width: '92',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'peCls',
          label: '体检分类',
          align: '',
          width: '80',
          sortable: false
        },
        {
          prop: 'sex',
          label: '性别',
          align: '',
          width: '75',
          sortable: true
        },
        {
          prop: 'age',
          label: '年龄',
          align: '',
          width: '75',
          sortable: true
        },
        {
          prop: 'clusPrice',
          label: '套餐价格',
          align: '',
          width: '102',
          sortable: false
        },
        {
          prop: 'clusName',
          label: '套餐名称',
          align: '',
          width: '150',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'payStatusName',
          label: '缴款状态',
          align: '',
          width: '80',
          sortable: false
        },
        {
          prop: 'patCode',
          label: '档案卡号',
          align: '',
          width: '102',
          sortable: true
        },
        {
          prop: 'isActive',
          label: '激活',
          align: '',
          width: '50',
          sortable: false
        },
        {
          prop: 'recheckNo',
          label: '复查号',
          align: '',
          width: '130px',
          sortable: true
        },
        {
          prop: 'isRecheck',
          label: '是否复查',
          align: 'center',
          width: '120px',
          sortable: true
        },
        {
          prop: 'isVIP',
          label: 'VIP',
          align: '',
          width: '50',
          sortable: false
        },
        {
          prop: 'guidancePrinted',
          label: '指引单',
          align: '',
          width: '65',
          sortable: false
        },
        {
          prop: 'guidancePrintTime',
          label: '指引单打印时间',
          align: '',
          width: '155',
          sortable: false
        },
        {
          prop: 'companyName',
          label: '工作单位',
          align: '',
          width: '',
          sortable: false,
          showOverflowTooltip: true
        },
        {
          prop: 'deptName',
          label: '部门',
          align: '',
          width: '',
          sortable: false,
          showOverflowTooltip: true
        }
      ],
      recordStatus: {
        totalNumber: 0,
        approvedCount: 0,
        checkedCount: 0,
        inActiveCount: 0,
        isCheckingCount: 0,
        issuedReportCount: 0,
        unCheckedCount: 0
      },
      checkList: [],
      currentRow: {},
      stepList: [],
      //激活
      activeInfo: {
        regNoArray: [], //体检号数组
        activeTime: new Date(), //"2022-07-25T03:41:51.684Z"
        activator: ''
      }
    };
  },
  methods: {
    //关闭弹出
    handleClose() {
      this.recordDrawer = false;
    },
    filterMethodPerson(val) {
      this.searchInfo.companyCode = val;
      if (val.trim() == '') {
        this.companyList = dataUtils.deepCopy(this.companyListCopy);
      } else {
        this.companyList = this.companyListCopy.filter(
          (item) =>
            item.companyName.includes(val) || item.companyCode.includes(val)
        );
      }
    },
    companyVisibleChange(data) {
      if (data)
        this.companyList = JSON.parse(JSON.stringify(this.companyListCopy));
    },
    // 获取单位列表
    getCompanyList() {
      const url = !this.isGroup
        ? this.$apiUrls.Company
        : this.$apiUrls.CompanyAndTimes;
      this.$ajax.post(url).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        if (this.isGroup) {
          this.companyList = returnData.map((item) => {
            return {
              value: item.companyCode,
              label: item.companyName,
              children: item.companyTimes.map((child) => {
                return {
                  value: child.companyTimes,
                  label: `${child.companyTimes}　${
                    dataUtils.subBlankDate(child.beginDate) || ''
                  }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                  item: child
                };
              })
            };
          });
        } else this.companyList = returnData;
        this.companyListCopy = dataUtils.deepCopy(this.companyList);
      });
    },
    //单位下拉变化
    companyChange(data) {
      this.companyDeptCode = '';
      this.companyTimesChange(data);
      if (!data || data.length === 0) {
        this.isHavue = true;
        this.searchInfo.regTime = [dataUtils.getDate(), dataUtils.getDate()];
        this.search();
        return;
      }
      this.getDepartList(data[0]);
      this.isHavue = false;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.searchInfo.regTime = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.search();
    },
    personalCompanyChange(data) {
      this.companyDeptCode = '';
      if (!data) {
        this.isHavue = true;
        this.search();
        return;
      }
      this.isHavue = false;
      this.getDepartList(data);
      this.search();
    },
    //获取部门数据
    getDepartList(companyCode) {
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanyDepartment, {
          companyCode: companyCode || '',
          deptCode: '',
          deptName: ''
        })
        .then((r) => {
          //console.log("r", r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.companyDeptList = returnData || [];
        });
    },
    //获取套餐：
    getCluster() {
      this.$ajax.post(this.$apiUrls.Cluster).then((r) => {
        this.clusterList = r.data.returnData;
        this.clusterLists = r.data.returnData;
      });
    },
    //次数改变时
    companyTimesChange(data) {
      this.clusCode = '';
      if (!data || data.length === 0) {
        this.clusterList = dataUtils.deepCopy(this.clusterLists);
        return;
      }
      this.$ajax
        .post(this.$apiUrls.ReadCompanyCandidateCluster, '', {
          query: {
            companyCode: data[0],
            companyTimes: data[1]
          }
        })
        .then(async (r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          let companyMeal = returnData || [];
          this.clusterList = companyMeal;
        });
    },
    /**
     * @author: justin
     * @description: 查询
     * @return {*}
     */
    search() {
      if (this.searchInfo.regTime == null) {
        this.searchInfo.regTime = [];
      }
      if (this.searchInfo.regNo) {
        this.searchInfo = {
          ...this.$options.data().searchInfo,
          regNo: this.searchInfo.regNo,
          patCode: this.searchInfo.patCode,
          regTime: []
        };
      }
      if (!this.searchInfo.regNo && this.searchInfo.regTime.length == 0) {
        this.$message({
          type: 'warning',
          message: '请选择搜索的时间范围！',
          showClose: true
        });
        return;
      }

      this.params = {
        patCode: this.searchInfo.patCode,
        regNo: this.searchInfo.regNo,
        name: this.searchInfo.name,
        sex: this.searchInfo.sex || -1,
        cardNo: this.searchInfo.cardNo,
        startTime: this.searchInfo.regTime[0] || '',
        endTime: this.searchInfo.regTime[1] || '',
        bookType: this.stateVal,
        peStatus:
          this.searchInfo.peStatus !== '' ? this.searchInfo.peStatus : -1,
        peCls: this.searchInfo.peCls !== '' ? this.searchInfo.peCls : -1,
        queryType: this.searchInfo.queryType || 0,
        isActive: this.searchInfo.isActive,
        guidanceStatus: this.searchInfo.guidanceStatus,
        companyDeptCode: this.companyDeptCode,
        isOccupation: this.G_config.physicalMode.includes('职检')
      };
      if (this.isGroup) {
        this.params.companyCode = this.searchInfo.companyCode?.[0] || '';
        this.params.companyTimes = this.searchInfo.companyCode?.[1] || -1;
        this.params.isCompanyCheck = true;
      } else this.params.companyCode = this.searchInfo.companyCode;
      this.$refs.record_Ref.loadData();
    },

    /**
     * @author: justin
     * @description: 获取登记记录响应回调
     * @param {*} data
     * @return {*}
     */
    responseDataForGetRegisters(data) {
      this.recordStatus = data.returnData.recordStatus || {};
      this.$set(this.recordStatus, 'totalNumber', data.totalNumber);
    },

    // 选择记录的回调
    selectionChange(selection, checkList) {
      this.checkList = checkList;
      this.activeInfo.regNoArray = [];
      selection.map((item) => {
        if (item.regNo != '') {
          this.activeInfo.regNoArray.push(item.regNo);
        }
      });
    },
    // 删除记录按钮点击回调
    delBtnClick() {
      if (this.checkList.length == 0 && !this.currentRow?.regNo) {
        this.$message({
          type: 'warning',
          message: '请选择记录！',
          showClose: true
        });
        return;
      }
      this.$confirm('是否确定删除选中的记录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          if (this.checkList.length == 0) {
            this.delRecord([this.currentRow]);
          } else {
            this.delRecord(this.checkList);
          }
        })
        .catch(() => {});
    },
    // 删除的记录
    delRecord(parameter) {
      console.log('🚀 ~ delRecord ~ parameter:', parameter);
      this.$ajax
        .post(
          this.$apiUrls.RecycleRegisterOrder + '/person',
          parameter.map((item) => {
            return item.regNo;
          })
        )
        .then((r) => {
          let { success } = r.data;
          if (!success) return;
          this.$message({
            type: 'success',
            message: '删除成功!',
            showClose: true
          });
          this.search();
        });
    },
    // 返回
    backBtn() {
      this.$parent.recordShow = false;
    },
    // 选中列表高亮
    currentChange(row) {
      console.log(row);
      this.currentRow = row;
    },
    handleSelectionChange(selection, row) {
      this.currentRow = row;
      this.$refs.record_Ref.$refs.tableCom_Ref.setCurrentRow(row);
    },
    //激活
    activate() {
      if (this.checkList.length < 1 && !this.currentRow?.regNo) {
        this.$message({
          message: '请先选择要激活的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.displayActivation = true;
    },
    activationDisplay() {
      this.activeInfo.activator = this.G_userInfo.codeOper.operatorCode;
      if (this.checkList.length < 1) {
        this.activeInfo.regNoArray = [this.currentRow?.regNo];
      }
      console.log('[ this.activeInfo ]-347', this.activeInfo);
      this.$ajax
        .post(this.$apiUrls.ActivationOrCancel + '/Active', this.activeInfo)
        .then((r) => {
          this.displayActivation = false;
          if (!r.data.success) {
            return;
          }
          this.search();
          this.$message({
            message: '激活成功',
            type: 'success',
            showClose: true
          });
        });
    },
    //取消激活
    cancelAct() {
      if (this.checkList.length < 1 && !this.currentRow?.regNo) {
        this.$message({
          message: '请先选择要取消激活的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.activeInfo.activator = this.G_userInfo.codeOper.operatorCode;
      console.log('[ this.activeInfo ]-584', this.activeInfo);
      if (this.checkList.length < 1) {
        this.activeInfo.regNoArray = [this.currentRow?.regNo];
      }
      this.$ajax
        .post(this.$apiUrls.ActivationOrCancel + '/Deactive', this.activeInfo)
        .then((r) => {
          if (!r.data.success) {
            return;
          }
          this.search();
          this.$message({
            message: '取消激活成功',
            type: 'success',
            showClose: true
          });
        });
    },
    // 打印
    recordPrint() {
      console.log(this.checkList);
      // console.log(this.currentRow);
      if (!this.checkList?.length > 0 && !this.currentRow?.regNo) {
        this.$message({
          message: '请先选择需要打印的体检信息！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let regNoList;
      if (!this.checkList?.length > 0) {
        regNoList = [
          {
            regNo: this.currentRow.regNo,
            isActive: this.currentRow.isActive
          }
        ];
      } else {
        regNoList = this.checkList.map((item) => {
          return {
            regNo: item.regNo,
            isActive: item.isActive
          };
        });
      }

      this.$refs.PrintSelection_Ref.setPrintTypeList(regNoList);
      this.printDisplay = true;
    },
    //预览
    preview() {
      if (!this.currentRow.regNo) {
        this.$message({
          message: '请选择要预览的用户!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$refs.RegisterForThePreview_Ref.setPrintTypeList(
        this.currentRow.regNo
      );
      this.previewDisplay = true;
    },
    //双击返回
    backDbClick(row) {
      this.$emit('rowDBclick', row);
      this.$parent.recordShow = false;
    },
    // 导出
    exports() {
      this.$refs.record_Ref.exportToExcel({
        fileName: '个人登记记录查询列表',
        title: '个人登记记录查询列表'
      });
    },
    /**
     * @author: justin
     * @description: 导出excel数据处理
     * @param {*} data
     * @return {*}
     */
    excelDataMap(data) {
      return data.map((item, index) => {
        item.peStatus = this.G_EnumList['PeStatus'][item.peStatus];
        item.sex = this.G_EnumList['Sex'][item.sex]; //性别
        item.isActive = item.isActive ? '已激活' : '未激活';
        item.isVIP = item.isVIP ? '是' : '否';
        item.peCls = this.G_EnumList['PeCls'][item.peCls];
        return item;
      });
    },
    // 状态进程条
    stepShow(val) {
      this.stepList = [];
      let stepOne = {};
      let stepTwo = {};
      let stepThree = {};
      if (val === this.G_peStatus.length - 1) {
        stepOne.peStatus = val - 2;
        stepOne.className = 'step-finish';
        stepOne.icon = 'step-circle';

        stepTwo.peStatus = val - 1;
        stepTwo.className = 'step-finish';
        stepTwo.icon = 'step-circle';

        stepThree.peStatus = val;
        stepThree.className = 'step-active';
        stepThree.icon = 'iconfont icon-zhifuchenggong icon';
      } else if (val === 0) {
        stepOne.peStatus = val;
        stepOne.className = 'step-active';
        stepOne.icon = 'iconfont icon-zhifuchenggong icon';

        stepTwo.peStatus = val + 1;
        stepTwo.className = 'step-default';
        stepTwo.icon = 'step-circle';

        stepThree.peStatus = val + 2;
        stepThree.className = 'step-default';
        stepThree.icon = 'step-circle';
      } else {
        stepOne.peStatus = val - 1;
        stepOne.className = 'step-finish';
        stepOne.icon = 'step-circle';

        stepTwo.peStatus = val;
        stepTwo.className = 'step-active';
        stepTwo.icon = 'iconfont icon-zhifuchenggong icon';

        stepThree.peStatus = val + 1;
        stepThree.className = 'step-default';
        stepThree.icon = 'step-circle';
      }
      this.stepList.push(stepOne, stepTwo, stepThree);
    },
    // 体检状态格式化
    peStatusFormat(key) {
      let className = '';
      switch (key) {
        case 0:
          className = 'cell_yellow';
          break;
        case 1:
          className = 'cell_blue';
          break;
        case 2:
          className = 'cell_green';
          break;
        case 3:
          className = '';
          break;
        case 4:
          className = 'cell_violet';
          break;
      }
      return className;
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    }
  },
  mounted() {
    this.getCompanyList();
    this.getCluster();
    this.search();
  },
  created() {
    removeEventListener('keyup', this.$parent.keyUp);
    removeEventListener('keydown', this.$parent.keyDown);
    removeEventListener('keyup', this.$parent.keyUpFun);
    removeEventListener('keydown', this.$parent.keyDownFun);
  },
  beforeDestroy() {
    addEventListener('keyup', this.$parent.keyUp);
    addEventListener('keydown', this.$parent.keyDown);
    addEventListener('keyup', this.$parent.keyUpFun);
    addEventListener('keydown', this.$parent.keyDownFun);
  }
};
</script>
<style lang="less" scoped>
.RecordSearch_page {
  background-color: #fff;
  height: 100%;
  overflow: auto;
  border-radius: 4px;
  padding: 5px 15px 15px;
  display: flex;
  flex-direction: column;
  overflow-x: haidden;

  /deep/.el-button--mini {
    padding: 6px 10px;
  }

  .back_wrap {
    // margin-bottom: 10px;

    span {
      font-size: 18px;
      font-weight: 600;
      border-left: 2px solid #001618;
      color: #001618;
      margin-left: 15px;
      padding-left: 15px;
      line-height: 19px;
      display: inline-block;
    }
  }

  .record_head {
    li {
      display: flex;
      margin-bottom: 5px;
      // justify-content: space-between;
    }

    .every_inp {
      display: flex;
      align-items: center;
      // width: 350px;
      margin-right: 10px;

      p {
        // flex: 1;
      }

      label {
        margin-right: 10px;
        font-size: 14px;
        min-width: fit-content;
        /* 最小宽度适应内容 */
        text-align: right;
      }

      // p {
      //   flex: 1;
      // }

      // &:nth-child(4) {
      //   flex: 1;
      // }
    }

    .head_li {
      //放不下换行
      flex-wrap: wrap;
      gap: 5px;
    }

    .record_li {
      height: 32px;
      align-items: center;
      padding-left: 20px;

      .el-radio {
        margin-right: 15px;
      }

      el-radio-group {
        margin-right: 30px;
      }
    }

    .operate_li {
      .record_status {
        margin-right: 28px;

        label {
          margin-left: 15px;
          font-size: 14px;
          min-width: fit-content;
          vertical-align: top;
          display: inline-block;
          font-weight: 600;
          margin-top: -3px;

          &:nth-child(3) {
            color: #d63031;
          }

          &:nth-child(4) {
            color: #fab63b;
          }

          &:nth-child(5) {
            color: #1770df;
          }

          &:nth-child(6) {
            color: #3cb34f;
          }

          &:nth-child(7) {
            color: #7364f4;
          }

          &:nth-child(8) {
            color: #3cb34f;
          }
        }
      }

      & > div {
        display: flex;
        align-items: center;
        // margin-right: 28px;
        padding: 5px;
        border-radius: 2px;

        &:nth-child(1) {
          background: rgba(23, 112, 223, 0.1);
          width: 150px;
        }

        // &:nth-child(3) {
        //   background: rgba(60, 179, 79, 0.1);
        // }

        &:nth-child(4) {
          background: rgba(214, 48, 49, 0.1);
        }
      }

      // & > div:nth-child(1) {
      //     background: rgba(23, 112, 223, 0.1);
      //     width: 150px;
      //   }

      //   & > div:nth-of-type(2) {
      //     background: rgba(60, 179, 79, 0.1);
      //     margin-left: auto;
      //   }

      //   & > div:nth-of-type(3) {
      //     background: rgba(214, 48, 49, 0.1);
      //     margin-left: auto;
      //     margin-right: 0;
      //   }
      // span {
      //   margin-left: 15px;
      //   font-size: 14px;
      //   vertical-align: top;
      //   display: inline-block;
      //   font-weight: 600;
      //   // margin-top: -5px;
      //   align-content: center;
      //   margin-inline-end: 10px;

      //   &:nth-child(3) {
      //     color: #d63031;
      //   }

      //   &:nth-child(4) {
      //     color: #fab63b;
      //   }

      //   &:nth-child(5) {
      //     color: #1770df;
      //   }

      //   &:nth-child(6) {
      //     color: #3cb34f;
      //   }

      //   &:nth-child(7) {
      //     color: #7364f4;
      //   }

      //   &:nth-child(8) {
      //     color: #3cb34f;
      //   }
      // }
    }
  }

  .record_table {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
  }
}

.user_eve {
  display: flex;

  & > div {
    display: flex;
    align-content: center;
  }

  label {
    margin-right: 10px;
    min-width: 35px !important;
    align-items: center;
    margin-left: 8px;
    display: flex;
    align-content: center;
  }
}
.step-popover {
  .step {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    position: relative;
  }
  .step-item {
    flex: 1;
    .step-default {
      color: #969799;
      position: relative;
    }
    .step-active {
      color: #1770df;
      position: relative;
      .icon {
        font-size: 14px;
      }
    }
    .step-finish {
      color: #1770df;
      position: relative;
      .step-circle {
        background: #1770df;
      }
      .step-line {
        background: #1770df;
      }
    }
    .step-title {
      margin-bottom: 6px;
      margin-left: 14px;
      transform: translateX(-50%);
    }
    .step-circle-container {
      background: #fff;
      position: absolute;
      top: 30px;
      left: -14px;
      z-index: 1;
      padding: 0 8px;
      background-color: #fff;
      transform: translateY(-50%);
    }
    .step-circle {
      width: 10px;
      height: 10px;
      background: #969799;
      border-radius: 50%;
    }
    .step-line {
      position: absolute;
      top: 30px;
      left: 0;
      width: 100%;
      height: 1px;
      background: #ebedf0;
      transition: background-color 0.3s;
    }
    &:first-child {
      .step-title {
        margin-left: 0;
        transform: none;
      }
      .step-circle-container {
        right: auto;
        left: -9px;
      }
    }
    &:last-child {
      position: absolute;
      right: 1px;
      width: auto;
      .step-title {
        margin-left: 0;
        transform: none;
      }
      .step-circle-container {
        right: -9px;
        left: auto;
      }
      .step-line {
        width: 0;
      }
    }
  }
}
</style>
