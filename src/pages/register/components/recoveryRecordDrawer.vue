<template>
  <!-- 恢复记录 -->
  <div class="recoveryRecordDrawer">
    <div class="headrCont">
      <div class="header-item">
        <span>单位</span>
        <el-cascader
          style="width: 200px"
          v-model="recoveryForm.companyCode"
          :options="companyList"
          :props="{ multiple: false }"
          clearable
          filterable
          size="mini"
          collapse-tags
          @change="companyChange"
          :filter-method="filterMethod"
        >
        </el-cascader>
      </div>
      <div class="header-item">
        <span>部门</span>
        <el-select
          class="select"
          style="width: 180px"
          v-model.trim="recoveryForm.companyDeptCode"
          placeholder="请选择"
          size="mini"
          filterable
          clearable
          :disabled="isHavue"
          @change="search"
        >
          <el-option
            v-for="(item, index) in companyDeptList"
            :key="index"
            :label="item.deptName"
            :value="item.deptCode"
          ></el-option>
        </el-select>
      </div>
      <div class="header-item">
        <span>删除时间</span>
        <el-date-picker
          :picker-options="{
            ...pickerOptions,
            shortcuts: G_datePickerShortcuts
          }"
          style="width: 220px"
          size="mini"
          v-model.trim="delTime"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          clearable
          @change="search"
        >
        </el-date-picker>
      </div>
      <div class="header-item">
        <el-input
          v-model.trim="recoveryForm.queryValue"
          size="small"
          placeholder="姓名/身份证/电话"
          style="width: 180px"
          clearable
        ></el-input>
      </div>
      <el-button
        size="small"
        icon="iconfont icon-search"
        class="blue_btn"
        @click="search"
        >查询</el-button
      >
      <el-button
        size="small"
        icon="iconfont icon-huifuxitongmoren"
        class="violet_btn"
        @click="regainRecord"
        >恢复</el-button
      >
      <el-button
        size="small"
        class="red_btn"
        icon="iconfont icon-shanchu"
        @click="delRecord"
        >彻底删除</el-button
      >
    </div>
    <div style="margin: 5px 15px">
      <span style="color: #1770df; font-weight: 600"
        >总数:{{ tableData.length }}
      </span>
      <span style="color: #2d3436; font-weight: 600; margin-left: 15px"
        >已选:{{ selArr.length }}
      </span>
    </div>
    <div class="tableCont">
      <PublicTable
        :viewTableList.sync="tableData"
        :theads.sync="theads"
        :tableLoading.sync="loading"
        :columnWidth="columnWidth"
        @select="handleSelRow"
        @selectAll="handleSelectAll"
        isCheck
      >
        <template #peCls="{ scope }">
          <div>
            {{ G_EnumList['PeCls'][scope.row.peCls] }}
          </div>
        </template>
        <template #peStatus="{ scope }">
          <div>
            {{ G_EnumList['PeStatus'][scope.row.peStatus] }}
          </div>
        </template>
        <template #sex="{ scope }">
          <div>
            {{ G_EnumList['Sex'][scope.row.sex] }}
          </div>
        </template>
        <template #isDelete="{ scope }">
          <div>
            <el-checkbox
              v-model.trim="scope.row.isDelete"
              disabled
            ></el-checkbox>
          </div>
        </template>
        <template #isActive="{ scope }">
          <div>
            <el-checkbox
              v-model.trim="scope.row.isActive"
              disabled
            ></el-checkbox>
          </div>
        </template>
        <template #isCompanyCheck="{ scope }">
          <div>
            <el-checkbox
              v-model.trim="scope.row.isCompanyCheck"
              disabled
            ></el-checkbox>
          </div>
        </template>
        <template #isVIP="{ scope }">
          <div>
            <el-checkbox v-model.trim="scope.row.isVIP" disabled></el-checkbox>
          </div>
        </template>
      </PublicTable>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { dataUtils } from '@/common';
import PublicTable from '@/components/publicTable.vue';
export default {
  name: 'recoveryRecordDrawer',
  components: { PublicTable },
  props: {
    // 是否团体
    isGroup: {
      type: Boolean,
      default: () => {
        return false;
      }
    }
  },
  data() {
    return {
      companyList: [],
      personOrCompany: '/person', //个人/团体
      loading: false,
      delTime: [dataUtils.getDate(), dataUtils.getDate()],
      recoveryForm: {
        // queryType: "",
        queryValue: '',
        delStartTime: '',
        delEndTime: '',
        companyDeptCode: '',
        companyCode: ''
      }, //查询条件
      companyDeptList: [],
      isHavue: true,
      tableData: [], //表单数据
      theads: {
        peStatus: '体检状态',
        isActive: '激活',
        patCode: '档案号',
        regNo: '体检号',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        isCompanyCheck: '团检',
        isVIP: 'VIP',
        peCls: '体检分类',
        companyName: '单位名称',
        deptName: '单位部门',
        isDelete: '删除标识',
        deleteTime: '删除时间'
      },
      columnWidth: {
        companyName: 180,
        deptName: 180,
        regNo: 150,
        patCode: 150,
        deleteTime: 150
      },
      selArr: [],
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() > Date.now();
        }
      }
    };
  },
  created() {},
  mounted: function () {
    this.$nextTick(() => {
      this.search();
      this.getCompanyList();
    });
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_datePickerShortcuts'])
  },
  methods: {
    getCompanyList() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        //console.log('🚀 ~ this.$ajax.post ~ r:', r);

        let { success, returnData } = r.data;
        if (!success) return;
        this.companyList = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${
                  dataUtils.subBlankDate(child.beginDate) || ''
                }　${dataUtils.subBlankDate(child.endDate) || ''}`,
                item: child
              };
            })
          };
        });
      });
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    //单位下拉变化
    companyChange(data) {
      if (!data || data.length === 0) {
        this.isHavue = true;
        this.delTime = [dataUtils.getDate(), dataUtils.getDate()];
        this.search();
        return;
      }
      this.getDepartList(data);
      this.isHavue = false;
      const currentlySelected = this.companyList
        .find((item) => item.value === data[0])
        .children.find((item) => item.value === data[1]).item;
      this.delTime = [
        currentlySelected.beginDate,
        currentlySelected.endDate || new Date().toISOString().slice(0, 10)
      ];
      this.search();
    },
    //查询公司部门信息
    getDepartList(companyCode) {
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanyDepartment, {
          companyCode: companyCode?.[0] || '',
          deptCode: '',
          deptName: ''
        })
        .then((r) => {
          //console.log("r", r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.companyDeptList = returnData || [];
        });
    },
    //查询
    search() {
      this.selArr = [];
      console.log(this.delTime);
      if (!this.delTime) {
        this.$message({
          message: '请先选择删除时间!',
          type: 'warning',
          showClose: true
        });
        this.recoveryForm.delStartTime = null;
        this.recoveryForm.delEndTime = null;
        return;
      } else {
        this.recoveryForm.delStartTime = this.delTime[0] || null;
        this.recoveryForm.delEndTime = this.delTime[1] || null;
      }
      this.isGroup == true
        ? (this.personOrCompany = '/company')
        : (this.personOrCompany = '/person');
      console.log('this.personOrCompany', this.personOrCompany);
      this.tableData = [];
      let params = {
        queryValue: this.recoveryForm.queryValue,
        delStartTime: this.recoveryForm.delStartTime,
        delEndTime: this.recoveryForm.delEndTime,
        companyDeptCode: this.recoveryForm.companyDeptCode || '',
        companyCode: this.recoveryForm.companyCode[0] || ''
      };
      this.$ajax
        .post(
          this.$apiUrls.GetRecycleRegisterOrderNew + this.personOrCompany,
          params
        )
        .then((r) => {
          //console.log("r", r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.tableData = returnData || [];
        });
    },
    //勾选行
    handleSelRow: function (val) {
      this.selArr = [];
      val.map((item) => {
        if (item.regNo != '') {
          this.selArr.push(item.regNo);
        }
      });
      console.log('[ this.selArr ]-558', this.selArr);
    },
    handleSelectAll(_, selection) {
      this.handleSelRow(selection);
    },
    //恢复记录
    regainRecord() {
      if (this.selArr.length < 1) {
        this.$message({
          message: '请先选择要恢复的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.isGroup == true
        ? (this.personOrCompany = '/company')
        : (this.personOrCompany = '/person');
      this.$ajax
        .post(
          this.$apiUrls.RestoreRegisterOrder + this.personOrCompany,
          this.selArr
        )
        .then((r) => {
          //console.log("r", r);
          let { success } = r.data;
          if (!success) return;
          this.$message({
            message: '恢复成功!',
            type: 'success',
            showClose: true
          });
          this.search();
        });
    },
    //删除记录
    delRecord() {
      if (this.selArr.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            console.log(this.selArr);
            this.isGroup == true
              ? (this.personOrCompany = '/company')
              : (this.personOrCompany = '/person');
            this.$ajax
              .post(
                this.$apiUrls.DeleteRegisterOrder + this.personOrCompany,
                this.selArr
              )
              .then((r) => {
                let { success } = r.data;
                if (!success) return;
                this.$message({
                  showClose: true,
                  message: '删除成功',
                  type: 'success'
                });
                this.search();
              });
          })
          .catch(() => {
            return;
          });
      } else {
        return this.$message({
          message: '请先勾选至少一条数据再进行操作！',
          type: 'warning',
          showClose: true
        });
      }
    }
  }
};
</script>

<style lang="less" scoped>
.recoveryRecordDrawer {
  display: flex;
  flex-direction: column;
  height: 100%;
  /deep/.el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #409eff !important;
    border-color: #409eff !important;
  }
  .headrCont {
    display: flex;
    align-items: center;
    overflow-y: auto;
    background: rgba(178, 190, 195, 0.2);
    border-radius: 4px;
    padding: 5px 15px;
    span {
      font-size: 14px;
      font-weight: 600;
      margin-right: 10px;
      white-space: nowrap;
    }
  }
  .header-item {
    display: flex;
    align-items: center;
    margin-right: 10px;
  }
  .selCol {
    display: flex;
    .el-select {
      width: 100px;
      padding-left: 20px;
    }
    .el-form-item {
      flex: 1;
    }
    /deep/.el-form-item__content {
      margin-left: 3px !important;
    }
  }
  /deep/.searchCol {
    .el-form-item__content {
      margin-left: 20px !important;
    }
  }
  /deep/.el-input__inner {
    height: 32px;
    line-height: 32px;
  }
  .tableCont {
    flex: 1;
    padding: 0px 15px 15px 15px;
  }
  .el-button {
    height: 32px;
  }
}
</style>
