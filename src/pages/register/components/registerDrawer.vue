<template>
  <div class="registerDrawer">
    <div class="headrCont">
      <div class="headerLeft">
        <el-form
          :model="searchForm"
          ref="form_ref"
          :rules="rules"
          label-width="80px"
        >
          <el-row>
            <el-col>
              <el-form-item label="体检号" prop="regNo ">
                <el-input
                  v-model.trim="searchForm.regNo"
                  size="small"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="档案卡号" prop="patCode ">
                <el-input
                  v-model.trim="searchForm.patCode"
                  size="small"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="单位" prop="company">
                <el-select
                  class="select"
                  v-model.trim="searchForm.company"
                  :filter-method="filterMethod"
                  @visible-change="companyVisibleChange"
                  placeholder="请选择"
                  size="small"
                  filterable
                >
                  <el-option
                    v-for="(item, index) in companyList"
                    :key="index"
                    :label="item.companyName"
                    :value="item.companyCode"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="登记时间" prop="djTime">
                <el-date-picker
                  :picker-options="{ shortcuts: G_datePickerShortcuts }"
                  v-model.trim="searchForm.djTime"
                  type="daterange"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  size="small"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-form-item label="姓名" prop="name">
                <el-input
                  v-model.trim="searchForm.name"
                  size="small"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="身份证" prop="idCard">
                <el-input
                  v-model.trim="searchForm.idCard"
                  size="small"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="性别" prop="sex">
                <el-select
                  class="select"
                  v-model.trim="searchForm.sex"
                  placeholder="请选择"
                  size="small"
                  filterable
                >
                  <el-option
                    v-for="(item, index) in sexList"
                    :key="index"
                    :label="item.marrStatusName"
                    :value="item.marrStatus"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="体检时间" prop="tjTime">
                <el-date-picker
                  :picker-options="{ shortcuts: G_datePickerShortcuts }"
                  v-model.trim="searchForm.tjTime"
                  type="daterange"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  size="small"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row class="titleCont">
            <el-col :span="7">
              <div class="titleRadio">
                <el-radio-group v-model.trim="radioVal">
                  <el-radio :label="1">预约</el-radio>
                  <el-radio :label="2">登记</el-radio>
                  <el-radio :label="3">全部</el-radio>
                </el-radio-group>
              </div>
            </el-col>
            <el-col class="titleDiv">
              <div style="color: #2d3436">记录数：{{ recordCount }}</div>
              <div style="color: #d63031">未激活数：{{ inactiveCount }}</div>
              <div style="color: #fab63b">未检查数：{{ inCheckCount }}</div>
              <div style="color: #1770df">正在检查数：{{ checkingCount }}</div>
              <div style="color: #3cb34f">已检完数：{{ checkedCount }}</div>
              <div style="color: #7364f4">已审核数：{{ auditedCount }}</div>
              <div style="color: #3cb34f">已发报告数：{{ sendCount }}</div>
            </el-col>
          </el-row>
        </el-form>
        <div class="centerCont">
          <div class="titleCheckbox">
            <el-checkbox-group
              v-model.trim="checkedVal"
              @change="handleCheckedValChange"
            >
              <el-checkbox
                v-for="item in checkList"
                :label="item"
                :key="item"
                >{{ item }}</el-checkbox
              >
            </el-checkbox-group>
            <el-button
              type="success"
              size="small"
              class="print-btn"
              @click="print"
              >打印</el-button
            >
            <div>
              <span class="number-text">数量</span>
              <el-input
                v-model.trim="number"
                size="small"
                class="amount"
                onkeyup="value=value.replace(/[^1-9]/g,'')"
              ></el-input>
            </div>
          </div>
          <div class="titleBtn">
            <div class="btnTxt">
              <el-button
                type="text"
                size="small"
                class="del-btn"
                style="color: #d63031"
                @click="delRecord"
                >删除记录</el-button
              >
              <el-button
                type="text"
                size="small"
                class="search-btn"
                style="color: #1770df"
                @click="regainRecord"
                >恢复记录</el-button
              >
            </div>
          </div>
        </div>
      </div>
      <div class="headerRight">
        <el-button
          type="primary"
          size="small"
          class="search-btn"
          @click="search"
          >查询</el-button
        >
        <el-button size="small" class="yellow">身份证</el-button>
        <el-button size="small" class="default" @click="cancel">关闭</el-button>
      </div>
    </div>

    <div class="tableCont">
      <PublicTable
        :viewTableList.sync="tableData"
        :theads.sync="theads"
        :tableLoading.sync="loading"
        :columnWidth="columnWidth"
      >
        <template #state="{ scope }">
          <div>
            {{ enum_abnormity[scope.row.state] }}
          </div>
        </template>
        <template #sex="{ scope }">
          <div>
            {{ enum_abnormity2[scope.row.sex] }}
          </div>
        </template>
        <template #transfer="{ scope }">
          <div>
            <el-checkbox v-model.trim="scope.row.transfer"></el-checkbox>
          </div>
        </template>
        <template #activate="{ scope }">
          <div>
            <el-checkbox v-model.trim="scope.row.activate"></el-checkbox>
          </div>
        </template>
        <template #group="{ scope }">
          <div>
            <el-checkbox v-model.trim="scope.row.group"></el-checkbox>
          </div>
        </template>
        <template #vip="{ scope }">
          <div>
            <el-checkbox v-model.trim="scope.row.vip"></el-checkbox>
          </div>
        </template>
        <template #isCheck="{ scope }">
          <div>
            {{ enum_abnormity3[scope.row.isCheck] }}
          </div>
        </template>
      </PublicTable>
    </div>
  </div>
</template>

<script>
import PublicTable from '../../../components/publicTable.vue';
import { mapGetters } from 'vuex';
export default {
  name: 'registerDrawer',
  components: { PublicTable },
  props: {
    cancel: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      loading: false,
      companyList: [], //单位
      companyListCopy: [], //单位
      sexList: [], //性别
      searchForm: {}, //查询条件
      isEnabled: '',
      checkList: ['指引单', '检验标签', '体检条码'],
      tableData: [], //表单数据
      radioVal: '', //单选框勾选
      recordCount: '', //记录数
      inactiveCount: '', //未激活数
      inCheckCount: '', //未检查数
      checkingCount: '', //正在检查数
      checkedCount: '', //已检完数
      auditedCount: '', //已审核数
      sendCount: '', //已发报告数
      checkedVal: [], //复选框勾选
      number: '', //数量
      theads: {
        state: '状态',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        transfer: '传输',
        activate: '激活',
        group: '团体',
        vip: 'VIP',
        tjCls: '体检分类',
        tjNo: '体检序号',
        isCheck: '检查已否',
        groupPackage: '套餐分组',
        company: '工作单位',
        depart: '部门',
        serialNo: '体检号',
        patCode: '档案卡号'
      },
      enum_abnormity: {
        1: '登记',
        2: '预约'
      },
      enum_abnormity2: {
        0: '通用',
        1: '男',
        2: '女'
      },
      enum_abnormity3: {
        true: '已检',
        false: '未检'
      },
      columnWidth: {
        groupPackage: 100,
        company: 180,
        depart: 180,
        serialNo: 150,
        patCode: 150
      },
      rules: {}
    };
  },
  computed: {
    ...mapGetters(['G_datePickerShortcuts'])
  },
  created() {},
  mounted: function () {
    this.getCompany();
    this.getSex();
    this.tableData = [
      {
        state: '1',
        name: '陈晨飞',
        sex: '1',
        age: '36',
        transfer: false,
        activate: true,
        group: false,
        vip: false,
        tjCls: '入职',
        tjNo: '1',
        isCheck: true,
        groupPackage: '司机',
        company: '德玛西亚',
        depart: '技术工管部',
        serialNo: '220622060001',
        patCode: '********'
      }
    ];
  },
  methods: {
    handleCheckedValChange(value) {
      console.log(value);
    },
    //获取单位下拉
    getCompany() {
      this.$ajax.post(this.$apiUrls.Company).then((r) => {
        this.companyList = r.data.returnData;
        this.companyListCopy = JSON.parse(JSON.stringify(r.data.returnData));
      });
    },
    filterMethod(val) {
      this.searchForm.company = val;
      if (val.trim() == '') {
        this.companyList = JSON.parse(JSON.stringify(this.companyListCopy));
      } else {
        this.companyList = this.companyListCopy.filter(
          (item) =>
            item.companyName.includes(val) || item.companyCode.includes(val)
        );
      }
    },
    companyVisibleChange(data) {
      if (data)
        this.companyList = JSON.parse(JSON.stringify(this.companyListCopy));
    },
    //获取性别下拉
    getSex() {
      this.$ajax.post(this.$apiUrls.Sex).then((r) => {
        this.sexList = r.data.returnData;
      });
    },
    //查询
    search() {},
    //打印
    print() {},
    //删除记录
    delRecord() {},
    //恢复记录
    regainRecord() {}
  }
};
</script>

<style lang="less" scoped>
.registerDrawer {
  display: flex;
  flex-direction: column;
  height: 100%;
  .headrCont {
    display: flex;
    flex-direction: row;
    background: rgba(178, 190, 195, 0.2);
    border-radius: 4px;
    padding: 15px;
    .el-row {
      display: flex;
    }
    .el-form-item {
      margin-bottom: 10px;
      /deep/ .el-form-item__label,
      /deep/.el-form-item__content {
        height: 32px;
        line-height: 32px;
      }
    }
    .headerLeft {
      flex: 1;
      /deep/.el-input__inner {
        height: 32px;
        line-height: 32px;
      }
      /deep/.el-date-editor.el-input,
      .el-date-editor.el-input__inner {
        max-width: 220px;
      }
      .titleCont {
        line-height: 32px;
        display: flex;
        margin-left: 0 !important;
        padding-bottom: 10px;
        .titleRadio {
          padding-left: 20px;
          background: #fff;
          /deep/.el-radio {
            margin-right: 15px;
            /deep/.el-radio__inner {
              width: 18px;
              height: 18px;
            }
          }
        }
        .titleDiv {
          display: flex;
          background: #fff;
          margin-left: 20px;
          padding-left: 20px;
          div {
            flex: 1;
          }
        }
      }
    }
    .headerRight {
      width: 180px;
      display: flex;
      flex-direction: column;
      .el-button {
        width: 70px;
        margin: auto;
        margin-top: 0;
      }
    }
  }
  .centerCont {
    display: flex;
    flex-direction: row;
    width: 100%;
    height: 48px;
    line-height: 48px;
    border-radius: 4px;

    .titleCheckbox {
      display: flex;
      background: rgba(60, 179, 79, 0.1);
      border-radius: 2px;
      padding-left: 20px;
      .print-btn {
        margin: 0 20px;
        height: 32px;
        margin-top: 8px;
      }
    }
    .amount {
      margin: 0 20px 0 10px;
      height: 32px;
      width: 76px;
    }
    .btnTxt {
      background: rgba(214, 48, 49, 0.1);
      padding: 0 20px;
      border-radius: 2px;
      border-radius: 2px;
      margin-left: 40px;
      .del-btn {
        margin-right: 30px;
      }
    }
  }
  .tableCont {
    flex: 1;
    padding: 15px;
  }
  .select {
    width: 100%;
  }
  /deep/.el-checkbox__inner {
    width: 18px;
    height: 18px;
  }
  .yellow {
    background: #fab63b;
    color: #fff;
  }
  .default {
    background: #fff;
    color: #000;
  }
}
</style>
