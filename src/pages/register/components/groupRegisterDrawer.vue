<template>
  <div class="groupRegisterDrawer">
    <div class="content_wrap">
      <div class="right_wrap">
        <!-- 套餐和组合 -->
        <div class="combo_group">
          <el-tabs
            type="border-card"
            v-model="tabActiveName"
            stretch
            class="tabs_wrap"
          >
            <el-tab-pane label="登记信息" name="info">
              <LeftCont
                isShow
                ref="form_Ref"
                :disposeRecordData="disposeRecordData"
                @timesChange="timesChange"
                @initData="createBtn"
                class="groupLeft"
              />
            </el-tab-pane>
            <el-tab-pane label="体检套餐" name="mealTab">
              <div class="head_search">
                <div class="head_search_div">
                  <el-input
                    v-model.trim="mealSearchVal"
                    clearable
                    size="small"
                    @input="mealSearch"
                    @keypress.enter.native="mealSearch"
                    ref="mealSearch_Ref"
                    placeholder="请输入套餐名称搜索"
                  ></el-input>
                </div>
                <!-- <el-button type="primary" icon="iconfont icon-search" size="small" class="blue_btn">搜索</el-button> -->
              </div>
              <div class="table_wrap">
                <PublicTable
                  :viewTableList="searchMealList"
                  :theads="mealTheads"
                  :isSortShow="false"
                  :showHeader="false"
                  :cell_red="cell_red"
                  :columnWidth="columnWidth"
                  @rowDblclick="mealDBClick"
                  @currentChange="mealCurrentChange"
                  ref="mealTable_Ref"
                >
                  <template #columnLeft>
                    <el-table-column fixed="left" width="40">
                      <template slot-scope="scope">
                        <i
                          class="iconfont icon-xuanzhong"
                          v-if="scope.row.isSelected"
                        ></i>
                        <span v-if="!scope.row.isSelected"></span>
                      </template>
                    </el-table-column>
                  </template>
                  <template #price="{ scope }">
                    <div style="text-align: right">
                      {{ handlePrice(scope.row.price) }}
                    </div>
                  </template>
                </PublicTable>
              </div>
            </el-tab-pane>
            <el-tab-pane label="体检组合" name="combTab">
              <div class="head_search">
                <div class="head_search_div">
                  <el-select
                    @change="comboSearch"
                    clearable
                    v-model="combTypeVal"
                    placeholder="请选择"
                    style="width: 100px; margin-right: 5px"
                  >
                    <el-option
                      v-for="item in combTypeList"
                      :key="item.clsCode"
                      :label="item.clsName"
                      :value="item.clsCode"
                    >
                    </el-option>
                  </el-select>
                  <el-input
                    v-model.trim="comboSearchVal"
                    clearable
                    @input="comboSearch"
                    @keypress.enter.native="comboSearch"
                    size="small"
                    ref="combSearch_Ref"
                    style="flex: 1; flex-shrink: 0"
                    placeholder="请输入组合名称和代码搜索"
                  ></el-input>
                </div>
                <!-- <el-button type="primary" icon="iconfont icon-search" size="small" class="blue_btn">搜索</el-button> -->
              </div>
              <div class="table_wrap">
                <PublicTable
                  :viewTableList="searchComboList"
                  :theads="comboMealTheads"
                  :showHeader="false"
                  :cell_red="comboCell_red"
                  :columnWidth="comboColumnWidth"
                  :isSortShow="false"
                  @rowDblclick="comboDBClick"
                  @currentChange="combCurrentChange"
                  ref="combTable_Ref"
                >
                  <template #columnLeft>
                    <el-table-column fixed="left" width="40">
                      <template slot-scope="scope">
                        <i
                          class="iconfont icon-xuanzhong"
                          v-if="scope.row.isSelected"
                        ></i>
                        <span v-if="!scope.row.isSelected"></span>
                      </template>
                    </el-table-column>
                  </template>
                  <template #combName="{ scope }">
                    <div>
                      {{ `（${scope.row.combCode}）${scope.row.combName}` }}
                    </div>
                  </template>
                  <template #price="{ scope }">
                    <div style="text-align: right">
                      {{ handlePrice(scope.row.price) }}
                    </div>
                  </template>
                  <template #columnRight>
                    <el-table-column label="组合明细" width="50px">
                      <template slot-scope="scope">
                        <span
                          style="color: #1770df; cursor: pointer"
                          @click="lookDetail(scope.row)"
                          >查看</span
                        >
                      </template>
                    </el-table-column>
                  </template>
                </PublicTable>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
        <!-- 已选套餐和组合 -->
        <div class="check_combo_wrap">
          <header class="global_head">
            <div class="idCardBtn_wrap">
              <BtnGroup
                :btnList="['身份证', '新建', '保存']"
                @save="save"
                @create="createBtn"
              />
            </div>
          </header>
          <!-- 已选套餐 -->
          <div class="meal_div meal_combo_style">
            <header>
              <p>已选套餐：</p>
              <div class="meal_tag_wrap">
                <el-tag
                  v-for="tag in checkMealList"
                  :key="tag.clusName"
                  closable
                  class="checkMeal_tag"
                  @close="delMeal(tag)"
                >
                  {{ tag.clusName }}
                  <span>￥{{ tag.price }}</span>
                </el-tag>
              </div>
            </header>
          </div>
          <!-- 已选组合 -->
          <div class="combo_div meal_combo_style">
            <header>
              <p>已选组合：{{ checkComboList.length }}</p>
              <p>
                <el-button
                  type="text"
                  style="color: #1770df"
                  icon="iconfont icon-fuzhi"
                  >复制
                </el-button>
                <el-button
                  type="text"
                  @click="personDelCombo"
                  style="color: #d63031"
                  icon="iconfont icon-shanchu"
                  >删除</el-button
                >
              </p>
            </header>
            <div class="combo_table">
              <PublicTable
                ref="checkCombo_Ref"
                :viewTableList="checkComboList"
                :theads="groupCheckComboTheads"
                :tableRowClassName="tableRowClassName"
                :columnWidth="checkComboColumnWidth"
                :columnAlign="{
                  price: 'right',
                  testTubes: 'right',
                  originalPrice: 'right'
                }"
                :cell_red="checkComboCell_red"
                :isStripe="false"
                :isSortShow="false"
                :isOpenPage="false"
                isCheck
                :headerCellStyle="{
                  background: '#fff',
                  fontSize: '14px',
                  color: 'rgba(45,52,54,.6)'
                }"
              >
                <!-- <template #count="{ scope }">
                    <div>
                      <el-input
                        size="mini"
                        type="number"
                        class="count_input"
                        v-model.trim="scope.row.count"
                      ></el-input>
                    </div>
                  </template> -->
                <template #discount="{ scope }">
                  <el-input-number
                    size="mini"
                    @change="discountChange(scope.row)"
                    v-model="scope.row.discount"
                    controls-position="right"
                    :precision="2"
                    :min="0.01"
                    :step="0.01"
                    :max="1"
                  ></el-input-number>
                </template>
                <template #price="{ scope }">
                  {{ handlePrice(scope.row.price) }}
                </template>
                <template #testTubes="{ scope }">
                  <div class="testTubes_div" v-if="!!scope.row.testTubes">
                    <el-popover
                      placement="top-start"
                      width="200"
                      trigger="hover"
                    >
                      <div
                        v-for="(item, idx) in scope.row.testTubes.testTubes"
                        :key="idx"
                      >
                        {{ item.combName + ' *' + item.discount }}
                      </div>
                      <div slot="reference">
                        <span>
                          {{
                            scope.row.testTubes.testTubesPrice
                              ? handlePrice(scope.row.testTubes.testTubesPrice)
                              : ''
                          }}
                        </span>
                      </div>
                    </el-popover>
                  </div>
                </template>
                <!-- <template #isPayBySelf="{ scope }">
                  <div>
                    <el-checkbox
                      :disabled="containComboCode.includes(scope.row.combCode)"
                      v-model="scope.row.isPayBySelf"
                      >自费</el-checkbox
                    >
                  </div>
                </template> -->
                <template #payStatus="{ scope }">
                  <div
                    :class="
                      scope.row.payStatus == 0 ? 'noPay_text' : 'havePay_text'
                    "
                  >
                    {{ G_EnumList['PayStatus'][scope.row.payStatus] }}
                  </div>
                </template>
              </PublicTable>
            </div>
            <div class="price_wrap">
              <div class="project_price" style="line-height: 24px">
                <p>
                  项目价格： <span>{{ feeList.combTotal }}</span>
                </p>
                <p>
                  附加费： <span>{{ feeList.materialTotal }}</span>
                </p>
              </div>

              <div class="project_price" style="line-height: 24px">
                <p>
                  未缴费： <span>{{ feeList.unpaidAmount }}</span>
                </p>
                <p class="all_price">
                  总价格： <span>{{ feeList.orderTotal }}</span>
                </p>
              </div>

              <!-- <p class="all_price">总价格： {{ feeList.orderTotal }}</p> -->
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 查看组合明细弹窗 -->
    <el-dialog
      :title="combDetailTitle + '明细'"
      :visible.sync="combDetailShow"
      custom-class="combDetail_dialog"
      append-to-body
      width="50%"
    >
      <div style="height: 300px; overflow: auto">
        <el-table :data="combDetailList" size="small">
          <el-table-column
            property="itemCode"
            label="项目代码"
            width="100"
          ></el-table-column>
          <el-table-column
            property="itemName"
            label="项目名称"
          ></el-table-column>
        </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="combDetailShow = false">取 消</el-button>
        <el-button type="primary" @click="combDetailShow = false"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import BtnGroup from './btnGroup';
import PublicTable from '@/components/publicTable';
import LeftCont from './leftCont';
import Combo from '../mixins/combo';
import { mapGetters } from 'vuex';
import { dataUtils } from '../../../common';
import shortcut from '@/common/shortcut';
import IdCardMixins from '@/components/IdCardMixins';
import Moment from 'moment';
export default {
  name: 'personRegister',
  mixins: [Combo, shortcut, IdCardMixins],
  components: {
    BtnGroup,
    PublicTable,
    LeftCont
  },
  props: {
    //登记/编辑值
    editInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      shortcutList: {
        112: this.createBtn,
        113: this.save
      },
      recordShow: false,
      param: '',
      dialogList: [],
      columnWidth: {
        companyCode: '160',
        companyDeptCode: '160',
        regNo: '160',
        patCode: '160'
      },
      isPayBySelf: false,
      isFirstEntry: false // 编辑模式下第一次进入单位次数改变不触发页面初始化；
    };
  },
  computed: {
    ...mapGetters(['G_userInfo', 'G_EnumList'])
  },
  methods: {
    tableRowClassName({ row, rowIndex }) {
      // console.log(row,rowIndex);
      if (this.containComboCode.includes(row.combCode.trim())) {
        return 'have_meal';
      } else {
        return 'no_meal';
      }
    },
    // 保存
    save() {
      console.log(this.$refs.form_Ref.searchForm);
      let searchForm = this.$refs.form_Ref.searchForm;
      let checkMealList = [];
      this.checkMealList.map((item, index) => {
        checkMealList.push({
          regNo: searchForm.regNo,
          clusCode: item.clusCode,
          clusName: item.clusName,
          price: item.price,
          isMain: index === 0 ? true : false
        });
      });
      let datas = {
        patient: searchForm,
        clusters: checkMealList,
        combs: this.checkComboList,
        TestTubes: this.$refs.form_Ref.setData?.testTubes || []
      };
      // datas.patient.age = Number(datas.patient.age);
      datas.patient.operatorCode = this.G_userInfo.codeOper.operatorCode;
      if (datas.patient.photoUrl === null) {
        datas.patient.photoUrl = '';
      }
      console.log(datas);
      // if (this.checkComboList.length == 0) {
      //   this.$message({
      //     message: "请选择套餐或者组合！",
      //     type: "warning",
      //     showClose: true
      //   });
      //   return;
      // }
      this.$refs.form_Ref.$refs.form.validate((valid) => {
        if (valid) {
          this.$ajax
            .post(this.$apiUrls.SaveRegisterOrder + '/Company', datas)
            .then(async (r) => {
              let { success, returnData } = r.data;
              if (!success) return;
              let regNo = this.$refs.form_Ref.searchForm.regNo
                ? this.$refs.form_Ref.searchForm.regNo
                : returnData;
              // this.$refs.form_Ref.uploadImg(regNo);
              await this.$refs.form_Ref
                .dataURLtoFile(searchForm.photoUrl)
                .then((r) => {
                  this.$refs.form_Ref.uploadImg(regNo);
                });
              this.$message({
                message: '保存成功',
                type: 'success',
                showClose: true
              });
              await this.createBtn();
              this.$refs.form_Ref.take_a_picture();
              // this.searchGroup();
            });
        } else {
          this.$message({
            message: '请正确输入个人信息！',
            type: 'warning',
            showClose: true
          });
          return false;
        }
      });
    },
    // 次数改变的回调
    timesChange() {
      let form_Ref = this.$refs.form_Ref;
      if (!!form_Ref.searchForm.companyTimes) {
        this.$ajax
          .post(this.$apiUrls.ReadCompanyCandidateCluster, '', {
            query: {
              companyCode: form_Ref.searchForm.companyCode,
              companyTimes: Number(form_Ref.searchForm.companyTimes)
            }
          })
          .then(async (r) => {
            let { success, returnData } = r.data;
            if (!success) return;
            let companyMeal = returnData || [];
            console.log(companyMeal);
            if (this.isFirstEntry) {
              await this.clearMealData();
            }
            this.isFirstEntry = true;
            let companyMealCode = [];
            companyMeal.map((item) => {
              companyMealCode.push(item.clusCode);
              this.mealListCodeObj[item.clusCode] = item;
            });
            this.mealListCode.unshift(...companyMealCode);
            this.mealList.unshift(...companyMeal);
          });
      }
    },
    // 新建按钮
    createBtn() {
      // 清空选中的套餐
      this.checkMealList = [];
      this.checkMealObj = {};
      this.checkMealCodeArr = [];
      // 清空选中的组合
      this.selectCombo = [];
      this.checkComboObj = {};
      this.selectComboCode = [];
      this.checkComboList = [];
      this.saveCombListObj = {};
      this.$refs.form_Ref.$refs.form.resetFields();
      this.$refs.form_Ref.searchForm.photoUrl = '';
      this.$refs.form_Ref.videoShow = false;
      // this.$refs.form_Ref.take_a_picture();
      this.$refs.form_Ref.searchForm.peCls = 3;
      this.$refs.form_Ref.searchForm.isLeader = false;
      this.$refs.form_Ref.searchForm.isRecheck = false;
      this.$refs.form_Ref.searchForm.isVIP = false;
      this.$refs.form_Ref.searchForm.isConstitution = false;
      this.$refs.form_Ref.companyDeptList = [];
      this.navLiVal = 'info';
      this.feeList = {
        combTotal: '0.00', //组合价格
        materialTotal: '0.00', //材料费
        orderTotal: '0.00', //总价格
        unpaidAmount: '0.00' //未缴费
      };
      // this.getMealList();
      // this.getComboList();
      return Promise.all([this.getMealList(), this.getComboList()]);
    },
    // 连接本地的身份证读卡器
    connectInit() {
      this.connectIdcard((r) => {
        // console.log(r.data);
        try {
          let datas = JSON.parse(r.data);
          if (datas.Data) {
            this.createBtn().then((r) => {
              let idcardInfo = datas.Data;
              this.$refs.form_Ref.searchForm = Object.assign(
                this.$refs.form_Ref.searchForm,
                {
                  name: idcardInfo.Name,
                  sex: idcardInfo.Sex,
                  photoUrl: 'data:image/jpeg;base64,' + idcardInfo.PhotoBase64,
                  cardNo: idcardInfo.IDCardNo,
                  age: dataUtils.getByIdCard(idcardInfo.IDCardNo).age,
                  birthday: Moment(idcardInfo.Born, 'YYYY年MM月DD日').format(
                    'YYYY-MM-DD'
                  ),
                  address: idcardInfo.Address
                }
              );
              this.$refs.form_Ref.searchCard();
            });
          }
        } catch (error) {}
      });
    }
  },
  // watch: {
  //   editInfo() {
  //     this.$refs.form_Ref.searchForm = this.editInfo.patient;
  //     this.$refs.form_Ref.setData = JSON.parse(JSON.stringify(this.editInfo));
  //     setTimeout(() => {
  //       this.disposeRecordData();
  //     }, 500);
  //   },
  // },
  mounted() {
    setTimeout(() => {
      console.log('this.editInfo', this.editInfo);
      // this.$refs.form_Ref.searchForm = this.editInfo.patient;
      this.$refs.form_Ref.setData = JSON.parse(JSON.stringify(this.editInfo));
      this.disposeRecordData();
    }, 300);
  },
  created() {
    if (this.$parent.$parent.drawer) {
      removeEventListener('keyup', this.$parent.$parent.keyUpFun);
      removeEventListener('keydown', this.$parent.$parent.keyDownFun);
    }
    addEventListener('keyup', this.keyUpFun);
    addEventListener('keydown', this.keyDownFun);
  },
  beforeDestroy() {
    addEventListener('keyup', this.$parent.$parent.keyUpFun);
    addEventListener('keydown', this.$parent.$parent.keyDownFun);
    removeEventListener('keyup', this.keyUpFun);
    removeEventListener('keydown', this.keyDownFun);
  }
};
</script>
<style lang="less" scoped>
.groupRegisterDrawer {
  display: flex;
  flex-direction: column;
  height: 100%;

  .global_head {
    height: 68px;
    background-color: #fff;
    // margin-bottom: 18px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 18px;
  }
  /deep/.el-dialog__wrapper {
    /deep/.el-dialog {
      height: 500px !important;
      width: 700px;
      display: flex;
      flex-direction: column;
      padding: 15px;
    }
    /deep/.el-dialog__body {
      height: calc(100% - 120px);
    }
  }
  /deep/.combDetail_dialog {
    height: auto !important;
    table {
      font-size: 14px;
    }
  }

  .content_wrap {
    display: flex;
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    padding: 18px;
    .left_wrap {
      width: 380px;
      .left_content {
        height: 100%;
        overflow: auto;
      }
    }
    .groupLeft {
      // border: 1px solid #b2bec3;
      border-radius: 4px;
    }
    .right_wrap {
      flex: 1;
      flex-shrink: 0;
      background-color: #fff;
      border-radius: 5px;
      overflow: auto;
      display: flex;
      padding: 10px;
      position: relative;
      // padding: 18px;
      & > div {
        border: 1px solid #b2bec3;
        border-radius: 4px;
      }
      .nav_ul {
        position: absolute;
        top: 10px;
        left: 1px;
        width: 25px;
        font-size: 16px;
        border: 1px solid #b2bec3;
        border-radius: 4px;
        li {
          cursor: pointer;
          padding: 2px;
          text-align: center;
          border-bottom: 1px solid #b2bec3;
          & + li {
            border-bottom: 0;
          }
        }
        .navLi_active {
          color: #1770df;
        }
      }
      .combo_group {
        margin-right: 18px;
        width: 40%;
        overflow: hidden;
      }
      .check_combo_wrap {
        flex: 1;
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        overflow: auto;
      }
      .meal_combo_style {
        position: relative;
        overflow: auto;
        padding-top: 48px;
        header {
          height: 48px;
          padding: 0 18px;
          background: rgba(23, 112, 223, 0.2);
          border-radius: 4px;
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          display: flex;
          justify-content: space-between;
          line-height: 48px;
          overflow: hidden;
        }
        .combo_table {
          height: 100%;
          overflow: auto;
          .checkMeal_tag {
            margin-bottom: 5px;
            span {
              color: #d63031;
            }
          }
          .checkMeal_tag + .checkMeal_tag {
            margin-left: 5px;
          }
        }
        /deep/.combo_table {
          .el-input-number--mini {
            width: 100%;
          }
          .el-input__inner {
            padding-left: 0 !important;
            padding-right: 32px !important;
          }
        }
      }
      .meal_div {
        header {
          background-color: #fff;
          justify-content: flex-start;
          align-items: center;
        }
        .meal_tag_wrap {
          flex: 1;
          flex-shrink: 0;
          overflow: auto;
          white-space: nowrap;
        }
        .checkMeal_tag {
          span {
            color: #d63031;
          }
        }
        .checkMeal_tag + .checkMeal_tag {
          margin-left: 5px;
        }
      }
      .combo_div {
        flex: 1;
        flex-shrink: 0;
        overflow: auto;
        padding-bottom: 48px;
        overflow: auto;
      }
    }
    .price_wrap {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 48px;
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      line-height: 48px;
      background: rgba(23, 112, 223, 0.1);
      border-radius: 4;
      overflow: hidden;
      color: #d63031;
      font-weight: 600;
      padding: 0 15px;
      .all_price {
        font-size: 18px;
      }
    }
  }
  .el-tabs--border-card {
    border: none;
  }
  .icon-xuanzhong {
    color: #1770df;
  }
  .global_head {
    height: 52px;
    // margin: 18px 18px 0 18px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 18px;
    // background: rgba(178, 190, 195, 0.1);
    border-radius: 4px;
  }
  .testTubes_div {
    span {
      display: block;
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>
<style lang="less">
.dialogCss {
  height: 520px !important;
  width: 800px;
  display: flex;
  flex-direction: column;
  padding: 15px;
}
.groupRegisterDrawer {
  .left_search_div {
    input {
      height: 36px;
      line-height: 36px;
    }
  }
  .tabs_wrap {
    height: 100%;
    display: flex;
    flex-direction: column;
    .el-tabs__content {
      padding: 0;
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
    }
    .el-tab-pane {
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: auto;
    }
    .head_search {
      display: flex;
      padding: 10px;
      .head_search_div {
        flex: 1;
        margin-right: 10px;
        display: flex;
        input {
          height: 36px;
          line-height: 36px;
        }
      }
    }
    .table_wrap {
      flex: 1;
      overflow: auto;
    }
  }
  .el-tabs--border-card > .el-tabs__header {
    background: rgba(23, 112, 223, 0.2);
    .is-active {
      color: #2d3436;
    }
  }
  .el-tabs__item {
    color: rgba(45, 52, 54, 0.6);
    font-size: 18px;
    font-weight: 500;
    height: 48px;
    line-height: 48px;
  }
  .count_input {
    width: 50px;
  }
  .project_price {
    width: 150px;
    p {
      display: flex;
      justify-content: space-between;
    }
  }
}
</style>
