<!-- 社区档案信息 -->
<template>
  <el-form
    ref="form"
    :model="form"
    label-width="80px"
    class="communityInfo_page"
  >
    <div class="imgRow_wrap">
      <!-- <img :src="form.photoUrl" class="head_img" alt=""> -->
      <el-image :src="form.photoUrl" class="head_img" alt="">
        <div
          slot="error"
          class="image-slot"
          style="
            width: 100%;
            height: 100%;
            align-content: center;
            background-color: #f5f7fa;
          "
        >
          <span>暂未上传</span>
        </div>
      </el-image>
      <div class="img_right">
        <el-form-item label="姓名" class="formItem">
          <el-input readonly v-model="form.name" size="mini"></el-input>
        </el-form-item>
        <el-form-item label="性别" class="formItem">
          <el-select
            v-model.trim="form.sex"
            placeholder="请选择"
            size="small"
            width="100%"
            disabled
          >
            <el-option
              v-for="(item, index) in G_sexList"
              :key="index"
              :value="item.value"
              :label="item.label"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="年龄" class="formItem">
          <el-input readonly v-model="form.age" size="mini"></el-input>
        </el-form-item>
        <el-form-item label="证件类型" class="formItem">
          <el-select
            class="select"
            v-model.trim="form.cardType"
            placeholder="请选择"
            size="small"
            width="100%"
            clearable
            filterable
            disabled
          >
            <el-option
              v-for="(item, index) in G_cardType"
              :key="index"
              :value="item.value"
              :label="item.label"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="证件号" class="formItem">
          <el-input readonly v-model="form.cardNo" size="mini"></el-input>
        </el-form-item>
        <el-form-item label="出生日期" class="formItem">
          <el-date-picker
            style="width: 100%"
            v-model.trim="userInfo.birthday"
            type="date"
            placeholder="选择日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            size="mini"
            readonly
          >
          </el-date-picker>
        </el-form-item>
      </div>
    </div>
    <el-row>
      <el-col :span="12">
        <el-form-item label="籍贯" class="formItem">
          <el-input readonly v-model="form.nativePlace" size="mini"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="诊疗卡号" class="formItem">
          <el-input readonly v-model="form.hisCard" size="mini"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-form-item label="联系地址" class="formItem">
      <el-input readonly v-model="form.address" size="mini"></el-input>
    </el-form-item>
    <div class="dialog-footer" style="text-align: right">
      <el-button size="small" @click="$parent.$parent.communityInfoShow = false"
        >取 消</el-button
      >
      <el-button type="primary" size="small" @click="confirmImport"
        >确 定</el-button
      >
    </div>
  </el-form>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  name: 'community',
  computed: {
    ...mapGetters(['G_sexList', 'G_cardType'])
  },
  data() {
    return {
      userInfo: {},
      form: {
        photoUrl: '',
        name: '',
        sex: '',
        age: '',
        cardType: '',
        cardNo: '',
        birthday: '',
        nativePlace: '',
        hisCard: '',
        address: ''
      }
    };
  },
  methods: {
    getInfo() {
      this.resetForm();
      this.$ajax
        .post(this.$apiUrls.GetHistoryArchivesNew, '', {
          query: {
            queryType: 'CardNo',
            queryValue: this.userInfo.cardNo
          }
        })
        .then((r) => {
          console.log('r', r);
          let { success, returnData } = r.data;
          if (!success) return;
          if (returnData?.length == 0) {
            this.$message({
              showClose: true,
              message: '暂无社区信息',
              type: 'warning'
            });
            return;
          }
          this.form = returnData[0];
          this.form.hisCard = this.form.cardNo;
          this.form.address = '广东省汕尾市海丰县城东镇赤山路尚韩美工作室';
        });
    },
    confirmImport() {
      this.$emit('confirmImport', this.form);
    },
    resetForm() {
      this.$refs['form'].resetFields();
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getInfo();
    });
  }
};
</script>

<style lang="less" scoped>
.communityInfo_page {
  height: 500px;
  .imgRow_wrap {
    display: flex;
    .img_right {
      flex: 1 0 0;
      display: flex;
      flex-wrap: wrap;
      .formItem {
        width: 50%;
        flex-shrink: 0;
      }
    }
    .head_img {
      width: 88px;
      height: 110px;
      border: 1px solid #ccc;
      border-radius: 4px;
      overflow: hidden;
      text-align: center;
    }
  }
  .formItem {
    margin-bottom: 3px;
  }
  /deep/.el-input.is-disabled .el-input__inner {
    background-color: #fff !important;
    color: #606266 !important;
  }
}
</style>
