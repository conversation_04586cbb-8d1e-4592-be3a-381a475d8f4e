<template>
  <div class="supplementDetails">
    <AllBtn
      :btnList="['查询', '新建', '删除']"
      :methodCreat="create"
      :methodDelete="delMore"
      :methodSearch="search"
      ref="allBtn_Ref"
    />
    <div class="table-wrap">
      <PublicTable
        ref="table_ref"
        :viewTableList.sync="tableData"
        :theads.sync="theads"
        @rowDblclick="handleClick"
        @selectionChange="handleSelectChangeTable"
        isCheck
      >
      </PublicTable>
    </div>
    <el-dialog
      title="排除列表信息属性"
      :visible.sync="drawer"
      width="30%"
      :before-close="cancel"
      :append-to-body="true"
    >
      <el-form
        :model="popupForm"
        ref="ruleForms"
        :rules="rules"
        label-width="125px"
      >
        <!-- <el-form-item label="父级关键字代码" prop="parentKeywordCode">
          <el-input
            v-model.trim="popupForm.parentKeywordCode"
            autocomplete="off"
            size="small"
            placeholder="请输入父级关键字代码"
            disabled
          ></el-input>
        </el-form-item> -->
        <el-form-item label="关键字代码" prop="keywordCode">
          <el-input
            v-model.trim="popupForm.keywordCode"
            autocomplete="off"
            size="small"
            placeholder="请输入关键字代码"
            :disabled="!isAdd"
          ></el-input>
        </el-form-item>
        <el-form-item label="重大阳性关键字" prop="keywordName">
          <el-input
            v-model.trim="popupForm.keywordName"
            autocomplete="off"
            size="small"
            placeholder="请输入重大阳性关键字"
            type="textarea"
            :rows="5"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel" size="small" class="search-btn"
          >取消</el-button
        >
        <el-button @click="submit" size="small" class="search-btn blue_btn"
          >保存</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import PublicTable from '@/components/publicTable.vue';
import AllBtn from '../../allBtn.vue';
import { dataUtils } from '@/common';
export default {
  name: 'supplementDetails',
  components: { PublicTable, AllBtn },
  props: {
    parentKeywordCode: {
      type: String,
      default: () => {
        return '';
      }
    }
  },
  data() {
    return {
      tableData: [],
      tableDataCopy: [],
      theads: {
        // parentKeywordCode: "父级关键字代码",
        keywordCode: '关键字代码',
        keywordName: '重大阳性关键字'
      },
      popupForm: {
        parentKeywordCode: '',
        keywordCode: '',
        keywordName: ''
      },
      drawer: false,
      isAdd: false,
      rules: {
        parentKeywordCode: [
          { required: true, message: '请输入父级关键字代码', trigger: 'blur' }
        ],
        keywordCode: [
          { required: true, message: '请输入关键字代码', trigger: 'blur' }
        ],
        keywordName: [
          { required: true, message: '请输入重大阳性关键字', trigger: 'blur' }
        ]
      },
      codeArr: [],
      formInline: ''
    };
  },
  created() {},
  methods: {
    // 获取表格数据
    getTableData() {
      this.$ajax
        .post(this.$apiUrls.PageQuery_KeywordPositiveKeyword, '', {
          query: { parentKeywordCode: this.parentKeywordCode }
        })
        .then((r) => {
          console.log('r: ', r);
          const { returnData, success } = r.data;
          if (!success) {
            return;
          }
          this.tableData = returnData;
          this.tableDataCopy = returnData;
        });
    },
    // 取消
    cancel() {
      this.drawer = false;
      this.$nextTick(() => {
        this.$refs.ruleForms.resetFields();
      });
    },
    // 表格勾选
    handleSelectChangeTable(row) {
      this.codeArr = row;
    },
    // 新建
    create() {
      this.isAdd = true;
      this.drawer = true;
      this.$nextTick(() => {
        this.$refs.ruleForms.resetFields();
        this.popupForm.parentKeywordCode = this.parentKeywordCode;
      });
    },
    // 新建请求
    createSubmit() {
      this.$ajax
        .post(
          `${this.$apiUrls.CU_KeywordPositiveKeyword}/Create`,
          this.popupForm
        )
        .then((r) => {
          console.log('r: ', r);
          const { returnData, success } = r.data;
          if (!success) {
            return;
          }
          this.getTableData();
          this.$message.success('新增成功!');
          this.drawer = false;
        });
    },
    // 双击编辑
    handleClick(row) {
      this.isAdd = false;
      this.drawer = true;
      this.$nextTick(() => {
        this.popupForm = dataUtils.deepCopy(row);
        this.$refs.ruleForms.resetFields();
      });
    },
    // 编辑请求
    editSubmit() {
      this.$ajax
        .post(
          `${this.$apiUrls.CU_KeywordPositiveKeyword}/Update`,
          this.popupForm
        )
        .then((r) => {
          const { returnData, success } = r.data;
          if (!success) {
            return;
          }
          this.getTableData();
          this.$message.success('修改成功!');
          this.drawer = false;
        });
    },
    //提交
    submit() {
      this.$refs.ruleForms.validate((valid) => {
        if (valid) {
          if (this.isAdd) {
            this.createSubmit();
            return;
          }
          this.editSubmit();
        } else {
          return false;
        }
      });
    },
    // 删除
    delMore() {
      if (this.codeArr.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据!',
          type: 'warning'
        });
      }
      if (this.codeArr.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.$ajax
              .post(this.$apiUrls.Delete_KeywordPositiveKeyword, this.codeArr)
              .then((r) => {
                console.log('r111: ', r);
                const { returnData, success } = r.data;
                if (!success) {
                  return;
                }
                this.$message.success('删除成功!');
                this.getTableData();
              });
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    // 搜索
    search() {
      this.formInline = this.$refs.allBtn_Ref.searchInfo.trim(); //获取组件文本框的值
      if (this.formInline) {
        setTimeout(() => {
          this.tableData = this.tableDataCopy.filter((item) => {
            return (
              item.keywordCode.indexOf(this.formInline) !== -1 ||
              item.keywordName.indexOf(this.formInline) !== -1
            );
          });
        }, 500);
      } else {
        setTimeout(() => {
          this.getTableData();
        }, 500);
      }
    }
  }
};
</script>

<style lang="less" scoped>
.supplementDetails {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding-left: 20px;
  padding-bottom: 20px;
  overflow: auto;
  .table-wrap {
    flex: 1;
    overflow: auto;
    border: 1px solid #d9dfe2;
    border-radius: 4px;
  }
}
</style>
