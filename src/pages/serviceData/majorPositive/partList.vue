<template>
  <!-- 重大阳性触发关键字列表 -->
  <div class="majorPositiveKeywordsList">
    <el-container>
      <el-header>
        <AllBtn
          :btnList="['查询', '新建', '删除']"
          :methodCreat="add"
          :methodDelete="delMore"
          :methodSearch="getTableData"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          ref="table_ref"
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          :params="params"
          :url="$apiUrls.GetMajorPositiveParts"
          rowKey="partCode"
          remoteByPage
          @rowDblclick="handleClick"
          @selectionChange="handleSelectChangeTable"
        >
        </PublicTable>
      </el-main>
    </el-container>
    <el-drawer
      :title="`重大阳性关键字${isAdd ? '新建' : '编辑'}`"
      :visible.sync="drawer"
      :before-close="cancel"
      size="25%"
      :wrapperClosable="false"
    >
      <el-form
        :model="popupForm"
        ref="ruleForms"
        :rules="rules"
        label-width="125px"
      >
        <el-form-item label="部位名称" prop="partName">
          <el-input
            v-model.trim="popupForm.partName"
            autocomplete="off"
            size="small"
            placeholder="请输入部位名称"
            type="textarea"
            :rows="5"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="cancel" size="small" class="search-btn"
          >取消</el-button
        >
        <el-button @click="submit" size="small" class="search-btn blue_btn"
          >保存</el-button
        >
      </div>
    </el-drawer>
    <el-drawer
      :title="drawerTitle"
      :visible.sync="drawers"
      :close-on-click-modal="false"
      :wrapperClosable="false"
      size="80%"
    >
      <DetailsDrawer
        :cancel="handleClose"
        :drawerInfo="drawerInfo"
        ref="drawer_ref"
      ></DetailsDrawer>
    </el-drawer>
    <el-drawer
      :title="supplementTitle + '-排除列表'"
      :visible.sync="supplement"
      :close-on-click-modal="false"
      :wrapperClosable="false"
      size="80%"
    >
      <SupplementDetails
        :cancel="handleClose"
        :parentKeywordCode="parentKeywordCode"
        ref="supplement_ref"
      ></SupplementDetails>
    </el-drawer>
  </div>
</template>

<script>
import PublicTable from '@/components/publicTable2.vue';
import AllBtn from '../allBtn.vue';
import DetailsDrawer from '../otherCodeMapping/detailsDrawer.vue';
import SupplementDetails from './components/supplementDetails.vue';
import { mapGetters } from 'vuex';
import { dataUtils } from '@/common';
export default {
  name: 'majorPositiveKeywordsList',
  components: { PublicTable, AllBtn, DetailsDrawer, SupplementDetails },
  props: {
    //总传
    allInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      drawerTitle: '',
      drawerInfo: this.allInfo, //总传
      drawers: false,
      formInline: '',
      codeArr: [],
      resizeFlag: '',
      loading: false,
      disabled: false,
      tableDataCopy: [],
      tableData: [],
      pageSize: 50,
      currentPage: 1,
      dialogTitle: '',
      drawer: false,
      isAdd: true,
      formLabelWidth: '125px',
      roleList: [],
      popupForm: {
        partCode: '',
        partName: ''
      },
      theads: [
        {
          prop: 'partCode',
          label: '部位代码',
          align: '',
          width: '115',
          sortable: true
        },
        {
          prop: 'partName',
          label: '部位名称',
          align: '',
          width: '',
          sortable: false
        }
      ],
      params: {},
      rules: {
        partCode: [
          { required: true, message: '请输入部位代码', trigger: 'blur' }
        ],
        partName: [
          { required: true, message: '请输入部位名称', trigger: 'blur' }
        ]
      },
      supplement: false,
      supplementTitle: '',
      parentKeywordCode: ''
    };
  },
  computed: {
    ...mapGetters(['G_EnumList'])
  },
  created() {},
  mounted() {
    this.getTableData();
  },
  methods: {
    handleClose() {
      this.drawers = false;
      this.$nextTick(() => {
        this.$refs.ruleForm.resetFields();
      });
    },
    // 获取表格信息数据
    getTableData() {
      this.loading = true;
      this.params = {
        keyword: this.$refs.allBtn_Ref.searchInfo.trim(),
        partCodes: []
      };
      this.$refs.table_ref.loadData();
    },
    //新增
    add() {
      this.isAdd = true;
      this.drawer = true;
      this.popupForm.partCode = '';
      this.$nextTick(() => {
        this.$refs.ruleForms.resetFields();
      });
    },
    //编辑
    handleClick(row) {
      this.isAdd = false;
      this.drawer = true;
      this.$nextTick(() => {
        this.$refs.ruleForms.resetFields();
        this.popupForm = dataUtils.deepCopy(row);
      });
    },
    //提交
    submit() {
      console.log(this.popupForm);
      this.$ajax
        .post(this.$apiUrls.SaveMajorPositivePart, this.popupForm)
        .then((r) => {
          this.getTableData();
          this.$message.success('保存成功!');
          this.cancel();
        });
    },
    // 取消
    cancel() {
      this.$refs.ruleForms.resetFields();
      this.$nextTick(() => {
        this.drawer = false;
      });
    },
    //复选框勾选操作
    handleSelectChangeTable(val) {
      this.codeArr = [];
      val.map((item) => {
        if (item.partCode != '') {
          this.codeArr.push(item.partCode);
        }
      });
      console.log('this.codeArr: ', this.codeArr);
    },
    //多条删除
    delMore() {
      console.log(this.codeArr);
      if (this.codeArr.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据!',
          type: 'warning'
        });
      }
      if (this.codeArr.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.$ajax
              .post(this.$apiUrls.BatchRemoveMajorPositivePart, {
                partCodes: this.codeArr
              })
              .then((r) => {
                console.log('r111: ', r);
                const { returnData, success } = r.data;
                if (!success) {
                  return;
                }
                this.$message.success('删除成功!');
                this.getTableData();
              });
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    }
  }
};
</script>
<style lang="less" scoped>
.majorPositiveKeywordsList {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-weight: 600;
  // padding: 0 10px;
  header {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-header,
  .el-main {
    padding: 0;
  }
  .searchWrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  // .search-btn {
  //   padding: 8px 25px;
  //   font-size: 14px;
  // }
  // /deep/.el-drawer__body {
  //   padding: 0 20px;
  //   .el-form-item {
  //     margin-bottom: 12px;
  //   }
  // }
  /deep/.el-drawer__header {
    background: #d1e2f9;
    margin-bottom: 0;
    padding: 18px;
  }
  /deep/.el-drawer__header,
  /deep/.el-form-item__label {
    color: #2d3436;
  }
  /deep/.el-checkbox__inner {
    width: 20px;
    height: 20px;
  }
  /deep/.el-checkbox__inner::after {
    left: 7px;
    top: 3px;
  }
  /deep/.el-checkbox__inner::before {
    top: 8px;
  }
  .search-input {
    margin-right: 10px;
    // width: 322px;
  }
  .select {
    width: 100%;
  }
  .table-text {
    color: #1770df;
    // cursor: pointer;
  }
  .print_table {
    display: none;
  }
  .operation {
    display: flex;
    align-items: center;
  }
}
</style>
