<template>
  <div class="majorPositiveList_page">
    <!-- 重大阳性列表 -->
    <el-container>
      <el-header>
        <AllBtn
          :btnList="['查询', '新建', '删除']"
          :methodCreat="add"
          :methodDelete="delMore"
          :methodSearch="search"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          @rowDblclick="handleClick"
          :cell_blue="cell_blue"
          :columnWidth="columnWidth"
          @selectionChange="handleSelectChangeTable"
          :tableLoading.sync="loading"
          isCheck
        >
          <template #positiveType="{ scope }">
            <div>{{ G_EnumList['PositiveType'][scope.row.positiveType] }}</div>
          </template>
          <template #columnRight>
            <el-table-column prop="operation" label="操作" width="80">
              <template slot-scope="scope">
                <div class="operation">
                  <el-button
                    type="primary"
                    plain
                    size="mini"
                    class="view"
                    @click="expressionBtn(scope.row)"
                    >设置条件</el-button
                  >
                </div>
              </template>
            </el-table-column>
          </template>
        </PublicTable>
      </el-main>
    </el-container>
    <el-drawer
      title="重大阳性信息属性"
      :visible.sync="drawer"
      :before-close="handleClose"
      :wrapperClosable="false"
    >
      <el-form :model="popupForm" ref="ruleForm" :rules="rules">
        <el-form-item
          label="重大阳性代码"
          :label-width="formLabelWidth"
          prop="positiveCode"
        >
          <el-input
            v-model.trim="popupForm.positiveCode"
            autocomplete="off"
            size="small"
            placeholder="请输入重大阳性代码"
            :disabled="funTpye == '/Create' ? false : true"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="重大阳性名称"
          :label-width="formLabelWidth"
          prop="positiveName"
        >
          <el-input
            v-model.trim="popupForm.positiveName"
            autocomplete="off"
            size="small"
            placeholder="请输入重大阳性名称"
          ></el-input>
        </el-form-item>
        <el-form-item
          :label-width="formLabelWidth"
          label="类别"
          prop="positiveType"
        >
          <el-select
            style="width: 100%"
            size="small"
            v-model="popupForm.positiveType"
            placeholder="请选择活动区域"
          >
            <el-option
              v-for="item in G_PositiveType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="检查情况" :label-width="formLabelWidth">
          <el-select
            style="width: 100%"
            size="small"
            v-model="popupForm.checkType"
            placeholder="请选择"
          >
            <el-option
              v-for="item in G_MajorCheckType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否新建疾病对应" :label-width="formLabelWidth">
          <el-switch v-model="isCreateFlag" active-color="#13ce66"> </el-switch>
        </el-form-item>
        <el-form-item
          label="项目分类"
          style="width: 100%"
          v-if="isCreateFlag"
          :label-width="formLabelWidth"
        >
          <el-select
            size="small"
            filterable
            v-model="popupForm.clsCode"
            placeholder="请选择"
          >
            <el-option
              v-for="item in G_codeItemCls"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="建议"
          v-if="isCreateFlag"
          :label-width="formLabelWidth"
        >
          <el-input
            type="textarea"
            size="small"
            :autosize="{ minRows: 2 }"
            placeholder="请输入内容"
            v-model="popupForm.suggestContent"
          >
          </el-input>
        </el-form-item>
        <el-form-item
          label="疾病对应"
          v-if="!isCreateFlag"
          :label-width="formLabelWidth"
        >
          <el-select
            :loading="loading"
            v-model="popupForm.diseaseName"
            style="width: 100%"
            filterable
            popper-class="disease_select"
            :filter-method="diseaseFilter"
            ref="disease_Ref"
            @focus="diseaseSelectFocus"
            size="small"
          >
            <el-option :value="popupForm.diseaseName">
              <!--:data 初始化获取的数据-->
              <!--lazy 表示节点是否懒加载，即是否是节点展开时才去异步加载数据-->
              <!--@node-click 当节点被点击时触发handleNodeClick方法-->
              <!--:load 当节点需要加载子节点时触发loadNode方法-->
              <el-tree
                :data="menuList"
                :props="defaultProps"
                :default-expanded-keys="defaultExpanded"
                node-key="id"
                @node-click="handleNodeClick"
              >
              </el-tree>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="drawer = false" size="small">取消</el-button>
        <el-button class="blue_btn" @click="submit" size="small"
          >保存</el-button
        >
      </div>
    </el-drawer>
    <el-drawer
      class="preserve_drawer"
      size="90%"
      :visible.sync="preserveShow"
      @close="preserveDrawerClose"
    >
      <template #title>
        <ul>
          <li
            :class="{ active: drawerTabIdx == item.id }"
            v-for="item in drawerTabList"
            :key="item.id"
            @click="drawerTabClick(item.id)"
          >
            {{ item.label }}
          </li>
        </ul>
      </template>
      <!-- 部位关键字 -->
      <div class="partDrawer_wrap" v-if="drawerTabIdx == 1">
        <div class="part_table">
          <ul class="keyword_wrap">
            <li>
              <el-table
                :data="addPartList"
                height="300"
                border
                style="width: 100%"
              >
                <el-table-column label="部位">
                  <template slot-scope="scope">
                    <el-input
                      size="mini"
                      :class="C_redColor(!scope.row.partName)"
                      :ref="'partInp_' + scope.$index"
                      v-model="scope.row.partName"
                      @change="partInputChange(scope.row, scope.$index)"
                    ></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="102">
                  <template #header="scope">
                    <el-button
                      size="mini"
                      type="primary"
                      @click="addPartBtn(scope)"
                      >添加部位</el-button
                    >
                  </template>
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="primary"
                      @click="addPartList.splice(scope.$index, 1)"
                      >删除</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </li>
            <p style="width: 5px"></p>
            <li>
              <el-table
                :data="addKeywordList"
                height="300"
                border
                style="width: 100%"
              >
                <el-table-column label="关键词">
                  <template slot-scope="scope">
                    <el-input
                      size="mini"
                      :class="C_redColor(!scope.row.keywordName)"
                      :ref="'keywordInp_' + scope.$index"
                      v-model="scope.row.keywordName"
                      @change="keywordInputChange(scope.row, scope.$index)"
                    ></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="113">
                  <template #header>
                    <el-button size="mini" type="primary" @click="addKeywordBtn"
                      >添加关键词</el-button
                    >
                  </template>
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="primary"
                      @click="addKeywordList.splice(scope.$index, 1)"
                      >删除</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </li>
          </ul>
          <div
            style="flex: 1; flex-shrink: 0; overflow: auto"
            class="source_wrap"
          >
            <el-table
              :data="partList"
              :span-method="objectSpanMethod"
              border
              height="100%"
              style="width: 100%"
            >
              <el-table-column label="部位">
                <template slot-scope="scope">
                  <el-input
                    :class="C_redColor(!scope.row.partName)"
                    :ref="'sourcePartInp_' + scope.$index"
                    size="mini"
                    v-model="scope.row.partName"
                    @change="sourcePartInputChange(scope.row, scope.$index)"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column width="100" prop="size" label="尺寸(mm)">
                <template slot-scope="scope">
                  <el-input-number
                    :controls="false"
                    :class="C_redColor(scope.row.size !== 0 && !scope.row.size)"
                    size="mini"
                    v-model="scope.row.size"
                  ></el-input-number>
                </template>
              </el-table-column>
              <el-table-column width="150" label="回声情况">
                <template slot-scope="scope">
                  <el-select
                    v-model="scope.row.echoType"
                    size="mini"
                    filterable
                    clearable
                    placeholder="请选择回声情况"
                    @filter-change="partListFilter"
                  >
                    <el-option
                      v-for="item in G_EchoType"
                      :key="item.value"
                      :label="item.label"
                      :value="item.label"
                    >
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column width="80" prop="ageLowest" label="年龄下限">
                <template slot-scope="scope">
                  <el-input-number
                    :controls="false"
                    size="mini"
                    v-model="scope.row.ageLowest"
                  ></el-input-number>
                </template>
              </el-table-column>
              <el-table-column width="80" prop="ageHighest" label="年龄上限">
                <template slot-scope="scope">
                  <el-input-number
                    :controls="false"
                    size="mini"
                    v-model="scope.row.ageHighest"
                  ></el-input-number>
                </template>
              </el-table-column>
              <el-table-column width="150" label="操作">
                <template #header>
                  <el-button size="mini" type="primary" @click="addSourceBtn"
                    >添加来源</el-button
                  >
                </template>
                <template slot-scope="scope">
                  <el-button size="mini" @click="addSource(scope.row)"
                    >添加</el-button
                  >
                  <el-button
                    size="mini"
                    @click="delSource(scope.row, scope.$index)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div style="text-align: right; padding: 5px 0">
          <el-button @click="preserveShow = false">取消</el-button>
          <el-button @click="saveRelevancePart">保存</el-button>
        </div>
      </div>
      <!-- 表达式 -->
      <majorPositiveExpression
        class="expression_dom"
        v-if="preserveShow && drawerTabIdx == 2"
        :positiveInfo="positiveInfo"
      ></majorPositiveExpression>
      <!-- 审核条件 -->
      <AuditCondition v-if="drawerTabIdx == 3" :positiveInfo="positiveInfo" />
    </el-drawer>
  </div>
</template>

<script>
import { dataUtils } from '@/common';
import PublicTable from '@/components/publicTable.vue';
import AllBtn from '../allBtn.vue';
import majorPositiveExpression from './majorPositiveExpression.vue';
import AuditCondition from './components/auditCondition';
import { mapGetters } from 'vuex';
import majorPositiveList from './mixins/majorPositiveList.js';
export default {
  name: 'majorPositiveList',
  mixins: [majorPositiveList],
  components: { PublicTable, AllBtn, majorPositiveExpression, AuditCondition },
  computed: {
    ...mapGetters([
      'G_EchoType',
      'G_MajorCheckType',
      'G_codeItemCls',
      'G_PositiveType',
      'G_EnumList'
    ]),
    C_redColor() {
      return function (flag) {
        // let vals = typeof val === 'number'? false : (val?.trim()  === '');
        // if((val === undefined || vals) && this.isEmpty){
        //   return 'red_color';
        // }
        // return '';
        if (flag && this.isEmpty) {
          return 'red_color';
        }
        return '';
      };
    }
  },
  data() {
    return {
      formInline: '',
      codeArr: [],
      loading: false,
      excelData: [],
      multipleSelection: [],
      tableDataCopy: [],
      tableData: [],
      pageSize: 50,
      currentPage: 1,
      drawer: false,
      funTpye: '/Create',
      formLabelWidth: '135px',
      popupForm: {
        positiveCode: '',
        positiveName: '',
        positiveType: ''
      },
      theads: {
        positiveType: '类别',
        positiveCode: '重大阳性代码',
        positiveName: '重大阳性名称'
      },
      cell_blue: ['positiveName'],
      columnWidth: {
        positiveType: '50',
        positiveCode: '110'
      },
      rules: {
        positiveCode: [
          { required: true, message: '请输入重大阳性代码', trigger: 'blur' }
        ],
        positiveName: [
          { required: true, message: '请输入重大阳性名称', trigger: 'blur' }
        ]
      },
      positiveShow: false,
      positiveInfo: {},
      partList: [],
      mergeRowIdx: 0,
      allPartList: [], //候选部位列表
      filterPartList: [], //过滤已选的部位列表
      focusIdx: '', //聚焦显示部位下拉列表
      keywordList: [], // 关键字列表
      keywordListObj: {}, // 关键字代码对应的中文
      checkMajorPositive: {}, //选中的重大阳性
      addPartList: [
        {
          partCode: '111',
          partName: '测试部位'
        }
      ], //添加的部位列表
      addKeywordList: [
        {
          keywordCode: '0000',
          keywordName: '测试关键词'
        }
      ], //添加的关键词列表
      isEmpty: false,
      preserveShow: false,
      drawerTabList: [
        {
          id: 1,
          label: '部位关键字'
        },
        {
          id: 2,
          label: '表达式'
        },
        {
          id: 3,
          label: '审核条件'
        }
      ],
      drawerTabIdx: 1,
      isCreateFlag: false // 是否新建疾病对应
    };
  },
  created: function () {
    this.getAllPartList().then((r) => {
      this.filterPartList = dataUtils.deepCopy(this.allPartList);
    });
    this.GetMajorPositiveKeywords();
  },
  mounted: function () {
    this.getTableData(); //默认查询
  },
  methods: {
    handleClose() {
      this.drawer = false;
    },
    //模糊查询
    search() {
      this.formInline = this.$refs.allBtn_Ref.searchInfo.trim(); //获取组件文本框的值
      if (this.formInline) {
        this.tableData = this.tableDataCopy.filter((item) => {
          return (
            item.positiveCode.indexOf(this.formInline) !== -1 ||
            item.positiveName.indexOf(this.formInline) !== -1
          );
        });
      } else {
        this.getTableData();
      }
      //this.loading = false;
    },
    //查询
    getTableData() {
      this.loading = true;
      this.$ajax
        .post(this.$apiUrls.RD_MajorPositive + '/Read', [])
        .then((r) => {
          this.tableData = r.data.returnData;
          this.tableDataCopy = r.data.returnData;
          this.loading = false;
        });
    },
    //新增
    add() {
      this.drawer = true;
      this.funTpye = '/Create';
      this.$nextTick(function () {
        this.$refs.ruleForm.resetFields();
      });
    },
    //编辑
    handleClick(row) {
      console.log('row', row);
      this.drawer = true;
      this.isCreateFlag = !row.diseaseCode;
      this.funTpye = '/Update';
      this.$nextTick(() => {
        this.$refs.ruleForm.resetFields();
        this.popupForm = dataUtils.deepCopy(row);
        this.checkDisease = {
          diseaseCode: row.diseaseCode,
          diseaseName: row.diseaseName
        };
      });
    },
    //提交
    submit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$ajax
            .post(this.$apiUrls.CU_MajorPositive + this.funTpye, this.popupForm)
            .then((r) => {
              r = r.data;
              if (!r.success) {
                return;
              }
              this.tableData = [];
              this.drawer = false;
              this.$nextTick(() => {
                this.$refs.allBtn_Ref.searchInfo = '';
                this.search();
              });
              if (this.funTpye == '/Create') {
                this.$message({
                  message: '新建成功',
                  type: 'success',
                  showClose: true
                });
              } else {
                this.$message({
                  message: '修改成功',
                  type: 'success',
                  showClose: true
                });
              }
            });
        } else {
          return false;
        }
      });
    },
    //复选框勾选操作
    handleSelectChangeTable(val) {
      //console.log(val);
      this.multipleSelection = val;
      this.codeArr = [];
      val.map((item, i) => {
        if (item.positiveCode != '') {
          this.codeArr.push(item.positiveCode);
        }
      });
    },
    //多条删除
    delMore() {
      if (this.codeArr.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据!',
          type: 'warning'
        });
      }
      if (this.codeArr.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.delete(this.codeArr);
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    //确认删除
    delete(codeArr) {
      this.$ajax
        .post(this.$apiUrls.RD_MajorPositive + '/Delete', codeArr)
        .then((r) => {
          let { success } = r.data;
          if (!success) return;
          this.$message({
            showClose: true,
            message: '删除成功',
            type: 'success'
          });
          this.$nextTick(() => {
            this.$refs.allBtn_Ref.searchInfo = '';
            this.search();
          });
        });
    },
    // 表达式按钮的回调
    expressionBtn(row) {
      console.log(row);
      this.positiveShow = true;
      this.positiveInfo = row;
      this.preserveShow = true;
      this.addRelevancePart(row);
    },
    // 添加关联部位
    addRelevancePart(row) {
      this.checkMajorPositive = row;
      this.addPartList = [];
      this.addKeywordList = [];
      this.partList = [];
      this.$ajax
        .get(
          this.$apiUrls.GetMapPartWithKeywordMajorPositives +
            `/${row.positiveCode}`
        )
        .then((r) => {
          let { success, returnData } = r.data;
          let mergeRow, rowSpan;
          let partList = [],
            addPartList = [],
            addKeywordList = [];
          (returnData || []).map((item) => {
            if (item.keywords?.length != 0) {
              addPartList.push({
                partCode: item.partCode,
                partName: item.partName
              });
              if (addKeywordList.length === 0) {
                addKeywordList = item.keywords.map((twoItem) => {
                  return {
                    keywordCode: twoItem.keywordCode,
                    keywordName: twoItem.keywordName
                  };
                });
              }
            }

            if (item.parts?.length != 0) {
              let arr = item.parts.map((twoItem, twoIdx) => {
                return {
                  ...twoItem,
                  partCode: item.partCode,
                  partName: item.partName,
                  ageLowest:
                    twoItem.ageLowest === null ? undefined : twoItem.ageLowest,
                  ageHighest:
                    twoItem.ageHighest === null
                      ? undefined
                      : twoItem.ageHighest,
                  rowSpan: twoIdx === 0 ? item.parts.length : 0
                };
              });
              partList.push(...arr);
            }
          });
          this.addPartList = addPartList;
          this.addKeywordList = addKeywordList;
          this.partList = partList;
        });
    },
    // 部位列表合并单元格的回调
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        return {
          rowspan: row.rowSpan,
          colspan: row.rowSpan ? 1 : 0
        };
      }
    },
    // 添加部位按钮点击回调
    addPartBtn(row) {
      console.log(row);
      this.addPartList.unshift({
        partCode: '',
        partName: ''
      });
      this.$nextTick(() => {
        this.$refs[`partInp_0`].focus();
      });
    },
    // 部位输入框的改变回调
    partInputChange(row, rowIdx) {
      row.partCode = '';
      let isSame = false;
      this.addPartList.some((item, idx) => {
        if (idx !== rowIdx && row.partName === item.partName) {
          isSame = true;
          return true;
        }
      });
      if (isSame) {
        this.$message({
          message: '已有相同的部位，请重新输入！',
          type: 'warning'
        });
        row.partName = '';
        this.$refs[`partInp_${rowIdx}`].focus();
        return;
      }
    },
    // 来源的部位输入框改变回调
    sourcePartInputChange(row, rowIdx) {
      row.partCode = '';
      let isSame = false;
      this.partList.some((item, idx) => {
        if (
          idx !== rowIdx &&
          row.rowSpan !== 0 &&
          row.partName === item.partName
        ) {
          isSame = true;
          return true;
        }
      });
      if (isSame) {
        this.$message({
          message: '已有相同的部位，请重新输入！',
          type: 'warning'
        });
        row.partName = '';
        this.$refs[`sourcePartInp_${rowIdx}`].focus();
        return;
      }
      if (row.rowSpan === undefined) return;
      for (let idx = rowIdx + 1; idx < rowIdx + row.rowSpan; idx++) {
        let partRow = this.partList[idx];
        partRow.partCode = row.partCode;
        partRow.partName = row.partName;
      }
    },
    // 添加关键词按钮点击回调
    addKeywordBtn(row) {
      this.addKeywordList.unshift({
        keywordCode: '',
        keywordName: ''
      });
      this.$nextTick(() => {
        this.$refs[`keywordInp_0`].focus();
      });
    },
    // 关键词输入框的改变回调
    keywordInputChange(row, rowIdx) {
      row.keywordCode = '';
      let isSame = false;
      this.addKeywordList.some((item, idx) => {
        if (
          idx !== rowIdx &&
          row.rowSpan !== 0 &&
          row.keywordName === item.keywordName
        ) {
          isSame = true;
          return true;
        }
      });
      if (isSame) {
        this.$message({
          message: '已有相同的关键词，请重新输入！',
          type: 'warning'
        });
        row.keywordName = '';
        this.$refs[`keywordInp_${rowIdx}`].focus();
        return;
      }
    },
    // 添加来源
    addSourceBtn() {
      this.partList.unshift({
        partCode: '', // 部位代码
        partName: '', //部位代码
        size: undefined, //尺寸
        echoType: '', //回声情况
        ageLowest: undefined, //年龄-下限
        ageHighest: undefined, //年龄-上限
        rowSpan: 1
      });
      this.$nextTick(() => {
        this.$refs[`sourcePartInp_0`].focus();
      });
    },
    // 添加来源
    addSource(row) {
      // if(!row.partCode) {
      //   this.$message({
      //     message: '请先选择部位！',
      //     type: 'warning',
      //     showClose: true
      //   });
      //   return;
      // }
      this.partList.some((item, idx) => {
        if (item.partCode == row.partCode) {
          this.partList.splice(idx + item.rowSpan, 0, {
            partCode: item.partCode, // 部位代码
            partName: item.partName, //部位代码
            size: undefined, //尺寸
            echoType: '', //回声情况
            ageLowest: undefined, //年龄-下限
            ageHighest: undefined, //年龄-上限
            keywords: [], //关键词代码
            rowSpan: 0
          });
          // this.partList[idx].rowSpan++;
          let rowSpan = item.rowSpan + 1;
          // row.rowSpan ++
          this.$nextTick(() => {
            this.$set(this.partList[idx], 'rowSpan', rowSpan);
            // this.partList[idx] = Object.assign({}, this.partList[idx], {rowSpan: rowSpan});
          });

          console.log(this.partList);
          return true;
        }
      });
    },
    // 删除来源
    delSource(row, rowIdx) {
      if (row.rowSpan === 1) {
        this.partList.splice(rowIdx, 1);
        return;
      }
      this.partList.some((item, idx, arr) => {
        if (item.partCode == row.partCode) {
          if (!row.rowSpan) {
            this.$set(item, 'rowSpan', item.rowSpan - 1);
            arr.splice(rowIdx, 1);
            return true;
          }
          if (row.rowSpan > 1) {
            arr[idx + 1].rowSpan = row.rowSpan - 1;
            arr[idx + 1].keywords = row.keywords;
            arr.splice(idx, 1);
            return true;
          }
        }
      });
      console.log(this.partList);
    },
    // 获取候选部位列表
    getAllPartList() {
      return new Promise((resolve, reject) => {
        this.$ajax
          .post(this.$apiUrls.GetMajorPositiveParts, { keyword: '' })
          .then((r) => {
            let { success, returnData } = r.data;
            if (!success) return;
            this.allPartList = returnData || [];
            resolve();
          });
      });
    },
    // 部位下拉列表的聚焦回调
    partFocus(row) {
      let checkPartArr = [];
      this.partList.forEach((item) => {
        if (item.partCode !== row.partCode) {
          checkPartArr.push(item.partCode);
        }
      });
      let filterPartList = this.allPartList.filter((item) => {
        return !checkPartArr.includes(item.partCode);
      });
      console.log(filterPartList);
      this.filterPartList = filterPartList;
    },
    // 部位下拉列表选择的回调
    partChange(row, rowIdx) {
      console.log(rowIdx, row);
      let isHave = false;
      this.allPartList.some((item) => {
        if (item.partCode === row.partCode) {
          isHave = true;
          row.partName = item.partName;
          return true;
        }
      });
      for (let idx = rowIdx + 1; idx < rowIdx + row.rowSpan; idx++) {
        let partRow = this.partList[idx];
        partRow.partCode = row.partCode;
        partRow.partName = row.partName;
      }
      console.log(this.partList);
      if (!isHave) {
        row.partName = row.partCode;
        this.SaveMajorPositivePart(row);
      }
    },
    // 保存重大阳性部位
    // SaveMajorPositivePart(row){
    //   this.$ajax.post(this.$apiUrls.SaveMajorPositivePart,{partName: row.partCode}).then(r=>{
    //
    //     row.partCode = r.data.returnMsg
    //     console.log(row);
    //     this.getAllPartList();
    //   })
    // },
    // 部位输入框的聚焦回调
    partRowInputFocus(idx) {
      this.focusIdx = idx;
    },
    // 过滤部位下拉的回调
    partListFilter(value) {
      this.filterPartList = this.allPartList.filter((item) => {
        return item.partCode.includes(value) || item.partName.includes(value);
      });
    },
    // 获取重大阳性关键字列表
    GetMajorPositiveKeywords() {
      this.keywordListObj = {};
      this.$ajax
        .post(this.$apiUrls.GetMajorPositiveKeywords, { keyword: '' })
        .then((r) => {
          this.keywordList = r.data?.returnData || [];
          this.keywordList.forEach((item) => {
            this.keywordListObj[item.keywordCode] = item.keywordName;
          });
        });
    },
    // 保存重大阳性关联的部位
    saveRelevancePart() {
      let datas = [];
      let datasIdx;
      this.isEmpty = false;
      let addPartNames = this.addPartList.map((item) => {
        if (item.partName.trim() === '') {
          this.isEmpty = true;
        }
        datas.push({
          partCode: item.partCode,
          partName: item.partName,
          positiveCode: this.checkMajorPositive.positiveCode,
          positiveName: this.checkMajorPositive.positiveName,
          keywords: this.addKeywordList,
          parts: []
        });
        return item.partName;
      });
      this.partList.map((item) => {
        if (item.partName?.trim() === '' || isNaN(item.size)) {
          this.isEmpty = true;
        }
        let parts = {
          ageHighest: item.ageHighest,
          ageLowest: item.ageLowest,
          echoType: item.echoType,
          size: item.size
        };
        let idx = addPartNames.indexOf(item.partName);
        if (idx != -1) {
          datas[idx].parts.push(parts);
          return;
        }
        if (item.rowSpan) {
          datas.push({
            partCode: item.partCode,
            partName: item.partName,
            positiveCode: this.checkMajorPositive.positiveCode,
            positiveName: this.checkMajorPositive.positiveName,
            keywords: [],
            parts: [parts]
          });
          datasIdx = datas.length - 1;
          addPartNames.push(item.partName);
          return;
        }
        datas[datasIdx].parts.push(parts);
      });
      this.addKeywordList.some((item) => {
        if (item.keywordName.trim() === '') {
          this.isEmpty = true;
          return true;
        }
      });
      if (this.isEmpty) {
        this.$message({
          message: '部位、关键词、尺寸(低值)、高值、回声情况不能为空！',
          type: 'warning'
        });
        return;
      }
      console.log(datas);
      this.$ajax
        .post(this.$apiUrls.SaveMapPartWithKeywordMajorPositive, datas)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$message({
            message: '保存成功！',
            type: 'success',
            showClose: true
          });
        });
    },
    // 抽屉的tab切换回调
    drawerTabClick(tabId) {
      this.drawerTabIdx = tabId;
    },
    // 维护抽屉关闭的回调
    preserveDrawerClose() {
      this.drawerTabIdx = 1;
    }
  }
};
</script>
<style lang="less" scoped>
.majorPositiveList_page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-weight: 600;
  header {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
    .el-form-item {
      margin-bottom: 18px;
    }
    .el-button--small {
      width: 64px;
    }
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-container.is-vertical {
    height: 100%;
  }
  .el-header,
  .el-main {
    padding: 0;
  }

  .searchWrap .el-form-item:last-child {
    margin-right: 0;
    margin-left: 10px;
  }
  .indexPage .contentWrap .tabPage {
    margin-top: 0;
  }
  .el-pagination {
    text-align: center;
  }
  .el-drawer.rtl {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #2d3436;
    padding: 0 20px 0 10px;
  }
  .el-drawer__body {
    padding-right: 20px;

    .el-form-item {
      margin-bottom: 18px;
    }
  }
  .positive_drawer,
  .part_drawer {
    /deep/.el-drawer__body {
      padding: 5px;
    }
    /deep/.el-drawer__header {
      margin-bottom: 5px;
    }
  }
  .part_drawer,
  .preserve_drawer {
    .partDrawer_wrap {
      display: flex;
      flex-direction: column;
      height: 100%;
    }
    .part_table {
      flex: 1;
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      /deep/.el-table__body tbody tr:nth-child(even) td {
        background-color: #fff;
      }
      .keyword_wrap {
        display: flex;
        overflow: auto;
        li {
          flex: 1;
          flex-shrink: 0;
        }
      }
      .source_wrap {
        /deep/.el-input-number {
          width: 100%;
        }
      }
    }
    .red_color {
      /deep/input {
        border-color: red;
      }
      /deep/ select {
        border-color: red;
      }
    }
  }
  .preserve_drawer {
    /deep/header {
      margin-bottom: 0;
      padding: 5px;
      border-bottom: 1px solid #ccc;
      ul {
        display: flex;
        font-weight: normal;
        li {
          padding: 5px;
          cursor: pointer;
        }
        .active {
          color: #1770df;
        }
      }
    }
    .expression_dom {
      /deep/.allBtn {
        header {
          line-height: 26px;
        }
      }
    }
    /deep/.el-drawer__body {
      padding: 5px;
    }
  }
}
</style>
<style lang="less">
.el-dialog {
  width: 480px;
}
.el-table .el-table__cell {
  padding: 5px 0;
}
.el-main .el-checkbox__inner {
  width: 20px;
  height: 20px;
}
.el-drawer__body {
  padding-right: 20px;
}
.el-drawer__header,
.el-form-item__label {
  color: #2d3436;
}
.positive_popover {
  max-height: 300px;
  overflow: auto;
  .my-list-item {
    display: flex;
    cursor: pointer;
    span {
      flex: 1;
      flex-shrink: 0;
    }
  }
}
.disease_select {
  width: 300px;
  .el-select-dropdown__item {
    height: auto;
  }
  .el-select-dropdown__item.hover {
    background-color: transparent;
  }
}
</style>
