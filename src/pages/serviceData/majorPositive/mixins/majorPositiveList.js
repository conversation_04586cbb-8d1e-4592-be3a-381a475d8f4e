import XEUtils from 'xe-utils';
import { dataUtils } from '../../../../common';
export default {
  data() {
    return {
      menuList: [],
      fixed_menuList: [],
      // 定义属性控件节点的数据
      defaultProps: {
        children: 'children',
        label: 'title'
      },
      loading: false,
      defaultExpanded: []
    };
  },
  methods: {
    // 获取疾病列表
    GetDiseaseInDept() {
      return new Promise((resolve, reject) => {
        this.$ajax.post(this.$apiUrls.GetDiseaseInDept).then((r) => {
          let { success, returnData } = r.data;
          let newData1 = [];
          returnData.push(returnData.shift());
          returnData.forEach((item, index) => {
            newData1.push({
              id: 'id' + index,
              title: item.clsName,
              children: [],
              _LEVEL: 0
            });
            item.diseases.forEach((data, idx) => {
              newData1[index].children.push({
                id: 'two_id' + data.diseaseCode,
                diseaseCode: data.diseaseCode,
                title: data.diseaseName,
                icon: 'iconfont icon-leixing1',
                path: 'DiseaseInformation',
                parentIdx: [index],
                _LEVEL: 1
              });
            });
          });
          this.fixed_menuList = newData1;
          resolve(this.fixed_menuList);
        });
      });
    },
    // 疾病下拉的搜索回调
    diseaseFilter(query) {
      console.log(query);
      this.defaultExpanded = [];
      if (query !== '') {
        this.loading = true;
        setTimeout(() => {
          this.loading = false;
          let menuList = [];
          this.fixed_menuList.map((item) => {
            let childArr = item.children.filter((twoItem) => {
              return twoItem.title.toLowerCase().includes(query.toLowerCase());
            });
            if (childArr.length !== 0) {
              let row = dataUtils.deepCopy(item);
              row.children = childArr;
              this.defaultExpanded.push(row.id);
              menuList.push(row);
            }
          });
          this.menuList = menuList;
        }, 200);
      } else {
        this.menuList = this.fixed_menuList;
      }
    },
    // 疾病下拉的选中回调
    handleNodeClick(data, node, component) {
      console.log(data, node, component);

      if (data._LEVEL === 0) {
        return;
      }
      this.popupForm.diseaseName = data.title;
      this.$refs.disease_Ref.blur();
    },
    // 疾病下拉的聚焦回调
    diseaseSelectFocus() {
      this.$set(this, 'defaultExpanded', []);
      this.menuList = this.fixed_menuList;
      setTimeout(() => {
        let dom = document.querySelector(
          '.disease_select .el-select-dropdown__wrap'
        );
        dom.scrollTo({ top: 0 });
        console.log(dom);
      }, 10);
    }
  },
  mounted() {
    this.GetDiseaseInDept();
  }
};
