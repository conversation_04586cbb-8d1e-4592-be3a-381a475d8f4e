<template>
  <div class="TCMManage_page" id="BasedOnTheCode">
    <div class="left_wrap wrap_style" id="LeftWrap">
      <header>
        <h3>模板维护</h3>
      </header>
      <div class="left_content">
        <el-menu :default-active="menuId" class="el-menu-vertical-demo">
          <el-menu-item
            :index="item.constitutionCode"
            v-for="(item, index) in menuList"
            :key="index"
            @click="menuSelect(item, index)"
          >
            <i class="iconfont icon-biaodan icons"></i>
            <span slot="title">{{ item.constitutionName }}</span>
          </el-menu-item>
        </el-menu>
      </div>
    </div>
    <div class="right_wrap wrap_style">
      <header>
        <el-button
          class="blue_btn"
          size="small"
          @click="editBtn"
          icon="iconfont icon-bianji"
          >{{ isEditFlag ? '编辑' : '取消编辑' }}</el-button
        >
        <el-button
          class="blue_btn"
          size="small"
          @click="saveBtn"
          icon="iconfont icon-baocun"
          >保存</el-button
        >
      </header>
      <div class="right_content">
        <el-form
          :model="formInfo"
          ref="form_Ref"
          label-width="110px"
          class="demo-dynamic"
        >
          <el-form-item label="编号" prop="">
            <el-input
              disabled
              v-model.trim="formInfo.constitutionCode"
            ></el-input>
          </el-form-item>
          <el-form-item label="名称" prop="">
            <el-input
              :disabled="isEditFlag"
              v-model.trim="formInfo.constitutionName"
            ></el-input>
          </el-form-item>
          <el-form-item label="体质特征" prop="">
            <el-input
              :disabled="isEditFlag"
              v-model.trim="formInfo.physiqueFeature"
            ></el-input>
          </el-form-item>
          <el-form-item label="体质定义" prop="">
            <el-input
              :disabled="isEditFlag"
              type="textarea"
              :autosize="{ minRows: 2 }"
              placeholder="请输入内容"
              v-model.trim="formInfo.physiqueDefine"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="体质成因" prop="">
            <el-input
              :disabled="isEditFlag"
              v-model.trim="formInfo.physiqueOrigin"
            ></el-input>
          </el-form-item>
          <el-form-item label="体质介绍" prop="">
            <el-input
              :disabled="isEditFlag"
              type="textarea"
              :autosize="{ minRows: 2 }"
              placeholder="请输入内容"
              v-model.trim="formInfo.physiqueIntroduce"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="形态特征" prop="">
            <el-input
              :disabled="isEditFlag"
              v-model.trim="formInfo.formFeature"
            ></el-input>
          </el-form-item>
          <el-form-item label="常见表现" prop="">
            <el-input
              :disabled="isEditFlag"
              type="textarea"
              :autosize="{ minRows: 2 }"
              placeholder="请输入内容"
              v-model.trim="formInfo.commonManifestations"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="心理特征" prop="">
            <el-input
              :disabled="isEditFlag"
              v-model.trim="formInfo.mentalProfile"
            ></el-input>
          </el-form-item>
          <el-form-item label="发病倾向" prop="">
            <el-input
              :disabled="isEditFlag"
              type="textarea"
              :autosize="{ minRows: 2 }"
              placeholder="请输入内容"
              v-model.trim="formInfo.incidenceTendency"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="人群分布" prop="">
            <el-input
              :disabled="isEditFlag"
              type="textarea"
              :autosize="{ minRows: 2 }"
              placeholder="请输入内容"
              v-model.trim="formInfo.populationDistribution"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="适应能力" prop="">
            <el-input
              :disabled="isEditFlag"
              v-model.trim="formInfo.adaptability"
            ></el-input>
          </el-form-item>
          <el-form-item label="生活起居" prop="">
            <el-input
              :disabled="isEditFlag"
              type="textarea"
              :autosize="{ minRows: 2 }"
              placeholder="请输入内容"
              v-model.trim="formInfo.dailyLife"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="精神调摄" prop="">
            <el-input
              :disabled="isEditFlag"
              type="textarea"
              :autosize="{ minRows: 2 }"
              placeholder="请输入内容"
              v-model.trim="formInfo.mentalAdjustment"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="形态运动" prop="">
            <el-input
              :disabled="isEditFlag"
              type="textarea"
              :autosize="{ minRows: 2 }"
              placeholder="请输入内容"
              v-model.trim="formInfo.bodyMovement"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="饮食调养" prop="">
            <el-input
              :disabled="isEditFlag"
              type="textarea"
              :autosize="{ minRows: 2 }"
              placeholder="请输入内容"
              v-model.trim="formInfo.dietRecuperation"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="推荐药膳" prop="">
            <el-input
              :disabled="isEditFlag"
              type="textarea"
              :autosize="{ minRows: 2 }"
              placeholder="请输入内容"
              v-model.trim="formInfo.medicinalDiet"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="音乐疗法" prop="">
            <el-input
              :disabled="isEditFlag"
              type="textarea"
              :autosize="{ minRows: 2 }"
              placeholder="请输入内容"
              v-model.trim="formInfo.musicTherapy"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="常用药物" prop="">
            <el-input
              :disabled="isEditFlag"
              type="textarea"
              :autosize="{ minRows: 2 }"
              placeholder="请输入内容"
              v-model.trim="formInfo.commonlyUsedDrugs"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="经典名方" prop="">
            <el-input
              :disabled="isEditFlag"
              type="textarea"
              :autosize="{ minRows: 2 }"
              placeholder="请输入内容"
              v-model.trim="formInfo.classicFormula"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="中成药物" prop="">
            <el-input
              :disabled="isEditFlag"
              type="textarea"
              :autosize="{ minRows: 2 }"
              placeholder="请输入内容"
              v-model.trim="formInfo.chinesePatentDrug"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="经络保健" prop="">
            <el-input
              :disabled="isEditFlag"
              type="textarea"
              :autosize="{ minRows: 2 }"
              placeholder="请输入内容"
              v-model.trim="formInfo.meridianHealthCare"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="个性化建议模版" prop="">
            <el-input
              :disabled="isEditFlag"
              type="textarea"
              :autosize="{ minRows: 2 }"
              placeholder="请输入内容"
              v-model.trim="formInfo.suggestedTemplate"
            >
            </el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { dataUtils } from '@/common';

export default {
  name: 'TCMManage',

  data() {
    return {
      isEditFlag: true,
      menuId: 'A',
      checkIndex: 0,
      menuList: [],
      formInfo: {},
      checkMenu: {}
    };
  },
  methods: {
    // 获取体质信息
    getCONInfo() {
      this.$ajax.post(this.$apiUrls.R_CodeConstitution).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.menuList = returnData || [];
        this.formInfo = dataUtils.deepCopy(this.menuList[this.checkIndex]);
        this.checkMenu = dataUtils.deepCopy(this.menuList[this.checkIndex]);
      });
    },
    // 编辑
    editBtn() {
      this.isEditFlag = !this.isEditFlag;
      this.$message({
        message: this.isEditFlag ? '已取消编辑' : '已开启了编辑',
        type: 'success',
        showClose: true
      });
    },
    // 保存
    saveBtn() {
      console.log(this.formInfo);
      this.$ajax
        .post(this.$apiUrls.CUD_CodeConstitution + '/Update', this.formInfo)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$message({
            message: '修改成功',
            type: 'success',
            showClose: true
          });
          this.isEditFlag = true;
          this.getCONInfo();
        });
    },
    // 选择菜单
    menuSelect(item, index) {
      console.log(item, this.formInfo);
      if (JSON.stringify(this.checkMenu) != JSON.stringify(this.formInfo)) {
        this.$confirm('是否保存已修改的内容?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            await this.saveBtn();
            this.switchMenuFun(item, index);
          })
          .catch(() => {
            this.switchMenuFun(item, index);
          });
        return;
      }
      this.switchMenuFun(item, index);
    },
    //  切换菜单的函数封装
    switchMenuFun(item, index) {
      this.formInfo = dataUtils.deepCopy(item);
      this.checkMenu = dataUtils.deepCopy(item);
      this.isEditFlag = true;
      this.menuId = item.constitutionCode;
      this.checkIndex = index;
    }
  },
  created() {
    this.getCONInfo();
  }
};
</script>
<style lang="less" scoped>
.TCMManage_page {
  display: flex;

  .wrap_style {
    background-color: #fff;
    border-radius: 5px;
    overflow: auto;
    position: relative;
    padding-top: 48px;

    header {
      height: 48px;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      display: flex;
      align-items: center;
      padding: 0 15px;

      h3 {
        font-size: 16px;
        color: #2d3436;
        font-weight: 600;
      }
    }
  }
  .icons {
    font-size: 16px;
    margin-right: 8px;
  }
  .left_wrap {
    width: 360px;
    margin-right: 20px;

    .el-menu-item {
      height: 32px;
      line-height: 32px;
      transition: none;
    }

    .el-menu {
      border: none;
    }
  }

  .right_wrap {
    flex: 1;
    flex-shrink: 0;
    padding-top: 58px;

    header {
      top: 10px;
      justify-content: flex-end;
    }

    .right_content {
      padding: 15px;
      height: 100%;
      overflow: auto;
    }
  }
}
</style>
<style lang="less">
.TCMManage_page {
  .el-submenu .el-menu-item:focus,
    // .el-menu-item:hover,
    .el-menu-item.is-active {
    outline: 0;
    color: #fff;
    background-color: #1770df;
    border-radius: 4px;
    border-radius: 4px;
  }
  .el-textarea__inner,
  .el-input__inner {
    color: #2d3436 !important;
    font-weight: 500;
  }
}
</style>
