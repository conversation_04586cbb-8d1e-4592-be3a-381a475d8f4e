<template>
  <div class="otherCodeMapping">
    <el-container>
      <el-header>
        <AllBtn
          :btnList="['查询', '新建', '删除', '打印', '导出']"
          :methodCreat="add"
          :methodDelete="delMore"
          :methodSearch="search"
          :methodPrint="print"
          :methodExport="exportTable"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          ref="table_ref"
          :viewTableList.sync="tableDataCopy"
          :theads.sync="viewConfig.theads"
          @rowDblclick="handleClick"
          :cell_blue="viewConfig.cell_blue"
          @selectionChange="handleSelectChangeTable"
          :tableLoading.sync="loading"
          :columnWidth="columnWidth"
          isCheck
        >
          <template #deptName="{ scope }">
            <div class="myNote" :title="scope.row.deptName">
              {{ scope.row.deptName }}
            </div>
          </template>
          <template #columnRight>
            <el-table-column
              prop="operation"
              :label="viewConfig.operation"
              width="120"
              fixed="right"
            >
              <template slot-scope="scope">
                <el-button
                  type="primary"
                  plain
                  size="mini"
                  class="view"
                  @click="viewDetails(scope.row)"
                  >对应详情</el-button
                >
              </template>
            </el-table-column>
          </template>
        </PublicTable>
      </el-main>
    </el-container>
    <el-drawer
      :title="drawerTitle"
      :visible.sync="drawers"
      :before-close="cancel"
      size="70%"
      destroy-on-close
      :wrapperClosable="false"
    >
      <DetailsDrawer
        :drawerInfo="drawerInfo"
        :viewConfigId="viewConfig.id"
        :isEdit="isEdit"
        ref="drawer_ref"
      ></DetailsDrawer>
    </el-drawer>
    <!-- 打印的表格 -->
    <div class="print_table">
      <PublicTable
        id="printHtml"
        :viewTableList.sync="checkTableList"
        :theads.sync="viewConfig.theads"
        :cell_blue="viewConfig.cell_blue"
        :columnWidth="columnWidth"
      >
      </PublicTable>
    </div>
  </div>
</template>

<script>
import { export2Excel } from '@/common/excelUtil';
import moment from 'moment';
import PublicTable from '../../../components/publicTable.vue';
import AllBtn from '../allBtn.vue';
import printJS from '@/common/printJS';
import DetailsDrawer from './detailsDrawer.vue';
export default {
  name: 'otherCodeMapping',
  components: {
    PublicTable,
    AllBtn,
    DetailsDrawer
  },
  props: {
    viewConfig: {
      type: Object,
      default: {}
    },
    claName: {
      type: String,
      default: ''
    },
    //总传
    allInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      drawerTitle: '',
      drawerInfo: {}, //总传
      drawers: false,
      formInline: '',
      codeArr: [],
      loading: false,
      disabled: false,
      tableDataCopy: [],
      tableData: [],
      drawers: false,
      isAdd: true,
      columnWidth: {
        expressionShow: 300,
        deptName: 490,
        name: 90
      },
      excelList: [],
      searchTableList: [],
      checkTableList: [],
      isEdit: false,
      drawerRow: {}
    };
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {
    this.getTableData();
  },
  methods: {
    //对应详情
    viewDetails(row) {
      console.log('row: ', row);
      this.drawerRow = row;
      this.drawerInfo = {};
      this.drawerInfo = this.allInfo;
      this.drawerInfo.active = 2;
      this.isEdit = true;
      this.drawers = true;
      let id = this.viewConfig.id;

      if (id == '1') {
        if (row.itemName) {
          this.drawerTitle = '计算项目：' + row.itemName + '对应';
          this.drawerInfo.title2 = row.itemName;
          this.drawerInfo.setInfo = { itemCode: row.itemCode };
          this.drawerInfo.setInfos = { itemCode: row.itemCode };
        }
      } else if (id == '2') {
        if (row.combName) {
          this.drawerTitle = row.combName + '-体检对应组合';
          this.drawerInfo.title2 = row.combName + '-体检对应组合列表';
          this.drawerInfo.setInfo = { combCode: row.combCode };
          this.drawerInfo.setInfos = { combCode: row.combCode };
        }
      } else if (id == '3') {
        if (row.name) {
          this.drawerTitle = row.name + '-对应科室';
          this.drawerInfo.title2 = row.name + '-对应科室列表';
          this.drawerInfo.setInfo = { operatorCode: row.operatorCode };
          this.drawerInfo.setInfos = { operatorCode: row.operatorCode };
        }
      }

      //执行获取下拉,左右表格数据
      this.$nextTick(() => {
        this.$refs.drawer_ref.getAll();
        this.$refs.drawer_ref.clearInput();
        this.$refs.drawer_ref.textarea = row.expression;
      });
    },
    // 获取表格信息数据
    getTableData() {
      this.loading = true;
      this.$ajax.post(this.viewConfig.apiUrl.get).then((r) => {
        console.log('r: ', r);
        const { returnData, success } = r.data;
        if (!success) {
          return;
        }
        this.loading = false;
        this.tableData = returnData;
        this.tableDataCopy = returnData;
        this.$refs.allBtn_Ref.searchInfo = '';
      });
    },
    //查询
    search() {
      // this.getTableData();
      this.formInline = this.$refs.allBtn_Ref.searchInfo; //获取组件文本框的值
      this.tableLoading = true;
      if (!this.formInline) {
        this.$nextTick(() => {
          this.tableLoading = false;
        });

        return (this.tableDataCopy = this.tableData);
      }
      let newTableData = [];
      this.tableData.map((item) => {
        let flag = new Function(
          'item',
          'searchVal',
          `return ${this.viewConfig.order}`
        )(item, this.formInline.trim());
        if (flag) {
          newTableData.push(item);
        }
      });
      this.tableDataCopy = newTableData;
      this.$nextTick(() => {
        this.tableLoading = false;
      });
    },

    //新增
    add() {
      this.isAdd = true;
      this.drawers = true;
      this.disabled = false;
      this.isEdit = false;
      this.drawerInfo = this.allInfo;
      this.drawerInfo.active = 1;
      if (this.viewConfig.id == '1') {
        this.drawerTitle = '新建计算项目对应';
      } else if (this.viewConfig.id == '2') {
        this.drawerTitle = '新建体检对应组合';
      } else if (this.viewConfig.id == '3') {
        this.drawerTitle = '新建对应科室';
      }
      this.$nextTick(() => {
        this.$refs.drawer_ref.getAll();
        this.$refs.drawer_ref.clearInput();
      });
    },
    //新增提交
    addSubmit() {
      this.$ajax
        .post(`${this.$apiUrl.CU_CodeBasicCls}/Create`, this.popupForm)
        .then((r) => {
          console.log('r: ', r);
          const { returnData, success } = r.data;
          if (!success) {
            return;
          }
          this.getTableData();
          this.$message.success('新增成功!');
          this.drawer = false;
        });
    },
    //编辑
    handleClick(row) {
      // console.log("row", row);
      // this.isAdd = false;
      // this.drawer = true;
      // this.disabled = true;
      // this.popupForm = {
      //   clsCode: row.clsCode,
      //   clsName: row.clsName
      // };
    },
    //编辑提交
    editSubmit() {
      this.$ajax
        .post(`${this.$apiUrl.CU_CodeBasicCls}/Update`, this.popupForm)
        .then((r) => {
          const { returnData, success } = r.data;
          if (!success) {
            return;
          }
          this.getTableData();
          this.$message.success('修改成功!');
          this.drawer = false;
        });
    },
    //提交
    submit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.isAdd) {
            this.addSubmit();
            return;
          }
          this.editSubmit();
        } else {
          return false;
        }
      });
    },
    // 取消
    async cancel() {
      this.drawer = false;
      this.drawers = false;
      await this.getTableData();
      this.$nextTick(() => {
        this.search();
        if (!this.$refs.drawer_ref) return;
        this.$refs.drawer_ref.textarea = '';
      });
      // this.$refs.ruleForm.resetFields();
    },
    //复选框勾选操作
    handleSelectChangeTable(val) {
      // console.log("val: ", val);
      this.excelList = val;
      this.codeArr = val;

      console.log('this.codeArr: ', this.codeArr);
    },
    //多条删除
    delMore() {
      if (this.codeArr.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据!',
          type: 'warning'
        });
      }
      if (this.codeArr.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.$ajax
              .post(this.viewConfig.apiUrl.delete, this.codeArr)
              .then((r) => {
                console.log('r111: ', r);
                const { returnData, success } = r.data;
                if (!success) {
                  return;
                }
                this.$message.success('删除成功!');
                this.getTableData();
              });
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    // 导出excel
    exportTable() {
      if (this.excelList.length == 0) {
        this.$message.warning('请至少选择一条数据进行导出!');
        return;
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const entries = Object.entries(this.viewConfig.theads);
        const columns = [];
        for (const [keys, values] of entries) {
          columns.push({
            key: keys,
            title: values
          });
        }
        this.excelList.map((item, i) => {
          item.index = i + 1;
        });
        const title = this.claName;
        this.$nextTick(() => {
          export2Excel(columns, this.excelList, title);
          this.$refs.table_ref.$refs.tableCom_Ref.clearSelection();
        });
      });
    },
    //打印
    print() {
      let tableCom_Ref = this.$refs.table_ref.$refs.tableCom_Ref;
      console.log('tableCom_Ref: ', tableCom_Ref.selection);
      if (tableCom_Ref.selection.length == 0) {
        return this.$message({
          message: '请至少选择一条数据进行打印！',
          type: 'warning',
          showClose: true
        });
      }
      let viewTableList_prop = this.$refs.table_ref._props.viewTableList;
      if (tableCom_Ref.store.states.isAllSelected) {
        this.checkTableList = viewTableList_prop;
      } else {
        this.checkTableList = tableCom_Ref.selection;
      }
      setTimeout(() => {
        printJS('printHtml');
      }, 500);
    }
  }
};
</script>
<style lang="less" scoped>
.otherCodeMapping {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-weight: 600;
  // padding: 0 10px;
  header {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-container.is-vertical {
    height: 100%;
  }
  .el-header,
  .el-main {
    padding: 0;
  }
  .searchWrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  // .search-btn {
  //   padding: 8px 25px;
  //   font-size: 14px;
  // }
  // /deep/.el-drawer__body {
  //   padding: 0 20px;
  //   .el-form-item {
  //     margin-bottom: 12px;
  //   }
  // }
  /deep/.el-drawer__header {
    background: #d1e2f9;
    margin-bottom: 0;
    padding: 18px;
  }
  /deep/.el-drawer__body {
    // background: #f0f2f3;
    margin: 0 !important;
    padding: 0 !important;
  }
  /deep/.el-drawer__header,
  /deep/.el-form-item__label {
    color: #2d3436;
  }
  /deep/.el-checkbox__inner {
    width: 20px;
    height: 20px;
  }
  /deep/.el-checkbox__inner::after {
    left: 7px;
    top: 3px;
  }
  /deep/.el-checkbox__inner::before {
    top: 8px;
  }
  .search-input {
    margin-right: 10px;
    // width: 322px;
  }
  .select {
    width: 100%;
  }
  .table-text {
    color: #1770df;
    // cursor: pointer;
  }
  .print_table {
    display: none;
  }
  .myNote {
    display: -webkit-box;
    text-overflow: ellipsis;
    overflow: hidden;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }
}
</style>
