<template>
  <div class="detailsDrawer">
    <div class="stepDiv" v-show="drawerInfo.isHead">
      <el-steps :active="drawerInfo.active">
        <el-step title="步骤1:" :description="drawerInfo.sel1"></el-step>
        <el-step title="步骤2:" :description="drawerInfo.sel2"></el-step>
      </el-steps>
      <div class="btnDiv">
        <el-button
          style="margin-top: 12px"
          @click="last"
          v-show="drawerInfo.active == 2"
          :disabled="isEdit"
          >上一步</el-button
        >
        <el-button
          style="margin-top: 12px"
          @click="next"
          v-show="drawerInfo.active == 1"
          >下一步</el-button
        >
        <el-button
          style="margin-top: 12px"
          size="small"
          class="blue_btn"
          icon="iconfont icon-baocun"
          @click="saveClick"
          v-show="
            drawerInfo.active == 2 && drawerInfo.isExpression && dataIsEdit
          "
          >保存</el-button
        >
      </div>
    </div>
    <!-- 第一页 -->
    <div
      class="bodyDiv1"
      v-show="drawerInfo.active == 1 && drawerInfo.title !== ''"
    >
      <ul class="record_head head1">
        <li>
          <div class="every_inp">
            <label>{{ drawerInfo.title }}</label>
          </div>
        </li>
        <li>
          <div class="every_inp1">
            <el-select
              v-model.trim="selVal"
              placeholder="请选择"
              size="small"
              clearable
              filterable
              @change="getTable1"
              v-show="drawerInfo.isSel"
            >
              <el-option
                v-for="(item, index) in selList1"
                :key="index"
                :label="viewConfigId !== '3' ? item.clsName : item.deptName"
                :value="viewConfigId !== '3' ? item.clsCode : item.deptCode"
              >
              </el-option>
            </el-select>
            <el-input
              v-model.trim="iptVal"
              @input="getTable1"
              @clear="getTable1"
              size="small"
              style="margin: 0 20px"
              clearable
              placeholder="代码/名称"
            ></el-input>
            <el-button
              size="small"
              class="blue_btn btn"
              icon="iconfont icon-search"
              @click="getTable1"
              >搜索</el-button
            >
          </div>
        </li>
      </ul>
      <div class="record_table">
        <PublicTable
          :viewTableList="viewList"
          :theads="drawerInfo.drawerThead"
          @currentChange="handleClick"
          :isSortShow="false"
          :cell_red="drawerInfo.cell_red"
          @rowDblclick="next"
        >
        </PublicTable>
      </div>
    </div>
    <!-- 互斥组合第一页 -->
    <div
      class="mutex"
      v-show="drawerInfo.active == 1 && drawerInfo.title === ''"
    >
      <el-form
        :model="popupForm"
        ref="ruleForm"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="互斥代码" prop="mutexCode">
          <el-input
            v-model.trim="popupForm.mutexCode"
            autocomplete="off"
            size="small"
            :disabled="disabled"
            placeholder="请输入互斥代码"
          ></el-input>
        </el-form-item>
        <el-form-item label="互斥名称" prop="mutexName">
          <el-input
            v-model.trim="popupForm.mutexName"
            autocomplete="off"
            size="small"
            placeholder="请输入互斥名称"
          ></el-input>
        </el-form-item>
        <!-- <el-form-item label="组合代码" prop="combCode">
          <el-input
            v-model.trim="popupForm.combCode"
            autocomplete="off"
            size="small"
            placeholder="请输入组合代码"
          ></el-input>
        </el-form-item>
        <el-form-item label="组合名称" prop="combName">
          <el-input
            v-model.trim="popupForm.combName"
            autocomplete="off"
            size="small"
            placeholder="请输入组合名称"
          ></el-input>
        </el-form-item> -->
      </el-form>
    </div>
    <!-- 第二页 -->
    <div
      :class="drawerInfo.isHead ? 'fullbodyDiv' : 'bodyDiv'"
      v-show="drawerInfo.active == 2 && !drawerInfo.isExpression"
    >
      <div class="leftBody">
        <ul class="record_head">
          <li>
            <div class="every_inp">
              <label>{{ drawerInfo.title1 }}</label>
              <p v-show="drawerInfo.isSel">
                <el-select
                  v-model.trim="leftSel"
                  placeholder="请选择"
                  size="small"
                  clearable
                  filterable
                  @change="getleftList"
                >
                  <el-option
                    v-for="(item, index) in selList"
                    :key="index"
                    :label="item.clsName"
                    :value="item.clsCode"
                  >
                  </el-option>
                </el-select>
              </p>
            </div>
            <div class="every_inp1">
              <p>
                <el-input
                  v-model.trim="leftIpt"
                  @input="getleftList"
                  @clear="getleftList"
                  size="small"
                  placeholder="代码/名称"
                  clearable
                ></el-input>
              </p>
            </div>
            <el-button
              size="small"
              class="blue_btn btn"
              icon="iconfont icon-search"
              @click="getleftList"
              >搜索</el-button
            >
          </li>
        </ul>
        <div class="record_table">
          <PublicTable
            :viewTableList="leftList"
            :theads="drawerInfo.drawerTheads"
            :isCheck="drawerInfo.isArchive === 1 ? false : true"
            :isSortShow="true"
            :cell_red="drawerInfo.cell_red"
            @currentChange="leftHandleClick"
            @selectionChange="selectionChange"
            ref="leftTable"
          >
            <template #sex="{ scope }">
              {{ storeGetters['Sex'][scope.row.sex] }}
            </template>
            <template #positiveType="{ scope }">
              {{ storeGetters['PositiveType'][scope.row.positiveType] }}
            </template>
          </PublicTable>
        </div>
      </div>
      <div class="centerBody">
        <el-button
          size="small"
          class="blue_btn add-del"
          style="margin-bottom: 40px"
          @click="addComb"
          >添加
          <i class="iconfont icon-Rightxiangyou34"></i>
        </el-button>
        <el-button
          size="small"
          class="red_btn add-del"
          @click="delComb"
          icon="iconfont icon-Leftxiangzuo35"
        >
          删除</el-button
        >
        <el-button
          v-if="drawerInfo.isCopy"
          style="margin-top: 40px"
          size="small"
          class="red_btn add-del"
          @click="dialogVisible = true"
          icon="el-icon-document-copy"
        >
          复制</el-button
        >
      </div>
      <div class="rightBody">
        <ul class="record_head">
          <li>
            <div class="every_inp">
              <label :title="drawerInfo.title2">{{ drawerInfo.title2 }}</label>
            </div>
            <div class="every_inp1">
              <el-popover
                placement="bottom"
                width="200"
                trigger="hover"
                @after-enter="showDiscount"
                @after-leave="closePopover"
                v-show="C_discount"
              >
                <div style="display: flex">
                  <el-input-number
                    ref="ref_input"
                    @keyup.enter.native.stop="discountConfirmAll"
                    size="mini"
                    v-model="discountValue"
                    controls-position="right"
                    :precision="2"
                    :min="0"
                    :step="0.01"
                    :max="1"
                  ></el-input-number>
                  <el-button
                    type="primary"
                    style="margin-left: 10px"
                    size="mini"
                    @click="discountConfirmAll"
                    >确 定</el-button
                  >
                </div>
                <span
                  slot="reference"
                  type="text"
                  style="
                    white-space: nowrap;
                    display: flex;
                    align-items: center;
                    color: #1770df;
                    margin-left: 10px;
                    cursor: pointer;
                    margin-right: 10px;
                  "
                  icon="iconfont icon-shanchu"
                >
                  <i class="el-icon-price-tag"></i>
                  批打折
                </span>
              </el-popover>
              <p>
                <el-input
                  style="min-width: 150px"
                  v-model.trim="rightIpt"
                  @input="getrightData"
                  @clear="getrightData"
                  placeholder="代码/名称"
                  size="small"
                  clearable
                >
                </el-input>
              </p>
            </div>
            <el-button
              size="small"
              class="blue_btn btn"
              icon="iconfont icon-search"
              @click="getrightData"
              >搜索</el-button
            >
          </li>
        </ul>
        <div class="record_table">
          <div :class="drawerInfo.isMterials ? 'tableDiv' : 'tableDiv1'">
            <PublicTable
              :viewTableList="rightData"
              :theads="
                drawerInfo.isRightTheads
                  ? drawerInfo.rightTheads
                  : drawerInfo.drawerTheads
              "
              isCheck
              :isSortShow="true"
              :highlightCurrentRow="false"
              :cell_red="drawerInfo.cell_red"
              @selectionChange="rightSelectionChange"
              :columnWidth="columnWidth"
              ref="rightTable"
            >
              <template #sex="{ scope }">
                {{ storeGetters['Sex'][scope.row.sex] }}
              </template>
              <template #positiveType="{ scope }">
                {{ storeGetters['PositiveType'][scope.row.positiveType] }}
              </template>
              <template #isPrimary="{ scope }">
                <el-switch
                  @change="changeSwitch(scope.row)"
                  v-model="scope.row.isPrimary"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </template>
              <template #discount="{ scope }" v-if="C_discount">
                <el-popover
                  placement="bottom"
                  width="200"
                  trigger="hover"
                  @after-enter="showDiscount"
                  @after-leave="closePopover"
                >
                  <div style="display: flex">
                    <el-input-number
                      ref="ref_input"
                      @keyup.enter.native.stop="discountConfirm(scope.row)"
                      size="mini"
                      v-model="discountValue"
                      controls-position="right"
                      :precision="2"
                      :min="0"
                      :step="0.01"
                      :max="1"
                    ></el-input-number>
                    <el-button
                      type="primary"
                      style="margin-left: 10px"
                      size="mini"
                      @click="discountConfirm(scope.row)"
                      >确 定</el-button
                    >
                  </div>
                  <div slot="reference" style="cursor: pointer">
                    {{ scope.row.discount }}
                  </div>
                </el-popover>
              </template>
            </PublicTable>
          </div>
          <div class="all_price" v-show="drawerInfo.isMterials">
            <p>合计</p>
            <p>{{ totalSumAll }}</p>
          </div>
        </div>
      </div>
    </div>
    <!-- 计算公式 -->
    <div
      class="bodyDiv2"
      v-show="drawerInfo.active == 2 && drawerInfo.isExpression"
    >
      <div class="leftBody">
        <ul class="record_head">
          <li>
            <div class="every_inp">
              <label>{{ drawerInfo.title1 }}</label>
              <p v-show="drawerInfo.isSel">
                <el-select
                  v-model.trim="leftSel"
                  placeholder="请选择"
                  size="small"
                  clearable
                  filterable
                  @change="getleftList"
                >
                  <el-option
                    v-for="(item, index) in selList"
                    :key="index"
                    :label="item.clsName"
                    :value="item.clsCode"
                  >
                  </el-option>
                </el-select>
              </p>
            </div>
            <div class="every_inp1">
              <p>
                <el-input
                  v-model.trim="leftIpt"
                  @input="getleftList"
                  @clear="getleftList"
                  size="small"
                  placeholder="代码/名称"
                  clearable
                ></el-input>
              </p>
            </div>
            <el-button
              size="small"
              class="blue_btn btn"
              icon="iconfont icon-search"
              @click="getleftList"
              >搜索</el-button
            >
          </li>
        </ul>
        <div class="record_table">
          <PublicTable
            :viewTableList="leftList"
            :theads="drawerInfo.drawerTheads"
            :isSortShow="false"
            @rowDblclick="handleLeftClick"
          >
          </PublicTable>
        </div>
      </div>
      <div class="rightCont2">
        <div class="symbol-box">
          <div class="check" @click="check">校验</div>
          <div class="symbol">
            <div
              v-for="item in symbolData"
              :key="item"
              @click="symbolClick(item)"
            >
              {{ item }}
            </div>
          </div>
          <div class="clear">
            <div @click="clear">清空</div>
          </div>
        </div>
        <div class="right-input">
          <header>
            <h4 class="lable">计算项目：{{ drawerInfo.title2 }}</h4>
          </header>

          <div class="textarea-box">
            <el-input
              type="textarea"
              placeholder="请输入表达式"
              v-model.trim="textarea"
              @input="textareaVale"
            >
            </el-input>
          </div>
        </div>
      </div>
    </div>
    <!-- 复制弹窗 -->
    <CopyPackage :dialogVisible.sync="dialogVisible" @determine="determine" />
  </div>
</template>

<script lang="ts">
import PublicTable from '../../../components/publicTable.vue';
import { dataUtils } from '@/common';
import CopyPackage from './copyPackage.vue';
import { apiUrls as $apiUrls, ajax as $ajax } from '@/common';
import Vue from 'vue';
import store from '@/store';

export default Vue.extend({
  name: 'detailsDrawer',
  components: {
    PublicTable,
    CopyPackage
  },
  props: {
    //总传
    drawerInfo: {
      type: Object,
      default: () => {
        return {};
      }
    },
    viewConfigId: {
      type: String,
      default: () => {
        return '';
      }
    },
    isEdit: {
      type: Boolean,
      default: () => {
        return false;
      }
    }
  },
  data() {
    return {
      active: '',
      iptVal: '', //
      selVal: '',
      leftIpt: '',
      leftSel: '',
      rightIpt: '',
      checked: false,
      searchVal: '',
      selList1: [],
      selList: [],
      // 表格数据
      tableData: [],
      loading: false,
      viewList: [],
      leftList: [],
      rightData: [],
      allPrice: '0',
      rowInfo: [], //双击第一页行
      symbolData: ['+', '-', '*', '/', '(', ')'],
      textarea: '',
      leftSelection: [],
      rightSelection: [],
      arrData: [],
      columnWidth: {
        appendCombCode: 120,
        appendCombName: 120
      },
      popupForm: {} as Record<string, any>,
      rules: {
        mutexCode: [
          { required: true, message: '请输入互斥代码', trigger: 'blur' }
        ],
        mutexName: [
          { required: true, message: '请输入互斥名称', trigger: 'blur' }
        ],
        combCode: [
          { required: true, message: '请输入组合代码', trigger: 'blur' }
        ],
        combName: [
          { required: true, message: '请输入组合名称', trigger: 'blur' }
        ]
      },
      disabled: false,
      isCheck: false,
      dataIsEdit: false,
      list1: [],
      list2: [],
      discountValue: 1,
      dialogVisible: false
    };
  },
  computed: {
    // 计算总价
    totalSumAll() {
      let totalSumAll = 0;
      this.rightData.map((item) => {
        if (!isNaN(item.price)) totalSumAll += parseFloat(item.price);
      });
      if (isNaN(totalSumAll)) {
        return 0;
      }
      return totalSumAll.toFixed(2);
    },
    C_discount() {
      let head = this.drawerInfo.isRightTheads
        ? this.drawerInfo.rightTheads
        : this.drawerInfo.drawerTheads;
      //判断head对象中是否有discount属性
      return head.hasOwnProperty('discount') && this.drawerInfo.isDiscount;
    },
    storeGetters() {
      return store.getters;
    }
  },
  created() {
    this.active = '';
    this.active = this.drawerInfo.active;
    console.log('this.drawerInfo: ', this.drawerInfo);
  },
  methods: {
    changeSwitch(row) {
      this.rightData.map((item) => {
        if (item.combCode !== row.combCode) {
          item.isPrimary = false;
        }
      });
      $ajax
        .post(
          $apiUrls.BatchSaveMutexCodeMapComb + `/${row.mutexCode}`,
          this.rightData
        )
        .then((r) => {
          if (!r.data.success) {
            return;
          }
          this.$message({
            showClose: true,
            message: '修改成功!',
            type: 'success'
          });
        });
    },
    //复制确定回调
    determine(data) {
      let params = data.map((item) => {
        return {
          clusCode: this.drawerInfo.setInfos.clusCode,
          combCode: item.combCode,
          discount: item.discount,
          originalPrice: item.originalPrice,
          price: item.price
        };
      });
      params = params.filter((item) => {
        return !this.rightData.some((i) => i.combCode == item.combCode);
      });
      console.log(params);

      console.log(this.rightData);
      if (params.length == 0) {
        return this.$message.warning('该套餐中所有组合都已添加!');
      }
      $ajax.post(this.drawerInfo.apiUrls.set, params).then((r) => {
        this.$refs.leftTable.$refs.tableCom_Ref.clearSelection();
        this.$refs.rightTable.$refs.tableCom_Ref.clearSelection();

        if (!r.data.success) {
          return;
        }
        this.$message({
          showClose: true,
          message: '添加成功!',
          type: 'success'
        });
        this.getRightData();
      });
    },
    // 左边表格单选
    leftHandleClick(row) {
      console.log('row: ', row);
      if (this.drawerInfo.isArchive === 1) {
        this.leftSelection = [];
        this.leftSelection.push(row);
      }
    },
    // 左边表格多选
    selectionChange(val) {
      console.log('val: ', val);
      this.leftSelection = val;
    },
    // 右边表格多选
    rightSelectionChange(val) {
      this.rightSelection = val;
      console.log('this.rightSelection: ', this.rightSelection);
    },
    // 计算项目公式保存
    saveClick() {
      if (!this.isCheck) {
        this.$message({
          showClose: true,
          message: '请先校验!',
          type: 'warning'
        });
        return;
      }
      console.log(this.drawerInfo);
      let data = {
        itemCode: this.drawerInfo.setInfos.itemCode,
        expression: this.textarea,
        expressionShow: ''
      };
      let arr = [];
      arr.push(data);
      console.log('arr: ', arr);
      $ajax.post(this.drawerInfo.apiUrls.set, arr).then((r) => {
        console.log('r: ', r);
        const { returnData, success } = r.data;
        if (!success) {
          return;
        }
        this.$message({
          showClose: true,
          message: '新建成功!',
          type: 'success'
        });
        this.$parent.$parent.drawers = false;
        this.getviewList();
        this.$parent.$parent.getTableData();
      });
    },
    // 符号点击
    symbolClick(item) {
      this.textarea = this.textarea + item;
    },
    // 文本域输入事件
    textareaVale(val) {
      this.textarea = val;
    },
    // 清空
    clear() {
      this.textarea = '';
      this.selectRight = [];
    },
    // 校验
    check() {
      if (!this.textarea) {
        this.$message({
          showClose: true,
          message: '请先选择需要校验的数据!',
          type: 'warning'
        });
        return;
      }
      $ajax
        .post(this.drawerInfo.apiUrls.expressionVerify, `"${this.textarea}"`)
        .then((r) => {
          console.log('r: ', r);
          const { returnData, success } = r.data;
          if (!success) {
            this.dataIsEdit = false;
            return;
          }
          this.$message({
            showClose: true,
            message: '校验成功!',
            type: 'success'
          });
          this.isCheck = true;
          this.dataIsEdit = true;
        });
    },
    // 计算公式左边表格单选
    handleLeftClick(row) {
      this.textarea = this.textarea + `[${row.itemCode}]`;
    },
    // 清空输入框,选择器值
    clearInput() {
      this.iptVal = '';
      this.selVal = '';
      this.leftIpt = '';
      this.leftSel = '';
      this.rightIpt = '';
    },
    // 上一步
    last() {
      if (this.drawerInfo.active-- <= 1) this.drawerInfo.active = 1;
    },
    // 下一步
    next() {
      console.log(this.rowInfo.length);
      if (this.drawerInfo.title == '') {
        this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            this.drawerInfo.setInfo = {
              mutexCode: this.isEdit
                ? this.drawerInfo.setInfo
                : this.popupForm.mutexCode
            };
            this.getRightData();
            if (this.drawerInfo.active++ >= 2) this.drawerInfo.active = 2;
          }
        });
      } else {
        if (this.rowInfo.length < 1) {
          this.$message({
            message: '请先选择一条信息！',
            type: 'warning',
            showClose: true
          });
          return;
        }
        if (this.drawerInfo.active++ >= 2) this.drawerInfo.active = 2;
      }
    },
    //获取项目分类下拉
    optionList() {
      if (this.drawerInfo.apiUrls.optionPort) {
        $ajax.post(this.drawerInfo.apiUrls.optionPort, []).then((r) => {
          let { returnData, success } = r.data;
          if (!success) return;
          this.$nextTick(function () {
            this.selList1 = returnData || [];
            this.selList = returnData || [];
          });
        });
      }
    },
    // 获取表一数据
    getviewList() {
      if (!this.drawerInfo.apiUrls.viewPort) return; //
      $ajax.post(this.drawerInfo.apiUrls.viewPort, []).then((r) => {
        let { returnData, success } = r.data;
        if (!success) return;
        this.$nextTick(() => {
          this.viewList = returnData || [];
          this.list1 = returnData || [];
        });
      });
    },
    // 获取左边数据
    getLeftList() {
      if (this.drawerInfo.isOnly) {
        this.onlyLeftList();
        return;
      }
      $ajax.post(this.drawerInfo.apiUrls.leftPort, []).then((r) => {
        let { returnData, success } = r.data;
        if (!success) return;
        this.$nextTick(() => {
          if (this.drawerInfo.isCopy) {
            this.leftList =
              this.rightData.length > 0
                ? returnData.filter(
                    (item) =>
                      !this.rightData.some((i) => i.combCode == item.combCode)
                  )
                : returnData || [];
          } else {
            this.leftList = returnData || [];
          }
          this.list2 = JSON.parse(JSON.stringify(this.leftList)) || [];
        });
      });
    },
    //体检项目信息
    onlyLeftList() {
      let data = {
        pageSize: 0,
        pageNumber: 0,
        itemCode: '',
        keyword: ''
      };

      console.log('[  data]-705', data);
      $ajax.post(this.drawerInfo.apiUrls.leftPort, data).then((r) => {
        let { returnData, success } = r.data;
        if (!success) return;
        this.$nextTick(() => {
          this.leftList = returnData || [];
          this.list2 = returnData || [];
        });
      });
    },
    //获取右边表格数据
    getRightData() {
      if (this.drawerInfo.isOnly) {
        this.onlyRightData();
        return;
      }
      console.log('[ this.drawerInfo.setInfo ]-696', this.drawerInfo.setInfo);
      if (!this.drawerInfo.setInfo) return;
      if (this.drawerInfo.apiUrls.rightPort) {
        this.rightData = [];
        $ajax
          .post(this.drawerInfo.apiUrls.rightPort, '', {
            query: this.drawerInfo.setInfo
          })
          .then((r) => {
            let { returnData, success } = r.data;
            if (!success) return;
            this.$nextTick(function () {
              this.rightData = returnData || [];
              this.list3 = returnData || [];
              this.filterLeftList();
            });
          });
      }
    },
    //左侧列表过滤
    filterLeftList() {
      if (this.drawerInfo.isCopy) {
        this.leftList = this.leftList.filter(
          (item) => !this.rightData.some((i) => i.combCode == item.combCode)
        );
        this.list2 = this.list2.filter(
          (item) => !this.rightData.some((i) => i.combCode == item.combCode)
        );
      }
    },
    //体检项目信息
    onlyRightData() {
      let data = {
        pageSize: 0,
        pageNumber: 0,
        itemCode: this.drawerInfo.setInfo.itemCode,
        keyword: ''
      };
      console.log('[  data]-747', data);
      $ajax.post(this.drawerInfo.apiUrls.rightPort, data).then((r) => {
        let { returnData, success } = r.data;
        if (!success) return;
        this.$nextTick(() => {
          this.rightData = returnData || [];
          this.list3 = returnData || [];
        });
      });
    },
    // 第一页表格单选
    handleClick(row) {
      console.log('row: ', row);
      if (row === null) return;
      this.rowInfo = row;
      if (this.viewConfigId == '1') {
        if (row.itemName) {
          this.drawerInfo.title2 = row.combName;
          this.drawerInfo.setInfo = { itemCode: row.itemCode };
          this.drawerInfo.setInfos = { itemCode: row.itemCode };
          this.textarea = `[${row.itemCode}]+`;
        }
      } else if (this.viewConfigId == '2') {
        if (row.combName) {
          this.drawerInfo.title2 = row.combName + '-项目对应列表';
          this.drawerInfo.setInfo = { combCode: row.combCode };
          this.drawerInfo.setInfos = { combCode: row.combCode };
        }
      } else if (this.viewConfigId == '3') {
        if (row.name) {
          this.drawerInfo.title2 = row.name + '-项目对应列表';
          this.drawerInfo.setInfo = { operatorCode: row.operatorCode };
          this.drawerInfo.setInfos = { operatorCode: row.operatorCode };
        }
      }
      this.getRightData();
    },
    //获取页面一表格数据
    getTable1() {
      this.searchIpt = this.iptVal; //获取组件文本框的值
      this.searchSel = this.selVal; //下拉
      console.log(this.searchIpt, this.searchSel);
      if (!this.searchIpt && !this.searchSel) {
        return (this.viewList = this.list1);
      }
      if (this.drawerInfo.isSel) {
        var newTableData = [];
        this.list1.map((item) => {
          let flag = new Function(
            'item',
            'searchVal',
            `return ${this.drawerInfo.orderSels}`
          )(item, this.searchSel.trim());
          if (flag) {
            newTableData.push(item);
          }
        });
        var newData = [];
        newTableData.map((item) => {
          let flag = new Function(
            'item',
            'searchVal',
            `return ${this.drawerInfo.orders}`
          )(item, this.searchIpt.trim());
          if (flag) {
            newData.push(item);
          }
        });
        this.viewList = newData;
        console.log('[  this.viewList ]-465', this.viewList);
      } else {
        var newData = [];
        this.list1.map((item) => {
          let flag = new Function(
            'item',
            'searchVal',
            `return ${this.drawerInfo.orders}`
          )(item, this.searchIpt.trim());
          if (flag) {
            newData.push(item);
          }
        });
        this.viewList = newData;
      }
    },
    //获取页面二表格数据
    getleftList() {
      this.searchIpt = this.leftIpt; //获取组件文本框的值
      this.searchSel = this.leftSel; //下拉
      console.log(this.searchIpt, this.searchSel);
      if (!this.searchIpt && !this.searchSel) {
        return (this.leftList = this.list2);
      }
      if (this.drawerInfo.isSel) {
        var newTableData = [];
        this.list2.map((item) => {
          let flag = new Function(
            'item',
            'searchVal',
            `return ${this.drawerInfo.orderSel}`
          )(item, this.searchSel.trim());
          if (flag) {
            newTableData.push(item);
          }
        });
        var newData = [];
        newTableData.map((item) => {
          let flag = new Function(
            'item',
            'searchVal',
            `return ${this.drawerInfo.order}`
          )(item, this.searchIpt.trim().toLowerCase());
          if (flag) {
            newData.push(item);
          }
        });
        this.leftList = newData;
      } else {
        var newData = [];
        this.list2.map((item) => {
          let flag = new Function(
            'item',
            'searchVal',
            `return ${this.drawerInfo.order}`
          )(item, this.searchIpt.trim());
          if (flag) {
            newData.push(item);
          }
        });
        this.leftList = newData;
      }
      console.log('[ this.leftList ]-503', this.leftList);
    },
    //右边
    getrightData() {
      this.searchIpt = this.rightIpt; //获取组件文本框的值
      console.log(this.searchIpt);
      if (!this.searchIpt) {
        return (this.rightData = this.list3);
      }
      var newTableData = [];
      let order = this.drawerInfo.isRightTheads
        ? this.drawerInfo.rightOrder
        : this.drawerInfo.order;
      if (this.searchIpt) {
        this.list3.map((item) => {
          let flag = new Function('item', 'searchVal', `return ${order}`)(
            item,
            this.searchIpt.trim()
          );
          if (flag) {
            newTableData.push(item);
          }
        });
        this.rightData = newTableData;
        console.log('[  this.rightData ]-527', this.rightData);
      }
    },
    getAll() {
      this.optionList();
      this.getviewList();
      this.getLeftList();
      this.getRightData();
    },
    /**
     * 处理传参
     * @param {Array<Record<string, any>>} selection - 选中的数据项数组
     * @param {string} text - 提示消息文本
     * @returns {Promise<Array<Record<string, any>>>} 处理后的数据数组
     */
    paramData(
      selection: Array<Record<string, any>>,
      text: string
    ): Promise<Array<Record<string, any>>> {
      return new Promise((resolve, reject) => {
        // 初始化数据
        let leftData: Array<Record<string, any>> = [];
        this.arrData = [];
        leftData = selection;

        // 先返回数据，以便调用方可以继续处理
        resolve(leftData);

        // 检查数据是否为空
        if (leftData.length === 0) {
          this.$message({
            message: text,
            type: 'warning',
            duration: this.$config.messageTime,
            showClose: true
          });
          return;
        }

        // 深拷贝参数映射配置
        const rightParam: Record<string, string> = JSON.parse(
          JSON.stringify(this.drawerInfo.param)
        );

        // 处理数据映射
        const data: Array<Record<string, any>> = [];
        const paramsArr: string[] = Object.keys(rightParam);
        const paramsValue: string[] = Object.values(rightParam);

        leftData.forEach((items, i) => {
          const paramObj: Record<string, any> = {};
          paramsArr.forEach((par, index) => {
            paramObj[par] = items[paramsValue[index]];
          });
          data.push(paramObj);
        });

        // 处理特殊情况：互斥代码
        if (this.drawerInfo.title === '') {
          this.drawerInfo.setInfos = this.isEdit
            ? this.drawerInfo.setInfos
            : this.popupForm;
        }

        // 合并数据
        data.forEach((item) => {
          const datas: Record<string, any> = {
            ...item,
            ...this.drawerInfo.setInfos
          };
          this.arrData.push(datas);
        });

        console.log('this.arrData: ', this.arrData);
      });
    },
    //添加
    addComb() {
      let leftCheckCode = [];
      let allMutexCombs = [];
      let mutexCombsText = [];
      let rightCombsCode = this.rightData.map((item) => item.combCode);
   
      this.leftSelection.map((item) => {
        //leftCheckCode.push(item.combCode);
        //allMutexCombs.push(...item.mutexCombs);
        allMutexCombs.push(...item.feeClsCode);
        // item.mutexCombs?.forEach((twoItem) => {
        //   let rightIdx = rightCombsCode.indexOf(twoItem);
        //   if (rightIdx !== -1) {
        //     !mutexCombsText.includes(item.combName) &&
        //       mutexCombsText.push(item.combName);
        //     !mutexCombsText.includes(this.rightData[rightIdx].combName) &&
        //       mutexCombsText.push(this.rightData[rightIdx].combName);
        //   }
        // });
      });

      allMutexCombs = [...new Set(allMutexCombs)];
      leftCheckCode.forEach((item, index) => {
        if (
          allMutexCombs.includes(item) &&
          !mutexCombsText.includes(this.leftSelection[index].combName)
        ) {
          mutexCombsText.push(this.leftSelection[index].combName);
        }
      });
      leftCheckCode = null;
      allMutexCombs = null;
      rightCombsCode = null;
      if (mutexCombsText.length !== 0) {
        this.$message({
          message: `${mutexCombsText.join('、')}存在互斥！`,
          type: 'warning',
          duration: this.$config.messageTime,
          showClose: true
        });
        mutexCombsText = null;
        return;
      }
      this.paramData(this.leftSelection, '请选择左边表格需要添加的数据!').then(
        (r) => {
          if (r.length == 0) return;
          let data = [];
          if (this.drawerInfo.isOnly) {
            data = data.concat(this.leftSelection);
            data.map((item) => {
              item.itemCode = this.drawerInfo.setInfo.itemCode;
            });
          } else {
            data = this.arrData;
          }
          if (this.C_discount) {
            data = data.map((item) => {
              let itemData = this.leftList.find(
                (i) => i.combCode == item.combCode
              );

              if (itemData) {
                return {
                  ...item,
                  originalPrice: itemData.price,
                  price: itemData.price,
                  discount: 1
                };
              } else {
                return item;
              }
            });
          }
          $ajax.post(this.drawerInfo.apiUrls.set, data).then((r) => {
            this.$refs.leftTable.$refs.tableCom_Ref.clearSelection();
            this.$refs.rightTable.$refs.tableCom_Ref.clearSelection();

            if (!r.data.success) {
              return;
            }
            this.$message({
              showClose: true,
              message: '添加成功!',
              type: 'success'
            });
            this.getRightData();
            if (this.drawerInfo.title === '') {
              this.drawerInfo.setInfo = this.isEdit
                ? this.drawerInfo.setInfo
                : this.popupForm.mutexCode;
              this.$parent.$parent.getItemCls();
            }
          });
        }
      );
    },
    //删除
    delComb() {
      this.paramData(this.rightSelection, '请选择右边表格需要删除的数据!').then(
        (r) => {
          if (r.length == 0) return;
          let data = [];
          if (this.drawerInfo.isOnly) {
            data = data.concat(this.rightSelection);
            data.map((item) => {
              item.itemCode = this.drawerInfo.setInfo.itemCode;
            });
          } else {
            data = this.arrData;
          }
          console.log('[ data ]-1012', data);
          $ajax.post(this.drawerInfo.apiUrls.del, data).then((r) => {
            this.$refs.leftTable.$refs.tableCom_Ref.clearSelection();
            this.$refs.rightTable.$refs.tableCom_Ref.clearSelection();
            if (!r.data.success) {
              return;
            }
            this.$message({
              showClose: true,
              message: '删除成功!',
              type: 'success'
            });
            this.getRightData();
          });
        }
      );
    },
    //点击上移
    clickUp(index) {
      this.swapArray(this.rightData, index - 1, index);
    },
    //点击下移
    clickDown(index) {
      this.swapArray(this.rightData, index, index + 1);
    },
    //数组元素互换位置
    swapArray(arr, index1, index2) {
      arr[index1] = arr.splice(index2, 1, arr[index1])[0];
      let newArr = arr.map((item, index) => {
        return {
          keywordCode: item.keywordCode,
          positiveCode: item.positiveCode,
          sortIndex: arr.length - index
        };
      });
      $ajax.post($apiUrls.UpdateKeywordMajorPositive, newArr).then((r) => {
        if (!r.data.success) {
          return;
        }
        this.$message({
          showClose: true,
          message: '顺序调整成功!',
          type: 'success'
        });
        this.getRightData();
      });
    },
    //更新组合
    updateComb(data) {
      $ajax.post($apiUrls.UpdateClusterComb, data).then((r) => {
        if (!r?.data?.success) {
          this.$message.error('更新失败！');
        }
      });
    },
    //批打折
    discountConfirmAll() {
      if (!(this.discountValue >= 0)) {
        this.showDiscount();
        return this.$message.error('请输入正确的折扣！');
      }
      let data = dataUtils.deepCopy(this.rightData);
      data.forEach((item) => {
        let price = dataUtils.multiply(item.originalPrice, this.discountValue);
        item.discount = this.discountValue;
        item.price = price;
      });
      this.rightData = data;
      this.updateComb(data);
    },
    //单独打折
    discountConfirm(data) {
      if (!(this.discountValue >= 0)) {
        this.showDiscount();
        return this.$message.error('请输入正确的折扣！');
      }
      let idx = this.rightData.findIndex(
        (item) => (item.combCode = data.combCode)
      );
      if (idx == -1) return;
      let price = dataUtils.multiply(data.originalPrice, this.discountValue);
      data.discount = this.discountValue;
      data.price = parseFloat(price);
      this.updateComb([data]);
    },
    //批打折隐藏回调
    closePopover() {
      this.discountValue = 1;
    },
    //批打折显示回调
    showDiscount() {
      this.$nextTick(() => {
        this.$refs.ref_input.focus();
      });
    }
  },
  watch: {
    rightData: {
      handler(val) {
        this.filterLeftList();
      },
      immediate: true
    }
  }
});
</script>

<style lang="less" scoped>
.detailsDrawer {
  height: 100%;
  width: 100%;
  overflow: auto;
  border-radius: 4px;
  padding: 18px;
  display: flex;
  flex-direction: column;
  .btn {
    padding: 6px 9px;
  }
  .add-del {
    padding: 9px 9px;
  }
  .stepDiv {
    display: flex;
    padding: 15px;
    .el-steps {
      width: 50%;
    }
    .btnDiv {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      height: 42px;
      .el-button {
        line-height: 0;
      }
    }
  }
  .bodyDiv {
    flex: 1;
    flex-shrink: 0;
    width: 100%;
    height: 100%;
    display: flex;
  }
  .fullbodyDiv {
    flex: 1;
    flex-shrink: 0;
    width: 100%;
    height: calc(100% - 108px);
    display: flex;
  }
  .bodyDiv1 {
    height: calc(100% - 108px);
    // padding: 15px;
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .bodyDiv2 {
    height: calc(100% - 108px);
    flex: 1;
    flex-shrink: 0;
    width: 100%;
    // height: 100%;
    display: flex;
  }
  .leftBody {
    background-color: #fff;
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: auto;
    // padding: 15px;
  }
  .centerBody {
    width: 100px;
    height: 100%;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    .el-button + .el-button {
      margin-left: 0;
    }
  }
  .rightBody {
    background-color: #fff;
    // padding: 15px;
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    overflow: auto;
    .combo_group {
      flex: 1;
    }
  }
  .head1 {
    display: flex;
    justify-content: space-between;
  }
  .record_head {
    li {
      display: flex;
      margin-bottom: 18px;
    }

    .every_inp,
    .every_inp1 {
      display: flex;
      align-items: center;
      margin-right: 10px;

      label {
        margin-right: 10px;
        width: auto;
        text-align: right;
        overflow: hidden; /*溢出的部分隐藏*/
        white-space: nowrap; /*文本不换行*/
        text-overflow: ellipsis;
      }

      p {
        flex: 1;
      }

      &:nth-child(4) {
        flex: 1;
      }
    }
    .every_inp {
      width: 290px;
    }
    .every_inp1 {
      flex: 1;
    }
  }
  .record_table {
    height: calc(100% - 50px);
    flex: 1;
    flex-shrink: 0;
    border-radius: 4px;
    border: 1px solid #dbe1e4;
    overflow: auto;
    .tableDiv {
      height: calc(100% - 32px);
    }
    .tableDiv1 {
      height: 100%;
    }
    .all_price {
      height: 32px;
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      line-height: 32px;
      background: rgba(214, 48, 49, 0.1);
      border-radius: 4;
      overflow: hidden;
      color: #d63031;
      font-weight: 600;
      padding: 0 15px;
    }
  }
  .rightCont2 {
    flex: 1;
    height: 100%;
    flex-shrink: 0;
    display: flex;
    margin-left: 20px;

    .symbol-box {
      margin-right: 20px;
      margin-top: 90px;
      margin-bottom: 30px;
      text-align: center;
      display: flex;
      flex-direction: column;
      // justify-content: space-around;
    }
    .check {
      width: 172px;
      height: 48px;
      line-height: 48px;
      background: #1770df;
      color: #fff;
      border-radius: 2px;
      margin-bottom: 28px;
      font-size: 24px;
      font-weight: normal;
    }
    .symbol {
      width: 174px;
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 48px;
      div {
        width: 86px;
        height: 48px;
        line-height: 48px;
        background: #1770df;
        color: #fff;
        border-radius: 2px;
        margin-right: 1px;
        margin-bottom: 1px;
        font-size: 28px;
        font-weight: normal;
      }
    }
    .clear {
      width: 172px;
      height: 48px;
      line-height: 48px;
      background: #fff;
      color: #1770df;
      border: 1px solid #1770df;
      border-radius: 2px;
      font-size: 24px;
      font-weight: normal;
    }
    .check:hover,
    .symbol div:hover,
    .clear:hover {
      background: #66b1ff;
      border-color: #66b1ff;
      color: #fff;
    }
    .right-input {
      flex: 1;
      header {
        height: 32px;
        line-height: 32px;
        margin-bottom: 18px;
        flex: 1;
        display: flex;
        justify-content: space-between;
        .lable {
          font-size: 16px;
          // margin-right: 10px;
        }
      }
    }
    .textarea-box {
      height: calc(100% - 50px);
      /deep/.el-textarea,
      /deep/.el-textarea__inner {
        height: 100%;
      }
    }
  }
  .mutex {
    width: 50%;
    margin: 0 auto;
    margin-top: 20px;
  }
}
</style>
