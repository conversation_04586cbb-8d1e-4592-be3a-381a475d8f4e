<template>
  <div class="hisDetailsDrawer">
    <div class="bodyDiv">
      <div class="leftBody">
        <ul class="record_head">
          <li>
            <div class="every_inp1">
              <label for="">收费来源</label>
              <el-select
                @change="searchData"
                v-model.trim="type"
                placeholder="请选择"
                size="small"
              >
                <el-option
                  v-for="item in typeList"
                  :key="item.type"
                  :label="item.typeName"
                  :value="item.type"
                >
                </el-option>
              </el-select>
              <el-input
                v-model.trim="leftIpt"
                @input="searchData(leftIpt)"
                @clear="searchData"
                size="small"
                placeholder="代码/名称"
                clearable
              ></el-input>
              <el-button
                size="small"
                class="blue_btn btn"
                icon="iconfont icon-search"
                @click="searchData(leftIpt)"
                >搜索</el-button
              >
            </div>
          </li>
        </ul>
        <div class="record_table2">
          <PublicTable
            :viewTableList="leftData"
            :theads="tableHead"
            :isCheck="true"
            :isSortShow="false"
            @selectionChange="selectionChange"
            ref="leftTable"
            :tableLoading="tableLoading"
            :columnWidth="{
              chargeItemCode: 120,
              basePrice: 90,
              minPrice: 110,
              minPackSepc: 120,
              minPkgUnit: 130,
              bigPrice: 120,
              bigPkgSpec: 120,
              bigPkgUnit: 120,
              chargeTypeName: 120
            }"
          >
          </PublicTable>
        </div>
      </div>

      <div class="rightBody">
        <ul class="record_head">
          <li>
            <div class="every_inp2">
              <div class="div1">
                <el-input
                  v-model.trim="rightIpt"
                  @input="getRightData(rightIpt)"
                  @clear="getRightData"
                  placeholder="代码/名称"
                  size="small"
                  clearable
                >
                </el-input>
                <el-button
                  size="small"
                  class="blue_btn btn"
                  icon="iconfont icon-search"
                  @click="getRightData(rightIpt)"
                  >搜索</el-button
                >
                <el-button
                  size="small"
                  class="blue_btn btn"
                  icon="iconfont icon-xuanze"
                  @click="addCombHis"
                  >添加
                </el-button>
                <el-button
                  size="small"
                  class="red_btn btn"
                  @click="delCombHis"
                  icon="iconfont icon-shanchu"
                  >删除</el-button
                >
              </div>

              <div class="siv2">
                <el-button
                  size="small"
                  class="blue_btn btn"
                  icon="iconfont icon-huifuxitongmoren"
                  @click="unedit()"
                  >取消编辑</el-button
                >
                <el-button
                  size="small"
                  class="blue_btn btn"
                  icon="iconfont icon-bianji"
                  @click="editRow()"
                  >编辑</el-button
                >
                <el-button
                  size="small"
                  class="blue_btn btn"
                  icon="iconfont icon-baocun"
                  @click="saveEdit"
                  >保存</el-button
                >
              </div>
              <!-- <el-button
                size="small"
                class="blue_btn btn"
                icon="iconfont icon-huifuxitongmoren"
                @click="synchronousUpdate"
                >同步</el-button
              > -->
            </div>
          </li>
        </ul>
        <div class="record_table">
          <div class="tableDiv">
            <PublicTable
              :viewTableList="rightData"
              :theads="rightHead"
              isCheck
              :isSortShow="false"
              :highlightCurrentRow="false"
              @selectionChange="rightSelectionChange"
              :columnWidth="columnWidth"
              ref="rightTable"
              :tableRowClassName="rowStyle"
            >
              <template #hisChargeItemPriceHeader>
                <span>价格</span><span class="span2">（参考）</span>
              </template>
              <template #hisChargeItemCountHeader>
                <span>数量</span><span class="span2">（参考）</span>
              </template>
              <template #hisChargeItemDiscountHeader>
                <span>折扣</span><span class="span2">（参考）</span>
              </template>
              <template #hisChargeItemPrice="{ scope }">
                <span v-if="isEdit" class="flexSpan">
                  <el-input
                    v-model.trim="scope.row.hisChargeItemPrice"
                    size="mini"
                    clearable
                    maxlength="8"
                    :class="
                      scope.row.hisChargeItemPrice > 0 ? 'flexIpt' : 'redSpan'
                    "
                    oninput="value=value.replace(/[^0-9.]/g,'')"
                    @input="calculatePrice(scope.row)"
                  >
                  </el-input
                  ><span class="span2">{{
                    scope.row.basePrice ? '（' + scope.row.basePrice + '）' : ''
                  }}</span></span
                >
                <span v-else>
                  <span
                    :class="
                      scope.row.hisChargeItemPrice > 0 ? 'span1' : 'redSpan'
                    "
                    >{{ scope.row.hisChargeItemPrice }}</span
                  >
                  <span class="span2">{{
                    scope.row.basePrice ? '（' + scope.row.basePrice + '）' : ''
                  }}</span></span
                >
              </template>
              <template #hisChargeItemCount="{ scope }">
                <span v-if="isEdit" class="flexSpan">
                  <el-input
                    v-model.trim="scope.row.hisChargeItemCount"
                    size="mini"
                    clearable
                    maxlength="6"
                    onkeyup="value=value.replace(/[^0-9.]/g,'')"
                    :class="
                      scope.row.hisChargeItemCount > 0 ? 'flexIpt' : 'redSpan'
                    "
                    @input="calculatePrice(scope.row)"
                  >
                  </el-input
                  ><span class="span2">{{
                    scope.row.quantity ? '（' + scope.row.quantity + '）' : ''
                  }}</span></span
                >
                <span v-else>
                  <span
                    :class="
                      scope.row.hisChargeItemCount > 0 ? 'span1' : 'redSpan'
                    "
                    >{{ scope.row.hisChargeItemCount }}</span
                  >
                  <span class="span2">{{
                    scope.row.quantity ? '（' + scope.row.quantity + '）' : ''
                  }}</span>
                </span>
              </template>
              <template #hisChargeItemDiscount="{ scope }">
                <span v-if="isEdit" class="flexSpan">
                  <el-input-number
                    v-model.trim="scope.row.hisChargeItemDiscount"
                    size="mini"
                    clearable
                    :min="0"
                    :max="1"
                    :step="0.01"
                    :precision="2"
                    :class="
                      scope.row.hisChargeItemDiscount > 0
                        ? 'flexIpt'
                        : 'redSpan'
                    "
                    @input="calculatePrice(scope.row)"
                  ></el-input-number
                  ><span class="span2">{{
                    scope.row.priceRatio
                      ? '（' + scope.row.priceRatio + '）'
                      : ''
                  }}</span>
                </span>

                <span v-else>
                  <span
                    :class="
                      scope.row.hisChargeItemDiscount > 0 ? 'span1' : 'redSpan'
                    "
                    >{{ scope.row.hisChargeItemDiscount }}</span
                  >
                  <span class="span2">{{
                    scope.row.priceRatio
                      ? '（' + scope.row.priceRatio + '）'
                      : ''
                  }}</span>
                </span>
              </template>
              <template #hisChargeItemTotalPrice="{ scope }">
                <span
                  :class="
                    scope.row.hisChargeItemTotalPrice > 0 ? 'span1' : 'redSpan'
                  "
                >
                  {{ scope.row.hisChargeItemTotalPrice }}</span
                >
              </template>

              <template #remark="{ scope }">
                <span v-if="isEdit">
                  <el-input
                    v-model.trim="scope.row.remark"
                    size="mini"
                    clearable
                  >
                  </el-input
                ></span>
                <span v-else :title="scope.row.remark">
                  {{ scope.row.remark }}</span
                >
              </template>

              <template #isOrder="{ scope }">
                <div style="width: 100%; text-align: center">
                  <el-switch
                    v-model="scope.row.isOrder"
                    :disabled="!isEdit"
                    active-color="#079C66"
                  >
                  </el-switch>
                </div>
              </template>
            </PublicTable>
          </div>
        </div>
        <div class="all_price">
          <p>修改前总价:{{ price }}</p>
          <p>修改后总价:{{ totalPrice }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import PublicTable from '../../../components/publicTable.vue';
import { mapGetters } from 'vuex';
import { dataUtils } from '../../../common';
export default {
  name: 'hisDetailsDrawer',
  components: {
    PublicTable
  },
  props: {
    righthisdrawerTitle: {
      type: String,
      default: () => {
        return '';
      }
    },
    rowInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      title: '',
      price: this.rowInfo.price,
      tableHead: {
        chargeItemCode: '收费代码',
        chageItemCNName: '收费项目',
        chargeTypeName: '收费类型',
        basePrice: '收费单价',
        itemUnit: '收费单位'
      },
      leftHead: {
        chargeItemCode: '收费代码',
        chageItemCNName: '收费项目',
        chargeTypeName: '收费类型',
        basePrice: '收费单价',
        itemUnit: '收费单位'
      },
      leftHead2: {
        drugCode: '药品代码',
        drugCNName: '药品名称',
        minPrice: '小包装单价',
        minPackSepc: '小包装规格',
        minDosage: '含量',
        minDosageUnit: '含量单位',
        minPkgUnit: '最小包装单位',
        bigPrice: '大包装单价',
        bigPkgSpec: '大包装规格',
        bigPkgUnit: '大包装单位'
      },
      leftData: [],
      rightHead: {
        itemTypeName: '收费来源',
        hisChargeItemCode: '收费代码',
        hisChargeItemCNName: '收费项目',
        chargeTypeName: '收费类型',
        hisChargeItemPrice: '价格',
        hisChargeItemCount: '数量',
        hisChargeItemDiscount: '折扣',
        hisChargeItemTotalPrice: '总价',
        isOrder: '是否医嘱项目',
        remark: '备注'
      },
      rightData: [],
      columnWidth: {
        hisChargeItemCode: 120,
        hisChargeItemCNName: 180,
        chargeTypeName: 120,
        hisChargeItemPrice: 190,
        hisChargeItemCount: 190,
        hisChargeItemDiscount: 200,
        hisChargeItemTotalPrice: 140,
        remark: 250,
        isOrder: 120
      },
      leftSelection: [],
      rightSelection: [],
      leftIpt: '',
      rightIpt: '',
      addList: [],
      delList: [],
      isEdit: false,
      tableLoading: false,
      totalPrice: 0,
      typeList: [],
      type: 0
    };
  },
  computed: {
    ...mapGetters(['G_EnumList'])
  },
  created() {
    this.GetCodeHisItemTypes();
    this.searchData();
    this.getRightData();
  },
  directives: {
    focus: {
      inserted: function (el) {
        el.querySelector('input').focus();
      }
    }
  },
  methods: {
    GetCodeHisItemTypes() {
      this.$ajax.post(this.$apiUrls.GetCodeHisItemTypes).then((r) => {
        //
        let { returnData, success } = r.data;
        if (!success) {
          return;
        }
        this.typeList = returnData || [];
      });
    },
    searchData() {
      if (this.type === 0) {
        this.tableHead = this.leftHead;
        this.getLeftList(this.leftIpt);
      } else {
        //console.log("[ this.leftIpt ]-378", this.leftIpt);
        this.tableHead = this.leftHead2;
        this.GetCodeHisDrugItems(this.leftIpt);
      }
    },
    // 左边表格多选
    selectionChange(val) {
      //console.log("val: ", val);
      this.leftSelection = val || [];
      this.addList = [];
      this.leftSelection.forEach((item) => {
        this.addList.push({
          combCode: this.rowInfo.combCode,
          combName: this.rowInfo.combName,
          hisChargeItemId: this.type === 0 ? item.chargeItemId : item.drugId,
          hisChargeItemCode:
            this.type === 0 ? item.chargeItemCode : item.drugCode,
          hisChargeItemCNName:
            this.type === 0 ? item.chageItemCNName : item.drugCNName,
          hisChargeItemPrice: this.type === 0 ? item.basePrice : item.minPrice,
          hisChargeItemCount: 1, //minDosage
          hisChargeItemDiscount: 1,
          remark: '',
          itemType: this.type,
          isOrder: true
        });
      });

      // console.log("[ this.addList ]-412", this.addList);
    },
    // 右边表格多选
    rightSelectionChange(val) {
      //console.log("[ val ]-250", val);
      this.rightSelection = val || [];
      this.delList = val || [];
    },

    // 获取左边数据
    getLeftList(keyword) {
      let data = {
        pageSize: 0, //每页数量 (0：查全部，>0：分页查)
        pageNumber: 0,
        chargeItemCode: '',
        chageItemCNName: '',
        keyword: keyword ? keyword : ''
      };
      this.tableLoading = true;
      this.leftData = [];
      this.$ajax.post(this.$apiUrls.GetCodeHisChargeItems, data).then((r) => {
        //
        let { returnData, success } = r.data;
        if (!success) {
          this.tableLoading = false;
          return;
        }

        this.leftData = returnData || [];
        this.tableLoading = false;
      });
    },
    GetCodeHisDrugItems(keyword) {
      let data = {
        pageSize: 0, //每页数量 (0：查全部，>0：分页查)
        pageNumber: 0,
        chargeItemCode: '',
        chageItemCNName: '',
        keyword: keyword ? keyword : ''
      };

      // console.log("[data  ]-443", data);
      this.tableLoading = true;
      this.leftData = [];
      this.$ajax.post(this.$apiUrls.GetCodeHisDrugItems, data).then((r) => {
        //
        let { returnData, success } = r.data;
        if (!success) {
          this.tableLoading = false;
          return;
        }
        this.leftData = returnData || [];
        //console.log("[ this.leftData ]-456", this.leftData);
        this.tableLoading = false;
      });
    },
    //获取右边表格数据
    getRightData(keyword) {
      let data = {
        pageSize: 0, //每页数量 (0：查全部，>0：分页查)
        pageNumber: 0,
        combCode: this.rowInfo.combCode,
        combName: this.rowInfo.combName,
        hisChargeItemCode: '',
        hisChargeItemCNName: '',
        keyword: keyword ? keyword : ''
      };
      this.$ajax
        .post(this.$apiUrls.GetMapCodeItemCombHisChargeItems, data)
        .then((r) => {
          //
          let { returnData, success } = r.data;
          if (!success) return;
          this.rightData = [];
          //处理数据
          returnData.map((item) => {
            this.rightData.push({
              combCode: this.rowInfo.combCode,
              combName: this.rowInfo.combName,
              hisChargeItemId: item.hisChargeItemId,
              hisChargeItemCode: item.hisChargeItemCode,
              hisChargeItemCNName: item.hisChargeItemCNName,
              hisChargeItemPrice: item.hisChargeItemPrice,
              hisChargeItemCount: item.hisChargeItemCount,
              hisChargeItemDiscount: item.hisChargeItemDiscount,
              hisChargeItemTotalPrice: item.hisChargeItemTotalPrice,
              remark: item.remark,
              itemType: item.itemType,
              itemTypeName: item.itemTypeName,
              chargeTypeName:
                item.codeHisChargeItem.chargeTypeName ??
                item.codeHisDrugItem.drugTypeName,
              basePrice: item.codeHisOrderItemCharge?.basePrice,
              priceRatio: item.codeHisOrderItemCharge?.priceRatio,
              quantity: item.codeHisOrderItemCharge?.quantity,
              isOrder: item.isOrder
            });
          });

          //console.log("[this.rightData  ]-498", this.rightData);
          this.totalSumAll(this.rightData);
          //同步价格后把价格赋值给当前点击行单价
          const item = this.$parent.$parent.tableData.find(
            (item) => item.combCode === this.rowInfo.combCode
          );
          if (item) {
            item.price = this.totalPrice;
          }
        });
    },
    //添加
    addCombHis() {
      if (this.leftSelection.length === 0) {
        this.$message({
          message: '没有选择添加的数据!!!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      //过滤右边已有项目
      const data = this.addList.filter(
        (item) =>
          !this.rightData.some(
            (ele) => ele.hisChargeItemCode === item.hisChargeItemCode
          )
      );
      //console.log("[data  ]-321", data);
      if (data.length === 0) {
        this.$message({
          message: '所选的数据已经都添加过了,请重新选择',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$ajax
        .post(this.$apiUrls.SaveMapCodeItemCombHisChargeItems, data)
        .then((r) => {
          //
          let { returnData, returnMsg, success } = r.data;
          if (!success) return;
          this.$message({
            message: returnMsg,
            type: 'success',
            showClose: true
          });
          this.$refs.leftTable.$refs.tableCom_Ref.clearSelection();
          this.$refs.rightTable.$refs.tableCom_Ref.clearSelection();
          this.getRightData();
        });
    },
    //删除
    delCombHis() {
      if (this.rightSelection.length === 0) {
        this.$message({
          message: '没有选择删除的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$ajax
        .post(this.$apiUrls.RemoveCodeHisChargeItems, this.delList)
        .then((r) => {
          //
          let { returnData, returnMsg, success } = r.data;
          if (!success) return;
          this.$refs.leftTable.$refs.tableCom_Ref.clearSelection();
          this.$refs.rightTable.$refs.tableCom_Ref.clearSelection();
          this.getRightData();
          this.$message({
            message: returnMsg,
            type: 'success',
            showClose: true
          });
        });
    },
    //取消编辑
    unedit() {
      this.getRightData();
      this.isEdit = false;
    },
    editRow() {
      this.isEdit = true;
    },
    //保存编辑
    saveEdit() {
      if (this.rightData.length == 0) {
        this.$message({
          message: '没有可保存数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      //console.log("[ this.rightData ]-592", this.rightData);
      const rightData = dataUtils.deepCopy(this.rightData);
      rightData.map((item) => {
        (item.itemType = item.itemType),
          (item.hisChargeItemPrice = Number(item.hisChargeItemPrice) || 0);
        item.hisChargeItemCount = Number(item.hisChargeItemCount) || 0;
        item.hisChargeItemDiscount = Number(item.hisChargeItemDiscount) || 0;
        item.hisChargeItemTotalPrice = (
          item.hisChargeItemPrice *
          item.hisChargeItemCount *
          item.hisChargeItemDiscount
        ).toFixed(2);
      });
      //console.log("[rightData  ]-469", rightData);
      this.$ajax
        .post(this.$apiUrls.SaveMapCodeItemCombHisChargeItems, rightData)
        .then((r) => {
          this.isEdit = false;
          //
          this.totalSumAll(rightData);
          let { returnData, returnMsg, success } = r.data;
          if (!success) return;
          this.$message({
            message: returnMsg,
            type: 'success',
            showClose: true
          });
          //同步价格后把价格赋值给当前点击行单价
          const item = this.$parent.$parent.tableData.find(
            (item) => item.combCode === this.rowInfo.combCode
          );
          if (item) {
            item.price = this.totalPrice;
          }
          this.$refs.leftTable.$refs.tableCom_Ref.clearSelection();
          this.$refs.rightTable.$refs.tableCom_Ref.clearSelection();
        });
    },
    calculatePrice(row) {
      row.hisChargeItemTotalPrice = (
        row.hisChargeItemPrice *
        row.hisChargeItemCount *
        row.hisChargeItemDiscount
      ).toFixed(2);

      row.isTotalZero = row.hisChargeItemTotalPrice <= 0;
      console.log('[ row.isTotalZero ]-648', row.isTotalZero);
      this.totalSumAll(this.rightData);
    },
    // 计算总价
    totalSumAll(rightData) {
      let totalSumAll = 0;
      rightData.map((item) => {
        if (!isNaN(item.hisChargeItemTotalPrice))
          totalSumAll += Number(item.hisChargeItemTotalPrice) * 100;
      });
      if (isNaN(totalSumAll)) {
        return 0;
      }
      this.totalPrice = (totalSumAll / 100).toFixed(2);
      //console.log("[ this.totalPrice ]-665", this.totalPrice);
    },
    //同步更新体检组合价格
    synchronousUpdate() {
      if (this.isEdit) {
        this.$message({
          message: '编辑状态不能同步更新体检组合价格',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.rightData.length > 0) {
        this.$confirm('是否确认同步更新体检组合价格?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.SyncCodeItemCombPrice();
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    SyncCodeItemCombPrice() {
      this.$ajax
        .post(this.$apiUrls.SyncCodeItemCombPrice, [this.rowInfo.combCode])
        .then((r) => {
          //
          let { returnMsg, success } = r.data;
          if (!success) return;
          //同步价格后把价格赋值给当前点击行单价
          const item = this.$parent.$parent.tableData.find(
            (item) => item.combCode === this.rowInfo.combCode
          );
          if (item) {
            item.price = this.totalPrice;
          }
          this.$message({
            message: returnMsg,
            type: 'success',
            showClose: true
          });
        });
    },
    rowStyle(row) {
      console.log('[ row ]-711', row);

      if (row.isTotalZero) {
        return 'tr_red';
      } else {
        return '';
      }
    }
  },
  mounted() {}
};
</script>
<style lang="less" scoped>
.hisDetailsDrawer {
  height: 100%;
  width: 100%;
  overflow: auto;
  border-radius: 4px;
  padding: 18px;
  display: flex;
  flex-direction: column;
  .btn {
    padding: 6px 9px;
  }
  .bodyDiv {
    flex: 1;
    flex-shrink: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .leftBody {
    background-color: #fff;
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: auto;
  }
  .rightBody {
    background-color: #fff;
    margin-top: 20px;
    overflow: auto;
    display: flex;
    flex-direction: column;
    overflow: auto;
    flex: 1;
    .combo_group {
      flex: 1;
    }
    /deep/.el-input--mini .el-input__inner {
      padding: 0 5px;
    }
  }
  .flexSpan {
    display: flex;
    flex-direction: row;
  }
  .flexIpt {
    width: 90px;
  }
  .span1 {
    font-size: 16px;
  }
  .redSpan {
    color: red;
    font-size: 16px;
    width: 90px;
    /deep/.el-input__inner {
      color: red;
      width: 90px;
    }
  }
  .span2 {
    color: #999898;
    flex: 1;
  }

  .record_head {
    li {
      display: flex;
      margin-bottom: 18px;
    }

    .every_inp,
    .every_inp1,
    .every_inp2 {
      display: flex;
      align-items: center;
      margin-right: 10px;

      label {
        margin-right: 10px;
        width: auto;
        text-align: right;
        overflow: hidden; /*溢出的部分隐藏*/
        white-space: nowrap; /*文本不换行*/
        text-overflow: ellipsis;
      }

      p {
        flex: 1;
      }

      &:nth-child(4) {
        flex: 1;
      }
    }
    .every_inp {
      width: 350px;
    }
    .every_inp1 {
      flex: 1;
      width: 100%;
      display: flex;
      .el-input {
        width: 150px;
        margin: 0 20px;
      }
    }
    .every_inp2 {
      flex: 1;
      width: 100%;
      display: flex;
      justify-content: space-between;
      .el-input {
        width: 150px;
        margin-right: 20px;
      }
    }
  }
  .inputP {
    display: flex;
    label {
      width: 142px !important;
      line-height: 32px;
    }
    .el-select {
      margin-right: 10px;
    }
  }
  .record_table {
    flex: 1;
    flex-shrink: 0;
    border-radius: 4px;
    border: 1px solid #dbe1e4;
    overflow: auto;
    display: flex;
    .tableDiv1 {
      height: 100%;
    }
  }
  .record_table2 {
    flex: 1;
    overflow: auto;
  }
  .all_price {
    height: 32px;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    line-height: 32px;
    background: rgba(214, 48, 49, 0.1);
    border-radius: 4;
    overflow: hidden;
    color: #d63031;
    font-weight: 600;
    padding: 0 15px;
  }
  /deep/.el-input-number__decrease,
  .el-input-number__increase {
    width: 30px;
  }
  /deep/.el-input-number {
    width: 130px;
  }
}
</style>
