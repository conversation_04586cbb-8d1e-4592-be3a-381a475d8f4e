<template>
  <el-dialog
    title="复制套餐"
    :visible.sync="dialogVisible"
    width="700px"
    :close-on-click-modal="false"
    :show-close="false"
    append-to-body
    :close-on-press-escape="false"
    @close="closeDialog"
    :before-close="
      () => {
        $emit('update:dialogVisible', false);
      }
    "
  >
    <div>
      <div>
        <el-select
          size="small"
          @change="mealTypeChange"
          v-model="mealTypeVal"
          placeholder="请选择"
          style="width: 90px"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <el-cascader
          style="margin: 0 10px"
          v-model="checkCompanyVal"
          size="small"
          placeholder="请选择单位和次数"
          :options="companyWithTimesList"
          :filter-method="filterMethod"
          @change="companyChange"
          clearable
          filterable
          :disabled="mealTypeVal == 'personal'"
        >
        </el-cascader>
        <el-select
          class="select-input"
          v-model.trim="checkPackage"
          placeholder="选择套餐"
          size="small"
          filterable
          clearable
          @change="changeSelect"
        >
          <el-option
            v-for="(item, index) in packageOption"
            :key="index"
            :label="item.clusName"
            :value="item.clusCode"
          ></el-option>
        </el-select>
        <!-- <el-button
                  size="small"
                  class="green_btn btn"
                  icon="iconfont icon-qingkonghuancun"
                  @click="clearClick"
                  >清空</el-button
                >
                <el-button
                  size="small"
                  class="blue_btn btn"
                  icon="iconfont icon-baocun"
                  @click="save('ruleForm')"
                  >保存</el-button
                > -->
      </div>
      <div class="table-box">
        <PublicTable
          ref="table_ref"
          style="height: 100%"
          :theads.sync="thead"
          :viewTableList.sync="tableData"
          :cell_red="['price', 'originalPrice']"
          :tableLoading.sync="tableLoading"
          :isSortShow="false"
          :columnWidth="columnWidth"
          @rowDblclick="rowClick"
        >
          <template #sex="{ scope }">
            {{ G_EnumList['Sex'][scope.row.sex] }}
          </template>
        </PublicTable>
      </div>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button size="small" @click="$emit('update:dialogVisible', false)"
        >取 消</el-button
      >
      <el-button size="small" type="primary" @click="determine"
        >复 制</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import PublicTable from '@/components/publicTable';
import { mapGetters } from 'vuex';
export default {
  name: 'copyPackage',
  components: { PublicTable },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
      required: true
    }
  },
  data() {
    return {
      options: [
        {
          value: 'personal',
          label: '个人'
        },
        {
          value: 'group',
          label: '团体'
        }
      ],
      thead: {
        combCode: '组合代码',
        combName: '组合名称',
        sex: '性别',
        originalPrice: '单价'
      },
      tableData: [],
      tableLoading: false,
      columnWidth: {
        // combName: 200,
        combCode: 150,
        sex: 70,
        originalPrice: 100
      },
      mealTypeVal: 'personal',
      checkPackage: '',
      checkCompanyVal: [],
      companyWithTimesList: [],
      packageOption: [],
      fixed_packageOption: []
    };
  },
  watch: {},
  computed: { ...mapGetters(['G_EnumList']) },
  methods: {
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    determine() {
      if (this.tableData.length == 0) {
        return this.$message.warning('请选择套餐');
      }
      console.log(this.tableData);
      this.$emit('determine', this.tableData);
      this.$emit('update:dialogVisible', false);
    },
    closeDialog() {
      this.checkPackage = '';
      this.mealTypeVal = 'personal';
      this.checkCompanyVal = [];
      this.packageOption = this.fixed_packageOption;
      this.tableData = [];
    },
    companyChange() {
      this.companyTimesChange();
    },
    mealTypeChange(val) {
      this.checkPackage = '';
      if (val == 'personal') {
        this.checkCompanyVal = [];
        this.packageOption = this.fixed_packageOption;
      } else {
        this.packageOption = [];
      }
    },
    changeSelect() {
      this.getCopyCompanyClusterCombs();
    },
    rowClick(row) {},
    // 获取单位信息和单位次数
    GetCompaniesWithTimes() {
      let datas = {
        pageSize: 0,
        pageNumber: 1
        // companyClsCode,
        // companyCode,
        // parent,
        // keyword
      };
      this.$ajax.post(this.$apiUrls.GetCompaniesWithTimes, datas).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyWithTimesList = returnData?.map((item) => {
          let childArr = item.codeCompanyTimes.map((twoItem) => {
            return {
              value: `${twoItem.companyTimes}`,
              label: `体检次数：${twoItem.companyTimes}`
            };
          });
          return {
            value: `${item.companyCode}`,
            label: `${item.companyName}`,
            children: childArr
          };
        });
        console.log(this.companyWithTimesList);
      });
    },
    companyTimesChange() {
      this.checkPackage = '';
      if (!this.checkCompanyVal[1]) {
        this.packageOption = this.fixed_checkPackage;
        return;
      }
      this.$ajax
        .post(this.$apiUrls.ReadCompanyCandidateCluster, '', {
          query: {
            companyCode: this.checkCompanyVal[0] || '',
            companyTimes: this.checkCompanyVal[1] || ''
          }
        })
        .then(async (r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.packageOption = returnData || [];
        });
    },
    // 获取套餐组合列表
    getCopyCompanyClusterCombs() {
      this.$ajax
        .post(this.$apiUrls.CopyCompanyClusterCombs, '', {
          query: {
            isCompany: this.mealTypeVal == 'group' ? true : false,
            clusterCode: this.checkPackage
          }
        })
        .then((r) => {
          console.log('CopyCompanyClusterCombs: ', r);
          this.tableLoading = false;
          let { success, returnData } = r.data;
          if (!success) return;
          this.tableData = returnData;
          // this.CalculateTustTubeCombs(returnData);
        });
    },
    getClusterList() {
      this.$ajax.post(this.$apiUrls.Cluster).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.packageOption = returnData;
        this.fixed_packageOption = returnData;
      });
    }
  },
  created() {},
  mounted() {
    this.GetCompaniesWithTimes();
    this.getClusterList();
  }
};
</script>
<style lang="less" scoped>
.table-box {
  width: 100%;
  height: 350px;
  margin-top: 10px;
}

/deep/ .el-dialog__body {
  padding: 10px 20px;
}
</style>
