<template>
  <div class="defineDisease_page">
    <header>
      <span>自定义疾病管理</span>
      <div style="display: flex; align-items: center">
        <!-- 前端过滤 -->
        <el-input
          style="margin-right: 10px"
          size="small"
          clearable
          v-model="keyword"
          placeholder="输入疾病名搜索"
        ></el-input>
        <span style="margin-right: 10px; white-space: nowrap">登记时间</span>
        <el-date-picker
          :picker-options="{ shortcuts: G_datePickerShortcuts }"
          size="small"
          v-model="dateVal"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          :clearable="false"
          @change="dateChange"
        >
        </el-date-picker>
        <el-button size="small" class="blue_btn btn" @click="toOfficialBtn"
          >转入疾病库</el-button
        >
      </div>
    </header>
    <div class="content_wrap">
      <PublicTable
        ref="record_Ref"
        :theads="theads"
        :url="$apiUrls.GetCustomDiseaseSummaries"
        :params="params"
        :elStyle="{
          border: true
        }"
        rowKey="diseaseName"
        remoteByPage
        :tableDataFilter="tableDataFilter"
        @selectionChange="selectionChange"
        reserveSelection
      >
      </PublicTable>
    </div>
    <!-- 转正的抽屉 -->
    <el-drawer
      title="转入疾病库"
      :visible.sync="drawerShow"
      size="90%"
      direction="rtl"
      destroy-on-close
      :wrapperClosable="false"
    >
      <div class="drawer_wrap">
        <div class="drawer_left">
          <div class="disease_wrap">
            <header>疾病名称</header>
            <div class="list_wrap">
              <el-checkbox
                v-model="item.checked"
                :label="item.diseaseName"
                @change="checkBoxChange(item)"
                class="disease_btn"
                size="small"
                border
                v-for="(item, idx) in diseaseList"
                :key="idx"
              >
                {{ item.diseaseName }}
                <i
                  class="el-icon-success create_btn"
                  title="生成疾病名称"
                  @click.prevent="createDiseaseName(item)"
                ></i>
              </el-checkbox>
            </div>
          </div>

          <div class="suggest_wrap">
            <header>疾病建议</header>
            <div class="list_wrap">
              <el-collapse
                v-model="suggestActive"
                v-if="suggestList.length !== 0"
              >
                <template v-for="(item, idx) in suggestList">
                  <el-collapse-item
                    :name="idx"
                    :key="item.diseaseName"
                    v-if="item.suggestions.length !== 0"
                  >
                    <template #title>
                      <div class="suggest_title">
                        <p>{{ item.diseaseName }}</p>
                        <!-- <span>引用</span> -->
                      </div>
                    </template>
                    <div
                      class="every_suggest"
                      v-for="(twoItem, twoIdx) in item.suggestions"
                      :key="twoIdx"
                    >
                      <i>{{ twoIdx + 1 }}、</i>
                      <p>{{ twoItem }}</p>
                      <span @click="quoteBtn(twoItem)">引用</span>
                    </div>
                  </el-collapse-item>
                </template>
              </el-collapse>
              <el-empty v-else description="暂无建议"></el-empty>
            </div>
          </div>
        </div>
        <div class="drawer_right">
          <div class="info-content">
            <el-form
              ref="form"
              :model="form"
              label-width="80px"
              class="form"
              :rules="rules"
            >
              <el-row :gutter="10">
                <!-- <el-col :span="12">
                  <el-form-item label="疾病代码">
                    <el-input
                      v-model="form.diseaseCode"
                      size="small"
                      placeholder="请输入疾病代码"
                      clearable
                      disabled
                    ></el-input>
                  </el-form-item>
                </el-col> -->
                <el-col :span="12">
                  <el-form-item label="项目分类" prop="clsCode">
                    <el-select
                      size="small"
                      v-model="form.clsCode"
                      placeholder="请选择项目分类"
                      class="select"
                      clearable
                      filterable
                      :disabled="isEdit"
                    >
                      <el-option
                        :label="item.label"
                        :value="item.value"
                        v-for="item in G_codeItemCls"
                        :key="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="疾病名称" prop="diseaseName">
                <el-input
                  v-model="form.diseaseName"
                  size="small"
                  placeholder="请输入疾病名称"
                  readonly
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item label="疾病词条" prop="codeDiseaseEntry">
                <div class="disease-entry">
                  <div
                    v-for="(tag, index) in form.codeDiseaseEntry"
                    :key="index"
                    class="entry-box"
                  >
                    <el-input
                      v-if="editDiseaseEntryList[index]"
                      :ref="formInpTagName + index"
                      v-model="tag.entryText"
                      class="input-edit-tag"
                      size="small"
                      placeholder="请输入词条"
                      maxlength="100"
                      @keyup.enter.native="
                        editEntryInputConfirm(
                          form.codeDiseaseEntry,
                          tag,
                          index,
                          formInpTagName
                        )
                      "
                      @focus="
                        editEntryInputFoucs(
                          form.codeDiseaseEntry,
                          tag,
                          index,
                          formInpTagName
                        )
                      "
                      @blur="
                        editEntryInputBlur(
                          form.codeDiseaseEntry,
                          tag,
                          index,
                          formInpTagName
                        )
                      "
                    />
                    <el-tag
                      v-else
                      :disable-transitions="true"
                      :type="tag.err ? 'danger' : ''"
                      :ref="formInpTagName + index + '_readonly'"
                    >
                      {{ tag.entryText }}
                    </el-tag>
                  </div>

                  <el-input
                    class="input-new-tag"
                    v-if="inputVisible"
                    v-model="inputValue"
                    ref="saveTagInput"
                    size="small"
                    placeholder="请输入词条"
                    maxlength="100"
                    @keyup.enter.native="handleInputConfirm"
                    @blur="handleInputConfirm"
                  >
                  </el-input>
                  <!-- <el-button
                    v-else
                    class="button-new-tag"
                    size="small"
                    @click="showInput"
                    icon="iconfont icon-xinjian"
                    >新增</el-button
                  > -->
                </div>
              </el-form-item>
              <el-form-item label="科普知识" prop="presentation">
                <el-input
                  type="textarea"
                  v-model="form.presentation"
                  :autosize="{ minRows: 7, maxRows: 7 }"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
              <el-form-item label="建议内容" prop="suggestContent">
                <el-input
                  type="textarea"
                  v-model="form.suggestContent"
                  :autosize="{ minRows: 7, maxRows: 7 }"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
              <!-- <el-row :gutter="10">
                            <el-col :span="8"> -->
              <el-form-item label="疾病等级" prop="diseaseGrade">
                <el-select
                  size="small"
                  v-model="form.diseaseGrade"
                  placeholder="请选择疾病等级"
                  class="select"
                  clearable
                >
                  <el-option
                    :label="item.label"
                    :value="item.value"
                    v-for="item in G_diseaseGrade"
                    :key="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <!-- </el-col>
                        </el-row> -->
            </el-form>
          </div>
          <!-- 操作按钮 -->
          <div class="drawer_btn">
            <el-button @click="drawerShow = false">取消</el-button>
            <el-button @click="diseaseSave" :loading="loadFlag">保存</el-button>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import PublicTable from '@/components/publicTable2';
import moment from 'moment';
import DefineDisease from './mixins/defineDisease';
import { mapGetters } from 'vuex';
export default {
  name: 'defineDisease',
  mixins: [DefineDisease],
  components: {
    PublicTable
  },
  data() {
    return {
      keyword: '',
      dateVal: [
        moment().startOf('year').format('YYYY-MM-DD'),
        moment().endOf('year').format('YYYY-MM-DD')
      ],
      theads: [
        {
          prop: 'diseaseName',
          label: '疾病名称',
          width: 300
        },
        {
          prop: 'totalNumber',
          label: '使用/统计次数'
        }
      ],
      params: {
        keyword: '',
        startTime: '',
        endTime: ''
      },
      drawerShow: false,
      checkList: [],
      diseaseList: [],
      suggestActive: [],
      suggestList: []
    };
  },
  computed: {
    ...mapGetters(['G_datePickerShortcuts'])
  },
  methods: {
    tableDataFilter(data) {
      if (!data || data?.length == 0) return data;
      const that = this;
      const keyword = (that.keyword || '').trim().toLowerCase();
      return data.filter((item) =>
        item.diseaseName.toLowerCase().includes(keyword)
      );
    },
    // 获取自定义疾病列表
    dateChange() {
      this.params.startTime = this.dateVal[0];
      this.params.endTime = this.dateVal[1];
      this.$refs.record_Ref.loadData();
    },
    // 转正按钮的点击回调
    toOfficialBtn() {
      if (this.checkList.length == 0) {
        this.$message({
          message: '请选择需要转正的疾病！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let diseaseNames = [];
      let codeDiseaseEntry = [];
      this.diseaseList = this.checkList.map((item, idx) => {
        if (!diseaseNames.includes(item.diseaseName.trim())) {
          diseaseNames.push(item.diseaseName);
          if (idx !== 0) {
            codeDiseaseEntry.push({
              diseaseCode: this.form.diseaseCode,
              entryText: item.diseaseName
            });
          }
        }
        this.suggestActive.push(idx);
        return {
          ...item,
          checked: true
        };
      });
      this.form.diseaseName = diseaseNames[0];
      this.form.codeDiseaseEntry = codeDiseaseEntry;
      this.getCustomDiseaseSuggestions(diseaseNames);
      this.drawerShow = true;
    },
    // 选择疾病的回调
    selectionChange(selection, checkList) {
      this.checkList = checkList;
    },
    // 获取疾病建议
    getCustomDiseaseSuggestions(diseaseNames) {
      let datas = {
        startTime: this.dateVal[0],
        endTime: this.dateVal[1],
        diseaseNames
      };
      this.$ajax
        .post(this.$apiUrls.GetCustomDiseaseSuggestions, datas)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.suggestList = returnData || [];
        });
    },
    // 疾病的点击回调
    checkBoxChange(diseaseObj) {
      if (diseaseObj.checked) {
        if (
          this.form.codeDiseaseEntry.length === 0 &&
          this.form.diseaseName === ''
        ) {
          this.form.diseaseName = diseaseObj.diseaseName.trim();
          return;
        }
        this.form.codeDiseaseEntry.unshift({
          diseaseCode: this.form.diseaseCode,
          entryText: diseaseObj.diseaseName
        });
      } else {
        if (
          this.form.codeDiseaseEntry.length === 0 &&
          this.form.diseaseName === diseaseObj.diseaseName.trim()
        ) {
          this.form.diseaseName = '';
          return;
        }
        if (this.form.diseaseName === diseaseObj.diseaseName.trim()) {
          this.form.diseaseName = this.form.codeDiseaseEntry[0]?.entryText;
          this.$nextTick(() => {
            this.form.codeDiseaseEntry.splice(0, 1);
          });

          return;
        }
        let index = undefined;
        this.form.codeDiseaseEntry.some((item, idx) => {
          if (item.entryText == diseaseObj.diseaseName.trim()) {
            index = idx;
            return true;
          }
        });
        if (index === undefined) return;
        this.form.codeDiseaseEntry.splice(index, 1);
      }
    },
    // 生成疾病名称
    createDiseaseName(diseaseObj) {
      console.log(diseaseObj);
      let oldName = this.form.diseaseName;
      this.form.diseaseName = diseaseObj.diseaseName;
      diseaseObj.checked = true;
      this.form.codeDiseaseEntry.some((item, idx, arr) => {
        if (item.entryText === diseaseObj.diseaseName.trim()) {
          arr.splice(idx, 1);
          return true;
        }
      });
      if (oldName === diseaseObj.diseaseName) return;
      this.form.codeDiseaseEntry.unshift({
        diseaseCode: this.form.diseaseCode,
        entryText: oldName
      });
    },
    // 引用建议
    quoteBtn(suggest) {
      this.form.suggestContent +=
        (this.form.suggestContent == '' ? '' : '\r\n') + suggest;
    },
    // 转入疾病库的保存
    diseaseSave() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loadFlag = true;
          if (this.form.codeDiseaseEntry.some((x) => x.err)) {
            this.$message({
              message: '存在相同疾病词条，请修改！',
              type: 'warning',
              showClose: true
            });
            return;
          }
          let type = this.isEdit ? 'Update' : 'Create';
          this.$ajax
            .post(`${this.$apiUrls.CU_CodeDisease}/${type}`, this.form)
            .then((r) => {
              this.loadFlag = false;
              let { success, returnData } = r.data;
              if (!success) return;
              this.drawerShow = false;
              this.dateChange();
              this.form = this.$options.data().form;
              this.$message({
                message: this.isEdit ? '修改成功!' : '新建成功!',
                type: 'success',
                showClose: true
              });
            })
            .catch((e) => {
              this.loadFlag = false;
            });
        }
      });
    }
  },
  mounted() {
    this.dateChange();
  }
};
</script>

<style lang="less" scoped>
.defineDisease_page {
  display: flex;
  flex-direction: column;
  background: #fff;
  header {
    display: flex;
    justify-content: space-between;
    .btn {
      margin-left: 10px;
    }
  }
  .content_wrap {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
  }
  .drawer_wrap {
    height: 100%;
    display: flex;
    padding: 5px;
    .drawer_left {
      width: 400px;
      margin-right: 5px;
      border-radius: 4px;
      overflow: auto;
      display: flex;
      flex-direction: column;
      overflow: auto;
      .suggest_wrap,
      .disease_wrap {
        overflow: auto;
        border: 1px solid #ccc;
        border-radius: 4px;
        padding: 5px;
        display: flex;
        flex-direction: column;
        header {
          margin-bottom: 5px;
        }
        .list_wrap {
          flex: 1;
          flex-shrink: 0;
          overflow: auto;
        }
      }
      .disease_wrap {
        height: 300px;
        margin-bottom: 5px;
        .list_wrap {
          padding: 10px 0;
        }
        label {
          margin: 0 5px 5px 0;
        }
        .disease_btn {
          position: relative;
          .create_btn {
            position: absolute;
            top: -50%;
            right: 0;
            font-size: 20px;
            color: #409eff;
            transform: translate(50%, 50%);
            z-index: 1;
            display: none;
          }
          &:hover .create_btn {
            display: block;
          }
        }
      }
      .suggest_wrap {
        flex: 1;
        flex-shrink: 0;
        .suggest_title {
          display: flex;
          justify-content: space-between;
          flex: 1;
          flex-shrink: 0;
          font-size: 16px;
          padding-right: 10px;
          p {
            font-weight: 600;
          }
          span {
            color: #409eff;
            cursor: pointer;
          }
        }
        .every_suggest {
          display: flex;
          font-size: 14px;
          border-bottom: 1px solid #ddd;
          margin-bottom: 5px;
          i {
            font-style: normal;
            width: 30px;
          }
          p {
            flex: 1;
            flex-shrink: 0;
            padding-right: 8px;
          }
          span {
            font-size: 16px;
            color: #409eff;
            // line-height: 16px;
            cursor: pointer;
          }
        }
      }
    }
    .drawer_right {
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
      border: 1px solid #ccc;
      border-radius: 4px;
      background: rgba(23, 112, 223, 0.2);
      padding: 5px;
      display: flex;
      flex-direction: column;
      .disease-entry {
        display: flex;
        flex-wrap: wrap;
        height: 96px;
        background: #fff;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        overflow: auto;
        align-items: baseline;

        .entry-box {
          display: flex;
          align-items: center;
          height: max-content;
        }

        .el-tag {
          margin-left: 10px;
          margin-top: 10px;
          white-space: pre-line;
          word-wrap: break-word;
          height: auto;
        }

        .button-new-tag {
          margin-left: 10px;
          height: 32px;
          line-height: 30px;
          padding-top: 0;
          padding-bottom: 0;
          font-size: 14px !important;
          margin-top: 10px;

          /deep/.iconfont {
            font-size: 12px;
          }
        }

        .input-new-tag,
        .input-edit-tag {
          width: 120px;
          margin-left: 10px;
        }
      }
      .info-content {
        flex: 1;
        flex-shrink: 0;
        overflow: auto;
        .el-row {
          margin: 0 !important;
        }
      }
      .drawer_btn {
        text-align: right;
      }
    }
  }
  /deep/.el-drawer__header {
    margin-bottom: 5px;
  }
}
</style>
