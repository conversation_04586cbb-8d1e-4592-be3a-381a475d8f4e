<template>
  <!-- 体检分类-主检/审核人员对应 -->
  <div class="tjTypeMain">
    <el-container>
      <el-header>
        <AllBtn
          :btnList="['查询', '打印', '导出']"
          :methodSearch="search"
          :methodPrint="print"
          :methodExport="exportTable"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          ref="table_ref"
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          :cell_blue="cell_blue"
          @selectionChange="handleSelectChangeTable"
          :tableLoading.sync="loading"
          :columnWidth="columnWidth"
          isCheck
        >
          <template #columnRight>
            <el-table-column
              prop="operation"
              label="人员对应"
              width="230"
              fixed="right"
            >
              <template slot-scope="scope">
                <el-button
                  type="primary"
                  plain
                  size="mini"
                  class="view"
                  @click="viewDetails(scope.row)"
                  >主检人员</el-button
                >
                <el-button
                  type="primary"
                  plain
                  size="mini"
                  class="view"
                  @click="auditDetails(scope.row)"
                  >审核人员</el-button
                >
              </template>
            </el-table-column>
          </template>
        </PublicTable>
      </el-main>
    </el-container>
    <el-drawer
      title="对应详情"
      :visible.sync="drawers"
      :close-on-click-modal="false"
      :wrapperClosable="false"
      size="80%"
    >
      <TjTypeMainDrawer
        :cancel="handleClose"
        :drawerInfo="drawerInfo"
        ref="drawer_ref"
        :title="title"
        :checkAudits="checkAudits"
      ></TjTypeMainDrawer>
    </el-drawer>
    <el-drawer
      title="对应详情"
      :visible.sync="auditDrawers"
      :close-on-click-modal="false"
      :wrapperClosable="false"
      size="80%"
    >
      <TjTypeMainDrawer
        :cancel="auditHandleClose"
        :drawerInfo="auditDrawerInfo"
        :checkAudits="checkAudits"
        :title="title"
        ref="audit_ref"
      ></TjTypeMainDrawer>
    </el-drawer>
    <!-- 打印的表格 -->
    <div class="print_table">
      <PublicTable
        id="printHtml"
        :viewTableList.sync="checkTableList"
        :theads.sync="theads"
        :cell_blue="cell_blue"
        :columnWidth="columnWidth"
      >
      </PublicTable>
    </div>
  </div>
</template>

<script>
import { export2Excel } from '@/common/excelUtil';
import moment from 'moment';
import PublicTable from '../../../components/publicTable.vue';
import AllBtn from '../allBtn.vue';
import printJS from '@/common/printJS';
import TjTypeMainDrawer from './tjTypeMainDrawer';
import { mapGetters } from 'vuex';
export default {
  name: 'hospitalInfo',
  components: { PublicTable, AllBtn, TjTypeMainDrawer },
  props: {
    //总传
    allInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      drawerTitle: '',
      drawerInfo: {}, //总传
      drawers: false,
      auditDrawers: false,
      auditDrawerInfo: {}, //总传
      formInline: '',
      codeArr: [],
      loading: false,
      tableDataCopy: [],
      tableData: [],
      pageSize: 50,
      currentPage: 1,
      theads: {
        value: '分类代码',
        label: '体检分类名称'
      },
      cell_blue: ['label'],
      columnWidth: {},
      excelList: [],
      checkTableList: [],
      title: '',
      checkAudits: 0
    };
  },
  created() {},
  mounted() {
    this.getTableData();
  },
  computed: { ...mapGetters(['G_EnumList', 'G_peClsList']) },
  methods: {
    //对应详情
    viewDetails(row) {
      this.title = row.label + '-主检医生对应列表';
      this.drawers = true;
      this.drawerInfo = row;
      this.checkAudits = 0;
      this.$nextTick(() => {
        console.log('[ this ]-213', this);
        this.$refs.drawer_ref.getAllData(this.checkAudits, row.value);
      });
    },
    auditDetails(row) {
      this.title = row.label + '-审核医生对应列表';
      this.auditDrawers = true;
      this.auditDrawerInfo = row;
      this.checkAudits = 1;
      this.$nextTick(() => {
        console.log('[ this ]-213', this);
        this.$refs.audit_ref.getAllData(this.checkAudits, row.value);
      });
    },
    handleClose() {
      this.drawers = false;
    },
    auditHandleClose() {
      this.auditDrawers = false;
    },
    // 获取表格信息数据
    getTableData() {
      this.tableData = this.G_peClsList;
      console.log(this.tableData);
    },
    //查询drawerInfo
    search() {
      this.formInline = this.$refs.allBtn_Ref.searchInfo.trim(); //获取组件文本框的值
      if (this.formInline) {
        setTimeout(() => {
          this.tableData = this.G_peClsList.filter((item) => {
            return (
              item.value.toString().indexOf(this.formInline) !== -1 ||
              item.label.indexOf(this.formInline) !== -1
            );
          });
          //this.loading = false;
        }, 500);
      } else {
        setTimeout(() => {
          this.getTableData();
          //this.loading = false;
        }, 500);
      }
    },

    // 取消
    cancel() {
      this.drawer = false;
      this.$refs.ruleForm.resetFields();
    },
    //复选框勾选操作
    handleSelectChangeTable(val) {
      // console.log("val: ", val);
      this.excelList = val;
      this.codeArr = [];
      val.map((item, i) => {
        if (item.value != '') {
          this.codeArr.push(item.value);
        }
      });
      console.log('this.codeArr: ', this.codeArr);
    },
    //多条删除
    delMore() {
      if (this.codeArr.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据!',
          type: 'warning'
        });
      }
      if (this.codeArr.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.$ajax
              .post(`${this.$apiUrls.RD_CodeFee}/Delete`, this.codeArr)
              .then((r) => {
                console.log('r111: ', r);
                const { returnData, success } = r.data;
                if (!success) {
                  return;
                }
                this.$message.success('删除成功!');
                this.getTableData();
              });
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    // 导出excel
    exportTable() {
      if (this.excelList.length == 0) {
        this.$message.warning('请选择至少一条数据进行操作!');
        return;
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const columns = [
          {
            title: '序号',
            key: 'index'
          },
          {
            title: '分类代码',
            key: 'value'
          },
          {
            title: '分类体检名称',
            key: 'label'
          }
        ];
        this.excelList.map((item, i) => {
          item.index = i + 1;
        });
        const title =
          '体检分类-主检/审核人员对应' + moment().format('YYYY-MM-DD');
        this.$nextTick(() => {
          export2Excel(columns, this.excelList, title);
          //this.$refs.multipleTable.clearSelection();
        });
      });
    },
    //打印
    print() {
      let tableCom_Ref = this.$refs.table_ref.$refs.tableCom_Ref;
      console.log('tableCom_Ref: ', tableCom_Ref.selection);
      if (tableCom_Ref.selection.length == 0) {
        return this.$message({
          message: '请至少选择一条数据进行打印！',
          type: 'warning',
          showClose: true
        });
      }
      let viewTableList_prop = this.$refs.table_ref._props.viewTableList;
      if (tableCom_Ref.store.states.isAllSelected) {
        this.checkTableList = viewTableList_prop;
      } else {
        this.checkTableList = tableCom_Ref.selection;
      }
      setTimeout(() => {
        printJS('printHtml');
      }, 500);
    }
  }
};
</script>
<style lang="less" scoped>
.tjTypeMain {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-weight: 600;
  // padding: 0 10px;
  header {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-header,
  .el-main {
    padding: 0;
  }
  .searchWrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  // .search-btn {
  //   padding: 8px 25px;
  //   font-size: 14px;
  // }
  // /deep/.el-drawer__body {
  //   padding: 0 20px;
  //   .el-form-item {
  //     margin-bottom: 12px;
  //   }
  // }
  /deep/.el-drawer__header {
    background: #d1e2f9;
    margin-bottom: 0;
    padding: 18px;
  }
  /deep/.el-drawer__header,
  /deep/.el-form-item__label {
    color: #2d3436;
  }
  /deep/.el-checkbox__inner {
    width: 20px;
    height: 20px;
  }
  /deep/.el-checkbox__inner::after {
    left: 7px;
    top: 3px;
  }
  /deep/.el-checkbox__inner::before {
    top: 8px;
  }
  .search-input {
    margin-right: 10px;
    // width: 322px;
  }
  .select {
    width: 100%;
  }
  .table-text {
    color: #1770df;
    // cursor: pointer;
  }
  .print_table {
    display: none;
  }
}
</style>
