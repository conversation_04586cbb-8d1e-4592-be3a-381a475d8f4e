<template>
  <div class="barcodeClassification">
    <!-- 体检项目信息 -->
    <el-container>
      <el-header>
        <AllBtn
          :btnList="['查询', '新建', '删除', '打印', '导出']"
          :methodCreat="newProject"
          :methodDelete="delMore"
          :methodSearch="search"
          :methodPrint="print"
          :methodExport="exportTable"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          @rowDblclick="handleClick"
          :cell_blue="cell_blue"
          @selectionChange="handleSelectChangeTable"
          :tableLoading.sync="loading"
          isCheck
          :columnWidth="columnWidth"
        >
          <template #reportShow="{ scope }">
            <div>
              {{ enum_abnormity[scope.row.reportShow] }}
            </div>
          </template>
          <template #isEnabled="{ scope }">
            <div>
              {{ enum_abnormity3[scope.row.isEnabled] }}
            </div>
          </template>
          <template #valueType="{ scope }">
            <div>
              {{ enum_abnormity2[scope.row.valueType] }}
            </div>
          </template>
          <template #columnRight>
            <el-table-column
              prop="operation"
              label="操作"
              width="110"
              fixed="right"
            >
              <template slot-scope="scope">
                <el-button
                  type="primary"
                  plain
                  size="mini"
                  class="view"
                  @click="viewDetails(scope.row)"
                  >{{ drawerInfo.btnName || '对应详情' }}</el-button
                >
              </template>
            </el-table-column>
          </template>
        </PublicTable>
      </el-main>
    </el-container>
    <el-drawer
      :title="claName"
      :visible.sync="drawer"
      :before-close="handleClose"
      :wrapperClosable="false"
    >
      <el-form :model="popupForm" ref="ruleForm" :rules="rules">
        <el-form-item
          v-if="funTpye !== '/Create'"
          label="项目代码"
          :label-width="formLabelWidth"
          prop="itemCode"
        >
          <el-input
            v-model.trim="popupForm.itemCode"
            autocomplete="off"
            size="small"
            :disabled="disabled"
            placeholder="请输入项目代码"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          label="项目名称"
          :label-width="formLabelWidth"
          prop="itemName"
        >
          <el-input
            v-model.trim="popupForm.itemName"
            autocomplete="off"
            size="small"
            placeholder="请输入项目名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          label="项目分类"
          :label-width="formLabelWidth"
          prop="clsCode"
        >
          <el-select
            v-model.trim="popupForm.clsCode"
            placeholder="请选择项目分类"
            style="width: 100%"
            size="small"
            clearable
            filterable
          >
            <el-option
              v-for="item in optionsClsCode"
              :key="item.clsCode"
              :label="item.clsName"
              :value="item.clsCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="结果单位"
          :label-width="formLabelWidth"
          prop="unit"
        >
          <el-input
            v-model.trim="popupForm.unit"
            autocomplete="off"
            size="small"
            placeholder="请输入结果单位"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          label="结果类型"
          :label-width="formLabelWidth"
          prop="valueType"
        >
          <el-select
            v-model.trim="popupForm.valueType"
            placeholder="请选择结果类型"
            size="small"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="item in G_valueType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="启用标识"
          :label-width="formLabelWidth"
          prop="isEnabled"
        >
          <el-radio-group v-model.trim="popupForm.isEnabled">
            <el-radio :label="true">启用</el-radio>
            <el-radio :label="false">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="报告中显示"
          :label-width="formLabelWidth"
          prop="reportShow"
        >
          <!-- <el-input
            v-model.trim="popupForm.reportShow"
            autocomplete="off"
            size="small"
          ></el-input> -->
          <el-select
            v-model.trim="popupForm.reportShow"
            placeholder="请选择是否报告中显示"
            style="width: 100%"
            size="small"
            clearable
          >
            <el-option
              v-for="item in options"
              :key="item.label"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="显示顺序"
          :label-width="formLabelWidth"
          prop="sortIndex"
        >
          <el-input
            v-model.trim="popupForm.sortIndex"
            autocomplete="off"
            size="small"
            placeholder="请输入显示顺序"
            clearable
          ></el-input>
        </el-form-item>

        <el-form-item
          label="拼音码"
          :label-width="formLabelWidth"
          prop="pinYinCode"
        >
          <el-input
            v-model.trim="popupForm.pinYinCode"
            autocomplete="off"
            size="small"
            placeholder="请输入拼音码"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          label="五笔码"
          :label-width="formLabelWidth"
          prop="wuBiCode"
        >
          <el-input
            v-model.trim="popupForm.wuBiCode"
            autocomplete="off"
            size="small"
            placeholder="请输入五笔码"
            clearable
          ></el-input>
        </el-form-item>
        <!-- <el-form-item
          label="标本类型"
          :label-width="formLabelWidth"
          prop="sampCode"
        >
          <el-select
            class="select"
            v-model.trim="popupForm.sampCode"
            placeholder="请选择标本类型"
            size="small"
            style="width: 100%"
            filterable
            clearable
          >
            <el-option
              v-for="item in sampList"
              :key="item.sampCode"
              :label="item.sampName"
              :value="item.sampCode"
            >
            </el-option>
          </el-select>
        </el-form-item> -->
        <!-- <el-form-item
          label="注意事项"
          :label-width="formLabelWidth"
          prop="attention"
        >
          <el-input
            type="textarea"
            v-model.trim="popupForm.attention"
            autocomplete="off"
            size="small"
            placeholder="请输入注意事项"
          ></el-input>
        </el-form-item> -->
        <el-form-item
          label="参考值"
          :label-width="formLabelWidth"
          prop="referenceValue"
        >
          <el-input
            v-model.trim="popupForm.referenceValue"
            autocomplete="off"
            size="small"
            placeholder="请输入参考值"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          label="国家编码"
          :label-width="formLabelWidth"
          prop="occupationalItemCode"
          v-if="G_config.physicalMode.includes('职检')"
        >
          <el-select
            v-model="popupForm.occupationalItemCode"
            placeholder="请选择结果类型"
            size="small"
            style="width: 100%"
            filterable
            clearable
          >
            <el-option
              v-for="item in countryCodeList"
              :key="item.itemCode"
              :label="item.itemName"
              :value="item.itemCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="handleClose" size="small">取消</el-button>
        <el-button class="blue_btn" @click="submit" size="small"
          >保存</el-button
        >
      </div>
    </el-drawer>
    <!-- 对应详情抽屉 -->
    <el-drawer
      :title="drawerTitle"
      :visible.sync="drawers"
      :close-on-click-modal="false"
      :wrapperClosable="false"
      size="80%"
    >
      <DetailsDrawer
        :cancel="handleClose"
        :drawerInfo="drawerInfo"
        ref="drawer_ref"
      ></DetailsDrawer>
    </el-drawer>
  </div>
</template>

<script>
import { export2Excel } from '@/common/excelUtil';
import PublicTable from '../../../components/publicTable.vue';
import AllBtn from '../allBtn.vue';
import { mapGetters } from 'vuex';
import moment from 'moment';
import DetailsDrawer from '../otherCodeMapping/detailsDrawer.vue';
import { dataUtils } from '@/common';
export default {
  name: 'barcodeClassification',
  components: { PublicTable, AllBtn, DetailsDrawer },
  props: {
    clsCode: {
      type: String
    },
    claName: {
      type: String
    },
    //总传
    allInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      formInline: '',
      codeArr: [],
      resizeFlag: '',
      loading: false,
      disabled: false,
      tableDataCopy: [],
      tableData: [],
      dialogTitle: '',
      drawer: false,
      isAdd: true,
      formLabelWidth: '160px',
      options: [
        {
          value: true,
          label: '是'
        },
        {
          value: false,
          label: '否'
        }
      ],
      roleList: [],
      optionsClsCode: [],
      popupForm: {
        itemCode: '',
        itemName: '',
        clsCode: '',
        unit: '',
        valueType: '',
        isEnabled: true,
        reportShow: true,
        sortIndex: '',
        pinYinCode: '',
        wuBiCode: '',
        occupationalItemCode: '',
        referenceValue: ''
      },
      theads: {
        itemCode: '项目代码',
        itemName: '项目名称',
        clsCodeName: '项目分类',
        unit: '结果单位',
        valueType: '结果类型',
        isEnabled: '启用标识',
        reportShow: '报告中显示',
        occupationalItemName: '国家编码',
        sortIndex: '显示顺序',
        pinYinCode: '拼音码',
        wuBiCode: '五笔码'
      },
      cell_blue: ['itemName'],
      columnWidth: {
        itemName: 240,
        reportShow: 120
      },
      enum_abnormity: {
        true: '是',
        false: '否'
      },
      enum_abnormity2: {
        0: '字符型',
        1: '数值型',
        2: '复合型'
      },
      enum_abnormity3: {
        true: '启用',
        false: '禁用'
      },
      rules: {
        // itemCode: [
        //   { required: true, message: "请输入项目代码", trigger: "blur" }
        // ],
        itemName: [
          { required: true, message: '请输入项目名称', trigger: 'blur' }
        ],
        clsCode: [
          { required: true, message: '请输入项目分类', trigger: 'blur' }
        ],
        valueType: [
          { required: true, message: '请输入结果类型', trigger: 'blur' }
        ],
        isEnabled: [
          { required: true, message: '请输入启用标识', trigger: 'blur' }
        ],
        reportShow: [
          { required: true, message: '请输入报告中显示', trigger: 'blur' }
        ]
      },
      funTpye: '/Create',
      excelList: [],
      drawers: false,
      drawerTitle: '',
      drawerInfo: this.allInfo, //总传
      sampList: [],
      countryCodeList: []
    };
  },
  watch: {
    clsCode() {
      this.tableData = [];
      this.tableDataCopy = [];
      this.getItemCls();
    }
  },
  created() {
    // 项目分类
    this.getCodeItemCls();
    this.getReadCodeOccupationalItemByPage();
  },
  mounted() {
    this.getItemCls();
    this.getSamp();
  },
  computed: {
    ...mapGetters(['G_valueType', 'G_config'])
  },
  methods: {
    //对应详情
    viewDetails(row) {
      this.drawers = true;
      this.drawerTitle = row.itemName + '-项目对应';
      this.drawerInfo.title2 = row.itemName + '-项目对应列表';
      this.drawerInfo.setInfo = { itemCode: row.itemCode };
      this.drawerInfo.setInfos = {
        itemCode: row.itemCode,
        itemName: row.itemName
      };
      //执行获取下拉,左右表格数据
      this.$nextTick(() => {
        this.$refs.drawer_ref.getAll();
        this.$refs.drawer_ref.clearInput();
      });
    },
    handleClose() {
      this.drawer = false;
      this.drawers = false;
      this.$nextTick(() => {
        this.$refs.ruleForm.resetFields();
      });
    },
    //查询
    search() {
      this.formInline = this.$refs.allBtn_Ref.searchInfo.trim(); //获取组件文本框的值
      if (this.formInline.length > 0) {
        this.tableData = this.tableDataCopy.filter((item) => {
          return `${item?.itemCode || ''}${item?.itemName || ''}${
            item?.pinYinCode || ''
          }${item?.wuBiCode || ''}`
            .toLocaleLowerCase()
            .includes(this.formInline.toLocaleLowerCase());
        });
      } else {
        this.getItemCls();
      }
    },
    // 获取项目分类信息数据
    getItemCls() {
      this.loading = true;
      this.$ajax
        .post(this.$apiUrls.PageQuery_CodeItem, '', {
          query: {
            clsCode: this.clsCode
          }
        })
        .then((r) => {
          // this.tableData = r.data.returnData;
          this.tableDataCopy = r.data.returnData;

          this.tableData = this.tableDataCopy.filter((item) => {
            return `${item?.itemCode || ''}${item?.itemName || ''}${
              item?.pinYinCode || ''
            }${item?.wuBiCode || ''}`
              .toLocaleLowerCase()
              .includes(this.formInline.toLocaleLowerCase());
          });

          this.totalPage = r.data.totalPage;
          this.loading = false;
        });
    },
    // 获取标本类型
    getSamp() {
      this.$ajax.post(this.$apiUrls.RD_CodeSample + '/Read', []).then((r) => {
        let { returnData, success } = r.data;
        if (!success) return;
        this.sampList = returnData;
      });
    },
    //编辑
    handleClick(row) {
      console.log('row', row);
      this.drawer = true;
      this.disabled = true;
      this.funTpye = '/Update';
      this.$nextTick(() => {
        this.popupForm = dataUtils.deepCopy(row);
        this.$refs.ruleForm.resetFields();
      });
    },
    //新增
    newProject() {
      this.popupForm = {
        itemCode: '',
        itemName: '',
        clsCode: this.clsCode,
        unit: '',
        valueType: '',
        isEnabled: true,
        reportShow: true,
        sortIndex: 0,
        pinYinCode: '',
        wuBiCode: '',
        occupationalItemCode: ''
      };
      this.drawer = true;
      this.funTpye = '/Create';
      this.disabled = false;
    },
    //提交
    submit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$ajax
            .post(this.$apiUrls.CU_CodeItem + this.funTpye, this.popupForm)
            .then((r) => {
              r = r.data;
              if (!r.success) {
                return;
              }
              this.tableData = [];
              this.drawer = false;
              this.$nextTick(() => {
                // this.$refs.allBtn_Ref.searchInfo = "";
                this.getItemCls();
              });
              if (this.funTpye == '/Create') {
                this.$message({
                  message: '新建成功',
                  type: 'success',
                  showClose: true
                });
              } else {
                this.$message({
                  message: '修改成功',
                  type: 'success',
                  showClose: true
                });
              }
            });
        } else {
          return false;
        }
      });
    },
    // 项目分类
    getCodeItemCls() {
      this.$ajax.post(this.$apiUrls.RD_CodeItemCls + '/Read', []).then((r) => {
        this.optionsClsCode = r.data.returnData;
      });
    },
    //复选框勾选操作
    handleSelectChangeTable(val) {
      //console.log(val);
      this.excelList = val;
      this.codeArr = [];
      val.map((item, i) => {
        if (item.barcode != '') {
          this.codeArr.push(item.itemCode);
        }
      });
    },
    //多条删除
    delMore() {
      if (this.codeArr.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据!',
          type: 'warning'
        });
      }
      if (this.codeArr.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            console.log(this.codeArr);
            this.$ajax
              .post(this.$apiUrls.Delete_CodeItem, this.codeArr)
              .then((r) => {
                let { success } = r.data;
                if (!success) return;
                this.$message({
                  showClose: true,
                  message: '删除成功',
                  type: 'success'
                });
                this.getItemCls();
              });
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    // 获取职业列表
    getReadCodeOccupationalItemByPage() {
      this.$ajax
        .post(this.$apiUrls.ReadCodeOccupationalItemByPage, { keyword: '' })
        .then((r) => {
          this.countryCodeList = r.data?.returnData || [];
        });
    },
    // 导出excel
    exportTable() {
      if (this.excelList.length == 0) {
        this.$message.warning('请选择至少一条数据进行操作!');
        return;
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const columns = [
          {
            title: '序号',
            key: 'index'
          },
          {
            title: '项目代码',
            key: 'itemCode'
          },
          {
            title: '项目名称',
            key: 'itemName'
          },
          {
            title: '结果单位',
            key: 'valueType'
          },
          {
            title: '启用标识',
            key: 'isEnabled'
          },
          {
            title: '报告中显示',
            key: 'reportShow'
          },
          {
            title: '显示顺序',
            key: 'sortIndex'
          },
          {
            title: '拼音码',
            key: 'pinYinCode'
          },
          {
            title: '五笔码',
            key: 'wuBiCode'
          }
        ];
        this.excelList.map((item, i) => {
          item.index = i + 1;
        });
        const title = '体检项目信息' + moment().format('YYYY-MM-DD');
        this.$nextTick(() => {
          export2Excel(columns, this.excelList, title);
        });
      });
    },
    print() {}
  }
};
</script>
<style lang="less" scoped>
.barcodeClassification {
  display: flex;
  flex-direction: column;
  font-weight: 600;
  width: 100%;
  height: 100%;
  header {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-container.is-vertical {
    height: 100%;
  }
  .el-header,
  .el-main {
    padding: 0;
  }

  .searchWrap .el-form-item:last-child {
    margin-right: 0;
    margin-left: 10px;
  }
  .indexPage .contentWrap .tabPage {
    margin-top: 0;
  }
  .el-pagination {
    text-align: center;
  }
  .el-drawer.rtl {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #2d3436;
    padding: 0 20px 0 10px;
  }
  .el-drawer__body {
    padding-right: 20px;

    .el-form-item {
      margin-bottom: 10px;
    }
  }
  /deep/.el-drawer__header {
    align-items: center;
    display: flex;
    background: rgba(23, 112, 223, 0.2);
    border-radius: 4px 4px 0 0;
    line-height: 42px;
    font-family: PingFangSC-Medium;
    font-size: 18px;
    padding: 0;
    padding-left: 20px;
    color: #2d3436;
    margin-bottom: 0;
  }
  /deep/.el-drawer__close-btn {
    font-size: 30px;
  }
  /deep/.el-drawer__body {
    flex: 1;
  }
}
</style>
<style>
.el-dialog {
  width: 480px;
}
.el-table .el-table__cell {
  padding: 5px 0;
}
.el-main .el-checkbox__inner {
  width: 20px;
  height: 20px;
}
.el-drawer__body {
  padding-right: 20px;
}
.el-drawer__header,
.el-form-item__label {
  color: #2d3436;
}
</style>
