<!--
 * @Date: 2022-06-08 09:11:19
 * @Autor: lvshuwen
 * @LastEditTime: 2022-06-13 18:00:57
 * @Description: 档案项目
-->

<template>
  <div class="classifyInformation">
    <!-- 档案项目 -->
    <el-container>
      <el-header>
        <AllBtn
          :btnList="['查询', '新建', '删除', '打印', '导出']"
          :methodCreat="newProject"
          :methodDelete="delMore"
          :methodSearch="search"
          :methodPrint="print"
          :methodExport="exportTable"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          @rowDblclick="handleClick"
          :cell_blue="cell_blue"
          @selectionChange="handleSelectChangeTable"
          :tableLoading.sync="loading"
          isCheck
        >
          <template #columnRight>
            <el-table-column
              prop="operation"
              label="操作"
              width="110"
              fixed="right"
            >
              <template slot-scope="scope">
                <el-button
                  type="primary"
                  plain
                  size="mini"
                  class="view"
                  @click="viewDetails(scope.row)"
                  >对应详情</el-button
                >
              </template>
            </el-table-column>
          </template>
        </PublicTable>
      </el-main>
    </el-container>
    <el-drawer
      title="档案项目信息属性"
      :visible.sync="drawer"
      :before-close="handleClose"
      :wrapperClosable="false"
    >
      <el-form :model="popupForm" ref="ruleForm" :rules="rules">
        <el-form-item
          label="档案项目代码"
          :label-width="formLabelWidth"
          prop="archiveCode"
        >
          <el-input
            v-model.trim="popupForm.archiveCode"
            autocomplete="off"
            size="small"
            :disabled="disabled"
            placeholder="请输入档案项目代码"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          label="档案项目名称"
          :label-width="formLabelWidth"
          prop="archiveName"
        >
          <el-input
            v-model.trim="popupForm.archiveName"
            autocomplete="off"
            size="small"
            placeholder="请输入档案项目名称"
            clearable
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="handleClose" size="small">取消</el-button>
        <el-button class="blue_btn" @click="submit" size="small"
          >保存</el-button
        >
      </div>
    </el-drawer>
    <el-drawer
      :title="drawerTitle"
      :visible.sync="drawers"
      :close-on-click-modal="false"
      :wrapperClosable="false"
      size="80%"
    >
      <DetailsDrawer
        :cancel="handleClose"
        :drawerInfo="drawerInfo"
        ref="drawer_ref"
      ></DetailsDrawer>
    </el-drawer>
  </div>
</template>

<script>
import { export2Excel } from '@/common/excelUtil';
import moment from 'moment';
import PublicTable from '../../../components/publicTable.vue';
import AllBtn from '../allBtn.vue';
import DetailsDrawer from '../otherCodeMapping/detailsDrawer.vue';
export default {
  name: 'classifyInformation',
  components: { PublicTable, AllBtn, DetailsDrawer },
  props: {
    //总传
    allInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      drawerTitle: '',
      drawers: false,
      drawerInfo: this.allInfo, //总传
      formInline: '',
      codeArr: [],
      resizeFlag: '',
      loading: false,
      disabled: false,
      tableDataCopy: [],
      tableData: [],
      pageSize: 50,
      currentPage: 1,
      dialogTitle: '',
      drawer: false,
      isAdd: true,
      formLabelWidth: '120px',
      roleList: [],
      popupForm: {
        archiveCode: '',
        archiveName: ''
      },
      theads: {
        archiveCode: '档案项目代码',
        archiveName: '档案项目名称'
      },
      cell_blue: ['archiveName'],
      rules: {
        archiveCode: [
          { required: true, message: '请输入档案项目代码', trigger: 'blur' }
        ],
        archiveName: [
          { required: true, message: '请输入档案项目名称', trigger: 'blur' }
        ]
      },
      funTpye: '/Create',
      excelList: []
    };
  },
  created: function () {},
  mounted: function () {
    this.getItemCls();
  },
  watch: {
    tableDataCopy(n, o) {
      this.tableData = [];
      this.$nextTick(() => {
        if (this.currentPage * this.pageSize >= n.length) {
          this.tableData.push(...n);
          return;
        }
        let start = (this.currentPage - 1) * this.pageSize;
        let end = this.currentPage * this.pageSize;
        let pageData = n.slice(start, end);
        console.log(pageData);
        this.tableData.push(...pageData);
      });
    }
  },
  methods: {
    //对应详情
    viewDetails(row) {
      console.log(this);
      this.drawers = true;
      this.drawerTitle = row.archiveName + '-项目对应';
      this.drawerInfo.title2 = row.archiveName + '-项目对应列表';
      this.drawerInfo.setInfo = { archiveCode: row.archiveCode };
      this.drawerInfo.setInfos = {
        archiveCode: row.archiveCode,
        archiveName: row.archiveName
      };
      console.log('[ this.drawerInfo ]-841', this.drawerInfo);
      //执行获取下拉,左右表格数据
      this.$nextTick(() => {
        this.$refs.drawer_ref.getAll();
        this.$refs.drawer_ref.clearInput();
      });
    },
    handleClose() {
      this.drawer = false;
      this.drawers = false;
      this.$refs.ruleForm.resetFields();
    },
    //查询
    search() {
      this.formInline = this.$refs.allBtn_Ref.searchInfo.trim(); //获取组件文本框的值
      if (this.formInline.length > 0) {
        this.tableData = this.tableDataCopy.filter((item) => {
          return (
            item.archiveCode.indexOf(this.formInline) !== -1 ||
            item.archiveName.indexOf(this.formInline) !== -1
          );
        });
      } else {
        this.getItemCls();
      }
      //this.loading = false;
    },

    //编辑
    handleClick(row, column, event) {
      console.log('row', row);
      this.drawer = true;
      this.disabled = true;
      this.funTpye = '/Update';
      this.popupForm = {
        archiveCode: row.archiveCode,
        archiveName: row.archiveName
      };
    },
    //新增
    newProject() {
      this.popupForm = {
        archiveCode: '',
        archiveName: ''
      };
      this.drawer = true;
      this.funTpye = '/Create';
      this.disabled = false;
    },
    // 获取项目分类信息数据
    getItemCls() {
      console.log(this.$apiUrls);
      this.loading = true;
      this.$ajax
        .post(this.$apiUrls.RD_CodeArchiveItem + '/Read', [])
        .then((r) => {
          // this.tableData = r.data.returnData;
          this.tableDataCopy = r.data.returnData;
          this.tableData = this.tableDataCopy.filter((item) => {
            return (
              item.archiveCode.indexOf(this.formInline) !== -1 ||
              item.archiveName.indexOf(this.formInline) !== -1
            );
          });
          this.loading = false;
        });
    },
    //提交
    submit() {
      console.log(this.popupForm);
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$ajax
            .post(
              this.$apiUrls.CU_CodeArchiveItem + this.funTpye,
              this.popupForm
            )
            .then((r) => {
              r = r.data;
              if (!r.success) {
                return;
              }
              this.tableData = [];
              this.drawer = false;
              this.$nextTick(() => {
                // this.$refs.allBtn_Ref.searchInfo = "";
                this.getItemCls();
              });
              if (this.funTpye == '/Create') {
                this.$message({
                  message: '新建成功',
                  type: 'success',
                  showClose: true
                });
              } else {
                this.$message({
                  message: '修改成功',
                  type: 'success',
                  showClose: true
                });
              }
            });
        } else {
          return false;
        }
      });
    },
    //复选框勾选操作
    handleSelectChangeTable: function (val) {
      this.excelList = val;
      this.codeArr = [];
      val.map((item, i) => {
        if (item.archiveCode != '') {
          this.codeArr.push(item.archiveCode);
        }
      });
      //console.log(this.codeArr);
    },
    //多条删除
    delMore() {
      if (this.codeArr.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据!',
          type: 'warning'
        });
      }
      if (this.codeArr.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            // this.delete(this.codeArr);
            console.log(this.codeArr);
            this.$ajax
              .post(this.$apiUrls.RD_CodeArchiveItem + '/Delete', this.codeArr)
              .then((r) => {
                let { success } = r.data;
                if (!success) return;
                this.$message({
                  showClose: true,
                  message: '删除成功',
                  type: 'success'
                });
                this.getItemCls();
              });
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    // 导出excel
    exportTable() {
      if (this.excelList.length == 0) {
        this.$message.warning('请选择至少一条数据进行操作!');
        return;
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const columns = [
          {
            title: '序号',
            key: 'index'
          },
          {
            title: '项目代码',
            key: 'archiveCode'
          },
          {
            title: '项目名称',
            key: 'archiveName'
          }
        ];
        this.excelList.map((item, i) => {
          item.index = i + 1;
        });
        const title = '档案项目信息' + moment().format('YYYY-MM-DD');
        this.$nextTick(() => {
          export2Excel(columns, this.excelList, title);
        });
      });
    },
    print() {}
  }
};
</script>
<style lang="less" scoped>
.classifyInformation {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  font-weight: 600;
  header {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-container.is-vertical {
    height: 100%;
  }
  .el-header,
  .el-main {
    padding: 0;
  }

  .searchWrap .el-form-item:last-child {
    margin-right: 0;
    margin-left: 10px;
  }
  .indexPage .contentWrap .tabPage {
    margin-top: 0;
  }
  .el-pagination {
    text-align: center;
  }
  /deep/.el-drawer__header {
    background: #d1e2f9;
    margin-bottom: 0;
    padding: 18px;
  }
  .el-drawer.rtl {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #2d3436;
    padding: 0 20px 0 10px;
  }
  .el-drawer__body {
    padding-right: 20px;

    .el-form-item {
      margin-bottom: 10px;
    }
  }
}
</style>
<style>
.el-dialog {
  width: 480px;
}
.el-table .el-table__cell {
  padding: 5px 0;
}
.el-main .el-checkbox__inner {
  width: 20px;
  height: 20px;
}
.el-drawer__body {
  padding-right: 20px;
}
.el-drawer__header,
.el-form-item__label {
  color: #2d3436;
}
</style>
