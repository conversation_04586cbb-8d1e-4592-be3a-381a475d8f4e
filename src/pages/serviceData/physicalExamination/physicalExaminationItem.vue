<!--
 * @FilePath: \shenshan\KrPeis\src\pages\serviceData\physicalExamination\physicalExaminationItem.vue
 * @Description  :  组合维护界面
 * <AUTHOR> justin
 * @Date         : 2024-07-16 09:58:56
 * @Version      : 0.0.1
 * @LastEditors: key
 * @LastEditTime: 2025-04-23 16:27:40
*
-->
<template>
  <div class="barcodeClassification">
    <!-- 体检组合信息 -->
    <el-container>
      <el-header>
        <AllBtn
          :btnList="['查询', '新建', '删除', '打印', '导出']"
          :methodCreat="newProject"
          :methodDelete="delMore"
          :methodSearch="search"
          :methodPrint="print"
          :methodExport="exportTable"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          @rowDblclick="handleClick"
          :cell_blue="cell_blue"
          @selectionChange="handleSelectChangeTable"
          :tableLoading.sync="loading"
          isCheck
          :columnWidth="columnWidth"
        >
          <template #discountAllow="{ scope }">
            <div>
              {{ enum_abnormity[scope.row.discountAllow] }}
            </div>
          </template>
          <template #weChatAddAllow="{ scope }">
            <div>
              {{ enum_abnormity[scope.row.weChatAddAllow] }}
            </div>
          </template>
          <template #reportShow="{ scope }">
            <div>
              {{ enum_abnormity1[scope.row.reportShow] }}
            </div>
          </template>
          <template #isEnabled="{ scope }">
            <div>
              {{ enum_abnormity2[scope.row.isEnabled] }}
            </div>
          </template>
          <template #checkLimit="{ scope }">
            <div>
              {{ enum_abnormity3[scope.row.checkLimit] }}
            </div>
          </template>
          <template #checkCls="{ scope }">
            <div>
              {{ enum_abnormity4[scope.row.checkCls] }}
            </div>
          </template>
          <template #columnRight>
            <el-table-column
              prop="operation"
              label="操作"
              width="220"
              fixed="right"
            >
              <template slot-scope="scope">
                <el-button
                  type="primary"
                  plain
                  size="mini"
                  class="view"
                  @click="viewDetails(scope.row)"
                  >{{ drawerInfo.btnName || '对应详情' }}</el-button
                >
                <el-button
                  type="primary"
                  plain
                  size="mini"
                  class="view"
                  @click="hisViewDetails(scope.row)"
                  >收费项目对应</el-button
                >
              </template>
            </el-table-column>
          </template>
        </PublicTable>
      </el-main>
    </el-container>
    <el-drawer
      :title="claName"
      :visible.sync="drawer"
      :before-close="handleClose"
      :wrapperClosable="false"
      size="50%"
    >
      <el-form :model="popupForm" ref="ruleForm" :rules="rules">
        <div style="overflow-y: auto; overflow-x: hidden; height: 88vh">
          <el-divider content-position="left">基础信息</el-divider>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item
                v-if="funTpye !== '/Create'"
                label="组合代码"
                :label-width="formLabelWidth"
                prop="combCode"
              >
                <el-input
                  v-model.trim="popupForm.combCode"
                  autocomplete="off"
                  size="small"
                  :disabled="disabled"
                  placeholder="请输入组合代码"
                  clearable
                ></el-input>
              </el-form-item>

              <el-form-item
                label="组合简称"
                :label-width="formLabelWidth"
                prop="shortName"
              >
                <el-input
                  v-model.trim="popupForm.shortName"
                  autocomplete="off"
                  size="small"
                  placeholder="请输入组合简称"
                  clearable
                ></el-input>
              </el-form-item>

              <el-form-item
                label="性别"
                :label-width="formLabelWidth"
                prop="sex"
              >
                <el-select
                  class="select"
                  v-model.trim="popupForm.sex"
                  placeholder="请选择性别"
                  size="small"
                  style="width: 100%"
                  clearable
                >
                  <el-option
                    v-for="item in G_shareSexList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item
                label="检查类型"
                :label-width="formLabelWidth"
                prop="checkCls"
              >
                <el-select
                  v-model.trim="popupForm.checkCls"
                  placeholder="请选择检查类型"
                  style="width: 100%"
                  size="small"
                  clearable
                  filterable
                  @change="checkClsChange"
                >
                  <el-option
                    v-for="item in G_checkCls"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item
                label="项目分类"
                :label-width="formLabelWidth"
                prop="clsCode"
              >
                <el-select
                  v-model.trim="popupForm.clsCode"
                  placeholder="请选择项目分类"
                  style="width: 100%"
                  size="small"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="item in optionsClsCode"
                    :key="item.clsCode"
                    :label="item.clsName"
                    :value="item.clsCode"
                  >
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item
                label="折扣标识"
                :label-width="formLabelWidth"
                prop="discountAllow"
              >
                <el-select
                  v-model.trim="popupForm.discountAllow"
                  placeholder="请选择折扣标识"
                  style="width: 100%"
                  size="small"
                  clearable
                >
                  <el-option
                    v-for="item in optionsDiscountAllow"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item
                label="报告中显示"
                :label-width="formLabelWidth"
                prop="reportShow"
              >
                <el-select
                  v-model.trim="popupForm.reportShow"
                  placeholder="请选择是否报告中显示"
                  style="width: 100%"
                  size="small"
                  clearable
                >
                  <el-option
                    v-for="item in optionsReportShow"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item
                label="拼音码"
                :label-width="formLabelWidth"
                prop="pinYinCode"
              >
                <el-input
                  v-model.trim="popupForm.pinYinCode"
                  autocomplete="off"
                  size="small"
                  placeholder="请输入拼音码"
                  clearable
                ></el-input>
              </el-form-item>

              <el-form-item
                label="五笔码"
                :label-width="formLabelWidth"
                prop="wuBiCode"
              >
                <el-input
                  v-model.trim="popupForm.wuBiCode"
                  autocomplete="off"
                  size="small"
                  placeholder="请输入五笔码"
                  clearable
                ></el-input>
              </el-form-item>

              <el-form-item
                label="启用标识"
                :label-width="formLabelWidth"
                prop="isEnabled"
              >
                <el-radio-group v-model.trim="popupForm.isEnabled">
                  <el-radio :label="true">启用</el-radio>
                  <el-radio :label="false">禁用</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item
                label="组合名称"
                :label-width="formLabelWidth"
                prop="combName"
              >
                <el-input
                  v-model.trim="popupForm.combName"
                  autocomplete="off"
                  size="small"
                  placeholder="请输入组合名称"
                  clearable
                ></el-input>
              </el-form-item>

              <el-form-item :label-width="formLabelWidth" prop="price">
                <template #label>
                  <span>单价</span>
                  <el-tooltip
                    style="display: inline; margin-left: 5px"
                    effect="light"
                    placement="top"
                  >
                    <i class="el-icon-question" />
                    <template slot="content">
                      <p style="color: red; font-size: 16px">
                        当前【单价】不可设置，请前往【收费项目对应】去设置
                      </p>
                    </template>
                  </el-tooltip>
                </template>

                <el-input
                  v-model.trim="popupForm.price"
                  autocomplete="off"
                  size="small"
                  disabled
                  placeholder="请输入单价"
                  clearable
                ></el-input>
              </el-form-item>

              <el-form-item
                label="显示顺序"
                :label-width="formLabelWidth"
                prop="sortIndex"
              >
                <el-input
                  v-model.trim="popupForm.sortIndex"
                  autocomplete="off"
                  size="small"
                  placeholder="请输入显示顺序"
                  clearable
                ></el-input>
              </el-form-item>

              <el-form-item
                label="检查科室"
                :label-width="formLabelWidth"
                prop="examDeptCode"
              >
                <el-select
                  v-model.trim="popupForm.examDeptCode"
                  placeholder="请选择检查科室"
                  style="width: 100%"
                  size="small"
                  filterable
                  clearable
                >
                  <el-option
                    v-for="item in optionsExamDeptCode"
                    :key="item.deptCode"
                    :label="item.deptName"
                    :value="item.deptCode"
                  >
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item
                label="采集地点"
                :label-width="formLabelWidth"
                prop="gatherPlace"
              >
                <el-select
                  v-model.trim="popupForm.gatherPlace"
                  placeholder="请选择检查地点"
                  style="width: 100%"
                  size="small"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="item in optionsExamRoom"
                    :key="item.placeCode"
                    :label="item.placeName"
                    :value="item.placeCode"
                  >
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item
                label="检查限制"
                :label-width="formLabelWidth"
                prop="checkLimit"
              >
                <el-select
                  v-model.trim="popupForm.checkLimit"
                  placeholder="请选择检查限制"
                  style="width: 100%"
                  size="small"
                  clearable
                >
                  <el-option
                    v-for="item in G_checkLimit"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item
                label="收费基础分类"
                :label-width="formLabelWidth"
                prop="feeBasicCls"
              >
                <el-select
                  v-model.trim="popupForm.feeBasicCls"
                  placeholder="请选择收费基础分类"
                  style="width: 100%"
                  size="small"
                  clearable
                >
                  <el-option
                    v-for="item in optionsFeeBasicCls"
                    :key="item.feeClsCode"
                    :label="item.feeClsName"
                    :value="item.feeClsCode"
                  >
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item
                label="提示信息"
                :label-width="formLabelWidth"
                prop="hint"
              >
                <el-input
                  type="textarea"
                  v-model.trim="popupForm.hint"
                  autocomplete="off"
                  size="small"
                  placeholder="请输入提示信息"
                ></el-input>
              </el-form-item>

              <el-form-item
                label="注意事项"
                :label-width="formLabelWidth"
                prop="attention"
              >
                <el-input
                  type="textarea"
                  v-model.trim="popupForm.attention"
                  autocomplete="off"
                  size="small"
                  placeholder="请输入注意事项"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider content-position="left">医嘱对应</el-divider>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item
                label="医嘱项目"
                :label-width="formLabelWidth"
                prop="orderItemCode"
                class="order-item-code-box"
              >
                <el-select
                  v-model.trim="popupForm.hisOrderCode"
                  filterable
                  clearable
                  remote
                  :remote-method="searchHisOrderList"
                  style="width: 100%"
                  placeholder="请选择"
                  size="small"
                  v-loadmore="loadMoreFun"
                  ref="hisOrderRef"
                  @change="getOrderItemCNName"
                >
                  <el-option
                    v-for="(item, index) in optionsHisOrder"
                    :key="index"
                    :label="item.orderItemCode"
                    :value="item.orderItemCode"
                    :title="item.orderItemCode"
                  >
                    <span style="float: left">{{
                      item.orderItemCode + ' -- ' + item.orderItemCNName
                    }}</span>
                  </el-option>
                </el-select>

                <el-checkbox
                  v-model="popupForm.isDedicatePies"
                  style="margin-left: 10px"
                >
                  <el-tooltip
                    class="item"
                    effect="light"
                    content="注：用于公告提示，若是体检专用，请勾选此项（勾选后则屏蔽提示）"
                    placement="top"
                  >
                    <span>体</span>
                  </el-tooltip></el-checkbox
                >
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item
                label="医嘱名称"
                :label-width="formLabelWidth"
                prop="orderItemCode"
              >
                <el-input
                  v-model.trim="popupForm.hisOrderCNName"
                  :title="popupForm.hisOrderCNName"
                  autocomplete="off"
                  size="small"
                  disabled
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <template v-if="popupForm.checkCls == 3">
            <!-- 检查类型为检验检查时显示 -->
            <el-divider content-position="left">检验条码</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item
                  label="条码类型"
                  :label-width="formLabelWidth"
                  prop="barCodeType"
                  :rules="[
                    {
                      required: true,
                      message: '请选择条码类型',
                      trigger: 'change'
                    }
                  ]"
                >
                  <el-select
                    v-model.trim="popupForm.barCodeType"
                    placeholder="请选择条码类型"
                    style="width: 100%"
                    size="small"
                    filterable
                    clearable
                  >
                    <el-option
                      v-for="item in optionsBarcodeType"
                      :key="item.barcode"
                      :label="item.barcodeName"
                      :value="item.barcode"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item
                  label="是否合管"
                  :label-width="formLabelWidth"
                  prop="isMergeTube"
                >
                  <el-radio-group v-model.trim="popupForm.isMergeTube">
                    <el-radio :label="true">是</el-radio>
                    <el-radio :label="false">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </template>

          <template v-if="popupForm.checkCls == 2">
            <!-- 检查类型为功能检查时显示容 -->
            <el-divider content-position="left">检查部位</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item
                  label="检查部位"
                  :label-width="formLabelWidth"
                  prop="bwName"
                >
                  <el-input
                    v-model.trim="popupForm.bwName"
                    autocomplete="off"
                    size="small"
                    placeholder="请输入检查部位"
                    clearable
                  ></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item
                  label="检查部位大类"
                  :label-width="formLabelWidth"
                  prop="bwDlName"
                >
                  <el-input
                    v-model.trim="popupForm.bwDlName"
                    autocomplete="off"
                    size="small"
                    placeholder="请输入检查部位大类"
                    clearable
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </template>

          <el-divider content-position="left">体检标签</el-divider>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item
                label="是否打印体检标签"
                :label-width="formLabelWidth"
                prop="isPeLabelPrintable"
              >
                <el-radio-group v-model.trim="popupForm.isPeLabelPrintable">
                  <el-radio :label="true">是</el-radio>
                  <el-radio :label="false">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item
                label="是否合并体检标签"
                :label-width="formLabelWidth"
                prop="isMergePeLabel"
              >
                <el-radio-group v-model.trim="popupForm.isMergePeLabel">
                  <el-radio :label="true">是</el-radio>
                  <el-radio :label="false">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider content-position="left">微信预约</el-divider>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item
                label="微信允许加项标识"
                :label-width="formLabelWidth"
                prop="weChatAddAllow"
              >
                <el-select
                  v-model.trim="popupForm.weChatAddAllow"
                  placeholder="请选择微信允许加项标识"
                  style="width: 100%"
                  size="small"
                  clearable
                >
                  <el-option
                    v-for="item in optionsWeChatAddAllow"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item
                label="意义说明(备注)"
                :label-width="formLabelWidth"
                prop="note"
              >
                <el-input
                  type="textarea"
                  v-model.trim="popupForm.note"
                  autocomplete="off"
                  size="small"
                  placeholder="请输入意义说明(备注)"
                ></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item
                label="不适宜人群"
                :label-width="formLabelWidth"
                prop="unsuitableCrowd"
              >
                <el-input
                  v-model.trim="popupForm.unsuitableCrowd"
                  autocomplete="off"
                  size="small"
                  placeholder="请输入不适宜人群"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <template v-if="G_config.physicalMode.includes('职检')">
            <el-divider content-position="left">附加配置</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item
                  label="附加流程"
                  :label-width="formLabelWidth"
                  prop="combExtensions"
                >
                  <el-select
                    v-model="popupForm.combExtensions"
                    placeholder="根据组合使用情况选择"
                    style="width: 100%"
                    size="small"
                    filterable
                    clearable
                    multiple
                  >
                    <el-option
                      v-for="item in G_combExtension"
                      :key="item.value"
                      :label="item.label"
                      :value="Number(item.value)"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="12"> </el-col>
            </el-row>
          </template>
        </div>
      </el-form>
      <div class="dialog-footer">
        <span></span>
        <span class="btnSpan"
          ><el-button @click="handleClose" size="small">取消</el-button>
          <el-button class="blue_btn" @click="submit" size="small"
            >保存</el-button
          ></span
        >
      </div>
    </el-drawer>
    <!-- 对应详情抽屉 -->
    <el-drawer
      :title="drawerTitle"
      :visible.sync="drawers"
      :close-on-click-modal="false"
      :wrapperClosable="false"
      size="80%"
    >
      <DetailsDrawer
        :cancel="handleClose"
        :drawerInfo="drawerInfo"
        ref="drawer_ref"
      ></DetailsDrawer>
    </el-drawer>
    <el-drawer
      :title="hisdrawerTitle"
      :visible.sync="hisdrawers"
      :close-on-click-modal="false"
      :wrapperClosable="false"
      size="90%"
      v-if="hisdrawers"
    >
      <HisDetailsDrawer
        :cancel="hisHandleClose"
        :righthisdrawerTitle="righthisdrawerTitle"
        :rowInfo="rowInfo"
        ref="hisDrawer_ref"
      ></HisDetailsDrawer>
    </el-drawer>
  </div>
</template>

<script>
import { export2Excel } from '@/common/excelUtil';
import PublicTable from '../../../components/publicTable.vue';
import AllBtn from '../allBtn.vue';
import moment from 'moment';
import { mapGetters } from 'vuex';
import DetailsDrawer from '../otherCodeMapping/detailsDrawer.vue';
import HisDetailsDrawer from '../otherCodeMapping/hisDetailsDrawer.vue';
export default {
  name: 'barcodeClassification',
  components: { PublicTable, AllBtn, DetailsDrawer, HisDetailsDrawer },
  props: {
    clsCode: {
      type: String
    },
    claName: {
      type: String
    },
    //总传
    allInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      drawerTitle: '',
      drawerInfo: this.allInfo, //总传
      formInline: '',
      codeArr: [],
      resizeFlag: '',
      loading: false,
      disabled: false,
      tableDataCopy: [],
      tableData: [],
      dialogTitle: '',
      drawer: false,
      drawers: false,
      isAdd: true,
      formLabelWidth: '160px',
      options: [
        {
          value: true,
          label: '是'
        },
        {
          value: false,
          label: '否'
        }
      ],
      optionsDiscountAllow: [
        {
          value: true,
          label: '允许'
        },
        {
          value: false,
          label: '不允许'
        }
      ],
      optionsWeChatAddAllow: [
        {
          value: true,
          label: '允许'
        },
        {
          value: false,
          label: '不允许'
        }
      ],
      optionsReportShow: [
        {
          value: true,
          label: '是'
        },
        {
          value: false,
          label: '否'
        }
      ],
      optionsCheckLimit: [
        {
          value: '2',
          label: '餐前'
        },
        {
          value: '3',
          label: '餐后'
        },
        {
          value: '1',
          label: '无限制'
        }
      ],
      optionsExamDeptCode: [],
      optionsClsCode: [],
      optionsExamRoom: [],
      optionsFeeBasicCls: [],
      optionsBarcodeType: [],
      roleList: [],
      popupForm: {
        combCode: '',
        combName: '',
        sex: '',
        shortName: '',
        clsCode: '',
        feeBasicCls: '',
        checkCls: '',
        hint: '',
        note: '',
        price: 0,
        discountAllow: false,
        barCodeType: '',
        // barCodeCount: 0,
        checkLimit: '',
        examDeptCode: '',
        gatherPlace: '',
        reportShow: true,
        // hisCode: "",
        bwName: '',
        bwDlName: '',
        weChatAddAllow: false,
        unsuitableCrowd: '',
        attention: '',
        sortIndex: 0,
        isEnabled: true,
        pinYinCode: '',
        wuBiCode: '',
        isMergeTube: true,
        isMergePeLabel: false,
        isPeLabelPrintable: true,
        combExtensions: null, // 附加流程(系统)
        isDedicatePies: false // 医嘱-体检专用
      },
      theads: {
        combCode: '组合代码',
        combName: '组合名称',
        shortName: '组合简称',
        hisOrderCNName: '医嘱项目',
        clsCodeName: '项目分类',
        price: '单价',
        isEnabled: '启用标识'
      },
      cell_blue: ['combName'],
      columnWidth: {
        combName: 220,
        feeBasicCls: 180,
        note: 180,
        // barCodeCount: 160,
        hisOrderCNName: 200,
        reportShow: 160,
        bwDlName: 160,
        weChatAddAllow: 180,
        unsuitableCrowd: 180
      },
      enum_abnormity: {
        true: '允许',
        false: '不允许'
      },
      enum_abnormity1: {
        true: '是',
        false: '否'
      },
      enum_abnormity2: {
        true: '启用',
        false: '禁用'
      },
      enum_abnormity3: {
        1: '无限制',
        2: '餐前',
        3: '餐后'
      },
      enum_abnormity4: {
        '*': '一般检查',
        0: '医生检查',
        1: '功能检查',
        2: '检验检查'
      },
      rules: {
        combName: [
          { required: true, message: '请输入组合名称', trigger: 'blur' }
        ],
        clsCode: [
          { required: true, message: '请输入项目分类', trigger: 'blur' }
        ],
        sex: [{ required: true, message: '请选择性别', trigger: 'change' }],
        checkCls: [
          { required: true, message: '请选择检查类型', trigger: 'change' }
        ],
        examDeptCode: [
          { required: true, message: '请选择检查科室', trigger: 'change' }
        ],
        reportShow: [
          { required: true, message: '请选择报告中显示', trigger: 'change' }
        ],
        checkCls: [
          { required: true, message: '请选择检查类型', trigger: 'change' }
        ],
        checkLimit: [
          { required: true, message: '请选择检查限制', trigger: 'change' }
        ]
      },
      funTpye: '/Create',
      excelList: [],
      drawerTheads: {},
      righthisdrawerTitle: '',
      hisdrawers: false,
      hisdrawerTitle: '',
      rowInfo: {},
      optionsHisOrder: [],
      totalNumber: 0,
      curRow: {},
      curRowIdx: null,
      hisChargeListPage: {
        pageSize: 100, //每页数量 (0：查全部，>0：分页查)
        pageNumber: 1,
        orderItemCode: '',
        orderItemCNName: '',
        keyword: ''
      }
    };
  },
  watch: {
    clsCode() {
      this.tableData = [];
      this.tableDataCopy = [];
      this.getItemCls();
    }
  },
  created() {
    this.popupFormInit = JSON.parse(JSON.stringify(this.popupForm));
  },
  mounted() {
    this.getItemCls();
    //获取科室信息
    this.getCodeDepartment();
    //获取项目分类
    this.getCodeItemCls();
    //获取检查地点
    this.getCodeInspectPlace();
    // 获取基础分类信息
    this.getCodeBasicClsRead();
    //获取条码类型
    this.getCodeBarcodeType();
  },
  computed: {
    ...mapGetters([
      'G_checkCls',
      'G_checkLimit',
      'G_shareSexList',
      'G_combExtension',
      'G_config'
    ])
  },

  methods: {
    //对应详情
    viewDetails(row) {
      this.drawers = true;
      this.drawerTitle = row.combName + '-项目对应';
      this.drawerInfo.title2 = row.combName + '-项目对应列表';
      this.drawerInfo.setInfo = { combCode: row.combCode };
      this.drawerInfo.setInfos = { combCode: row.combCode };
      //执行获取下拉,左右表格数据
      this.$nextTick(() => {
        this.$refs.drawer_ref.getAll();
        this.$refs.drawer_ref.clearInput();
      });
    },
    handleClose() {
      this.drawer = false;
      this.drawers = false;
      this.$refs.ruleForm.resetFields();
    },
    //his对应详情`
    hisViewDetails(row) {
      this.hisdrawers = true;
      this.hisdrawerTitle = row.combName + '-收费项目对应';
      this.righthisdrawerTitle = row.combName + '-收费项目对应列表';
      this.rowInfo = row;
    },
    //his关闭
    hisHandleClose() {
      this.drawer = false;
      this.drawers = false;
      this.hisdrawers = false;
    },

    //查询
    search() {
      this.formInline = this.$refs.allBtn_Ref.searchInfo.trim(); //获取组件文本框的值
      if (this.formInline) {
        this.tableData = this.tableDataCopy.filter((item) =>
          (item.combCode + item.combName + item.shortName)
            .toLowerCase()
            .includes(this.formInline.toLowerCase())
        );
      } else {
        this.getItemCls();
      }
      //this.loading = false;
    },

    // 获取项目分类信息数据
    getItemCls() {
      this.loading = true;
      this.$ajax
        .post(this.$apiUrls.PageQuery_CodeItemComb, '', {
          query: {
            clsCode: this.clsCode
          }
        })
        .then((r) => {
          this.tableData = r.data.returnData;
          this.tableDataCopy = r.data.returnData;
          this.totalPage = r.data.totalPage;
          this.loading = false;
        });
    },
    //远程搜索医嘱项目
    searchHisOrderList(keyword) {
      this.hisChargeListPage = {
        pageNumber: 1,
        pageSize: 50,
        orderItemCode: '',
        orderItemCNName: '',
        keyword: keyword ? keyword : ''
      };
      this.optionsHisOrder = [];
      this.getCodeHisOrderItems(this.hisChargeListPage, true);
    },
    //获取分段医嘱项目下拉
    getCodeHisOrderItems(data, flag = true) {
      this.$ajax.post(this.$apiUrls.GetCodeHisOrderItems, data).then((r) => {
        //
        let { returnData, success } = r.data;
        if (!success) return;
        let data = [];
        this.totalNumber = returnData.totalNumber; //总数
        returnData.forEach((item) => {
          data.push({
            orderItemCNName: item.orderItemCNName,
            orderItemCode: item.orderItemCode
          });
        });
        this.optionsHisOrder = [...data];
        if (flag) {
          this.getOrderItemCNName();
        }
        //console.log("[ this.optionsHisOrder ]-1029", this.optionsHisOrder);
      });
    },
    //获取医嘱名称
    getOrderItemCNName() {
      let data = this.optionsHisOrder.filter(
        (item) => item.orderItemCode == this.popupForm.hisOrderCode
      );
      console.log('[ data ]-1036', data);
      this.popupForm.hisOrderCNName = data[0]?.orderItemCNName;
    },
    // 鼠标滚动加载
    loadMoreFun() {
      this.hisChargeListPage.pageNumber++;
      // 判断是否最后一页
      if (
        this.hisChargeListPage.pageNumber * this.hisChargeListPage.pageSize >
        this.totalNumber
      ) {
        this.hisChargeListPage = {
          pageSize: 100, //每页数量 (0：查全部，>0：分页查)
          pageNumber: 1,
          orderItemCode: '',
          orderItemCNName: '',
          keyword: ''
        };
        return;
      }
      this.getCodeHisOrderItems(this.hisChargeListPage, true);
    },
    //编辑
    handleClick(row) {
      this.curRowIdx = this.tableData.indexOf(row);
      this.hisChargeListPage = {
        pageSize: 100, //每页数量 (0：查全部，>0：分页查)
        pageNumber: 1,
        orderItemCode: '',
        orderItemCNName: '',
        keyword: ''
      };
      //获取医嘱项目
      this.getCodeHisOrderItems(this.hisChargeListPage, false);
      this.curRow = row;
      console.log('row', row);
      this.drawer = true;
      this.disabled = true;
      this.funTpye = '/Update';
      this.popupForm = {
        combCode: row.combCode,
        combName: row.combName,
        sex: row.sex,
        shortName: row.shortName,
        clsCode: row.clsCode?.trim() || '',
        feeBasicCls: row.feeBasicCls?.trim() || '',
        checkCls: row.checkCls,
        hint: row.hint,
        note: row.note,
        price: row.price,
        discountAllow: row.discountAllow,
        barCodeType: row.barCodeType,
        checkLimit: row.checkLimit,
        examDeptCode: row.examDeptCode?.trim() || '',
        gatherPlace: row.gatherPlace?.trim() || '',
        reportShow: row.reportShow,
        bwName: row.bwName,
        bwDlName: row.bwDlName,
        weChatAddAllow: row.weChatAddAllow,
        unsuitableCrowd: row.unsuitableCrowd,
        attention: row.attention,
        sortIndex: row.sortIndex,
        isEnabled: row.isEnabled,
        pinYinCode: row.pinYinCode?.trim(),
        wuBiCode: row.wuBiCode?.trim(),
        isMergeTube: row.isMergeTube,
        hisOrderCode: row.hisOrderCode, //医嘱项目
        hisOrderCNName: row.hisOrderCNName,
        isMergePeLabel: row.isMergePeLabel,
        isPeLabelPrintable: row.isPeLabelPrintable,
        combExtensions: row.combExtensions, // 附加流程(系统)
        isDedicatePies: row.isDedicatePies // 医嘱-体检专用
      };
      console.log(this.popupForm);
    },
    //新增
    newProject() {
      this.popupForm = JSON.parse(JSON.stringify(this.popupFormInit));
      this.drawer = true;
      this.funTpye = '/Create';
      this.disabled = false;
      this.hisChargeListPage = {
        pageSize: 100, //每页数量 (0：查全部，>0：分页查)
        pageNumber: 1,
        orderItemCode: '',
        orderItemCNName: '',
        keyword: ''
      };
      //获取医嘱项目
      this.getCodeHisOrderItems(this.hisChargeListPage);
      this.$nextTick(() => {
        this.$refs.ruleForm.clearValidate();
      });
    },
    //提交
    submit() {
      this.popupForm.price = Number(this.popupForm.price);
      if (this.popupForm.hisOrderCode == '') {
        this.popupForm.hisOrderCNName = '';
      }
      //console.log("[  ]-1072", this.popupForm);
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$ajax
            .post(this.$apiUrls.CU_CodeItemComb + this.funTpye, this.popupForm)
            .then((r) => {
              r = r.data;
              if (!r.success) {
                return;
              }
              this.drawer = false;
              if (this.funTpye == '/Create') {
                this.$message({
                  message: '新建成功',
                  type: 'success',
                  showClose: true
                });
              } else {
                this.$message({
                  message: '修改成功',
                  type: 'success',
                  showClose: true
                });
              }
              this.$nextTick(() => {
                // this.$refs.allBtn_Ref.searchInfo = "";
                // // this.search();
                let row = {
                  ...this.tableData[this.curRowIdx],
                  ...this.popupForm
                };
                this.$set(this.tableData, this.curRowIdx, row);
              });
            });
        } else {
          return false;
        }
      });
    },
    //复选框勾选操作
    handleSelectChangeTable(val) {
      //console.log(val);
      this.excelList = val;
      this.codeArr = [];
      val.map((item, i) => {
        if (item.combCode != '') {
          this.codeArr.push(item.combCode);
        }
      });
      //console.log(this.codeArr);
    },
    //多条删除
    delMore() {
      if (this.codeArr.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据!',
          type: 'warning'
        });
      }
      if (this.codeArr.length > 0) {
        this.$confirm('是否确认删除数据?　项目对应也将全部删除!', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            console.log(this.codeArr);
            this.$ajax
              .post(this.$apiUrls.Delete_CodeItemComb, this.codeArr)
              .then((r) => {
                let { success } = r.data;
                if (!success) return;
                this.$message({
                  showClose: true,
                  message: '删除成功',
                  type: 'success'
                });
                this.getItemCls();
              });
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    // 项目分类
    getCodeItemCls() {
      this.$ajax.post(this.$apiUrls.RD_CodeItemCls + '/Read', []).then((r) => {
        this.optionsClsCode = r.data.returnData;
      });
    },
    //获取科室信息
    getCodeDepartment() {
      this.$ajax
        .post(this.$apiUrls.RD_CodeDepartment + '/Read', [])
        .then((r) => {
          //
          this.optionsExamDeptCode = r.data.returnData;
        });
    },
    //获取检查地点
    getCodeInspectPlace() {
      this.$ajax
        .post(this.$apiUrls.RD_CodeGatherPlace + '/Read', [])
        .then((r) => {
          //
          this.optionsExamRoom = r.data.returnData;
        });
    },
    //获取基础分类信息
    getCodeBasicClsRead() {
      this.$ajax.post(`${this.$apiUrls.RD_CodeFeeCls}/Read`, []).then((r) => {
        //
        this.optionsFeeBasicCls = r.data.returnData;
      });
    },
    //获取条码类型
    getCodeBarcodeType() {
      this.$ajax
        .post(this.$apiUrls.RD_CodeBarcodeType + '/Read', [])
        .then((r) => {
          //
          this.optionsBarcodeType = r.data.returnData;
        });
    },
    // 导出excel
    exportTable() {
      if (this.excelList.length == 0) {
        this.$message.warning('请选择至少一条数据进行操作!');
        return;
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const columns = [
          {
            title: '序号',
            key: 'index'
          },
          {
            title: '组合代码',
            key: 'combCode'
          },
          {
            title: '组合名称',
            key: 'combName'
          },
          {
            title: '组合简称',
            key: 'shortName'
          },
          {
            title: '收费基础分类',
            key: 'feeBasicCls'
          },
          {
            title: '检查类型',
            key: 'checkCls'
          },
          {
            title: '提示信息',
            key: 'hint'
          },
          {
            title: '意义说明(备注)',
            key: 'note'
          },
          {
            title: '单价',
            key: 'price'
          },
          {
            title: '折扣标识',
            key: 'discountAllow'
          },
          {
            title: '条码类型',
            key: 'barCodeType'
          },
          {
            title: '检查限制',
            key: 'checkLimit'
          },
          {
            title: '检查科室',
            key: 'examDeptCode'
          },
          {
            title: '报告中显示',
            key: 'reportShow'
          },
          {
            title: '检查部位',
            key: 'bwName'
          },
          {
            title: '检查部位大类',
            key: 'bwDlName'
          },
          {
            title: '微信允许加项标识',
            key: 'weChatAddAllow'
          },
          {
            title: '不适宜人群',
            key: 'unsuitableCrowd'
          },
          {
            title: '注意事项',
            key: 'attention'
          },
          {
            title: '显示顺序',
            key: 'sortIndex'
          },
          {
            title: '启用标识',
            key: 'isEnabled'
          },
          {
            title: '拼音码',
            key: 'pinYinCode'
          },
          {
            title: '五笔码',
            key: 'wuBiCode'
          },
          {
            title: '是否合管',
            key: 'isMergeTube'
          },
          {
            title: '是否合并体检标签',
            key: 'isMergePeLabel'
          },
          {
            title: '是否体检标签打印',
            key: 'isPeLabelPrintable'
          }
        ];
        let excelListCopy = JSON.parse(JSON.stringify(this.excelList));
        excelListCopy.map((item, i) => {
          item.index = i + 1;
          item.reportShow = item.reportShow ? '是' : '否';
          item.discountAllow = item.discountAllow ? '允许' : '不允许';
          item.weChatAddAllow = item.weChatAddAllow ? '允许' : '不允许';
          item.isEnabled = item.isEnabled ? '启用' : '禁用';
          item.isMergeTube = item.isMergeTube ? '是' : '否';
          item.isMergePeLabel = item.isMergePeLabel ? '是' : '否';
          item.isPeLabelPrintable = item.isPeLabelPrintable ? '是' : '否';
        });
        const title = this.claName + moment().format('YYYY-MM-DD');
        this.$nextTick(() => {
          export2Excel(columns, excelListCopy, title);
        });
      });
    },
    print() {},

    /**
     * @author: justin
     * @description: 检查类型变动
     * @return {*}
     */
    checkClsChange() {
      if (this.popupForm.checkCls != 2) {
        // 非功能检查，置空检查部位
        this.popupForm.bwName = '';
        this.popupForm.bwDlName = '';
      }
      if (this.popupForm.checkCls != 3) {
        // 非检验检查，置空条码类型
        this.popupForm.barCodeType = '';
      }
    }
  }
};
</script>
<style lang="less" scoped>
.barcodeClassification {
  display: flex;
  flex-direction: column;
  font-weight: 600;
  width: 100%;
  height: 100%;
  header {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
  }
  .dialog-footer {
    position: absolute;
    bottom: 20px;
    z-index: 1;
    width: 100%;
    padding: 0 20px;

    display: flex;
    justify-content: space-between;
    margin-top: 40px;
    .titleSpan {
      color: red;
      margin-left: 50px;
    }
  }
  .el-container.is-vertical {
    height: 100%;
  }
  .el-header,
  .el-main {
    padding: 0;
  }

  .searchWrap .el-form-item:last-child {
    margin-right: 0;
    margin-left: 10px;
  }
  .indexPage .contentWrap .tabPage {
    margin-top: 0;
  }
  .el-pagination {
    text-align: center;
  }
  .el-drawer.rtl {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #2d3436;
    padding: 0 20px 0 10px;
  }
  .el-drawer__body {
    padding-right: 20px;

    .el-form-item {
      margin-bottom: 10px;
    }

    .order-item-code-box {
      /deep/.el-form-item__content {
        display: flex;
        align-items: center;
        justify-content: space-around;
      }
    }
  }
  /deep/.el-drawer__header {
    align-items: center;
    display: flex;
    background: rgba(23, 112, 223, 0.2);
    border-radius: 4px 4px 0 0;
    line-height: 42px;
    font-family: PingFangSC-Medium;
    font-size: 18px;
    padding: 0;
    padding-left: 20px;
    color: #2d3436;
    margin-bottom: 0;
  }
  /deep/.el-drawer__close-btn {
    font-size: 30px;
  }
  /deep/.el-drawer__body {
    flex: 1;

    .el-divider--horizontal {
      margin: 15px 0;
      .el-divider__text {
        color: #b0b6c0;
      }
    }
  }
  /deep/.el-input.is-disabled .el-input__inner {
    color: #000;
  }
}
</style>
<style lang="less">
.el-dialog {
  width: 480px;
}
.el-table .el-table__cell {
  padding: 5px 0;
}
.el-main .el-checkbox__inner {
  width: 20px;
  height: 20px;
}
.el-drawer__body {
  padding-right: 20px;
}
.el-drawer__header,
.el-form-item__label {
  color: #2d3436;
}
</style>
