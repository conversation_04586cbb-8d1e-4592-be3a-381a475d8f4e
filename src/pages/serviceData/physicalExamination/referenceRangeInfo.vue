<template>
  <div class="referenceRangeInfo">
    <!-- 参考范围类型信息 -->
    <el-container>
      <el-header>
        <AllBtn
          :btnList="['查询', '新建', '删除', '打印', '导出']"
          :methodCreat="newProject"
          :methodDelete="delMore"
          :methodSearch="search"
          :methodPrint="print"
          :methodExport="exportTable"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          @rowDblclick="handleClick"
          :cell_blue="cell_blue"
          @selectionChange="handleSelectChangeTable"
          :tableLoading.sync="loading"
          isCheck
        >
          <template #sex="{ scope }">
            {{ G_EnumList['Sex'][scope.row.sex] }}
          </template>
        </PublicTable>
      </el-main>
    </el-container>
    <el-drawer
      title="参考范围信息属性"
      :visible.sync="drawer"
      :before-close="handleClose"
      :wrapperClosable="false"
    >
      <el-form :model="popupForm" ref="ruleForm" :rules="rules">
        <el-form-item
          label="参考范围代码"
          :label-width="formLabelWidth"
          prop="boundTypeCode"
        >
          <el-input
            v-model.trim="popupForm.boundTypeCode"
            autocomplete="off"
            size="small"
            :disabled="disabled"
            placeholder="请输入参考范围代码"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          label="参考范围名称"
          :label-width="formLabelWidth"
          prop="boundTypeName"
        >
          <el-input
            v-model.trim="popupForm.boundTypeName"
            autocomplete="off"
            size="small"
            placeholder="请输入参考范围名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="性别" :label-width="formLabelWidth" prop="sex">
          <el-select
            v-model.trim="popupForm.sex"
            placeholder="请选择性别"
            size="small"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="item in G_shareSexList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="年龄上限"
          :label-width="formLabelWidth"
          prop="upperAgeLimit"
        >
          <el-input
            v-model.trim="popupForm.upperAgeLimit"
            autocomplete="off"
            size="small"
            placeholder="请输入年龄上限(120)"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          label="年龄下限"
          :label-width="formLabelWidth"
          prop="lowerAgeLimit"
        >
          <el-input
            v-model.trim="popupForm.lowerAgeLimit"
            autocomplete="off"
            size="small"
            placeholder="请输入年龄下限(0)"
            clearable
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="handleClose" size="small">取消</el-button>
        <el-button class="blue_btn" @click="submit" size="small"
          >保存</el-button
        >
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { export2Excel } from '@/common/excelUtil';
import moment from 'moment';
import PublicTable from '../../../components/publicTable.vue';
import AllBtn from '../allBtn.vue';
import { mapGetters } from 'vuex';
export default {
  name: 'referenceRangeInfo',
  components: { PublicTable, AllBtn },
  data() {
    return {
      formInline: '',
      codeArr: [],
      resizeFlag: '',
      loading: false,
      disabled: false,
      options: [
        {
          value: '0',
          label: '通用'
        },
        {
          value: '1',
          label: '男'
        },
        {
          value: '2',
          label: '女'
        }
      ],
      tableDataCopy: [],
      tableData: [],
      pageSize: 50,
      currentPage: 1,
      dialogTitle: '',
      drawer: false,
      isAdd: true,
      formLabelWidth: '120px',
      roleList: [],
      popupForm: {
        boundTypeCode: '',
        boundTypeName: '',
        sex: '',
        upperAgeLimit: '',
        lowerAgeLimit: ''
      },
      theads: {
        boundTypeCode: '参考范围代码',
        boundTypeName: '参考范围名称',
        sex: '性别',
        lowerAgeLimit: '年龄下限',
        upperAgeLimit: '年龄上限'
      },
      cell_blue: ['boundTypeName'],
      rules: {
        boundTypeCode: [
          { required: true, message: '请输入参考范围代码', trigger: 'blur' }
        ],
        boundTypeName: [
          { required: true, message: '请输入参考范围名称', trigger: 'blur' }
        ],
        sex: [{ required: true, message: '请输入性别', trigger: 'blur' }],
        upperAgeLimit: [
          { required: true, message: '请输入年龄上限', trigger: 'blur' }
        ],
        lowerAgeLimit: [
          { required: true, message: '请输入年龄下限', trigger: 'blur' }
        ]
      },
      funTpye: '/Create',
      excelList: []
    };
  },
  created: function () {},
  mounted: function () {
    this.getItemCls();
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_shareSexList'])
  },
  methods: {
    handleClose() {
      this.drawer = false;
      this.$refs.ruleForm.resetFields();
    },
    //查询
    search() {
      this.formInline = this.$refs.allBtn_Ref.searchInfo.trim(); //获取组件文本框的值
      if (this.formInline.length > 0) {
        this.tableData = this.tableDataCopy.filter((item) => {
          return (
            item.boundTypeCode.indexOf(this.formInline) !== -1 ||
            item.boundTypeName.indexOf(this.formInline) !== -1
          );
        });
      } else {
        this.getItemCls();
      }
      //this.loading = false;
    },

    //新增
    newProject() {
      this.drawer = true;
      this.funTpye = '/Create';
      this.disabled = false;
      this.popupForm = {
        boundTypeCode: '',
        boundTypeName: '',
        sex: '',
        upperAgeLimit: 99 + '',
        lowerAgeLimit: 0 + ''
      };
    },

    //编辑
    handleClick(row) {
      console.log('row', row);
      this.drawer = true;
      this.funTpye = '/Update';
      this.disabled = true;
      this.popupForm = {
        boundTypeCode: row.boundTypeCode,
        boundTypeName: row.boundTypeName,
        sex: row.sex,
        upperAgeLimit: row.upperAgeLimit,
        lowerAgeLimit: row.lowerAgeLimit
      };
    },

    // 获取项目分类信息数据
    getItemCls() {
      console.log(this.$apiUrls);
      this.loading = true;
      this.$ajax.post(this.$apiUrls.RD_CodeBound + '/Read', []).then((r) => {
        this.tableDataCopy = r.data.returnData;
        this.tableData = this.tableDataCopy.filter((item) => {
          return (
            item.boundTypeCode.indexOf(this.formInline) !== -1 ||
            item.boundTypeName.indexOf(this.formInline) !== -1
          );
        });
        this.loading = false;
      });
    },
    //提交
    submit() {
      console.log(this.popupForm);
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$ajax
            .post(this.$apiUrls.CU_CodeBound + this.funTpye, this.popupForm)
            .then((r) => {
              r = r.data;
              if (!r.success) {
                return;
              }
              this.tableData = [];
              this.drawer = false;
              this.$nextTick(() => {
                // this.$refs.allBtn_Ref.searchInfo = "";
                this.getItemCls();
              });
              if (this.funTpye == '/Create') {
                this.$message({
                  message: '新建成功',
                  type: 'success',
                  showClose: true
                });
              } else {
                this.$message({
                  message: '修改成功',
                  type: 'success',
                  showClose: true
                });
              }
            });
        } else {
          return false;
        }
      });
    },
    //复选框勾选操作
    handleSelectChangeTable: function (val) {
      //console.log(val);
      this.excelList = val;
      this.codeArr = [];
      val.map((item, i) => {
        if (item.boundTypeCode != '') {
          this.codeArr.push(item.boundTypeCode);
        }
      });
      //console.log(this.codeArr);
    },
    //多条删除
    delMore() {
      if (this.codeArr.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据!',
          type: 'warning'
        });
      }
      if (this.codeArr.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.$ajax
              .post(this.$apiUrls.RD_CodeBound + '/Delete', this.codeArr)
              .then((r) => {
                let { success } = r.data;
                if (!success) return;
                this.$message({
                  showClose: true,
                  message: '删除成功',
                  type: 'success'
                });
                this.getItemCls();
              });
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    // 导出excel
    exportTable() {
      if (this.excelList.length == 0) {
        this.$message.warning('请选择至少一条数据进行操作!');
        return;
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const columns = [
          {
            title: '参考范围代码',
            key: 'boundTypeCode'
          },
          {
            title: '参考范围名称',
            key: 'boundTypeName'
          },
          {
            title: '性别',
            key: 'sex'
          },
          {
            title: '年龄上限',
            key: 'upperAgeLimit'
          },
          {
            title: '年龄下限',
            key: 'lowerAgeLimit'
          }
        ];
        this.excelList.map((item, i) => {
          item.index = i + 1;
        });
        const title = '参考范围类型信息' + moment().format('YYYY-MM-DD');
        this.$nextTick(() => {
          export2Excel(columns, this.excelList, title);
        });
      });
    },
    print() {}
  }
};
</script>
<style lang="less" scoped>
.referenceRangeInfo {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  font-weight: 600;
  header {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
  }
  /deep/.el-drawer__header,
  /deep/.el-form-item__label {
    color: #2d3436;
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-container.is-vertical {
    height: 100%;
  }
  .el-header,
  .el-main {
    padding: 0;
  }

  .searchWrap .el-form-item:last-child {
    margin-right: 0;
    margin-left: 10px;
  }
  .indexPage .contentWrap .tabPage {
    margin-top: 0;
  }
  .el-pagirange {
    text-align: center;
  }
  .el-drawer.rtl {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #2d3436;
    padding: 0 20px 0 10px;
  }
  .el-drawer__body {
    padding-right: 20px;

    .el-form-item {
      margin-bottom: 10px;
      .el-select {
        width: 100%;
      }
    }
  }
}
</style>
<style>
.el-table .el-table__cell {
  padding: 5px 0;
}
.el-main .el-checkbox__inner {
  width: 20px;
  height: 20px;
}
.el-drawer__body {
  padding-right: 20px;
}
</style>
