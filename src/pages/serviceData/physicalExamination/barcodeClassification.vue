<template>
  <div class="barcodeClassification">
    <!-- 条码分类信息 -->
    <el-container>
      <el-header>
        <AllBtn
          :btnList="['查询', '新建', '删除', '打印', '导出']"
          :methodCreat="newProject"
          :methodDelete="delMore"
          :methodSearch="search"
          :methodPrint="print"
          :methodExport="exportTable"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          @rowDblclick="handleClick"
          :cell_blue="cell_blue"
          @selectionChange="handleSelectChangeTable"
          :tableLoading.sync="loading"
          isCheck
          :columnWidth="columnWidth"
        >
          <template #color="{ scope }">
            <span
              class="iconfont icon-yanse"
              :style="`color:${scope.row.color}`"
            >
            </span>
          </template>
        </PublicTable>
      </el-main>
    </el-container>
    <el-drawer
      title="条码分类信息属性"
      :visible.sync="drawer"
      :before-close="handleClose"
      :wrapperClosable="false"
    >
      <el-form :model="popupForm" ref="ruleForm" :rules="rules">
        <el-form-item
          label="条码分类代码"
          :label-width="formLabelWidth"
          prop="barcode"
        >
          <el-input
            v-model.trim="popupForm.barcode"
            autocomplete="off"
            size="small"
            :disabled="disabled"
            placeholder="请输入条码分类代码"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          label="条码分类名称"
          :label-width="formLabelWidth"
          prop="barcodeName"
        >
          <el-input
            v-model.trim="popupForm.barcodeName"
            autocomplete="off"
            size="small"
            placeholder="请输入条码分类名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="试管" :label-width="formLabelWidth" prop="note">
          <el-input
            v-model.trim="popupForm.note"
            autocomplete="off"
            size="small"
            placeholder="请输入试管"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          label="打印次数"
          :label-width="formLabelWidth"
          prop="printTimes"
        >
          <el-input
            type="number"
            v-model.trim="popupForm.printTimes"
            autocomplete="off"
            size="small"
            placeholder="请输入打印次数"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          label="试管材料费用组合代码"
          :label-width="formLabelWidth"
          prop="feeCombCode"
        >
          <el-input
            v-model.trim="popupForm.feeCombCode"
            autocomplete="off"
            size="small"
            placeholder="请输入试管材料费用组合代码"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          label="颜色"
          :label-width="formLabelWidth"
          prop="colorName"
        >
          <!-- <el-select v-model.trim="popupForm.color" placeholder="请选择">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option> </el-select
          > -->
          <div class="color-form">
            <el-input
              v-model.trim="popupForm.colorName"
              autocomplete="off"
              size="small"
              style="width: 80%"
              placeholder="请输入颜色名"
              clearable
            ></el-input>
            <el-color-picker
              v-model.trim="popupForm.color"
              size="small"
            ></el-color-picker>
          </div>
        </el-form-item>

        <el-form-item
          label="序号前缀"
          :label-width="formLabelWidth"
          prop="preText"
        >
          <el-input
            v-model.trim="popupForm.preText"
            autocomplete="off"
            size="small"
            placeholder="请输入序号前缀"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          label="条码前缀"
          :label-width="formLabelWidth"
          prop="preNum"
        >
          <el-input
            v-model.trim="popupForm.preNum"
            autocomplete="off"
            size="small"
            placeholder="请输入条码前缀"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          label="标本类型"
          :label-width="formLabelWidth"
          prop="sampCode"
        >
          <el-select
            class="select"
            v-model.trim="popupForm.sampCode"
            placeholder="请选择标本类型"
            size="small"
            style="width: 100%"
            filterable
            clearable
          >
            <el-option
              v-for="item in sampList"
              :key="item.sampCode"
              :label="item.sampName"
              :value="item.sampCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="注意事项"
          :label-width="formLabelWidth"
          prop="attention"
        >
          <el-input
            type="textarea"
            v-model.trim="popupForm.attention"
            autocomplete="off"
            size="small"
            placeholder="请输入注意事项"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="handleClose" size="small">取消</el-button>
        <el-button class="blue_btn" @click="submit" size="small"
          >保存</el-button
        >
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { export2Excel } from '@/common/excelUtil';
import moment from 'moment';
import PublicTable from '../../../components/publicTable.vue';
import AllBtn from '../allBtn.vue';
export default {
  name: 'barcodeClassification',
  components: { PublicTable, AllBtn },
  data() {
    return {
      formInline: '',
      codeArr: [],
      resizeFlag: '',
      loading: false,
      disabled: false,
      tableDataCopy: [],
      tableData: [],
      pageSize: 50,
      currentPage: 1,
      dialogTitle: '',
      drawer: false,
      isAdd: true,
      formLabelWidth: '160px',
      options: [
        {
          value: '0',
          label: '红色'
        },
        {
          value: '1',
          label: '紫色'
        },
        {
          value: '2',
          label: '蓝色'
        }
      ],
      roleList: [],
      popupForm: {
        barcode: '',
        barcodeName: '',
        note: '',
        printTimes: '',
        feeCombCode: '',
        color: '',
        colorName: '',
        sampCode: '',
        attention: '',
        preText: '',
        preNum: ''
      },
      theads: {
        barcode: '条码分类代码',
        barcodeName: '条码分类名称',
        note: '试管',
        printTimes: '打印次数',
        feeCombCode: '试管材料费用组合代码',
        color: '颜色',
        colorName: '颜色名',
        preText: '序号前缀'
      },
      cell_blue: ['barcodeName'],
      columnWidth: {
        barcode: 120,
        barcodeName: 160,
        note: 200,
        feeCombCode: 200
      }, //设置宽度
      rules: {
        barcode: [
          { required: true, message: '请输入条码分类代码', trigger: 'blur' }
        ],
        barcodeName: [
          { required: true, message: '请输入条码分类名称', trigger: 'blur' }
        ],
        printTimes: [
          { required: true, message: '请输入打印次数', trigger: 'blur' }
        ]
      },
      funTpye: '/Create',
      excelList: [],
      sampList: []
    };
  },
  created: function () {},
  mounted: function () {
    this.getItemCls();
    this.getSamp();
  },
  methods: {
    handleClose() {
      this.drawer = false;
      this.$refs.ruleForm.resetFields();
    },
    //查询
    search() {
      this.formInline = this.$refs.allBtn_Ref.searchInfo.trim(); //获取组件文本框的值
      if (this.formInline.length > 0) {
        this.tableData = this.tableDataCopy.filter((item) => {
          return (
            item.barcode.indexOf(this.formInline) !== -1 ||
            item.barcodeName.indexOf(this.formInline) !== -1
          );
        });
      } else {
        this.getItemCls();
      }
      //this.loading = false;
    },

    // 获取项目分类信息数据
    getItemCls() {
      console.log(this.$apiUrls);
      this.loading = true;
      this.$ajax
        .post(this.$apiUrls.RD_CodeBarcodeType + '/Read', [])
        .then((r) => {
          // this.tableData = r.data.returnData;
          this.tableDataCopy = r.data.returnData;
          this.tableData = this.tableDataCopy.filter((item) => {
            return (
              item.barcode.indexOf(this.formInline) !== -1 ||
              item.barcodeName.indexOf(this.formInline) !== -1
            );
          });
          this.loading = false;
        });
    },
    //编辑
    handleClick(row) {
      console.log('row', row);
      this.drawer = true;
      this.disabled = true;
      this.funTpye = '/Update';
      this.popupForm = {
        barcode: row.barcode,
        barcodeName: row.barcodeName,
        note: row.note,
        printTimes: row.printTimes,
        feeCombCode: row.feeCombCode,
        color: row.color,
        colorName: row.colorName,
        sampCode: row.sampCode,
        attention: row.attention,
        preText: row.preText,
        preNum: row.preNum
      };
    },
    //新增
    newProject() {
      this.popupForm = {
        barcode: '',
        barcodeName: '',
        note: '',
        printTimes: '',
        feeCombCode: '',
        color: '#FF2200',
        sampCode: '',
        attention: '',
        preText: '',
        preNum: '',
        colorName: ''
      };
      this.drawer = true;
      this.funTpye = '/Create';
      this.disabled = false;
    },
    //提交
    submit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$ajax
            .post(
              this.$apiUrls.CU_CodeBarcodeType + this.funTpye,
              this.popupForm
            )
            .then((r) => {
              r = r.data;
              if (!r.success) {
                return;
              }
              this.tableData = [];
              this.drawer = false;
              this.$nextTick(() => {
                // this.$refs.allBtn_Ref.searchInfo = "";
                this.getItemCls();
              });
              if (this.funTpye == '/Create') {
                this.$message({
                  message: '新建成功',
                  type: 'success',
                  showClose: true
                });
              } else {
                this.$message({
                  message: '修改成功',
                  type: 'success',
                  showClose: true
                });
              }
            });
        } else {
          return false;
        }
      });
    },
    //复选框勾选操作
    handleSelectChangeTable: function (val) {
      //console.log(val);
      this.excelList = val;
      this.codeArr = [];
      val.map((item, i) => {
        if (item.barcode != '') {
          this.codeArr.push(item.barcode);
        }
      });
      //console.log(this.codeArr);
    },
    //多条删除
    delMore() {
      if (this.codeArr.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据!',
          type: 'warning'
        });
      }
      if (this.codeArr.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            console.log(this.codeArr);
            this.$ajax
              .post(this.$apiUrls.RD_CodeBarcodeType + '/Delete', this.codeArr)
              .then((r) => {
                let { success } = r.data;
                if (!success) return;
                this.$message({
                  showClose: true,
                  message: '删除成功',
                  type: 'success'
                });
                this.getItemCls();
              });
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    // 获取标本类型
    getSamp() {
      this.$ajax.post(this.$apiUrls.RD_CodeSample + '/Read', []).then((r) => {
        let { returnData, success } = r.data;
        if (!success) return;
        this.sampList = returnData;
      });
    },
    // 导出excel
    exportTable() {
      if (this.excelList.length == 0) {
        this.$message.warning('请选择至少一条数据进行操作!');
        return;
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const columns = [
          {
            title: '序号',
            key: 'index'
          },
          {
            title: '条码分类代码',
            key: 'barcode'
          },
          {
            title: '条码分类名称',
            key: 'barcodeName'
          },
          {
            title: '试管',
            key: 'note'
          },
          {
            title: '打印次数',
            key: 'printTimes'
          },
          {
            title: '试管材料费用组合代码',
            key: 'feeCombCode'
          },
          {
            title: '序号前缀',
            key: 'color'
          },
          {
            title: '颜色名',
            key: 'colorName'
          },
          {
            title: '颜色名',
            key: 'preText'
          }
        ];
        this.excelList.map((item, i) => {
          item.index = i + 1;
        });
        const title = '条码分类信息' + moment().format('YYYY-MM-DD');
        this.$nextTick(() => {
          export2Excel(columns, this.excelList, title);
        });
      });
    },
    print() {}
  }
};
</script>
<style lang="less" scoped>
.barcodeClassification {
  display: flex;
  flex-direction: column;
  font-weight: 600;
  width: 100%;
  height: 100%;
  header {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-container.is-vertical {
    height: 100%;
  }
  .el-header,
  .el-main {
    padding: 0;
  }
  .color-form {
    display: flex;
    align-items: center;
  }
  .searchWrap .el-form-item:last-child {
    margin-right: 0;
    margin-left: 10px;
  }
  .indexPage .contentWrap .tabPage {
    margin-top: 0;
  }
  .el-pagination {
    text-align: center;
  }
  .el-drawer.rtl {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #2d3436;
    padding: 0 20px 0 10px;
  }
  .el-drawer__body {
    padding-right: 20px;

    .el-form-item {
      margin-bottom: 10px;
    }
  }
}
</style>
<style>
.el-dialog {
  width: 480px;
}
.el-table .el-table__cell {
  padding: 5px 0;
}
.el-main .el-checkbox__inner {
  width: 20px;
  height: 20px;
}
.el-drawer__body {
  padding-right: 20px;
}
.el-drawer__header,
.el-form-item__label {
  color: #2d3436;
}
</style>
