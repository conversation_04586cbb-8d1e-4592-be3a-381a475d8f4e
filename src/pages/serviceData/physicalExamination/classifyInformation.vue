<script lang="ts">
import { export2Excel } from '@/common/excelUtil';
import moment from 'moment';
import PublicTable from '../../../components/publicTable.vue';
import AllBtn from '../allBtn.vue';
import { ajax as $ajax } from '@/common/ajax.js';
import { apiUrls as $apiUrls } from '@/common/apiUrls.js';
import Vue from 'vue';
// 基础代码->体检类代码->项目分类信息
export default Vue.extend({
  name: 'classifyInformation',
  components: { PublicTable, AllBtn },
  data() {
    return {
      formInline: '',
      codeArr: [],
      resizeFlag: '',
      loading: false,
      disabled: false,
      tableDataCopy: [],
      tableData: [],
      pageSize: 50,
      currentPage: 1,
      dialogTitle: '',
      drawer: false,
      isAdd: true,
      formLabelWidth: '170px',
      roleList: [],
      popupForm: {
        clsCode: '',
        clsName: '',
        sortIndex: '',
        peLabelPrintTimes: '',
        pinYinCode: '',
        wuBiCode: '',
        isPrintOnComb: false,
        combPrintTimes: 0,
        clsPrintTimes: 0,
        clsLabelAlias: null,
        combLabelAlias: null
      },
      theads: {
        clsCode: '分类代码',
        clsName: '分类名称',
        sortIndex: '显示顺序',
        pinYinCode: '五笔码',
        wuBiCode: '拼音码',
        combPrintTimes: '按组合打印次数',
        combLabelAlias: '合并标签备注',
        clsPrintTimes: '按分类打印次数',
        clsLabelAlias: '分类标签备注'
      },
      cell_blue: ['clsName'],
      rules: {
        clsCode: [
          { required: true, message: '请输入分类代码', trigger: 'blur' }
        ],
        clsName: [
          { required: true, message: '请输入分类名称', trigger: 'blur' }
        ],
        sortIndex: [
          { required: true, message: '请输入显示顺序', trigger: 'blur' }
        ],
        applicationOrder: [
          { required: true, message: '请输入申请单顺序', trigger: 'blur' }
        ]
      },
      funTpye: '/Create',
      excelList: []
    };
  },
  created: function () {
    this.popupFormInit = JSON.parse(JSON.stringify(this.popupForm));
  },
  mounted: function () {
    this.getItemCls();
  },
  methods: {
    handleClose() {
      this.drawer = false;
      this.$refs.ruleForm.resetFields();
    },
    //查询
    search() {
      this.formInline = this.$refs.allBtn_Ref.searchInfo.trim(); //获取组件文本框的值
      if (this.formInline.length > 0) {
        this.tableData = this.tableDataCopy.filter((item) => {
          return (
            item.clsCode.indexOf(this.formInline) !== -1 ||
            item.clsName.indexOf(this.formInline) !== -1 ||
            item.pinYinCode.indexOf(this.formInline) !== -1 ||
            item.wuBiCode.indexOf(this.formInline) !== -1
          );
        });
      } else {
        this.getItemCls();
      }
      //this.loading = false;
    },

    //编辑
    handleClick(row, column, event) {
      console.log('row', row);
      this.drawer = true;
      this.disabled = true;
      this.funTpye = '/Update';
      this.popupForm = JSON.parse(JSON.stringify(row));
    },
    //新增
    newProject() {
      this.popupForm = JSON.parse(JSON.stringify(this.popupFormInit));
      this.drawer = true;
      this.funTpye = '/Create';
      this.disabled = false;
    },
    // 获取项目分类信息数据
    getItemCls() {
      this.loading = true;
      $ajax.post($apiUrls.RD_CodeItemCls + '/Read', []).then((r) => {
        this.tableDataCopy = r.data.returnData;
        this.tableData = this.tableDataCopy.filter((item) => {
          return (
            item.clsCode.indexOf(this.formInline) !== -1 ||
            item.clsName.indexOf(this.formInline) !== -1 ||
            item.pinYinCode.indexOf(this.formInline) !== -1 ||
            item.wuBiCode.indexOf(this.formInline) !== -1
          );
        });
        this.loading = false;
      });
    },
    //提交
    submit() {
      console.log(this.popupForm);
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          $ajax
            .post($apiUrls.CU_CodeItemCls + this.funTpye, this.popupForm)
            .then((r) => {
              r = r.data;
              if (!r.success) {
                return;
              }
              this.tableData = [];
              this.drawer = false;
              this.$nextTick(() => {
                // this.$refs.allBtn_Ref.searchInfo = "";
                this.getItemCls();
              });
              if (this.funTpye == '/Create') {
                this.$message({
                  message: '新建成功',
                  type: 'success',
                  showClose: true
                });
              } else {
                this.$message({
                  message: '修改成功',
                  type: 'success',
                  showClose: true
                });
              }
            });
        } else {
          return false;
        }
      });
    },
    //复选框勾选操作
    handleSelectChangeTable: function (val) {
      //console.log(val);
      this.excelList = val;
      this.codeArr = [];
      val.map((item, i) => {
        if (item.clsCode != '') {
          this.codeArr.push(item.clsCode);
        }
      });
      //console.log(this.codeArr);
    },
    //多条删除
    delMore() {
      if (this.codeArr.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据!',
          type: 'warning'
        });
      }
      if (this.codeArr.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            // this.delete(this.codeArr);
            console.log(this.codeArr);
            $ajax
              .post($apiUrls.RD_CodeItemCls + '/Delete', this.codeArr)
              .then((r) => {
                let { success } = r.data;
                if (!success) return;
                this.$message({
                  showClose: true,
                  message: '删除成功',
                  type: 'success'
                });
                this.getItemCls();
              });
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    // 导出excel
    exportTable() {
      if (this.excelList.length == 0) {
        this.$message.warning('请选择至少一条数据进行操作!');
        return;
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const columns = [
          {
            title: '序号',
            key: 'index'
          },
          {
            title: '分类代码',
            key: 'clsCode'
          },
          {
            title: '分类名称',
            key: 'clsName'
          },
          {
            title: '显示顺序',
            key: 'sortIndex'
          },
          {
            title: '拼音码',
            key: 'pinYinCode'
          },
          {
            title: '五笔码',
            key: 'wuBiCode'
          },
          {
            title: '按组合打印次数',
            key: 'combPrintTimes'
          },
          {
            title: '合并标签备注',
            key: 'combLabelAlias'
          },
          {
            title: '按分类打印次数',
            key: 'clsPrintTimes'
          },
          {
            title: '分类标签备注',
            key: 'clsLabelAlias'
          }
        ];
        this.excelList.map((item, i) => {
          item.index = i + 1;
        });
        const title = '项目分类信息' + moment().format('YYYY-MM-DD');
        this.$nextTick(() => {
          export2Excel(columns, this.excelList, title);
        });
      });
    },
    print() {}
  }
});
</script>

<template>
  <div class="classifyInformation">
    <!-- 项目分类信息 -->
    <el-container>
      <el-header>
        <AllBtn
          :btnList="['查询', '新建', '删除', '打印', '导出']"
          :methodCreat="newProject"
          :methodDelete="delMore"
          :methodSearch="search"
          :methodPrint="print"
          :methodExport="exportTable"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          @rowDblclick="handleClick"
          :cell_blue="cell_blue"
          @selectionChange="handleSelectChangeTable"
          :tableLoading.sync="loading"
          isCheck
        >
        </PublicTable>
      </el-main>
    </el-container>
    <el-drawer
      title="项目分类信息属性"
      :visible.sync="drawer"
      :before-close="handleClose"
      :wrapperClosable="false"
      size="35%"
    >
      <el-form :model="popupForm" ref="ruleForm" :rules="rules">
        <el-form-item
          label="分类代码"
          :label-width="formLabelWidth"
          prop="clsCode"
        >
          <el-input
            v-model.trim="popupForm.clsCode"
            autocomplete="off"
            size="small"
            :disabled="disabled"
            placeholder="请输入分类代码"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          label="分类名称"
          :label-width="formLabelWidth"
          prop="clsName"
        >
          <el-input
            v-model.trim="popupForm.clsName"
            autocomplete="off"
            size="small"
            placeholder="请输入分类名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          label="显示顺序"
          :label-width="formLabelWidth"
          prop="sortIndex"
        >
          <el-input
            v-model.trim="popupForm.sortIndex"
            autocomplete="off"
            size="small"
            placeholder="请输入显示顺序"
            clearable
          ></el-input>
        </el-form-item>

        <el-form-item
          label="拼音码"
          :label-width="formLabelWidth"
          prop="pinYinCode"
        >
          <el-input
            v-model.trim="popupForm.pinYinCode"
            autocomplete="off"
            size="small"
            placeholder="请输入拼音码"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          label="五笔码"
          :label-width="formLabelWidth"
          prop="wuBiCode"
        >
          <el-input
            v-model.trim="popupForm.wuBiCode"
            autocomplete="off"
            size="small"
            placeholder="请输入五笔码"
            clearable
          ></el-input>
        </el-form-item>

        <el-row align="center" :gutter="20" type="flex">
          <el-col :span="12">
            <el-form-item
              label="按组合打印次数"
              :label-width="formLabelWidth"
              prop="combPrintTimes"
            >
              <el-input-number
                v-model="popupForm.combPrintTimes"
                controls-position="right"
                :min="0"
                :precision="0"
                placeholder="请输入按组合打印次数"
                size="small"
              ></el-input-number>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item
              label="合并标签备注"
              label-width="100px"
              prop="combLabelAlias"
            >
              <el-input
                v-model.trim="popupForm.combLabelAlias"
                autocomplete="off"
                size="small"
                placeholder="请输入合并标签备注"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row align="center" :gutter="20" type="flex">
          <el-col :span="12">
            <el-form-item
              label="按分类打印次数"
              :label-width="formLabelWidth"
              prop="clsPrintTimes"
            >
              <el-input-number
                v-model="popupForm.clsPrintTimes"
                controls-position="right"
                :min="0"
                :precision="0"
                placeholder="请输入按分类打印次数"
                size="small"
              ></el-input-number>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item
              label="分类标签备注"
              label-width="100px"
              prop="clsLabelAlias"
            >
              <el-input
                v-model.trim="popupForm.clsLabelAlias"
                autocomplete="off"
                size="small"
                placeholder="请输入分类标签备注"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="handleClose" size="small">取消</el-button>
        <el-button class="blue_btn" @click="submit" size="small"
          >保存</el-button
        >
      </div>
    </el-drawer>
  </div>
</template>

<style lang="less" scoped>
.classifyInformation {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  font-weight: 600;
  header {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-container.is-vertical {
    height: 100%;
  }
  .el-header,
  .el-main {
    padding: 0;
  }

  .searchWrap .el-form-item:last-child {
    margin-right: 0;
    margin-left: 10px;
  }
  .indexPage .contentWrap .tabPage {
    margin-top: 0;
  }
  .el-pagination {
    text-align: center;
  }
  .el-drawer.rtl {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #2d3436;
    padding: 0 20px 0 10px;
  }
  .el-drawer__body {
    padding-right: 20px;

    .el-form-item {
      margin-bottom: 10px;
    }

    .el-input-number {
      width: 100%;
    }
  }
}
</style>
<style>
.el-dialog {
  width: 480px;
}
.el-table .el-table__cell {
  padding: 5px 0;
}
.el-main .el-checkbox__inner {
  width: 20px;
  height: 20px;
}
.el-drawer__body {
  padding-right: 20px;
}
.el-drawer__header,
.el-form-item__label {
  color: #2d3436;
}
</style>
