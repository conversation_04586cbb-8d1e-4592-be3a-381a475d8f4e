<!--
 * @Date: 2022-06-08 09:11:19
 * @Autor: lvshuwen
 * @LastEditTime: 2025-03-31 14:56:44
 * @Description: 体检套餐信息
-->

<template>
  <div class="classifyInformation">
    <!-- 体检套餐信息 -->
    <el-container>
      <el-header>
        <AllBtn
          :btnList="['查询', '新建', '删除', '打印', '导出']"
          :methodCreat="newProject"
          :methodDelete="delMore"
          :methodSearch="search"
          :methodPrint="print"
          :methodExport="exportTable"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          @rowDblclick="handleClick"
          :cell_blue="cell_blue"
          @selectionChange="handleSelectChangeTable"
          :tableLoading.sync="loading"
          isCheck
          :columnWidth="columnWidth"
        >
          <template #wechatShow="{ scope }">
            {{ scope.row.wechatShow ? '是' : '否' }}
          </template>
          <template #sex="{ scope }">
            {{ G_EnumList['Sex'][scope.row.sex] }}
          </template>
          <template #columnRight>
            <el-table-column
              prop="operation"
              label="操作"
              width="200"
              fixed="right"
            >
              <template slot-scope="scope">
                <el-button
                  type="primary"
                  plain
                  size="mini"
                  class="view"
                  @click="viewDetails(scope.row)"
                  >对应详情</el-button
                >
                <el-button
                  type="primary"
                  plain
                  size="mini"
                  class="view"
                  @click="viewDetail(scope.row)"
                  >互斥组合</el-button
                >
              </template>
            </el-table-column>
          </template>
        </PublicTable>
      </el-main>
    </el-container>
    <el-drawer
      title="体检套餐信息属性"
      :visible.sync="drawer"
      :before-close="handleClose"
      :wrapperClosable="false"
      size="50%"
    >
      <el-form :model="popupForm" ref="ruleForm" :rules="rules">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              v-if="funTpye !== '/Create'"
              label="套餐代码"
              :label-width="formLabelWidth"
              prop="clusCode"
            >
              <el-input
                v-model.trim="popupForm.clusCode"
                autocomplete="off"
                size="small"
                :disabled="disabled"
                placeholder="请输入套餐代码"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              label="套餐名称"
              :label-width="formLabelWidth"
              prop="clusName"
            >
              <el-input
                v-model.trim="popupForm.clusName"
                autocomplete="off"
                size="small"
                placeholder="请输入套餐名称"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              label="单价"
              :label-width="formLabelWidth"
              prop="price"
            >
              <el-input
                v-model.trim="popupForm.price"
                autocomplete="off"
                size="small"
                placeholder="请输入单价"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="性别" :label-width="formLabelWidth" prop="sex">
              <el-select
                class="select"
                v-model.trim="popupForm.sex"
                placeholder="请选择性别"
                size="small"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="item in G_shareSexList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="年龄上限"
              :label-width="formLabelWidth"
              prop="upperAgeLimit"
            >
              <el-input
                v-model.trim="popupForm.upperAgeLimit"
                autocomplete="off"
                size="small"
                placeholder="请输入年龄上限"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              label="年龄下限"
              :label-width="formLabelWidth"
              prop="lowerAgeLimit"
            >
              <el-input
                v-model.trim="popupForm.lowerAgeLimit"
                autocomplete="off"
                size="small"
                placeholder="请输入年龄下限"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              label="显示顺序"
              :label-width="formLabelWidth"
              prop="sortIndex"
            >
              <el-input
                v-model.trim="popupForm.sortIndex"
                autocomplete="off"
                size="small"
                placeholder="请输入显示顺序"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              label="体检分类"
              :label-width="formLabelWidth"
              prop="peCls"
              v-if="G_config.physicalMode.includes('普检')"
            >
              <el-select
                class="select"
                v-model.trim="popupForm.peCls"
                placeholder="请选择体检分类"
                size="small"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="item in G_peClsList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="注意事项"
              :label-width="formLabelWidth"
              prop="attention"
            >
              <el-input
                type="textarea"
                v-model.trim="popupForm.attention"
                autocomplete="off"
                size="small"
                placeholder="请输入注意事项"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12"
            ><el-form-item
              label="启用标识"
              :label-width="formLabelWidth"
              prop="isEnabled"
            >
              <el-radio-group v-model.trim="popupForm.isEnabled">
                <el-radio :label="true">启用</el-radio>
                <el-radio :label="false">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="申请单格式"
              :label-width="formLabelWidth"
              prop="guidanceType"
            >
              <el-select
                class="select"
                v-model.trim="popupForm.guidanceType"
                placeholder="请选择申请单格式"
                size="small"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="item in G_codeGuidanceType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="报告单格式"
              :label-width="formLabelWidth"
              prop="reportType"
            >
              <el-select
                class="select"
                v-model.trim="popupForm.reportType"
                placeholder="请选择报告单格式"
                size="small"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="item in G_codeReportType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="拼音码"
              :label-width="formLabelWidth"
              prop="pinYinCode"
            >
              <el-input
                v-model.trim="popupForm.pinYinCode"
                autocomplete="off"
                size="small"
                placeholder="请输入拼音码"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              label="五笔码"
              :label-width="formLabelWidth"
              prop="wuBiCode"
            >
              <el-input
                v-model.trim="popupForm.wuBiCode"
                autocomplete="off"
                size="small"
                placeholder="请输入五笔码"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              label="打印健康证"
              :label-width="formLabelWidth"
              prop="printJKZ"
            >
              <el-select
                class="select"
                v-model.trim="popupForm.printJKZ"
                placeholder="请选择打印健康证"
                size="small"
                style="width: 100%"
                clearable
              >
                <el-option label="是" :value="true"></el-option>
                <el-option label="否" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="微信显示套餐"
              :label-width="formLabelWidth"
              prop="wechatShow"
            >
              <el-select
                class="select"
                v-model.trim="popupForm.wechatShow"
                placeholder="请选择微信显示套餐"
                size="small"
                style="width: 100%"
                clearable
              >
                <el-option label="是" :value="true"></el-option>
                <el-option label="否" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="体检须知"
              :label-width="formLabelWidth"
              prop="note"
            >
              <el-input
                v-model.trim="popupForm.note"
                type="textarea"
                autocomplete="off"
                size="small"
                placeholder="请输入体检须知"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12"> </el-col>
          <el-col :span="12"> </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12"> </el-col>
          <el-col :span="12"> </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12"> </el-col>
          <el-col :span="12"> </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12"> </el-col>
          <el-col :span="12"> </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12"> </el-col>
          <el-col :span="12"> </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12"> </el-col>
          <el-col :span="12"> </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12"> </el-col>
          <el-col :span="12"> </el-col>
        </el-row>
        <!-- <el-row :gutter="20">
          <el-col :span="12"> </el-col>
          <el-col :span="12"></el-col>
        </el-row> -->
      </el-form>
      <div class="dialog-footer">
        <el-button @click="handleClose" size="small">取消</el-button>
        <el-button class="blue_btn" @click="submit" size="small"
          >保存</el-button
        >
      </div>
    </el-drawer>
    <el-drawer
      :title="drawerTitle"
      :visible.sync="drawers"
      :close-on-click-modal="false"
      :wrapperClosable="false"
      size="80%"
    >
      <DetailsDrawer
        :cancel="handleClose"
        :drawerInfo="drawerInfo"
        ref="drawer_ref"
      ></DetailsDrawer>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { export2Excel } from '@/common/excelUtil';
import moment from 'moment';
import PublicTable from '../../../components/publicTable.vue';
import AllBtn from '../allBtn.vue';
import DetailsDrawer from '../otherCodeMapping/detailsDrawer.vue';
import Exceljs from '@/common/excel/groupDetailedExpenseReportExcel';
export default {
  name: 'classifyInformation',
  mixins: [Exceljs],
  components: { PublicTable, AllBtn, DetailsDrawer },
  props: {
    //总传
    allInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    // 价格校验
    const checkPrice = (rule, value, callback) => {
      if (value === '' || value === null) {
        callback(new Error('请输入价格'));
      } else {
        let reg = /^([1-9]\d*(\.\d*[1-9])?)|(0\.\d*[1-9])$/;
        if (!reg.test(value)) {
          callback(new Error('输入的价格必须大于0'));
        } else {
          callback();
        }
      }
    };
    return {
      drawerTitle: '',
      drawerInfo: this.allInfo, //总传
      drawerTheads: {},
      formInline: '',
      codeArr: [],
      resizeFlag: '',
      loading: false,
      disabled: false,
      tableDataCopy: [],
      tableData: [],
      pageSize: 50,
      currentPage: 1,
      dialogTitle: '',
      drawer: false,
      drawers: false,
      isAdd: true,
      formLabelWidth: '120px',
      roleList: [],
      optionsReportType: [],
      optionsGuidanceType: [],
      popupForm: {
        clusCode: '',
        clusName: '',
        price: 99999,
        sex: '',
        peCls: '',
        guidanceType: '',
        reportType: '',
        attention: '',
        note: '',
        lowerAgeLimit: 0,
        upperAgeLimit: 99,
        printJKZ: false,
        isEnabled: true,
        wechatShow: false,
        sortIndex: 99999,
        pinYinCode: '',
        wuBiCode: ''
      },
      theads: {
        clusCode: '参考套餐代码',
        clusName: '参考套餐名称',
        price: '价格',
        sex: '性别',
        sortIndex: '显示顺序',
        upperAgeLimit: '年龄上限',
        lowerAgeLimit: '年龄下限',
        wechatShow: '微信显示',
        note: '体检须知'
      },
      cell_blue: ['clusName'],
      columnWidth: {
        clusCode: 120,
        clusName: 200,
        note: 320
      }, //设置宽度
      rules: {
        // clusCode: [
        //   { required: true, message: "请输入参考套餐代码", trigger: "blur" }
        // ],
        clusName: [
          { required: true, message: '请输入参考套餐名称', trigger: 'blur' }
        ],
        price: [{ validator: checkPrice, trigger: 'blur' }],
        sex: [{ required: true, message: '请选择性别', trigger: 'change' }],
        reportType: [
          { required: true, message: '请选择报告单格式', trigger: 'change' }
        ],
        guidanceType: [
          { required: true, message: '请选择申请单格式', trigger: 'change' }
        ],
        peCls: [
          { required: true, message: '请选择体检分类', trigger: 'change' }
        ]
      },
      funTpye: '/Create',
      excelList: [],
      exportLoading: false
    };
  },
  created: function () {},
  mounted: function () {
    this.getItemCls();
    console.log('[ this.G_codeReportType ]-501', this.G_codeReportType);
    console.log('[ this.G_codeGuidanceType ]-502', this.G_codeGuidanceType);
  },
  computed: {
    ...mapGetters([
      'G_peClsList',
      'G_codeGuidanceType',
      'G_codeReportType',
      'G_EnumList',
      'G_shareSexList',
      'G_config',
      'G_userInfo'
    ])
  },
  methods: {
    //对应详情
    viewDetails(row) {
      this.drawers = true;
      this.drawerTitle = row.clusName + '-组合对应';
      this.drawerInfo.title2 = row.clusName + '-组合对应列表';
      this.drawerInfo.setInfo = { clusCode: row.clusCode };
      this.drawerInfo.setInfos = { clusCode: row.clusCode };
      this.drawerInfo.apiUrls = {
        optionPort: '/EnumData/ItemCls', //下拉
        leftPort: '/EnumData/ItemComb_ItemCls', //左边表格
        rightPort: '/CodeMapping/Query_ClusterComb', //右边表格(query:clusCode)
        del: '/CodeMapping/CD_ClusterComb/Delete',
        set: '/CodeMapping/CD_ClusterComb/Create'
      };
      this.drawerInfo.isRightTheads = true;
      this.drawerInfo.isDiscount = true;
      this.drawerInfo.isCopy = true;
      console.log('[ this.drawerInfo ]-841', this.drawerInfo);
      //执行获取下拉,左右表格数据
      this.$nextTick(() => {
        this.$refs.drawer_ref.getAll();
        this.$refs.drawer_ref.clearInput();
      });
    },
    // 互斥组合
    viewDetail(row) {
      this.drawers = true;
      this.drawerTitle = row.clusName + '-组合互斥';
      this.drawerInfo.title1 = '体检组合项目列表';
      this.drawerInfo.title2 = row.clusName + '-组合互斥列表';
      this.drawerInfo.setInfo = { clusCode: row.clusCode };
      this.drawerInfo.setInfos = { clusCode: row.clusCode };
      this.drawerInfo.param = {
        combCode: 'combCode'
      };
      this.drawerInfo.apiUrls = {
        optionPort: '/EnumData/ItemCls', //下拉
        leftPort: '/EnumData/ItemComb_ItemCls', //左边表格
        rightPort: '/CodeMapping/PageQuery_ClusMutexComb', //右边表格(query:itemCode)
        del: '/CodeMapping/CD_ClusMutexComb/Delete',
        set: '/CodeMapping/CD_ClusMutexComb/Create'
      };
      this.drawerInfo.isRightTheads = true;
      this.drawerInfo.isDiscount = false;
      this.drawerInfo.isCopy = false;
      console.log('[ this.drawerInfo ]-841', this.drawerInfo);
      //执行获取下拉,左右表格数据
      this.$nextTick(() => {
        this.$refs.drawer_ref.getAll();
        this.$refs.drawer_ref.clearInput();
      });
    },

    handleClose() {
      this.drawer = false;
      this.drawers = false;
      this.$refs.ruleForm.resetFields();
    },
    //查询
    search() {
      this.formInline = this.$refs.allBtn_Ref.searchInfo.trim(); //获取组件文本框的值
      if (this.formInline.length > 0) {
        this.tableData = this.tableDataCopy.filter((item) => {
          return (
            item.clusCode.indexOf(this.formInline) !== -1 ||
            item.clusName.indexOf(this.formInline) !== -1
          );
        });
      } else {
        this.getItemCls();
      }
      //this.loading = false;
    },

    //编辑
    handleClick(row, column, event) {
      console.log('row', row);
      this.drawer = true;
      this.disabled = true;
      this.funTpye = '/Update';
      this.popupForm = {
        clusCode: row.clusCode,
        clusName: row.clusName,
        price: row.price,
        sex: row.sex,
        peCls: row.peCls,
        guidanceType: row.guidanceType,
        reportType: row.reportType,
        attention: row.attention,
        note: row.note,
        lowerAgeLimit: row.lowerAgeLimit,
        upperAgeLimit: row.upperAgeLimit,
        printJKZ: row.printJKZ,
        isEnabled: row.isEnabled,
        wechatShow: row.wechatShow,
        sortIndex: row.sortIndex,
        pinYinCode: row.pinYinCode,
        wuBiCode: row.wuBiCode
      };
    },
    //新增
    newProject() {
      this.popupForm = {
        clusCode: '',
        clusName: '',
        price: 99999,
        sex: 0,
        peCls: 3,
        guidanceType: this.G_userInfo?.systemParams?.guidanceType || '',
        reportType: this.G_userInfo?.systemParams?.reportType || '',
        attention: '',
        note: '',
        lowerAgeLimit: 0,
        upperAgeLimit: 99,
        printJKZ: false,
        isEnabled: true,
        wechatShow: false,
        sortIndex: 99999,
        pinYinCode: '',
        wuBiCode: ''
      };
      this.drawer = true;
      this.funTpye = '/Create';
      this.disabled = false;

      //获取报告样式
      // this.getGuidanceType();
      //获取指引单样式
      // this.getReportType();
    },
    // 获取套餐分类信息数据
    getItemCls() {
      console.log(this.$apiUrls);
      this.loading = true;
      this.$ajax.post(this.$apiUrls.RD_CodeCluster + '/Read', []).then((r) => {
        this.tableDataCopy = r.data.returnData;
        this.tableData = this.tableDataCopy.filter((item) => {
          return (
            item.clusCode.indexOf(this.formInline) !== -1 ||
            item.clusName.indexOf(this.formInline) !== -1
          );
        });
        this.loading = false;
      });
    },
    //提交
    submit() {
      this.popupForm.price = Number(this.popupForm.price);
      console.log(this.popupForm);
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$ajax
            .post(this.$apiUrls.CU_CodeCluster + this.funTpye, this.popupForm)
            .then((r) => {
              r = r.data;
              if (!r.success) {
                return;
              }
              this.tableData = [];
              this.drawer = false;
              this.$nextTick(() => {
                // this.$refs.allBtn_Ref.searchInfo = "";
                this.getItemCls();
              });
              if (this.funTpye == '/Create') {
                this.$message({
                  message: '新建成功',
                  type: 'success',
                  showClose: true
                });
              } else {
                this.$message({
                  message: '修改成功',
                  type: 'success',
                  showClose: true
                });
              }
            });
        } else {
          return false;
        }
      });
    },
    //获取指引单样式
    getReportType() {
      this.$ajax.post(this.$apiUrls.GetReportType, []).then((r) => {
        //
        this.optionsGuidanceType = r.data.returnData;
      });
    },
    //获取报告样式
    getGuidanceType() {
      this.$ajax.post(this.$apiUrls.GetGuidanceType, []).then((r) => {
        //
        this.optionsReportType = r.data.returnData;
      });
    },
    //复选框勾选操作
    handleSelectChangeTable: function (val) {
      //console.log(val);
      this.excelList = val;
      this.codeArr = [];
      val.map((item, i) => {
        if (item.clusCode != '') {
          this.codeArr.push(item.clusCode);
        }
      });
      //console.log(this.codeArr);
    },
    //多条删除
    delMore() {
      if (this.codeArr.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据!',
          type: 'warning'
        });
      }
      if (this.codeArr.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            // this.delete(this.codeArr);
            console.log(this.codeArr);
            this.$ajax
              .post(this.$apiUrls.RD_CodeCluster + '/Delete', this.codeArr)
              .then((r) => {
                let { success } = r.data;
                if (!success) return;
                this.$message({
                  showClose: true,
                  message: '删除成功',
                  type: 'success'
                });
                this.getItemCls();
              });
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    // 导出excel
    exportTable() {
      // if (this.excelList.length == 0) {
      //   this.$message.warning("请选择至少一条数据进行操作!");
      //   return;
      // }
      this.$confirm('确定下载体检套餐?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.exportLoading = true;
        this.$ajax
          .post(this.$apiUrls.GetCodeClusterWithDetails)
          .then((r) => {
            let { returnData } = r.data;
            if (returnData?.length == 0) {
              this.$message({
                message: '暂无套餐!',
                type: 'warning',
                showClose: true
              });
              return;
            }
            returnData.forEach((item) => {
              switch (item.sex) {
                case 1:
                  item.sex = '男';
                  break;
                case 2:
                  item.sex = '女';
                  break;

                default:
                  item.sex = '通用';
                  break;
              }
              item.wechatShow = item.wechatShow ? '是' : '否';
            });
            let theads = {
              combCode: '组合代码',
              combName: '组合名称',
              discount: '折扣',
              originalPrice: '原价',
              price: '单价'
            };
            let clusInfo =
              '`套餐名称：${item.clusName}   套餐总价：${item.price}   性别：${item.sex}   显示顺序：${item.sortIndex}   年龄上限：${item.upperAgeLimit}   年龄下限：${item.lowerAgeLimit}   微信显示：${item.wechatShow}   \n体检须知：${item.note}`';
            this.exportExcel(
              theads,
              returnData || [],
              ``,
              clusInfo,
              `体检套餐`,
              'mapClusterCombs'
            );
          })
          .finally((r) => {
            this.exportLoading = false;
          });
      });
    },
    print() {}
  }
};
</script>
<style lang="less" scoped>
.classifyInformation {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  font-weight: 600;
  header {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-container.is-vertical {
    height: 100%;
  }
  .el-header,
  .el-main {
    padding: 0;
  }

  .searchWrap .el-form-item:last-child {
    margin-right: 0;
    margin-left: 10px;
  }
  .indexPage .contentWrap .tabPage {
    margin-top: 0;
  }
  .el-pagination {
    text-align: center;
  }
  .el-drawer.rtl {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #2d3436;
    padding: 0 20px 0 10px;
  }
  .el-drawer__body {
    padding-right: 20px;

    .el-form-item {
      margin-bottom: 10px;
    }
  }
  /deep/.el-drawer__header {
    align-items: center;
    display: flex;
    background: rgba(23, 112, 223, 0.2);
    border-radius: 4px 4px 0 0;
    line-height: 42px;
    font-family: PingFangSC-Medium;
    font-size: 18px;
    padding: 0;
    padding-left: 20px;
    color: #2d3436;
    margin-bottom: 0;
  }
  /deep/.el-drawer__close-btn {
    font-size: 30px;
  }
}
</style>
<style>
.el-dialog {
  width: 480px;
}
.el-table .el-table__cell {
  padding: 5px 0;
}
.el-main .el-checkbox__inner {
  width: 20px;
  height: 20px;
}
.el-drawer__body {
  padding-right: 20px;
}
.el-drawer__header,
.el-form-item__label {
  color: #2d3436;
}
</style>
