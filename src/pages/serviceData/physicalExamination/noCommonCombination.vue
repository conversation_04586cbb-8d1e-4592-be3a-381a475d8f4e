<!--
 * @Date: 2022-06-08 09:11:19
 * @Autor: lvshuwen
 * @LastEditTime: 2022-06-13 17:38:05
 * @Description: 不共用组合
-->

<template>
  <div class="classifyInformation">
    <!-- 互斥组合设置 -->
    <el-container>
      <el-header>
        <AllBtn
          :btnList="['查询', '新建', '删除', '打印', '导出']"
          :methodCreat="newProject"
          :methodDelete="delMore"
          :methodSearch="search"
          :methodPrint="print"
          :methodExport="exportTable"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          :viewTableList.sync="tableData"
          :theads.sync="viewConfig.theads"
          @rowDblclick="handleClick"
          :cell_blue="viewConfig.cell_blue"
          @selectionChange="handleSelectChangeTable"
          :tableLoading.sync="loading"
          isCheck
          :columnWidth="columnWidth"
        >
          <template #columnRight>
            <el-table-column
              prop="operation"
              :label="viewConfig.operation"
              width="120"
              fixed="right"
            >
              <template slot-scope="scope">
                <el-button
                  type="primary"
                  plain
                  size="mini"
                  class="view"
                  @click="viewDetails(scope.row)"
                  >对应详情</el-button
                >
              </template>
            </el-table-column>
          </template>
        </PublicTable>
      </el-main>
    </el-container>
    <!-- <el-drawer
      title="互斥组合设置属性"
      :visible.sync="drawer"
      :before-close="handleClose"
      :wrapperClosable="false"
    >
      <el-form :model="popupForm" ref="ruleForm" :rules="rules">
        <el-form-item
          label="互斥代码"
          :label-width="formLabelWidth"
          prop="mutexCode"
        >
          <el-input
            v-model.trim="popupForm.mutexCode"
            autocomplete="off"
            size="small"
            :disabled="disabled"
            placeholder="请输入互斥代码"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="互斥名称"
          :label-width="formLabelWidth"
          prop="mutexName"
        >
          <el-input
            v-model.trim="popupForm.mutexName"
            autocomplete="off"
            size="small"
            placeholder="请输入互斥名称"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="组合代码"
          :label-width="formLabelWidth"
          prop="combCode"
        >
          <el-input
            v-model.trim="popupForm.combCode"
            autocomplete="off"
            size="small"
            placeholder="请输入组合代码"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="组合名称"
          :label-width="formLabelWidth"
          prop="combName"
        >
          <el-input
            v-model.trim="popupForm.combName"
            autocomplete="off"
            size="small"
            placeholder="请输入组合名称"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="handleClose" size="small">取消</el-button>
        <el-button class="blue_btn" @click="submit" size="small"
          >保存</el-button
        >
      </div>
    </el-drawer> -->
    <el-drawer
      :title="drawerTitle"
      :visible.sync="drawers"
      :before-close="handleClose"
      size="70%"
      :wrapperClosable="false"
    >
      <DetailsDrawer
        :drawerInfo="drawerInfo"
        :viewConfigId="viewConfig.id"
        :isEdit="isEdit"
        ref="drawer_ref"
      ></DetailsDrawer>
    </el-drawer>
  </div>
</template>

<script>
import { export2Excel } from '@/common/excelUtil';
import moment from 'moment';
import PublicTable from '../../../components/publicTable.vue';
import AllBtn from '../allBtn.vue';
import DetailsDrawer from '../otherCodeMapping/detailsDrawer.vue';
export default {
  name: 'noCommonCombination',
  components: {
    PublicTable,
    AllBtn,
    DetailsDrawer
  },
  props: {
    viewConfig: {
      type: Object,
      default: {}
    },
    claName: {
      type: String,
      default: ''
    },
    //总传
    allInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      drawerInfo: {}, //总传
      formInline: '',
      codeArr: [],
      resizeFlag: '',
      loading: false,
      disabled: false,
      tableDataCopy: [],
      tableData: [],
      pageSize: 50,
      currentPage: 1,
      dialogTitle: '',
      drawer: false,
      drawers: false,
      isEdit: false,
      isAdd: true,
      formLabelWidth: '120px',
      roleList: [],
      popupForm: {
        mutexCode: '',
        mutexName: '',
        combCode: '',
        combName: ''
      },
      columnWidth: {
        mutexCode: 80,
        mutexName: 120
      },
      theads: {
        mutexCode: '代码',
        mutexName: '名称',
        combCode: '组合代码',
        combName: '组合名称'
      },
      rules: {
        mutexCode: [{ required: true, message: '请输入代码', trigger: 'blur' }],
        mutexName: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        combCode: [
          { required: true, message: '请输入组合代码', trigger: 'blur' }
        ],
        combName: [{ required: true, message: '请输入名称', trigger: 'blur' }]
      },
      funTpye: '/Create',
      excelList: [],
      drawerTitle: ''
    };
  },
  created: function () {},
  mounted: function () {
    this.getItemCls();
  },
  methods: {
    //对应详情
    viewDetails(row) {
      console.log('row: ', row);
      this.drawerRow = row;
      this.drawerInfo = {};
      this.drawerInfo = this.allInfo;
      this.drawerInfo.active = 2;
      this.isEdit = true;
      this.drawers = true;
      let id = this.viewConfig.id;
      if (id == '1') {
        if (row.mutexName) {
          this.drawerTitle = row.mutexName + '-互斥组合对应';
          this.drawerInfo.title2 = row.mutexName + '-互斥组合对应列表';
          this.drawerInfo.setInfo = { mutexCode: row.mutexCode };
          this.drawerInfo.setInfos = {
            mutexCode: row.mutexCode,
            mutexName: row.mutexName
          };
        }
      }
      //执行获取下拉,左右表格数据
      this.$nextTick(() => {
        this.$refs.drawer_ref.getAll();
        this.$refs.drawer_ref.clearInput();
      });
    },
    handleClose() {
      this.drawer = false;
      this.drawers = false;
      // this.$refs.ruleForm.resetFields();
    },
    //查询
    search() {
      this.formInline = this.$refs.allBtn_Ref.searchInfo.trim(); //获取组件文本框的值
      if (this.formInline.length > 0) {
        this.tableData = this.tableDataCopy.filter((item) => {
          return (
            item.mutexCode.indexOf(this.formInline) !== -1 ||
            item.mutexName.indexOf(this.formInline) !== -1
          );
        });
      } else {
        this.getItemCls();
      }
      //this.loading = false;
    },

    //编辑
    handleClick(row, column, event) {
      console.log('row', row);
      this.drawer = true;
      this.funTpye = '/Update';
      this.disabled = true;
      this.popupForm = {
        mutexCode: row.mutexCode,
        mutexName: row.mutexName,
        combCode: row.combCode,
        combName: row.combName
      };
    },
    //新增
    newProject() {
      this.popupForm = {
        mutexCode: '',
        mutexName: '',
        combCode: '',
        combName: ''
      };
      this.drawer = true;
      this.funTpye = '/Create';
      this.disabled = false;
      this.drawers = true;
      this.drawerInfo = this.allInfo;
      this.isEdit = false;
      this.drawerInfo.active = 1;
      this.drawerTitle = '新建互斥组合对应';

      //执行获取下拉,左右表格数据
      this.$nextTick(() => {
        this.$refs.drawer_ref.getAll();
        this.$refs.drawer_ref.clearInput();
      });
    },
    // 获取项目分类信息数据
    getItemCls() {
      this.loading = true;
      this.$ajax.post(this.viewConfig.apiUrl.get, []).then((r) => {
        // this.tableData = r.data.returnData;
        this.tableDataCopy = r.data.returnData;
        this.tableData = this.tableDataCopy.filter((item) => {
          return (
            item.mutexCode.indexOf(this.formInline) !== -1 ||
            item.mutexName.indexOf(this.formInline) !== -1
          );
        });
        this.loading = false;
      });
    },
    //提交
    submit() {
      console.log(this.popupForm);
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$ajax
            .post(this.$apiUrls.CU_CodeMutexComb + this.funTpye, this.popupForm)
            .then((r) => {
              r = r.data;
              if (!r.success) {
                return;
              }
              this.tableData = [];
              this.drawer = false;
              this.$nextTick(() => {
                // this.$refs.allBtn_Ref.searchInfo = "";
                this.getItemCls();
              });
              if (this.funTpye == '/Create') {
                this.$message({
                  message: '新建成功',
                  type: 'success',
                  showClose: true
                });
              } else {
                this.$message({
                  message: '修改成功',
                  type: 'success',
                  showClose: true
                });
              }
            });
        } else {
          return false;
        }
      });
    },
    //复选框勾选操作
    handleSelectChangeTable: function (val) {
      // console.log(val);
      this.excelList = val;
      this.codeArr = [];
      val.map((item, i) => {
        console.log(item);
        if (item.mutexName != '') {
          this.codeArr.push(item.mutexCode);
        }
      });
    },
    //多条删除
    delMore() {
      if (this.codeArr.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据!',
          type: 'warning'
        });
      }
      if (this.codeArr.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            // this.delete(this.codeArr);
            console.log(this.codeArr);
            this.$ajax
              .post(this.viewConfig.apiUrl.delete, this.codeArr)
              .then((r) => {
                let { success } = r.data;
                if (!success) return;
                this.$message({
                  showClose: true,
                  message: '删除成功',
                  type: 'success'
                });
                this.getItemCls();
              });
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    // 导出excel
    exportTable() {
      if (this.excelList.length == 0) {
        this.$message.warning('请选择至少一条数据进行操作!');
        return;
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const columns = [
          {
            title: '代码',
            key: 'mutexCode'
          },
          {
            title: '名称',
            key: 'mutexName'
          },
          {
            title: '组合代码',
            key: 'combCode'
          },
          {
            title: '名称',
            key: 'combName'
          }
        ];
        this.excelList.map((item, i) => {
          item.index = i + 1;
        });
        const title = '互斥组合设置' + moment().format('YYYY-MM-DD');
        this.$nextTick(() => {
          export2Excel(columns, this.excelList, title);
        });
      });
    },
    print() {}
  }
};
</script>
<style lang="less" scoped>
.classifyInformation {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  font-weight: 600;
  header {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-container.is-vertical {
    height: 100%;
  }
  .el-header,
  .el-main {
    padding: 0;
  }

  .searchWrap .el-form-item:last-child {
    margin-right: 0;
    margin-left: 10px;
  }
  .indexPage .contentWrap .tabPage {
    margin-top: 0;
  }
  .el-pagination {
    text-align: center;
  }
  .el-drawer.rtl {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #2d3436;
    padding: 0 20px 0 10px;
  }
  .el-drawer__body {
    padding-right: 20px;

    .el-form-item {
      margin-bottom: 10px;
    }
  }
  /deep/.el-drawer__header {
    background: #d1e2f9;
    margin-bottom: 0;
    padding: 18px;
  }
  /deep/.el-drawer__body {
    // background: #f0f2f3;
    margin: 0 !important;
    padding: 0 !important;
  }
  /deep/.el-drawer__header,
  /deep/.el-form-item__label {
    color: #2d3436;
  }
}
</style>
<style>
.el-dialog {
  width: 480px;
}
.el-table .el-table__cell {
  padding: 5px 0;
}
.el-main .el-checkbox__inner {
  width: 20px;
  height: 20px;
}
</style>
