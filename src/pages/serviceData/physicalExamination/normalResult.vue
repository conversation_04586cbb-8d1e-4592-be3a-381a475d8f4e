<template>
  <div class="referenceRangeInfo">
    <!-- 正常结果表 -->
    <el-container>
      <el-header>
        <AllBtn
          :btnList="['查询', '新建', '删除', '打印', '导出']"
          :methodCreat="newProject"
          :methodDelete="delMore"
          :methodSearch="search"
          :methodPrint="print"
          :methodExport="exportTable"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          @rowDblclick="handleClick"
          :cell_blue="cell_blue"
          @selectionChange="handleSelectChangeTable"
          :tableLoading.sync="loading"
          isCheck
        >
        </PublicTable>
      </el-main>
    </el-container>
    <el-drawer
      title="正常结果表属性"
      :visible.sync="drawer"
      :before-close="handleClose"
      :wrapperClosable="false"
    >
      <el-form :model="popupForm" ref="ruleForm" :rules="rules">
        <el-form-item
          label="代码"
          :label-width="formLabelWidth"
          prop="resultCode"
        >
          <el-input
            v-model.trim="popupForm.resultCode"
            autocomplete="off"
            size="small"
            :disabled="disabled"
            placeholder="请输入代码"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          label="正常结果"
          :label-width="formLabelWidth"
          prop="result"
        >
          <el-input
            v-model.trim="popupForm.result"
            autocomplete="off"
            size="small"
            type="textarea"
            placeholder="请输入正常结果"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="handleClose" size="small">取消</el-button>
        <el-button class="blue_btn" @click="submit" size="small"
          >保存</el-button
        >
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { export2Excel } from '@/common/excelUtil';
import moment from 'moment';
import PublicTable from '../../../components/publicTable.vue';
import AllBtn from '../allBtn.vue';
export default {
  name: 'referenceRangeInfo',
  components: { PublicTable, AllBtn },
  data() {
    return {
      formInline: '',
      codeArr: [],
      resizeFlag: '',
      loading: false,
      disabled: false,
      tableDataCopy: [],
      tableData: [],
      pageSize: 50,
      currentPage: 1,
      dialogTitle: '',
      drawer: false,
      isAdd: true,
      formLabelWidth: '120px',
      roleList: [],
      popupForm: {
        resultCode: '',
        result: ''
      },
      theads: {
        resultCode: '代码',
        result: '正常结果'
      },
      cell_blue: ['result'],
      rules: {
        resultCode: [
          { required: true, message: '请输入代码', trigger: 'blur' }
        ],
        result: [{ required: true, message: '请输入正常结果', trigger: 'blur' }]
      },
      funTpye: '/Create',
      excelList: []
    };
  },
  created: function () {},
  mounted: function () {
    this.getItemCls();
  },
  methods: {
    handleClose() {
      this.drawer = false;
      this.$refs.ruleForm.resetFields();
    },
    //查询
    search() {
      this.formInline = this.$refs.allBtn_Ref.searchInfo.trim(); //获取组件文本框的值
      if (this.formInline.length > 0) {
        this.tableData = this.tableDataCopy.filter((item) => {
          return (
            item.resultCode.indexOf(this.formInline) !== -1 ||
            item.result.indexOf(this.formInline) !== -1
          );
        });
      } else {
        this.getItemCls();
      }
      //this.loading = false;
    },
    // 获取项目分类信息数据
    getItemCls() {
      this.loading = true;
      this.$ajax
        .post(this.$apiUrls.RD_CodeNormalResult + '/Read', [])
        .then((r) => {
          this.tableDataCopy = r.data.returnData;
          this.tableData = this.tableDataCopy.filter((item) => {
            return (
              item.resultCode.indexOf(this.formInline) !== -1 ||
              item.result.indexOf(this.formInline) !== -1
            );
          });
          this.loading = false;
        });
    },
    //新增
    newProject() {
      this.popupForm = {
        resultCode: '',
        result: ''
      };
      this.drawer = true;
      this.funTpye = '/Create';
      this.disabled = false;
    },

    //编辑
    handleClick(row) {
      console.log('row', row);
      this.drawer = true;
      this.disabled = true;
      this.funTpye = '/Update';
      this.popupForm = {
        resultCode: row.resultCode,
        result: row.result
      };
    },

    //提交
    submit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$ajax
            .post(
              this.$apiUrls.CU_CodeNormalResult + this.funTpye,
              this.popupForm
            )
            .then((r) => {
              r = r.data;
              if (!r.success) {
                return;
              }
              this.tableData = [];
              this.drawer = false;
              this.$nextTick(() => {
                // this.$refs.allBtn_Ref.searchInfo = "";
                this.getItemCls();
              });
              if (this.funTpye == '/Create') {
                this.$message({
                  message: '新建成功',
                  type: 'success',
                  showClose: true
                });
              } else {
                this.$message({
                  message: '修改成功',
                  type: 'success',
                  showClose: true
                });
              }
            });
        } else {
          return false;
        }
      });
    },
    //复选框勾选操作
    handleSelectChangeTable: function (val) {
      //console.log(val);
      this.excelList = val;
      this.codeArr = [];
      val.map((item, i) => {
        if (item.resultCode != '') {
          this.codeArr.push(item.resultCode);
        }
      });
      //console.log(this.codeArr);
    },
    //多条删除
    delMore() {
      if (this.codeArr.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据!',
          type: 'warning'
        });
      }
      if (this.codeArr.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.$ajax
              .post(this.$apiUrls.RD_CodeNormalResult + '/Delete', this.codeArr)
              .then((r) => {
                let { success } = r.data;
                if (!success) return;
                this.$message({
                  showClose: true,
                  message: '删除成功',
                  type: 'success'
                });
                this.getItemCls();
              });
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    // 导出excel
    exportTable() {
      if (this.excelList.length == 0) {
        this.$message.warning('请选择至少一条数据进行操作!');
        return;
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const columns = [
          {
            title: '序号',
            key: 'index'
          },
          {
            title: '代码',
            key: 'resultCode'
          },
          {
            title: '正常结果',
            key: 'result'
          }
        ];
        this.excelList.map((item, i) => {
          item.index = i + 1;
        });
        const title = '正常结果表' + moment().format('YYYY-MM-DD');
        this.$nextTick(() => {
          export2Excel(columns, this.excelList, title);
        });
      });
    },
    print() {}
  }
};
</script>
<style lang="less" scoped>
.referenceRangeInfo {
  display: flex;
  flex-direction: column;
  font-weight: 600;
  width: 100%;
  height: 100%;
  header {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
  }
  /deep/.el-drawer__header,
  /deep/.el-form-item__label {
    color: #2d3436;
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-container.is-vertical {
    height: 100%;
  }
  .el-header,
  .el-main {
    padding: 0;
  }

  .searchWrap .el-form-item:last-child {
    margin-right: 0;
    margin-left: 10px;
  }
  .indexPage .contentWrap .tabPage {
    margin-top: 0;
  }
  .el-pagirange {
    text-align: center;
  }
  .el-drawer.rtl {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #2d3436;
    padding: 0 20px 0 10px;
  }
  .el-drawer__body {
    padding-right: 20px;

    .el-form-item {
      margin-bottom: 10px;
      .el-select {
        width: 100%;
      }
    }
  }
}
</style>
<style>
.el-table .el-table__cell {
  padding: 5px 0;
}
.el-main .el-checkbox__inner {
  width: 20px;
  height: 20px;
}
.el-drawer__body {
  padding-right: 20px;
}
</style>
