<template>
  <div class="mergeTable_com">
    <el-table
      ref="tableCom_Ref"
      v-loading="tableLoading"
      highlight-current-row
      style="width: 100%; color: #2d3436; font-weight: 600; font-size: 14px"
      size="small"
      border
      :data="tableData"
      @row-dblclick="rowDblclick"
      v-el-table-infinite-scroll="load"
      :cell-class-name="cellClassName"
      :header-cell-style="{
        background: 'rgba(23,112,223,.2)',
        fontSize: '14px',
        color: 'rgba(45,52,54,.6)'
      }"
      :span-method="objectSpanMethod"
      height="100%"
    >
      <el-table-column
        type="selection"
        width="50"
        v-if="isCheck"
      ></el-table-column>
      <el-table-column
        :prop="th"
        :label="theads[th]"
        v-for="th in Object.keys(theads)"
        :key="th"
      >
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'mergeTable',
  props: {
    viewConfig: {
      type: Object,
      default: () => {
        return {};
      }
    },
    // 表头数据
    theads: {
      type: Object,
      default: () => {
        return {};
      }
    },
    // 表格数据
    viewTableList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    // 合并的key
    mergeKeys: {
      type: Array,
      default: () => {
        return [];
      }
    },
    // 是否可选
    isCheck: {
      type: Boolean,
      default: () => {
        return false;
      }
    },
    // 是否开启分页
    isOpenPage: {
      type: Boolean,
      default: () => {
        return true;
      }
    },
    // 加载动画
    tableLoading: {
      type: Boolean,
      default: () => {
        return false;
      }
    }
  },
  data() {
    return {
      tableData: [],
      pageSize: 50,
      currentPage: 1
    };
  },
  methods: {
    mergeRowMethod({ row, _rowIndex, column, visibleData }) {
      const fields = this.mergeKeys;
      const cellValue = row[column.property];

      if (cellValue && fields.includes(column.property)) {
        const prevRow = visibleData[_rowIndex - 1];
        let nextRow = visibleData[_rowIndex + 1];
        if (prevRow && prevRow[column.property] === cellValue) {
          return { rowspan: 0, colspan: 0 };
        } else {
          let countRowspan = 1;
          while (nextRow && nextRow[column.property] === cellValue) {
            nextRow = visibleData[++countRowspan + _rowIndex];
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 };
          }
        }
      }
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      const fields = this.mergeKeys;
      const cellValue = row[column.property];
      let visibleData = this.tableData;

      if (cellValue && fields.includes(column.property)) {
        const prevRow = visibleData[rowIndex - 1];
        let nextRow = visibleData[rowIndex + 1];
        if (prevRow && prevRow[column.property] === cellValue) {
          return { rowspan: 0, colspan: 0 };
        } else {
          let countRowspan = 1;
          while (nextRow && nextRow[column.property] === cellValue) {
            nextRow = visibleData[++countRowspan + rowIndex];
          }
          if (countRowspan > 1) {
            //console.log(row);
            return { rowspan: countRowspan, colspan: 1 };
          }
        }
      }
    },
    // 滚动加载
    load() {
      console.log('加载表格分页');
      this.currentPage++;
      // if (this.currentPage * this.pageSize >= this.viewTableList.length) return;
      let loadSize = this.currentPage * this.pageSize;
      if (loadSize - this.viewTableList.length >= this.pageSize) return;
      let start = (this.currentPage - 1) * this.pageSize;
      let end = this.currentPage * this.pageSize;
      let pageData = this.viewTableList.slice(start, end);
      console.log(pageData);
      this.tableData.push(...pageData);
      console.log(this.tableData);

      let tableRef = this.$refs.tableCom_Ref;
      console.log(tableRef.store.states.isAllSelected);
      if (tableRef.store.states.isAllSelected) {
        tableRef.toggleAllSelection();
      }
    },
    // 单元格样式
    cellClassName({ row, rowIndex, column, columnIndex }) {
      if (this.viewConfig.cell_blue.includes(column.property)) {
        return 'cell_blue';
      }
      if (this.viewConfig.cell_red.includes(column.property)) {
        return 'cell_red';
      }
    },
    // 表格的双击事件
    rowDblclick(row) {
      console.log(row);
      this.$emit('rowDblclick', row);
    }
  },
  watch: {
    viewTableList(n, o) {
      this.tableData = [];
      this.currentPage = 1;
      this.$nextTick(() => {
        if (!this.isOpenPage) {
          this.tableData = n;
          return;
        }
        if (this.currentPage * this.pageSize >= n.length) {
          this.tableData.push(...n);
          return;
        }
        let start = (this.currentPage - 1) * this.pageSize;
        let end = this.currentPage * this.pageSize;
        let pageData = n.slice(start, end);
        console.log(pageData);
        this.tableData.push(...pageData);
      });
    }
  }
};
</script>
<style lang="less" scoped></style>
<style lang="less">
.mergeTable_com {
  height: 100%;
  width: 100%;

  .el-table__row--striped td.el-table__cell,
  .vxe-table--render-default .vxe-body--row.row--stripe {
    background-color: #e7f0fb !important;
  }

  .vxe-table--render-default .vxe-body--row.row--current {
    background-color: #e6f7ff;
  }

  .el-table__cell {
    vertical-align: top;
  }

  .cell_blue {
    color: #1770df;
  }

  .cell_red {
    color: #d63031;
  }
}
</style>
