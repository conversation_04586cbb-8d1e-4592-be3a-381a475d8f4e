<template>
  <div class="drawerTable">
    <vxe-table
      border="inner"
      style="width: 100%; color: #2d3436; font-weight: 600; font-size: 14px"
      size="small"
      :cell-class-name="cellClassName"
      :header-cell-style="{
        background: '#d1e2f9',
        fontSize: '14px',
        color: '#909399'
      }"
      height="100%"
      :row-config="{ isCurrent: true, isHover: true }"
      :column-config="{ isCurrent: true, isHover: true }"
      stripe
      :scroll-y="{ gt: 20, oSize: 20 }"
      :span-method="mergeRowMethod"
      :data="viewTableList"
      @row-dblclick="cellClick"
    >
      <vxe-column type="seq" width="60" title="序号"></vxe-column>
      <vxe-column
        :field="th"
        :title="theads[th]"
        v-for="th in Object.keys(theads)"
        :key="th"
      ></vxe-column>
    </vxe-table>
  </div>
</template>

<script>
export default {
  name: 'drawerTable',
  props: {
    viewConfig: {
      type: Object,
      default: () => {
        return {};
      }
    },
    // 表头数据
    theads: {
      type: Object,
      default: () => {
        return {};
      }
    },
    // 表格数据
    viewTableList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    // 合并的key
    mergeKeys: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  methods: {
    mergeRowMethod({ row, _rowIndex, column, visibleData }) {
      const fields = this.mergeKeys;
      const cellValue = row[column.property];
      if (cellValue && fields.includes(column.property)) {
        const prevRow = visibleData[_rowIndex - 1];
        let nextRow = visibleData[_rowIndex + 1];
        if (prevRow && prevRow[column.property] === cellValue) {
          return { rowspan: 0, colspan: 0 };
        } else {
          let countRowspan = 1;
          while (nextRow && nextRow[column.property] === cellValue) {
            nextRow = visibleData[++countRowspan + _rowIndex];
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 };
          }
        }
      }
    },
    // 单元格样式
    cellClassName({ row, rowIndex, column, columnIndex }) {
      if (this.drawerConfig.leftTable.cell_blue.includes(column.property)) {
        return 'cell_blue';
      }
      if (this.drawerConfig.leftTable.cell_red.includes(column.property)) {
        return 'cell_red';
      }
    }
  }
};
</script>
<style lang="less" scoped></style>
<style lang="less">
.drawerTable {
  height: 100%;
  .vxe-table--render-default .vxe-body--row.row--stripe {
    background-color: #e7f0fb;
  }

  .vxe-table--render-default .vxe-body--row.row--current {
    background-color: #e6f7ff;
  }

  .cell_blue {
    color: #1770df;
  }

  .cell_red {
    color: #d63031;
  }
}
</style>
