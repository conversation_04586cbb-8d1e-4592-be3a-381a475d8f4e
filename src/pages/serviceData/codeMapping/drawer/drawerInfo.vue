<template>
  <!-- 测试 -->
  <div class="drawerInfo">
    <div class="upperHalf">
      <div class="leftCont">
        <header>
          <h4>{{ drawerConfig.titleList[1] }}</h4>
          <div class="searchWrap">
            <el-select
              v-show="drawerConfig.leftTable.isSelect"
              v-model.trim="selectLeft"
              placeholder="请选择"
              size="small"
              class="search-input"
              filterable
              clearable
            >
              <el-option
                v-for="item in drawerOptionList"
                :key="item.clsCode"
                :value="item.clsName"
                :title="item.clsCode"
              >
              </el-option>
            </el-select>
            <div v-show="drawerConfig.leftTable.isSearch" style="display: flex">
              <el-input
                clearable
                v-model.trim="inputLeft"
                size="small"
                placeholder="请输入查找内容"
                class="search-input"
                @keyup.enter.native="methodSearch1"
              ></el-input>
              <el-button size="small" class="blue_btn" @click="methodSearch1"
                >查找</el-button
              >
            </div>
          </div>
        </header>
        <div class="tableCont">
          <component
            class="table_com"
            :is="drawerConfig.leftTable.tableComponent"
            :mergeKeys="drawerConfig.leftTable.mergeKeys"
            :theads="drawerConfig.leftTable.theads"
            :isCheck="drawerConfig.leftTable.isCheck"
            :viewConfig="viewConfig"
            :viewTableList.sync="drawerTableList1"
            @currentChange="currentChangeEvent"
            :tableLoading.sync="tableLoading"
            ref="leftTable_Ref"
          >
          </component>
        </div>
      </div>
      <div
        class="rightCont"
        v-if="
          drawerConfig.titleList.length !== 5 &&
          drawerConfig.titleList[2] !== ''
        "
      >
        <header>
          <h4>{{ drawerConfig.titleList[2] }}</h4>
          <div class="searchWrap">
            <el-select
              v-show="drawerConfig.rightTable.isSelect"
              v-model.trim="selectRight"
              placeholder="请选择"
              size="small"
              class="search-input"
              filterable
              clearable
            >
              <el-option
                v-for="item in drawerOptionList"
                :key="item.clsCode"
                :value="item.clsName"
                :title="item.clsCode"
              >
              </el-option>
            </el-select>
            <div
              v-show="drawerConfig.rightTable.isSearch"
              style="display: flex"
            >
              <el-input
                clearable
                v-model.trim="inputRight"
                size="small"
                placeholder="请输入查找内容"
                class="search-input"
                @keyup.enter.native="methodSearch2"
              ></el-input>
              <el-button size="small" class="blue_btn" @click="methodSearch2"
                >查找</el-button
              >
            </div>
          </div>
        </header>
        <div class="tableCont">
          <component
            class="table_com"
            :is="drawerConfig.rightTable.tableComponent"
            :mergeKeys="drawerConfig.rightTable.mergeKeys"
            :theads="drawerConfig.rightTable.theads"
            isCheck
            :viewConfig="viewConfig"
            :viewTableList.sync="drawerTableList2"
            :tableLoading.sync="tableLoading"
            ref="rightTable_Ref"
          >
          </component>
        </div>
      </div>

      <div class="rightCont1" v-if="drawerConfig.titleList.length === 5">
        <div class="rightTable1">
          <header>
            <h4>{{ drawerConfig.titleList[2] }}</h4>
          </header>
          <div class="tableCont">
            <component
              class="table_com"
              :is="drawerConfig.rightTable1.tableComponent"
              :mergeKeys="drawerConfig.rightTable1.mergeKeys"
              :theads="drawerConfig.rightTable1.theads"
              :viewConfig="viewConfig"
              :viewTableList.sync="drawerTableList"
              :tableLoading.sync="tableLoading"
              ref="rightTable1_Ref"
            >
            </component>
          </div>
        </div>
        <div class="rightTable2">
          <header>
            <h4>{{ drawerConfig.titleList[3] }}</h4>
          </header>
          <div class="tableCont">
            <component
              class="table_com"
              :is="drawerConfig.rightTable2.tableComponent"
              :mergeKeys="drawerConfig.rightTable2.mergeKeys"
              :theads="drawerConfig.rightTable2.theads"
              :viewConfig="viewConfig"
              :viewTableList.sync="drawerTableList"
              :tableLoading.sync="tableLoading"
              ref="rightTable1_Ref"
            >
            </component>
          </div>
        </div>
      </div>

      <div class="rightCont2" v-if="drawerConfig.titleList[2] === ''">
        <div class="symbol-box">
          <div class="check" @click="check">校验</div>
          <div class="symbol">
            <div
              v-for="item in symbolData"
              :key="item"
              @click="symbolClick(item)"
            >
              {{ item }}
            </div>
          </div>
          <div class="clear">
            <div @click="clear">清空</div>
          </div>
        </div>
        <div class="right-input">
          <header>
            <span class="lable">计算项目</span>
            <el-select
              v-model.trim="selectRight"
              placeholder="请选择"
              size="small"
              filterable
              @change="selectChange"
            >
              <el-option
                v-for="item in drawerOptionList"
                :key="item.itemCode"
                :value="item.itemCode"
                :label="item.itemName"
              >
              </el-option>
            </el-select>
          </header>

          <div class="textarea-box">
            <el-input
              type="textarea"
              placeholder="请输入表达式"
              v-model.trim="textarea"
              @input="textareaVale"
            >
            </el-input>
          </div>
        </div>
      </div>
    </div>

    <div class="lowerHalf">
      <header>
        <h4>
          {{
            drawerConfig.titleList.length === 5
              ? drawerConfig.titleList[4]
              : drawerConfig.titleList[3]
          }}
        </h4>
        <div class="searchWrap" style="display: flex">
          <div class="search" style="display: flex">
            <el-input
              clearable
              v-model.trim="inputLowerHalf"
              size="small"
              placeholder="请输入查找内容"
              class="search-input"
              @keyup.enter.native="methodSearch3"
            ></el-input>

            <el-button size="small" class="blue_btn" @click="methodSearch3"
              >查找</el-button
            >
          </div>
          <div v-show="drawerConfig.isNumber">
            <span class="number-text">数量</span>
            <el-input-number
              v-model.trim="number"
              controls-position="right"
              :min="1"
              size="small"
              class="number-input"
            ></el-input-number>
          </div>
          <div v-show="drawerConfig.isDiscount">
            <span class="number-text">折扣率</span>
            <el-input
              clearable
              size="small"
              class="discount-input"
              v-model.trim="discount"
              placeholder="只能输入0-1的小数"
              oninput="value=value.replace(/^0[0-9]|^[2-9]|^1[0-9]|^1\.|[^\d.]/g,'')"
            ></el-input>
          </div>
          <el-button @click="drawerAddBtn" size="small" class="blue_btn"
            >增加</el-button
          >
          <el-button
            type="danger"
            size="small"
            class="search-btn"
            @click="rawerDels"
            >删除</el-button
          >
        </div>
      </header>
      <div class="tableCont">
        <component
          class="table_com"
          :is="viewConfig.tableComponent"
          :mergeKeys="viewConfig.mergeKeys"
          :theads="viewConfig.theads"
          isCheck
          :viewConfig="viewConfig"
          :viewTableList.sync="viewTableListTemp"
          :tableLoading.sync="tableLoading"
          ref="lowerHalf_Ref"
          @selectionChange="currentChange"
        >
        </component>
      </div>
    </div>
  </div>
</template>

<script>
import MergeTable from '../tableCom/mergeTable';
import TotalTable from '../tableCom/totalTable.vue';
export default {
  name: 'drawerInfo',
  components: {
    MergeTable,
    TotalTable
  },
  props: {
    viewConfig: {
      type: Object,
      default: () => {
        return [];
      }
    },
    drawerConfig: {
      type: Object,
      default: () => {
        return [];
      }
    },
    tableLoading: {
      type: Boolean,
      default: () => {
        return false;
      }
    },
    // 表头数据
    theads: {
      type: Object,
      default: () => {
        return {};
      }
    },
    drawerOptionList: {
      type: Array,
      default: () => {
        return [];
      }
    }, // 表格数据
    drawerTableList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    drawerTableList1: {
      type: Array,
      default: () => {
        return [];
      }
    },
    drawerTableList2: {
      type: Array,
      default: () => {
        return [];
      }
    }, // 表格数据3
    viewTableList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    methodSearch1: {
      type: Function,
      default: null
    },
    methodSearch2: {
      type: Function,
      default: null
    },
    methodSearch3: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      inputLeft: '',
      selectLeft: '',
      inputRight: '',
      selectRight: '',
      inputLowerHalf: '',
      selectLowerHalf: '',
      textarea: '',
      number: '',
      discount: '',
      titleList: [],
      delArr: [],
      leftCurrentChangeEvent: '',
      num: 1,
      num1: '',
      symbolData: ['+', '-', '*', '/', '(', ')'],
      onSelOrder: '',
      viewTableListTemp: this.viewTableList
    };
  },
  created() {
    //console.log(this.drawerConfig);
    this.titleList = this.drawerConfig.titleList;
    //console.log("this: ", this);
  },
  mounted() {},
  methods: {
    handleChange(value) {
      console.log(value);
    },
    // 下面表格选择
    currentChange(row) {
      this.delArr = row;
    },
    // 删除
    rawerDels() {
      let leftParam = JSON.parse(JSON.stringify(this.drawerConfig.leftParam));
      let rightParam = JSON.parse(JSON.stringify(this.drawerConfig.rightParam));
      let obj = {
        ...leftParam,
        ...rightParam
      };
      let paramsArr = [];
      let paramsKeys = Object.keys(obj);
      let paramsValue = Object.values(obj);
      this.delArr.map((items, i) => {
        let paramObj = {};
        paramsKeys.map((par, index) => {
          paramObj[par] = items[paramsValue[index]];
        });
        paramsArr.push(paramObj);
      });
      this.delArr = paramsArr;
      // console.log("this.delArr: ", this.delArr);
      if (this.delArr.length === 0) {
        this.$message({
          message: '请至少选择一条要删除的数据!',
          type: 'warning',
          duration: this.$config.messageTime,
          showClose: true
        });
        return;
      }
      if (this.delArr.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.$ajax
              .post(this.drawerConfig.apiUrls.del, this.delArr)
              .then((r) => {
                const { returnData, success } = r.data;
                if (!success) {
                  return;
                }
                this.$message({
                  showClose: true,
                  message: '删除成功',
                  type: 'success'
                });
                this.$parent.$parent.getDrawerTableList3();
              });
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    // 添加
    drawerAddBtn() {
      let leftData = {};
      let rightData = [];
      if (this.drawerConfig.titleList[2] === '') {
        leftData = this.leftCurrentChangeEvent;
      } else {
        leftData = this.leftCurrentChangeEvent;
        rightData = this.$refs.rightTable_Ref.$refs.tableCom_Ref.selection;
      }
      if (leftData === null || rightData === null) {
        this.$message({
          message: '请选择需要添加的数据!',
          type: 'warning',
          duration: this.$config.messageTime,
          showClose: true
        });
        return;
      }
      let leftParam = JSON.parse(JSON.stringify(this.drawerConfig.leftParam));
      let rightParam = JSON.parse(JSON.stringify(this.drawerConfig.rightParam));
      // console.log("leftParam", leftParam);
      //console.log("rightParam", rightParam);
      Object.keys(leftParam).map((item) => {
        console.log(item);
        leftParam[item] = leftData[leftParam[item]];
      });
      console.log('leftParam', leftParam);
      let rightArr = [];
      let paramsArr = Object.keys(rightParam);
      let paramsValue = Object.values(rightParam);
      rightData.map((items, i) => {
        let paramObj = {};
        paramsArr.map((par, index) => {
          paramObj[par] = items[paramsValue[index]];
        });
        rightArr.push(paramObj);
      });
      // console.log("rightArr", rightArr);
      var number1 = {
        discount: 0,
        count: 0
      };
      var number2 = {
        count: 0
      };
      let data = {};
      let params = [];
      if (this.drawerConfig.titleList[2] === '') {
        let expression = {
          expression: this.textarea,
          expressionShow: ''
        };
        data = {
          ...leftParam,
          ...expression
        };
        params.push(data);
      }
      //有数量和折扣率
      else if (this.drawerConfig.isNumber && this.drawerConfig.isDiscount) {
        number1 = {
          discount: this.discount == '' ? '0' : this.discount,
          count: this.number
        };
        data = {
          ...leftParam,
          ...number1
        };
        params.push(data);
      }
      //有数量
      else if (this.drawerConfig.isNumber) {
        number2 = {
          count: this.number
        };
        data = {
          ...leftParam,
          ...number2
        };
        params.push(data);
        //正常数据
      } else {
        data = {
          ...leftParam
        };
      }
      let datas = {};
      rightArr.map((item) => {
        datas = {
          ...item,
          ...data
        };
        params.push(datas);
      });
      params.map((item) => {
        if (item.fitemClsType) {
          item.fitemClsType = item.fitemClsType === '发票分类' ? 0 : 1;
        }
        datas = {
          ...item,
          ...data
        };
      });
      console.log('params', params);
      this.$ajax.post(this.drawerConfig.apiUrls.set, params).then((r) => {
        if (!r.data.success) {
          return;
        }
        this.$message({
          showClose: true,
          message: '添加成功!',
          type: 'success'
        });
        this.$parent.$parent.getDrawerTableList3();
      });
    },
    // 文本域输入事件
    textareaVale(val) {
      this.textarea = val;
    },
    // 清空
    clear() {
      this.textarea = '';
      this.selectRight = [];
    },
    selectChange(val) {
      this.textarea = this.textarea + `[${val}]`;
    },
    // 当前行选中
    currentChangeEvent(row) {
      console.log('row: ', row);
      this.leftCurrentChangeEvent = row;
      var newArr = [];
      var leftOrder = this.drawerConfig.leftTable.leftOrder;
      this.onSelOrder = row[leftOrder];
      this.viewTableList.map((item) => {
        if (item[leftOrder] === this.onSelOrder) {
          newArr.push(item);
        }
      });
      this.viewTableListTemp = newArr;
      this.textarea = this.textarea + `[${row.combCode}]`;
    },
    // 校验符号点击
    symbolClick(item) {
      this.textarea = this.textarea + item;
    },
    // 校验按钮
    check() {
      if (!this.textarea) {
        this.$message({
          showClose: true,
          message: '请先选择需要校验的数据!',
          type: 'warning'
        });
        return;
      }
      this.$ajax
        .post(this.drawerConfig.apiUrls.expressionVerify, `"${this.textarea}"`)
        .then((r) => {
          console.log('r: ', r);
          const { returnData, success } = r.data;
          if (!success) {
            return;
          }
          this.$message({
            showClose: true,
            message: '校验成功!',
            type: 'success'
          });
        });
    }
  }
};
</script>
<style lang="less" scoped>
.drawerInfo {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-weight: 600;
  padding: 0 10px;
  /deep/.el-table__row--striped td.el-table__cell,
  .el-table--render-default .el-table__body--row.row--stripe {
    background-color: #e7f0fb !important;
  }

  /deep/.el-table--render-default .el-table__body--row.row--current {
    background-color: #1770df;
    color: #fff;
  }
  /deep/.el-table__cell {
    vertical-align: top;
  }
  /deep/.cell_blue {
    color: #1770df;
  }

  /deep/.cell_red {
    color: #d63031;
  }
}
</style>
