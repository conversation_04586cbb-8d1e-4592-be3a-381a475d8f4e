let commonThead = {
  clsName: '项目分类',
  combCode: '组合代码',
  combName: '组合名称',
  price: '单价',
  hisCode: 'his代码'
};
export default {
  data() {
    return {
      menuList: [
        {
          id: '1',
          title: '体检类代码',
          icon: require('@/assets/img/serviceDataIcon/classify_gray.png'),
          children: [
            {
              id: '1-1',
              title: '体检组合-项目对应信息',
              icon: require('@/assets/img/serviceDataIcon/chart_gray.png'),
              path: 'classifyInformation',
              children: [],
              viewConfig: {
                tableComponent: 'MergeTable',
                mergeKeys: ['clsName', 'combName', 'combCode'], // 需要合并的字段
                cell_blue: ['clsName', 'combName', 'itemName'], // 蓝色字体的字段
                cell_red: [], // 红色字体的字段
                theads: {
                  clsName: '项目分类',
                  combCode: '组合代码',
                  combName: '组合名称',
                  itemCode: '项目代码',
                  itemName: '项目名称'
                },
                apiUrls: {
                  search: '/search/search',
                  del: '/CodeMapping/CD_ItemComb/Delete',
                  set: '/set/set',
                  viewTableList: '/CodeMapping/pageQuery_ItemComb'
                },
                order: `item.clsName?.includes(searchVal) ||
                item.clsName?.includes(searchVal) ||
                                    item.combName?.includes(searchVal) ||
                                    item.itemCode?.includes(searchVal)|| 
                                    item.itemName?.includes(searchVal)
                                `,
                orderSel: `item.clsName?.includes(searchVal)`
              },
              drawerConfig: {
                titleList: [
                  '体检组合-项目对应信息设置',
                  '体检组合项目列表',
                  '体检项目列表',
                  '组合-项目对应列表'
                ],
                leftTable: {
                  tableComponent: 'MergeTable',
                  mergeKeys: ['clsName'],
                  cell_blue: [], // 蓝色字体的字段
                  cell_red: ['price'], // 红色字体的字段
                  isSelect: false, // 是否显示选择框
                  isSearch: true, // 是否显示搜索框
                  theads: commonThead,
                  order: `item.clsName?.includes(searchVal) ||
                  item.combCode?.includes(searchVal) ||
                  item.combName?.includes(searchVal) 
                  `,
                  leftOrder: `combCode`
                },
                rightTable: {
                  tableComponent: 'MergeTable',
                  mergeKeys: ['clsName'],
                  isSelect: true, // 是否显示选择框
                  isSearch: true, // 是否显示搜索框
                  cell_blue: [], // 蓝色字体的字段
                  cell_red: [], // 红色字体的字段
                  theads: {
                    clsName: '项目分类',
                    itemCode: '项目代码',
                    itemName: '项目名称'
                  },
                  order: `item.clsName?.includes(searchVal) ||
                  item.itemCode?.includes(searchVal)|| 
                  item.itemName?.includes(searchVal)
                  `,
                  orderSel: `item.clsName?.includes(searchVal)`
                },
                apiUrls: {
                  search: '/search/search',
                  del: '/CodeMapping/CD_ItemComb/Delete',
                  set: '/CodeMapping/CD_ItemComb/Create',
                  viewTableList: '/viewTableList/viewTableList',
                  tableList1: '/EnumData/ItemComb_ItemCls',
                  tableList2: '/EnumData/Item_ItemCls',
                  optionList: '/EnumData/ItemCls'
                },
                leftParam: {
                  combCode: 'combCode'
                },
                rightParam: {
                  itemCode: 'itemCode',
                  clsCode: 'clsCode'
                }
              }
            },
            {
              id: '1-2',
              title: '体检套餐-组合对应信息',
              icon: require('@/assets/img/serviceDataIcon/chart_gray.png'),
              path: 'barcodeClassification',
              children: [],
              viewConfig: {
                tableComponent: 'TotalTable',
                mergeKeys: ['clusCode', 'clusName', 'totalPrice'], // 需要合并的字段
                cell_blue: ['clusName', 'combName'], // 蓝色字体的字段
                cell_red: ['price', 'discount', 'totalPrice'], // 红色字体的字段
                theads: {
                  clusCode: '套餐代码',
                  clusName: '套餐名称',
                  combCode: '组合代码',
                  combName: '组合名称',
                  price: '组合单价',
                  discount: '组合折扣',
                  count: '数量',
                  totalPrice: '合计'
                },
                apiUrls: {
                  search: '/search/search',
                  del: '/CodeMapping/CD_ClusterComb/Delete',
                  set: '/set/set',
                  viewTableList: '/CodeMapping/Query_ClusterComb'
                },
                order: `item.clusCode?.includes(searchVal) ||

                                    item.clusName?.includes(searchVal) ||
                                    item.combCode?.includes(searchVal) ||
                                    item.combName?.includes(searchVal)
                                `,
                orderSel: `item.combName?.includes(searchVal)`
              },
              drawerConfig: {
                isNumber: true,
                isDiscount: true,
                titleList: [
                  '体检套餐-组合对应信息设置',
                  '套餐体检列表',
                  '体检组合项目列表',
                  '套餐-组合对应列表'
                ],
                leftTable: {
                  tableComponent: 'MergeTable',
                  mergeKeys: [],
                  cell_blue: [], // 蓝色字体的字段
                  cell_red: ['price'], // 红色字体的字段
                  isSelect: false, // 是否显示选择框
                  isSearch: true, // 是否显示搜索框
                  theads: {
                    clusCode: '套餐代码',
                    clusName: '套餐名称',
                    price: '单价'
                  },
                  order: `item.clusCode?.includes(searchVal) ||
                                      item.clusName?.includes(searchVal)
                                  `,
                  leftOrder: `clusCode`
                },
                rightTable: {
                  tableComponent: 'MergeTable',
                  mergeKeys: ['clsName'],
                  cell_blue: [], // 蓝色字体的字段
                  cell_red: ['price'], // 红色字体的字段
                  isSelect: false, // 是否显示选择框
                  isSearch: true, // 是否显示搜索框
                  isCheck: true,
                  theads: commonThead,
                  order: `item.clsName?.includes(searchVal) ||
                  item.combCode?.includes(searchVal) ||
                  item.combName?.includes(searchVal)
              `,
                  orderSel: `item.clsName?.includes(searchVal)`
                },
                apiUrls: {
                  search: '/search/search',
                  del: '/CodeMapping/CD_ClusterComb/Delete',
                  set: '/CodeMapping/CD_ClusterComb/Create',
                  viewTableList: '/CodeMapping/Query_ClusterComb',
                  tableList1: '/EnumData/Cluster',
                  tableList2: '/EnumData/ItemComb_ItemCls',
                  optionList: ''
                },
                leftParam: {
                  clusCode: 'clusCode'
                },
                rightParam: {
                  combCode: 'combCode'
                  //   discount: 0,
                  //   count: 0
                }
              }
            },
            {
              id: '1-3',
              title: '计算项目公式信息',
              icon: require('@/assets/img/serviceDataIcon/chart_gray.png'),
              path: 'referenceRangeInfo',
              children: [],
              viewConfig: {
                tableComponent: 'MergeTable',
                mergeKeys: ['clsName'], // 需要合并的字段
                cell_blue: ['itemName'], // 蓝色字体的字段
                cell_red: [], // 红色字体的字段
                theads: {
                  clsName: '项目分类',
                  itemCode: '项目代码',
                  itemName: '项目名称',
                  expressionShow: '表达式'
                },
                apiUrls: {
                  search: '/search/search',
                  del: '/CodeMapping/CD_ItemResultExpression/Delete',
                  set: '/set/set',
                  viewTableList: '/CodeMapping/PageQuery_ItemResultExpression'
                },
                order: `item.clsName?.includes(searchVal) ||
                                    item.itemCode?.includes(searchVal) ||
                                    item.itemName?.includes(searchVal) 
                                `,
                orderSel: `item.combName?.includes(searchVal)`
              },
              drawerConfig: {
                titleList: [
                  '计算项目公式信息设置',
                  '体检项目列表',
                  '',
                  '计算项目定义列表'
                ],
                leftTable: {
                  tableComponent: 'MergeTable',
                  cell_blue: [], // 蓝色字体的字段
                  cell_red: [], // 红色字体的字段
                  isSelect: true, // 是否显示选择框
                  isSearch: true, // 是否显示搜索框
                  mergeKeys: ['clsName'],
                  theads: {
                    clsName: '分类名称',
                    combCode: '项目代码',
                    combName: '项目名称'
                  },
                  order: `item.combCode?.includes(searchVal) ||
                item.combName?.includes(searchVal) ||
                item.clsName?.includes(searchVal)
            `,
                  orderSel: `item.clsName?.includes(searchVal)`,
                  leftOrder: `combCode`
                },
                rightTable: {
                  tableComponent: 'MergeTable',
                  mergeKeys: ['itemCls'],
                  cell_blue: [], // 蓝色字体的字段
                  cell_red: ['price'], // 红色字体的字段
                  isSelect: true, // 是否显示选择框
                  isSearch: false, // 是否显示搜索框
                  theads: {},
                  order: ``
                },
                apiUrls: {
                  search: '/search/search',
                  del: '/CodeMapping/CD_ItemResultExpression/Delete',
                  set: '/CodeMapping/CD_ItemResultExpression/Create',
                  viewTableList: '/viewTableList/viewTableList',
                  tableList1: '/EnumData/ItemComb_ItemCls',
                  tableList2: '/EnumData/ItemComb_ItemCls',
                  optionList: '/EnumData/Item',
                  expressionVerify: '/CodeMapping/ItemResultExpressionVerify'
                },
                leftParam: {
                  itemCode: 'combCode'
                },
                rightParam: {
                  // expression: "expression"
                }
              }
            },
            {
              id: '1-4',
              title: '档案项目对应信息',
              icon: require('@/assets/img/serviceDataIcon/chart_gray.png'),
              path: 'physicalExaminationItem',
              children: [],
              viewConfig: {
                tableComponent: 'MergeTable',
                mergeKeys: ['clsName'],
                cell_blue: ['archiveName', 'itemName'], // 蓝色字体的字段
                cell_red: [], // 红色字体的字段
                theads: {
                  clsName: '项目分类',
                  archiveCode: '档案项目代码',
                  archiveName: '档案项目名称',
                  itemCode: '体检项目代码',
                  itemName: '体检项目名称'
                },
                apiUrls: {
                  search: '/search/search',
                  del: '/CodeMapping/CD_MapArchiveItem/Delete',
                  set: '/set/set',
                  viewTableList: '/CodeMapping/PageQuery_MapArchiveItem'
                },
                order: `item.clsName?.includes(searchVal) ||
                                    item.archiveCode?.includes(searchVal) ||
                                    item.archiveName?.includes(searchVal) ||
                                    item.itemCode?.includes(searchVal) ||
                                    item.itemName?.includes(searchVal)
                                `,
                orderSel: `item.clsName?.includes(searchVal)`
              },
              drawerConfig: {
                titleList: [
                  '档案项目对应信息设置',
                  '体检组合项目列表',
                  '体检对应组合列表',
                  '组合-组合对应列表'
                ],
                leftTable: {
                  tableComponent: 'MergeTable',
                  cell_blue: [], // 蓝色字体的字段
                  cell_red: [], // 红色字体的字段
                  isSelect: false, // 是否显示选择框
                  isSearch: true, // 是否显示搜索框
                  mergeKeys: [],
                  theads: {
                    itemCode: '档案项目代码',
                    itemName: '档案项目名称'
                  },
                  order: `item.itemCode?.includes(searchVal) ||
                item.itemName?.includes(searchVal)
            `,
                  leftOrder: `itemCode`
                },
                rightTable: {
                  tableComponent: 'MergeTable',
                  mergeKeys: ['clsName'],
                  cell_blue: [], // 蓝色字体的字段
                  cell_red: [], // 红色字体的字段
                  isSelect: true, // 是否显示选择框
                  isSearch: true, // 是否显示搜索框
                  theads: {
                    clsName: '项目分类',
                    itemCode: '项目代码',
                    itemName: '项目名称'
                  },
                  order: `item.clsName?.includes(searchVal) ||
                item.itemCode?.includes(searchVal) ||
                item.itemName?.includes(searchVal)
            `,
                  orderSel: `item.clsName?.includes(searchVal)`
                },
                apiUrls: {
                  search: '/search/search',
                  del: '/CodeMapping/CD_MapArchiveItem/Delete',
                  set: '/CodeMapping/CD_MapArchiveItem/Create',
                  viewTableList: '/viewTableList/viewTableList',
                  tableList1: '/EnumData/ArchiveItem',
                  tableList2: '/EnumData/Item_ItemCls',
                  optionList: ''
                },
                leftParam: {
                  archiveCode: 'itemCode',
                  archiveName: 'itemName'
                },
                rightParam: {
                  itemCode: 'itemCode'
                }
              }
            },
            {
              id: '1-5',
              title: '体检自动添加组合对应信息',
              icon: require('@/assets/img/serviceDataIcon/file_open_gray.png'),
              children: [],
              viewConfig: {
                tableComponent: 'MergeTable',
                mergeKeys: ['clsName', 'combCode', 'combName'],
                cell_blue: ['combName', 'childCombName'], // 蓝色字体的字段
                cell_red: [], // 红色字体的字段
                show1: true,
                theads: {
                  clsName: '项目分类',
                  combCode: '组合代码',
                  combName: '组合名称',
                  appendCombCode: '对应组合代码',
                  appendCombName: '对应组合名称',
                  count: '数量'
                },
                apiUrls: {
                  search: '/search/search',
                  del: '/CodeMapping/CUD_CodeCombComb/Delete',
                  set: '/set/set',
                  viewTableList: '/CodeMapping/PageQuery_CodeCombComb'
                },
                order: `item.clsName?.includes(searchVal) ||
                                    item.combCode?.includes(searchVal) ||
                                    item.combName?.includes(searchVal) ||
                                    item.appendCombCode?.includes(searchVal) ||
                                    item.appendCombName?.includes(searchVal)
                                `,
                orderSel: `item.clsName?.includes(searchVal)`
              },
              drawerConfig: {
                isNumber: true,
                titleList: [
                  '体检自动添加组合对应信息设置',
                  '体检组合项目列表',
                  '体检对应组合列表',
                  '组合-组合对应列表'
                ],
                leftTable: {
                  tableComponent: 'MergeTable',
                  cell_blue: [], // 蓝色字体的字段
                  cell_red: ['price'], // 红色字体的字段
                  isSelect: false, // 是否显示选择框
                  isSearch: true, // 是否显示搜索框
                  mergeKeys: ['clsName'],
                  theads: commonThead,
                  order: `item.clsName?.includes(searchVal) ||
                                        item.combCode?.includes(searchVal) ||
                                        item.combName?.includes(searchVal)
                                    `,
                  leftOrder: `combCode`
                },
                rightTable: {
                  tableComponent: 'MergeTable',
                  mergeKeys: ['clsName'],
                  cell_blue: [], // 蓝色字体的字段
                  cell_red: ['price'], // 红色字体的字段
                  isSelect: true, // 是否显示选择框
                  isSearch: true, // 是否显示搜索框
                  theads: commonThead,
                  order: `item.clsName?.includes(searchVal) ||
                                    item.combCode?.includes(searchVal) ||
                                    item.combName?.includes(searchVal)
                                    `,
                  orderSel: `item.clsName?.includes(searchVal)`
                },
                apiUrls: {
                  search: '/search/search',
                  del: '/CodeMapping/CUD_CodeCombComb/Delete',
                  set: '/CodeMapping/CUD_CodeCombComb/Create',
                  viewTableList: '/viewTableList/viewTableList',
                  tableList1: '/EnumData/ItemComb_ItemCls',
                  tableList2: '/EnumData/ItemComb_ItemCls',
                  optionList: '/EnumData/ItemCls'
                },
                leftParam: {
                  combCode: 'combCode'
                },
                rightParam: {
                  appendCombCode: 'combCode'
                }
              }
            },
            {
              id: '1-6',
              title: '医生跨科关系设置',
              icon: require('@/assets/img/serviceDataIcon/chart_gray.png'),
              path: 'physicalExaminationSetMeal',
              children: [],
              viewConfig: {
                tableComponent: 'MergeTable',
                mergeKeys: [],
                cell_blue: ['name', 'deptName'], // 蓝色字体的字段
                cell_red: [], // 红色字体的字段
                theads: {
                  operatorCode: '人员编号',
                  name: '用户名称',
                  deptCode: '科室代码',
                  deptName: '科室名称'
                },
                apiUrls: {
                  search: '/search/search',
                  del: '/CodeMapping/CD_MapDoctorDept/Delete',
                  set: '/set/set',
                  viewTableList: '/CodeMapping/PageQuery_MapDoctorDept'
                },
                order: `item.operatorCode?.includes(searchVal) ||
                                    item.name?.includes(searchVal) ||
                                    item.deptCode?.includes(searchVal) ||
                                    item.deptName?.includes(searchVal)
                                `,
                orderSel: `item.deptName?.includes(searchVal)`
              },
              drawerConfig: {
                titleList: [
                  '医生跨科关系设置设置',
                  '医生列表',
                  '科室列表',
                  '医生跨科关系设置列表'
                ],
                leftTable: {
                  tableComponent: 'MergeTable',
                  mergeKeys: [],
                  cell_blue: [], // 蓝色字体的字段
                  cell_red: [], // 红色字体的字段
                  isSelect: false, // 是否显示选择框
                  isSearch: true, // 是否显示搜索框
                  theads: {
                    operatorCode: '人员编号',
                    name: '姓名',
                    deptName: '科室名称'
                  },
                  order: `item.operatorCode?.includes(searchVal) ||
                  item.name?.includes(searchVal) ||
                  item.deptName?.includes(searchVal)
              `,
                  leftOrder: `operatorCode`
                },
                rightTable: {
                  tableComponent: 'MergeTable',
                  mergeKeys: [],
                  cell_blue: [], // 蓝色字体的字段
                  cell_red: [], // 红色字体的字段
                  isSelect: false, // 是否显示选择框
                  isSearch: true, // 是否显示搜索框
                  theads: {
                    deptCode: '科室代码',
                    deptName: '科室名称'
                  },
                  order: `item.deptCode?.includes(searchVal) ||
                  item.deptName?.includes(searchVal)
              `
                },
                apiUrls: {
                  search: '/search/search',
                  del: '/CodeMapping/CD_MapDoctorDept/Delete',
                  set: '/CodeMapping/CD_MapDoctorDept/Create',
                  viewTableList: '/viewTableList/viewTableList',
                  tableList1: '/EnumData/Operator',
                  tableList2: '/EnumData/Department',
                  optionList: '/EnumData/ItemCls'
                },
                leftParam: {
                  operCode: 'operatorCode'
                },
                rightParam: {
                  deptCode: 'deptCode'
                }
              }
            },
            {
              id: '1-7',
              title: '套餐与组合互斥信息',
              icon: require('@/assets/img/serviceDataIcon/chart_gray.png'),
              path: 'archiveItems',
              children: [],
              viewConfig: {
                tableComponent: 'MergeTable',
                mergeKeys: ['clusCode', 'clusName'],
                cell_blue: ['clusName', 'combName'], // 蓝色字体的字段
                cell_red: [], // 红色字体的字段
                theads: {
                  clusCode: '套餐代码',
                  clusName: '套餐名称',
                  combCode: '组合代码',
                  combName: '组合名称'
                },
                apiUrls: {
                  search: '/search/search',
                  del: '/CodeMapping/CD_ClusMutexComb/Delete',
                  set: '/set/set',
                  viewTableList: '/CodeMapping/PageQuery_ClusMutexComb'
                },
                order: `item.clusCode?.includes(searchVal) ||
                                    item.clusName?.includes(searchVal) ||
                                    item.combCode?.includes(searchVal) ||
                                    item.combName?.includes(searchVal)
                                `,
                orderSel: `item.clusName?.includes(searchVal)`
              },
              drawerConfig: {
                titleList: [
                  '套餐与组合互斥信息设置',
                  '体检套餐列表',
                  '体检组合列表',
                  '套餐与组合互斥列表'
                ],
                leftTable: {
                  tableComponent: 'MergeTable',
                  mergeKeys: [],
                  cell_blue: [], // 蓝色字体的字段
                  cell_red: ['price'], // 红色字体的字段
                  isSelect: false, // 是否显示选择框
                  isSearch: true, // 是否显示搜索框
                  theads: {
                    clusCode: '套餐代码',
                    clusName: '套餐名称',
                    price: '单价'
                  },
                  order: `item.clusCode?.includes(searchVal) ||
                  item.clusName?.includes(searchVal)
              `,
                  leftOrder: `clusCode`
                },
                rightTable: {
                  tableComponent: 'MergeTable',
                  mergeKeys: ['clsName'],
                  cell_blue: [], // 蓝色字体的字段
                  cell_red: ['price'], // 红色字体的字段
                  isSelect: false, // 是否显示选择框
                  isSearch: true, // 是否显示搜索框
                  theads: commonThead,
                  order: `item.clsName?.includes(searchVal) ||
                  item.combCode?.includes(searchVal) ||
                  item.combName?.includes(searchVal)
              `,
                  orderSel: `item.clsName?.includes(searchVal)`
                },
                apiUrls: {
                  search: '/search/search',
                  del: '/CodeMapping/CD_ClusMutexComb/Delete',
                  set: '/CodeMapping/CD_ClusMutexComb/Create',
                  viewTableList: '/viewTableList/viewTableList',
                  tableList1: '/EnumData/Cluster',
                  tableList2: '/EnumData/ItemComb_ItemCls',
                  optionList: '/EnumData/ItemCls'
                },
                leftParam: {
                  clusCode: 'clusCode'
                },
                rightParam: {
                  combCode: 'combCode'
                }
              }
            },
            {
              id: '1-8',
              title: '检验组合对应',
              icon: require('@/assets/img/serviceDataIcon/chart_gray.png'),
              path: 'conclusion',
              children: [],
              viewConfig: {
                tableComponent: 'MergeTable',
                mergeKeys: ['clsName', 'roomCode', 'roomName'], // 需要合并的字段
                cell_blue: ['combName', 'groupName'], // 蓝色字体的字段
                cell_red: [], // 红色字体的字段
                theads: {
                  clsName: '项目分类名称',
                  combCode: '体检组合代码',
                  combName: '体检组合名称',
                  roomCode: '专业组代码',
                  roomName: '专业组名称',
                  groupCode: '检验组合代码',
                  groupName: '检验组合名称'
                },
                apiUrls: {
                  search: '/search/search',
                  del: '/CodeMapping/CD_MapCombGroup/Delete',
                  set: '/set/set',
                  viewTableList: '/CodeMapping/PageQuery_MapCombGroup'
                },
                order: `item.clsName?.includes(searchVal) ||
                                    item.combCode?.includes(searchVal) ||
                                    item.combName?.includes(searchVal) ||
                                    item.roomCode?.includes(searchVal) ||
                                    item.roomName?.includes(searchVal) ||
                                    item.groupCode?.includes(searchVal) ||
                                    item.groupName?.includes(searchVal)
                                `,
                orderSel: `item.clsName?.includes(searchVal)`
              },
              drawerConfig: {
                titleList: [
                  '检验组合对应设置',
                  '体检组合列表',
                  '检验组合列表',
                  '检验组合对应列表'
                ],
                leftTable: {
                  tableComponent: 'MergeTable',
                  mergeKeys: ['clsName'],
                  cell_blue: [], // 蓝色字体的字段
                  cell_red: [], // 红色字体的字段
                  isSelect: false, // 是否显示选择框
                  isSearch: true, // 是否显示搜索框
                  theads: {
                    clsName: '项目分类名称',
                    combCode: '体检组合代码',
                    combName: '体检组合名称'
                  },
                  order: `item.clsName?.includes(searchVal) ||
                  item.combCode?.includes(searchVal) ||
                  item.combName?.includes(searchVal)
              `,
                  leftOrder: `combCode`
                },
                rightTable: {
                  tableComponent: 'MergeTable',
                  mergeKeys: [],
                  cell_blue: [], // 蓝色字体的字段
                  cell_red: [], // 红色字体的字段
                  isSelect: false, // 是否显示选择框
                  isSearch: true, // 是否显示搜索框
                  theads: {
                    groupName: '专业组名称',
                    roomCode: '检验组合代码',
                    roomName: '检验组合名称'
                  },
                  order: `item.groupName?.includes(searchVal) ||
                  item.roomCode?.includes(searchVal)||
                  item.roomName?.includes(searchVal)
              `,
                  orderSel: `item.clsName?.includes(searchVal)`
                },
                apiUrls: {
                  search: '/search/search',
                  del: '/CodeMapping/CD_MapCombGroup/Delete',
                  set: '/CodeMapping/CD_MapCombGroup/Create',
                  viewTableList: '/viewTableList/viewTableList',
                  tableList1: '/EnumData/ItemComb_ItemCls',
                  tableList2: '',
                  optionList: '/EnumData/ItemCls'
                }
                // leftParam: {
                //   combCode: "combCode",
                //   combName: "combName"
                //   groupCode: "string",
                //   groupName: "string",
                //   roomCode: "string",
                //   roomName: "string"
                // }
                // rightParam: {
                // }
              }
            },
            {
              id: '1-9',
              title: '检验对应',
              icon: require('@/assets/img/serviceDataIcon/chart_gray.png'),
              path: 'normalResult',
              children: [],
              viewConfig: {
                tableComponent: 'MergeTable',
                mergeKeys: ['clsName', 'roomCode', 'roomName'], // 需要合并的字段
                cell_blue: ['itemName', 'lisItemName'], // 蓝色字体的字段
                cell_red: [], // 红色字体的字段
                theads: {
                  clsName: '项目分类名称',
                  itemCode: '体检项目代码',
                  itemName: '体检项目名称',
                  roomCode: '专业组代码',
                  roomName: '专业组名称',
                  lisItemCode: '检验项目代码',
                  lisItemName: '检验项目名称'
                },
                apiUrls: {
                  search: '/search/search',
                  del: '/CodeMapping/CD_MapItemLisItem/Delete',
                  set: '/set/set',
                  viewTableList: '/CodeMapping/PageQuery_MapItemLisItem'
                },
                order: `item.clsName?.includes(searchVal) ||
                                    item.itemCode?.includes(searchVal) ||
                                    item.itemName?.includes(searchVal) ||
                                    item.roomCode?.includes(searchVal) ||
                                    item.roomName?.includes(searchVal) ||
                                    item.lisItemCode?.includes(searchVal) ||
                                    item.lisItemName?.includes(searchVal)
                                `,
                orderSel: `item.clsName?.includes(searchVal)`
              },
              drawerConfig: {
                titleList: [
                  '检验组合对应设置',
                  '体检项目列表',
                  '检验项目列表',
                  '检验对应列表'
                ],
                leftTable: {
                  tableComponent: 'MergeTable',
                  mergeKeys: ['clsName'],
                  cell_blue: [], // 蓝色字体的字段
                  cell_red: [], // 红色字体的字段
                  isSelect: false, // 是否显示选择框
                  isSearch: true, // 是否显示搜索框
                  theads: {
                    clsName: '项目分类名称',
                    itemCode: '体检项目代码',
                    itemName: '体检项目名称'
                  },
                  order: `item.clsName?.includes(searchVal) ||
                                    item.itemCode?.includes(searchVal) ||
                                    item.itemName?.includes(searchVal)
                                    `,
                  orderSel: `item.clsName?.includes(searchVal)`,
                  leftOrder: `itemCode`
                },
                rightTable: {
                  tableComponent: 'MergeTable',
                  mergeKeys: [],
                  cell_blue: [], // 蓝色字体的字段
                  cell_red: [], // 红色字体的字段
                  isSelect: false, // 是否显示选择框
                  isSearch: true, // 是否显示搜索框
                  theads: {
                    lisItemName: '专业组名称',
                    roomCode: '检验项目代码',
                    roomName: '检验项目名称'
                  },
                  order: `item.lisItemName?.includes(searchVal) ||
                  item.roomCode?.includes(searchVal) ||
                  item.roomName?.includes(searchVal)
              `,
                  orderSel: `item.clsName?.includes(searchVal)`
                },
                apiUrls: {
                  search: '/search/search',
                  del: '/CodeMapping/CD_MapItemLisItem/Delete',
                  set: '/CodeMapping/CD_MapItemLisItem/Create',
                  viewTableList: '/viewTableList/viewTableList',
                  tableList1: '/EnumData/Item_ItemCls',
                  tableList2: ''
                }
                // leftParam: {
                //   itemCode: "string",
                //   itemName: "string",
                //   lisItemCode: "string",
                //   lisItemName: "string",
                //   roomCode: "string",
                //   roomName: "string"
                // },
                // rightParam: {
                // }
              }
            },
            {
              id: '1-10',
              title: '影像对应',
              icon: require('@/assets/img/serviceDataIcon/chart_gray.png'),
              path: 'normalResult',
              children: [],
              viewConfig: {
                tableComponent: 'MergeTable',
                mergeKeys: [], // 需要合并的字段
                cell_blue: ['itemName'], // 蓝色字体的字段
                cell_red: [], // 红色字体的字段
                theads: {
                  clsName: '项目分类',
                  itemCode: '体检项目代码',
                  itemName: '体检项目名称',
                  risClsCode: '影像分类代码',
                  risClsName: '影像分类名称',
                  partCode: '影像部位代码',
                  partName: '影像部位名称'
                },
                apiUrls: {
                  search: '/search/search',
                  del: '/CodeMapping/CD_MapItemRisPart/Delete',
                  set: '/CodeMapping/CD_MapItemRisPart/Create',
                  viewTableList: '/CodeMapping/PageQuery_MapItemRisPart'
                },
                order: `item.clsName?.includes(searchVal) ||
                                    item.itemCode?.includes(searchVal) ||
                                    item.itemName?.includes(searchVal) ||
                                    item.risClsCode?.includes(searchVal) ||
                                    item.risClsName?.includes(searchVal) ||
                                    item.partCode?.includes(searchVal) ||
                                    item.partName?.includes(searchVal)
                                `,
                orderSel: `item.clsName?.includes(searchVal)`
              },
              drawerConfig: {
                titleList: [
                  '影像对应设置',
                  '体检项目列表',
                  '影像分类列表',
                  '检查部位列表',
                  '影像对应列表'
                ],
                leftTable: {
                  tableComponent: 'MergeTable',
                  mergeKeys: ['clsName'],
                  cell_blue: [], // 蓝色字体的字段
                  cell_red: [], // 红色字体的字段
                  isSelect: false, // 是否显示选择框
                  isSearch: true, // 是否显示搜索框
                  theads: {
                    clsName: '分类名称',
                    itemCode: '项目代码',
                    itemName: '项目名称'
                  },
                  order: `item.clsName?.includes(searchVal) ||
                                      item.itemCode?.includes(searchVal) ||
                                      item.itemName?.includes(searchVal)
                                  `,
                  leftOrder: `itemCode`
                },
                rightTable1: {
                  tableComponent: 'MergeTable',
                  mergeKeys: [],
                  cell_blue: [], // 蓝色字体的字段
                  cell_red: [], // 红色字体的字段
                  theads: {
                    majorGroupName: '分类代码',
                    testName: '名称'
                  },
                  order: `item.majorGroupName?.includes(searchVal) ||
                                      item.testName?.includes(searchVal)
                                  `
                },
                rightTable2: {
                  tableComponent: 'MergeTable',
                  mergeKeys: [],
                  cell_blue: [], // 蓝色字体的字段
                  cell_red: [], // 红色字体的字段
                  theads: {
                    majorGroupName: '部位代码',
                    testName: '部位名称'
                  }
                },
                apiUrls: {
                  search: '/search/search',
                  del: '/CodeMapping/CD_MapItemRisPart/Delete',
                  set: '/CodeMapping/CD_MapItemRisPart/Create',
                  viewTableList: '/viewTableList/viewTableList',
                  tableList1: '/EnumData/Item_ItemCls',
                  tableList2: ''
                }
                // leftParam: {
                //   itemCode: "itemCode",
                //   itemName: "itemName",
                //   lisItemCode: "string",
                //   lisItemName: "string",
                //   roomCode: "string",
                //   roomName: "string"
                // },
                // rightParam: {
                // }
              }
            }
          ]
        },

        {
          id: '2',
          title: '收费类代码',
          icon: require('@/assets/img/serviceDataIcon/classify_gray.png'),
          children: [
            {
              id: '2-1',
              title: '项目分类与基础分类对应信息',
              icon: require('@/assets/img/serviceDataIcon/chart_gray.png'),
              path: 'basicInformation',
              children: [],
              viewConfig: {
                tableComponent: 'MergeTable',
                mergeKeys: ['fitemClsType', 'fitemClsCode', 'fitemClsName'], // 需要合并的字段
                cell_blue: ['fitemClsName', 'basicClsName'], // 蓝色字体的字段
                cell_red: [], // 红色字体的字段
                theads: {
                  fitemClsType: '项目分类类型',
                  fitemClsCode: '项目分类代码',
                  fitemClsName: '项目分类名称',
                  basicClsCode: '基础分类代码',
                  basicClsName: '基础分类名称'
                },
                apiUrls: {
                  search: '/search/search',
                  del: '/CodeMapping/CD_MapFitemClsBasicCls/Delete',
                  set: '/set/set',
                  viewTableList: '/CodeMapping/PageQuery_MapFitemClsBasicCls'
                },
                order: `item.fitemClsType?.includes(searchVal) ||
                                    item.fitemClsCode?.includes(searchVal) ||
                                    item.fitemClsName?.includes(searchVal) ||
                                    item.basicClsCode?.includes(searchVal) ||
                                    item.basicClsName?.includes(searchVal)
                                `
              },
              drawerConfig: {
                titleList: [
                  '项目分类与基础分类对应信息设置',
                  '项目分类列表',
                  '基础分类列表',
                  '项目分类-基础分类对应列表'
                ],
                leftTable: {
                  tableComponent: 'MergeTable',
                  mergeKeys: ['clsType'],
                  cell_blue: [], // 蓝色字体的字段
                  cell_red: [], // 红色字体的字段
                  isSelect: false, // 是否显示选择框
                  isSearch: false, // 是否显示搜索框
                  theads: {
                    clsType: '项目分类类型',
                    clsCode: '项目分类代码',
                    clsName: '项目分类名称'
                  },
                  order: `item.clsType?.includes(searchVal) ||
                                      item.clsCode?.includes(searchVal) ||
                                      item.clsName?.includes(searchVal)
                                  `,
                  leftOrder: `clsCode`
                },
                rightTable: {
                  tableComponent: 'MergeTable',
                  mergeKeys: [],
                  cell_blue: [], // 蓝色字体的字段
                  cell_red: [], // 红色字体的字段
                  isSelect: false, // 是否显示选择框
                  isSearch: true, // 是否显示搜索框
                  theads: {
                    clsCode: '基础分类代码',
                    clsName: '基础分类名称'
                  },
                  order: `item.clsCode?.includes(searchVal) ||
                                      item.clsName?.includes(searchVal)
                                  `
                },
                apiUrls: {
                  search: '/search/search',
                  del: '/CodeMapping/CD_MapFitemClsBasicCls/Delete',
                  set: '/CodeMapping/CD_MapFitemClsBasicCls/Create',
                  viewTableList: '/viewTableList/viewTableList',
                  tableList1: '/BasicCode/RD_CodeFitemCls/Read',
                  tableList2: '/BasicCode/RD_CodeBasicCls/Read'
                },
                leftParam: {
                  fitemClsCode: 'clsCode',
                  fitemClsName: 'clsName',
                  fitemClsType: 'clsType'
                },
                rightParam: {
                  basicClsCode: 'clsCode',
                  basicClsName: 'clsName'
                }
              }
            },
            {
              id: '2-2',
              title: '费别-支付方式对应信息',
              icon: require('@/assets/img/serviceDataIcon/chart_gray.png'),
              path: 'projectInformation',
              children: [],
              viewConfig: {
                tableComponent: 'MergeTable',
                mergeKeys: ['feeCode', 'feeName'], // 需要合并的字段
                cell_blue: ['feeName', 'payName'], // 蓝色字体的字段
                cell_red: [], // 红色字体的字段
                theads: {
                  feeCode: '费别代码',
                  feeName: '费别名称',
                  payCode: '支付方式代码',
                  payName: '支付方式名称'
                },
                apiUrls: {
                  search: '/search/search',
                  del: '/CodeMapping/CD_MapFeePayment',
                  set: '/set/set',
                  viewTableList: '/CodeMapping/PageQuery_MapFeePayment'
                },
                order: `item.feeCode?.includes(searchVal) ||
                                    item.feeName?.includes(searchVal) ||
                                    item.payCode?.includes(searchVal) ||
                                    item.payName?.includes(searchVal) 
                                `
              },
              drawerConfig: {
                titleList: [
                  '费别-支付方式对应信息设置',
                  '费别分类列表',
                  '支付方式列表',
                  '费别-支付方式对应列表'
                ],
                leftTable: {
                  tableComponent: 'MergeTable',
                  mergeKeys: [],
                  cell_blue: [], // 蓝色字体的字段
                  cell_red: [], // 红色字体的字段
                  isSelect: false, // 是否显示选择框
                  isSearch: false, // 是否显示搜索框
                  theads: {
                    feeCode: '费别代码',
                    feeName: '费别名称'
                  },
                  order: `item.feeCode?.includes(searchVal) ||
                                      item.feeName?.includes(searchVal)
                                  `,
                  leftOrder: `feeCode`
                },
                rightTable: {
                  tableComponent: 'MergeTable',
                  mergeKeys: [],
                  cell_blue: [], // 蓝色字体的字段
                  cell_red: [], // 红色字体的字段
                  isSelect: false, // 是否显示选择框
                  isSearch: false, // 是否显示搜索框
                  theads: {
                    payCode: '支付方式代码',
                    payName: '支付方式名称'
                  },
                  order: `item.payCode?.includes(searchVal) ||
                                      item.payName?.includes(searchVal)
                                  `
                },
                apiUrls: {
                  search: '/search/search',
                  del: '/CodeMapping/CD_MapFeePayment/Delete',
                  set: '/CodeMapping/CD_MapFeePayment/Create',
                  viewTableList: '/viewTableList/viewTableList',
                  tableList1: '/BasicCode/RD_CodeFee/Read',
                  tableList2: '/BasicCode/RD_CodePayment/Read'
                },
                leftParam: {
                  feeCode: 'feeCode',
                  feeName: 'feeName',
                  payCode: 'string',
                  payName: 'string'
                },
                rightParam: {
                  payCode: 'payCode',
                  payName: 'payName'
                }
              }
            }
          ]
        }
      ]
    };
  }
};
