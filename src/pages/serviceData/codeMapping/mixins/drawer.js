export default {
  data() {
    return {
      drawer: false,
      drawerConfig: {}, //抽屉页配置
      drawerTableList: [], //抽屉页数据
      drawerTableList1: [], //左边数据
      searchTableList1: [], //筛选后的数据
      drawerTableList2: [], //右边数据
      searchTableList2: [], //筛选后的数据
      drawerTableList3: [], //下边数据
      searchTableList3: [], //筛选后的数据
      drawerOptionList: [] //项目分类下拉
    };
  },
  methods: {
    drawerClose() {
      this.drawer = false;
      this.$refs.drawers.inputLeft = '';
      this.$refs.drawers.selectLeft = '';
      this.$refs.drawers.inputRight = '';
      this.$refs.drawers.selectRight = '';
      this.$refs.drawers.inputLowerHalf = '';
      this.$refs.drawers.selectLowerHalf = '';
    },
    //获取左边表格数据
    getDrawerFilterTable1() {
      this.searchIpt = this.$refs.drawers.inputLeft; //获取组件文本框的值
      this.searchSel = this.$refs.drawers.selectLeft;
      console.log(this.searchIpt, this.searchSel);
      if (!this.searchIpt && !this.searchSel) {
        return (this.searchTableList1 = this.drawerTableList1);
      }
      var newTableData = [];
      if (this.searchSel) {
        this.drawerTableList1.map((item) => {
          let flag = new Function(
            'item',
            'searchVal',
            `return ${this.drawerConfig.leftTable.orderSel}`
          )(item, this.searchSel.trim());
          if (flag) {
            newTableData.push(item);
          }
        });
        this.searchTableList1 = newTableData;
      }
      if (this.searchIpt) {
        newTableData = [];
        this.drawerTableList1.map((item) => {
          let flag = new Function(
            'item',
            'searchVal',
            `return ${this.drawerConfig.leftTable.order}`
          )(item, this.searchIpt.trim());
          if (flag) {
            newTableData.push(item);
          }
        });
        this.searchTableList1 = newTableData;
        console.log(22, newTableData);
      }
    },
    getDrawerTableList1() {
      console.log(11);
      this.$ajax.post(this.drawerConfig.apiUrls.tableList1, []).then((r) => {
        let { returnData, success } = r.data;
        if (!success) return;
        returnData.map((item) => {
          if (item.clsType) {
            item.clsType = item.clsType === 0 ? '发票分类' : '财务分类';
          }
        });
        this.drawerTableList1 = returnData || [];
        this.searchTableList1 = returnData || [];
      });
    },
    //获取右边数据
    getDrawerFilterTable2() {
      this.searchIpt = this.$refs.drawers.inputRight; //获取组件文本框的值
      this.searchSel = this.$refs.drawers.selectRight; //获取下拉值
      console.log(this.searchIpt, this.searchSel);
      if (!this.searchIpt && !this.searchSel) {
        return (this.searchTableList2 = this.drawerTableList2);
      }
      var newTableData = [];
      if (this.searchSel) {
        this.drawerTableList2.map((item) => {
          let flag = new Function(
            'item',
            'searchVal',
            `return ${this.drawerConfig.rightTable.orderSel}`
          )(item, this.searchSel.trim());
          if (flag) {
            newTableData.push(item);
          }
        });
        this.searchTableList2 = newTableData;
      }

      if (this.searchIpt) {
        newTableData = [];
        this.searchTableList2.map((item) => {
          let flag = new Function(
            'item',
            'searchVal',
            `return ${this.drawerConfig.rightTable.order}`
          )(item, this.searchIpt.trim());
          if (flag) {
            newTableData.push(item);
          }
        });
        this.searchTableList2 = newTableData;
      }
    },
    getDrawerTableList2() {
      this.$ajax.post(this.drawerConfig.apiUrls.tableList2, []).then((r) => {
        let { returnData, success } = r.data;
        if (!success) return;
        this.drawerTableList2 = returnData || [];
        this.searchTableList2 = returnData || [];
      });
    },
    //获取下边的过滤数据
    getDrawerFilterTable3() {
      this.searchIpt = this.$refs.drawers.inputLowerHalf; //获取组件文本框的值
      this.searchSel = this.$refs.drawers.selectLowerHalf; //获取下拉值
      console.log(this.searchIpt, this.searchSel);
      if (!this.searchIpt && !this.searchSel) {
        return (this.searchTableList3 = this.drawerTableList3);
      }
      var newTableData = [];
      if (this.searchSel) {
        this.drawerTableList3.map((item) => {
          let flag = new Function(
            'item',
            'searchVal',
            `return ${this.viewConfig.orderSel}`
          )(item, this.searchSel.trim());
          if (flag) {
            newTableData.push(item);
          }
        });
        this.searchTableList3 = newTableData;
      }
      if (this.searchIpt) {
        newTableData = [];
        this.drawerTableList3.map((item) => {
          let flag = new Function(
            'item',
            'searchVal',
            `return ${this.viewConfig.order}`
          )(item, this.searchIpt.trim());
          if (flag) {
            newTableData.push(item);
          }
        });
        this.searchTableList3 = newTableData;
        console.log(444, newTableData);
      }
    },
    // 获取视图的表格数据
    getDrawerTableList3() {
      this.tableLoading = true;
      this.$ajax.post(this.viewConfig.apiUrls.viewTableList).then((r) => {
        this.tableLoading = false;
        let { returnData, success } = r.data;
        if (!success) return;
        returnData.map((item) => {
          if (item.fitemClsType) {
            item.fitemClsType =
              item.fitemClsType === 0 ? '发票分类' : '财务分类';
          }
        });
        this.drawerTableList3 = returnData || [];
        this.searchTableList3 = returnData || [];
        console.log('returnData: ', returnData);
      });
    },
    getDrawerDel() {
      this.$ajax.post(this.drawerConfig.apiUrls.del).then((r) => {
        if (!success) {
          return;
        }
      });
    },
    //获取项目分类下拉
    optionList() {
      this.$ajax.post(this.drawerConfig.apiUrls.optionList, []).then((r) => {
        let { returnData, success } = r.data;
        if (!success) return;
        this.drawerOptionList = returnData || [];
      });
    }
  }
};
