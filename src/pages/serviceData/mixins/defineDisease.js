import { mapGetters } from 'vuex';
export default {
  computed: {
    ...mapGetters(['G_diseaseGrade', 'G_codeItemCls'])
  },
  data() {
    return {
      form: {
        diseaseCode: '',
        diseaseName: '',
        deptCode: '',
        presentation: '',
        suggestContent: '',
        diseaseGrade: null,
        codeDiseaseEntry: [],
        CreatedFrom: 1
      },
      editDiseaseEntryList: [],
      formInpTagName: 'form_inp_tag',
      drawerInpTagName: 'drawer_inp_tag',
      inputValue: '',
      inputVisible: false,
      rules: {
        diseaseName: [
          { required: true, message: '请输入疾病名称', trigger: 'blur' }
        ],
        clsCode: [
          { required: true, message: '请选择项目分类', trigger: 'change' }
        ]
      },
      isEdit: false,
      loadFlag: false
    };
  },
  methods: {
    /**
     * @author: justin
     * @description: 表单、词条整理-词条 input发生改变
     * @param {*} item
     * @param {*} index
     * @return {*}
     */
    editEntryInputConfirm(arr, item, index, inpTagName) {
      const that = this;
      item.confirm = true;
      if (that.checkFormEntry(arr, item, index, inpTagName)) {
        that.$set(that.editDiseaseEntryList, index, false);
      }
    },
    /**
     * @author: justin
     * @description: 表单、词条整理-词条输入聚焦
     * @param {*} item
     * @param {*} index
     * @return {*}
     */
    editEntryInputFoucs(arr, item, index, inpTagName) {
      const that = this;
      item.confirm = false;
    },
    /**
     * @author: justin
     * @description: 表单、词条整理-词条 input失去焦点
     * @param {*} item
     * @param {*} index
     * @return {*}
     */
    editEntryInputBlur(arr, item, index, inpTagName) {
      const that = this;
      if (item.confirm || !arr.some((x) => x.entryText === item.entryText))
        return;

      if (that.checkFormEntry(arr, item, index, inpTagName)) {
        that.$set(that.editDiseaseEntryList, index, false);
      }
    },
    // 标签删除
    handleClose(tag) {
      this.$confirm(`是否确定要删除这个词条?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.form.codeDiseaseEntry.splice(
            this.form.codeDiseaseEntry.indexOf(tag),
            1
          );
          this.checkFormEntry(this.form.codeDiseaseEntry, null, -1, null);
        })
        .catch(() => {
          return false;
        });
    },
    /**
     * @author: justin
     * @description: 表单、词条整理-词条 input显示
     * @param {*} index
     * @return {*}
     */
    showEditEntryInput(arr, item, index, inpTagName) {
      this.$set(this.editDiseaseEntryList, index, true);
      this.$nextTick((_) => {
        const editTagInput = `${inpTagName}${index}`;
        this.$refs[editTagInput][0].$refs.input.focus();
        arr[index].entryTextOld = arr[index].entryText;
      });
    },
    // 输入框提交
    handleInputConfirm() {
      let inputValue = this.inputValue;
      if (inputValue) {
        let item = {
          diseaseCode: this.form.diseaseCode,
          entryText: inputValue
        };
        this.form.codeDiseaseEntry.push(item);
        this.checkFormEntry(
          this.form.codeDiseaseEntry,
          item,
          this.form.codeDiseaseEntry.length - 1,
          this.formInpTagName
        );
      }
      this.inputVisible = false;
      this.inputValue = '';
    },
    // 显示输入框
    showInput() {
      this.inputVisible = true;
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    /**
     * @author: justin
     * @description: 检查表单、词条整理-词条
     * @param {*} item
     * @param {*} index
     * @return {*}
     */
    checkFormEntry(arr, item, index, inpTagName) {
      const that = this;
      const duplicates = arr.reduce((acc, item) => {
        acc[item.entryText] = (acc[item.entryText] || 0) + 1;
        return acc;
      }, {});
      for (const key in duplicates) {
        arr
          .filter((item) => item.entryText == key)
          .map((item) => {
            that.$set(item, 'err', duplicates[key] >= 2);
          });
      }

      if (!item) return;

      const editTagInput = `${inpTagName}${index}`;
      let flag = true;
      if (!item.entryText || !item.entryText.trim().length === 0) {
        that
          .$confirm(`当前词条内容为空，继续将不保存？`, '提示', {
            confirmButtonText: '继续',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(() => {
            arr.splice(index, 1);
            that.editDiseaseEntryList.splice(index, 1);
          })
          .catch(() => {
            item.confirm = false;
            that.$set(that.editDiseaseEntryList, index, true);
            that.$nextTick((_) => {
              const ref = that.$refs[editTagInput][0].$refs;
              if (ref) {
                ref.input.focus();
              }
            });

            if (arr[index].entryTextOld)
              arr[index].entryText = arr[index].entryTextOld;
            return false;
          });

        flag = false;
      } else if (
        arr.some((x, i) => x.entryText === item.entryText && i !== index)
      ) {
        that.$message({
          message: '当前词条已存在，请勿重复录入!',
          type: 'warning',
          showClose: true
        });

        that.$set(that.editDiseaseEntryList, index, false);
        flag = false;
      }

      return flag;
    }
  }
};
