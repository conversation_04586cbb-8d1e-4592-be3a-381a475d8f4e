<!-- 报告数据模型设计 -->
<template>
  <div class="reportDataModelDesign_page">
    <div class="left_wrap">
      <header>报告模板</header>
      <div class="search_wrap">
        <el-input
          v-model="searchVal"
          @input="searchModel"
          clearable
          size="small"
          placeholder="请输入结果名称/模板号搜索"
        ></el-input>
        <el-button class="blue_btn btn" size="small" @click="addDataModelBtn"
          >添加</el-button
        >
      </div>
      <ul v-if="dataModelList.length !== 0">
        <li
          @click="getDataModelResult(item)"
          :class="{ li_active: dataModelActive == item.modelCode }"
          v-for="(item, idx) in dataModelList"
          :key="item.modelCode"
        >
          <span>{{ `(${item.modelCode})` + item.modelName }}</span>
          <i
            class="el-icon-edit-outline"
            title="编辑"
            @click="modifyDataModelBtn(item)"
          ></i>
          <i
            class="el-icon-delete"
            title="删除"
            @click="delDataModel(item, idx)"
          ></i>
        </li>
      </ul>
      <el-empty
        v-else
        description="暂无模板"
        style="flex: 1; flex-shrink: 0; overflow: auto"
      ></el-empty>
    </div>
    <div class="right_wrap">
      <div class="right_head">
        <div class="template_info">
          <span>模板名称：{{ checkModel.modelName }}</span>
          <span>模板号：{{ checkModel.modelCode }}</span>
        </div>
        <div class="btn_wrap">
          <el-button size="small" class="blue_btn btn" @click="addBtn"
            >添加</el-button
          >
        </div>
      </div>
      <div class="right_content" v-if="jsonData.length !== 0">
        <el-collapse v-model="activeNames">
          <el-collapse-item
            :name="item.resultSet"
            v-for="(item, idx) in jsonData"
            :key="item.resultSet"
          >
            <template #title>
              <div class="collapse_title">
                <span>{{ item.resultSet }}</span>
                <i
                  class="el-icon-edit-outline"
                  title="编辑"
                  @click.stop="editBtn(item, idx)"
                ></i>
                <i
                  class="el-icon-delete"
                  title="删除"
                  @click.stop="delModelResult(item, idx)"
                ></i>
              </div>
            </template>
            <div
              v-for="(twoItem, twoIdx) in item.results"
              :key="twoIdx"
              class="result_wrap"
            >
              <i class="num_i">{{ twoIdx + 1 }}、</i>
              <label>{{ twoItem.resultName }}：</label>
              <span v-if="twoItem.resultType == 1">
                <el-tag
                  class="result_tag"
                  size="medium"
                  v-for="threeItem in twoItem.mappers"
                  :key="threeItem.itemCode"
                  >{{ threeItem.itemName }}</el-tag
                >
              </span>
              <span v-else>{{ twoItem.value }}</span>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>

      <el-empty
        v-else
        description="暂无数据"
        style="flex: 1; flex-shrink: 0; overflow: auto"
      ></el-empty>
    </div>
    <!-- 添加数据模型 -->
    <el-dialog
      :title="dialogTitle + '模板'"
      :visible.sync="dataModelDialogShow"
      :close-on-click-modal="false"
      width="60%"
    >
      <div class="json_editDialog">
        <el-form
          :model="dataModelForm"
          :rules="dataModelRules"
          ref="dataModelForm_Ref"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="模板号" v-if="dialogTitle == '修改'">
            <el-input
              size="small"
              readonly
              v-model="dataModelForm.modelCode"
            ></el-input>
          </el-form-item>
          <el-form-item label="模板名称" prop="modelName">
            <el-input size="small" v-model="dataModelForm.modelName"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dataModelDialogShow = false">取 消</el-button>
        <el-button type="primary" @click="saveDataModel">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 添加/编辑数据模型 -->
    <el-dialog
      :title="dialogTitle + '数据'"
      :visible.sync="jsonEditDialogShow"
      :close-on-click-modal="false"
      width="60%"
    >
      <div class="json_editDialog">
        <el-form
          :model="jsonEditForm"
          ref="jsonEditForm_Ref"
          :rules="rules"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="结果集名称" prop="resultSet">
            <el-input
              :readonly="dialogTitle == '修改'"
              size="small"
              v-model="jsonEditForm.resultSet"
            ></el-input>
          </el-form-item>
          <el-form-item label="" prop="region">
            <el-button size="small" @click="dialogAddBtn">新增结果</el-button>
          </el-form-item>
          <el-form-item v-for="(item, idx) in jsonEditForm.results" :key="idx">
            <template #label>
              <span class="form_label">
                结果名称
                <i
                  class="el-icon-error"
                  title="删除"
                  @click="formDel(jsonEditForm.results, idx)"
                ></i>
              </span>
            </template>
            <div style="display: flex">
              <el-input
                size="small"
                style="flex: 1; flex-shrink: 0"
                v-model="item.resultName"
              ></el-input>
              <el-form-item label="结果类型" style="width: 25%">
                <el-select
                  size="small"
                  @change="resultTypeChange(item)"
                  v-model="item.resultType"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in resultTypeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                label-width="80px"
                label="项目列表"
                style="width: 50%"
              >
                <el-select-v2
                  filterable
                  style="width: 100%"
                  v-if="item.resultType == 1"
                  multiple
                  size="small"
                  label-key="itemName"
                  value-key="itemCode"
                  v-model="item.mappers"
                  placeholder="请选择"
                  :options="mappersList"
                >
                  <template #default="{ item }">
                    <p>{{ item.itemCode }} -- {{ item.itemName }}</p>
                  </template>
                </el-select-v2>
                <el-input
                  v-else
                  style="width: 100%"
                  size="small"
                  v-model="item.value"
                ></el-input>
              </el-form-item>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="jsonEditDialogShow = false">取 消</el-button>
        <el-button type="primary" @click="editConfirmBtnClick">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { dataUtils } from '../../common';
import ElSelectV2 from 'el-select-v2';

export default {
  name: 'reportDataModelDesign',
  components: {
    ElSelectV2
  },
  data() {
    return {
      searchVal: '',
      routeJson: {
        msg: 'asdsddsadasdsf'
      },
      activeNames: 1,
      jsonData: [],
      jsonEditDialogShow: false,
      jsonEditForm: {
        resultSet: '',
        results: [
          // {
          //     resultName: "矫正视力左",
          //     resultType: 1,
          //     mappers: [],
          //     value: null
          // },
          // {
          //     resultName: "矫正视力右",
          //     resultType: 1,
          //     mappers: [],
          //     value: null
          // }
        ]
      },
      rules: {
        resultSet: [
          { required: true, message: '请输入结果集名称', trigger: 'blur' }
        ]
      },
      dialogTitle: '添加',
      dataModelList: [],
      fixed_dataModelList: [],
      dataModelActive: '',
      dataModelDialogShow: false,
      dataModelForm: {
        modelCode: '',
        modelName: ''
      },
      dataModelRules: {
        modelName: [
          { required: true, message: '请输入模板名称', trigger: 'blur' }
        ]
      },
      checkModel: {
        modelName: '',
        modelCode: ''
      },
      resultTypeList: [
        {
          label: '映射项目',
          value: 1
        },
        {
          label: '字符串',
          value: 2
        }
      ],
      mappersList: [],
      editModelJson: {}
    };
  },
  methods: {
    // 重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    // 获取数据模型列表
    getDataList() {
      this.$ajax.get(this.$apiUrls.List).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.dataModelList = returnData || [];
        this.fixed_dataModelList = dataUtils.deepCopy(this.dataModelList);
      });
    },
    searchModel() {
      let searchVal = this.searchVal.trim();
      let dataModelList = this.fixed_dataModelList.filter(
        (item) =>
          item.modelName.includes(searchVal) ||
          item.modelCode.toLowerCase().includes(searchVal)
      );
      this.dataModelList = dataModelList;
    },
    // 添加按钮的点击回调
    addBtn() {
      if (!this.checkModel.modelCode) {
        this.$message({
          message: '请先选择模板！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.jsonEditDialogShow = true;
      this.$nextTick(() => {
        this.resetForm('jsonEditForm_Ref');
        this.dialogTitle = '添加';
        this.jsonEditForm = this.$options.data().jsonEditForm;
      });
    },
    // 弹窗的新增按钮点击的回调
    dialogAddBtn() {
      this.jsonEditForm.results.unshift({
        resultName: '',
        resultType: 1,
        mappers: [],
        value: null
      });
    },
    // 添加/编辑结果弹窗的确定按钮点击的回调
    editConfirmBtnClick() {
      this.$refs['jsonEditForm_Ref'].validate((valid) => {
        if (valid) {
          console.log(this.jsonEditForm);
          let jsonEditForm = dataUtils.deepCopy(this.jsonEditForm);
          jsonEditForm.results.forEach((oneItem) => {
            let mappers = [];
            oneItem.mappers.forEach((twoItem) => {
              let itemObj = this.mappersList.find(
                (item) => item.itemCode == twoItem
              );
              if (itemObj) {
                mappers.push(itemObj);
              }
            });
            oneItem.mappers = mappers;
          });
          console.log(jsonEditForm);
          if (this.dialogTitle == '添加') {
            this.addModelResult(jsonEditForm);
          } else {
            this.editModelResult(jsonEditForm);
          }
        } else {
          return false;
        }
      });
    },
    // 编辑按钮的点击回调
    editBtn(row) {
      this.dialogTitle = '修改';
      this.jsonEditDialogShow = true;
      this.editModelJson = row;
      let jsonEditForm = dataUtils.deepCopy(row);
      jsonEditForm.results.forEach((oneItem) => {
        let mappers = [];
        oneItem.mappers.map((twoItem) => {
          mappers.push(twoItem.itemCode);
        });
        oneItem.mappers = mappers;
      });
      console.log(jsonEditForm);
      this.$nextTick(() => {
        this.resetForm('jsonEditForm_Ref');
        this.jsonEditForm = jsonEditForm;
      });
    },
    // 新增结果
    addModelResult(jsonEditForm) {
      this.$ajax
        .post(this.$apiUrls.ResultSetCreate, jsonEditForm, {
          query: {
            modelCode: this.checkModel.modelCode
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$message({
            message: '添加成功！',
            type: 'success',
            showClose: true
          });
          this.jsonEditDialogShow = false;
          this.jsonData.unshift(jsonEditForm);
          this.activeNames.unshift(jsonEditForm.resultSet);
        });
    },
    // 修改结果
    editModelResult(jsonEditForm) {
      this.$ajax
        .post(this.$apiUrls.ResultSetUpdate, jsonEditForm, {
          query: {
            modelCode: this.checkModel.modelCode
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$message({
            message: '修改成功！',
            type: 'success',
            showClose: true
          });
          this.jsonEditDialogShow = false;
          this.editModelJson.results = jsonEditForm.results;
        });
    },
    // 删除字段
    formDel(arr, idx) {
      arr?.splice(idx, 1);
    },
    // 添加数据模板按钮的点击回调
    addDataModelBtn() {
      this.dialogTitle = '添加';
      this.dataModelDialogShow = true;
      this.$nextTick(() => {
        this.resetForm('dataModelForm_Ref');
      });
    },
    // 修改模板按钮的点击回调
    modifyDataModelBtn(row) {
      this.dialogTitle = '修改';
      this.dataModelDialogShow = true;
      this.$nextTick(() => {
        this.resetForm('dataModelForm_Ref');
        this.dataModelForm = { ...row };
      });
    },
    // 保存数据模板
    saveDataModel() {
      this.$refs['dataModelForm_Ref'].validate((valid) => {
        if (valid) {
          if (this.dialogTitle == '添加') {
            this.addDataModel();
          } else {
            this.modifyDataModel();
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    // 新增模板
    addDataModel() {
      this.$ajax
        .post(this.$apiUrls.ReportDataModelCreate, this.dataModelForm)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$message({
            message: '保存成功！',
            type: 'success',
            showClose: true
          });
          this.dataModelDialogShow = false;
          this.getDataList();
        });
    },
    // 修改模板
    modifyDataModel() {
      this.$ajax
        .post(this.$apiUrls.ReportDataModelUpdate, this.dataModelForm)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$message({
            message: '保存成功！',
            type: 'success',
            showClose: true
          });
          this.dataModelDialogShow = false;
          this.checkModel.modelName = this.dataModelForm.modelName;
          this.getDataList();
        });
    },
    // 删除模板
    delDataModel(row, idx) {
      this.$confirm(`是否删除模板  ${row.modelName}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax
            .post(this.$apiUrls.ReportDataModelDelete, row)
            .then((r) => {
              let { success, returnData } = r.data;
              if (!success) return;
              this.$message({
                message: '删除成功',
                type: 'success',
                showClose: true
              });
              this.dataModelList.splice(idx, 1);
            });
        })
        .catch(() => {});
    },
    // 获取数据模型的结果
    getDataModelResult(row) {
      this.checkModel = row;
      this.dataModelActive = row.modelCode;
      this.$ajax
        .get(this.$apiUrls.ResultSetList + `?modelCode=${row.modelCode}`)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.jsonData = returnData || [];
          this.activeNames = returnData?.map((item) => item.resultSet);
        });
    },
    // 获取项目
    getItems() {
      this.$ajax.post(this.$apiUrls.Item).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        let mappersList = returnData?.map((item) => {
          return {
            itemCode: item.itemCode,
            itemName: item.itemName
          };
        });
        this.mappersList = mappersList;
      });
    },
    // 结果类型改变的回调
    resultTypeChange(row) {
      row.mappers = [];
      row.value = '';
    },
    // 删除数据模型的结果集
    delModelResult(row, idx) {
      this.$confirm(`是否删除 ${row.resultSet} 结果？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax
            .post(this.$apiUrls.ResultSetDelete, row, {
              query: {
                modelCode: this.checkModel.modelCode
              }
            })
            .then((r) => {
              let { success, returnData } = r.data;
              if (!success) return;
              this.jsonData.splice(idx, 1);
              this.activeNames.splice(idx, 1);
              this.$message({
                type: 'success',
                message: '删除成功!',
                showClose: true
              });
            });
        })
        .catch(() => {});
    }
  },
  created() {
    this.getDataList();
    this.getItems();
  }
};
</script>

<style lang="less" scoped>
.reportDataModelDesign_page {
  display: flex;
  .left_wrap {
    display: flex;
    flex-direction: column;
    width: 300px;
    height: 100%;
    overflow: auto;
    margin-right: 5px;
    background: #fff;
    border-radius: 4px;
    header {
      height: 30px;
      line-height: 30px;
      padding: 0 5px;
    }
    .search_wrap {
      display: flex;
      padding: 5px;
      align-items: center;
      .btn {
        margin-left: 10px;
      }
    }
    ul {
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
      li {
        padding: 10px 5px;
        cursor: pointer;
        display: flex;
        align-items: center;
        span {
          flex: 1;
          flex-shrink: 0;
        }
        i {
          display: none;
          font-size: 18px;
          & + i {
            margin-left: 10px;
          }
        }
        &:hover {
          i {
            display: block;
          }
        }
      }
      .li_active {
        background: #1770df;
        color: #ffffff;
        border-radius: 4px;
      }
    }
  }
  .right_wrap {
    flex: 1;
    flex-shrink: 0;
    height: 100%;
    overflow: auto;
    background: #fff;
    border-radius: 4px;
    padding: 5px;
    display: flex;
    flex-direction: column;
    .right_head {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
      align-items: center;
      .template_info {
        span {
          margin-right: 20px;
        }
      }
    }
    .right_content {
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
      .result_wrap {
        display: flex;
        padding-left: 20px;
        font-size: 16px;
        .num_i {
          width: 20px;
          font-style: normal;
          margin-right: 5px;
        }
        span {
          flex: 1;
          flex-shrink: 0;
          flex-wrap: wrap;
        }
      }
      .result_tag {
        margin-right: 5px;
        margin-bottom: 5px;
        font-size: 16px;
      }
    }
    .collapse_title {
      flex: 1;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: 600;
      span {
        flex: 1;
        flex-shrink: 0;
      }
      i {
        font-size: 18px;
      }
      i + i {
        margin-left: 10px;
      }
    }
  }
  .json_editDialog {
    max-height: 500px;
    overflow: auto;
    .el-form-item {
      margin-bottom: 10px;
    }
    /deep/.el-form-item__label {
      position: relative;
      i {
        position: absolute;
        top: 0;
        right: 0;
        color: red;
      }
    }
  }
}
</style>
