<!--
 * @FilePath: \KrPeis\src\pages\serviceData\occDiseases\components\baseCodeOccupationalSymptomTable.vue
 * @Description:  症状询问维护组件
 * @Author: justin
 * @Date: 2024-05-10 14:43:20
 * @Version: 0.0.1
 * @LastEditors: justin
 * @LastEditTime: 2024-06-20 11:17:02
*
-->

<template>
  <div class="code-occupational-symptom-table-job-diseases">
    <!-- 列表信息 -->
    <el-container>
      <el-header>
        <AllBtn
          :btnList="btnList"
          :methodSearch="search"
          :methodCreat="add"
          :methodDelete="remove"
          :methodExport="exportToExcel"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          ref="publicTable_Ref"
          :theads="theads"
          :url="$apiUrls.ReadCodeOccupationalSymptom"
          :params="params"
          rowKey="symptomCode"
          :tableDataFilter="filterTableDataCallback"
          :excelColumnsMap="excelColumnsMap"
          @selectionChange="selectChange"
          @rowDblclick="dbClickRow"
          @request-success="responseDataCallback"
        >
          <template #operation="{ scope }">
            <el-button size="mini" @click="showCodeItemDialog(scope.row)"
              >体检项目对应</el-button
            >
          </template>
        </PublicTable>
      </el-main>
    </el-container>

    <!-- 弹出框基本信息 -->
    <el-drawer
      :title="title + '属性'"
      :visible.sync="showDrawer"
      :wrapperClosable="false"
      @closed="closedDrawer"
    >
      <div v-loading="loadingDrawer">
        <el-form :model="popupForm" ref="ruleForm" :rules="rules">
          <el-form-item
            label="症状询问代码"
            :label-width="formLabelWidth"
            prop="symptomCode"
          >
            <el-input
              v-model.trim="popupForm.symptomCode"
              size="small"
              placeholder="请输入症状询问代码"
              :disabled="!isNew"
              :maxlength="10"
            ></el-input>
          </el-form-item>

          <el-form-item
            label="症状询问名称"
            :label-width="formLabelWidth"
            prop="symptomName"
          >
            <el-input
              v-model.trim="popupForm.symptomName"
              autocomplete="off"
              size="small"
              placeholder="请输入症状询问名称"
              :maxlength="255"
            ></el-input>
          </el-form-item>
        </el-form>
        <div class="dialog-footer">
          <el-button
            @click="showDrawer = false"
            size="small"
            :loading="loadingSubmit"
            >取消</el-button
          >
          <el-button
            type="primary"
            @click="submit"
            size="small"
            :loading="loadingSubmit"
            >保存</el-button
          >
        </div>
      </div>
    </el-drawer>

    <!-- 弹出框体检项目对应 -->
    <el-drawer
      :title="currentRow.symptomName + '-体检项目对应'"
      :visible.sync="showCodeItemDrawer"
      :wrapperClosable="false"
      size="60%"
      @closed="closedCodeItemDrawer"
      @opened="openedCodeItemDrawer"
    >
      <div v-loading="loadingCodeItemDrawer">
        <!-- 体检项目内容  -->
        <div class="code-item-wrapper">
          <el-row
            :gutter="10"
            type="flex"
            justify="space-between"
            align="middle"
          >
            <el-col :span="8"> </el-col>
            <el-col :span="6">
              <el-select
                v-model="codeItemClsCode"
                filterable
                clearable
                @change="searchCodeItem"
                size="mini"
                placeholder="项目分类筛选"
              >
                <el-option
                  v-for="item in codeItemClsList"
                  :key="item.clsCode"
                  :label="item.clsName"
                  :value="item.clsCode"
                /> </el-select
            ></el-col>
            <el-col :span="10">
              <AllBtn
                :btnList="['查询']"
                :methodSearch="searchCodeItem"
                ref="allBtnCodeItem_Ref"
              />
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <PublicTable
                ref="publicTableCodeItem_Ref"
                :url="$apiUrls.PageQuery_CodeItem"
                method="paramsPost"
                :params="{
                  clsCode: codeItemClsCode
                }"
                :theads="[
                  {
                    prop: 'itemCode',
                    label: '项目代码',
                    align: '',
                    width: '100px',
                    sortable: false
                  },
                  {
                    prop: 'itemName',
                    label: '项目名称',
                    align: '',
                    width: '',
                    sortable: false,
                    cellClassName: 'cell_blue'
                  },
                  {
                    prop: 'clsCode',
                    label: '项目分类',
                    align: '',
                    width: '150px',
                    sortable: false
                  },
                  {
                    prop: 'unit',
                    label: '结果单位',
                    align: '',
                    width: '100px',
                    sortable: false
                  }
                ]"
                :elStyle="{
                  height: '40vh'
                }"
                rowKey="itemCode"
                :tableDataFilter="filterTableDataCodeItemCallback"
                @selectionChange="selectChangeTableCodeItem"
              >
                <template #clsCode="{ scope }">
                  <span>{{
                    scope.row.clsCode | getClsName(codeItemClsList)
                  }}</span>
                </template>
              </PublicTable>
            </el-col>
          </el-row>
        </div>

        <!-- 已对应项目内容  -->
        <div class="mapping-wrapper">
          <el-row
            :gutter="10"
            type="flex"
            justify="space-between"
            align="middle"
            style="padding: 15px 10px"
          >
            <el-col :span="9"></el-col>
            <el-col :span="6">
              <el-button
                size="small"
                class="blue_btn btn"
                @click="addCodeItemMapping"
                icon="el-icon-arrow-down"
                >添加</el-button
              >

              <el-button
                size="small"
                class="blue_btn btn"
                @click="removeCodeItemMapping"
                icon="el-icon-delete-solid"
                >删除</el-button
              >
            </el-col>
            <el-col :span="7">
              <el-input
                clearable
                v-model.trim="codeItemMappingKeyword"
                size="small"
                placeholder="请输入查找内容"
                class="search-input"
                @input="searchCodeItemMapping"
              ></el-input>
            </el-col>

            <el-col :span="2">
              <el-button
                size="small"
                class="blue_btn btn"
                @click="searchCodeItemMapping"
                icon="iconfont icon-search"
                >查询</el-button
              >
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <PublicTable
                ref="publicTableCodeItemMapping_Ref"
                :url="$apiUrls.QueryMapOccupationalSymptomPeItem"
                method="paramsPost"
                :params="{
                  symptomCode: currentRow.symptomCode
                }"
                :theads="[
                  {
                    prop: 'peItemCode',
                    label: '对应项目代码',
                    align: '',
                    width: '200px',
                    sortable: false
                  },
                  {
                    prop: 'peItemName',
                    label: '对应项目名称',
                    align: '',
                    width: '',
                    sortable: false,
                    cellClassName: 'cell_blue'
                  }
                ]"
                :elStyle="{
                  height: '35vh'
                }"
                rowKey="peItemCode"
                :tableDataFilter="filterTableDataCodeItemMappingCallback"
                @selectionChange="selectChangeTableCodeItemMapping"
                @request-success="
                  (res) => {
                    this.tableDataCodeItemMapping = res.returnData;
                  }
                "
              >
              </PublicTable>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { dataUtils } from '@/common';
import PublicTable from '@/components/publicTable2.vue';
import AllBtn from '../../allBtn.vue';

export default {
  name: 'baseCodeOccupationalSymptomTable',
  components: { PublicTable, AllBtn },
  data() {
    return {
      title: '症状询问',
      btnList: ['查询', '新建', '删除', '导出'],
      keyword: '',
      params: {},
      excelData: [],
      multipleSelectionList: [],
      tableData: [],
      theads: [
        {
          prop: 'symptomCode',
          label: '症状询问代码',
          align: '',
          width: '200px',
          sortable: false
        },
        {
          prop: 'symptomName',
          label: '症状询问名称',
          align: '',
          width: '',
          sortable: false,
          cellClassName: 'cell_blue'
        },
        {
          prop: 'operation',
          label: '操作',
          align: 'center',
          width: '150px',
          sortable: false
        }
      ],
      currentRow: {},

      showDrawer: false,
      formLabelWidth: '150px',
      popupForm: {
        symptomCode: '',
        symptomName: ''
      },
      rules: {
        symptomCode: [
          {
            required: true,
            message: `请输入症状询问代码`,
            trigger: 'blur'
          }
        ],
        symptomName: [
          {
            required: true,
            message: `请输入症状询问名称`,
            trigger: 'blur'
          }
        ]
      },
      isNew: false,
      loadingSubmit: false,
      loadingDrawer: true,

      showCodeItemDrawer: false,
      loadingCodeItemDrawer: true,
      loadingCodeItemSubmit: false,
      tableDataCodeItem: [],
      loadingCodeItem: false,
      codeItemClsList: [],
      codeItemClsCode: '',
      codeItemKeyword: '',
      multipleSelectionCodeItemList: [],
      codeItemMappingKeyword: '',
      loadingCodeItemMapping: false,
      tableDataCodeItemMapping: [],
      multipleSelectionCodeItemMappingList: []
    };
  },
  mounted() {
    this.search();
  },
  filters: {
    /**
     * @author: justin
     * @description: 获取分类名称
     * @param {*} value
     * @param {*} arr
     * @return {*}
     */
    getClsName(value, arr) {
      return arr.find((item) => item.clsCode === value)?.clsName;
    }
  },
  methods: {
    /**
     * @author: justin
     * @description: 模糊查询
     * @return {*}
     */
    search() {
      const that = this;
      that.tableData = [];
      that.keyword = (that.$refs.allBtn_Ref.searchInfo || '')
        .trim()
        .toLowerCase();
      if (!that.keyword) {
        that.params = [];
        that.$refs.publicTable_Ref.loadData();
      }
    },

    /**
     * @author: justin
     * @description: 获取记录响应回调
     * @param {*} data
     * @return {*}
     */
    responseDataCallback(data) {
      this.tableData = data.returnData;
    },

    /**
     * @author: justin
     * @description: 数据过滤回调
     * @param {*} data
     * @return {*}
     */
    filterTableDataCallback(data) {
      if (!data || data.length == 0) return data;

      const that = this;
      return data.filter(
        (item) =>
          item.symptomCode.toLowerCase().includes(that.keyword) ||
          item.symptomName.toLowerCase().includes(that.keyword)
      );
    },

    /**
     * @author: justin
     * @description: 表格多选行数据
     * @param {*} selection 已选中的行集合
     * @param {*} list 已选中行数据，若是全选操作，则会带出未在界面体现的数据
     * @return {*}
     */
    selectChange: function (selection, list) {
      this.multipleSelectionList = list;
    },

    /**
     * @author: justin
     * @description: 导出excel
     * @return {*}
     */
    exportToExcel() {
      this.$refs.publicTable_Ref.exportToExcel({
        fileName: this.title + '列表'
      });
    },

    /**
     * @author: justin
     * @description: 导出excel标题列数据处理
     * @param {*} data
     * @return {*}
     */
    excelColumnsMap(data) {
      return data.filter((x) => x.key !== 'operation');
    },

    /**
     * @author: justin
     * @description: 关闭弹出框回调
     * @return {*}
     */
    closedDrawer() {
      this.loadingDrawer = true;
      this.$nextTick(function () {
        this.$refs.ruleForm.resetFields();
      });
    },

    /**
     * @author: justin
     * @description: 新增
     * @return {*}
     */
    add() {
      this.isNew = true;
      this.showDrawer = true;
      this.$nextTick(() => {
        this.loadingDrawer = false;
      });
    },

    /**
     * @author: justin
     * @description: 双击row弹出编辑框
     * @param {*} row
     * @return {*}
     */
    dbClickRow(row) {
      this.isNew = false;
      this.showDrawer = true;
      this.$nextTick(() => {
        this.popupForm = dataUtils.deepCopy(row);
        this.loadingDrawer = false;
      });
    },

    /**
     * @author: justin
     * @description: 保存
     * @return {*}
     */
    submit() {
      this.$refs.ruleForm.validate((valid) => {
        valid = valid && this.exitKey();
        if (valid) {
          this.loadingSubmit = true;
          this.$ajax
            .post(
              this.isNew
                ? this.$apiUrls.CreateCodeOccupationalSymptom
                : this.$apiUrls.UpdateCodeOccupationalSymptom,
              this.popupForm
            )
            .then(async (res) => {
              res = res.data;
              if (!res.success) {
                return;
              }

              this.showDrawer = false;
              this.$nextTick(() => {
                this.$refs.allBtn_Ref.searchInfo = '';
                this.search();
              });
              this.$message({
                message: this.isNew ? '新增成功' : '修改成功',
                type: 'success',
                showClose: true
              });
            })
            .finally((_) => {
              this.loadingSubmit = false;
            });
        }
      });
    },

    /**
     * @author: justin
     * @description: 是否存在该代码
     * @return {*}
     */
    exitKey() {
      if (!this.isNew) return true;

      if (
        this.tableData.some(
          (item) => item.symptomCode === this.popupForm.symptomCode
        )
      ) {
        this.$message.warning('该代码已存在，请重新输入！');
        return false;
      }

      return true;
    },

    /**
     * @author: justin
     * @description: 删除
     * @return {*}
     */
    remove() {
      if (this.multipleSelectionList.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据！',
          type: 'warning'
        });
      }
      if (this.multipleSelectionList.length > 0) {
        this.$confirm('是否确认删除已选择的记录？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$ajax
            .post(
              this.$apiUrls.DeleteCodeOccupationalSymptom,
              this.multipleSelectionList.map((x) => x.symptomCode)
            )
            .then((res) => {
              let { success } = res.data;
              if (!success) return;
              this.$message({
                showClose: true,
                message: '删除成功',
                type: 'success'
              });
              this.$nextTick(() => {
                this.$refs.allBtn_Ref.searchInfo = '';
                this.search();
              });
            });
        });
      }
    },

    /**
     * @author: justin
     * @description: 显示体检项目对应窗口
     * @param {*} row  行数据
     * @return {*}
     */
    showCodeItemDialog(row) {
      this.showCodeItemDrawer = true;
      this.currentRow = row;
      this.$nextTick(() => {
        this.getCodeItemClsList();
        this.searchCodeItem();
        this.searchCodeItemMapping();
      });
    },

    /**
     * @author: justin
     * @description:  关闭体检项目对应窗口回调
     * @return {*}
     */
    closedCodeItemDrawer() {
      this.loadingCodeItemDrawer = true;
    },

    /**
     * @author: justin
     * @description:  打开体检项目对应窗口回调
     * @return {*}
     */
    openedCodeItemDrawer() {},

    /**
     * @author: justin
     * @description: 获取体检项目分类列表
     * @return {*}
     */
    getCodeItemClsList() {
      const that = this;
      that.$ajax
        .post(that.$apiUrls.RD_CodeItemCls + '/Read', [])
        .then((res) => {
          if (!res.data.success) return;

          that.codeItemClsList = res.data.returnData;
        })
        .finally((_) => {
          that.loadingCodeItemDrawer = false;
        });
    },

    /**
     * @author: justin
     * @description: 检索体检项目对应数据
     * @return {*}
     */
    searchCodeItem() {
      const that = this;
      that.codeItemKeyword = that.$refs.allBtnCodeItem_Ref.searchInfo || '';
      if (!that.codeItemKeyword) {
        that.$refs.publicTableCodeItem_Ref.loadData();
      }
    },

    /**
     * @author: justin
     * @description: 体检项目对应数据过滤回调
     * @param {*} data
     * @return {*}
     */
    filterTableDataCodeItemCallback(data) {
      if (!data || data.length == 0) return data;

      const that = this;
      data = data
        .filter((x) => x.isEnabled)
        .filter(
          (x) =>
            (that.codeItemClsCode && x.clsCode === that.codeItemClsCode) ||
            !that.codeItemClsCode
        );

      const _keyword = (that.codeItemKeyword || '').trim().toLowerCase();
      if (!_keyword) return data;

      return data.filter(
        (item) =>
          item.itemCode.toLowerCase().includes(_keyword) ||
          item.itemName.toLowerCase().includes(_keyword)
      );
    },

    /**
     * @author: justin
     * @description: 复选框勾选操作
     * @param {*} val
     * @return {*}
     */
    selectChangeTableCodeItem(val, list) {
      this.multipleSelectionCodeItemList = list;
    },

    /**
     * @author: justin
     * @description: 新增体检项目对应数据
     * @return {*}
     */
    addCodeItemMapping() {
      if (this.multipleSelectionCodeItemList.length <= 0) {
        this.$message.warning('请勾选需要添加对应的体检项目');
        return;
      }

      const existItemCodeList = this.tableDataCodeItemMapping.map(
        (x) => x.itemCode
      );
      const filterItemCodeList = this.multipleSelectionCodeItemList
        .filter((x) => existItemCodeList.includes(x.itemCode))
        .map((x) => x.itemCode);
      if (filterItemCodeList.length > 0) {
        this.$message.warning(
          `以下体检项目不能重复添加：${filterItemCodeList.join('、')}`
        );
        return;
      }

      this.$confirm('确认添加已勾选的体检项目？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$ajax
          .post(
            this.$apiUrls.CreateMapOccupationalSymptomPeItem,
            this.multipleSelectionCodeItemList.map((x) => {
              return {
                symptomCode: this.currentRow.symptomCode,
                itemCode: x.itemCode
              };
            })
          )
          .then((res) => {
            let { success } = res.data;
            if (!success) return;

            this.$message.success('添加成功！');
            this.$nextTick(() => {
              this.codeItemMappingKeyword = '';
              this.$refs.publicTableCodeItem_Ref.clearSelection();
              this.searchCodeItemMapping();
            });
          });
      });
    },

    /**
     * @author: justin
     * @description: 删除体检项目对应数据
     * @return {*}
     */
    removeCodeItemMapping() {
      if (this.multipleSelectionCodeItemMappingList.length <= 0) {
        this.$message.warning('请勾选需要删除已添加对应的体检项目');
        return;
      }
      this.$confirm('确认删除已添加对应的体检项目？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$ajax
          .post(
            this.$apiUrls.DeleteMapOccupationalSymptomPeItem,
            this.multipleSelectionCodeItemMappingList.map((x) => {
              return {
                symptomCode: this.currentRow.symptomCode,
                itemCode: x.peItemCode
              };
            })
          )
          .then((res) => {
            let { success } = res.data;
            if (!success) return;

            this.$message.success('删除成功！');
            this.$nextTick(() => {
              this.codeItemMappingKeyword = '';
              this.$refs.publicTableCodeItemMapping_Ref.clearSelection();
              this.searchCodeItemMapping();
            });
          });
      });
    },

    /**
     * @author: justin
     * @description:
     * @return {*}
     */
    searchCodeItemMapping() {
      const that = this;
      if (!that.codeItemMappingKeyword) {
        that.$refs.publicTableCodeItemMapping_Ref.loadData();
      }
    },

    /**
     * @author: justin
     * @description: 体检项目已对应数据过滤回调
     * @param {*} data
     * @return {*}
     */
    filterTableDataCodeItemMappingCallback(data) {
      if (!data || data.length == 0) return data;

      const that = this;
      const _keyword = (that.codeItemMappingKeyword || '').trim().toLowerCase();
      if (!_keyword) return data;

      return data.filter(
        (item) =>
          item.peItemCode.toLowerCase().includes(_keyword) ||
          item.peItemName.toLowerCase().includes(_keyword)
      );
    },

    /**
     * @author: justin
     * @description: 复选框勾选操作
     * @param {*} val
     * @return {*}
     */
    selectChangeTableCodeItemMapping(val, list) {
      this.multipleSelectionCodeItemMappingList = list;
    }
  },
  activated() {
    this.$nextTick(() => {
      this.$refs.publicTable_Ref.doLayout();
    });
  }
};
</script>

<style lang="less" scoped>
.code-occupational-symptom-table-job-diseases {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-weight: 600;

  header {
    display: flex;
    justify-content: flex-end;

    .el-form-item {
      margin-bottom: 18px;
    }
    .el-button--small {
      width: 64px;
    }
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-container.is-vertical {
    height: 100%;
  }
  .el-header,
  .el-main {
    padding: 0;
  }

  .searchWrap .el-form-item:last-child {
    margin-right: 0;
    margin-left: 10px;
  }
  .indexPage .contentWrap .tabPage {
    margin-top: 0;
  }

  .el-showDrawer.rtl {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #2d3436;
    padding: 0 20px 0 10px;
  }
  /deep/.el-drawer__body {
    padding-right: 20px;

    .el-form {
      padding-top: 20px;

      .el-form-item {
        margin-bottom: 18px;
      }
    }
  }

  /deep/ .el-select {
    width: 100%;
  }

  /deep/ .el-drawer__header {
    height: 42px;
    background: rgba(23, 112, 223, 0.2);
    margin-bottom: unset;
    color: #000;

    span,
    button {
      margin-top: -20px;
    }
  }

  .btn {
    padding: 6.5px 10px;
  }
}
</style>
