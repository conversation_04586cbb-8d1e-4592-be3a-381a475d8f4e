<!--
 * @FilePath: \KrPeis\src\pages\serviceData\occDiseases\components\baseCodeOccupationalDicTable.vue
 * @Description: 职业病通用字典组件（只适合key-value内容）
 * @Author: justin
 * @Date: 2024-04-26 10:14:37
 * @Version: 0.0.1
 * @LastEditors: justin
 * @LastEditTime: 2024-06-20 11:16:13
*
-->

<template>
  <div class="base-code-dic-container">
    <!-- 列表信息 -->
    <el-container>
      <el-header>
        <AllBtn
          :btnList="btnListPermission"
          :methodCreat="add"
          :methodDelete="remove"
          :methodSearch="search"
          :methodExport="exportToExcel"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          ref="publicTable_Ref"
          :theads="theads"
          :url="api.getList"
          :params="params"
          :remoteByPage="api.remoteByPage"
          :rowKey="tKey.prop"
          :tableDataFilter="filterTableDataCallback"
          @rowDblclick="dbClickRow"
          @selectionChange="selectChange"
          @request-success="responseDataCallback"
        >
        </PublicTable>
      </el-main>
    </el-container>

    <!-- 弹出框基本信息 -->
    <el-drawer
      :title="title + '属性'"
      :visible.sync="showDrawer"
      :wrapperClosable="false"
      @closed="closedDrawer"
    >
      <div v-loading="loadingDrawer">
        <el-form :model="popupForm" ref="ruleForm" :rules="rules">
          <el-form-item
            :label="tKey.name"
            :label-width="formLabelWidth"
            prop="code"
          >
            <el-input
              v-model.trim="popupForm.code"
              size="small"
              :placeholder="'请输入' + tKey.name"
              :disabled="!isNew"
              :maxlength="tKey.length"
            ></el-input>
          </el-form-item>

          <el-form-item
            :label="tValue.name"
            :label-width="formLabelWidth"
            prop="name"
          >
            <el-input
              v-model.trim="popupForm.name"
              autocomplete="off"
              size="small"
              :placeholder="'请输入' + tValue.name"
              :maxlength="tValue.length"
            ></el-input>
          </el-form-item>

          <el-form-item
            label="系统对应"
            :label-width="formLabelWidth"
            prop="mappingCode"
            v-if="showMappingField"
          >
            <el-select
              v-model="popupForm.mappingCode"
              placeholder="请选择系统对应内容"
              filterable
              clearable
            >
              <el-option
                v-for="item in mappingList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div class="dialog-footer">
          <el-button
            @click="showDrawer = false"
            size="small"
            :loading="loadingSubmit"
            >取消</el-button
          >
          <el-button
            type="primary"
            @click="submit"
            size="small"
            :loading="loadingSubmit"
            >保存</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { dataUtils } from '@/common';
import PublicTable from '@/components/publicTable2.vue';
import AllBtn from '../../allBtn.vue';
import { mapGetters } from 'vuex';

export default {
  name: 'baseCodeOccupationalDicTable',
  components: { PublicTable, AllBtn },
  props: {
    id: {
      // 公共字典id
      type: String,
      default: ''
    },
    title: {
      // 标题
      type: String,
      default: '信息'
    },
    api: {
      // 接口地址
      type: Object,
      default: {
        add: '', // 新增
        update: '', // 修改
        getList: '', // 获取列表
        delete: '', // 删除
        remoteByPage: false // 是否远程分页获取
      }
    },
    tKey: {
      // 字典key
      type: Object,
      default: {
        name: '代码',
        prop: 'code', // 属性名
        length: 2 // 内容最大长度
      }
    },
    tValue: {
      // 字典value
      type: Object,
      default: {
        name: '名称',
        prop: 'name', // 属性名
        length: 20 // 内容最大长度
      }
    }
  },
  data() {
    return {
      btnList: ['查询', '新建', '删除', '导出'],
      keyword: '',
      params: {},
      multipleSelectionList: [],
      excelData: [],
      tableData: [],
      theads: [],
      showDrawer: false,
      formLabelWidth: '150px',
      popupForm: {
        code: '',
        name: '',
        mappingCode: undefined
      },
      rules: {
        code: [
          {
            required: true,
            message: `请输入${this.tKey.name}`,
            trigger: 'blur'
          }
        ],
        name: [
          {
            required: true,
            message: `请输入${this.tValue.name}`,
            trigger: 'blur'
          }
        ]
      },
      isNew: false,
      loadingSubmit: false,
      showMappingField: false,
      mappingList: [],
      loadingDrawer: true
    };
  },
  mounted() {
    this.theads = [
      {
        prop: this.tKey.prop,
        label: this.tKey.name,
        align: '',
        width: '250px',
        sortable: false
      },
      {
        prop: this.tValue.prop,
        label: this.tValue.name,
        align: '',
        width: '',
        sortable: false,
        cellClassName: 'cell_blue'
      }
    ];
    this.search();
  },
  computed: {
    ...mapGetters(['G_cardType', 'G_marriageStatus']),
    /**
     * @author: justin
     * @description: 列表按钮授权
     * @return {*}
     */
    btnListPermission() {
      if (!this.api.add) delete this.btnList[1];
      if (!this.api.delete) delete this.btnList[2];

      return this.btnList;
    }
  },
  methods: {
    /**
     * @author: justin
     * @description: 关闭弹出框回调
     * @return {*}
     */
    closedDrawer() {
      this.loadingDrawer = true;
      this.$nextTick(function () {
        this.$refs.ruleForm.resetFields();
        this.popupForm.mappingCodeBk = undefined;
      });
    },

    /**
     * @author: justin
     * @description: 模糊查询
     * @return {*}
     */
    search() {
      const that = this;
      that.keyword = (that.$refs.allBtn_Ref.searchInfo || '')
        .trim()
        .toLowerCase();

      if (!that.keyword || that.api.remoteByPage) {
        // 请求接口查询
        if (that.api.remoteByPage) that.params.keyword = that.keyword;
        else that.params = [];

        that.$refs.publicTable_Ref.loadData();
      }
    },

    /**
     * @author: justin
     * @description: 获取记录响应回调
     * @param {*} data
     * @return {*}
     */
    responseDataCallback(data) {
      this.tableData = data.returnData;
    },

    /**
     * @author: justin
     * @description: 数据过滤回调
     * @param {*} data
     * @return {*}
     */
    filterTableDataCallback(data) {
      if (!data || data.length == 0) return data;

      const that = this;
      return data.filter(
        (item) =>
          item[that.tKey.prop].toLowerCase().includes(that.keyword) ||
          item[that.tValue.prop].toLowerCase().includes(that.keyword)
      );
    },

    /**
     * @author: justin
     * @description: 新增
     * @return {*}
     */
    add() {
      if (!this.api.add) return;
      this.isNew = true;
      this.showDrawer = true;
      this.mmappingListInit();
    },

    /**
     * @author: justin
     * @description: 双击row弹出编辑框
     * @param {*} row
     * @return {*}
     */
    dbClickRow(row) {
      if (!this.api.update) return;
      this.isNew = false;
      this.showDrawer = true;
      this.$nextTick(() => {
        this.$set(this.popupForm, 'code', row[this.tKey.prop]);
        this.$set(this.popupForm, 'name', row[this.tValue.prop]);
        this.mmappingListInit();
      });
    },

    /**
     * @author: justin
     * @description: 保存
     * @return {*}
     */
    submit() {
      this.$refs.ruleForm.validate((valid) => {
        valid = valid && this.exitKey();
        if (valid) {
          this.loadingSubmit = true;
          let data = {};
          data[this.tKey.prop] = this.popupForm.code;
          data[this.tValue.prop] = this.popupForm.name;
          this.$ajax
            .post(this.isNew ? this.api.add : this.api.update, data)
            .then(async (res) => {
              res = res.data;
              if (!res.success) {
                return;
              }
              await this.saveMapping();
              this.showDrawer = false;
              this.$nextTick(() => {
                this.$refs.allBtn_Ref.searchInfo = '';
                this.search();
              });
              this.$message({
                message: this.isNew ? '新增成功' : '修改成功',
                type: 'success',
                showClose: true
              });
            })
            .finally((_) => {
              this.loadingSubmit = false;
            });
        }
      });
    },

    /**
     * @author: justin
     * @description: 是否存在该代码
     * @return {*}
     */
    exitKey() {
      if (!this.isNew) return true;

      if (
        this.tableData.some(
          (item) => item[this.tKey.prop] === this.popupForm.code
        )
      ) {
        this.$message.warning('该代码已存在，请重新输入！');
        return false;
      }

      return true;
    },

    /**
     * @author: justin
     * @description: 表格多选行数据
     * @param {*} selection 已选中的行集合
     * @param {*} list 已选中行数据，若是全选操作，则会带出未在界面体现的数据
     * @return {*}
     */
    selectChange: function (selection, list) {
      this.multipleSelectionList = list;
    },

    /**
     * @author: justin
     * @description: 删除
     * @return {*}
     */
    remove() {
      if (this.multipleSelectionList.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据！',
          type: 'warning'
        });
      }
      if (this.multipleSelectionList.length > 0) {
        this.$confirm('确认删除已选择的记录？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.delete(this.multipleSelectionList);
        });
      }
    },

    /**
     * @author: justin
     * @description: 确认删除
     * @param {*} multipleSelectionList
     * @return {*}
     */
    delete(multipleSelectionList) {
      this.$ajax
        .post(
          this.api.delete,
          multipleSelectionList.map((x) => x[this.tKey.prop])
        )
        .then((res) => {
          let { success } = res.data;
          if (!success) return;
          this.$message({
            showClose: true,
            message: '删除成功',
            type: 'success'
          });
          this.$nextTick(() => {
            this.$refs.allBtn_Ref.searchInfo = '';
            this.search();
          });
        });
    },

    /**
     * @author: justin
     * @description: 导出excel
     * @return {*}
     */
    exportToExcel() {
      this.$refs.publicTable_Ref.exportToExcel({
        fileName: this.title + '列表'
      });
    },

    /**
     * @author: justin
     * @description: 对应列表初始化
     * @return {*}
     */
    mmappingListInit() {
      const that = this;
      switch (that.id) {
        case 'codeOccupationalCardType': // 证件类型
          that.showMappingField = true;
          that.mappingList = that.G_cardType.map((x) => {
            x.value = Number(x.value);
            return x;
          });
          if (!that.popupForm.code) {
            that.loadingDrawer = false;
            return;
          }

          that.$ajax
            .paramsPost(that.$apiUrls.QueryMapCardType, {
              cardTypeCode: that.popupForm.code
            })
            .then((res) => {
              if (!res.data.success || !res.data.returnData) return;

              if (res.data.returnData.length > 0) {
                that.popupForm.mappingCodeBk = res.data.returnData[0].peCode;
                that.$set(
                  that.popupForm,
                  'mappingCode',
                  that.popupForm.mappingCodeBk
                );
              }
            })
            .finally(() => {
              that.loadingDrawer = false;
            });
          break;

        case 'codeOccupationalMarryStatus': // 婚姻状况
          that.showMappingField = true;
          that.mappingList = that.G_marriageStatus.map((x) => {
            x.value = Number(x.value);
            return x;
          });
          if (!that.popupForm.code) {
            that.loadingDrawer = false;
            return;
          }

          that.$ajax
            .paramsPost(that.$apiUrls.QueryMapMarryStatus, {
              statusCode: that.popupForm.code
            })
            .then((res) => {
              if (!res.data.success || !res.data.returnData) return;

              if (res.data.returnData.length > 0) {
                that.popupForm.mappingCodeBk = res.data.returnData[0].peCode;
                that.$set(
                  that.popupForm,
                  'mappingCode',
                  that.popupForm.mappingCodeBk
                );
              }
            })
            .finally(() => {
              that.loadingDrawer = false;
            });
          break;
        default:
          that.loadingDrawer = false;
          break;
      }
    },

    /**
     * @author: justin
     * @description: 保存键值对对应内容
     * @return {*}
     */
    async saveMapping() {
      const that = this;
      let createFunc = null;
      switch (that.id) {
        case 'codeOccupationalCardType': // 证件类型
          createFunc = async () => {
            if (
              !that.popupForm.mappingCode &&
              typeof that.popupForm.mappingCode !== 'number'
            )
              return;
            await that.$ajax.post(that.$apiUrls.CreateMapCardType, [
              {
                occupationalCode: that.popupForm.code,
                peCode: that.popupForm.mappingCode
              }
            ]);
          };
          if (
            !that.popupForm.mappingCodeBk &&
            typeof that.popupForm.mappingCodeBk !== 'number'
          ) {
            createFunc();
            return;
          }
          await that.$ajax
            .post(that.$apiUrls.DeleteMapCardType, [
              {
                occupationalCode: that.popupForm.code,
                peCode: that.popupForm.mappingCodeBk
              }
            ])
            .then((res) => {
              if (!res.data.success) return;

              createFunc();
            });
          break;

        case 'codeOccupationalMarryStatus': // 婚姻状况
          createFunc = async () => {
            if (
              !that.popupForm.mappingCode &&
              typeof that.popupForm.mappingCode !== 'number'
            )
              return;
            await that.$ajax.post(that.$apiUrls.CreateMapMarryStatus, [
              {
                occupationalCode: that.popupForm.code,
                peCode: that.popupForm.mappingCode
              }
            ]);
          };
          if (
            !that.popupForm.mappingCodeBk &&
            typeof that.popupForm.mappingCodeBk !== 'number'
          ) {
            createFunc();
            return;
          }
          await that.$ajax
            .post(that.$apiUrls.DeleteMapMarryStatus, [
              {
                occupationalCode: that.popupForm.code,
                peCode: that.popupForm.mappingCodeBk
              }
            ])
            .then((res) => {
              if (!res.data.success) return;

              createFunc();
            });
          break;
      }
    }
  },
  activated() {
    this.$nextTick(() => {
      this.$refs.publicTable_Ref.doLayout();
    });
  }
};
</script>

<style lang="less" scoped>
.base-code-dic-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-weight: 600;

  header {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
    .el-form-item {
      margin-bottom: 18px;
    }
    .el-button--small {
      width: 64px;
    }
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-container.is-vertical {
    height: 100%;
  }
  .el-header,
  .el-main {
    padding: 0;
  }

  .searchWrap .el-form-item:last-child {
    margin-right: 0;
    margin-left: 10px;
  }
  .indexPage .contentWrap .tabPage {
    margin-top: 0;
  }

  .el-showDrawer.rtl {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #2d3436;
    padding: 0 20px 0 10px;
  }
  /deep/.el-drawer__body {
    padding-right: 20px;

    .el-form {
      padding-top: 20px;

      .el-form-item {
        margin-bottom: 18px;
      }
    }
  }

  /deep/ .el-select {
    width: 100%;
  }

  /deep/ .el-drawer__header {
    height: 42px;
    background: rgba(23, 112, 223, 0.2);
    margin-bottom: unset;
    color: #000;

    span,
    button {
      margin-top: -20px;
    }
  }
}
</style>
