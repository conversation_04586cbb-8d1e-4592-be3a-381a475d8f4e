<!--
 * @FilePath: \KrPeis\src\pages\serviceData\occDiseases\components\baseCodeOccupationalAuditoryCorrectedValueTable.vue
 * @Description:  检查项目表格组件
 * @Author: -
 * @Date: 2024-05-07 15:11:39
 * @Version: 0.0.1
 * @LastEditors: -
 * @LastEditTime: 2024-06-28 13:30:22
*
-->

<template>
  <div class="code-occupational-item-limit-table-job-diseases">
    <!-- 列表信息 -->
    <el-container>
      <el-header>
        <AllBtn
          :btnList="btnList"
          :methodSearch="search"
          :methodExport="exportToExcel"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          ref="publicTable_Ref"
          :theads="theads"
          :url="$apiUrls.ReadCodeOccupationalImportantMonitorItem"
          :params="params"
          rowKey="key"
          :excelDataMap="excelDataMap"
          @selectionChange="selectChange"
          @rowDblclick="dbClickRow"
          @request-success="responseDataCallback"
        >
          <template #sex="{ scope }">
            <span>{{ G_EnumList.Sex[scope.row.sex] }}</span>
          </template>
        </PublicTable>
      </el-main>
    </el-container>

    <!-- 弹出框基本信息 -->
    <el-drawer
      :title="title + '属性'"
      :visible.sync="showDrawer"
      :wrapperClosable="false"
      @closed="closedDrawer"
      @opened="openedDrawer"
    >
      <div v-loading="loadingDrawer">
        <el-form :model="popupForm" ref="ruleForm" :rules="rules">
          <el-form-item label="编码" :label-width="formLabelWidth" prop="key">
            <el-input
              v-model.trim="popupForm.key"
              size="small"
              placeholder="请输入编码"
              :disabled="true"
              :maxlength="5"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="名称"
            :label-width="formLabelWidth"
            prop="keyName"
          >
            <el-input
              v-model.trim="popupForm.keyName"
              size="small"
              placeholder="请输入编码"
              :disabled="true"
              :maxlength="5"
            ></el-input>
          </el-form-item>

          <el-form-item
            label="项目代码"
            :label-width="formLabelWidth"
            prop="itemCode"
          >
            <el-select
              v-model="popupForm.itemCode"
              placeholder="请选择项目代码"
              filterable
              clearable
              size="small"
              style="width: 100%"
            >
              <el-option
                v-for="item in itemList"
                :key="item.itemCode"
                :label="item.label"
                :value="item.itemName"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item
            label="结果代码"
            :label-width="formLabelWidth"
            prop="resultCode"
          >
            <el-input
              v-model.trim="popupForm.resultCode"
              size="small"
              placeholder="请输入结果代码"
              :maxlength="5"
            ></el-input>
          </el-form-item>

          <el-form-item
            label="结果上限"
            :label-width="formLabelWidth"
            prop="upperLimit"
          >
            <el-input
              v-model.trim="popupForm.upperLimit"
              size="small"
              placeholder="请输入结果上限"
              :maxlength="5"
            ></el-input>
          </el-form-item>

          <el-form-item
            label="结果下限"
            :label-width="formLabelWidth"
            prop="lowerLimit"
          >
            <el-input
              v-model.trim="popupForm.lowerLimit"
              size="small"
              placeholder="请输入结果下限"
              :maxlength="5"
            ></el-input>
          </el-form-item>

          <el-form-item
            label="项目单位代码"
            :label-width="formLabelWidth"
            prop="unitCode"
          >
            <el-input
              v-model.trim="popupForm.unitCode"
              size="small"
              placeholder="请输入项目单位代码"
              :maxlength="5"
            ></el-input>
          </el-form-item>

          <el-form-item
            label="项目单位名称"
            :label-width="formLabelWidth"
            prop="unitName"
          >
            <el-input
              v-model.trim="popupForm.unitName"
              size="small"
              placeholder="请输入项目单位名称"
              :maxlength="5"
            ></el-input>
          </el-form-item>
        </el-form>

        <div class="dialog-footer">
          <el-button
            @click="showDrawer = false"
            size="small"
            :loading="loadingSubmit"
            >取消</el-button
          >
          <el-button
            type="primary"
            @click="submit"
            size="small"
            :loading="loadingSubmit"
            >保存</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { dataUtils } from '@/common';
import PublicTable from '@/components/publicTable2.vue';
import AllBtn from '../../allBtn.vue';
import { mapGetters } from 'vuex';

export default {
  name: 'baseCodeOccupationalImportantMonitorItemTable',
  components: { PublicTable, AllBtn },
  data() {
    return {
      title: '重点监测项目',
      btnList: ['查询', '导出'],
      keyword: '',
      params: {},
      multipleSelectionList: [],
      excelData: [],
      tableData: [],
      theads: [
        {
          prop: 'key',
          label: '编码',
          align: '',
          width: '',
          sortable: false
        },
        {
          prop: 'keyName',
          label: '名称',
          align: '',
          width: '80px',
          sortable: false
        },
        {
          prop: 'itemCode',
          label: '项目代码',
          align: '',
          width: '120px',
          sortable: false
        },
        {
          prop: 'resultCode',
          label: '结果代码',
          align: '',
          width: '120px',
          sortable: false
        },
        {
          prop: 'upperLimit',
          label: '结果上限',
          align: '',
          width: '120px',
          sortable: false
        },
        {
          prop: 'lowerLimit',
          label: '结果下限',
          align: '',
          width: '120px',
          sortable: false
        },
        {
          prop: 'unitCode',
          label: '项目单位代码',
          align: '',
          width: '120px',
          sortable: false
        },
        {
          prop: 'unitName',
          label: '项目单位名称',
          align: '',
          width: '120px',
          sortable: false
        }
      ],
      showDrawer: false,
      formLabelWidth: '150px',
      popupForm: {
        key: '',
        itemCode: '',
        resultCode: '',
        upperLimit: '',
        lowerLimit: '',
        unitCode: '',
        unitName: '',
        keyName: ''
      },
      rules: {
        itemCode: [
          {
            required: true,
            message: `请输入项目代码性别`,
            trigger: 'blur'
          }
        ]
      },
      isNew: false,
      loadingSubmit: false,
      sexTypeList: [],
      itemList: [],
      loadingDrawer: false
    };
  },
  mounted() {
    this.search();
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_sexList'])
  },
  methods: {
    /**
     * @author: justin
     * @description: 模糊查询
     * @return {*}
     */
    search() {
      const that = this;
      that.keyword = (that.$refs.allBtn_Ref.searchInfo || '')
        .trim()
        .toLowerCase();
      if (!that.keyword) {
        that.params = [];
        that.$refs.publicTable_Ref.loadData();
      }
    },

    /**
     * @author: justin
     * @description: 获取记录响应回调
     * @param {*} data
     * @return {*}
     */
    responseDataCallback(data) {
      this.tableData = data.returnData;
    },

    /**
     * @author: justin
     * @description: 数据过滤回调
     * @param {*} data
     * @return {*}
     */
    filterTableDataCallback(data) {
      if (!data || data.length == 0) return data;

      const that = this;
      return data.filter((item) =>
        item.correctedValueCode.toLowerCase().includes(that.keyword)
      );
    },

    /**
     * @author: justin
     * @description: 表格多选行数据
     * @param {*} selection 已选中的行集合
     * @param {*} list 已选中行数据，若是全选操作，则会带出未在界面体现的数据
     * @return {*}
     */
    selectChange: function (selection, list) {
      this.multipleSelectionList = list;
    },

    /**
     * @author: justin
     * @description: 导出excel
     * @return {*}
     */
    exportToExcel() {
      this.$refs.publicTable_Ref.exportToExcel({
        fileName: this.title + '列表'
      });
    },

    /**
     * @author: justin
     * @description: 导出excel数据处理
     * @param {*} data
     * @return {*}
     */
    excelDataMap(data) {
      return data.map((x, index) => {
        x.sex = this.G_EnumList.Sex[x.sex];
        return x;
      });
    },

    /**
     * @author: justin
     * @description: 关闭弹出框回调
     * @return {*}
     */
    closedDrawer() {
      this.$nextTick(function () {
        this.$refs.ruleForm.resetFields();
      });
    },

    /**
     * @author: justin
     * @description: 新增
     * @return {*}
     */
    add() {
      this.isNew = true;
      this.showDrawer = true;
    },

    /**
     * @author: justin
     * @description: 双击row弹出编辑框
     * @param {*} row
     * @return {*}
     */
    dbClickRow(row) {
      this.isNew = false;
      this.showDrawer = true;
      this.$nextTick(() => {
        this.popupForm = dataUtils.deepCopy(row);
      });
    },

    /**
     * @author: justin
     * @description: 保存
     * @return {*}
     */
    submit() {
      this.$refs.ruleForm.validate((valid) => {
        valid = valid && this.exitKey();
        if (valid) {
          this.loadingSubmit = true;
          this.$ajax
            .post(
              this.$apiUrls.SaveCodeOccupationalImportantMonitorItem,
              this.popupForm
            )
            .then((res) => {
              res = res.data;
              if (!res.success) {
                return;
              }
              this.showDrawer = false;
              this.$nextTick(() => {
                this.$refs.allBtn_Ref.searchInfo = '';
                this.search();
              });
              this.$message({
                message: this.isNew ? '新增成功' : '修改成功',
                type: 'success',
                showClose: true
              });
            })
            .finally((_) => {
              this.loadingSubmit = false;
            });
        }
      });
    },

    /**
     * @author: justin
     * @description: 是否存在该代码
     * @return {*}
     */
    exitKey() {
      if (!this.isNew) return true;

      if (
        this.tableData.some(
          (item) =>
            item.correctedValueCode === this.popupForm.correctedValueCode
        )
      ) {
        this.$message.warning('该代码已存在，请重新输入！');
        return false;
      }

      return true;
    },

    /**
     * @author: justin
     * @description: 删除
     * @return {*}
     */
    remove() {
      if (this.multipleSelectionList.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据！',
          type: 'warning'
        });
      }
      if (this.multipleSelectionList.length > 0) {
        this.$confirm('是否确认删除已选择的记录？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.delete(this.multipleSelectionList);
        });
      }
    },

    /**
     * @author: justin
     * @description: 确认删除
     * @param {*} multipleSelectionList
     * @return {*}
     */
    delete(multipleSelectionList) {
      this.$ajax
        .post(
          this.$apiUrls.DeleteCodeAuditoryCorrectedValue,
          multipleSelectionList.map((x) => x.correctedValueCode)
        )
        .then((res) => {
          let { success } = res.data;
          if (!success) return;
          this.$message({
            showClose: true,
            message: '删除成功',
            type: 'success'
          });
          this.$nextTick(() => {
            this.$refs.allBtn_Ref.searchInfo = '';
            this.search();
          });
        });
    },

    /**
     * @author: justin
     * @description:  打开弹出框回调
     * @return {*}
     */
    openedDrawer() {
      this.$ajax
        .post(this.$apiUrls.PageQuery_CodeItem, '', {
          query: {
            clsCode: ''
          }
        })
        .then((r) => {
          this.itemList = r.data.returnData || [];
        });
    }
  },
  activated() {
    this.$nextTick(() => {
      this.$refs.publicTable_Ref.doLayout();
    });
  }
};
</script>

<style lang="less" scoped>
.code-occupational-item-limit-table-job-diseases {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-weight: 600;
  header {
    display: flex;
    justify-content: flex-end;
    .el-form-item {
      margin-bottom: 18px;
    }
    .el-button--small {
      width: 64px;
    }
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-container.is-vertical {
    height: 100%;
  }
  .el-header,
  .el-main {
    padding: 0;
  }

  .searchWrap .el-form-item:last-child {
    margin-right: 0;
    margin-left: 10px;
  }
  .indexPage .contentWrap .tabPage {
    margin-top: 0;
  }

  .el-showDrawer.rtl {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #2d3436;
    padding: 0 20px 0 10px;
  }
  /deep/.el-drawer__body {
    padding-right: 20px;

    .el-form {
      padding-top: 20px;

      .el-form-item {
        margin-bottom: 18px;
      }
    }
  }

  /deep/ .el-select {
    width: 100%;
  }

  /deep/ .el-drawer__header {
    height: 42px;
    background: rgba(23, 112, 223, 0.2);
    margin-bottom: unset;
    color: #000;

    span,
    button {
      margin-top: -20px;
    }
  }
}
</style>
