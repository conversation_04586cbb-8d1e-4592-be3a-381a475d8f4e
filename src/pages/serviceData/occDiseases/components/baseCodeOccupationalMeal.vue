<!--
 * @Date: 2022-06-08 09:11:19
 * @Autor: lvshuwen
 * @LastEditTime: 2025-03-14 09:48:50
 * @Description: 体检套餐信息
-->

<template>
  <div class="classifyInformation">
    <!-- 体检套餐信息 -->
    <el-container>
      <el-header>
        <AllBtn
          :btnList="['查询', '新建', '删除', '打印', '导出']"
          :methodCreat="newProject"
          :methodDelete="delMore"
          :methodSearch="search"
          :methodPrint="print"
          :methodExport="exportTable"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          @rowDblclick="handleClick"
          :cell_blue="cell_blue"
          @selectionChange="handleSelectChangeTable"
          :tableLoading.sync="loading"
          isCheck
          :columnWidth="columnWidth"
        >
          <template #wechatShow="{ scope }">
            {{ scope.row.wechatShow ? '是' : '否' }}
          </template>
          <template #sex="{ scope }">
            {{ G_EnumList['Sex'][scope.row.sex] }}
          </template>
          <template #columnRight>
            <el-table-column
              prop="operation"
              label="操作"
              width="200"
              fixed="right"
            >
              <template slot-scope="scope">
                <el-button
                  type="primary"
                  plain
                  size="mini"
                  class="view"
                  @click="viewDetails(scope.row)"
                  >对应详情</el-button
                >
                <el-button
                  type="primary"
                  plain
                  size="mini"
                  class="view"
                  @click="viewDetail(scope.row)"
                  >互斥组合</el-button
                >
              </template>
            </el-table-column>
          </template>
        </PublicTable>
      </el-main>
    </el-container>
    <el-drawer
      title="体检套餐信息属性"
      :visible.sync="drawer"
      :before-close="handleClose"
      :wrapperClosable="false"
      size="50%"
    >
      <el-form :model="popupForm" ref="ruleForm" :rules="rules">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              v-if="funTpye !== '/Create'"
              label="套餐代码"
              :label-width="formLabelWidth"
              prop="clusCode"
            >
              <el-input
                v-model.trim="popupForm.clusCode"
                autocomplete="off"
                size="small"
                :disabled="disabled"
                placeholder="请输入套餐代码"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              label="套餐名称"
              :label-width="formLabelWidth"
              prop="clusName"
            >
              <el-input
                v-model.trim="popupForm.clusName"
                autocomplete="off"
                size="small"
                placeholder="请输入套餐名称"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              label="单价"
              :label-width="formLabelWidth"
              prop="price"
            >
              <el-input
                v-model.trim="popupForm.price"
                autocomplete="off"
                size="small"
                placeholder="请输入单价"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="性别" :label-width="formLabelWidth" prop="sex">
              <el-select
                class="select"
                v-model.trim="popupForm.sex"
                placeholder="请选择性别"
                size="small"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="item in G_shareSexList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="年龄上限"
              :label-width="formLabelWidth"
              prop="upperAgeLimit"
            >
              <el-input
                v-model.trim="popupForm.upperAgeLimit"
                autocomplete="off"
                size="small"
                placeholder="请输入年龄上限"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              label="年龄下限"
              :label-width="formLabelWidth"
              prop="lowerAgeLimit"
            >
              <el-input
                v-model.trim="popupForm.lowerAgeLimit"
                autocomplete="off"
                size="small"
                placeholder="请输入年龄下限"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              label="显示顺序"
              :label-width="formLabelWidth"
              prop="sortIndex"
            >
              <el-input
                v-model.trim="popupForm.sortIndex"
                autocomplete="off"
                size="small"
                placeholder="请输入显示顺序"
                clearable
              ></el-input>
            </el-form-item>
            <!-- <el-form-item
              label="体检分类"
              :label-width="formLabelWidth"
              prop="peCls"
            >
              <el-select
                class="select"
                v-model.trim="popupForm.peCls"
                placeholder="请选择体检分类"
                size="small"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="item in G_peClsList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item> -->
            <el-form-item
              label="微信显示套餐"
              :label-width="formLabelWidth"
              prop="wechatShow"
            >
              <el-select
                class="select"
                v-model.trim="popupForm.wechatShow"
                placeholder="请选择微信显示套餐"
                size="small"
                style="width: 100%"
                clearable
              >
                <el-option label="是" :value="true"></el-option>
                <el-option label="否" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="在岗状态"
              :label-width="formLabelWidth"
              prop="jobStatus"
            >
              <el-select
                clearable
                class="select"
                style="width: 100%"
                v-model.trim="popupForm.jobStatus"
                placeholder="请选择"
                size="small"
                filterable
              >
                <el-option
                  v-for="(item, index) in G_CodeOccupationalPositionStatus"
                  :key="index"
                  :value="item.value"
                  :label="item.label"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="注意事项"
              :label-width="formLabelWidth"
              prop="attention"
            >
              <el-input
                type="textarea"
                v-model.trim="popupForm.attention"
                autocomplete="off"
                size="small"
                placeholder="请输入注意事项"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12"
            ><el-form-item
              label="启用标识"
              :label-width="formLabelWidth"
              prop="isEnabled"
            >
              <el-radio-group v-model.trim="popupForm.isEnabled">
                <el-radio :label="true">启用</el-radio>
                <el-radio :label="false">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="申请单格式"
              :label-width="formLabelWidth"
              prop="guidanceType"
            >
              <el-select
                class="select"
                v-model.trim="popupForm.guidanceType"
                placeholder="请选择申请单格式"
                size="small"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="item in G_codeGuidanceType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="报告单格式"
              :label-width="formLabelWidth"
              prop="reportType"
            >
              <el-select
                class="select"
                v-model.trim="popupForm.reportType"
                placeholder="请选择报告单格式"
                size="small"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="item in G_codeReportType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="拼音码"
              :label-width="formLabelWidth"
              prop="pinYinCode"
            >
              <el-input
                v-model.trim="popupForm.pinYinCode"
                autocomplete="off"
                size="small"
                placeholder="请输入拼音码"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              label="五笔码"
              :label-width="formLabelWidth"
              prop="wuBiCode"
            >
              <el-input
                v-model.trim="popupForm.wuBiCode"
                autocomplete="off"
                size="small"
                placeholder="请输入五笔码"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              label="打印健康证"
              :label-width="formLabelWidth"
              prop="printJKZ"
            >
              <el-select
                class="select"
                v-model.trim="popupForm.printJKZ"
                placeholder="请选择打印健康证"
                size="small"
                style="width: 100%"
                clearable
              >
                <el-option label="是" :value="true"></el-option>
                <el-option label="否" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="危害因素"
              :label-width="formLabelWidth"
              prop="hazardFactors"
            >
              <el-select
                class="select"
                style="width: 100%"
                multiple
                v-model.trim="popupForm.hazardFactors"
                placeholder="请选择"
                size="small"
                filterable
              >
                <el-option
                  v-for="(item, index) in harmList"
                  :key="index"
                  :value="item.hazardousCode"
                  :label="item.hazardousName"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="体检须知"
              :label-width="formLabelWidth"
              prop="note"
            >
              <el-input
                v-model.trim="popupForm.note"
                type="textarea"
                autocomplete="off"
                size="small"
                placeholder="请输入体检须知"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12"> </el-col>
          <el-col :span="12"> </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12"> </el-col>
          <el-col :span="12"> </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12"> </el-col>
          <el-col :span="12"> </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12"> </el-col>
          <el-col :span="12"> </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12"> </el-col>
          <el-col :span="12"> </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12"> </el-col>
          <el-col :span="12"> </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12"> </el-col>
          <el-col :span="12"> </el-col>
        </el-row>
        <!-- <el-row :gutter="20">
          <el-col :span="12"> </el-col>
          <el-col :span="12"></el-col>
        </el-row> -->
      </el-form>
      <div class="dialog-footer">
        <el-button @click="handleClose" size="small">取消</el-button>
        <el-button class="blue_btn" @click="submit" size="small"
          >保存</el-button
        >
      </div>
    </el-drawer>
    <el-drawer
      :title="drawerTitle"
      :visible.sync="drawers"
      :close-on-click-modal="false"
      :wrapperClosable="false"
      size="80%"
    >
      <DetailsDrawer
        :cancel="handleClose"
        :drawerInfo="drawerInfo"
        ref="drawer_ref"
      ></DetailsDrawer>
    </el-drawer>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { export2Excel } from '@/common/excelUtil';
import moment from 'moment';
import PublicTable from '@/components/publicTable.vue';
import AllBtn from '@/pages/serviceData/allBtn.vue';
import DetailsDrawer from '@/pages/serviceData/otherCodeMapping/detailsDrawer.vue';
import { dataUtils } from '@/common';
import Exceljs from '@/common/excel/groupDetailedExpenseReportExcel';
export default {
  name: 'baseCodeOccupationalMeal',
  mixins: [Exceljs],
  components: { PublicTable, AllBtn, DetailsDrawer },
  props: {
    //总传
    allInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    // 价格校验
    const checkPrice = (rule, value, callback) => {
      if (value === '' || value === null) {
        callback(new Error('请输入价格'));
      } else {
        let reg = /^([1-9]\d*(\.\d*[1-9])?)|(0\.\d*[1-9])$/;
        if (!reg.test(value)) {
          callback(new Error('输入的价格必须大于0'));
        } else {
          callback();
        }
      }
    };
    return {
      harmList: [], //危害因素列表
      drawerTitle: '',
      drawerInfo: this.allInfo, //总传
      drawerTheads: {},
      formInline: '',
      codeArr: [],
      resizeFlag: '',
      loading: false,
      disabled: false,
      tableDataCopy: [],
      tableData: [],
      pageSize: 50,
      currentPage: 1,
      dialogTitle: '',
      drawer: false,
      drawers: false,
      isAdd: true,
      formLabelWidth: '120px',
      roleList: [],
      optionsReportType: [],
      optionsGuidanceType: [],
      popupForm: {
        hazardFactors: [],
        jobStatus: '',
        clusCode: '',
        clusName: '',
        price: 99999,
        sex: 0,
        peCls: -1,
        guidanceType: '',
        reportType: '',
        attention: '',
        note: '',
        lowerAgeLimit: 0,
        upperAgeLimit: 99,
        printJKZ: false,
        isEnabled: true,
        wechatShow: false,
        sortIndex: 99999,
        pinYinCode: '',
        wuBiCode: ''
      },
      theads: {
        clusCode: '参考套餐代码',
        clusName: '参考套餐名称',
        price: '价格',
        sex: '性别',
        sortIndex: '显示顺序',
        upperAgeLimit: '年龄上限',
        lowerAgeLimit: '年龄下限',
        wechatShow: '微信显示',
        attention: '注意事项'
      },
      cell_blue: ['clusName'],
      columnWidth: {
        clusCode: 120,
        clusName: 200,
        attention: 320
      }, //设置宽度
      rules: {
        // clusCode: [
        //   { required: true, message: "请输入参考套餐代码", trigger: "blur" }
        // ],
        clusName: [
          { required: true, message: '请输入参考套餐名称', trigger: 'blur' }
        ],
        price: [{ validator: checkPrice, trigger: 'blur' }],
        sex: [{ required: true, message: '请选择性别', trigger: 'change' }],
        reportType: [
          { required: true, message: '请选择报告单格式', trigger: 'change' }
        ],
        guidanceType: [
          { required: true, message: '请选择申请单格式', trigger: 'change' }
        ],
        //  peCls: [
        //   { required: true, message: "请选择体检分类", trigger: "change" }
        // ],
        jobStatus: [
          { required: true, message: '请选择在岗状态', trigger: 'change' }
        ],
        hazardFactors: [
          { required: true, message: '请选择危害因素', trigger: 'change' }
        ]
      },
      funTpye: '/Create',
      excelList: []
    };
  },
  created: function () {},
  mounted: function () {
    this.getItemCls();
    this.getHarmList();
    console.log('[ this0.G_codeReportType ]-501', this.G_codeReportType);
    console.log('[ this.G_codeGuidanceType ]-502', this.G_codeGuidanceType);
  },
  computed: {
    ...mapGetters([
      'G_peClsList',
      'G_codeGuidanceType',
      'G_codeReportType',
      'G_EnumList',
      'G_shareSexList',
      'G_CodeOccupationalPositionStatus'
    ])
  },
  methods: {
    getHarmList() {
      let datas = {
        pageSize: 0,
        pageNumber: 0
      };
      this.$ajax
        .post(this.$apiUrls.ReadCodeOccupationalHazardousByPage, datas)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.harmList = returnData;
        });
    },
    //对应详情
    viewDetails(row) {
      console.log(this);
      console.log(row);
      this.drawers = true;
      this.drawerTitle = row.clusName + '-组合对应';
      this.drawerInfo.title2 = row.clusName + '-组合对应列表';
      this.drawerInfo.setInfo = { clusCode: row.clusCode };
      this.drawerInfo.setInfos = { clusCode: row.clusCode };
      this.drawerInfo.apiUrls = {
        optionPort: '/EnumData/ItemCls', //下拉
        leftPort: '/EnumData/ItemComb_ItemCls', //左边表格
        rightPort: '/CodeMapping/Query_ClusterComb', //右边表格(query:clusCode)
        del: '/CodeMapping/CD_ClusterComb/Delete',
        set: '/CodeMapping/CD_ClusterComb/Create'
      };
      console.log('[ this.drawerInfo ]-841', this.drawerInfo);
      //执行获取下拉,左右表格数据
      this.$nextTick(() => {
        this.$refs.drawer_ref.getAll();
        this.$refs.drawer_ref.clearInput();
      });
    },
    // 互斥组合
    viewDetail(row) {
      this.drawers = true;
      this.drawerTitle = row.clusName + '-组合互斥';
      this.drawerInfo.title1 = '体检组合项目列表';
      this.drawerInfo.title2 = row.clusName + '-组合互斥列表';
      this.drawerInfo.setInfo = { clusCode: row.clusCode };
      this.drawerInfo.setInfos = { clusCode: row.clusCode };
      this.drawerInfo.param = {
        combCode: 'combCode'
      };
      this.drawerInfo.apiUrls = {
        optionPort: '/EnumData/ItemCls', //下拉
        leftPort: '/EnumData/ItemComb_ItemCls', //左边表格
        rightPort: '/CodeMapping/PageQuery_ClusMutexComb', //右边表格(query:itemCode)
        del: '/CodeMapping/CD_ClusMutexComb/Delete',
        set: '/CodeMapping/CD_ClusMutexComb/Create'
      };

      console.log('[ this.drawerInfo ]-841', this.drawerInfo);
      //执行获取下拉,左右表格数据
      this.$nextTick(() => {
        this.$refs.drawer_ref.getAll();
        this.$refs.drawer_ref.clearInput();
      });
    },

    handleClose() {
      this.drawer = false;
      this.drawers = false;
      this.$refs.ruleForm.resetFields();
    },
    //查询
    search() {
      this.formInline = this.$refs.allBtn_Ref.searchInfo.trim(); //获取组件文本框的值
      if (this.formInline.length > 0) {
        this.tableData = this.tableDataCopy.filter((item) => {
          return (
            item.clusCode.indexOf(this.formInline) !== -1 ||
            item.clusName.indexOf(this.formInline) !== -1
          );
        });
      } else {
        this.getItemCls();
      }
      //this.loading = false;
    },

    //编辑
    handleClick(row, column, event) {
      console.log('row', row);
      this.drawer = true;
      this.disabled = true;
      this.funTpye = '/Update';
      this.popupForm = {
        clusCode: row.clusCode,
        clusName: row.clusName,
        price: row.price,
        sex: row.sex,
        guidanceType: row.guidanceType,
        reportType: row.reportType,
        attention: row.attention,
        note: row.note,
        lowerAgeLimit: row.lowerAgeLimit,
        upperAgeLimit: row.upperAgeLimit,
        printJKZ: row.printJKZ,
        isEnabled: row.isEnabled,
        wechatShow: row.wechatShow,
        sortIndex: row.sortIndex,
        pinYinCode: row.pinYinCode,
        wuBiCode: row.wuBiCode,
        hazardFactors: row.hazardFactors,
        jobStatus: row.jobStatus
      };
    },
    //新增
    newProject() {
      this.popupForm = {
        clusCode: '',
        clusName: '',
        price: 99999,
        sex: 0,
        peCls: -1,
        guidanceType: this.G_userInfo?.systemParams?.guidanceType ?? '',
        reportType:
          this.G_codeReportType[this.G_codeReportType.length - 1].value,
        attention: '',
        note: '',
        lowerAgeLimit: 0,
        upperAgeLimit: 99,
        printJKZ: false,
        isEnabled: true,
        wechatShow: false,
        sortIndex: 99999,
        pinYinCode: '',
        wuBiCode: ''
      };
      this.drawer = true;
      this.funTpye = '/Create';
      this.disabled = false;

      //获取报告样式
      // this.getGuidanceType();
      //获取指引单样式
      // this.getReportType();
    },
    // 获取套餐分类信息数据
    getItemCls() {
      console.log(this.$apiUrls);
      this.loading = true;
      this.$ajax
        .post(this.$apiUrls.RD_CodeClusterOccupation + '/Read', [])
        .then((r) => {
          this.tableData = r.data.returnData.map((item) => {
            return {
              clusCode: item.clusCode,
              clusName: item.clusName,
              price: item.price,
              sex: item.sex,
              peCls: -1,
              guidanceType: item.guidanceType,
              reportType: item.reportType,
              attention: item.attention,
              note: item.note,
              lowerAgeLimit: item.lowerAgeLimit,
              upperAgeLimit: item.upperAgeLimit,
              printJKZ: item.printJKZ,
              isEnabled: item.isEnabled,
              wechatShow: item.wechatShow,
              sortIndex: item.sortIndex,
              pinYinCode: item.pinYinCode,
              wuBiCode: item.wuBiCode,
              hazardFactors: item.hazardFactors,
              jobStatus: item.jobStatus
            };
          });
          this.tableDataCopy = r.data.returnData;
          this.loading = false;
        });
    },
    //提交
    submit() {
      this.popupForm.price = Number(this.popupForm.price);
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$ajax
            .post(
              this.$apiUrls.CU_CodeClusterOccupation + this.funTpye,
              this.popupForm
            )
            .then((r) => {
              r = r.data;
              if (!r.success) {
                return;
              }
              this.tableData = [];
              this.drawer = false;
              this.$nextTick(() => {
                this.$refs.allBtn_Ref.searchInfo = '';
                this.search();
              });
              if (this.funTpye == '/Create') {
                // this.addComb(this.popupForm,r.returnData);
                this.$message({
                  message: '新建成功',
                  type: 'success',
                  showClose: true
                });
              } else {
                this.$message({
                  message: '修改成功',
                  type: 'success',
                  showClose: true
                });
              }
            });
        } else {
          return false;
        }
      });
    },
    //添加组合对应
    addComb(from, clusCode) {
      let data = [];
      from.hazardFactors.forEach((item) => {
        let combsByStatus = this.harmList.find(
          (h) => h.hazardousCode == item
        ).combsByStatus;
        combsByStatus[from.jobStatus].forEach((c) => {
          if (data.findIndex((d) => d.combCode == c) == -1) {
            data.push({
              clusCode: clusCode,
              combCode: c
            });
          }
        });
      });
      this.$ajax.post('/CodeMapping/CD_ClusterComb/Create', data).then((r) => {
        if (!r.data.success) {
          this.$message.error('添加组合对应失败!');
          return;
        }
      });
    },
    //获取指引单样式
    getReportType() {
      this.$ajax.post(this.$apiUrls.GetReportType, []).then((r) => {
        //
        this.optionsGuidanceType = r.data.returnData;
      });
    },
    //获取报告样式
    getGuidanceType() {
      this.$ajax.post(this.$apiUrls.GetGuidanceType, []).then((r) => {
        //
        this.optionsReportType = r.data.returnData;
      });
    },
    //复选框勾选操作
    handleSelectChangeTable: function (val) {
      //console.log(val);
      this.excelList = val;
      this.codeArr = [];
      val.map((item, i) => {
        if (item.clusCode != '') {
          this.codeArr.push(item.clusCode);
        }
      });
      //console.log(this.codeArr);
    },
    //多条删除
    delMore() {
      if (this.codeArr.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据!',
          type: 'warning'
        });
      }
      if (this.codeArr.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            // this.delete(this.codeArr);
            console.log(this.codeArr);
            this.$ajax
              .post(
                this.$apiUrls.RD_CodeClusterOccupation + '/Delete',
                this.codeArr
              )
              .then((r) => {
                let { success } = r.data;
                if (!success) return;
                this.$message({
                  showClose: true,
                  message: '删除成功',
                  type: 'success'
                });
                this.getItemCls();
              });
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    // 导出excel
    exportTable() {
      this.$confirm('确定下载职业病体检套餐?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.exportLoading = true;
        this.$ajax
          .post(this.$apiUrls.OccGetCodeClusterWithDetails)
          .then((r) => {
            let { returnData } = r.data;
            if (returnData?.length == 0) {
              this.$message({
                message: '暂无套餐!',
                type: 'warning',
                showClose: true
              });
              return;
            }
            returnData.forEach((item) => {
              switch (item.sex) {
                case 1:
                  item.sex = '男';
                  break;
                case 2:
                  item.sex = '女';
                  break;

                default:
                  item.sex = '通用';
                  break;
              }
              item.wechatShow = item.wechatShow ? '是' : '否';
            });
            let theads = {
              combCode: '组合代码',
              combName: '组合名称',
              discount: '折扣',
              originalPrice: '原价',
              price: '单价'
            };
            let clusInfo =
              '`套餐名称：${item.clusName}   套餐总价：${item.price}   性别：${item.sex}   上岗状态：${item.jobStatusName}   危害因素：${item.hazardNames}   显示顺序：${item.sortIndex}   年龄上限：${item.upperAgeLimit}   年龄下限：${item.lowerAgeLimit}   微信显示：${item.wechatShow}   \n注意事项：${item.note}`';
            this.exportExcel(
              theads,
              returnData || [],
              ``,
              clusInfo,
              `职业病体检套餐`,
              'mapClusterCombs'
            );
          })
          .finally((r) => {
            this.exportLoading = false;
          });
      });
    },
    print() {}
  }
};
</script>
<style lang="less" scoped>
.classifyInformation {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  font-weight: 600;
  header {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-container.is-vertical {
    height: 100%;
  }
  .el-header,
  .el-main {
    padding: 0;
  }

  .searchWrap .el-form-item:last-child {
    margin-right: 0;
    margin-left: 10px;
  }
  .indexPage .contentWrap .tabPage {
    margin-top: 0;
  }
  .el-pagination {
    text-align: center;
  }
  .el-drawer.rtl {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #2d3436;
    padding: 0 20px 0 10px;
  }
  .el-drawer__body {
    padding-right: 20px;

    .el-form-item {
      margin-bottom: 10px;
    }
  }
  /deep/.el-drawer__header {
    align-items: center;
    display: flex;
    background: rgba(23, 112, 223, 0.2);
    border-radius: 4px 4px 0 0;
    line-height: 42px;
    font-family: PingFangSC-Medium;
    font-size: 18px;
    padding: 0;
    padding-left: 20px;
    color: #2d3436;
    margin-bottom: 0;
  }
  /deep/.el-drawer__close-btn {
    font-size: 30px;
  }
}
</style>
<style>
.el-dialog {
  width: 480px;
}
.el-table .el-table__cell {
  padding: 5px 0;
}
.el-main .el-checkbox__inner {
  width: 20px;
  height: 20px;
}
.el-drawer__body {
  padding-right: 20px;
}
.el-drawer__header,
.el-form-item__label {
  color: #2d3436;
}
</style>
