<!--
 * @FilePath: \KrPeis\src\pages\serviceData\occDiseases\components\baseCodeOccupationalHazardousTable.vue
 * @Description:  危害因素表格组件
 * @Author: justin
 * @Date: 2024-05-07 15:11:39
 * @Version: 0.0.1
 * @LastEditors: justin
 * @LastEditTime: 2024-06-20 11:18:12
*
-->

<template>
  <div class="code-hazardous-table-job-diseases">
    <!-- 列表信息 -->
    <el-container>
      <el-header>
        <AllBtn
          :btnList="btnList"
          :methodSearch="search"
          :methodCreat="add"
          :methodDelete="remove"
          :methodExport="exportToExcel"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          ref="publicTable_Ref"
          :theads="theads"
          :url="$apiUrls.ReadCodeOccupationalHazardousByPage"
          :params="params"
          rowKey="hazardousCode"
          remoteByPage
          :excelDataMap="excelDataMap"
          @selectionChange="selectChange"
          @rowDblclick="dbClickRow"
          @request-success="responseDataCallback"
        >
          <template #isMainDust="{ scope }">
            <el-checkbox v-model="scope.row.isMainDust" disabled></el-checkbox>
          </template>

          <template #isOtherHazardous="{ scope }">
            <el-checkbox
              v-model="scope.row.isOtherHazardous"
              disabled
            ></el-checkbox>
          </template>
          <template #operation="{ scope }">
            <el-button size="mini" @click="showCombDialog(scope.row)"
              >体检组合对应</el-button
            >
          </template>
        </PublicTable>
      </el-main>
    </el-container>

    <!-- 弹出框基本信息 -->
    <el-drawer
      :title="title + '属性'"
      :visible.sync="showDrawer"
      :wrapperClosable="false"
      @closed="closedDrawer"
      @opened="openedDrawer"
    >
      <div v-loading="loadingDrawer">
        <el-form :model="popupForm" ref="ruleForm" :rules="rules">
          <el-form-item
            label="危害因素代码"
            :label-width="formLabelWidth"
            prop="hazardousCode"
          >
            <el-input
              v-model.trim="popupForm.hazardousCode"
              size="small"
              placeholder="请输入危害因素代码"
              :disabled="!isNew"
              :maxlength="10"
            ></el-input>
          </el-form-item>

          <el-form-item
            label="危害因素名称"
            :label-width="formLabelWidth"
            prop="hazardousName"
          >
            <el-input
              v-model.trim="popupForm.hazardousName"
              autocomplete="off"
              size="small"
              placeholder="请输入危害因素名称"
              :maxlength="80"
            ></el-input>
          </el-form-item>

          <el-form-item
            label="危害因素种类"
            :label-width="formLabelWidth"
            prop="hazardousType"
          >
            <el-select
              v-model="popupForm.hazardousType"
              placeholder="请选择危害因素种类"
              filterable
              clearable
            >
              <el-option
                v-for="item in hazardousTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item
            label="是否主要粉尘"
            :label-width="formLabelWidth"
            prop="isMainDust"
          >
            <el-checkbox v-model="popupForm.isMainDust"></el-checkbox>
          </el-form-item>

          <el-form-item
            label="是否其他危害因素"
            :label-width="formLabelWidth"
            prop="isOtherHazardous"
          >
            <el-checkbox v-model="popupForm.isOtherHazardous"></el-checkbox>
          </el-form-item>
        </el-form>

        <div class="dialog-footer">
          <el-button
            @click="showDrawer = false"
            size="small"
            :loading="loadingSubmit"
            >取消</el-button
          >
          <el-button
            type="primary"
            @click="submit"
            size="small"
            :loading="loadingSubmit"
            >保存</el-button
          >
        </div>
      </div>
    </el-drawer>

    <!-- 弹出框体检组合对应 -->
    <el-drawer
      :title="currentRow.hazardousName + '-体检组合对应'"
      :visible.sync="showCombDrawer"
      :wrapperClosable="false"
      size="60%"
      @closed="closedCombDrawer"
      @opened="openedCombDrawer"
    >
      <div v-loading="loadingCombDrawer">
        <!-- 体检组合内容  -->
        <div class="code-item-wrapper">
          <el-row
            :gutter="10"
            type="flex"
            justify="space-between"
            align="middle"
          >
            <el-col :span="8"> </el-col>
            <el-col :span="6">
              <el-select
                v-model="combClsCode"
                filterable
                clearable
                @change="searchComb"
                size="mini"
                placeholder="组合分类筛选"
              >
                <el-option
                  v-for="item in combClsList"
                  :key="item.clsCode"
                  :label="item.clsName"
                  :value="item.clsCode"
                /> </el-select
            ></el-col>
            <el-col :span="10">
              <AllBtn
                :btnList="['查询']"
                :methodSearch="searchComb"
                ref="allBtnComb_Ref"
              />
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <PublicTable
                ref="publicTableComb_Ref"
                :url="$apiUrls.ItemComb_ItemCls"
                method="paramsPost"
                :params="{
                  clsCode: combClsCode
                }"
                :theads="[
                  {
                    prop: 'combCode',
                    label: '组合代码',
                    align: '',
                    width: '100px',
                    sortable: false
                  },
                  {
                    prop: 'combName',
                    label: '组合名称',
                    align: '',
                    width: '',
                    sortable: false,
                    cellClassName: 'cell_blue'
                  },
                  {
                    prop: 'clsCode',
                    label: '组合分类',
                    align: '',
                    width: '150px',
                    sortable: false
                  },
                  {
                    prop: 'price',
                    label: '单价',
                    align: '',
                    width: '100px',
                    sortable: false
                  }
                ]"
                :elStyle="{
                  height: '40vh'
                }"
                rowKey="combCode"
                :tableDataFilter="filterTableDataCombCallback"
                @selectionChange="selectChangeTableComb"
              >
                <template #clsCode="{ scope }">
                  <span>{{ scope.row.clsCode | getClsName(combClsList) }}</span>
                </template>
              </PublicTable>
            </el-col>
          </el-row>
        </div>

        <!-- 已对应组合内容  -->
        <div class="mapping-wrapper">
          <el-row
            :gutter="10"
            type="flex"
            justify="space-between"
            align="middle"
            style="padding: 15px 10px"
          >
            <el-col :span="1"> </el-col>
            <el-col :span="3" style="text-align: right">
              <span style="font-size: 15px; font-weight: 500; color: #606266"
                >岗位状态:</span
              >
            </el-col>
            <el-col :span="4">
              <el-select
                v-model="positionStatusCode"
                filterable
                @change="searchCombMapping"
                size="mini"
                placeholder="岗位状态"
              >
                <el-option
                  v-for="item in positionStatusList"
                  :key="item.statusCode"
                  :label="item.statusName"
                  :value="item.statusCode"
                />
              </el-select>
            </el-col>

            <el-col :span="1"> </el-col>
            <el-col :span="6">
              <el-button
                size="small"
                class="blue_btn btn"
                @click="addCombMapping"
                icon="el-icon-arrow-down"
                >添加</el-button
              >

              <el-button
                size="small"
                class="blue_btn btn"
                @click="removeCombMapping"
                icon="el-icon-delete-solid"
                >删除</el-button
              >
            </el-col>
            <el-col :span="7">
              <el-input
                clearable
                v-model.trim="combMappingKeyword"
                size="small"
                placeholder="请输入查找内容"
                class="search-input"
                @input="searchCombMapping"
              ></el-input>
            </el-col>

            <el-col :span="2">
              <el-button
                size="small"
                class="blue_btn btn"
                @click="searchCombMapping"
                icon="iconfont icon-search"
                >查询</el-button
              >
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <PublicTable
                ref="publicTableCombMapping_Ref"
                :url="$apiUrls.ReadMapHazardousComb"
                method="paramsPost"
                :params="{
                  hazardousCode: currentRow.hazardousCode,
                  statusCode: positionStatusCode
                }"
                :theads="[
                  {
                    prop: 'combCode',
                    label: '对应组合代码',
                    align: '',
                    width: '200px',
                    sortable: false
                  },
                  {
                    prop: 'combName',
                    label: '对应组合名称',
                    align: '',
                    width: '',
                    sortable: false,
                    cellClassName: 'cell_blue'
                  }
                ]"
                :elStyle="{
                  height: '35vh'
                }"
                rowKey="combCode"
                :tableDataFilter="filterTableDataCombMappingCallback"
                @selectionChange="selectChangeTableCombMapping"
                @request-success="
                  (res) => {
                    this.tableDataCombMapping = res.returnData;
                  }
                "
              >
              </PublicTable>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { dataUtils } from '@/common';
import PublicTable from '@/components/publicTable2.vue';
import AllBtn from '../../allBtn.vue';
import { mapGetters } from 'vuex';
import Exceljs from '@/common/excel/groupDetailedExpenseReportExcel';

export default {
  name: 'baseCodeOccupationalHazardousTable',
  mixins: [Exceljs],
  components: { PublicTable, AllBtn },
  data() {
    return {
      title: '危害因素',
      btnList: ['查询', '新建', '删除', '导出'],
      params: {},
      multipleSelectionList: [],
      excelData: [],
      tableData: [],
      theads: [
        {
          prop: 'hazardousCode',
          label: '危害因素代码',
          align: '',
          width: '120px',
          sortable: false
        },
        {
          prop: 'hazardousName',
          label: '危害因素名称',
          align: '',
          width: '',
          sortable: false,
          cellClassName: 'cell_blue'
        },
        {
          prop: 'hazardousTypeName',
          label: '危害因素种类',
          align: '',
          width: '150px',
          sortable: false
        },
        {
          prop: 'isMainDust',
          label: '是否主要粉尘',
          align: '',
          width: '120px',
          sortable: false
        },
        {
          prop: 'isOtherHazardous',
          label: '是否其他危害因素',
          align: '',
          width: '150px',
          sortable: false
        },
        {
          prop: 'operation',
          label: '操作',
          align: '',
          width: '150px',
          sortable: false
        }
      ],
      showDrawer: false,
      formLabelWidth: '150px',
      popupForm: {
        hazardousCode: '',
        hazardousName: '',
        hazardousType: null,
        isMainDust: false,
        isOtherHazardous: false
      },
      rules: {
        hazardousCode: [
          {
            required: true,
            message: `请输入危害因素代码`,
            trigger: 'blur'
          }
        ],
        hazardousName: [
          {
            required: true,
            message: `请输入危害因素名称`,
            trigger: 'blur'
          }
        ],
        hazardousType: [
          {
            required: true,
            message: `请选择危害因素种类`,
            trigger: 'change'
          }
        ]
      },
      isNew: false,
      loadingSubmit: false,
      hazardousTypeList: [],
      loadingDrawer: false,

      currentRow: {},
      showCombDrawer: false,
      loadingCombDrawer: true,
      loadingCombSubmit: false,
      tableDataComb: [],
      loadingComb: false,
      combClsList: [],
      combClsCode: '',
      combKeyword: '',
      multipleSelectionCombList: [],
      combMappingKeyword: '',
      loadingCombMapping: false,
      tableDataCombMapping: [],
      multipleSelectionCombMappingList: [],
      positionStatusList: [],
      positionStatusCode: ''
    };
  },
  mounted() {
    this.search();
  },
  computed: {
    ...mapGetters(['G_occupationalHazardousType'])
  },
  filters: {
    /**
     * @author: justin
     * @description: 获取分类名称
     * @param {*} value
     * @param {*} arr
     * @return {*}
     */
    getClsName(value, arr) {
      return arr.find((item) => item.clsCode === value)?.clsName;
    }
  },
  methods: {
    /**
     * @author: justin
     * @description: 模糊查询
     * @return {*}
     */
    search() {
      const that = this;
      that.tableData = [];
      that.params.keyword = that.$refs.allBtn_Ref.searchInfo || '';
      that.$refs.publicTable_Ref.loadData();
    },

    /**
     * @author: justin
     * @description: 获取记录响应回调
     * @param {*} data
     * @return {*}
     */
    responseDataCallback(data) {
      this.tableData = data.returnData;
    },

    /**
     * @author: justin
     * @description: 表格多选行数据
     * @param {*} selection 已选中的行集合
     * @param {*} list 已选中行数据，若是全选操作，则会带出未在界面体现的数据
     * @return {*}
     */
    selectChange: function (selection, list) {
      console.log(list);

      this.multipleSelectionList = list;
    },

    /**
     * @author: justin
     * @description: 导出excel
     * @return {*}
     */
    exportToExcel() {
      let checkedList = [];
      if (
        !this.multipleSelectionList ||
        this.multipleSelectionList.length > 0
      ) {
        checkedList = this.multipleSelectionList;
      } else if (this.$refs.publicTable_Ref.currentRow) {
        checkedList.push(this.$refs.publicTable_Ref.currentRow);
      }
      if (checkedList.length <= 0) {
        return this.$message.warning('请至少选择一条记录进行Excel导出！');
      }
      let theads = {
        combCode: '组合代码',
        combName: '组合名称',
        price: '组合单价',
        statusName: '岗位状态'
      };
      let clusInfo =
        '`危害因素代码：${item.hazardousCode}  危害因素名称：${item.hazardousName}   危害因素种类：${item.hazardousTypeName}`';
      this.exportExcel(
        theads,
        checkedList,
        ``,
        clusInfo,
        `危害因素套餐`,
        'mapCombs'
      );
      // this.$refs.publicTable_Ref.exportToExcel({fileName: this.title + "列表"});
    },

    /**
     * @author: justin
     * @description: 导出excel数据处理
     * @param {*} data
     * @return {*}
     */
    excelDataMap(data) {
      return data.map((x, index) => {
        x.isMainDust = x.isMainDust ? '是' : '否';
        x.isOtherHazardous = x.isOtherHazardous ? '是' : '否';
        return x;
      });
    },

    /**
     * @author: justin
     * @description: 格式转换
     * @param {*} filterVal
     * @param {*} jsonData
     * @return {*}
     */
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) => filterVal.map((j) => v[j]));
    },

    /**
     * @author: justin
     * @description: 关闭弹出框回调
     * @return {*}
     */
    closedDrawer() {
      this.$nextTick(function () {
        this.$refs.ruleForm.resetFields();
      });
    },

    /**
     * @author: justin
     * @description: 新增
     * @return {*}
     */
    add() {
      this.isNew = true;
      this.showDrawer = true;
      this.$nextTick(() => {
        this.popupForm.isMainDust = false;
        this.popupForm.isOtherHazardous = false;
      });
    },

    /**
     * @author: justin
     * @description: 双击row弹出编辑框
     * @param {*} row
     * @return {*}
     */
    dbClickRow(row) {
      this.isNew = false;
      this.showDrawer = true;
      this.$nextTick(() => {
        this.popupForm = dataUtils.deepCopy(row);
      });
    },

    /**
     * @author: justin
     * @description: 保存
     * @return {*}
     */
    submit() {
      this.$refs.ruleForm.validate((valid) => {
        valid = valid && this.exitKey();
        if (valid) {
          this.loadingSubmit = true;
          this.$ajax
            .post(
              this.isNew
                ? this.$apiUrls.CreateCodeOccupationalHazardous
                : this.$apiUrls.UpdateCodeOccupationalHazardous,
              this.popupForm
            )
            .then((res) => {
              res = res.data;
              if (!res.success) {
                return;
              }
              this.showDrawer = false;
              this.$nextTick(() => {
                this.$refs.allBtn_Ref.searchInfo = '';
                this.search();
              });
              this.$message({
                message: this.isNew ? '新增成功' : '修改成功',
                type: 'success',
                showClose: true
              });
            })
            .finally((_) => {
              this.loadingSubmit = false;
            });
        }
      });
    },

    /**
     * @author: justin
     * @description: 是否存在该代码
     * @return {*}
     */
    exitKey() {
      if (!this.isNew) return true;

      if (
        this.tableData.some(
          (item) => item.hazardousCode === this.popupForm.hazardousCode
        )
      ) {
        this.$message.warning('该代码已存在，请重新输入！');
        return false;
      }

      return true;
    },

    /**
     * @author: justin
     * @description: 删除
     * @return {*}
     */
    remove() {
      if (this.multipleSelectionList.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据！',
          type: 'warning'
        });
      }
      if (this.multipleSelectionList.length > 0) {
        this.$confirm('是否确认删除已选择的记录？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.delete(this.multipleSelectionList);
        });
      }
    },

    /**
     * @author: justin
     * @description: 确认删除
     * @param {*} multipleSelectionList
     * @return {*}
     */
    delete(multipleSelectionList) {
      this.$ajax
        .post(
          this.$apiUrls.DeleteCodeOccupationalHazardous,
          multipleSelectionList.map((x) => x.hazardousCode)
        )
        .then((res) => {
          let { success } = res.data;
          if (!success) return;
          this.$message({
            showClose: true,
            message: '删除成功',
            type: 'success'
          });
          this.$nextTick(() => {
            this.$refs.allBtn_Ref.searchInfo = '';
            this.search();
          });
        });
    },

    /**
     * @author: justin
     * @description:  打开弹出框回调
     * @return {*}
     */
    openedDrawer() {
      this.hazardousTypeList = this.G_occupationalHazardousType.map((x) => {
        x.value = Number(x.value);
        return x;
      });
    },

    /**
     * @author: justin
     * @description: 显示体检组合对应窗口
     * @param {*} row  行数据
     * @return {*}
     */
    showCombDialog(row) {
      this.showCombDrawer = true;
      this.currentRow = row;
      this.$nextTick(() => {
        this.getCombClsList();
        this.getPositionStatusList();
        this.searchComb();
        this.searchCombMapping();
      });
    },

    /**
     * @author: justin
     * @description:  关闭体检组合对应窗口回调
     * @return {*}
     */
    closedCombDrawer() {
      this.loadingCombDrawer = true;
    },

    /**
     * @author: justin
     * @description:  打开体检组合对应窗口回调
     * @return {*}
     */
    openedCombDrawer() {},

    /**
     * @author: justin
     * @description: 获取体检组合分类列表
     * @return {*}
     */
    getCombClsList() {
      const that = this;
      that.loadingCombDrawer = true;
      that.$ajax
        .post(that.$apiUrls.RD_CodeItemCls + '/Read', [])
        .then((res) => {
          if (!res.data.success) return;

          that.combClsList = res.data.returnData;
        })
        .finally((_) => {
          that.loadingCombDrawer = false;
        });
    },

    /**
     * @author: justin
     * @description: 获取岗位状态列表
     * @return {*}
     */
    getPositionStatusList() {
      const that = this;
      that.loadingCombDrawer = true;
      that.$ajax
        .post(that.$apiUrls.ReadCodeOccupationalPositionStatus, [])
        .then((res) => {
          if (!res.data.success) return;

          that.positionStatusList = res.data.returnData;
          if (that.positionStatusList && that.positionStatusList.length > 0) {
            that.positionStatusCode = that.positionStatusList[0].statusCode;
            that.searchCombMapping();
          } else {
            that.$message.warning('请补充岗位状态基础数据后再操作！');
          }
        })
        .finally((_) => {
          that.loadingCombDrawer = false;
        });
    },

    /**
     * @author: justin
     * @description: 检索体检组合对应数据
     * @return {*}
     */
    searchComb() {
      const that = this;
      that.combKeyword = that.$refs.allBtnComb_Ref.searchInfo || '';
      if (!that.combKeyword) {
        that.$refs.publicTableComb_Ref.loadData();
      }
    },

    /**
     * @author: justin
     * @description: 体检组合对应数据过滤回调
     * @param {*} data
     * @return {*}
     */
    filterTableDataCombCallback(data) {
      if (!data || data.length == 0) return data;

      const that = this;
      data = data.filter(
        (x) =>
          (that.combClsCode && x.clsCode === that.combClsCode) ||
          !that.combClsCode
      );

      const _keyword = (that.combKeyword || '').trim().toLowerCase();
      if (!_keyword) return data;

      return data.filter(
        (item) =>
          item.combCode.toLowerCase().includes(_keyword) ||
          item.combName.toLowerCase().includes(_keyword)
      );
    },

    /**
     * @author: justin
     * @description: 复选框勾选操作
     * @param {*} val
     * @return {*}
     */
    selectChangeTableComb(val, list) {
      console.log(list);

      this.multipleSelectionCombList = list;
    },

    /**
     * @author: justin
     * @description: 新增体检组合对应数据
     * @return {*}
     */
    addCombMapping() {
      let leftCheckCode = [];
      let allMutexCombs = [];
      let mutexCombsText = [];

      let rightData = this.$refs.publicTableCombMapping_Ref.tableData;
      let rightCombsCode = rightData.map((item) => item.combCode);
      this.multipleSelectionCombList.map((item) => {
        leftCheckCode.push(item.combCode);
        allMutexCombs.push(...item.mutexCombs);
        item.mutexCombs?.forEach((twoItem) => {
          let rightIdx = rightCombsCode.indexOf(twoItem);
          if (rightIdx !== -1) {
            !mutexCombsText.includes(item.combName) &&
              mutexCombsText.push(item.combName);
            !mutexCombsText.includes(rightData[rightIdx].combName) &&
              mutexCombsText.push(rightData[rightIdx].combName);
          }
        });
      });

      allMutexCombs = [...new Set(allMutexCombs)];
      leftCheckCode.forEach((item, index) => {
        if (
          allMutexCombs.includes(item) &&
          !mutexCombsText.includes(
            this.multipleSelectionCombList[index].combName
          )
        ) {
          mutexCombsText.push(this.multipleSelectionCombList[index].combName);
        }
      });
      leftCheckCode = null;
      allMutexCombs = null;
      rightCombsCode = null;
      if (mutexCombsText.length !== 0) {
        this.$message({
          message: `${mutexCombsText.join('、')}存在互斥！`,
          type: 'warning',
          duration: this.$config.messageTime,
          showClose: true
        });
        mutexCombsText = null;
        return;
      }

      if (this.multipleSelectionCombList.length <= 0) {
        this.$message.warning('请勾选需要添加对应的体检组合');
        return;
      }

      const existCombCodeList = this.tableDataCombMapping.map(
        (x) => x.combCode
      );
      const filterCombCodeList = this.multipleSelectionCombList
        .filter((x) => existCombCodeList.includes(x.combCode))
        .map((x) => x.combCode);
      if (filterCombCodeList.length > 0) {
        this.$message.warning(
          `以下体检组合不能重复添加：${filterCombCodeList.join('、')}`
        );
        return;
      }

      this.$confirm('确认添加已勾选的体检组合？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$ajax
          .post(
            this.$apiUrls.CreateMapHazardousComb,
            this.multipleSelectionCombList.map((x) => {
              return {
                hazardousCode: this.currentRow.hazardousCode,
                statusCode: this.positionStatusCode,
                combCode: x.combCode
              };
            })
          )
          .then((res) => {
            let { success } = res.data;
            if (!success) return;

            this.$message.success('添加成功！');
            this.$nextTick(() => {
              this.combMappingKeyword = '';
              this.$refs.publicTableComb_Ref.clearSelection();
              this.searchCombMapping();
            });
          });
      });
    },

    /**
     * @author: justin
     * @description: 删除体检组合对应数据
     * @return {*}
     */
    removeCombMapping() {
      if (this.multipleSelectionCombMappingList.length <= 0) {
        this.$message.warning('请勾选需要删除已添加对应的体检组合');
        return;
      }
      this.$confirm('确认删除已添加对应的体检组合？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$ajax
          .post(
            this.$apiUrls.DeleteMapHazardousComb,
            this.multipleSelectionCombMappingList.map((x) => {
              return {
                hazardousCode: this.currentRow.hazardousCode,
                statusCode: this.positionStatusCode,
                combCode: x.combCode
              };
            })
          )
          .then((res) => {
            let { success } = res.data;
            if (!success) return;

            this.$message.success('删除成功！');
            this.$nextTick(() => {
              this.combMappingKeyword = '';
              this.$refs.publicTableCombMapping_Ref.clearSelection();
              this.searchCombMapping();
            });
          });
      });
    },

    /**
     * @author: justin
     * @description:
     * @return {*}
     */
    searchCombMapping() {
      const that = this;
      if (!that.combMappingKeyword && that.positionStatusCode) {
        that.$refs.publicTableCombMapping_Ref.loadData();
      }
    },

    /**
     * @author: justin
     * @description: 体检组合已对应数据过滤回调
     * @param {*} data
     * @return {*}
     */
    filterTableDataCombMappingCallback(data) {
      if (!data || data.length == 0) return data;

      const that = this;
      const _keyword = (that.combMappingKeyword || '').trim().toLowerCase();
      if (!_keyword) return data;

      return data.filter(
        (item) =>
          item.combCode.toLowerCase().includes(_keyword) ||
          item.combName.toLowerCase().includes(_keyword)
      );
    },

    /**
     * @author: justin
     * @description: 复选框勾选操作
     * @param {*} val
     * @return {*}
     */
    selectChangeTableCombMapping(val, list) {
      this.multipleSelectionCombMappingList = list;
    }
  },
  activated() {
    this.$nextTick(() => {
      this.$refs.publicTable_Ref.doLayout();
    });
  }
};
</script>

<style lang="less" scoped>
.code-hazardous-table-job-diseases {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-weight: 600;
  header {
    display: flex;
    justify-content: flex-end;
    .el-form-item {
      margin-bottom: 18px;
    }
    .el-button--small {
      width: 64px;
    }
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-container.is-vertical {
    height: 100%;
  }
  .el-header,
  .el-main {
    padding: 0;
  }

  .searchWrap .el-form-item:last-child {
    margin-right: 0;
    margin-left: 10px;
  }
  .indexPage .contentWrap .tabPage {
    margin-top: 0;
  }

  .el-showDrawer.rtl {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #2d3436;
    padding: 0 20px 0 10px;
  }
  /deep/.el-drawer__body {
    padding-right: 20px;

    .el-form {
      padding-top: 20px;

      .el-form-item {
        margin-bottom: 18px;
      }
    }
  }

  /deep/ .el-select {
    width: 100%;
  }

  /deep/ .el-drawer__header {
    height: 42px;
    background: rgba(23, 112, 223, 0.2);
    margin-bottom: unset;
    color: #000;

    span,
    button {
      margin-top: -20px;
    }
  }
}
</style>
