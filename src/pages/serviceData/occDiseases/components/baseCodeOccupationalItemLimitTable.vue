<!--
 * @FilePath: \KrPeis\src\pages\serviceData\occDiseases\components\baseCodeOccupationalItemLimitTable.vue
 * @Description:  检查项目表格组件
 * @Author: justin
 * @Date: 2024-05-07 15:11:39
 * @Version: 0.0.1
 * @LastEditors: justin
 * @LastEditTime: 2024-05-24 13:30:22
*
-->

<template>
  <div class="code-occupational-item-limit-table-job-diseases">
    <!-- 列表信息 -->
    <el-container>
      <el-header>
        <AllBtn
          :btnList="btnList"
          :methodSearch="search"
          :methodCreat="add"
          :methodDelete="remove"
          :methodExport="exportToExcel"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          ref="publicTable_Ref"
          :theads="theads"
          :url="$apiUrls.ReadCodeOccupationalItemLimit"
          :params="params"
          rowKey="itemCodeSex"
          :tableDataFilter="filterTableDataCallback"
          :excelDataMap="excelDataMap"
          @selectionChange="selectChange"
          @rowDblclick="dbClickRow"
          @request-success="responseDataCallback"
        >
          <template #sex="{ scope }">
            <span>{{ G_EnumList.Sex[scope.row.sex] }}</span>
          </template>
        </PublicTable>
      </el-main>
    </el-container>

    <!-- 弹出框基本信息 -->
    <el-drawer
      :title="title + '属性'"
      :visible.sync="showDrawer"
      :wrapperClosable="false"
      @closed="closedDrawer"
      @opened="openedDrawer"
    >
      <div v-loading="loadingDrawer">
        <el-form :model="popupForm" ref="ruleForm" :rules="rules">
          <el-form-item
            label="组合代码"
            :label-width="formLabelWidth"
            prop="itemCode"
          >
            <el-input
              v-model.trim="popupForm.itemCode"
              size="small"
              placeholder="请输入组合代码"
              :disabled="!isNew"
              :maxlength="5"
            ></el-input>
          </el-form-item>

          <el-form-item
            label="组合名称"
            :label-width="formLabelWidth"
            prop="itemName"
          >
            <el-input
              v-model.trim="popupForm.itemName"
              autocomplete="off"
              size="small"
              placeholder="请输入组合名称"
              :maxlength="50"
            ></el-input>
          </el-form-item>

          <el-form-item label="性别" :label-width="formLabelWidth" prop="sex">
            <el-select
              v-model="popupForm.sex"
              placeholder="请选择性别"
              filterable
              clearable
              :disabled="!isNew"
            >
              <el-option
                v-for="item in sexTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item
            label="参考下限"
            :label-width="formLabelWidth"
            prop="lowerLimit"
          >
            <el-input-number
              v-model.trim="popupForm.lowerLimit"
              size="small"
              placeholder="请输入参考下限"
              :precision="0"
              :step="1"
              :max="1000000"
              :controls="false"
            ></el-input-number>
          </el-form-item>

          <el-form-item
            label="参考上限"
            :label-width="formLabelWidth"
            prop="upperLimit"
          >
            <el-input-number
              v-model.trim="popupForm.upperLimit"
              size="small"
              placeholder="请输入参考上限"
              :precision="0"
              :step="1"
              :max="1000000"
              :controls="false"
            ></el-input-number>
          </el-form-item>

          <el-form-item
            label="结果最小值"
            :label-width="formLabelWidth"
            prop="minValue"
          >
            <el-input-number
              v-model.trim="popupForm.minValue"
              size="small"
              placeholder="请输入结果最小值"
              :precision="0"
              :step="1"
              :max="1000000"
              :controls="false"
            ></el-input-number>
          </el-form-item>

          <el-form-item
            label="结果最大值"
            :label-width="formLabelWidth"
            prop="maxValue"
          >
            <el-input-number
              v-model.trim="popupForm.maxValue"
              size="small"
              placeholder="请输入结果最大值"
              :precision="0"
              :step="1"
              :max="1000000"
              :controls="false"
            ></el-input-number>
          </el-form-item>
        </el-form>

        <div class="dialog-footer">
          <el-button
            @click="showDrawer = false"
            size="small"
            :loading="loadingSubmit"
            >取消</el-button
          >
          <el-button
            type="primary"
            @click="submit"
            size="small"
            :loading="loadingSubmit"
            >保存</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { dataUtils } from '@/common';
import PublicTable from '@/components/publicTable2.vue';
import AllBtn from '../../allBtn.vue';
import { mapGetters } from 'vuex';

export default {
  name: 'baseCodeItemLimitTable',
  components: { PublicTable, AllBtn },
  data() {
    return {
      title: '检查项目限定',
      btnList: ['查询', '新建', '删除', '导出'],
      keyword: '',
      params: {},
      multipleSelectionList: [],
      excelData: [],
      tableData: [],
      theads: [
        {
          prop: 'itemCode',
          label: '组合代码',
          align: '',
          width: '120px',
          sortable: false
        },
        {
          prop: 'itemName',
          label: '组合名称',
          align: '',
          width: '',
          sortable: false,
          cellClassName: 'cell_blue'
        },
        {
          prop: 'sex',
          label: '性别',
          align: '',
          width: '80px',
          sortable: false
        },
        {
          prop: 'lowerLimit',
          label: '参考下限',
          align: '',
          width: '120px',
          sortable: false
        },
        {
          prop: 'upperLimit',
          label: '参考上限',
          align: '',
          width: '120px',
          sortable: false
        },
        {
          prop: 'minValue',
          label: '结果最小值',
          align: '',
          width: '120px',
          sortable: false
        },
        {
          prop: 'maxValue',
          label: '结果最大值',
          align: '',
          width: '120px',
          sortable: false
        }
      ],
      showDrawer: false,
      formLabelWidth: '150px',
      popupForm: {
        itemCode: '',
        itemName: '',
        sex: '',
        lowerLimit: undefined,
        upperLimit: undefined,
        minValue: undefined,
        maxValue: undefined
      },
      rules: {
        itemCode: [
          {
            required: true,
            message: `请输入组合代码`,
            trigger: 'blur'
          }
        ],
        itemName: [
          {
            required: true,
            message: `请输入组合名称`,
            trigger: 'blur'
          }
        ],
        sex: [
          {
            required: true,
            message: `请选择性别`,
            trigger: 'change'
          }
        ]
      },
      isNew: false,
      loadingSubmit: false,
      sexTypeList: [],
      loadingDrawer: false
    };
  },
  mounted() {
    this.search();
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_sexList'])
  },
  methods: {
    /**
     * @author: justin
     * @description: 模糊查询
     * @return {*}
     */
    search() {
      const that = this;
      that.keyword = (that.$refs.allBtn_Ref.searchInfo || '')
        .trim()
        .toLowerCase();
      if (!that.keyword) {
        that.params = [];
        that.$refs.publicTable_Ref.loadData();
      }
    },

    /**
     * @author: justin
     * @description: 获取记录响应回调
     * @param {*} data
     * @return {*}
     */
    responseDataCallback(data) {
      this.tableData = data.returnData;
    },

    /**
     * @author: justin
     * @description: 数据过滤回调
     * @param {*} data
     * @return {*}
     */
    filterTableDataCallback(data) {
      if (!data || data.length == 0) return data;

      const that = this;
      return data
        .filter(
          (item) =>
            item.itemCode.toLowerCase().includes(that.keyword) ||
            item.itemName.toLowerCase().includes(that.keyword)
        )
        .map((x) => {
          // 复合主键
          x.itemCodeSex = `${x.itemCode}_${x.sex}`;
          return x;
        });
    },

    /**
     * @author: justin
     * @description: 表格多选行数据
     * @param {*} selection 已选中的行集合
     * @param {*} list 已选中行数据，若是全选操作，则会带出未在界面体现的数据
     * @return {*}
     */
    selectChange: function (selection, list) {
      this.multipleSelectionList = list;
    },

    /**
     * @author: justin
     * @description: 导出excel
     * @return {*}
     */
    exportToExcel() {
      this.$refs.publicTable_Ref.exportToExcel({
        fileName: this.title + '列表'
      });
    },

    /**
     * @author: justin
     * @description: 导出excel数据处理
     * @param {*} data
     * @return {*}
     */
    excelDataMap(data) {
      return data.map((x, index) => {
        x.sex = this.G_EnumList.Sex[x.sex];
        return x;
      });
    },

    /**
     * @author: justin
     * @description: 关闭弹出框回调
     * @return {*}
     */
    closedDrawer() {
      this.$nextTick(function () {
        this.$refs.ruleForm.resetFields();
      });
    },

    /**
     * @author: justin
     * @description: 新增
     * @return {*}
     */
    add() {
      this.isNew = true;
      this.showDrawer = true;
    },

    /**
     * @author: justin
     * @description: 双击row弹出编辑框
     * @param {*} row
     * @return {*}
     */
    dbClickRow(row) {
      this.isNew = false;
      this.showDrawer = true;
      this.$nextTick(() => {
        this.popupForm = dataUtils.deepCopy(row);
        this.popupForm.lowerLimit = this.popupForm.lowerLimit || undefined;
        this.popupForm.upperLimit = this.popupForm.upperLimit || undefined;
        this.popupForm.minValue = this.popupForm.minValue || undefined;
        this.popupForm.maxValue = this.popupForm.maxValue || undefined;
      });
    },

    /**
     * @author: justin
     * @description: 保存
     * @return {*}
     */
    submit() {
      this.$refs.ruleForm.validate((valid) => {
        valid = valid && this.exitKey();
        if (valid) {
          this.loadingSubmit = true;
          this.$ajax
            .post(
              this.isNew
                ? this.$apiUrls.CreateCodeOccupationalItemLimit
                : this.$apiUrls.UpdateCodeOccupationalItemLimit,
              this.popupForm
            )
            .then((res) => {
              res = res.data;
              if (!res.success) {
                return;
              }
              this.showDrawer = false;
              this.$nextTick(() => {
                this.$refs.allBtn_Ref.searchInfo = '';
                this.search();
              });
              this.$message({
                message: this.isNew ? '新增成功' : '修改成功',
                type: 'success',
                showClose: true
              });
            })
            .finally((_) => {
              this.loadingSubmit = false;
            });
        }
      });
    },

    /**
     * @author: justin
     * @description: 是否存在该代码
     * @return {*}
     */
    exitKey() {
      if (!this.isNew) return true;

      if (
        this.tableData.some((item) => item.itemCode === this.popupForm.itemCode)
      ) {
        this.$message.warning('该代码已存在，请重新输入！');
        return false;
      }

      return true;
    },

    /**
     * @author: justin
     * @description: 删除
     * @return {*}
     */
    remove() {
      if (this.multipleSelectionList.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据！',
          type: 'warning'
        });
      }
      if (this.multipleSelectionList.length > 0) {
        this.$confirm('是否确认删除已选择的记录？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.delete(this.multipleSelectionList);
        });
      }
    },

    /**
     * @author: justin
     * @description: 确认删除
     * @param {*} multipleSelectionList
     * @return {*}
     */
    delete(multipleSelectionList) {
      this.$ajax
        .post(
          this.$apiUrls.DeleteCodeOccupationalItemLimit,
          multipleSelectionList.map((x) => x.itemCode)
        )
        .then((res) => {
          let { success } = res.data;
          if (!success) return;
          this.$message({
            showClose: true,
            message: '删除成功',
            type: 'success'
          });
          this.$nextTick(() => {
            this.$refs.allBtn_Ref.searchInfo = '';
            this.search();
          });
        });
    },

    /**
     * @author: justin
     * @description:  打开弹出框回调
     * @return {*}
     */
    openedDrawer() {
      this.sexTypeList = this.G_sexList.map((x) => {
        x.value = Number(x.value);
        return x;
      });
    }
  },
  activated() {
    this.$nextTick(() => {
      this.$refs.publicTable_Ref.doLayout();
    });
  }
};
</script>

<style lang="less" scoped>
.code-occupational-item-limit-table-job-diseases {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-weight: 600;
  header {
    display: flex;
    justify-content: flex-end;
    .el-form-item {
      margin-bottom: 18px;
    }
    .el-button--small {
      width: 64px;
    }
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-container.is-vertical {
    height: 100%;
  }
  .el-header,
  .el-main {
    padding: 0;
  }

  .searchWrap .el-form-item:last-child {
    margin-right: 0;
    margin-left: 10px;
  }
  .indexPage .contentWrap .tabPage {
    margin-top: 0;
  }

  .el-showDrawer.rtl {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #2d3436;
    padding: 0 20px 0 10px;
  }
  /deep/.el-drawer__body {
    padding-right: 20px;

    .el-form {
      padding-top: 20px;

      .el-form-item {
        margin-bottom: 18px;
      }
    }
  }

  /deep/ .el-select {
    width: 100%;
  }

  /deep/ .el-drawer__header {
    height: 42px;
    background: rgba(23, 112, 223, 0.2);
    margin-bottom: unset;
    color: #000;

    span,
    button {
      margin-top: -20px;
    }
  }
}
</style>
