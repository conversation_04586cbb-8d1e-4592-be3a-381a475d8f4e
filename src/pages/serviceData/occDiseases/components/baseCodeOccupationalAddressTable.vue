<!--
 * @FilePath: \KrPeis\src\pages\serviceData\occDiseases\components\baseCodeOccupationalAddressTable.vue
 * @Description:  地区列表
 * @Author: justin
 * @Date: 2024-05-07 13:26:13
 * @Version: 0.0.1
 * @LastEditors: justin
 * @LastEditTime: 2024-05-24 11:12:10
*
-->
<template>
  <div class="code-area-table-job-diseases">
    <!-- 列表信息 -->
    <el-container>
      <el-header>
        <AllBtn
          :btnList="btnList"
          :methodSearch="search"
          :methodCreat="add"
          :methodDelete="remove"
          :methodExport="exportToExcel"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          ref="publicTable_Ref"
          :theads="theads"
          :url="$apiUrls.ReadCodeOccupationalAddressByPage"
          :params="params"
          rowKey="addressCode"
          remoteByPage
          @selectionChange="selectChange"
          @rowDblclick="dbClickRow"
          @request-success="responseDataCallback"
        >
        </PublicTable>
      </el-main>
    </el-container>

    <!-- 弹出框基本信息 -->
    <el-drawer
      :title="title + '属性'"
      :visible.sync="showDrawer"
      :wrapperClosable="false"
      @closed="closedDrawer"
      @opened="openedDrawer"
    >
      <div v-loading="loadingDrawer">
        <el-form :model="popupForm" ref="ruleForm" :rules="rules">
          <el-form-item
            label="地区编码"
            :label-width="formLabelWidth"
            prop="addressCode"
          >
            <el-input
              v-model.trim="popupForm.addressCode"
              size="small"
              placeholder="请输入地区编码"
              :disabled="!isNew"
              :maxlength="10"
            ></el-input>
          </el-form-item>

          <el-form-item
            label="地区简称"
            :label-width="formLabelWidth"
            prop="addressShortName"
          >
            <el-input
              v-model.trim="popupForm.addressShortName"
              autocomplete="off"
              size="small"
              placeholder="请输入地区简称"
              :maxlength="50"
            ></el-input>
          </el-form-item>

          <el-form-item
            label="地区全称"
            :label-width="formLabelWidth"
            prop="addressFullName"
          >
            <el-input
              v-model.trim="popupForm.addressFullName"
              autocomplete="off"
              size="small"
              placeholder="请输入地区全称"
              :maxlength="50"
            ></el-input>
          </el-form-item>

          <el-form-item
            label="父级编码"
            :label-width="formLabelWidth"
            prop="parentCode"
          >
            <el-input
              v-model.trim="popupForm.parentCode"
              autocomplete="off"
              size="small"
              placeholder="请输入父级编码"
              :maxlength="10"
            ></el-input>
          </el-form-item>
        </el-form>

        <div class="dialog-footer">
          <el-button
            @click="showDrawer = false"
            size="small"
            :loading="loadingSubmit"
            >取消</el-button
          >
          <el-button
            type="primary"
            @click="submit"
            size="small"
            :loading="loadingSubmit"
            >保存</el-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { dataUtils } from '@/common';
import PublicTable from '@/components/publicTable2.vue';
import AllBtn from '../../allBtn.vue';
export default {
  name: 'baseCodeOccupationalAddressTable',
  components: { PublicTable, AllBtn },
  data() {
    return {
      title: '地区',
      btnList: ['查询', '新建', '删除', '导出'],
      params: {},
      multipleSelectionList: [],
      excelData: [],
      tableData: [],
      theads: [
        {
          prop: 'addressCode',
          label: '地区编码',
          align: '',
          width: '150px',
          sortable: false
        },
        {
          prop: 'addressShortName',
          label: '地区简称',
          align: '',
          width: '200px',
          sortable: false,
          cellClassName: 'cell_blue'
        },
        {
          prop: 'addressFullName',
          label: '地区全称',
          align: '',
          width: '',
          sortable: false
        },
        {
          prop: 'parentCode',
          label: '父级编码',
          align: '',
          width: '150px',
          sortable: false
        }
      ],
      showDrawer: false,
      formLabelWidth: '150px',
      popupForm: {
        addressCode: '',
        addressShortName: '',
        addressFullName: '',
        parentCode: null
      },
      rules: {
        addressCode: [
          {
            required: true,
            message: `请输入地区编码`,
            trigger: 'blur'
          }
        ],
        addressShortName: [
          {
            required: true,
            message: `请输入地区简称`,
            trigger: 'blur'
          }
        ],
        addressFullName: [
          {
            required: true,
            message: `请输入地区全称`,
            trigger: 'blur'
          }
        ]
      },
      isNew: false,
      loadingSubmit: false,
      hazardousTypeList: [],
      loadingDrawer: false
    };
  },
  mounted() {
    this.search();
  },
  methods: {
    /**
     * @author: justin
     * @description: 模糊查询
     * @return {*}
     */
    search() {
      const that = this;
      that.params.keyword = that.$refs.allBtn_Ref.searchInfo || '';
      that.$refs.publicTable_Ref.loadData();
    },

    /**
     * @author: justin
     * @description: 获取记录响应回调
     * @param {*} data
     * @return {*}
     */
    responseDataCallback(data) {
      this.tableData = data.returnData;
    },

    /**
     * @author: justin
     * @description: 表格多选行数据
     * @param {*} selection 已选中的行集合
     * @param {*} list 已选中行数据，若是全选操作，则会带出未在界面体现的数据
     * @return {*}
     */
    selectChange: function (selection, list) {
      this.multipleSelectionList = list;
    },

    /**
     * @author: justin
     * @description: 导出excel
     * @return {*}
     */
    exportToExcel() {
      this.$refs.publicTable_Ref.exportToExcel({
        fileName: this.title + '列表'
      });
    },

    /**
     * @author: justin
     * @description: 关闭弹出框回调
     * @return {*}
     */
    closedDrawer() {
      this.$nextTick(function () {
        this.$refs.ruleForm.resetFields();
      });
    },

    /**
     * @author: justin
     * @description: 新增
     * @return {*}
     */
    add() {
      this.isNew = true;
      this.showDrawer = true;
    },

    /**
     * @author: justin
     * @description: 双击row弹出编辑框
     * @param {*} row
     * @return {*}
     */
    dbClickRow(row) {
      this.isNew = false;
      this.showDrawer = true;
      this.$nextTick(() => {
        this.popupForm = dataUtils.deepCopy(row);
      });
    },

    /**
     * @author: justin
     * @description: 保存
     * @return {*}
     */
    submit() {
      this.$refs.ruleForm.validate((valid) => {
        valid = valid && this.exitKey();
        if (valid) {
          this.loadingSubmit = true;
          this.popupForm.parentCode = this.popupForm.parentCode || null;
          this.$ajax
            .post(
              this.isNew
                ? this.$apiUrls.CreateCodeOccupationalAddress
                : this.$apiUrls.UpdateCodeOccupationalAddress,
              this.popupForm
            )
            .then((res) => {
              res = res.data;
              if (!res.success) {
                return;
              }
              this.showDrawer = false;
              this.$nextTick(() => {
                this.$refs.allBtn_Ref.searchInfo = '';
                this.search();
              });
              this.$message({
                message: this.isNew ? '新增成功' : '修改成功',
                type: 'success',
                showClose: true
              });
            })
            .finally((_) => {
              this.loadingSubmit = false;
            });
        }
      });
    },

    /**
     * @author: justin
     * @description: 是否存在该代码
     * @return {*}
     */
    exitKey() {
      if (!this.isNew) return true;

      if (
        this.tableData.some(
          (item) => item.addressCode === this.popupForm.addressCode
        )
      ) {
        this.$message.warning('该代码已存在，请重新输入！');
        return false;
      }

      return true;
    },

    /**
     * @author: justin
     * @description: 删除
     * @return {*}
     */
    remove() {
      if (this.multipleSelectionList.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据！',
          type: 'warning'
        });
      }
      if (this.multipleSelectionList.length > 0) {
        this.$confirm('是否确认删除已选择的记录？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.delete(this.multipleSelectionList);
        });
      }
    },

    /**
     * @author: justin
     * @description: 确认删除
     * @param {*} multipleSelectionList
     * @return {*}
     */
    delete(multipleSelectionList) {
      this.$ajax
        .post(
          this.$apiUrls.DeleteCodeOccupationalAddress,
          multipleSelectionList.map((x) => x.addressCode)
        )
        .then((res) => {
          let { success } = res.data;
          if (!success) return;
          this.$message({
            showClose: true,
            message: '删除成功',
            type: 'success'
          });
          this.$nextTick(() => {
            this.$refs.allBtn_Ref.searchInfo = '';
            this.search();
          });
        });
    },

    /**
     * @author: justin
     * @description:  打开弹出框回调
     * @return {*}
     */
    openedDrawer() {}
  },
  activated() {
    this.$nextTick(() => {
      this.$refs.publicTable_Ref.doLayout();
    });
  }
};
</script>

<style lang="less" scoped>
.code-area-table-job-diseases {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-weight: 600;
  header {
    display: flex;
    justify-content: flex-end;
    .el-form-item {
      margin-bottom: 18px;
    }
    .el-button--small {
      width: 64px;
    }
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-container.is-vertical {
    height: 100%;
  }
  .el-header,
  .el-main {
    padding: 0;
  }

  .searchWrap .el-form-item:last-child {
    margin-right: 0;
    margin-left: 10px;
  }
  .indexPage .contentWrap .tabPage {
    margin-top: 0;
  }

  .el-showDrawer.rtl {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #2d3436;
    padding: 0 20px 0 10px;
  }
  /deep/.el-drawer__body {
    padding-right: 20px;

    .el-form {
      padding-top: 20px;

      .el-form-item {
        margin-bottom: 18px;
      }
    }
  }

  /deep/ .el-select {
    width: 100%;
  }

  /deep/ .el-drawer__header {
    height: 42px;
    background: rgba(23, 112, 223, 0.2);
    margin-bottom: unset;
    color: #000;

    span,
    button {
      margin-top: -20px;
    }
  }
}
</style>
