<!--
 * @FilePath: \shenshan\KrPeis\src\pages\serviceData\occDiseases\baseCodeOccDisease.vue
 * @Description:  职业病基础代码维护
 * @Author: justin
 * @Date: 2024-04-25 15:33:04
 * @Version: 0.0.1
 * @LastEditors: key
 * @LastEditTime: 2024-08-27 09:28:02
*
-->
<template>
  <div class="base-code-occ-disease-container">
    <!-- 左侧菜单栏 -->
    <div class="left-wrapper">
      <p>职业病基础代码维护</p>
      <el-menu
        ref="elMenu_Ref"
        :default-active="activeMenuIndex"
        :default-openeds="openedMenuIndexList"
        class="el-menu-vertical-demo"
        style="height: calc(100% - 50px); overflow: auto"
      >
        <el-submenu :index="item.id" v-for="item in menuList" :key="item.id">
          <template slot="title">
            <i :class="item.icon" class="icon"></i>
            <span>{{ item.title }}</span>
          </template>

          <template v-for="child in item.children">
            <el-menu-item
              v-if="child.children && child.children.length == 0"
              :key="child.id"
              :index="child.id"
              @click="showRightComponent(child)"
            >
              <template slot="title">
                <i :class="child.icon" class="icons"></i>
                <span>{{ child.title }}</span>
              </template>
            </el-menu-item>
          </template>
        </el-submenu>
      </el-menu>
    </div>

    <!-- 右侧内容 -->
    <div class="right-wrapper">
      <transition name="fade-transform" mode="out-in">
        <keep-alive>
          <template v-if="currentMenu.path === 'baseCodeOccupationalDicTable'">
            <!-- key-value通用字典组件 -->
            <component
              :is="currentMenu.path"
              :title="currentMenu.title"
              :api="currentMenu.data.api"
              :tKey="currentMenu.data.tKey"
              :tValue="currentMenu.data.tValue"
              :key="currentMenu.id"
              :id="currentMenu.id"
            />
          </template>

          <template v-else>
            <!-- 其他页面 -->
            <component
              :is="currentMenu.path"
              :key="currentMenu.id"
              :id="currentMenu.id"
              :allInfo="
                currentMenu.viewConfig && currentMenu.viewConfig.viewInfo
                  ? currentMenu.viewConfig.viewInfo
                  : undefined
              "
            />
          </template>
        </keep-alive>
      </transition>
    </div>
  </div>
</template>

<script>
import { menuData } from './configs/baseCodeMenu'; // 菜单数据
import baseCodeOccupationalDicTable from './components/baseCodeOccupationalDicTable'; // key-value通用字典组件
import baseCodeOccupationalAddressTable from './components/baseCodeOccupationalAddressTable'; // 地区维护组件
import baseCodeOccupationalHazardousTable from './components/baseCodeOccupationalHazardousTable'; // 危害因素维护组件
import baseCodeOccupationalItemLimitTable from './components/baseCodeOccupationalItemLimitTable'; // 检查项目限定维护组件
import baseCodeOccupationalItemTable from './components/baseCodeOccupationalItemTable'; // 职业病项目维护组件
import baseCodeOccupationalReportModelTable from './components/baseCodeOccupationalReportModelTable'; // 报告模块维护组件
import baseCodeOccupationalSymptomTable from './components/baseCodeOccupationalSymptomTable'; // 症状询问维护组件
import baseCodeAuditoryCorrectedValueTable from './components/baseCodeOccupationalAuditoryCorrectedValueTable'; // 听诊修正值维护组件
import baseCodeOccupationalImportantMonitorItemTable from './components/baseCodeOccupationalImportantMonitorItemTable'; // 听诊修正值维护组件
import baseCodeOccupationalMeal from './components/baseCodeOccupationalMeal'; // 职业病套餐维护组件

export default {
  name: 'baseCodeOccDisease',
  components: {
    baseCodeOccupationalDicTable,
    baseCodeOccupationalAddressTable,
    baseCodeOccupationalHazardousTable,
    baseCodeOccupationalItemLimitTable,
    baseCodeOccupationalItemTable,
    baseCodeOccupationalReportModelTable,
    baseCodeOccupationalSymptomTable,
    baseCodeAuditoryCorrectedValueTable,
    baseCodeOccupationalImportantMonitorItemTable,
    baseCodeOccupationalMeal
  },
  data() {
    return {
      menuList: menuData,
      activeMenuIndex: '', // 当前选中的菜单
      openedMenuIndexList: [], // 默认展开菜单数组(sub-menu 的 index 的数组)
      currentMenu: {}
    };
  },
  created() {
    this.currentMenu = this.menuList[0].children[0];
    this.activeMenuIndex = this.currentMenu.id;
    this.openedMenuIndexList = this.menuList.map((x) => x.id);
  },
  methods: {
    /**
     * @author: justin
     * @description: 显示右侧组件
     * @param {*} item
     * @return {*}
     */
    showRightComponent(item) {
      this.currentMenu = item;
    }
  }
};
</script>

<style lang="less" scoped>
.base-code-occ-disease-container {
  display: flex;
  .left-wrapper {
    width: 320px;
    background: white;
    p {
      font-size: 16px;
      color: #2d3436;
      line-height: 48px;
      margin-left: 10px;
    }
    img {
      width: 16px;
    }
    .icon {
      margin-right: 8px;
      font-size: 18px;
    }
    .icons {
      font-size: 16px;
      margin-right: 8px;
    }
    /deep/.el-submenu__title {
      font-size: 16px;
      height: 30px;
      line-height: 30px;
    }
    .el-submenu .el-menu-item {
      height: 30px;
      line-height: 30px;
    }
    .el-submenu .el-menu-item:focus,
    .el-menu-item.is-active {
      outline: 0;
      color: #fff;
      background-color: #1770df;
      border-radius: 4px;
    }
  }

  .right-wrapper {
    display: flex;
    flex-direction: column;
    flex: 1;
    flex-shrink: 0;
    background: white;
    margin-left: 20px;
    overflow: auto;
    padding: 0 15px 15px 15px;
  }
}
</style>
