/**
 * @FilePath: \KrPeis\src\pages\serviceData\occDiseases\configs\baseCodeMenu.js
 * @Description:  职业病基础代码菜单配置
 * @Author: justin
 * @Date: 2024-04-26 08:57:35
 * @Version: 0.0.1
 * @LastEditors: justin
 * @LastEditTime: 2024-05-10 15:27:25
 
 */

import { apiUrls } from '@/common/apiUrls';

export const menuData = [
  {
    id: 'public',
    title: '公共类代码',
    icon: 'iconfont icon-leixing1',
    children: [
      {
        id: 'codeOccupationalCardType',
        title: '证件类型',
        icon: 'iconfont icon-biaodan',
        path: 'baseCodeOccupationalDicTable',
        data: {
          // 根据baseCodeOccupationalDicTable组件配置
          api: {
            add: apiUrls.CreateCodeOccupationalCardType,
            update: apiUrls.UpdateCodeOccupationalCardType,
            getList: apiUrls.ReadCodeOccupationalCardType,
            delete: apiUrls.DeleteCodeOccupationalCardType
          },
          tKey: {
            name: '证件类型代码',
            prop: 'cardTypeCode',
            length: 2
          },
          tValue: {
            name: '证件类型名称',
            prop: 'cardTypeName',
            length: 30
          }
        },
        children: []
      },
      {
        id: 'codeOccupationalMarryStatus',
        title: '婚姻状况',
        icon: 'iconfont icon-biaodan',
        path: 'baseCodeOccupationalDicTable',
        data: {
          // 根据baseCodeOccupationalDicTable组件配置
          api: {
            add: apiUrls.CreateCodeOccupationalMarryStatus,
            update: apiUrls.UpdateCodeOccupationalMarryStatus,
            getList: apiUrls.ReadCodeOccupationalMarryStatus,
            delete: apiUrls.DeleteCodeOccupationalMarryStatus
          },
          tKey: {
            name: '婚姻状况代码',
            prop: 'statusCode',
            length: 1
          },
          tValue: {
            name: '婚姻状况名称',
            prop: 'statusName',
            length: 10
          }
        },
        children: []
      },
      {
        id: 'codeOccupationalPositionStatus',
        title: '岗位状态',
        icon: 'iconfont icon-biaodan',
        path: 'baseCodeOccupationalDicTable',
        data: {
          // 根据baseCodeOccupationalDicTable组件配置
          api: {
            add: apiUrls.CreateCodeOccupationalPositionStatus,
            update: apiUrls.UpdateCodeOccupationalPositionStatus,
            getList: apiUrls.ReadCodeOccupationalPositionStatus,
            delete: apiUrls.DeleteCodeOccupationalPositionStatus
          },
          tKey: {
            name: '岗位状态代码',
            prop: 'statusCode',
            length: 4
          },
          tValue: {
            name: '岗位状态名称',
            prop: 'statusName',
            length: 20
          }
        },
        children: []
      },
      {
        id: 'codeOccupationalAddress',
        title: '地区代码',
        icon: 'iconfont icon-biaodan',
        path: 'baseCodeOccupationalAddressTable',
        children: []
      },
      {
        id: 'codeEconomicType',
        title: '经济类型',
        icon: 'iconfont icon-biaodan',
        path: 'baseCodeOccupationalDicTable',
        data: {
          // 根据baseCodeOccupationalDicTable组件配置
          api: {
            add: apiUrls.CreateCodeOccupationalEconomicType,
            update: apiUrls.UpdateCodeOccupationalEconomicType,
            getList: apiUrls.ReadCodeOccupationalEconomicType,
            delete: apiUrls.DeleteCodeOccupationalEconomicType
          },
          tKey: {
            name: '经济类型代码',
            prop: 'economicCode',
            length: 10
          },
          tValue: {
            name: '经济类型名称',
            prop: 'economicName',
            length: 50
          }
        },
        children: []
      },
      {
        id: 'codeOccupationalIndustry',
        title: '行业分类',
        icon: 'iconfont icon-biaodan',
        path: 'baseCodeOccupationalDicTable',
        data: {
          // 根据baseCodeOccupationalDicTable组件配置
          api: {
            add: apiUrls.CreateCodeOccupationalIndustry,
            update: apiUrls.UpdateCodeOccupationalIndustry,
            getList: apiUrls.ReadCodeOccupationalIndustryByPage,
            delete: apiUrls.DeleteCodeOccupationalIndustry,
            remoteByPage: true
          },
          tKey: {
            name: '行业分类代码',
            prop: 'industryCode',
            length: 200
          },
          tValue: {
            name: '行业分类名称',
            prop: 'industryName',
            length: 200
          }
        },
        children: []
      }
    ]
  },
  {
    id: 'peis',
    title: '体检类代码',
    icon: 'iconfont icon-leixing1',
    children: [
      {
        id: 'codeOccupationalItem',
        title: '体检项目',
        icon: 'iconfont icon-biaodan',
        path: 'baseCodeOccupationalItemTable',
        children: []
      },
      // {
      //   id: "codeOccupationalMeal",
      //   title: "职业病套餐",
      //   icon: "iconfont icon-biaodan",
      //   path: "baseCodeOccupationalMeal",
      //   children: [],
      //   viewConfig: {
      //     viewInfo: {
      //       drawerTheads: {
      //         // clsName: "项目分类",
      //         combCode: "组合代码",
      //         combName: "组合名称",
      //         sex: "性别",
      //         price: "单价",
      //         hisCode: "his代码",
      //       },
      //       title1: "体检组合项目列表",
      //       title2: "",
      //       isItem: true, //项目分类?专业组///EnumData/Department
      //       isSel: true,
      //       isMterials: true, //是否显示材料费
      //       isHead: false, //是否显示步
      //       active: 2, //显示哪个页面
      //       cell_red: ["price"],
      //       param: {
      //         combCode: "combCode",
      //       },
      //       order: `item.combCode?.includes(searchVal) ||
      //       item.combName?.includes(searchVal)`,
      //       orderSel: `item.clsCode?.includes(searchVal)`,
      //       setInfos: { clusCode: "" }, //点击行传参
      //       setInfo: { clusCode: "" }, //点击行传参
      //       apiUrls: {
      //         optionPort: "/EnumData/ItemCls", //下拉
      //         leftPort: "/EnumData/ItemComb_ItemCls", //左边表格
      //         rightPort: "/CodeMapping/Query_ClusterComb", //右边表格(query:clusCode)
      //         del: "/CodeMapping/CD_ClusterComb/Delete",
      //         set: "/CodeMapping/CD_ClusterComb/Create",
      //       },
      //     },
      //   },
      // },
      {
        id: 'codeOccupationalSymptom',
        title: '症状询问',
        icon: 'iconfont icon-biaodan',
        path: 'baseCodeOccupationalSymptomTable',
        children: []
      },
      {
        id: 'codeReportModel',
        title: '报告模块',
        icon: 'iconfont icon-biaodan',
        path: 'baseCodeOccupationalReportModelTable',
        children: []
      },
      {
        id: 'codeOccupationalHazardous',
        title: '危害因素',
        icon: 'iconfont icon-biaodan',
        path: 'baseCodeOccupationalHazardousTable',
        children: []
      },
      {
        id: 'codeOccupationalItemUnit',
        title: '项目计量单位',
        icon: 'iconfont icon-biaodan',
        path: 'baseCodeOccupationalDicTable',
        data: {
          // 根据baseCodeOccupationalDicTable组件配置
          api: {
            add: apiUrls.CreateCodeOccupationalItemUnit,
            update: apiUrls.UpdateCodeOccupationalItemUnit,
            getList: apiUrls.ReadCodeOccupationalItemUnitByPage,
            delete: apiUrls.DeleteCodeOccupationalItemUnit,
            remoteByPage: true
          },
          tKey: {
            name: '项目计量单位代码',
            prop: 'unitCode',
            length: 4
          },
          tValue: {
            name: '项目计量单位名称',
            prop: 'unitName',
            length: 20
          }
        },
        children: []
      },
      {
        id: 'codeOccupationalContraindication',
        title: '职业禁忌证',
        icon: 'iconfont icon-biaodan',
        path: 'baseCodeOccupationalDicTable',
        data: {
          // 根据baseCodeOccupationalDicTable组件配置
          api: {
            add: apiUrls.CreateCodeOccupationalContraindication,
            update: apiUrls.UpdateCodeOccupationalContraindication,
            getList: apiUrls.ReadCodeOccupationalContraindicationByPage,
            delete: apiUrls.DeleteCodeOccupationalContraindication,
            remoteByPage: true
          },
          tKey: {
            name: '职业禁忌证代码',
            prop: 'contraindicationCode',
            length: 5
          },
          tValue: {
            name: '职业禁忌证名称',
            prop: 'contraindicationName',
            length: 200
          }
        },
        children: []
      },
      {
        id: 'codeOccupationalDisease',
        title: '疑似职业病',
        icon: 'iconfont icon-biaodan',
        path: 'baseCodeOccupationalDicTable',
        data: {
          // 根据baseCodeOccupationalDicTable组件配置
          api: {
            add: apiUrls.CreateCodeOccupationalDisease,
            update: apiUrls.UpdateCodeOccupationalDisease,
            getList: apiUrls.ReadCodeOccupationalDiseaseByPage,
            delete: apiUrls.DeleteCodeOccupationalDisease,
            remoteByPage: true
          },
          tKey: {
            name: '疑似职业病代码',
            prop: 'diseaseCode',
            length: 5
          },
          tValue: {
            name: '疑似职业病名称',
            prop: 'diseaseName',
            length: 50
          }
        },
        children: []
      },
      {
        id: 'CodeOccupationalConclusion',
        title: '体检结论',
        icon: 'iconfont icon-biaodan',
        path: 'baseCodeOccupationalDicTable',
        data: {
          // 根据baseCodeOccupationalDicTable组件配置
          api: {
            getList: apiUrls.ReadCodeOccupationalConclusion,
            remoteByPage: false
          },
          tKey: {
            name: '职业病体检结论代码',
            prop: 'conclusionCode',
            length: 10
          },
          tValue: {
            name: '职业病体检结论名称',
            prop: 'conclusionValue',
            length: 255
          }
        },
        children: []
      },
      {
        id: 'CodeOccupationalJob',
        title: '职业病工种',
        icon: 'iconfont icon-biaodan',
        path: 'baseCodeOccupationalDicTable',
        data: {
          // 根据baseCodeOccupationalDicTable组件配置
          api: {
            add: apiUrls.CreateCodeOccupationalJob,
            update: apiUrls.UpdateCodeOccupationalJob,
            getList: apiUrls.ReadCodeOccupationalJobByPage,
            delete: apiUrls.DeleteCodeOccupationalJob,
            remoteByPage: true
          },
          tKey: {
            name: '职业病工种代码',
            prop: 'jobCode',
            length: 10
          },
          tValue: {
            name: '职业病工种名称',
            prop: 'jobName',
            length: 255
          }
        },
        children: []
      },
      {
        id: 'codeOccupationalItemLimit',
        title: '检查项目限定',
        icon: 'iconfont icon-biaodan',
        path: 'baseCodeOccupationalItemLimitTable',
        children: []
      },
      {
        id: 'CodeAuditoryCorrectedValue',
        title: '听力校正值',
        icon: 'iconfont icon-biaodan',
        path: 'baseCodeAuditoryCorrectedValueTable',
        children: []
      },
      {
        id: 'CodeOccupationalImportantMonitorItem',
        title: '重点监测项目',
        icon: 'iconfont icon-biaodan',
        path: 'baseCodeOccupationalImportantMonitorItemTable',
        children: []
      }
    ]
  }
];
