<template>
  <!-- 疾病信息 -->
  <div class="diseaseInformation">
    <div style="display: flex; justify-content: end; align-items: center">
      <DiseaseSearch @change="selectChange" style="margin-right: 10px" />
      <AllBtn
        :btnList="['新建', '保存']"
        :methodCreat="createClick"
        :showSelect="true"
        :hideSearch="false"
        :methodSava="saveClick"
        ref="allBtn_Ref"
      >
        <template #footAdd>
          <el-button
            size="small"
            class="red_btn btn"
            @click="entryClick"
            icon="el-icon-tickets"
            >词条整理</el-button
          >
          <el-button
            size="small"
            class="red_btn btn"
            @click="deleteClick"
            icon="iconfont icon-shanchu"
            >删除</el-button
          >
        </template>
      </AllBtn>
    </div>
    <div class="info-content">
      <el-form
        ref="form"
        :model="form"
        label-width="80px"
        class="form"
        :rules="rules"
      >
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="疾病代码">
              <el-input
                v-model="form.diseaseCode"
                size="small"
                placeholder="请输入疾病代码"
                clearable
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目分类" prop="clsCode">
              <el-select
                size="small"
                v-model="form.clsCode"
                placeholder="请选择项目分类"
                class="select"
                clearable
                filterable
              >
                <el-option
                  :label="item.label"
                  :value="item.value"
                  v-for="item in G_codeItemCls"
                  :key="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="疾病名称" prop="diseaseName">
          <el-input
            v-model="form.diseaseName"
            size="small"
            placeholder="请输入疾病名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="疾病词条" prop="codeDiseaseEntry">
          <div class="disease-entry">
            <div
              v-for="(tag, index) in form.codeDiseaseEntry"
              :key="index"
              class="entry-box"
            >
              <el-input
                v-if="editDiseaseEntryList[index]"
                :ref="formInpTagName + index"
                v-model="tag.entryText"
                class="input-edit-tag"
                size="small"
                placeholder="请输入词条"
                maxlength="100"
                @keyup.enter.native="
                  editEntryInputConfirm(
                    form.codeDiseaseEntry,
                    tag,
                    index,
                    formInpTagName
                  )
                "
                @focus="
                  editEntryInputFoucs(
                    form.codeDiseaseEntry,
                    tag,
                    index,
                    formInpTagName
                  )
                "
                @blur="
                  editEntryInputBlur(
                    form.codeDiseaseEntry,
                    tag,
                    index,
                    formInpTagName
                  )
                "
              />
              <el-tag
                v-else
                closable
                :disable-transitions="false"
                @close="handleClose(tag)"
                @click="
                  showEditEntryInput(
                    form.codeDiseaseEntry,
                    tag,
                    index,
                    formInpTagName
                  )
                "
                :type="tag.err ? 'danger' : ''"
                :ref="formInpTagName + index + '_readonly'"
              >
                {{ tag.entryText }}
              </el-tag>
            </div>

            <el-input
              class="input-new-tag"
              v-if="inputVisible"
              v-model="inputValue"
              ref="saveTagInput"
              size="small"
              placeholder="请输入词条"
              maxlength="100"
              @keyup.enter.native="handleInputConfirm"
              @blur="handleInputConfirm"
            >
            </el-input>
            <el-button
              v-else
              class="button-new-tag"
              size="small"
              @click="showInput"
              icon="iconfont icon-xinjian"
              >新增</el-button
            >
          </div>
        </el-form-item>
        <el-form-item label="科普知识" prop="presentation">
          <el-input
            type="textarea"
            v-model="form.presentation"
            :autosize="{ minRows: 7, maxRows: 7 }"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="建议内容" prop="suggestContent">
          <el-input
            type="textarea"
            v-model="form.suggestContent"
            :autosize="{ minRows: 7, maxRows: 7 }"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="疾病等级" prop="diseaseGrade">
              <el-select
                size="small"
                v-model="form.diseaseGrade"
                placeholder="请选择疾病等级"
                class="select"
                clearable
              >
                <el-option
                  :label="item.label"
                  :value="item.value"
                  v-for="item in G_diseaseGrade"
                  :key="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="8">
            <el-form-item label="排序号">
              <el-input
                v-model="form.name"
                size="small"
                placeholder="请输入排序号"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="进入小结">
              <el-select
                size="small"
                v-model="form.region"
                placeholder="请选择进入小结"
                class="select"
                clearable
              >
                <el-option label="是" :value="true"></el-option>
                <el-option label="否" :value="false"></el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
        </el-row>
        <!-- <el-form-item label="备注" class="note">
          <el-input
            v-model="form.name"
            size="small"
            placeholder="请输入备注"
            clearable
          ></el-input>
        </el-form-item> -->
      </el-form>
    </div>

    <!-- 词条整理 -->
    <el-drawer
      :title="disTitle"
      :visible.sync="detailsShow"
      @close="detailsClose"
      :wrapperClosable="false"
      size="1100px"
    >
      <div class="bodyDiv">
        <div class="leftBody">
          <div class="every_inp">
            <label class="labelDiv">疾病列表</label>
            <el-input
              size="small"
              placeholder="疾病代码/名称"
              clearable
              v-model="searchInfo.keyword"
              @clear="GetCodeDiseases"
              @keyup.enter.native="GetCodeDiseases"
            ></el-input>
            <el-button
              size="small"
              class="blue_btn btn"
              icon="iconfont icon-search"
              @click="GetCodeDiseases"
              >搜索</el-button
            >
          </div>

          <div class="record_table">
            <PublicTable
              :viewTableList.sync="leftData"
              :theads="leftTheads"
              :isSortShow="false"
              ref="leftTable"
              :tableLoading="tableLoading"
              @currentChange="rowClick"
              :columnWidth="{
                diseaseCode: 110,
                diseaseName: 180
              }"
            >
            </PublicTable>
          </div>
        </div>
        <div class="centerBody">
          <el-button
            size="small"
            class="blue_btn add-del"
            style="margin-bottom: 40px"
            @click="addDisease"
          >
            转
            <i class="iconfont icon-Rightxiangyou34"></i>
          </el-button>
        </div>
        <div class="rightBody">
          <div class="every_inp">
            <el-button
              size="small"
              class="blue_btn btn"
              icon="iconfont icon-baocun"
              @click="savedisClick"
              >保存</el-button
            >

            <el-button
              size="small"
              class="red_btn btn"
              icon="iconfont icon-shanchu"
              v-if="originalData.length > 0"
              @click="delClick"
              >删除</el-button
            >
          </div>

          <div class="record_table">
            <el-empty
              description="暂无数据"
              v-if="originalData.length == 0 && rightData.length == 0"
            ></el-empty>
            <div class="originalDiv" v-if="originalData.length > 0">
              <el-checkbox
                :indeterminate="isIndeterminate"
                v-model="checkAll"
                @change="handleCheckAllChange"
                class="checkbox"
                >全选</el-checkbox
              >

              <el-checkbox-group
                v-model="checkedData"
                @change="handleCheckedChange"
                class="drawer-entry-check-group"
              >
                <el-checkbox
                  v-for="(tag, index) in originalData"
                  :key="index"
                  :label="index"
                  :title="tag.entryText"
                >
                  <div class="entry-box" @click.stop.prevent="() => {}">
                    <el-input
                      v-if="editDiseaseEntryList[index]"
                      :ref="drawerInpTagName + index"
                      v-model="tag.entryText"
                      class="input-edit-tag"
                      size="small"
                      placeholder="请输入词条"
                      maxlength="100"
                      @keyup.enter.native="
                        editEntryInputConfirm(
                          originalData,
                          tag,
                          index,
                          drawerInpTagName
                        )
                      "
                      @focus="
                        editEntryInputFoucs(
                          originalData,
                          tag,
                          index,
                          drawerInpTagName
                        )
                      "
                      @blur="
                        editEntryInputBlur(
                          originalData,
                          tag,
                          index,
                          drawerInpTagName
                        )
                      "
                    />
                    <el-tag
                      v-else
                      :disable-transitions="false"
                      @click="
                        showEditEntryInput(
                          originalData,
                          tag,
                          index,
                          drawerInpTagName
                        )
                      "
                      :type="tag.err ? 'danger' : ''"
                      :ref="drawerInpTagName + index + '_readonly'"
                    >
                      {{ tag.entryText }}
                    </el-tag>
                  </div>
                </el-checkbox>
              </el-checkbox-group>
            </div>
            <div class="newDiv">
              <div class="dataDiv" v-for="(items, idx) in rightData" :key="idx">
                <p class="disp1">
                  <span
                    class="textOverflow"
                    :title="'（' + items.diseaseCode + '）' + items.diseaseName"
                    >{{
                      '（' + items.diseaseCode + '）' + items.diseaseName
                    }}</span
                  ><span>
                    <el-button size="mini" @click="deleteDisease(items)"
                      >撤销</el-button
                    ></span
                  >
                </p>
                <p
                  class="disp2"
                  v-for="(diseaseEntry, i) in items.codeDiseaseEntry"
                  :key="i"
                >
                  <span class="textOverflow" :title="diseaseEntry.entryText"
                    >{{ i + 1 }}、{{ diseaseEntry.entryText }}</span
                  ><span>
                    <el-button
                      size="mini"
                      @click="deleteDiseaseEntry(diseaseEntry)"
                      >删除</el-button
                    ></span
                  >
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import AllBtn from '@/pages/serviceData/allBtn';
import { mapGetters } from 'vuex';
import PublicTable from '@/components/publicTable';
import TreeTable from '../codeMapping/tableCom/treeTable.vue';
import DiseaseSearch from '@/components/diseaseSearch';
import XEUtils from 'xe-utils';
export default {
  name: 'diseaseInformation',

  props: {
    menuList: {
      type: Array,
      default: []
    }
  },
  components: {
    AllBtn,
    PublicTable,
    TreeTable,
    DiseaseSearch
  },
  data() {
    return {
      form: {
        diseaseCode: '',
        diseaseName: '',
        deptCode: '',
        presentation: '',
        suggestContent: '',
        diseaseGrade: null,
        codeDiseaseEntry: []
      },
      inputVisible: false,
      inputValue: '',
      isEdit: true,
      rules: {
        // diseaseCode: [
        //   { required: true, message: "请输入疾病代码", trigger: "blur" }
        // ],
        diseaseName: [
          { required: true, message: '请输入疾病名称', trigger: 'blur' }
        ],
        clsCode: [
          { required: true, message: '请选择项目分类', trigger: 'change' }
        ]
      },
      detailsShow: false,
      leftTheads: {
        diseaseCode: '疾病代码',
        diseaseName: '疾病名称',
        clsName: '项目分类'
      },
      tableLoading: false,
      leftData: [],
      leftDataCopy: [],
      rowList: {},
      searchInfo: {
        pageSize: 0,
        pageNumber: 0,
        keyword: ''
      },
      rightData: [],
      checkAll: false,
      isIndeterminate: null,
      checkedData: [],
      originalData: [],
      disTitle: '',
      isDel: false,
      editDiseaseEntryList: [],
      formInpTagName: 'form_inp_tag',
      drawerInpTagName: 'drawer_inp_tag'
    };
  },
  created() {},
  computed: {
    ...mapGetters(['G_diseaseGrade', 'G_codeItemCls'])
  },
  methods: {
    //疾病表格
    GetCodeDiseases() {
      this.$ajax
        .post(this.$apiUrls.GetCodeDiseases, this.searchInfo)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          const data = returnData.filter((item) => {
            return item.diseaseCode != this.form.diseaseCode;
          });
          this.leftData = data;
          this.leftDataCopy = data;
          this.$nextTick(() => {
            this.filteredLeftArray();
          });
          this.getCodeDisease(this.form.diseaseCode, true, this.form.clsName);
        });
    },
    //打开词条抽屉
    entryClick() {
      if (!this.isEdit) {
        this.$message({
          message: '新建状态不可整理词条!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.rightData = [];
      this.GetCodeDiseases();
      this.detailsShow = true;
      this.disTitle = `${this.form.diseaseName}--词条整理`;
      this.editDiseaseEntryList = [];
    },
    //关闭词条抽屉
    detailsClose() {
      this.detailsShow = false;
      this.rightData = [];
      this.originalData = [];
      this.checkAll = false;
      this.checkedData = [];
      this.isIndeterminate = null;
      this.searchInfo.keyword = '';
      this.isDel = false;
    },
    //点击左边疾病表格
    rowClick(row) {
      this.rowList = row;
    },
    //获取疾病信息
    getCodeDisease(diseaseCode, flag = false, clsName = '') {
      this.$ajax
        .post(`${this.$apiUrls.RD_CodeDisease}/Read`, '', {
          query: { diseaseCode: diseaseCode }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success || !returnData) return;
          if (flag) {
            this.originalData = returnData.codeDiseaseEntry || [];
          } else {
            returnData.clsName = clsName;
            //已有就替换,没有就新增进去
            let found = false;
            this.rightData.forEach((item, index) => {
              if (item.diseaseCode === returnData.diseaseCode) {
                this.rightData[index] = returnData;
                found = true;
              }
            });
            if (!found) {
              this.rightData.push(returnData);
            }
            this.$nextTick(() => {
              this.filteredLeftArray();
            });
            this.$message({
              message: '成功',
              type: 'success',
              showClose: true
            });
          }
        });
    },
    //转词条
    addDisease() {
      if (!this.rowList.diseaseCode) {
        this.$message({
          message: '请选择需要转为词条的疾病。',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm('转为词条后，此疾病将无法使用，请确认是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.getCodeDisease(
            this.rowList.diseaseCode,
            false,
            this.rowList.clsName
          );
        })
        .catch(() => {});
    },
    //批量疾病词条转所属疾病词条
    BatchCodeDiseasesToEntries(data) {
      this.$ajax
        .post(this.$apiUrls.BatchCodeDiseasesToEntries, data)
        .then((r) => {
          let { success, returnMsg } = r.data;
          if (!success) return;
          //保存成功后重新刷新数据
          this.updateMenuTreeList(2, '');
          this.getDiseaseInfo(this.form.diseaseCode);
          this.getCodeDisease(this.form.diseaseCode, true, this.form.clsName);
          this.checkedData = [];
          this.checkAll = false;
          this.rightData = [];
          this.$message({
            message: returnMsg,
            type: 'success',
            showClose: true
          });
        });
    },

    //保存
    savedisClick() {
      //本来就是空的,直接返回
      const that = this;
      if (
        that.originalData.length < 1 &&
        that.rightData.length < 1 &&
        !that.isDel
      ) {
        return that.$message({
          message: '没有可保存的词条!',
          type: 'warning',
          showClose: true
        });
      }

      let data = [];
      //都为空,默认传当前疾病代码
      if (this.originalData.length < 1 && this.rightData.length < 1) {
        data.push({
          diseaseCode: this.form.diseaseCode,
          entryText: this.form.diseaseCode,
          diseaseCodeFrom: this.form.diseaseCode
        });
      }
      //原有内容有数据,来源疾病词条默认传空
      if (this.originalData.length > 0) {
        this.originalData.forEach((item) => {
          data.push({
            diseaseCode: item.diseaseCode,
            entryText: item.entryText,
            diseaseCodeFrom: ''
          });
        });
      }
      //新增词条转成固定数据格式传参,有过删除的就不传来源疾病代码
      if (this.rightData.length > 0) {
        this.rightData.forEach((item) => {
          data.push({
            diseaseCode: this.form.diseaseCode,
            entryText: item.diseaseName,
            diseaseCodeFrom: item.isBack ? '' : item.diseaseCode
          });
          if (item.codeDiseaseEntry.length > 0) {
            item.codeDiseaseEntry.forEach((items) => {
              data.push({
                diseaseCode: this.form.diseaseCode,
                entryText: items.entryText,
                diseaseCodeFrom: item.isBack ? '' : items.diseaseCode
              });
            });
          }
        });
      }

      const duplicates = data.reduce((acc, item) => {
        acc[item.entryText] = (acc[item.entryText] || 0) + 1;
        return acc;
      }, {});
      const values = Object.values(duplicates);
      if (data && values.some((x) => x >= 2)) {
        this.$message({
          message: '存在相同疾病词条，请修改！',
          type: 'warning',
          showClose: true
        });
        return;
      }

      let msg = '转为词条后，疾病将无法使用，请确认是否继续？';
      msg =
        that.rightData.filter((x) => x.isBack === true).length ===
        that.rightData.length
          ? '请确认是否进行保存操作？'
          : msg;
      that
        .$confirm(msg, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        .then(() => {
          that.saveFun(data);
        })
        .catch(() => {});
    },
    //保存函数
    saveFun(data) {
      //this.updateMenuTreeList(2, "");//测试更新菜单树列表
      //return;
      //执行批量疾病词条转所属疾病词条
      this.BatchCodeDiseasesToEntries(data);
    },
    //原有词条全选
    handleCheckAllChange(val) {
      this.checkedData = val
        ? this.originalData.map((item, index) => index)
        : [];
      this.isIndeterminate = false;

      this.handleCheckedChange(this.checkedData);
    },
    //原有词条改变勾选
    handleCheckedChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.originalData.length;
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.originalData.length;
    },
    //删除原有词条
    delClick() {
      if (this.checkedData.length < 1) {
        this.$message({
          message: '没有可删除的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm('请确认是否删除已选择词条？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.delete();
        })
        .catch(() => {});
    },
    //确定删除
    delete() {
      const that = this;
      that.originalData = that.originalData.filter((item, index) => {
        return !that.checkedData.some((x) => x == index);
      });
      that.checkedData = []; // 重置
      that.isIndeterminate = null;
      that.checkFormEntry(that.originalData, null, -1, null);
      that.isDel = true;
      that.$message({
        message: '删除成功',
        type: 'success'
      });
    },

    //撤销疾病
    deleteDisease(data) {
      this.$confirm('撤销后，将恢复待转化的疾病，请确认是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.deleteDiseaseFun(data);
        })
        .catch(() => {});
    },

    //确定撤销
    deleteDiseaseFun(data) {
      this.rightData = this.rightData.filter((items) => items !== data);
      this.filteredLeftArray();
      this.$message({
        message: '撤销成功',
        type: 'success'
      });
    },
    //删除词条
    deleteDiseaseEntry(data) {
      // 如果未恢复,则提示: 删除词条,将恢复待转化的疾病,请确认是否继续？
      // 已恢复: 是否确定要删除这个词条?
      let msg = '删除词条,将恢复待转化的疾病,请确认是否继续？';
      msg = this.rightData.some(
        (x) => x.diseaseCode === data.diseaseCode && x.isBack === true
      )
        ? '是否确定要删除这个词条?'
        : msg;
      this.$confirm(msg, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.deleteDiseaseEntryFun(data);
        })
        .catch(() => {});
    },
    //确定删除词条
    deleteDiseaseEntryFun(data) {
      //通过加标识位,在保存时过滤掉以及恢复
      const updatedData = this.rightData.map((item) => {
        item.codeDiseaseEntry = item.codeDiseaseEntry.filter(
          (items) => items !== data
        );
        if (item.diseaseCode !== data.diseaseCode) return item;

        item.isBack = true;
        return item;
      });
      this.rightData = updatedData;
      this.leftData = this.leftDataCopy.filter((leftItem) => {
        return !this.rightData.some(
          (rightItem) =>
            rightItem.diseaseCode === leftItem.diseaseCode && !rightItem.isBack
        );
      });
      this.$message({
        message: '删除成功!',
        type: 'success',
        showClose: true
      });
    },
    //过滤词条整理左边表格
    filteredLeftArray() {
      const that = this;
      if (that.rightData.length === 0) {
        that.leftData = that.leftDataCopy;
        return;
      }
      that.leftData = that.leftDataCopy.filter((leftItem) => {
        return !that.rightData.some(
          (rightItem) =>
            rightItem.diseaseCode === leftItem.diseaseCode && !rightItem.isBack
        );
      });
    },
    // 获取疾病信息详情
    getDiseaseInfo(diseaseCode) {
      this.$nextTick(() => {
        this.$refs.form.resetFields();
      });
      this.$ajax
        .post(`${this.$apiUrls.RD_CodeDisease}/Read`, '', {
          query: { diseaseCode: diseaseCode }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.form = returnData || {};
          this.originalData = returnData.codeDiseaseEntry || [];
          this.isEdit = true;
        });
    },
    // 新建
    createClick() {
      this.isEdit = false;
      this.$nextTick(() => {
        this.$refs.form.resetFields();
        this.form.diseaseCode = '';
      });
    },
    // 搜索
    async selectChange(val) {
      let diseaseCode = val.diseaseCode;
      if (!val) return;
      this.$parent.muenId = diseaseCode;
      await this.getDiseaseInfo(diseaseCode);
      const matchObj = XEUtils.findTree(
        this.$parent.menuList,
        (item) => item.id === 'two_id' + diseaseCode,
        { children: 'children' }
      );
      this.$parent.treeExpand(matchObj.item);
      this.$parent.rightTitle =
        matchObj.nodes[0].title +
        ' | ' +
        matchObj.nodes[1].title +
        ' | ' +
        matchObj.nodes[2].title;
    },
    // 删除
    deleteClick() {
      if (!this.form.diseaseCode) {
        this.$message({
          message: '请选择要删除的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm(`是否确定要删除数据?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax
            .post(`${this.$apiUrls.RD_CodeDisease}/Delete`, '', {
              query: { diseaseCode: this.form.diseaseCode }
            })
            .then((r) => {
              let { success, returnData } = r.data;
              if (!success) return;
              this.$message({
                message: '删除成功!',
                type: 'success',
                showClose: true
              });
              this.rightData = [];
              this.rightData.push({
                clsName: this.$parent.clsName,
                diseaseCode: this.form.diseaseCode
              });
              this.$nextTick(() => {
                this.$refs.form.resetFields();
              });
              this.updateMenuTreeList(2, '');
              this.$parent.muenId =
                this.$parent.menuList[0].children[0].children[0].id;
            });
        })
        .catch(() => {
          return false;
        });
    },
    // 保存
    saveClick() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.form.codeDiseaseEntry.some((x) => x.err)) {
            this.$message({
              message: '存在相同疾病词条，请修改！',
              type: 'warning',
              showClose: true
            });
            return;
          }
          let type = this.isEdit ? 'Update' : 'Create';
          this.$ajax
            .post(`${this.$apiUrls.CU_CodeDisease}/${type}`, this.form)
            .then((r) => {
              let { success, returnData } = r.data;
              if (!success) return;

              if (this.isEdit) {
                this.rightData = [];
                this.rightData.push({
                  clsName: this.$parent.clsName,
                  diseaseCode: this.form.diseaseCode
                });
                this.updateMenuTreeList(1, this.form.diseaseName);
              } else {
                this.$parent.initData();
              }

              this.$message({
                message: this.isEdit ? '修改成功!' : '新建成功!',
                type: 'success',
                showClose: true
              });
            });
        }
      });
    },

    /**
     * 更新菜单树列表
     * @param {number} type 1 修改疾病名称 2 移除该疾病
     * @param {string} diseaseName 疾病名称
     */
    updateMenuTreeList(type, diseaseName = '') {
      const that = this;
      const { menuList } = that.$parent; //获取父级菜单数据
      const removeDisFunc = (type, topParent) => {
        if (!topParent) return;
        const array = topParent.children;
        if (!array) return;
        for (let a = 0; a < array.length; a++) {
          const cls = array[a];
          const diseArr = that.rightData.filter(
            (x) => x.clsName === cls.title && !x.isBack
          );
          if (diseArr.length === 0) continue;

          for (let b = 0; b < cls.children.length; b++) {
            const dise = cls.children[b];
            if (!dise) continue;

            const curDise = diseArr.find(
              (x) => x.diseaseCode === dise.diseaseCode
            );
            if (!curDise) continue;

            switch (type) {
              case 1: // 修改疾病名称
                array[a].children[b].diseaseName = diseaseName;
                array[a].children[b].title = diseaseName;
                if (topParent._EXPAND)
                  this.$parent.rightTitle = `${topParent.title}｜${array[a].title}｜${array[a].children[b].title}`;
                break;
              case 2: // 移除该疾病
                delete array[a].children[b];
                break;
            }
          }
        }
      };

      for (let index = 0; index < menuList.length; index++) {
        const topParent = menuList[index];
        if (!topParent.children || topParent.children.length === 0) continue;
        removeDisFunc(type, topParent);
      }
      //重新刷新树
      this.$parent.refreshTree();
    },
    // 标签删除
    handleClose(tag) {
      this.$confirm(`是否确定要删除这个词条?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.form.codeDiseaseEntry.splice(
            this.form.codeDiseaseEntry.indexOf(tag),
            1
          );
          this.checkFormEntry(this.form.codeDiseaseEntry, null, -1, null);
        })
        .catch(() => {
          return false;
        });
    },
    // 显示输入框
    showInput() {
      this.inputVisible = true;
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    // 输入框提交
    handleInputConfirm() {
      let inputValue = this.inputValue;
      if (inputValue) {
        let item = {
          diseaseCode: this.form.diseaseCode,
          entryText: inputValue
        };
        this.form.codeDiseaseEntry.push(item);
        this.checkFormEntry(
          this.form.codeDiseaseEntry,
          item,
          this.form.codeDiseaseEntry.length - 1,
          this.formInpTagName
        );
      }
      this.inputVisible = false;
      this.inputValue = '';
    },

    /**
     * @author: justin
     * @description: 表单、词条整理-词条 input显示
     * @param {*} index
     * @return {*}
     */
    showEditEntryInput(arr, item, index, inpTagName) {
      this.$set(this.editDiseaseEntryList, index, true);
      this.$nextTick((_) => {
        const editTagInput = `${inpTagName}${index}`;
        this.$refs[editTagInput][0].$refs.input.focus();
        arr[index].entryTextOld = arr[index].entryText;
      });
    },

    /**
     * @author: justin
     * @description: 表单、词条整理-词条 input发生改变
     * @param {*} item
     * @param {*} index
     * @return {*}
     */
    editEntryInputConfirm(arr, item, index, inpTagName) {
      const that = this;
      item.confirm = true;
      if (that.checkFormEntry(arr, item, index, inpTagName)) {
        that.$set(that.editDiseaseEntryList, index, false);
      }
    },

    /**
     * @author: justin
     * @description: 表单、词条整理-词条 input失去焦点
     * @param {*} item
     * @param {*} index
     * @return {*}
     */
    editEntryInputBlur(arr, item, index, inpTagName) {
      const that = this;
      if (item.confirm || !arr.some((x) => x.entryText === item.entryText))
        return;

      if (that.checkFormEntry(arr, item, index, inpTagName)) {
        that.$set(that.editDiseaseEntryList, index, false);
      }
    },

    /**
     * @author: justin
     * @description: 检查表单、词条整理-词条
     * @param {*} item
     * @param {*} index
     * @return {*}
     */
    checkFormEntry(arr, item, index, inpTagName) {
      const that = this;
      const duplicates = arr.reduce((acc, item) => {
        acc[item.entryText] = (acc[item.entryText] || 0) + 1;
        return acc;
      }, {});
      for (const key in duplicates) {
        arr
          .filter((item) => item.entryText == key)
          .map((item) => {
            that.$set(item, 'err', duplicates[key] >= 2);
          });
      }

      if (!item) return;

      const editTagInput = `${inpTagName}${index}`;
      let flag = true;
      if (!item.entryText || !item.entryText.trim().length === 0) {
        that
          .$confirm(`当前词条内容为空，继续将不保存？`, '提示', {
            confirmButtonText: '继续',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(() => {
            arr.splice(index, 1);
            that.editDiseaseEntryList.splice(index, 1);
          })
          .catch(() => {
            item.confirm = false;
            that.$set(that.editDiseaseEntryList, index, true);
            that.$nextTick((_) => {
              const ref = that.$refs[editTagInput][0].$refs;
              if (ref) {
                ref.input.focus();
              }
            });

            if (arr[index].entryTextOld)
              arr[index].entryText = arr[index].entryTextOld;
            return false;
          });

        flag = false;
      } else if (
        arr.some((x, i) => x.entryText === item.entryText && i !== index)
      ) {
        that.$message({
          message: '当前词条已存在，请勿重复录入!',
          type: 'warning',
          showClose: true
        });

        that.$set(that.editDiseaseEntryList, index, false);
        flag = false;
      }

      return flag;
    },

    /**
     * @author: justin
     * @description: 表单、词条整理-词条输入聚焦
     * @param {*} item
     * @param {*} index
     * @return {*}
     */
    editEntryInputFoucs(arr, item, index, inpTagName) {
      const that = this;
      item.confirm = false;
    }
  }
};
</script>

<style lang="less" scoped>
.diseaseInformation {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .info-content {
    background: rgba(23, 112, 223, 0.2);
    border-radius: 4px;
    padding: 20px 18px 18px 8px;
    flex: 1;
    overflow: auto;
  }

  .form {
    /deep/.el-form-item__label {
      color: #2d3436;
      font-weight: 600;
    }

    /deep/.el-form-item {
      margin-bottom: 18px;
    }

    .note {
      margin-bottom: 0;
    }
  }

  .select {
    width: 100%;
  }

  .disease-entry {
    display: flex;
    flex-wrap: wrap;
    height: 96px;
    background: #fff;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: auto;
    align-items: baseline;

    .entry-box {
      display: flex;
      align-items: center;
      height: max-content;
    }

    .el-tag {
      margin-left: 10px;
      margin-top: 10px;
      white-space: pre-line;
      word-wrap: break-word;
      height: auto;
    }

    .button-new-tag {
      margin-left: 10px;
      height: 32px;
      line-height: 30px;
      padding-top: 0;
      padding-bottom: 0;
      font-size: 14px !important;
      margin-top: 10px;

      /deep/.iconfont {
        font-size: 12px;
      }
    }

    .input-new-tag,
    .input-edit-tag {
      width: 120px;
      margin-left: 10px;
    }
  }

  .btn {
    padding: 6.5px 7px;
  }

  /deep/.el-drawer__header {
    align-items: center;
    display: flex;
    background: rgba(23, 112, 223, 0.2);
    line-height: 42px;
    font-size: 18px;
    padding: 0;
    padding-left: 20px;
    color: #2d3436;
    margin-bottom: 0;
    font-weight: 600;
  }

  /deep/.el-drawer__close-btn {
    font-size: 30px;
    margin-right: 16px;
  }

  .bodyDiv {
    flex: 1;
    flex-shrink: 0;
    height: calc(100% - 40px);
    display: flex;
    flex-direction: row;
    margin: 20px 20px 0px 20px;

    .leftBody {
      width: 450px;
      display: flex;
      flex-direction: column;

      .labelDiv {
        line-height: 32px;
        font-weight: 600;
        margin-right: 20px;
      }
    }

    .centerBody {
      width: 100px;
      height: 100%;
      display: flex;
      justify-content: center;
      flex-direction: column;
      align-items: center;

      .el-button + .el-button {
        margin-left: 0;
      }
    }

    .rightBody {
      flex: 1;
      overflow: auto;
      display: flex;
      flex-direction: column;

      .record_table {
        padding: 15px;
        display: flex;
        flex-direction: column;
      }

      .el-checkbox-group {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: auto;
      }

      .el-checkbox {
        margin: 0;
        margin-bottom: 6px;
        display: flex;

        /deep/.el-checkbox__label {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      /deep/.el-checkbox__inner::after {
        left: 7px;
        top: 4px;
      }

      .checkbox {
        padding-bottom: 5px;
        border-bottom: 1px solid #ccc;
      }

      /deep/.el-checkbox__inner {
        width: 20px;
        height: 20px;
      }

      /deep/.el-checkbox__label {
        font-weight: 600;
        color: rgb(45, 52, 54);
        font-size: 15px;
      }

      .originalDiv {
        border-bottom: 1px solid #ccc;
        overflow: auto;
        min-height: 100px;
        max-height: 50%;
        display: flex;
        flex-direction: column;

        .drawer-entry-check-group {
          /deep/.el-checkbox {
            width: min-content;
            display: flex;
            align-items: center;
            margin: 5px 0;
          }

          .entry-box {
            .input-edit-tag {
              width: max-content;
            }
          }
        }
      }

      .newDiv {
        flex: 1;
        overflow: auto;

        .dataDiv {
          border-bottom: 1px solid #ccc;
          padding-top: 15px;
        }
      }
    }

    .every_inp {
      display: flex;
      margin-bottom: 18px;
      justify-content: end;

      .el-input {
        width: 200px;
        margin-right: 20px;
      }
    }

    .record_table {
      flex: 1;
      flex-shrink: 0;
      border-radius: 4px;
      border: 1px solid #dbe1e4;
      overflow: auto;

      .disp,
      .disp1,
      .disp2 {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        color: rgb(45, 52, 54);
        font-weight: 600;
        font-size: 14px;

        .span {
          margin-right: 10px;
        }
      }

      .disp1 {
        font-size: 15px;
      }

      .disp2 {
        margin-left: 20px;
        margin-bottom: 5px;
        font-size: 15px;
      }

      .textOverflow {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 28px;
      }
    }
  }
}
</style>
