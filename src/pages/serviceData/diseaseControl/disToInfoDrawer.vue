<template>
  <div class="disToInfoDrawer">
    <div class="stepDiv">
      <el-steps :active="active">
        <el-step title="步骤1:" description="选择父疾病"></el-step>
        <el-step title="步骤2:" description="选择子疾病"></el-step>
      </el-steps>
      <div class="btnDiv">
        <el-button
          style="margin-top: 12px"
          @click="last"
          v-show="active == 2"
          :disabled="isEdit"
          >上一步</el-button
        >
        <el-button style="margin-top: 12px" @click="next" v-show="active == 1"
          >下一步</el-button
        >
        <!-- <el-button
          style="margin-top: 12px"
          size="small"
          class="blue_btn"
          icon="iconfont icon-baocun"
          v-show="active == 2 && !isEdit"
          >保存</el-button
        > -->
      </div>
    </div>
    <!-- 第一页 -->
    <div class="bodyDiv1" v-show="active == 1">
      <ul class="record_head head1">
        <li>
          <div class="every_inp">
            <label>父类疾病列表</label>
          </div>
        </li>
        <li>
          <div class="every_inp1">
            <el-select
              v-model.trim="selVal"
              placeholder="请选择"
              size="small"
              clearable
              filterable
              @change="getviewList"
              @clear="getviewList"
            >
              <el-option
                v-for="item in G_codeItemCls"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
            <el-input
              v-model.trim="iptVal"
              @keyup.enter.native="getviewList"
              @clear="getviewList"
              size="small"
              style="margin: 0 20px"
              clearable
              placeholder="代码/名称"
            ></el-input>
            <el-button
              size="small"
              class="blue_btn btn"
              icon="iconfont icon-search"
              @click="getTable1"
              >搜索</el-button
            >
          </div>
        </li>
      </ul>
      <div class="record_table">
        <PublicTable
          :viewTableList="firstList"
          :theads="drawerThead"
          @currentChange="handleClick"
          :isSortShow="false"
        >
        </PublicTable>
      </div>
    </div>

    <!-- 第二页 -->
    <div class="fullbodyDiv" v-show="active == 2">
      <div class="leftBody">
        <ul class="record_head">
          <li>
            <div class="every_inp">
              <label> 子疾病列表 </label>
              <p>
                <el-select
                  v-model.trim="leftSel"
                  placeholder="请选择"
                  size="small"
                  clearable
                  filterable
                  @change="getLeftList"
                  @clear="getLeftList"
                >
                  <el-option
                    v-for="item in G_codeItemCls"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </p>
            </div>
            <div class="every_inp1">
              <p>
                <el-input
                  v-model.trim="leftIpt"
                  @keyup.enter.native="getLeftList"
                  @clear="getLeftList"
                  size="small"
                  placeholder="代码/名称"
                  clearable
                ></el-input>
              </p>
            </div>
            <el-button
              size="small"
              class="blue_btn btn"
              icon="iconfont icon-search"
              @click="getLeftList"
              >搜索</el-button
            >
          </li>
        </ul>
        <div class="record_table">
          <PublicTable
            :viewTableList="leftList"
            :theads="comThead"
            isCheck
            :isSortShow="false"
            @selectionChange="selectionChange"
            ref="leftTable"
          >
          </PublicTable>
        </div>
      </div>
      <div class="centerBody">
        <el-button
          size="small"
          class="blue_btn add-del"
          style="margin-bottom: 40px"
          @click="addComb"
          >添加
          <i class="iconfont icon-Rightxiangyou34"></i>
        </el-button>
        <el-button
          size="small"
          class="red_btn add-del"
          @click="delComb"
          icon="iconfont icon-Leftxiangzuo35"
        >
          删除</el-button
        >
      </div>
      <div class="rightBody">
        <ul class="record_head">
          <li>
            <label :title="title2">{{ title2 }}</label>

            <div class="div_inp">
              <p>
                <el-select
                  v-model.trim="rightSel"
                  placeholder="请选择"
                  size="small"
                  clearable
                  filterable
                  @change="getRightData"
                  @clear="getRightData"
                >
                  <el-option
                    v-for="item in G_codeItemCls"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
                <el-input
                  v-model.trim="rightIpt"
                  @keyup.enter.native="getRightData"
                  @clear="getRightData"
                  placeholder="代码/名称"
                  size="small"
                  clearable
                >
                </el-input>
                <el-button
                  size="small"
                  class="blue_btn btn"
                  icon="iconfont icon-search"
                  @click="getRightData"
                  >搜索</el-button
                >
              </p>
            </div>
          </li>
        </ul>
        <div class="record_table">
          <div class="tableDiv">
            <PublicTable
              :viewTableList="rightList"
              :theads="comThead"
              isCheck
              :isSortShow="false"
              :highlightCurrentRow="false"
              @selectionChange="rightSelectionChange"
              ref="rightTable"
            >
            </PublicTable>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import PublicTable from '@/components/publicTable.vue';
import { mapGetters } from 'vuex';
export default {
  name: 'disToInfoDrawer',
  components: {
    PublicTable
  },
  props: {
    rowInfo: {
      type: Object,
      default: () => {
        return {};
      }
    },
    actives: {
      type: Number,
      default: () => {
        return 1;
      }
    },
    isEdit: {
      type: Boolean,
      default: () => {
        return false;
      }
    }
  },
  data() {
    return {
      active: this.actives,
      loading: false,
      iptVal: '', //
      selVal: '',
      selList: [],
      drawerThead: {
        diseaseCode: '父疾病代码',
        diseaseName: '父疾病名称',
        clsName: '项目分类'
      },
      firstList: [],
      firstListCopy: [],
      leftIpt: '',
      leftSel: '',
      comThead: {
        diseaseCode: '子疾病代码',
        diseaseName: '子疾病名称',
        clsName: '科室'
      },
      leftList: [],
      rowInfos: [], //点击第一行数据
      leftSelection: [],
      rightIpt: '',
      rightSel: '',
      rightSelection: [],
      rightList: [],
      rightListCopy: [],
      columnWidth: {},
      title2: this.rowInfo.diseaseName + '-对应子疾病列表',
      diseaseCode: this.rowInfo.diseaseCode
    };
  },
  computed: { ...mapGetters(['G_EnumList', 'G_codeItemCls']) },
  created() {},
  mounted() {
    this.getTable1();
  },
  methods: {
    // 左边表格多选
    selectionChange(val) {
      console.log('val: ', val);
      this.leftSelection = [];
      val.map((item, index) => {
        this.leftSelection.push({
          parentDiseaseCode: this.diseaseCode,
          childDiseaseCode: item.diseaseCode
        });
      });
    },
    // 右边表格多选
    rightSelectionChange(val) {
      this.rightSelection = [];
      val.map((item, index) => {
        this.rightSelection.push({
          parentDiseaseCode: this.diseaseCode,
          childDiseaseCode: item.diseaseCode
        });
      });
      console.log('this.rightSelection: ', this.rightSelection);
    },
    // 上一步
    last() {
      if (this.active-- <= 1) this.active = 1;
    },
    // 下一步
    next() {
      console.log(this.rowInfos.length);
      if (this.rowInfos.length < 1) {
        this.$message({
          message: '请先选择一个体检项目！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.getAllData();
      if (this.active++ >= 2) this.active = 2;
    },

    //获取页面一表格数据
    getTable1() {
      this.$ajax.post(this.$apiUrls.GetParentDeptDisease, []).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.firstList = returnData || [];
        this.firstListCopy = returnData || [];
        this.loading = false;
      });
    },
    // 筛选表一数据
    getviewList() {
      if (!this.selVal && !this.iptVal) {
        return (this.firstList = this.firstListCopy);
      }
      var obj = {};
      obj = this.G_codeItemCls.find((item) => {
        return item.value === this.selVal;
      });
      let newData = [];
      if (obj) {
        newData = this.firstListCopy.filter((item) => {
          return item.clsName.indexOf(obj.label) !== -1;
        });
      } else {
        newData = this.firstListCopy;
      }
      this.firstList = newData.filter((item) => {
        return (
          item.diseaseCode.indexOf(this.iptVal) !== -1 ||
          item.diseaseName.indexOf(this.iptVal) !== -1
        );
      });
    },

    //页面一点击事件
    handleClick(row) {
      console.log('[ row ]-389', row);
      this.diseaseCode = row.diseaseCode;
      this.title2 = row.diseaseName + '-对应子疾病列表';
      this.rowInfos = row;
    },

    //获取页面二表格数据
    //左边
    getLeftSearch() {
      this.$ajax
        .post(this.$apiUrls.GetChildDeptDisease, '', {
          query: {
            diseaseCode: this.diseaseCode
          }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.leftList = returnData || [];
          this.leftListCopy = returnData || [];
          this.loading = false;
          if (this.leftIpt || this.leftSel) {
            this.getLeftList();
          }
        });
    },
    //右边
    getRightSearch() {
      console.log('[ this.rowInfo ]-308', this.rowInfo);
      this.$ajax
        .post(this.$apiUrls.Query_MapDiseaseDisease, '', {
          query: { diseaseCode: this.diseaseCode }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.rightList = returnData || [];
          this.rightListCopy = returnData || [];
          this.loading = false;
          if (this.rightIpt || this.rightSel) {
            this.getRightData();
          }
        });
    },
    // 筛选左边数据
    getLeftList() {
      if (!this.leftSel && !this.leftIpt) {
        return (this.leftList = this.leftListCopy);
      }
      var obj = {};
      obj = this.G_codeItemCls.find((item) => {
        return item.value === this.leftSel;
      });
      let newData = [];
      if (obj) {
        newData = this.leftListCopy.filter((item) => {
          return item.clsName.indexOf(obj.label) !== -1;
        });
      } else {
        newData = this.leftListCopy;
      }
      this.leftList = newData.filter((item) => {
        return (
          item.diseaseCode.indexOf(this.leftIpt) !== -1 ||
          item.diseaseName.indexOf(this.leftIpt) !== -1
        );
      });
    },
    //筛选右边表格数据
    getRightData() {
      if (!this.rightSel && !this.rightIpt) {
        return (this.rightList = this.rightListCopy);
      }
      var obj = {};
      obj = this.G_codeItemCls.find((item) => {
        return item.value === this.rightSel;
      });
      let newData = [];
      if (obj) {
        newData = this.rightListCopy.filter((item) => {
          return item.clsName.indexOf(obj.label) !== -1;
        });
      } else {
        newData = this.rightListCopy;
      }
      this.rightList = newData.filter((item) => {
        return (
          item.diseaseCode.indexOf(this.rightIpt) !== -1 ||
          item.diseaseName.indexOf(this.rightIpt) !== -1
        );
      });
    },
    //执行获取左右表格数据
    getAllData() {
      this.getLeftSearch();
      this.getRightSearch();
    },
    //添加
    addComb() {
      if (this.leftSelection.length < 1) {
        return this.$message({
          message: '请勾选至少一条左边表格数据再进行操作！',
          type: 'warning',
          showClose: true
        });
      }
      this.$ajax
        .post(
          this.$apiUrls.CD_MapDiseaseDisease + '/Create',
          this.leftSelection
        )
        .then((r) => {
          if (!r.data.success) {
            return;
          }
          this.getAllData();
          this.$message({
            showClose: true,
            message: '添加成功!',
            type: 'success'
          });
        });
    },
    //删除
    delComb() {
      if (this.rightSelection.length > 0) {
        this.$confirm('是否确认删除对应信息??', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.$ajax
              .post(
                this.$apiUrls.CD_MapDiseaseDisease + '/Delete',
                this.rightSelection
              )
              .then((r) => {
                let { success } = r.data;
                if (!success) return;
                this.$message({
                  showClose: true,
                  message: '删除成功',
                  type: 'success'
                });
                this.getAllData();
              });
          })
          .catch(() => {
            return;
          });
      } else {
        return this.$message({
          message: '请勾选至少一条数据再进行操作！',
          type: 'warning',
          showClose: true
        });
      }
    }
  }
};
</script>
<style lang="less" scoped>
.disToInfoDrawer {
  height: 100%;
  width: 100%;
  overflow: auto;
  border-radius: 4px;
  padding: 18px;
  display: flex;
  flex-direction: column;
  .btn {
    padding: 6px 9px;
  }
  .add-del {
    padding: 9px 9px;
  }
  .stepDiv {
    display: flex;
    padding: 15px;
    .el-steps {
      width: 50%;
    }
    .btnDiv {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      height: 42px;
      .el-button {
        line-height: 0;
      }
    }
  }
  .bodyDiv {
    flex: 1;
    flex-shrink: 0;
    width: 100%;
    height: 100%;
    display: flex;
  }
  .fullbodyDiv {
    flex: 1;
    flex-shrink: 0;
    width: 100%;
    height: calc(100% - 108px);
    display: flex;
  }
  .bodyDiv1 {
    height: calc(100% - 108px);
    // padding: 15px;
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .bodyDiv2 {
    height: calc(100% - 108px);
    flex: 1;
    flex-shrink: 0;
    width: 100%;
    // height: 100%;
    display: flex;
  }
  .leftBody {
    background-color: #fff;
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: auto;
    // padding: 15px;
    .btn {
      margin-left: 10px;
    }
  }
  .centerBody {
    width: 100px;
    height: 100%;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    .el-button + .el-button {
      margin-left: 0;
    }
  }
  .rightBody {
    background-color: #fff;
    // padding: 15px;
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    .combo_group {
      flex: 1;
    }
  }
  .head1 {
    display: flex;
    justify-content: space-between;
  }
  .record_head {
    li {
      display: flex;
      margin-bottom: 18px;
    }
    label {
      margin-right: 10px;
      width: auto;
      text-align: right;
      overflow: hidden; /*溢出的部分隐藏*/
      white-space: nowrap; /*文本不换行*/
      text-overflow: ellipsis;
    }
    .every_inp,
    .every_inp1 {
      display: flex;
      align-items: center;

      label {
        margin-right: 10px;
        width: auto;
        text-align: right;
        overflow: hidden; /*溢出的部分隐藏*/
        white-space: nowrap; /*文本不换行*/
        text-overflow: ellipsis;
      }

      p {
        flex: 1;
      }

      &:nth-child(4) {
        flex: 1;
      }
    }
    .every_inp {
      width: 290px;
    }
    .every_inp1 {
      flex: 1;
    }
    .div_inp {
      width: 75%;
      p {
        display: flex;
      }
    }
    .el-input {
      margin: 0 5px;
    }
  }
  .record_table {
    height: calc(100% - 50px);
    flex: 1;
    flex-shrink: 0;
    border-radius: 4px;
    border: 1px solid #dbe1e4;
    overflow: auto;
    .tableDiv {
      height: 100%;
    }
    .tableDiv1 {
      height: 100%;
    }
    .all_price {
      height: 32px;
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      line-height: 32px;
      background: rgba(214, 48, 49, 0.1);
      border-radius: 4;
      overflow: hidden;
      color: #d63031;
      font-weight: 600;
      padding: 0 15px;
    }
  }
  .rightCont2 {
    flex: 1;
    height: 100%;
    flex-shrink: 0;
    display: flex;
    margin-left: 20px;

    .symbol-box {
      margin-right: 20px;
      margin-top: 90px;
      margin-bottom: 30px;
      text-align: center;
      display: flex;
      flex-direction: column;
      // justify-content: space-around;
    }
    .check {
      width: 172px;
      height: 48px;
      line-height: 48px;
      background: #1770df;
      color: #fff;
      border-radius: 2px;
      margin-bottom: 28px;
      font-size: 24px;
      font-weight: normal;
    }
    .symbol {
      width: 174px;
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 48px;
      div {
        width: 86px;
        height: 48px;
        line-height: 48px;
        background: #1770df;
        color: #fff;
        border-radius: 2px;
        margin-right: 1px;
        margin-bottom: 1px;
        font-size: 28px;
        font-weight: normal;
      }
    }
    .clear {
      width: 172px;
      height: 48px;
      line-height: 48px;
      background: #fff;
      color: #1770df;
      border: 1px solid #1770df;
      border-radius: 2px;
      font-size: 24px;
      font-weight: normal;
    }
    .check:hover,
    .symbol div:hover,
    .clear:hover {
      background: #66b1ff;
      border-color: #66b1ff;
      color: #fff;
    }
    .right-input {
      flex: 1;
      header {
        height: 32px;
        line-height: 32px;
        margin-bottom: 18px;
        flex: 1;
        display: flex;
        justify-content: space-between;
        .lable {
          font-size: 16px;
          // margin-right: 10px;
        }
      }
    }
    .textarea-box {
      height: calc(100% - 50px);
      /deep/.el-textarea,
      /deep/.el-textarea__inner {
        height: 100%;
      }
    }
  }
  .mutex {
    width: 50%;
    margin: 0 auto;
    margin-top: 20px;
  }
}
</style>
