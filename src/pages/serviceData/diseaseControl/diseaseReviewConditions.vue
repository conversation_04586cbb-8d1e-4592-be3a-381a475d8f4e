<template>
  <!-- 疾病审核条件 -->
  <div class="diseaseReviewConditions">
    <div>
      <AllBtn
        :btnList="['查询', '新建', '删除']"
        :methodCreat="createClick"
        :methodSearch="selectChange"
        :methodDelete="deleteClick"
        ref="allBtn_Ref"
        showSelect
        :selectList="$parent.searchList1"
        @selectChange="selectChange"
      />
      <div>
        <el-form ref="form" :model="form" label-width="80px" class="form">
          <el-form-item label="疾病名称" class="diseaseName">
            <el-input
              v-model="form.diseaseName"
              size="small"
              disabled
              class="input"
            ></el-input>
          </el-form-item>
          <el-form-item label="逻辑值" class="logical-value">
            <el-select
              size="small"
              v-model="form.logicalValue"
              placeholder="请选择逻辑值"
              class="select"
              @change="logicalValueChange"
            >
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in logicalValueList"
                :key="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="conditions-table">
      <PublicTable
        :viewTableList.sync="tableData"
        :theads.sync="theads"
        :tableLoading.sync="loading"
        @rowDblclick="rowDblclick"
        :isSortShow="false"
        isCheck
        @selectionChange="handleSelectionChange"
      >
        <template #operator="{ scope }">
          {{ operatorList[scope.row.operator - 1].label }}
        </template>
        <template #valueType="{ scope }">
          {{ dataTypeList[scope.row.valueType - 1].label }}
        </template>
      </PublicTable>
    </div>
    <el-drawer
      title="疾病审核信息属性"
      :visible.sync="drawer"
      :before-close="cancel"
      size="25%"
      :wrapperClosable="false"
    >
      <el-form
        :model="popupForm"
        ref="ruleForm"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="疾病代码" prop="diseaseCode">
          <el-input
            v-model.trim="popupForm.diseaseCode"
            size="small"
            placeholder="请输入疾病代码"
            disabled
          ></el-input>
        </el-form-item>
        <el-form-item label="项目代码" prop="itemCode">
          <!-- <el-input
            v-model.trim="popupForm.itemCode"
            size="small"
            placeholder="请输入项目代码"
            :disabled="isEdit"
          ></el-input> -->
          <el-select
            size="small"
            v-model="popupForm.itemCode"
            placeholder="请选择项目代码"
            class="select"
            filterable
          >
            <el-option
              :label="'(' + item.itemCode + ')' + item.itemName"
              :value="item.itemCode"
              v-for="item in itemList"
              :key="item.itemCode"
            ></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="项目名称" prop="feeClsName">
          <el-input
            v-model.trim="popupForm.feeClsName"
            size="small"
            placeholder="请输入项目名称"
          ></el-input>
        </el-form-item> -->
        <el-form-item label="操作符" prop="operator">
          <el-select
            size="small"
            v-model="popupForm.operator"
            placeholder="请选择操作符"
            class="select"
          >
            <el-option
              :label="item.label"
              :value="item.value"
              v-for="item in operatorList"
              :key="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="数据类型" prop="valueType">
          <el-select
            size="small"
            v-model="popupForm.valueType"
            placeholder="请选择数据类型"
            class="select"
          >
            <el-option
              :label="item.label"
              :value="item.value"
              v-for="item in dataTypeList"
              :key="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="标准值" prop="value">
          <el-input
            v-model.trim="popupForm.value"
            size="small"
            placeholder="请输入标准值"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="cancel" size="small">取消</el-button>
        <el-button @click="saveClick" size="small" class="blue_btn"
          >保存</el-button
        >
      </div>
    </el-drawer>
  </div>
</template>

<script>
import AllBtn from '@/pages/serviceData/allBtn';
import PublicTable from '@/components/publicTable.vue';
import { dataUtils } from '@/common';
import XEUtils from 'xe-utils';
export default {
  name: 'diseaseReviewConditions',
  components: {
    AllBtn,
    PublicTable
  },
  data() {
    return {
      form: {
        diseaseName: '',
        logicalValue: ''
      },
      tableData: [],
      searchInput: '',
      theads: {
        diseaseCode: '疾病代码',
        itemCode: '项目代码',
        itemName: '项目名称',
        operator: '操作符',
        valueType: '数据类型',
        value: '标准值'
      },
      loading: false,
      drawer: false,
      popupForm: {},
      popupFormCopy: {},
      rules: {
        diseaseCode: [
          { required: true, message: '请输入疾病代码', trigger: 'blur' }
        ],
        itemCode: [
          { required: true, message: '请输入项目代码', trigger: 'blur' }
        ],
        operator: [
          { required: true, message: '请选择操作符', trigger: 'change' }
        ],
        valueType: [
          { required: true, message: '请选择数据类型', trigger: 'change' }
        ],
        value: [{ required: true, message: '请输入标准值', trigger: 'blur' }]
      },
      isEdit: false,
      selectRow: [],
      operatorList: [
        {
          label: '大于',
          value: 1
        },
        {
          label: '小于',
          value: 2
        },
        {
          label: '等于',
          value: 3
        },
        {
          label: '大于等于',
          value: 4
        },
        {
          label: '小于等于',
          value: 5
        },
        {
          label: '不等于',
          value: 6
        },
        {
          label: '包含',
          value: 7
        }
      ],
      dataTypeList: [
        {
          label: '数值型',
          value: 1
        },
        {
          label: '字符型',
          value: 2
        }
      ],
      logicalValueList: [
        {
          label: '与',
          value: 1
        },
        {
          label: '或',
          value: 2
        }
      ],
      logicValue: '',
      itemList: []
    };
  },
  created() {},
  mounted() {},
  methods: {
    // 获取项目下拉列表
    getItem() {
      this.$ajax.post(this.$apiUrls.Item).then((r) => {
        console.log('Item', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.itemList = returnData;
      });
    },
    // 逻辑值保存
    getCodeDiseaseCriteria() {
      this.$ajax
        .post(this.$apiUrls.ReadCodeDiseaseCriteria, '', {
          query: {
            diseaseCode: this.$parent.diseaseCode
          }
        })
        .then((r) => {
          console.log('ReadCodeDiseaseCriteria', r);
          let { success, returnData } = r.data;
          if (!success) return;
          if (returnData === null) {
            this.logicValue = '';
            return;
          }
          this.form.logicalValue = returnData.logic;
          this.logicValue = returnData.logic;
        });
    },
    // 获取表格数据
    getDiseaseCriteriaItem(diseaseCode) {
      this.$ajax
        .post(this.$apiUrls.ReadCodeDiseaseCriteriaItem, '', {
          query: {
            diseaseCode: diseaseCode
          }
        })
        .then((r) => {
          console.log('ReadCodeDiseaseCriteriaItem', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.tableData = returnData.criteriaItem || [];
          this.form.diseaseName = this.$parent.grandsonInfo.title;
        });
    },
    // 表格双击
    rowDblclick(row) {
      this.drawer = true;
      this.isEdit = true;
      this.$nextTick(() => {
        this.resetForm('ruleForm');
        this.popupForm = dataUtils.deepCopy(row);
        this.popupFormCopy = row;
      });
      this.getItem();
    },
    // 表格勾选
    handleSelectionChange(rows) {
      console.log('rows: ', rows);
      this.selectRow = rows;
    },
    // 逻辑值切换
    logicalValueChange(value) {
      let type = this.logicValue === '' ? '/Create' : '/Update';
      let data = {
        diseaseCode: this.$parent.diseaseCode,
        logic: this.form.logicalValue
      };
      this.$ajax
        .post(this.$apiUrls.CU_CodeDiseaseCriteria + type, data)
        .then((r) => {
          console.log('ReadCodeDiseaseCriteriaItem', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.getCodeDiseaseCriteria();
          this.$message({
            message: '逻辑值选择成功!',
            type: 'success',
            showClose: true
          });
        });
    },
    // 搜索
    async selectChange(val) {
      console.log('val: ', val);
      if (!val) return;
      this.$parent.muenId = val;
      await this.getDiseaseCriteriaItem(val);
      this.$parent.rightTitle = this.$parent.searchObj[val];
      const matchObj = XEUtils.findTree(
        this.$parent.menuList,
        (item) => item.id === 'two_ids' + val,
        { children: 'children' }
      );
      console.log(matchObj);
      this.$parent.treeExpand(matchObj.item);
      this.$parent.rightTitle =
        matchObj.nodes[0].title +
        ' | ' +
        matchObj.nodes[1].title +
        ' | ' +
        matchObj.nodes[2].title;
    },
    // 关闭抽屉
    cancel() {
      this.drawer = false;
    },
    // 新建请求
    createDiseaseCriteriaItem() {
      this.$ajax
        .post(this.$apiUrls.CreateCodeDiseaseCriteriaItem, this.popupForm)
        .then((r) => {
          console.log('CreateCodeDiseaseCriteriaItem', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.$message({
            message: '新建成功!',
            type: 'success',
            showClose: true
          });
          this.drawer = false;
          this.getDiseaseCriteriaItem(this.$parent.diseaseCode);
          this.getCodeDiseaseCriteria();
        });
    },
    // 修改请求
    UpdateDiseaseCriteriaItem() {
      let data = {
        oldCriteriaItem: this.popupFormCopy,
        newCriteriaItem: this.popupForm
      };
      this.$ajax
        .post(this.$apiUrls.UpdateCodeDiseaseCriteriaItem, data)
        .then((r) => {
          console.log('UpdateCodeDiseaseCriteriaItem', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.$message({
            message: '修改成功!',
            type: 'success',
            showClose: true
          });
          this.drawer = false;
          this.getDiseaseCriteriaItem(this.$parent.diseaseCode);
          this.getCodeDiseaseCriteria();
        });
    },
    // 保存
    saveClick() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (!this.isEdit) {
            this.createDiseaseCriteriaItem();
          } else {
            this.UpdateDiseaseCriteriaItem();
          }
        }
      });
    },
    // 新建
    createClick() {
      if (this.form.logicalValue === '') {
        this.$message({
          message: '请选择逻辑值!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.drawer = true;
      this.isEdit = false;
      this.$nextTick(() => {
        this.resetForm('ruleForm');
        this.popupForm = dataUtils.deepCopy(this.popupForm);
        this.popupForm.diseaseCode = this.$parent.diseaseCode;
      });
      this.getItem();
    },
    // 删除
    deleteClick() {
      if (this.selectRow.length === 0) {
        this.$message({
          message: '请至少选择一条要删除的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm(`是否确定要删除数据?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax
            .post(this.$apiUrls.DeleteCodeDiseaseCriteriaItem, this.selectRow)
            .then((r) => {
              console.log('DeleteCodeDiseaseCriteriaItem', r);
              let { success, returnData } = r.data;
              if (!success) return;
              this.$message({
                message: '删除成功!',
                type: 'success',
                showClose: true
              });
              this.getDiseaseCriteriaItem(this.$parent.diseaseCode);
            });
        })
        .catch(() => {
          return false;
        });
    },
    // 重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields();
    }
  }
};
</script>

<style lang="less" scoped>
.diseaseReviewConditions {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .form {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-right: 2px;
    /deep/.el-form-item__label {
      color: #2d3436;
      font-weight: 600;
    }
  }
  .diseaseName {
    flex: 1;
  }
  .logical-value {
    margin-right: 6px;
  }
  .select {
    width: 100%;
  }
  .input {
    width: 100%;
  }
  .conditions-table {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  /deep/.el-form-item {
    margin-bottom: 18px;
  }
  /deep/.el-drawer__header {
    background: #d1e2f9;
    margin-bottom: 0;
    padding: 18px;
  }
  /deep/.el-drawer__body {
    margin: 18px !important;
    padding: 0 !important;
  }
  /deep/.el-drawer__header,
  /deep/.el-form-item__label {
    color: #2d3436;
    font-weight: 600;
  }
}
</style>
