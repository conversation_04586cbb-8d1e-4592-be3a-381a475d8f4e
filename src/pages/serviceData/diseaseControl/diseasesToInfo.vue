<template>
  <!-- 疾病包含关系对应信息 -->
  <div class="diseasesToInfo">
    <el-container>
      <el-header>
        <AllBtn
          :btnList="['查询', '新建', '删除', '打印', '导出']"
          :methodCreat="add"
          :methodDelete="delMore"
          :methodSearch="search"
          :methodPrint="print"
          :methodExport="exportTable"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          ref="table_ref"
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          @rowDblclick="handleClick"
          :cell_blue="cell_blue"
          @selectionChange="handleSelectChangeTable"
          :tableLoading.sync="loading"
          isCheck
        >
          <template #columnRight>
            <el-table-column
              prop="operation"
              label="子疾病对应"
              width="110"
              fixed="right"
            >
              <template slot-scope="scope">
                <el-button
                  type="primary"
                  plain
                  size="mini"
                  class="view"
                  @click="viewDetails(scope.row)"
                  >对应详情</el-button
                >
              </template>
            </el-table-column>
          </template>
        </PublicTable>
      </el-main>
    </el-container>
    <el-drawer
      :title="title"
      :visible.sync="drawer"
      :before-close="cancel"
      size="70%"
      :wrapperClosable="false"
      v-if="drawer"
    >
      <DetailsDrawer
        ref="drawer_ref"
        :actives="actives"
        :isEdit="isEdit"
        :rowInfo="rowInfo"
      ></DetailsDrawer>
    </el-drawer>
    <!-- 打印的表格 -->
    <div class="print_table">
      <PublicTable
        id="printHtml"
        :viewTableList.sync="checkTableList"
        :theads.sync="theads"
        :cell_blue="cell_blue"
      >
      </PublicTable>
    </div>
  </div>
</template>

<script>
import PublicTable from '@/components/publicTable';
import AllBtn from '../allBtn.vue';
import DetailsDrawer from './disToInfoDrawer.vue';
import { export2Excel } from '@/common/excelUtil';
import moment from 'moment';
import printJS from '@/common/printJS';
export default {
  name: 'diseasesToInfo',
  components: { PublicTable, AllBtn, DetailsDrawer },
  data() {
    return {
      loading: false,
      theads: {
        diseaseCode: '父疾病代码',
        diseaseName: '父疾病名称',
        clsName: '项目分类'
      },
      cell_blue: ['diseaseName'],
      tableData: [],
      codeArr: [],
      drawer: false,
      actives: 2,
      isEdit: false,
      title: '新增疾病包含关系对应信息',
      excelList: [],
      checkTableList: [],
      rowInfo: {}
    };
  },
  created() {},
  mounted() {
    this.getSearch();
  },
  methods: {
    cancel() {
      this.drawer = false;
    },
    //查询
    search() {
      this.formInline = this.$refs.allBtn_Ref.searchInfo.trim(); //获取组件文本框的值
      if (this.formInline.length > 0) {
        this.tableData = this.tableDataCopy.filter((item) => {
          return (
            item.diseaseCode.indexOf(this.formInline) !== -1 ||
            item.diseaseName.indexOf(this.formInline) !== -1
          );
        });
      } else {
        this.getSearch();
      }
    },
    // 获取首页数据
    getSearch() {
      console.log(this.$apiUrls);
      this.loading = true;
      this.$ajax.post(this.$apiUrls.ReadMapDiseaseDisease, []).then((r) => {
        this.tableData = r.data.returnData;
        this.tableDataCopy = r.data.returnData;
        this.loading = false;
      });
    },
    //新增
    add() {
      this.drawer = true;
      this.actives = 1;
      this.isEdit = false;
      this.title = '新增疾病包含关系对应信息';
    },
    //对应详情
    viewDetails(row) {
      this.rowInfo = row;
      console.log('[ row ]-151', row);
      this.drawer = true;
      this.actives = 2;
      this.isEdit = true;
      this.title = '编辑疾病包含关系对应信息';
      this.$nextTick(() => {
        console.log('[ this ]-213', this);
        this.$refs.drawer_ref.getAllData();
      });
    },
    //编辑
    handleClick(row) {},
    //复选框勾选操作
    handleSelectChangeTable(val) {
      console.log('val: ', val);
      this.excelList = val;
      this.codeArr = [];
      val.map((item) => {
        if (item.diseaseCode != '') {
          this.codeArr.push(item.diseaseCode);
        }
      });
      console.log('this.codeArr: ', this.codeArr);
    },
    //多条删除
    delMore() {
      if (this.codeArr.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据!',
          type: 'warning'
        });
      }
      if (this.codeArr.length > 0) {
        this.$confirm('是否确认删除对应信息?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.$ajax
              .post(this.$apiUrls.RD_CodeFeeCls, this.codeArr)
              .then((r) => {
                console.log('r111: ', r);
                const { returnData, success } = r.data;
                if (!success) {
                  return;
                }
                this.$message.success('删除成功!');
              });
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    // 导出excel
    exportTable() {
      if (this.excelList.length == 0) {
        this.$message.warning('请选择至少一条数据进行操作!');
        return;
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const columns = [
          {
            title: '序号',
            key: 'index'
          },
          {
            title: '分类代码',
            key: 'diseaseCode'
          },
          {
            title: '分类名称',
            key: 'diseaseName'
          }
        ];
        this.excelList.map((item, i) => {
          item.index = i + 1;
        });
        const title = '疾病包含关系对应信息' + moment().format('YYYY-MM-DD');
        this.$nextTick(() => {
          export2Excel(columns, this.excelList, title);
          //this.$refs.multipleTable.clearSelection();
        });
      });
    },
    //打印
    print() {
      let tableCom_Ref = this.$refs.table_ref.$refs.tableCom_Ref;
      console.log('tableCom_Ref: ', tableCom_Ref.selection);
      if (tableCom_Ref.selection.length == 0) {
        return this.$message({
          message: '请至少选择一条数据进行打印！',
          type: 'warning',
          showClose: true
        });
      }
      let viewTableList_prop = this.$refs.table_ref._props.viewTableList;
      if (tableCom_Ref.store.states.isAllSelected) {
        this.checkTableList = viewTableList_prop;
      } else {
        this.checkTableList = tableCom_Ref.selection;
      }
      setTimeout(() => {
        printJS('printHtml');
      }, 500);
    }
  }
};
</script>
<style lang="less" scoped>
.diseasesToInfo {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-weight: 600;
  // padding: 0 10px;
  header {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-header,
  .el-main {
    padding: 0;
  }
  .searchWrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  // .search-btn {
  //   padding: 8px 25px;
  //   font-size: 14px;
  // }
  // /deep/.el-drawer__body {
  //   padding: 0 20px;
  //   .el-form-item {
  //     margin-bottom: 12px;
  //   }
  // }
  /deep/.el-container.is-vertical {
    height: 100%;
  }
  /deep/.el-drawer__header,
  /deep/.el-form-item__label {
    color: #2d3436;
  }
  /deep/.el-checkbox__inner {
    width: 20px;
    height: 20px;
  }
  /deep/.el-checkbox__inner::after {
    left: 7px;
    top: 3px;
  }
  /deep/.el-checkbox__inner::before {
    top: 8px;
  }
  .search-input {
    margin-right: 10px;
    // width: 322px;
  }
  .select {
    width: 100%;
  }
  .table-text {
    color: #1770df;
    // cursor: pointer;
  }
  .print_table {
    display: none;
  }
  /deep/.el-drawer__header {
    align-items: center;
    display: flex;
    background: rgba(23, 112, 223, 0.2);
    border-radius: 4px 4px 0 0;
    line-height: 42px;
    font-family: PingFangSC-Medium;
    font-size: 18px;
    padding: 0;
    padding-left: 20px;
    color: #2d3436;
    margin-bottom: 0;
  }
  /deep/.el-drawer__close-btn {
    font-size: 30px;
  }
}
</style>
