<template>
  <!-- 疾病分类与疾病对应信息 -->
  <div class="diseasesTypeToInfo">
    <el-container>
      <el-header>
        <AllBtn
          :btnList="['查询', '新建', '删除', '打印', '导出']"
          :methodCreat="add"
          :methodDelete="delMore"
          :methodSearch="search"
          :methodPrint="print"
          :methodExport="exportTable"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          ref="table_ref"
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          @rowDblclick="handleClick"
          :cell_blue="cell_blue"
          @selectionChange="handleSelectChangeTable"
          :tableLoading.sync="loading"
          isCheck
        >
          <template #columnRight>
            <el-table-column
              prop="operation"
              label="操作"
              width="110"
              fixed="right"
            >
              <template slot-scope="scope">
                <!-- <el-button
                  type="primary"
                  plain
                  size="mini"
                  class="view"
                  @click="viewDetails(scope.row)"
                  >对应详情</el-button
                > -->
                <el-button
                  type="primary"
                  plain
                  size="mini"
                  class="view"
                  @click="viewDetails(scope.row)"
                  >维护对应</el-button
                >
              </template>
            </el-table-column>
          </template>
        </PublicTable>
      </el-main>
    </el-container>
    <el-drawer
      :title.sync="title"
      :visible.sync="drawer"
      :before-close="cancel"
      size="90%"
      :wrapperClosable="false"
      v-if="drawer"
    >
      <el-tabs v-model="detailTabsActive" class="detail_tabs">
        <el-tab-pane label="疾病对应详情" name="disease">
          <DisTypeToInfoDrawer
            ref="drawer_ref"
            :drawerInfo="drawerInfo"
          ></DisTypeToInfoDrawer>
        </el-tab-pane>
        <el-tab-pane label="组合对应详情" name="combos">
          <CombosDetail
            ref="CombosDetail_ref"
            :drawerInfo="drawerInfo"
          ></CombosDetail>
        </el-tab-pane>
      </el-tabs>
    </el-drawer>
    <el-drawer
      :title.sync="title"
      :visible.sync="drawers"
      :before-close="cancels"
      size="30%"
      :wrapperClosable="false"
    >
      <el-form
        :model="popupForm"
        ref="ruleForm"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="分类代码" prop="diseaseClsCode" v-if="disabled">
          <el-input
            v-model.trim="popupForm.diseaseClsCode"
            autocomplete="off"
            size="small"
            placeholder="请输入代码"
            :disabled="disabled"
          ></el-input>
        </el-form-item>
        <el-form-item label="分类名称" prop="diseaseClsName">
          <el-input
            v-model.trim="popupForm.diseaseClsName"
            autocomplete="off"
            size="small"
            placeholder="请输入名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="疾病建议" prop="suggestion">
          <el-input
            v-model.trim="popupForm.suggestion"
            autocomplete="off"
            :autosize="{ minRows: 2 }"
            size="small"
            placeholder="请输入疾病建议"
            type="textarea"
          ></el-input>
        </el-form-item>
        <el-form-item label="疾病科普" prop="popularScience">
          <el-input
            v-model.trim="popupForm.popularScience"
            autocomplete="off"
            :autosize="{ minRows: 2 }"
            size="small"
            placeholder="请输入疾病科普"
            type="textarea"
          ></el-input>
        </el-form-item>
        <el-form-item label="参与异常分析" prop="isEnableTop">
          <el-radio-group v-model="popupForm.isEnableTop" size="small">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="cancels" size="small">取消</el-button>
        <el-button class="blue_btn" size="small" @click="submit"
          >保存</el-button
        >
      </div>
    </el-drawer>
    <!-- 打印的表格 -->
    <div class="print_table">
      <PublicTable
        id="printHtml"
        :viewTableList.sync="checkTableList"
        :theads.sync="theads"
      >
      </PublicTable>
    </div>
  </div>
</template>

<script>
import PublicTable from '@/components/publicTable';
import AllBtn from '../allBtn.vue';
import DisTypeToInfoDrawer from './components/disTypeToInfoDrawer.vue';
import { export2Excel } from '@/common/excelUtil';
import moment from 'moment';
import CombosDetail from './components/combosDetail.vue';
import printJS from '@/common/printJS';
import { dataUtils } from '../../../common';
export default {
  name: 'diseasesTypeToInfo',
  components: { PublicTable, AllBtn, DisTypeToInfoDrawer, CombosDetail },
  data() {
    return {
      loading: false,
      theads: {
        diseaseClsCode: '分类代码',
        diseaseClsName: '分类名称'
      },
      cell_blue: ['diseaseClsName'],
      tableData: [],
      tableDataCopy: [],
      codeArr: [],
      drawer: false,
      drawers: false,
      drawerInfo: {},
      title: '',
      popupForm: {
        diseaseClsCode: '',
        diseaseClsName: '',
        suggestion: '',
        popularScience: '',
        isEnableTop: true
      },
      rules: {
        diseaseClsCode: [
          { required: true, message: '请输入代码', trigger: 'blur' }
        ],
        diseaseClsName: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ]
      },
      formInline: '',
      disabled: false,
      funTpye: '/Create',
      excelList: [],
      checkTableList: [],
      detailTabsActive: 'disease'
    };
  },
  created() {},
  mounted() {
    this.getSearch();
  },
  methods: {
    cancel() {
      this.detailDrawerClose();
      this.drawer = false;
    },
    cancels() {
      this.drawers = false;
    },
    //查询
    search() {
      this.formInline = this.$refs.allBtn_Ref.searchInfo.trim(); //获取组件文本框的值
      if (this.formInline.length > 0) {
        this.tableData = this.tableDataCopy.filter((item) => {
          return (
            item.diseaseCode.indexOf(this.formInline) !== -1 ||
            item.diseaseName.indexOf(this.formInline) !== -1
          );
        });
      } else {
        this.getSearch();
      }
    },
    // 获取首页数据
    getSearch() {
      console.log(this.$apiUrls);
      this.loading = true;
      this.$ajax
        .post(this.$apiUrls.RD_CodeDiseaseCls + '/Read', [])
        .then((r) => {
          this.tableData = r.data.returnData;
          this.tableDataCopy = r.data.returnData;
          this.loading = false;
        });
    },
    //新增
    add() {
      this.drawers = true;
      this.funTpye = '/Create';
      this.disabled = false;
      this.$nextTick(() => {
        this.$refs.ruleForm.resetFields();
      });
    },
    //对应详情
    viewDetails(row) {
      console.log('[ row ]-207', row);
      this.drawer = true;
      this.drawerInfo = row;
      this.title = row.diseaseClsName + '对应详情';
      console.log('[ this ]-212', this);
      this.$nextTick(() => {
        console.log('[ this ]-213', this);
        this.$refs.drawer_ref.getAllData();
        this.$refs.CombosDetail_ref.getAllData();
      });
    },
    //编辑
    handleClick(row) {
      console.log('row', row);
      this.drawers = true;
      this.disabled = true;
      this.funTpye = '/Update';
      this.$nextTick(() => {
        this.$refs.ruleForm.resetFields();
        this.popupForm = dataUtils.deepCopy(row);
      });
    },
    //提交
    submit() {
      console.log(this.popupForm);
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$ajax
            .post(
              this.$apiUrls.CU_CodeDiseaseCls + this.funTpye,
              this.popupForm
            )
            .then((r) => {
              r = r.data;
              if (!r.success) {
                return;
              }
              this.tableData = [];
              this.drawers = false;
              this.$nextTick(() => {
                this.$refs.allBtn_Ref.searchInfo = '';
                this.search();
              });
              if (this.funTpye == '/Create') {
                this.$message({
                  message: '新建成功',
                  type: 'success',
                  showClose: true
                });
              } else {
                this.$message({
                  message: '修改成功',
                  type: 'success',
                  showClose: true
                });
              }
            });
        } else {
          return false;
        }
      });
    },
    //复选框勾选操作
    handleSelectChangeTable(val) {
      console.log('val: ', val);
      this.excelList = val;
      this.codeArr = [];
      val.map((item) => {
        if (item.diseaseCode != '') {
          this.codeArr.push(item.diseaseClsCode);
        }
      });
      console.log('this.codeArr: ', this.codeArr);
    },
    //多条删除
    delMore() {
      if (this.codeArr.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据!',
          type: 'warning'
        });
      }
      if (this.codeArr.length > 0) {
        this.$confirm('是否确认删除对应信息?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.$ajax
              .post(this.$apiUrls.RD_CodeDiseaseCls + '/Delete', this.codeArr)
              .then((r) => {
                console.log('r111: ', r);
                const { returnData, success } = r.data;
                if (!success) {
                  return;
                }
                this.$message.success('删除成功!');
                this.search();
              });
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    // 导出excel
    exportTable() {
      if (this.excelList.length == 0) {
        this.$message.warning('请选择至少一条数据进行操作!');
        return;
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const columns = [
          {
            title: '序号',
            key: 'index'
          },
          {
            title: '分类代码',
            key: 'diseaseClsCode'
          },
          {
            title: '分类名称',
            key: 'diseaseClsName'
          }
        ];
        this.excelList.map((item, i) => {
          item.index = i + 1;
        });
        const title = '疾病分类与疾病对应信息' + moment().format('YYYY-MM-DD');
        this.$nextTick(() => {
          export2Excel(columns, this.excelList, title);
          //this.$refs.multipleTable.clearSelection();
        });
      });
    },
    //打印
    print() {
      let tableCom_Ref = this.$refs.table_ref.$refs.tableCom_Ref;
      console.log('tableCom_Ref: ', tableCom_Ref.selection);
      if (tableCom_Ref.selection.length == 0) {
        return this.$message({
          message: '请至少选择一条数据进行打印！',
          type: 'warning',
          showClose: true
        });
      }
      let viewTableList_prop = this.$refs.table_ref._props.viewTableList;
      if (tableCom_Ref.store.states.isAllSelected) {
        this.checkTableList = viewTableList_prop;
      } else {
        this.checkTableList = tableCom_Ref.selection;
      }
      setTimeout(() => {
        printJS('printHtml');
      }, 500);
    },
    // 打开维护对应抽屉的回调
    detailDrawerClose() {
      console.log('disease');

      this.$set(this, 'detailTabsActive', 'disease');
    }
  }
};
</script>
<style lang="less" scoped>
.diseasesTypeToInfo {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-weight: 600;
  // padding: 0 10px;
  header {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-header,
  .el-main {
    padding: 0;
  }
  .searchWrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  // .search-btn {
  //   padding: 8px 25px;
  //   font-size: 14px;
  // }
  // /deep/.el-drawer__body {
  //   padding: 0 20px;
  //   .el-form-item {
  //     margin-bottom: 12px;
  //   }
  // }
  /deep/.el-container.is-vertical {
    height: 100%;
  }
  /deep/.el-drawer__header,
  /deep/.el-form-item__label {
    color: #2d3436;
  }
  /deep/.el-checkbox__inner {
    width: 20px;
    height: 20px;
  }
  /deep/.el-checkbox__inner::after {
    left: 7px;
    top: 3px;
  }
  /deep/.el-checkbox__inner::before {
    top: 8px;
  }
  .search-input {
    margin-right: 10px;
    // width: 322px;
  }
  .select {
    width: 100%;
  }
  .table-text {
    color: #1770df;
    // cursor: pointer;
  }
  .print_table {
    display: none;
  }
  /deep/.el-drawer__header {
    align-items: center;
    display: flex;
    background: rgba(23, 112, 223, 0.2);
    border-radius: 4px 4px 0 0;
    line-height: 42px;
    font-family: PingFangSC-Medium;
    font-size: 18px;
    padding: 0;
    padding-left: 20px;
    color: #2d3436;
    margin-bottom: 0;
  }
  /deep/.el-drawer__close-btn {
    font-size: 30px;
  }
  /deep/.el-drawer__body {
    padding: 0 5px 5px;
  }
  .detail_tabs {
    display: flex;
    flex-direction: column;
    height: 100%;
    /deep/.el-tabs__header {
      margin-bottom: 5px;
    }
    /deep/.el-tabs__content {
      flex: 1 0 0;
      overflow: auto;
    }

    /deep/.el-tab-pane {
      height: 100%;
      overflow: auto;
    }
  }
}
</style>
