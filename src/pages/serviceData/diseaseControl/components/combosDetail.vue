<template>
  <div class="combosDetail">
    <div class="fullbodyDiv">
      <div class="leftBody">
        <ul class="record_head">
          <li>
            <div class="every_inp">
              <label> 组合列表 </label>
              <p>
                <el-select
                  v-model.trim="leftSel"
                  placeholder="请选择"
                  size="small"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="item in G_codeItemCls"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </p>
            </div>
            <div class="every_inp1">
              <p>
                <el-input
                  v-model.trim="leftIpt"
                  size="small"
                  placeholder="代码/名称"
                  clearable
                ></el-input>
              </p>
            </div>
            <el-button
              size="small"
              class="blue_btn btn"
              icon="iconfont icon-search"
              >搜索</el-button
            >
          </li>
        </ul>
        <div class="record_table">
          <PublicTable
            :viewTableList="C_leftList"
            :theads="leftTheads"
            isCheck
            :isSortShow="false"
            @selectionChange="selectionChange"
            :columnWidth="columnWidth"
            v-model="leftSelection"
            ref="leftTable"
          >
            <template #count="{ scope }">
              <el-popover
                placement="right-start"
                trigger="hover"
                popper-class="detail_popover"
                @hide="onPopoverHide"
              >
                <el-table :data="scope.row.diseases" height="300px">
                  <el-table-column
                    width="80"
                    property="diseaseCode"
                    label="疾病代码"
                  ></el-table-column>
                  <el-table-column
                    property="diseaseTag"
                    label="疾病名称"
                  ></el-table-column>
                  <el-table-column
                    property="count"
                    label="建议频次"
                  ></el-table-column>
                </el-table>
                <span slot="reference" style="color: #1770df">{{
                  scope.row.count
                }}</span>
              </el-popover>
            </template>
          </PublicTable>
        </div>
      </div>
      <div class="centerBody">
        <el-button
          size="small"
          class="blue_btn add-del"
          style="margin-bottom: 40px"
          @click="addComb"
          >添加
          <i class="iconfont icon-Rightxiangyou34"></i>
        </el-button>
        <el-button
          size="small"
          class="red_btn add-del"
          @click="delComb"
          icon="iconfont icon-Leftxiangzuo35"
        >
          删除</el-button
        >
      </div>
      <div class="rightBody">
        <ul class="record_head">
          <li>
            <div class="every_inp">
              <label :title="title2">{{ title2 }}</label>
            </div>
            <p class="p">
              <el-input
                v-model.trim="rightIpt"
                placeholder="代码/名称"
                size="small"
                clearable
              >
              </el-input>
              <el-button
                size="small"
                class="blue_btn btn"
                icon="iconfont icon-search"
                >搜索</el-button
              >
            </p>
          </li>
        </ul>
        <div class="record_table">
          <div class="tableDiv">
            <PublicTable
              :viewTableList="C_rightData"
              :theads="rightTheads"
              isCheck
              :isSortShow="false"
              :highlightCurrentRow="false"
              @selectionChange="rightSelectionChange"
              v-model="rightSelection"
              :columnWidth="columnWidth"
              ref="rightTable"
            >
            </PublicTable>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import PublicTable from '@/components/publicTable.vue';
import { mapGetters } from 'vuex';
export default {
  name: 'combosDetail',
  components: {
    PublicTable
  },
  props: {
    drawerInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      leftIpt: '',
      leftSel: '',
      rightIpt: '',
      loading: false,
      leftList: [],
      rightData: [],
      rightDataCopy: [],
      leftSelection: [],
      rightSelection: [],
      columnWidth: {
        combCode: 80,
        clsName: 80,
        count: 80
      },
      leftTheads: {
        combCode: '组合代码',
        combName: '组合名称',
        clsName: '组合类型',
        count: '建议频次'
      },
      rightTheads: {
        combCode: '组合代码',
        combName: '组合名称',
        clsName: '组合类型'
      },
      title2: '',
      diseaseClsCode: this.drawerInfo.diseaseClsCode
    };
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_codeItemCls']),
    C_leftList() {
      let checkList = this.rightData.map((item) => item.combCode);
      let leftInp = this.leftIpt.toLowerCase();
      return this.leftList.filter((item) => {
        let searchFlag = this.leftIpt
          ? item.combName.toLowerCase().includes(leftInp) ||
            item.combCode.toLowerCase().includes(leftInp)
          : true;
        return (
          !checkList.includes(item.combCode) &&
          (this.leftSel ? item.clsCode == this.leftSel : true) &&
          searchFlag
        );
      });
    },
    C_rightData() {
      let rightIpt = this.rightIpt.toLowerCase();
      return this.rightDataCopy.filter((item) => {
        let searchFlag = this.rightIpt
          ? item.combName.toLowerCase().includes(rightIpt) ||
            item.combCode.toLowerCase().includes(rightIpt)
          : true;
        return searchFlag;
      });
    }
  },
  created() {},
  mounted() {
    this.title2 = this.drawerInfo.diseaseClsName + '-组合对应列表';
    this.diseaseClsCode = this.drawerInfo.diseaseClsCode;
  },
  methods: {
    // 获取所有的组合列表
    getLeftSearch() {
      let datas = {
        keyword: '',
        keywordOfDise: this.drawerInfo.diseaseClsName
      };
      this.$ajax.post(this.$apiUrls.GetCombWithDiseCounts, datas).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.leftList = returnData || [];
        this.loading = false;
      });
    },
    // 获取已对应的组合列表
    getRightSearch() {
      console.log('[ this.drawerInfo ]-201', this.drawerInfo);
      let datas = {
        keyword: '',
        diseaseClsCode: this.drawerInfo.diseaseClsCode
      };
      this.$ajax.post(this.$apiUrls.GetMapDiseaseClsCombs, datas).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.rightData = returnData || [];
        this.rightDataCopy = returnData || [];
        this.loading = false;
      });
    },
    // 左边表格单选
    leftHandleClick(row) {},
    // 左边表格多选
    selectionChange(val, checkList) {
      console.log('val: ', val);
      this.leftSelection = checkList;
    },
    // 右边表格多选
    rightSelectionChange(val, checkList) {
      this.rightSelection = checkList;
      console.log('this.rightSelection: ', this.rightSelection);
    },

    //获取右边表格数据
    getRightData() {
      if (!this.rightIpt) {
        return (this.rightData = this.rightDataCopy);
      } else {
        this.rightData = this.rightDataCopy.filter((item) => {
          return (
            item.diseaseCode.indexOf(this.rightIpt) !== -1 ||
            item.diseaseName.indexOf(this.rightIpt) !== -1
          );
        });
      }
    },
    getAllData() {
      this.getLeftSearch();
      this.getRightSearch();
    },
    //添加
    addComb() {
      if (this.leftSelection.length < 1) {
        return this.$message({
          message: '请勾选至少一条左边表格数据再进行操作！',
          type: 'warning',
          showClose: true
        });
      }
      console.log(this.leftSelection);
      let leftSelection = [...this.leftSelection, ...this.rightData].map(
        (item, idx) => {
          return {
            diseaseClsCode: this.drawerInfo.diseaseClsCode,
            diseaseClsName: this.drawerInfo.diseaseClsName,
            combCode: item.combCode,
            combName: item.combName,
            sortIndex: idx
          };
        }
      );
      console.log(leftSelection);

      this.$ajax
        .post(this.$apiUrls.BatchSaveMapDiseaseClsComb, leftSelection)
        .then((r) => {
          if (!r.data.success) {
            return;
          }
          this.getAllData();
          this.leftSelection = [];
          this.$message({
            showClose: true,
            message: '添加成功!',
            type: 'success'
          });
        });
    },
    //删除
    delComb() {
      if (this.rightSelection.length > 0) {
        this.$confirm('是否确认删除对应信息??', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            let rightSelection = this.rightSelection.map(
              (item) => item.combCode
            );
            this.$ajax
              .post(
                `${this.$apiUrls.BatchRemoveMapDiseaseClsComb}/${this.drawerInfo.diseaseClsCode}`,
                rightSelection
              )
              .then((r) => {
                let { success } = r.data;
                if (!success) return;
                this.$message({
                  showClose: true,
                  message: '删除成功',
                  type: 'success'
                });
                this.getAllData();
              });
          })
          .catch(() => {
            return;
          });
      } else {
        return this.$message({
          message: '请勾选至少一条数据再进行操作！',
          type: 'warning',
          showClose: true
        });
      }
    },
    // 频次明细弹窗隐藏的回调
    onPopoverHide() {
      let node = document.querySelector('body>.detail_popover'); //隐藏时删除
      node?.remove();
    }
  }
};
</script>
<style lang="less" scoped>
.combosDetail {
  height: 100%;
  width: 100%;
  overflow: auto;
  border-radius: 4px;
  // padding: 18px;
  display: flex;
  flex-direction: column;
  .btn {
    padding: 6px 9px;
  }
  .add-del {
    padding: 9px 9px;
  }
  .stepDiv {
    display: flex;
    padding: 15px;
    .el-steps {
      width: 50%;
    }
    .btnDiv {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      height: 42px;
      .el-button {
        line-height: 0;
      }
    }
  }
  .bodyDiv {
    flex: 1;
    flex-shrink: 0;
    width: 100%;
    height: 100%;
    display: flex;
  }
  .fullbodyDiv {
    flex: 1;
    flex-shrink: 0;
    width: 100%;
    height: calc(100% - 108px);
    display: flex;
  }
  .bodyDiv1 {
    height: calc(100% - 108px);
    // padding: 15px;
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .bodyDiv2 {
    height: calc(100% - 108px);
    flex: 1;
    flex-shrink: 0;
    width: 100%;
    // height: 100%;
    display: flex;
  }
  .leftBody {
    background-color: #fff;
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: auto;
    // padding: 15px;
  }
  .centerBody {
    width: 100px;
    height: 100%;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    .el-button + .el-button {
      margin-left: 0;
    }
  }
  .rightBody {
    background-color: #fff;
    // padding: 15px;
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    .combo_group {
      flex: 1;
    }
  }
  .head1 {
    display: flex;
    justify-content: space-between;
  }
  .record_head {
    li {
      display: flex;
      margin-bottom: 18px;
    }

    .every_inp,
    .every_inp1 {
      display: flex;
      align-items: center;
      margin-right: 10px;

      label {
        margin-right: 10px;
        width: auto;
        text-align: right;
        overflow: hidden; /*溢出的部分隐藏*/
        white-space: nowrap; /*文本不换行*/
        text-overflow: ellipsis;
      }

      p {
        flex: 1;
      }

      &:nth-child(4) {
        flex: 1;
      }
    }
    .every_inp {
      width: 250px;
    }
    .every_inp1 {
      flex: 1;
    }
  }
  .p {
    flex: 1;
    display: flex;
    .el-input {
      margin: 0 5px;
    }
  }
  .record_table {
    height: calc(100% - 50px);
    flex: 1;
    flex-shrink: 0;
    border-radius: 4px;
    border: 1px solid #dbe1e4;
    overflow: auto;
    .tableDiv {
      height: 100%;
    }
    .tableDiv1 {
      height: 100%;
    }
    .all_price {
      height: 32px;
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      line-height: 32px;
      background: rgba(214, 48, 49, 0.1);
      border-radius: 4;
      overflow: hidden;
      color: #d63031;
      font-weight: 600;
      padding: 0 15px;
    }
  }
  .rightCont2 {
    flex: 1;
    height: 100%;
    flex-shrink: 0;
    display: flex;
    margin-left: 20px;

    .symbol-box {
      margin-right: 20px;
      margin-top: 90px;
      margin-bottom: 30px;
      text-align: center;
      display: flex;
      flex-direction: column;
      // justify-content: space-around;
    }
    .check {
      width: 172px;
      height: 48px;
      line-height: 48px;
      background: #1770df;
      color: #fff;
      border-radius: 2px;
      margin-bottom: 28px;
      font-size: 24px;
      font-weight: normal;
    }
    .symbol {
      width: 174px;
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 48px;
      div {
        width: 86px;
        height: 48px;
        line-height: 48px;
        background: #1770df;
        color: #fff;
        border-radius: 2px;
        margin-right: 1px;
        margin-bottom: 1px;
        font-size: 28px;
        font-weight: normal;
      }
    }
    .clear {
      width: 172px;
      height: 48px;
      line-height: 48px;
      background: #fff;
      color: #1770df;
      border: 1px solid #1770df;
      border-radius: 2px;
      font-size: 24px;
      font-weight: normal;
    }
    .check:hover,
    .symbol div:hover,
    .clear:hover {
      background: #66b1ff;
      border-color: #66b1ff;
      color: #fff;
    }
    .right-input {
      flex: 1;
      header {
        height: 32px;
        line-height: 32px;
        margin-bottom: 18px;
        flex: 1;
        display: flex;
        justify-content: space-between;
        .lable {
          font-size: 16px;
          // margin-right: 10px;
        }
      }
    }
    .textarea-box {
      height: calc(100% - 50px);
      /deep/.el-textarea,
      /deep/.el-textarea__inner {
        height: 100%;
      }
    }
  }
  .mutex {
    width: 50%;
    margin: 0 auto;
    margin-top: 20px;
  }
}
</style>
