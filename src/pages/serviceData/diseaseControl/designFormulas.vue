<template>
  <div class="designFormulas_page">
    <!-- <h3>疾病判断计算式｜计算公式｜舒张压</h3> -->
    <AllBtn
      :btnList="['查询', '新建']"
      :methodCreat="createClick"
      ref="allBtn_Ref"
      :selectList="$parent.illnessList"
      showSelect
      @selectChange="selectChange"
    >
      <template #footAdd>
        <el-button
          :disabled="!isCheck"
          size="small"
          class="blue_btn btn"
          @click="createOrModify"
          icon="iconfont icon-baocun"
          >保存</el-button
        >
        <el-button
          class="btn red_btn"
          icon="iconfont icon-shanchu"
          size="small"
          @click="deleteClick"
          >删除</el-button
        >
        <el-button
          class="btn yellow_btn"
          icon="iconfont icon-component"
          size="small"
          @click="checkClick"
          >校验</el-button
        >
        <el-button
          class="btn green_btn"
          icon="iconfont icon-qingkonghuancun"
          size="small"
          >清空</el-button
        >
      </template>
    </AllBtn>
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="80px"
      class="demo-ruleForm"
    >
      <div class="form_head">
        <el-form-item label="公式代码" prop="expCode">
          <el-input
            :disabled="isCreateFlag == '/Update'"
            v-model="ruleForm.expCode"
            size="small"
          ></el-input>
        </el-form-item>
        <el-form-item label="公式名称" prop="expName">
          <el-input v-model="ruleForm.expName" size="small"></el-input>
        </el-form-item>
        <!-- <el-form-item label="启用标识" prop="name">
                    <el-input v-model="ruleForm.name" size="small"></el-input>
                </el-form-item> -->
      </div>
      <el-form-item label="项目列表(，隔开)" prop="itemText">
        <el-input
          type="textarea"
          :autosize="{ minRows: 3, maxRows: 6 }"
          placeholder="请输入内容"
          v-model="ruleForm.itemText"
          readonly
          @change="itemTextChange"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="公式内容" prop="expText">
        <el-input
          type="textarea"
          :autosize="{ minRows: 4, maxRows: 8 }"
          placeholder="请输入内容"
          v-model="ruleForm.expText"
        >
        </el-input>
      </el-form-item>
      <div class="form_head">
        <el-form-item style="flex: 1" label="对应疾病" prop="diseaseCode">
          <!--style="flex-basis: 280px;"-->
          <el-select
            size="small"
            clearable
            v-model="ruleForm.diseaseCode"
            filterable
            remote
            placeholder="请选择"
            :remote-method="remoteMethod"
          >
            <el-option
              v-for="item in diseaseList"
              :key="item.diseaseCode"
              :label="item.diseaseName"
              :value="item.diseaseCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item style="flex:1;" label="公式名称" prop="name">
                    <el-input v-model="ruleForm.name" size="small"></el-input>
                </el-form-item> -->
      </div>
    </el-form>
    <div class="list_wrap">
      <div class="left_list">
        <div class="search_list">
          <span>运算符及函数列表</span>
          <el-input
            style="flex: 1"
            v-model="funcSearchVal"
            size="small"
            @input="bottomLeftSearch"
          ></el-input>
        </div>
        <div class="list_div">
          <!-- 运算符 -->
          <div class="table_wrap" style="width: 228px; margin-right: 10px">
            <PublicTable
              :isSortShow="false"
              :viewTableList.sync="operatorList"
              :theads.sync="operatorTheads"
              @rowClick="operatorRowClick"
            />
          </div>
          <!-- 函数 -->
          <div class="table_wrap" style="flex: 1; flex-shrink: 0">
            <PublicTable
              :isSortShow="false"
              :viewTableList.sync="functionList"
              :theads.sync="functionTheads"
              @rowClick="funcRowClick"
            />
          </div>
        </div>
      </div>
      <div class="right_list">
        <div class="search_list">
          <span>项目列表</span>
          <el-input
            style="flex: 1"
            v-model.trim="projectSearchVal"
            clearable
            @input="projectSearchEnter"
            size="small"
          ></el-input>
        </div>
        <div class="list_div">
          <!-- 项目列表 -->
          <div class="table_wrap" style="flex: 1; flex-shrink: 0">
            <PublicTable
              :isSortShow="false"
              :viewTableList.sync="projectList"
              :theads.sync="projectTheads"
              @rowClick="projectRowClick"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import AllBtn from '../allBtn';
import PublicTable from '@/components/publicTable';
import XEUtils from 'xe-utils';

export default {
  name: 'designFormulas',
  components: {
    AllBtn,
    PublicTable
  },
  data() {
    return {
      isCreateFlag: '/Create',
      ruleForm: {
        expCode: '',
        expName: '',
        expText: '',
        diseaseCode: '',
        itemText: '',
        items: []
      },
      rules: {
        expCode: [
          { required: true, message: '请输入公式代码', trigger: 'blur' }
        ],
        expName: [
          { required: true, message: '请输入公式名称', trigger: 'blur' }
        ],
        expText: [
          { required: true, message: '请输入公式内容', trigger: 'blur' }
        ],
        diseaseCode: [
          { required: true, message: '请选择对应疾病', trigger: 'change' }
        ],
        itemText: [
          { required: true, message: '请输入项目列表内容', trigger: 'blur' }
        ]
      },
      // 运算符
      fixed_operatorList: [],
      operatorList: [],
      operatorTheads: {
        code: '运算符',
        name: '描述'
      },
      // 函数
      fixed_functionList: [],
      functionList: [],
      functionTheads: {
        name: '函数中文',
        code: '函数英文'
      },
      // 项目
      fixed_projectList: [],
      projectList: [],
      projectTheads: {
        itemCode: '项目代码',
        itemName: '项目名称'
      },
      funcSearchVal: '',
      projectSearchVal: '',
      // 疾病下拉列表
      diseaseList: [],
      fixed_diseaseList: [],
      diseaseListObj: {},
      isCheck: false,
      lastTime: 0
    };
  },
  methods: {
    // 获取运算符和函数列表
    getFunList() {
      this.$ajax.post(this.$apiUrls.OperationAndFunction).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.operatorList = returnData.operList || [];
        this.functionList =
          returnData.funcList.map((item) => {
            return {
              code: item.code || item.methodDesc,
              name: item.name || item.methodName
            };
          }) || [];
        this.fixed_operatorList = JSON.parse(JSON.stringify(this.operatorList));
        this.fixed_functionList = JSON.parse(JSON.stringify(this.functionList));
      });
    },
    // 获取项目
    getItemList() {
      this.$ajax.post(this.$apiUrls.Item).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.fixed_projectList = returnData || [];
        this.projectList = JSON.parse(JSON.stringify(returnData)) || [];
      });
    },
    // 新增或修改疾病判断计算公式
    createOrModify() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          console.log(this.ruleForm);
          this.$ajax
            .post(
              this.$apiUrls.CU_CodeDiseaseExpression + this.isCreateFlag,
              this.ruleForm
            )
            .then((r) => {
              let { success, returnData } = r.data;
              if (!success) return;
              this.$message({
                message: '保存成功！',
                type: 'success',
                showClose: true
              });
              this.$parent.getOperatorList().then((r) => {
                this.$parent.refreshTree();
              });
              if (this.isCreateFlag == '/Create') {
                this.resetForm();
              }

              this.ruleForm.items = [];
            });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    // 重置表单
    resetForm(formName) {
      this.$refs.ruleForm.resetFields();
    },
    // 获取疾病列表
    getDisease() {
      this.$ajax.post(this.$apiUrls.Disease).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        returnData?.map((item) => {
          this.diseaseListObj[item.diseaseCode] = item;
        });
        this.fixed_diseaseList = returnData;
        this.diseaseList = this.fixed_diseaseList.slice(0, 100);
      });
    },
    // 运算符表格行点击回调
    operatorRowClick(row) {
      console.log(row);
      this.ruleForm.expText += row.code;
    },
    // 函数表格行点击回调
    funcRowClick(row) {
      console.log(row);
      this.ruleForm.expText += row.code;
    },
    // 项目表格行点击回调
    projectRowClick(row) {
      console.log(row);
      this.ruleForm.expText += 'v' + row.itemCode;
      if (this.ruleForm.itemText.indexOf(row.itemCode) !== -1) {
        return;
      }
      this.ruleForm.itemText +=
        this.ruleForm.itemText == '' ? row.itemCode : ',' + row.itemCode;
      this.ruleForm.items.push({
        itemCode: row.itemCode,
        valueType: row.valueType
      });
    },
    // 项目列表内容改变
    itemTextChange(value) {
      let val = value.split(',');
      return;
      this.ruleForm.items = val.filter((item, idx) => {
        return val.indexOf(item, 0) === idx;
      });
      console.log(this.ruleForm.items);
    },
    // 获取或删除疾病判断计算式
    RD_CodeDiseaseExpression(menu, url) {
      console.log(menu);
      this.$ajax
        .paramsPost(this.$apiUrls.RD_CodeDiseaseExpression + url, {
          expCode: menu.id || menu.expCode
        })
        .then(async (r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          if (url == '/Read') {
            let isHave = false;
            this.diseaseList.map((item) => {
              if (item.diseaseCode == returnData.diseaseCode) {
                isHave = true;
              }
            });
            if (!isHave) {
              await this.diseaseList.push(
                this.diseaseListObj[returnData.diseaseCode]
              );
            }

            this.ruleForm = returnData;
            return;
          }
          this.$parent.getOperatorList().then((r) => {
            this.$parent.refreshTree();
          });
          this.$parent.rightTitle = this.$parent.menuList[2].title;
          this.$message({
            message: '删除成功',
            type: 'success',
            showClose: true
          });
        });
    },
    // 删除按钮的点击回调
    deleteClick() {
      if (!this.ruleForm.expCode) {
        this.$message({
          message: '请选择需要删除的公式',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm(`确定删除${this.ruleForm.expName}公式吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.RD_CodeDiseaseExpression(this.ruleForm, '/Delete');
          this.resetForm();
        })
        .catch(() => {});
    },
    // 新建按钮的点击回调
    createClick() {
      this.isCreateFlag = '/Create';
      this.resetForm();
      this.ruleForm.items = [];
      this.isCheck = false;
    },
    // 项目列表的搜索回调
    projectSearchEnter() {
      let list = this.fixed_projectList.filter((item) => {
        return item.itemName.indexOf(this.projectSearchVal) != -1;
      });
      this.projectList = list;
    },
    // 校验按钮点击
    checkClick() {
      let data = {
        items: this.ruleForm.items,
        expText: this.ruleForm.expText
      };
      this.$ajax.post(this.$apiUrls.VerifyExpression, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) {
          this.$message({
            message: '公式校验失败!',
            type: 'error',
            showClose: true
          });
          return;
        }
        this.isCheck = true;
        this.$message({
          message: '公式校验成功!',
          type: 'success',
          showClose: true
        });
      });
    },
    // 搜索疾病
    async selectChange(val) {
      console.log(val);
      if (!val) return;
      this.$parent.muenId = val;
      await this.RD_CodeDiseaseExpression({ id: val }, '/Read');
      this.$parent.rightTitle = this.$parent.illnessObj[val];
      const matchObj = XEUtils.findTree(
        this.$parent.menuList,
        (item) => item.id === val
      );
      console.log(matchObj);
      this.$parent.treeExpand(matchObj.item);
    },
    // 搜索运算符和函数
    bottomLeftSearch() {
      let operatorList = this.fixed_operatorList.filter((item) => {
        return item.code.indexOf(this.funcSearchVal) != -1;
      });
      this.operatorList = operatorList;
      let functionList = this.fixed_functionList.filter((item) => {
        return item.code.indexOf(this.funcSearchVal) != -1;
      });
      this.functionList = functionList;
    },
    // 对应疾病的远程搜索
    remoteMethod(query) {
      if (query !== '') {
        setTimeout(() => {
          this.loading = false;
          this.diseaseList = this.fixed_diseaseList.filter((item) => {
            return (
              item.diseaseName.toLowerCase().indexOf(query.toLowerCase()) > -1
            );
          });
        }, 200);
      } else {
        this.diseaseList = this.fixed_diseaseList.slice(0, 100);
      }
    }
  },
  watch: {
    // 监听公式输入内容
    'ruleForm.expText'(newName, oldName) {
      if (newName === '' || this.isCreateFlag === '/Update') return;
      if (this.lastTime == 0) {
        this.lastTime = setTimeout(() => {
          this.$message({
            message: '请点击校验按钮校验!',
            type: 'warning',
            showClose: true
          });
        }, 2000);
      } else {
        clearTimeout(this.lastTime);
        this.lastTime = setTimeout(() => {
          this.$message({
            message: '请点击校验按钮校验!',
            type: 'warning',
            showClose: true
          });
        }, 2000);
      }
    }
  },
  created() {
    this.getFunList();
    this.getItemList();
    this.getDisease();
  }
};
</script>

<style lang="less" scoped>
.designFormulas_page {
  display: flex;
  flex-direction: column;
  height: 100%;

  .head_search {
    display: flex;
  }

  .demo-ruleForm {
    background: rgba(23, 112, 223, 0.2);
    border-radius: 4px;
    padding: 10px 15px 10px 0;

    .form_head {
      display: flex;

      .el-form-item {
        flex: 1;
        flex-shrink: 0;
      }
    }

    .el-form-item {
      margin-bottom: 15px;

      &:last-child {
        margin: 0;
      }

      .el-form-item__label {
        font-weight: 600;
        color: #2d3436;
      }
    }
  }

  .list_wrap {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    display: flex;
    margin-top: 15px;

    .left_list,
    .right_list {
      display: flex;
      flex-direction: column;
    }

    .left_list {
      margin-right: 15px;
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
    }

    .search_list {
      display: flex;
      align-items: center;

      span {
        margin-right: 10px;
      }

      margin-bottom: 15px;
    }

    .right_list {
      width: 330px;
    }

    .list_div {
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
      display: flex;
    }
    .table_wrap {
      border: 1px solid #b2bec3;
      border-radius: 4px;
      overflow: hidden;
    }
  }
}
</style>
<style lang="less">
.designFormulas_page {
  .demo-ruleForm {
    .el-form-item__label {
      font-weight: 600;
      color: #2d3436;
    }
  }
}
</style>
