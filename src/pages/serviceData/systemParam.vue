<template>
  <div class="systemParam_page" id="BasedOnTheCode">
    <div class="left_wrap wrap_style" id="LeftWrap">
      <header>
        <h3>系统参数设置</h3>
      </header>
      <div class="left_content">
        <el-menu :default-active="menuId" class="el-menu-vertical-demo">
          <el-submenu
            v-for="(item, index) in menuList"
            :key="index"
            :index="index + ''"
          >
            <template slot="title">
              <span slot="title">{{ item.name }}</span>
            </template>
            <el-menu-item
              v-for="(twoItem, twoIdx) in item.children"
              :key="twoItem.code"
              :index="twoItem.code"
              @click="menuSelect(twoItem, index, twoIdx)"
            >
              <template slot="title">
                <i class="iconfont icon-biaodan icons"></i>
                <span slot="title">{{ twoItem.name }}</span>
              </template>
            </el-menu-item>
          </el-submenu>
        </el-menu>
      </div>
    </div>
    <div class="right_wrap wrap_style">
      <header>
        <el-button
          class="blue_btn btn"
          size="small"
          @click="editBtn"
          icon="iconfont icon-bianji"
          >{{ isEditFlag ? '编辑' : '取消编辑' }}</el-button
        >
        <el-button
          class="blue_btn btn"
          size="small"
          @click="saveBtn"
          icon="iconfont icon-baocun"
          >保存</el-button
        >
      </header>
      <div class="right_content">
        <el-form
          :model="formInfo"
          ref="form_Ref"
          label-width="110px"
          class="demo-dynamic"
        >
          <el-form-item label="参数代码" prop="">
            <el-input
              disabled
              v-model.trim="formInfo.code"
              size="small"
            ></el-input>
          </el-form-item>
          <el-form-item label="参数名称" prop="">
            <el-input
              :disabled="isEditFlag"
              v-model.trim="formInfo.name"
              size="small"
            ></el-input>
          </el-form-item>
          <el-form-item v-if="formInfo.enumData" label="参数数值" prop="">
            <el-select
              style="width: 100%"
              :disabled="isEditFlag"
              size="small"
              v-model="formInfo.value"
              placeholder="请选择"
            >
              <el-option
                v-for="item in formInfo.enumData"
                :key="item"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-else label="参数数值" prop="">
            <el-input
              :disabled="isEditFlag"
              v-model.trim="formInfo.value"
              size="small"
            ></el-input>
          </el-form-item>

          <!-- <el-form-item label="参数状态" prop="">
            <el-radio-group
              v-model.trim="formInfo.isEnabled"
              :disabled="isEditFlag"
            >
              <el-radio :label="true">启用</el-radio>
              <el-radio :label="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item> -->
          <el-form-item label="参数描述" prop="">
            <el-input
              :disabled="isEditFlag"
              type="textarea"
              :autosize="{ minRows: 2 }"
              placeholder="请输入内容"
              v-model.trim="formInfo.note"
            >
            </el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { dataUtils } from '@/common';
import { mapMutations } from 'vuex';

export default {
  name: 'systemParam',

  data() {
    return {
      isEditFlag: true,
      menuList: [],
      formInfo: {},
      checkIndex: 0,
      twoIdx: 0,
      menuId: '',
      checkMenu: {}
    };
  },
  methods: {
    ...mapMutations(['M_EnumList']),
    // 获取系统参数
    getSystemList() {
      return new Promise((resolve, reject) => {
        this.$ajax.post(this.$apiUrls.ReadSystemParameterList).then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.menuList = returnData || [];
          this.formInfo = dataUtils.deepCopy(
            this.menuList[this.checkIndex].children[this.twoIdx] || {}
          );
          this.checkMenu = dataUtils.deepCopy(
            this.menuList[this.checkIndex].children[this.twoIdx] || {}
          );
          console.log(this.formInfo);
          resolve(this.menuList);
        });
      });
    },
    // 编辑
    editBtn() {
      this.isEditFlag = !this.isEditFlag;
      this.$message({
        message: this.isEditFlag ? '已取消编辑' : '已开启了编辑',
        type: 'success',
        showClose: true
      });
    },
    // 保存
    saveBtn() {
      console.log(this.formInfo);
      this.$ajax
        .post(this.$apiUrls.UpdateSystemParameter, this.formInfo)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.$message({
            message: '修改成功',
            type: 'success',
            showClose: true
          });
          this.isEditFlag = true;
          this.getSystemList();
          this.GetEnumList();
        });
    },
    //获取常用枚举列表
    GetEnumList() {
      this.$ajax.post(this.$apiUrls.GetEnumList).then((r) => {
        // console.log('枚举类型数据', r);
        this.M_EnumList(r.data.returnData.data);
      });
    },
    // 选择菜单
    menuSelect(item, index, twoIdx) {
      console.log(item, this.formInfo);
      if (JSON.stringify(this.checkMenu) != JSON.stringify(this.formInfo)) {
        this.$confirm('是否保存已修改的内容?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            await this.saveBtn();
            this.switchMenuFun(item, index, twoIdx);
          })
          .catch(() => {
            this.switchMenuFun(item, index, twoIdx);
          });
        return;
      }
      this.switchMenuFun(item, index, twoIdx);
    },
    //  切换菜单的函数封装
    switchMenuFun(item, index, twoIdx) {
      this.formInfo = dataUtils.deepCopy(item);
      this.checkMenu = dataUtils.deepCopy(item);
      this.isEditFlag = true;
      this.menuId = item.code + '';
      this.checkIndex = index;
      this.twoIdx = twoIdx;
    }
  },
  created() {
    this.getSystemList().then((r) => {
      this.menuId = r[0].children[0].code;
      console.log(this.menuId);
    });
  }
};
</script>
<style lang="less" scoped>
.systemParam_page {
  display: flex;

  .wrap_style {
    background-color: #fff;
    border-radius: 5px;
    overflow: auto;
    position: relative;
    padding-top: 48px;

    header {
      height: 48px;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      display: flex;
      align-items: center;
      padding: 0 15px;

      h3 {
        font-size: 16px;
        color: #2d3436;
        font-weight: 600;
      }
    }
  }
  .icons {
    font-size: 16px;
    margin-right: 8px;
  }
  .left_wrap {
    width: 360px;
    margin-right: 20px;

    .el-menu-item {
      height: 32px;
      line-height: 32px;
      transition: none;
    }

    .el-menu {
      border: none;
    }
    .left_content {
      height: 100%;
      overflow: auto;
    }
  }

  .right_wrap {
    flex: 1;
    flex-shrink: 0;
    padding-top: 58px;

    header {
      top: 10px;
      justify-content: flex-end;
    }

    .right_content {
      padding: 15px;
      height: 100%;
      overflow: auto;
    }
  }
  .btn {
    padding: 6.5px 10px;
  }
}
</style>
<style lang="less">
.systemParam_page {
  .el-submenu .el-menu-item:focus,
    // .el-menu-item:hover,
    .el-menu-item.is-active {
    outline: 0;
    color: #fff;
    background-color: #1770df;
    border-radius: 4px;
    border-radius: 4px;
  }
  .el-textarea__inner,
  .el-input__inner {
    color: #2d3436 !important;
    font-weight: 500;
  }
}
</style>
