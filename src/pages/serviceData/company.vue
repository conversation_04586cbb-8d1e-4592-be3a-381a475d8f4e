<template>
  <div class="basedOnTheCode" id="BasedOnTheCode">
    <div class="left" id="LeftWrap">
      <p>体检单位维护</p>
      <div style="padding: 0 10px 5px 10px">
        <el-input
          v-model="keyword"
          size="small"
          clearable
          @input="search"
          placeholder="请输入单位名称进行检索"
        ></el-input>
      </div>
      <el-menu
        ref="elMenu_Ref"
        :default-active="muenId"
        :defaultOpeneds="defaultOpened"
        class="el-menu-vertical-demo"
        @open="handleOpen"
        @close="handleClose"
        @select="handleSelect"
        style="flex: 1 0 0; overflow: auto"
      >
        <el-menu-item index="0" @click="typeClick('TypeManage')">
          <!-- <i class="el-icon-menu"></i> -->
          <span slot="title">单位分类</span>
        </el-menu-item>
        <el-submenu
          :class="muenId === item.sortIndex ? 'menu_active' : ''"
          :index="item.sortIndex"
          v-for="item in menuList"
          :key="item.sortIndex"
        >
          <template slot="title">
            <div
              :title="item.companyClsName"
              @click="companyTypeClick('CompanyType', item)"
            >
              <i class="iconfont icon-leixing1 icon"></i>
              <span>{{ item.companyClsName }}</span>
            </div>
          </template>

          <template
            v-for="child in $options.filters.filterCompany(item.company)"
          >
            <!-- 有子公司 -->
            <template v-if="child.children && child.children.length > 0">
              <el-submenu
                :key="child.sortIndex"
                :index="child.sortIndex"
                :class="muenId === child.sortIndex ? 'menu_active' : ''"
              >
                <template slot="title">
                  <div
                    :title="child.companyName"
                    @click="companySetClick('CompanySet', child)"
                  >
                    <i class="iconfont icon-basesettingcompany icon"></i>
                    <span>{{
                      `(${child.companyCode})` + child.companyName
                    }}</span>
                  </div>
                </template>

                <template>
                  <el-menu-item
                    v-for="leaf in $options.filters.filterCompany(
                      child.children
                    )"
                    :key="leaf.sortIndex"
                    :index="leaf.sortIndex"
                    @click="companySetClick('CompanySet', leaf)"
                  >
                    <template slot="title">
                      <div :title="leaf.companyName">
                        <i class="iconfont icon-basesettingcompany icon"></i>
                        <span>{{
                          `(${leaf.companyCode})` + leaf.companyName
                        }}</span>
                      </div>
                    </template>
                  </el-menu-item>
                </template>
              </el-submenu>
            </template>

            <!-- 无子公司 -->
            <template v-else>
              <el-menu-item
                :key="child.sortIndex"
                :index="child.sortIndex"
                @click="companySetClick('CompanySet', child)"
              >
                <template slot="title">
                  <div :title="child.companyName">
                    <i class="iconfont icon-basesettingcompany icon"></i>
                    <span>{{
                      `(${child.companyCode})` + child.companyName
                    }}</span>
                  </div>
                </template>
              </el-menu-item>
            </template>
          </template>
        </el-submenu>
      </el-menu>
    </div>
    <!-- 右边模块 -->
    <component
      :is="componentId"
      class="right_com"
      :menuData="menuData"
      ref="company_Ref"
      :companyCode="companyCode"
      :companyName="companyName"
      :sortIndex="muenId"
      @jumpDetail="jumpDetail"
      :parentValue="detailData"
    ></component>
    <div class="my_mask" v-if="G_CompanyImportShow">
      <el-progress
        type="circle"
        :percentage="G_CompanyImport.currentProgressPercentage"
      ></el-progress>
      <p class="tips_p">{{ G_CompanyImport.message }}</p>
    </div>
  </div>
</template>

<script>
import TypeManage from './company/typeManage';
import CompanyType from './company/companyType';
import CompanySet from './company/companySet';
import PhysicalTimesDetail from './company/components/physicalTimesDetail.vue';

import { mapGetters } from 'vuex';

export default {
  name: 'company',

  components: {
    TypeManage,
    CompanyType,
    CompanySet,
    PhysicalTimesDetail
  },
  computed: {
    ...mapGetters(['G_CompanyImportShow', 'G_CompanyImport'])
  },
  data() {
    return {
      menuList: [],
      muenId: '0',
      defaultOpened: [],
      openedMenus: [],
      viewTableList: [],
      searchIpt: '', //查询值
      tableLoading: false,
      componentId: 'TypeManage',
      menuData: {},
      companyCode: '',
      companyName: '',
      detailData: {},
      keyword: ''
    };
  },
  created() {
    this.getCompany();
  },
  mounted() {
    this.dragControllerDiv();
  },
  methods: {
    //删除
    deletes() {},
    handleOpen() {},
    handleClose() {},
    handleSelect() {},
    // 获取单位列表
    getCompany() {
      this.$ajax.post(this.$apiUrls.ReadCompanyLevel).then((r) => {
        let { success, returnData } = r.data;
        if (!success) {
          return;
        }
        this.menuList = returnData || [];
        this.search();
        if (JSON.stringify(this.menuData) == '{}') return;

        let menuData = returnData?.filter((item) => {
          return item.companyClsCode == this.menuData.companyClsCode;
        });
        this.menuData = menuData[0];
      });
    },
    // 单位分类
    typeClick(component) {
      this.componentId = component;
      this.muenId = '0';
    },
    // 单位类型点击回调
    companyTypeClick(component, menu) {
      this.componentId = component;
      this.muenId = menu.sortIndex;
      this.menuData = menu;
      this.$nextTick(() => {
        this.$refs.company_Ref.getTableData();
      });
    },
    // 单位点击回调
    companySetClick(component, menu) {
      const that = this;
      console.log(menu);

      that.componentId = component;
      that.muenId = menu.sortIndex;
      that.companyCode = menu.companyCode;
      this.companyName = menu.companyName;
      that.$nextTick(() => {
        that.$refs.company_Ref.getTitleInfo();
      });
    },
    //跳转体检次数详情
    jumpDetail(value) {
      this.detailData = value;
      this.componentId = 'PhysicalTimesDetail';
    },

    /**
     * @description: 过滤公司
     * @return {*}
     */
    search() {
      const that = this;
      const hasVal = that.keyword && that.keyword.trim().length > 0;
      for (let index = 0; index < that.menuList.length; index++) {
        let element = that.menuList[index];
        element = element.company.map((a) => {
          let show =
            !hasVal ||
            a.companyName
              .toLowerCase()
              .includes((that.keyword + '').toLowerCase()) ||
            a.companyCode.includes(that.keyword.trim());
          a.children.map((b) => {
            b.show =
              !hasVal ||
              b.companyName
                .toLowerCase()
                .includes((that.keyword + '').toLowerCase()) ||
              a.companyCode.includes(that.keyword.trim());
            show = show || b.show;
          });
          a.show = show;
          return a;
        });
      }
    }
  },
  /**
   * @description: 过滤器
   * @return {*}
   */
  filters: {
    /**
     * @description: 过滤公司数据
     * @return {Array}
     */
    filterCompany: (arr) => {
      if (!arr) return [];

      return arr.filter((x) => x.show);
    }
  }
};
</script>

<style lang="less" scoped>
.basedOnTheCode {
  display: flex;

  .my_mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    /deep/.el-progress__text {
      color: #fff;
    }

    .tips_p {
      color: #fff;
      font-size: 16px;
      margin-top: 10px;
    }
  }

  .left {
    width: 360px;
    background: white;
    display: flex;
    flex-direction: column;
    .menu_active {
      background-color: #1770df !important;
      border-radius: 4px;

      > .el-submenu__title i,
      > .el-submenu__title span {
        color: #fff !important;
      }
    }

    p {
      font-size: 16px;
      color: #2d3436;
      line-height: 48px;
      margin-left: 10px;
    }

    img {
      width: 16px;
    }

    .icon {
      margin-right: 8px;
      font-size: 18px;
    }

    .el-menu-item,
    /deep/.el-submenu__title {
      font-size: 16px;
      height: 30px;
      line-height: 30px;
      transition: none;
      // background-color: #eee;
      // border-bottom: 1px solid #ccc;
    }

    .el-submenu .el-menu-item {
      height: 30px;
      line-height: 30px;
    }

    .el-submenu .el-menu-item:focus,
    // .el-menu-item:hover,
    .el-menu-item.is-active {
      outline: 0;
      color: #fff;
      background-color: #1770df;
      border-radius: 4px;
      border-radius: 4px;
    }
  }

  .right_com {
    display: flex;
    flex: 1;
    flex-shrink: 0;
    flex-direction: column;
    margin-left: 5px;
    overflow: auto;

    /deep/.el-container.is-vertical {
      padding-left: 20px;
    }

    header {
      display: flex;
      // justify-content: space-between;
      justify-content: flex-start;
      line-height: 60px;

      .el-input {
        width: 293px;
        margin-right: 20px;
      }

      /deep/.el-input--small .el-input__inner {
        height: 36px;
        line-height: 36px;
      }
    }

    .table_com {
      flex: 1;
      overflow: auto;
    }

    .el-header,
    .el-main {
      padding: 0;
    }
  }

  /deep/.el-drawer__header {
    font-family: PingFangSC-Medium;
    font-size: 18px;
    color: #2d3436;
    margin-bottom: 28px;
    font-weight: 600;
  }

  /deep/.el-drawer .el-drawer__body {
    // background-color: #f3f3f5;
    padding: 0 10px 10px 10px;
    border-radius: 4px;
    margin: 0 18px 18px 18px;
    font-size: 14px;
    color: #2d3436;
  }

  /deep/.el-drawer__body {
    display: flex;
    flex-direction: column;
    padding-bottom: 20px;
  }

  /deep/.el-form-item__label {
    color: #2d3436;
    font-weight: 600;
  }

  .print_table {
    display: none;
  }
}
</style>
<style lang="less">
.basedOnTheCode {
  display: flex;

  .left {
    .menu_active {
      > .el-submenu__title i {
        color: #fff;
      }

      > .el-submenu__title:hover {
        background-color: #1770df !important;
        border-radius: 4px;
      }
    }
  }
}
</style>
