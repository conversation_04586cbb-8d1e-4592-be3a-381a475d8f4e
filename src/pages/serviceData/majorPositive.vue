<template>
  <div class="basedOnTheCode box" ref="box" id="BasedOnTheCode">
    <div class="left" id="LeftWrap">
      <p>重大阳性类代码维护</p>
      <el-menu
        ref="elMenu_Ref"
        :default-active="muenId"
        :defaultOpeneds="defaultOpened"
        class="el-menu-vertical-demo"
        @open="handleOpen"
        @close="handleClose"
        @select="handleSelect"
        style="height: calc(100% - 50px); overflow: auto"
      >
        <el-menu-item
          :index="item.id"
          v-for="item in muenList"
          :key="item.id"
          @click="goTo(item)"
        >
          <i :class="item.icon" class="icon"></i>
          <span slot="title">{{ item.title }}</span>
        </el-menu-item>
      </el-menu>
    </div>
    <!-- <div class="resize" title="收缩侧边栏">⋮</div> -->
    <div class="right">
      <transition name="fade-transform" mode="out-in">
        <component
          ref="viewConfig"
          :is="path"
          :clsCode="clsCode"
          :claName="claName"
          :tableThead="tableThead"
          :viewConfig="viewConfig"
          :allInfo="drawerInfo"
        />
      </transition>
    </div>
  </div>
</template>

<script>
import majorPositiveList from './majorPositive/majorPositiveList'; //重大阳性列表
import majorPositiveKeywordsList from './majorPositive/majorPositiveKeywordsList.vue'; // 重大阳性关键字
import PartList from './majorPositive/partList.vue'; // 部位列表

export default {
  name: 'majorPositive',
  components: {
    majorPositiveList,
    majorPositiveKeywordsList,
    PartList
  },
  computed: {
    // path() {
    // },
  },
  data() {
    return {
      path: 'majorPositiveList',
      muenId: '1',
      defaultOpened: ['1'],
      clsCode: '',
      claName: '',
      openedMenus: [],
      muenList: [
        {
          id: '1',
          title: '重大阳性列表',
          icon: 'iconfont icon-biaodan',
          path: 'majorPositiveList'
        },
        {
          id: '2',
          title: '关键字列表',
          icon: 'iconfont icon-shezhiduiying',
          path: 'majorPositiveKeywordsList'
        },
        {
          id: '3',
          title: '部位列表',
          icon: 'iconfont icon-shezhiduiying',
          path: 'PartList'
        }
      ],
      tableThead: '',
      viewConfig: {},
      drawerInfo: ''
    };
  },
  created() {
    // this.viewConfig = this.muenList[3].children[0].viewConfig;
  },
  mounted() {
    // this.getItemCls();
    this.dragControllerDiv();
  },

  methods: {
    // 鼠标拖动改变菜单栏的宽度
    dragControllerDiv() {
      let c = document.getElementById('LeftWrap'); // body监听移动事件
      document
        .getElementById('BasedOnTheCode')
        .addEventListener('mousemove', move); // 鼠标按下事件
      c.addEventListener('mousedown', down); // 鼠标松开事件
      document.getElementById('BasedOnTheCode').addEventListener('mouseup', up); // 是否开启尺寸修改

      let resizeable = false; // 鼠标按下时的坐标，并在修改尺寸时保存上一个鼠标的位置

      let clientX, clientY; // div可修改的最小宽高

      let minW = 8;

      let direc = ''; // 鼠标松开时结束尺寸修改

      function up() {
        resizeable = false;
      }

      // 鼠标按下时开启尺寸修改
      function down(e) {
        let d = getDirection(e); // 当位置为四个边和四个角时才开启尺寸修改

        if (d !== '') {
          resizeable = true;
          direc = d;
          clientX = e.clientX;
          clientY = e.clientY;
        }
      } // 鼠标移动事件

      function move(e) {
        let d = getDirection(e);
        let cursor;
        if (d === '') cursor = 'default';
        else cursor = d + '-resize'; // 修改鼠标显示效果

        c.style.cursor = cursor; // 当开启尺寸修改时，鼠标移动会修改div尺寸

        if (resizeable) {
          // 鼠标按下的位置在右边，修改宽度
          if (Math.max(minW, c.offsetWidth + (e.clientX - clientX)) < 200)
            return;
          if (direc.indexOf('e') !== -1) {
            c.style.width =
              Math.max(minW, c.offsetWidth + (e.clientX - clientX)) + 'px';
            clientX = e.clientX;
          }
        }
      } // 获取鼠标所在div的位置

      function getDirection(ev) {
        let xP, yP, offset, dir;
        dir = '';
        xP = ev.offsetX;
        yP = ev.offsetY;
        offset = 10;
        // if(yP < offset) dir += 'n';
        // else if(yP > c.offsetHeight - offset) dir += 's';
        // if(xP < offset) dir += 'w';
        if (xP > c.offsetWidth - offset) dir += 'e';
        return dir;
      }
    },
    handleOpen(key, keyPath) {
      this.openedMenus = this.$refs.elMenu_Ref.$data.openedMenus;
      console.log(this.openedMenus);
      // console.log(key, keyPath);
    },
    handleClose(key, keyPath) {
      this.openedMenus = this.$refs.elMenu_Ref.$data.openedMenus;
      console.log(this.openedMenus);
      // console.log(key, keyPath);
    },
    handleSelect(index, indexPath) {
      // console.log(index);
      // console.log(indexPath);
    },
    goTo(data) {
      console.log('data: ', data);
      this.path = data.path;
      this.clsCode = data.clsCode;
      this.claName = data.title;
      if (
        data.path === 'otherCodeMapping' ||
        data.path === 'noCommonCombination'
      ) {
        this.viewConfig = data.viewConfig;
        this.$nextTick(() => {
          if (this.$refs.viewConfig) {
            this.$refs.viewConfig.getTableData();
          }
        });
      }
      this.drawerInfo = {};
      if (data.viewConfig) this.drawerInfo = data.viewConfig.viewInfo;
    },
    // 项目分类
    getItemCls() {
      this.$ajax.post(this.$apiUrls.RD_CodeItemCls + '/Read', []).then((r) => {
        r.data.returnData.map((item, i) => {
          this.muenList[1].children[4].children.push({
            clsCode: item.clsCode,
            id: 'id' + item.clsCode,
            title: item.clsName,
            path: 'physicalExaminationItem',
            viewConfig: {
              viewInfo: {
                drawerTheads: {
                  itemCode: '项目代码',
                  itemName: '项目名称',
                  clsName: '项目分类'
                },
                title1: '体检项目列表',
                title2: '',
                isItem: true, //项目分类?专业组///EnumData/Department
                isSel: true,
                isMterials: false, //是否显示材料费
                isHead: false, //是否显示步
                active: 2, //显示哪个页面
                param: {
                  itemCode: 'itemCode',
                  clsCode: 'clsCode'
                },
                order: `item.itemCode?.includes(searchVal) ||
                        item.itemName?.includes(searchVal)`,
                orderSel: `item.clsCode?.includes(searchVal)`,
                setInfos: { combCode: '' }, //点击行传参
                setInfo: { combCode: '' }, //点击行传参
                apiUrls: {
                  optionPort: '/EnumData/ItemCls', //下拉
                  leftPort: '/EnumData/Item_ItemCls', //左边表格
                  rightPort: '/CodeMapping/pageQuery_ItemComb', //右边表格(query:combCode)
                  del: '/CodeMapping/CD_ItemComb/Delete',
                  set: '/CodeMapping/CD_ItemComb/Create'
                }
              }
            }
          });
          this.muenList[1].children[3].children.push({
            clsCode: item.clsCode,
            id: 'ids' + item.clsCode,
            title: item.clsName,
            path: 'shareThePage',
            viewConfig: {
              viewInfo: {
                drawerTheads: {
                  lisItemCode: '检验项目代码',
                  lisItemCNName: '检验项目名称',
                  lisCombCode: '检验组合代码',
                  lisCombCNName: '检验组合名称'
                },
                title1: '检验项目列表',
                title2: '',
                isItem: true, //项目分类?专业组///EnumData/Department
                isSel: false,
                isMterials: false, //是否显示材料费
                isHead: false, //是否显示步
                active: 2, //显示哪个页面
                isOnly: true,
                param: {
                  pageSize: 'pageSize',
                  pageNumber: 'pageNumber',
                  itemCode: 'itemCode',
                  keyword: 'keyword'
                  // itemCode: "itemCode",
                  // itemName: "itemName",
                  // lisItemCode: "lisItemCode",
                  // lisItemName: "lisItemName",
                  // lisRoomCode: "lisRoomCode",
                  // lisRoomName: "lisRoomName"
                },
                order: `item.lisItemCode?.includes(searchVal) ||
                        item.lisItemCNName?.includes(searchVal)`, //第二页输入框搜索
                orderSel: `item.lisRoomCode?.includes(searchVal)`, //第二页下拉框搜索
                setInfos: { itemCode: '', itemName: '' }, //点击对应详情传参
                setInfo: { itemCode: '' }, //点击行传参
                apiUrls: {
                  optionPort: '/EnumData/ItemCls', //下拉
                  leftPort: this.$apiUrls.GetCodeLisItems, //左边表格
                  rightPort: this.$apiUrls.GetMapCodeLisItems, //右边表格(query:combCode)
                  del: this.$apiUrls.RemoveMapCodeLisItems,
                  set: this.$apiUrls.SaveMapCodeLisItems
                }
              }
            }
          });
        });
      });
    }
  }
};
</script>
<style lang="less" scoped>
.basedOnTheCode {
  display: flex;
  .left {
    width: 320px;
    background: white;
    p {
      font-size: 16px;
      color: #2d3436;
      line-height: 48px;
      margin-left: 10px;
    }
    img {
      width: 16px;
    }
    .icon {
      margin-right: 8px;
      font-size: 18px;
    }
    .icons {
      font-size: 16px;
      margin-right: 8px;
    }
    /deep/.el-submenu__title {
      font-size: 16px;
      height: 30px;
      line-height: 30px;
      // background-color: #eee;
      // border-bottom: 1px solid #ccc;
    }
    .el-menu-item {
      height: 30px;
      line-height: 30px;
    }
    .el-submenu .el-menu-item:focus,
      // .el-menu-item:hover,
      .el-menu-item.is-active {
      outline: 0;
      color: #fff;
      background-color: #1770df;
      border-radius: 4px;
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    flex: 1;
    flex-shrink: 0;
    // width: calc(100% - 360px);
    background: white;
    margin-left: 20px;
    overflow: auto;
    padding: 0 15px 15px 15px;
  }
}
</style>
<style lang="less">
.basedOnTheCode {
  .el-menu--inline {
    // background: #cde6fa;
    // flex: 1;
    // width: calc(100% - 320px);
    background: white;
    // margin-left: 20px;
    padding: 0 4px;
  }
  .resize {
    cursor: col-resize;
    position: relative;
    top: 45%;
    background-color: #d6d6d6;
    border-radius: 5px;
    margin-top: -10px;
    width: 10px;
    height: 50px;
    background-size: cover;
    background-position: center;
    width: 10px;
    font-size: 32px;
    color: white;
  }

  .resize:hover {
    color: #444444;
  }
  .el-table {
    color: #2d3436;
  }
  /* 表格内容颜色 */
  .el-table th.el-table__cell {
    background-color: #d1e2f9;
    color: #717c86;
  }
  .el-table__body tbody tr:nth-child(even) td {
    background-color: #e7f0fb;
  }
}
</style>
