<template>
  <!-- 费别信息 -->
  <div class="feeTypeInformation">
    <el-container>
      <el-header>
        <AllBtn
          :btnList="['查询', '新建', '删除', '打印', '导出']"
          :methodCreat="add"
          :methodDelete="delMore"
          :methodSearch="search"
          :methodPrint="print"
          :methodExport="exportTable"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          ref="table_ref"
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          @rowDblclick="handleClick"
          :cell_blue="cell_blue"
          @selectionChange="handleSelectChangeTable"
          :tableLoading.sync="loading"
          :columnWidth="columnWidth"
          isCheck
        >
          <template #isTerminal="{ scope }">
            {{ scope.row.isTerminal ? '是' : '否' }}
          </template>
          <template #isEnabled="{ scope }">
            {{ scope.row.isEnabled ? '启用' : '禁用' }}
          </template>
          <template #columnRight>
            <el-table-column
              prop="operation"
              label="操作"
              width="110"
              fixed="right"
            >
              <template slot-scope="scope">
                <el-button
                  type="primary"
                  plain
                  size="mini"
                  class="view"
                  @click="viewDetails(scope.row)"
                  >对应详情</el-button
                >
              </template>
            </el-table-column>
          </template>
        </PublicTable>
      </el-main>
    </el-container>
    <el-drawer
      title="费别信息属性"
      :visible.sync="drawer"
      :before-close="cancel"
      size="25%"
      :wrapperClosable="false"
    >
      <el-form :model="popupForm" ref="ruleForm" :rules="rules">
        <el-form-item
          label="费别代码"
          :label-width="formLabelWidth"
          prop="payModeCode"
        >
          <el-input
            v-model.trim="popupForm.payModeCode"
            autocomplete="off"
            size="small"
            placeholder="请输入费别代码"
            :disabled="disabled"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="费别名称"
          :label-width="formLabelWidth"
          prop="payModeName"
        >
          <el-input
            v-model.trim="popupForm.payModeName"
            autocomplete="off"
            size="small"
            placeholder="请输入费别名称"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="现金优惠"
          :label-width="formLabelWidth"
          prop="cashDiscount"
        >
          <el-input
            v-model.trim="popupForm.cashDiscount"
            autocomplete="off"
            size="small"
            placeholder="请输入现金优惠"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="卡支付优惠"
          :label-width="formLabelWidth"
          prop="carDiscount"
        >
          <el-input
            v-model.trim="popupForm.carDiscount"
            autocomplete="off"
            size="small"
            placeholder="请输入卡支付优惠"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="现金折扣率"
          :label-width="formLabelWidth"
          prop="cashRate"
        >
          <el-input
            v-model.trim="popupForm.cashRate"
            autocomplete="off"
            size="small"
            placeholder="请输入现金折扣率"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="卡支付折扣率"
          :label-width="formLabelWidth"
          prop="carRate"
        >
          <el-input
            v-model.trim="popupForm.carRate"
            autocomplete="off"
            size="small"
            placeholder="请输入卡支付折扣率"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="父代码"
          :label-width="formLabelWidth"
          prop="parent"
        >
          <el-input
            v-model.trim="popupForm.parent"
            autocomplete="off"
            size="small"
            placeholder="请输入父代码"
          ></el-input>
        </el-form-item>
        <el-form-item label="级别" :label-width="formLabelWidth" prop="grade">
          <el-input
            v-model.trim="popupForm.grade"
            autocomplete="off"
            size="small"
            placeholder="请输入级别"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="末节标志"
          :label-width="formLabelWidth"
          prop="isTerminal"
        >
          <el-select
            class="select"
            v-model.trim="popupForm.isTerminal"
            placeholder="请选择末节标志"
            size="small"
          >
            <el-option label="是" :value="true"></el-option>
            <el-option label="否" :value="false"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="启用标志" :label-width="formLabelWidth">
          <el-switch v-model.trim="popupForm.isEnabled" active-color="#3CB34F">
          </el-switch>
        </el-form-item>
        <el-form-item label="备注" :label-width="formLabelWidth">
          <el-input
            v-model.trim="popupForm.note"
            autocomplete="off"
            type="textarea"
            placeholder="请输入备注"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="cancel" size="small" class="search-btn"
          >取消</el-button
        >
        <el-button @click="submit" size="small" class="search-btn blue_btn"
          >保存</el-button
        >
      </div>
    </el-drawer>
    <el-drawer
      :title="drawerTitle"
      :visible.sync="drawers"
      :close-on-click-modal="false"
      :wrapperClosable="false"
      size="80%"
    >
      <DetailsDrawer
        :cancel="handleClose"
        :drawerInfo="drawerInfo"
        ref="drawer_ref"
      ></DetailsDrawer>
    </el-drawer>
    <!-- 打印的表格 -->
    <div class="print_table">
      <PublicTable
        id="printHtml"
        :viewTableList.sync="checkTableList"
        :theads.sync="theads"
        :cell_blue="cell_blue"
        :columnWidth="columnWidth"
      >
      </PublicTable>
    </div>
  </div>
</template>

<script>
import { export2Excel } from '@/common/excelUtil';
import moment from 'moment';
import PublicTable from '../../../components/publicTable.vue';
import AllBtn from '../allBtn.vue';
import printJS from '@/common/printJS';
import DetailsDrawer from '../otherCodeMapping/detailsDrawer.vue';
export default {
  name: 'hospitalInfo',
  components: { PublicTable, AllBtn, DetailsDrawer },
  props: {
    //总传
    allInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      drawerTitle: '',
      drawerInfo: this.allInfo, //总传
      drawers: false,
      formInline: '',
      codeArr: [],
      resizeFlag: '',
      loading: false,
      disabled: false,
      tableDataCopy: [],
      tableData: [],
      pageSize: 50,
      currentPage: 1,
      dialogTitle: '',
      drawer: false,
      isAdd: true,
      formLabelWidth: '120px',
      roleList: [],
      popupForm: {
        payModeCode: '',
        payModeName: '',
        cashDiscount: '',
        carDiscount: '',
        cashRate: '',
        carRate: '',
        parent: '',
        grade: '',
        isTerminal: '',
        isEnabled: true,
        note: ''
      },
      theads: {
        payModeCode: '费别代码',
        payModeName: '费别名称',
        cashDiscount: '现金优惠',
        carDiscount: '卡支付优惠',
        cashRate: '现金折扣率',
        carRate: '卡支付折扣率',
        parent: '父代码',
        grade: '级别',
        isTerminal: '末节标志',
        isEnabled: '启用标志',
        note: '备注'
      },
      cell_blue: ['payModeName'],
      columnWidth: {
        carDiscount: 90,
        cashRate: 90,
        carRate: 110
      },
      rules: {
        payModeCode: [
          { required: true, message: '请输入费别代码', trigger: 'blur' }
        ],
        payModeName: [
          { required: true, message: '请输入费别名称', trigger: 'blur' }
        ],
        cashDiscount: [
          { required: true, message: '请输入现金优惠', trigger: 'blur' }
        ],
        carDiscount: [
          { required: true, message: '请输入卡支付优惠', trigger: 'blur' }
        ],
        cashRate: [
          { required: true, message: '请输入现金折扣率', trigger: 'blur' }
        ],
        carRate: [
          { required: true, message: '请输入卡支付折扣率', trigger: 'blur' }
        ],
        parent: [{ required: true, message: '请输入父代码', trigger: 'blur' }],
        grade: [{ required: true, message: '请输入级别', trigger: 'blur' }],
        isTerminal: [
          { required: true, message: '请选择末节标志', trigger: 'change' }
        ]
      },
      excelList: [],
      checkTableList: []
    };
  },
  created() {},
  mounted() {
    this.getTableData();
  },
  methods: {
    //对应详情
    viewDetails(row) {
      console.log(this);
      this.drawers = true;
      this.drawerTitle = row.payModeName + '-支付方式对应';
      this.drawerInfo.title2 = row.payModeName + '-支付方式对应列表';
      this.drawerInfo.setInfos = {
        payModeCode: row.payModeCode,
        payModeName: row.payModeName
      };
      this.drawerInfo.setInfo = { payModeCode: row.payModeCode };
      console.log('[ this.drawerInfo ]-841', this.drawerInfo);
      //执行获取下拉,左右表格数据
      this.$nextTick(() => {
        this.$refs.drawer_ref.getAll();
        this.$refs.drawer_ref.clearInput();
      });
    },

    handleClose() {
      this.drawers = false;
      this.$refs.drawer_ref.clearInput();
    },
    // 获取表格信息数据
    getTableData() {
      this.loading = true;
      this.$ajax.post(`${this.$apiUrls.RD_CodeFee}/Read`, []).then((r) => {
        console.log('r: ', r);
        const { returnData, success } = r.data;
        if (!success) {
          return;
        }
        this.loading = false;
        this.tableData = returnData || [];
        this.tableDataCopy = this.tableData;
      });
    },
    //查询
    search() {
      this.formInline = this.$refs.allBtn_Ref.searchInfo.trim(); //获取组件文本框的值
      if (this.formInline) {
        setTimeout(() => {
          this.tableData = this.tableDataCopy.filter((item) => {
            return (
              item.payModeCode.indexOf(this.formInline) !== -1 ||
              item.payModeName.indexOf(this.formInline) !== -1
            );
          });
          //this.loading = false;
        }, 500);
      } else {
        setTimeout(() => {
          this.getTableData();
          //this.loading = false;
        }, 500);
      }
    },

    //新增
    add() {
      this.isAdd = true;
      this.drawer = true;
      this.disabled = false;
      this.popupForm = {
        payModeCode: '',
        payModeName: '',
        cashDiscount: '',
        carDiscount: '',
        cashRate: '',
        carRate: '',
        parent: '',
        grade: '',
        isTerminal: '',
        isEnabled: true,
        note: ''
      };
    },
    //新增提交
    addSubmit() {
      this.$ajax
        .post(`${this.$apiUrls.CU_CodeFee}/Create`, this.popupForm)
        .then((r) => {
          console.log('r: ', r);
          const { returnData, success } = r.data;
          if (!success) {
            return;
          }
          this.getTableData();
          this.$message.success('新增成功!');
          this.drawer = false;
        });
    },

    //编辑
    handleClick(row) {
      // console.log("row", row);
      this.isAdd = false;
      this.drawer = true;
      this.disabled = true;
      this.popupForm = row;
    },
    //编辑提交
    editSubmit() {
      this.$ajax
        .post(`${this.$apiUrls.CU_CodeFee}/Update`, this.popupForm)
        .then((r) => {
          const { returnData, success } = r.data;
          if (!success) {
            return;
          }
          this.getTableData();
          this.$message.success('修改成功!');
          this.drawer = false;
        });
    },
    //提交
    submit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.isAdd) {
            this.addSubmit();
            return;
          }
          this.editSubmit();
        } else {
          return false;
        }
      });
    },
    // 取消
    cancel() {
      this.drawer = false;
      this.$refs.ruleForm.resetFields();
    },
    //复选框勾选操作
    handleSelectChangeTable(val) {
      // console.log("val: ", val);
      this.excelList = val;
      this.codeArr = [];
      val.map((item, i) => {
        if (item.payModeCode != '') {
          this.codeArr.push(item.payModeCode);
        }
      });
      console.log('this.codeArr: ', this.codeArr);
    },
    //多条删除
    delMore() {
      if (this.codeArr.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据!',
          type: 'warning'
        });
      }
      if (this.codeArr.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.$ajax
              .post(`${this.$apiUrls.RD_CodeFee}/Delete`, this.codeArr)
              .then((r) => {
                console.log('r111: ', r);
                const { returnData, success } = r.data;
                if (!success) {
                  return;
                }
                this.$message.success('删除成功!');
                this.getTableData();
              });
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    // 导出excel
    exportTable() {
      if (this.excelList.length == 0) {
        this.$message.warning('请选择至少一条数据进行操作!');
        return;
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const columns = [
          {
            title: '序号',
            key: 'index'
          },
          {
            title: '费别代码',
            key: 'payModeCode'
          },
          {
            title: '费别名称',
            key: 'payModeName'
          },
          {
            title: '现金优惠',
            key: 'cashDiscount'
          },
          {
            title: '卡支付优惠',
            key: 'carDiscount'
          },
          {
            title: '现金折扣率',
            key: 'cashRate'
          },
          {
            title: '卡支付折扣率',
            key: 'carRate'
          },
          {
            title: '父代码',
            key: 'parent'
          },
          {
            title: '级别',
            key: 'grade'
          },
          {
            title: '末节标志',
            key: 'isTerminal'
          },
          {
            title: '启用标志',
            key: 'isEnabled'
          },
          {
            title: '备注',
            key: 'note'
          }
        ];
        this.excelList.map((item, i) => {
          item.index = i + 1;
          item.isEnabled = item.isEnabled ? '启用' : '不启用';
        });
        const title = '费别信息' + moment().format('YYYY-MM-DD');
        this.$nextTick(() => {
          export2Excel(columns, this.excelList, title);
          //this.$refs.multipleTable.clearSelection();
        });
      });
    },
    //打印
    print() {
      let tableCom_Ref = this.$refs.table_ref.$refs.tableCom_Ref;
      console.log('tableCom_Ref: ', tableCom_Ref.selection);
      if (tableCom_Ref.selection.length == 0) {
        return this.$message({
          message: '请至少选择一条数据进行打印！',
          type: 'warning',
          showClose: true
        });
      }
      let viewTableList_prop = this.$refs.table_ref._props.viewTableList;
      if (tableCom_Ref.store.states.isAllSelected) {
        this.checkTableList = viewTableList_prop;
      } else {
        this.checkTableList = tableCom_Ref.selection;
      }
      setTimeout(() => {
        printJS('printHtml');
      }, 500);
    }
  }
};
</script>
<style lang="less" scoped>
.feeTypeInformation {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-weight: 600;
  // padding: 0 10px;
  header {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-header,
  .el-main {
    padding: 0;
  }
  .searchWrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  // .search-btn {
  //   padding: 8px 25px;
  //   font-size: 14px;
  // }
  // /deep/.el-drawer__body {
  //   padding: 0 20px;
  //   .el-form-item {
  //     margin-bottom: 12px;
  //   }
  // }
  /deep/.el-drawer__header {
    background: #d1e2f9;
    margin-bottom: 0;
    padding: 18px;
  }
  /deep/.el-drawer__header,
  /deep/.el-form-item__label {
    color: #2d3436;
  }
  /deep/.el-checkbox__inner {
    width: 20px;
    height: 20px;
  }
  /deep/.el-checkbox__inner::after {
    left: 7px;
    top: 3px;
  }
  /deep/.el-checkbox__inner::before {
    top: 8px;
  }
  .search-input {
    margin-right: 10px;
    // width: 322px;
  }
  .select {
    width: 100%;
  }
  .table-text {
    color: #1770df;
    // cursor: pointer;
  }
  .print_table {
    display: none;
  }
}
</style>
