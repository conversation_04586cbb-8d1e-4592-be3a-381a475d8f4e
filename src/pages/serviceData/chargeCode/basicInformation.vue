<template>
  <!-- 基础分类信息 -->
  <div class="basicInformation">
    <el-container>
      <el-header>
        <AllBtn
          :btnList="['查询', '新建', '删除', '打印', '导出']"
          :methodCreat="add"
          :methodDelete="delMore"
          :methodSearch="search"
          :methodPrint="print"
          :methodExport="exportTable"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          ref="table_ref"
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          @rowDblclick="handleClick"
          :cell_blue="cell_blue"
          @selectionChange="handleSelectChangeTable"
          :tableLoading.sync="loading"
          isCheck
        >
        </PublicTable>
      </el-main>
    </el-container>
    <el-drawer
      title="基础分类信息属性"
      :visible.sync="drawer"
      :before-close="cancel"
      size="25%"
      :wrapperClosable="false"
    >
      <el-form :model="popupForm" ref="ruleForm" :rules="rules">
        <el-form-item
          label="分类代码"
          :label-width="formLabelWidth"
          prop="feeClsCode"
        >
          <el-input
            v-model.trim="popupForm.feeClsCode"
            autocomplete="off"
            size="small"
            placeholder="请输入分类代码"
            :disabled="disabled"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="分类名称"
          :label-width="formLabelWidth"
          prop="feeClsName"
        >
          <el-input
            v-model.trim="popupForm.feeClsName"
            autocomplete="off"
            size="small"
            placeholder="请输入分类名称"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="cancel" size="small" class="search-btn"
          >取消</el-button
        >
        <el-button @click="submit" size="small" class="search-btn blue_btn"
          >保存</el-button
        >
      </div>
    </el-drawer>
    <!-- 打印的表格 -->
    <div class="print_table">
      <PublicTable
        id="printHtml"
        :viewTableList.sync="checkTableList"
        :theads.sync="theads"
        :cell_blue="cell_blue"
        :columnWidth="columnWidth"
      >
      </PublicTable>
    </div>
  </div>
</template>

<script>
import { export2Excel } from '@/common/excelUtil';
import moment from 'moment';
import PublicTable from '../../../components/publicTable.vue';
import AllBtn from '../allBtn.vue';
import printJS from '@/common/printJS';
export default {
  name: 'hospitalInfo',
  components: { PublicTable, AllBtn },
  data() {
    return {
      formInline: '',
      codeArr: [],
      resizeFlag: '',
      loading: false,
      disabled: false,
      tableDataCopy: [],
      tableData: [],
      pageSize: 50,
      currentPage: 1,
      dialogTitle: '',
      drawer: false,
      isAdd: true,
      formLabelWidth: '120px',
      roleList: [],
      columnWidth: {},
      popupForm: {
        feeClsCode: '',
        feeClsName: ''
      },
      theads: {
        feeClsCode: '分类代码',
        feeClsName: '分类名称'
      },
      cell_blue: ['feeClsName'],
      rules: {
        feeClsCode: [
          { required: true, message: '请输入分类代码', trigger: 'blur' }
        ],
        feeClsName: [
          { required: true, message: '请输入分类名称', trigger: 'blur' }
        ]
      },
      excelList: [],
      checkTableList: []
    };
  },
  created() {},
  mounted() {
    this.getTableData();
  },
  methods: {
    // 获取表格信息数据
    getTableData() {
      this.loading = true;
      this.$ajax.post(`${this.$apiUrls.RD_CodeFeeCls}/Read`, []).then((r) => {
        console.log('r: ', r);
        const { returnData, success } = r.data;
        if (!success) {
          return;
        }
        this.loading = false;
        this.tableData = r.data.returnData;
        this.tableDataCopy = r.data.returnData;
      });
    },
    //查询
    search() {
      this.formInline = this.$refs.allBtn_Ref.searchInfo.trim(); //获取组件文本框的值
      if (this.formInline) {
        setTimeout(() => {
          this.tableData = this.tableDataCopy.filter((item) => {
            return (
              item.feeClsCode.indexOf(this.formInline) !== -1 ||
              item.feeClsName.indexOf(this.formInline) !== -1
            );
          });
          //this.loading = false;
        }, 500);
      } else {
        setTimeout(() => {
          this.getTableData();
          //this.loading = false;
        }, 500);
      }
    },

    //新增
    add() {
      this.isAdd = true;
      this.drawer = true;
      this.disabled = false;
      this.popupForm = {
        feeClsCode: '',
        feeClsName: ''
      };
    },
    //新增提交
    addSubmit() {
      this.$ajax
        .post(`${this.$apiUrls.CU_CodeFeeCls}/Create`, this.popupForm)
        .then((r) => {
          console.log('r: ', r);
          const { returnData, success } = r.data;
          if (!success) {
            return;
          }
          this.getTableData();
          this.$message.success('新增成功!');
          this.drawer = false;
        });
    },

    //编辑
    handleClick(row) {
      // console.log("row", row);
      this.isAdd = false;
      this.drawer = true;
      this.disabled = true;
      this.popupForm = {
        feeClsCode: row.feeClsCode,
        feeClsName: row.feeClsName
      };
    },
    //编辑提交
    editSubmit() {
      this.$ajax
        .post(`${this.$apiUrls.CU_CodeFeeCls}/Update`, this.popupForm)
        .then((r) => {
          const { returnData, success } = r.data;
          if (!success) {
            return;
          }
          this.getTableData();
          this.$message.success('修改成功!');
          this.drawer = false;
        });
    },
    //提交
    submit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.isAdd) {
            this.addSubmit();
            return;
          }
          this.editSubmit();
        } else {
          return false;
        }
      });
    },
    // 取消
    cancel() {
      this.drawer = false;
      this.$refs.ruleForm.resetFields();
    },
    //复选框勾选操作
    handleSelectChangeTable(val) {
      // console.log("val: ", val);
      this.excelList = val;
      this.codeArr = [];
      val.map((item) => {
        if (item.feeClsCode != '') {
          this.codeArr.push(item.feeClsCode);
        }
      });
      console.log('this.codeArr: ', this.codeArr);
    },
    //多条删除
    delMore() {
      if (this.codeArr.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据!',
          type: 'warning'
        });
      }
      if (this.codeArr.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.$ajax
              .post(`${this.$apiUrls.RD_CodeFeeCls}/Delete`, this.codeArr)
              .then((r) => {
                console.log('r111: ', r);
                const { returnData, success } = r.data;
                if (!success) {
                  return;
                }
                this.$message.success('删除成功!');
                this.getTableData();
              });
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    // 导出excel
    exportTable() {
      if (this.excelList.length == 0) {
        this.$message.warning('请选择至少一条数据进行操作!');
        return;
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const columns = [
          {
            title: '序号',
            key: 'index'
          },
          {
            title: '分类代码',
            key: 'feeClsCode'
          },
          {
            title: '分类名称',
            key: 'feeClsName'
          }
        ];
        this.excelList.map((item, i) => {
          item.index = i + 1;
        });
        const title = '基础分类信息' + moment().format('YYYY-MM-DD');
        this.$nextTick(() => {
          export2Excel(columns, this.excelList, title);
          //this.$refs.multipleTable.clearSelection();
        });
      });
    },
    //打印
    print() {
      let tableCom_Ref = this.$refs.table_ref.$refs.tableCom_Ref;
      console.log('tableCom_Ref: ', tableCom_Ref.selection);
      if (tableCom_Ref.selection.length == 0) {
        return this.$message({
          message: '请至少选择一条数据进行打印！',
          type: 'warning',
          showClose: true
        });
      }
      let viewTableList_prop = this.$refs.table_ref._props.viewTableList;
      if (tableCom_Ref.store.states.isAllSelected) {
        this.checkTableList = viewTableList_prop;
      } else {
        this.checkTableList = tableCom_Ref.selection;
      }
      setTimeout(() => {
        printJS('printHtml');
      }, 500);
    }
  }
};
</script>
<style lang="less" scoped>
.basicInformation {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-weight: 600;
  // padding: 0 10px;
  header {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-header,
  .el-main {
    padding: 0;
  }
  .searchWrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  // .search-btn {
  //   padding: 8px 25px;
  //   font-size: 14px;
  // }
  // /deep/.el-drawer__body {
  //   padding: 0 20px;
  //   .el-form-item {
  //     margin-bottom: 12px;
  //   }
  // }
  /deep/.el-drawer__header,
  /deep/.el-form-item__label {
    color: #2d3436;
  }
  /deep/.el-checkbox__inner {
    width: 20px;
    height: 20px;
  }
  /deep/.el-checkbox__inner::after {
    left: 7px;
    top: 3px;
  }
  /deep/.el-checkbox__inner::before {
    top: 8px;
  }
  .search-input {
    margin-right: 10px;
    // width: 322px;
  }
  .select {
    width: 100%;
  }
  .table-text {
    color: #1770df;
    // cursor: pointer;
  }
  .print_table {
    display: none;
  }
}
</style>
