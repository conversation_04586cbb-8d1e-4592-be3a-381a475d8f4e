<script lang="ts">
// 声明组件名称
export default {
  name: 'ProjectInformation'
};
</script>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue';
import PublicTable from '../../../components/publicTable.vue';
import AllBtn from '../allBtn.vue';
import { export2Excel } from '@/common/excelUtil';
import moment from 'moment';
import printJS from '@/common/printJS';
import DetailsDrawer from '../otherCodeMapping/detailsDrawer.vue';
import { ElMessage, ElMessageBox } from 'element-ui';
import store from '@/store';
import { apiUrls as $apiUrls, ajax as $ajax } from '@/common';
//

// 类型定义
type TableRow = {
  feeClsGroupCode: string;
  feeClsGroupName: string;
  feeClsGroupType: string;
  isAddUp: string;
  [key: string]: any;
};

// 定义props
const props = defineProps({
  // 总传
  allInfo: {
    type: Object,
    default: () => ({})
  }
});

// 获取store
const G_feeClsGroupType = computed(() => store.getters.G_feeClsGroupType);

// 定义refs
const allBtn_Ref = ref(null);
const table_ref = ref(null);
const drawer_ref = ref(null);
const ruleForm = ref(null);

// 响应式数据
const drawerTitle = ref('');
const drawerInfo = reactive({ ...props.allInfo });
const drawers = ref(false);
const formInline = ref('');
const codeArr = ref([]);
const loading = ref(false);
const disabled = ref(false);
const tableDataCopy = ref([]);
const tableData = ref([]);
const drawer = ref(false);
const isAdd = ref(true);
const formLabelWidth = ref('120px');
const excelList = ref([]);
const checkTableList = ref([]);
// 为PublicTable组件添加checkSelection属性
const tableSelection = ref<Array<any>>([]);

// 表单数据
const popupForm = reactive({
  feeClsGroupCode: '',
  feeClsGroupName: '',
  feeClsGroupType: '',
  isAddUp: ''
});

// 表头定义
const theads = reactive({
  feeClsGroupCode: '分类代码',
  feeClsGroupName: '分类名称',
  feeClsGroupType: '类型',
  isAddUp: '累计标志'
});

// 蓝色单元格
const cell_blue = ref(['feeClsGroupName']);
const columnWidth = reactive({});

// 表单验证规则
const rules = reactive({
  feeClsGroupCode: [
    { required: true, message: '请输入分类代码', trigger: 'blur' }
  ],
  feeClsGroupName: [
    { required: true, message: '请输入分类名称', trigger: 'blur' }
  ],
  feeClsGroupType: [
    { required: true, message: '请选择类型', trigger: 'change' }
  ],
  isAddUp: [{ required: true, message: '请选择累计标志', trigger: 'change' }]
});

// 生命周期钩子
onMounted(() => {
  getTableData();
});

// 对应详情
const viewDetails = async (row: TableRow) => {
  console.log(row);
  drawers.value = true;
  drawerTitle.value = row.feeClsGroupName + '-基础分类对应';
  drawerInfo.title2 = row.feeClsGroupName + '-基础分类对应列表';
  drawerInfo.setInfos = {
    feeClsGroupCode: row.feeClsGroupCode,
    feeClsGroupName: row.feeClsGroupName,
    feeClsGroupType: row.feeClsGroupType === '发票分类' ? '0' : '1'
  };
  drawerInfo.setInfo = { feeClsGroupCode: row.feeClsGroupCode };
  console.log('[ drawerInfo ]-841', drawerInfo);

  // 执行获取下拉,左右表格数据
  await nextTick();
  console.log('drawer_ref.value', drawer_ref.value);
  if (drawer_ref.value) {
    drawer_ref.value.getAll();
    drawer_ref.value.clearInput();
  } else {
    console.warn(
      'drawer_ref.value 是 null，无法调用 getAll() 和 clearInput() 方法'
    );
  }
};

// 关闭抽屉
const handleClose = () => {
  drawers.value = false;
  ruleForm.value.resetFields();
};

// 获取表格信息数据
const getTableData = () => {
  loading.value = true;
  $ajax.post(`${$apiUrls.RD_CodeFeeClsGroup}/Read`, []).then((r) => {
    console.log('r: ', r);
    const { returnData, success } = r.data;
    if (!success) {
      return;
    }
    loading.value = false;
    tableData.value = returnData.map((item) => {
      return {
        feeClsGroupCode: item.feeClsGroupCode,
        feeClsGroupName: item.feeClsGroupName,
        feeClsGroupType: item.feeClsGroupType === 0 ? '发票分类' : '财务分类',
        isAddUp: item.isAddUp ? '累计' : '不累计'
      };
    });
    tableDataCopy.value = [...tableData.value];
  });
};

// 查询
const search = () => {
  formInline.value = allBtn_Ref.value.searchInfo.trim(); // 获取组件文本框的值
  if (formInline.value) {
    setTimeout(() => {
      tableData.value = tableDataCopy.value.filter((item) => {
        return (
          item.feeClsGroupCode.indexOf(formInline.value) !== -1 ||
          item.feeClsGroupName.indexOf(formInline.value) !== -1
        );
      });
    }, 500);
  } else {
    setTimeout(() => {
      getTableData();
    }, 500);
  }
};

// 新增
const add = () => {
  isAdd.value = true;
  drawer.value = true;
  disabled.value = false;
  Object.assign(popupForm, {
    feeClsGroupCode: '',
    feeClsGroupName: '',
    feeClsGroupType: '',
    isAddUp: ''
  });
};

// 新增提交
const addSubmit = () => {
  console.log('popupForm: ', popupForm);
  $ajax.post(`${$apiUrls.CU_CodeFeeClsGroup}/Create`, popupForm).then((r) => {
    console.log('r: ', r);
    const { returnData, success } = r.data;
    if (!success) {
      return;
    }
    getTableData();
    ElMessage.success('新增成功!');
    drawer.value = false;
  });
};

// 编辑
const handleClick = (row) => {
  isAdd.value = false;
  drawer.value = true;
  disabled.value = true;
  Object.assign(popupForm, {
    feeClsGroupCode: row.feeClsGroupCode,
    feeClsGroupName: row.feeClsGroupName,
    feeClsGroupType: row.feeClsGroupType === '发票分类' ? 0 : 1,
    isAddUp: row.isAddUp === '累计' ? true : false
  });
  console.log('popupForm: ', popupForm);
};

// 编辑提交
const editSubmit = () => {
  console.log('popupForm: ', popupForm);
  $ajax.post(`${$apiUrls.CU_CodeFeeClsGroup}/Update`, popupForm).then((r) => {
    const { returnData, success } = r.data;
    if (!success) {
      return;
    }
    getTableData();
    ElMessage.success('修改成功!');
    drawer.value = false;
  });
};

// 提交
const submit = () => {
  ruleForm.value.validate((valid) => {
    if (valid) {
      if (isAdd.value) {
        addSubmit();
        return;
      }
      editSubmit();
    } else {
      return false;
    }
  });
};

// 取消
const cancel = () => {
  drawer.value = false;
  ruleForm.value.resetFields();
};

// 复选框勾选操作
const handleSelectChangeTable = (val: TableRow[]) => {
  excelList.value = val;
  tableSelection.value = val; // 更新tableSelection
  codeArr.value = [];
  val.forEach((item) => {
    if (item.feeClsGroupCode != '') {
      codeArr.value.push(item.feeClsGroupCode);
    }
  });
  console.log('codeArr: ', codeArr.value);
};

// 多条删除
const delMore = () => {
  if (codeArr.value.length <= 0) {
    ElMessage({
      showClose: true,
      message: '请勾选要删除的数据!',
      type: 'warning'
    });
    return;
  }

  if (codeArr.value.length > 0) {
    ElMessageBox.confirm('是否确认删除多条文件数据?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        $ajax
          .post(`${$apiUrls.RD_CodeFeeClsGroup}/Delete`, codeArr.value)
          .then((r) => {
            console.log('r111: ', r);
            const { returnData, success } = r.data;
            if (!success) {
              return;
            }
            ElMessage.success('删除成功!');
            getTableData();
          });
      })
      .catch(() => {
        return;
      });
  }
};

// 导出excel
const exportTable = () => {
  if (excelList.value.length == 0) {
    ElMessage.warning('请选择至少一条数据进行操作!');
    return;
  }

  ElMessageBox.confirm('确定下载列表文件?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const columns = [
      {
        title: '序号',
        key: 'index'
      },
      {
        title: '分类代码',
        key: 'feeClsGroupCode'
      },
      {
        title: '分类名称',
        key: 'feeClsGroupName'
      },
      {
        title: '类型',
        key: 'feeClsGroupType'
      },
      {
        title: '累计标志',
        key: 'isAddUp'
      }
    ];

    excelList.value.forEach((item, i) => {
      item.index = i + 1;
    });

    const title = '项目分类信息' + moment().format('YYYY-MM-DD');
    nextTick(() => {
      export2Excel(columns, excelList.value, title);
    });
  });
};

// 打印
const print = () => {
  const tableCom_Ref = table_ref.value.$refs.tableCom_Ref;
  console.log('tableCom_Ref: ', tableCom_Ref.selection);

  if (tableCom_Ref.selection.length == 0) {
    return ElMessage({
      message: '请至少选择一条数据进行打印！',
      type: 'warning',
      showClose: true
    });
  }

  const viewTableList_prop = table_ref.value._props.viewTableList;
  if (tableCom_Ref.store.states.isAllSelected) {
    checkTableList.value = viewTableList_prop;
  } else {
    checkTableList.value = tableCom_Ref.selection;
  }

  setTimeout(() => {
    printJS('printHtml');
  }, 500);
};
</script>
<template>
  <!-- 项目分类信息 -->
  <div class="projectInformation">
    <el-container>
      <el-header>
        <AllBtn
          :btnList="['查询', '新建', '删除', '打印', '导出']"
          :methodCreat="add"
          :methodDelete="delMore"
          :methodSearch="search"
          :methodPrint="print"
          :methodExport="exportTable"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          ref="table_ref"
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          @rowDblclick="handleClick"
          :cell_blue="cell_blue"
          @selectionChange="handleSelectChangeTable"
          :tableLoading.sync="loading"
          v-model="tableSelection"
          isCheck
        >
          <template #columnRight>
            <el-table-column
              prop="operation"
              label="操作"
              width="110"
              fixed="right"
            >
              <template #default="{ row }">
                <el-button
                  type="primary"
                  plain
                  size="mini"
                  class="view"
                  @click="viewDetails(row)"
                  >对应详情</el-button
                >
              </template>
            </el-table-column>
          </template>
        </PublicTable>
      </el-main>
    </el-container>
    <el-drawer
      title="项目分类信息属性"
      v-model="drawer"
      :before-close="cancel"
      size="25%"
      :wrapperClosable="false"
    >
      <el-form :model="popupForm" ref="ruleForm" :rules="rules">
        <el-form-item
          label="分类代码"
          :label-width="formLabelWidth"
          prop="feeClsGroupCode"
        >
          <el-input
            v-model.trim="popupForm.feeClsGroupCode"
            autocomplete="off"
            size="small"
            placeholder="请输入分类代码"
            :disabled="disabled"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="分类名称"
          :label-width="formLabelWidth"
          prop="feeClsGroupName"
        >
          <el-input
            v-model.trim="popupForm.feeClsGroupName"
            autocomplete="off"
            size="small"
            placeholder="请输入分类名称"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="类型"
          :label-width="formLabelWidth"
          prop="feeClsGroupType"
        >
          <el-select
            class="select"
            v-model.trim="popupForm.feeClsGroupType"
            placeholder="请选择类型"
            size="small"
          >
            <el-option
              :label="item.label"
              :value="item.value"
              v-for="item in G_feeClsGroupType"
              :key="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="累计标志"
          :label-width="formLabelWidth"
          prop="isAddUp"
        >
          <el-select
            class="select"
            v-model.trim="popupForm.isAddUp"
            placeholder="请选择累计标志"
            size="small"
          >
            <el-option label="累计" :value="true"></el-option>
            <el-option label="不累计" :value="false"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="cancel" size="small" class="search-btn"
          >取消</el-button
        >
        <el-button @click="submit" size="small" class="search-btn blue_btn"
          >保存</el-button
        >
      </div>
    </el-drawer>
    <el-drawer
      :title="drawerTitle"
      v-model="drawers"
      :close-on-click-modal="false"
      :wrapperClosable="false"
      size="80%"
    >
      <DetailsDrawer
        :cancel="handleClose"
        :drawerInfo="drawerInfo"
        ref="drawer_ref"
      ></DetailsDrawer>
    </el-drawer>
    <!-- 打印的表格 -->
    <div class="print_table">
      <PublicTable
        id="printHtml"
        :viewTableList.sync="checkTableList"
        :theads.sync="theads"
        :cell_blue="cell_blue"
        :columnWidth="columnWidth"
        :checkSelection="[]"
      >
      </PublicTable>
    </div>
  </div>
</template>
<style lang="less" scoped>
.projectInformation {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-weight: 600;
  // padding: 0 10px;
  header {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-header,
  .el-main {
    padding: 0;
  }
  .searchWrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  // .search-btn {
  //   padding: 8px 25px;
  //   font-size: 14px;
  // }
  // /deep/.el-drawer__body {
  //   padding: 0 20px;
  //   .el-form-item {
  //     margin-bottom: 12px;
  //   }
  // }
  /deep/.el-drawer__header {
    background: #d1e2f9;
    margin-bottom: 0;
    padding: 18px;
  }
  /deep/.el-drawer__header,
  /deep/.el-form-item__label {
    color: #2d3436;
  }
  /deep/.el-checkbox__inner {
    width: 20px;
    height: 20px;
  }
  /deep/.el-checkbox__inner::after {
    left: 7px;
    top: 3px;
  }
  /deep/.el-checkbox__inner::before {
    top: 8px;
  }
  .search-input {
    margin-right: 10px;
    // width: 322px;
  }
  .select {
    width: 100%;
  }
  .table-text {
    color: #1770df;
    // cursor: pointer;
  }
  .print_table {
    display: none;
  }
}
</style>
