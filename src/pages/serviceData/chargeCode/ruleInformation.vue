<template>
  <!-- 凑整规则信息 -->
  <div class="ruleInformation">
    <el-container>
      <el-header>
        <AllBtn
          :btnList="['查询', '新建', '删除', '打印', '导出']"
          :methodCreat="add"
          :methodDelete="delMore"
          :methodSearch="search"
          :methodPrint="print"
          :methodExport="exportTable"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          ref="table_ref"
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          @rowDblclick="handleClick"
          :cell_blue="cell_blue"
          @selectionChange="handleSelectChangeTable"
          :tableLoading.sync="loading"
          isCheck
        >
        </PublicTable>
      </el-main>
    </el-container>
    <el-drawer
      title="凑整规则信息属性"
      :visible.sync="drawer"
      :before-close="cancel"
      size="25%"
      :wrapperClosable="false"
    >
      <el-form :model="popupForm" ref="ruleForm" :rules="rules">
        <el-form-item
          label="规则代码"
          :label-width="formLabelWidth"
          prop="roundCode"
        >
          <el-input
            v-model.trim="popupForm.roundCode"
            autocomplete="off"
            size="small"
            placeholder="请输入规则代码"
            :disabled="disabled"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="规则名称"
          :label-width="formLabelWidth"
          prop="roundName"
        >
          <el-input
            v-model.trim="popupForm.roundName"
            autocomplete="off"
            size="small"
            placeholder="请输入规则名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="下限" :label-width="formLabelWidth" prop="lower">
          <el-input
            v-model.trim="popupForm.lower"
            autocomplete="off"
            size="small"
            placeholder="请输入下限"
          ></el-input>
        </el-form-item>
        <el-form-item label="上限" :label-width="formLabelWidth" prop="higher">
          <el-input
            v-model.trim="popupForm.higher"
            autocomplete="off"
            size="small"
            placeholder="请输入上限"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="凑整金额"
          :label-width="formLabelWidth"
          prop="amount"
        >
          <el-input
            v-model.trim="popupForm.amount"
            autocomplete="off"
            size="small"
            placeholder="请输入凑整金额"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="cancel" size="small" class="search-btn"
          >取消</el-button
        >
        <el-button @click="submit" size="small" class="search-btn blue_btn"
          >保存</el-button
        >
      </div>
    </el-drawer>
    <!-- 打印的表格 -->
    <div class="print_table">
      <PublicTable
        id="printHtml"
        :viewTableList.sync="checkTableList"
        :theads.sync="theads"
        :cell_blue="cell_blue"
        :columnWidth="columnWidth"
      >
      </PublicTable>
    </div>
  </div>
</template>

<script>
import PublicTable from '../../../components/publicTable.vue';
import AllBtn from '../allBtn.vue';
import { export2Excel } from '@/common/excelUtil';
import moment from 'moment';
import printJS from '@/common/printJS';
export default {
  name: 'hospitalInfo',
  components: { PublicTable, AllBtn },
  data() {
    return {
      formInline: '',
      codeArr: [],
      resizeFlag: '',
      loading: false,
      disabled: false,
      tableDataCopy: [],
      tableData: [],
      pageSize: 50,
      currentPage: 1,
      dialogTitle: '',
      drawer: false,
      isAdd: true,
      formLabelWidth: '120px',
      roleList: [],
      popupForm: {
        roundCode: '',
        roundName: '',
        lower: '',
        higher: '',
        amount: ''
      },
      theads: {
        roundCode: '规则代码',
        roundName: '规则名称',
        lower: '下限',
        higher: '上限',
        amount: '凑整金额'
      },
      cell_blue: ['roundName'],
      columnWidth: {},
      rules: {
        roundCode: [
          { required: true, message: '请输入规则代码', trigger: 'blur' }
        ],
        roundName: [
          { required: true, message: '请输入规则名称', trigger: 'blur' }
        ],
        lower: [{ required: true, message: '请输入下限', trigger: 'blur' }],
        higher: [{ required: true, message: '请输入上限', trigger: 'blur' }],
        amount: [{ required: true, message: '请输入凑整金额', trigger: 'blur' }]
      },
      excelList: [],
      checkTableList: []
    };
  },
  created() {},
  mounted() {
    this.getTableData();
  },
  methods: {
    // 获取表格信息数据
    getTableData() {
      this.loading = true;
      this.$ajax.post(`${this.$apiUrls.RD_CodeRound}/Read`, []).then((r) => {
        console.log('r: ', r);
        const { returnData, success } = r.data;
        if (!success) {
          return;
        }
        this.loading = false;
        this.tableData = r.data.returnData;
        this.tableDataCopy = r.data.returnData;
      });
    },
    //查询
    search() {
      this.formInline = this.$refs.allBtn_Ref.searchInfo.trim(); //获取组件文本框的值
      if (this.formInline) {
        setTimeout(() => {
          this.tableData = this.tableDataCopy.filter((item) => {
            return (
              item.roundCode.indexOf(this.formInline) !== -1 ||
              item.roundName.indexOf(this.formInline) !== -1
            );
          });
          //this.loading = false;
        }, 500);
      } else {
        setTimeout(() => {
          this.getTableData();
          //this.loading = false;
        }, 500);
      }
    },

    //新增
    add() {
      this.isAdd = true;
      this.drawer = true;
      this.disabled = false;
      this.popupForm = {
        roundCode: '',
        roundName: '',
        lower: '',
        higher: '',
        amount: ''
      };
    },
    //新增提交
    addSubmit() {
      if (!this.popupForm) {
        this.$message.warning('请填写完整!');
        return;
      }
      this.$ajax
        .post(`${this.$apiUrls.CU_CodeRound}/Create`, this.popupForm)
        .then((r) => {
          console.log('r: ', r);
          const { returnData, success } = r.data;
          if (!success) {
            return;
          }
          this.getTableData();
          this.$message.success('新增成功!');
          this.drawer = false;
        });
    },

    //编辑
    handleClick(row) {
      // console.log("row", row);
      this.isAdd = false;
      this.drawer = true;
      this.disabled = true;
      this.popupForm = {
        roundCode: row.roundCode,
        roundName: row.roundName,
        lower: row.lower,
        higher: row.higher,
        amount: row.amount
      };
    },
    //编辑提交
    editSubmit() {
      this.$ajax
        .post(`${this.$apiUrls.CU_CodeRound}/Update`, this.popupForm)
        .then((r) => {
          const { returnData, success } = r.data;
          if (!success) {
            return;
          }
          this.getTableData();
          this.$message.success('修改成功!');
          this.drawer = false;
        });
    },
    //提交
    submit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.isAdd) {
            this.addSubmit();
            return;
          }
          this.editSubmit();
        } else {
          return false;
        }
      });
    },
    // 取消
    cancel() {
      this.drawer = false;
      this.$refs.ruleForm.resetFields();
    },
    //复选框勾选操作
    handleSelectChangeTable(val) {
      // console.log("val: ", val);
      this.excelList = val;
      this.codeArr = [];
      val.map((item, i) => {
        if (item.roundCode != '') {
          this.codeArr.push(item.roundCode);
        }
      });
      console.log('this.codeArr: ', this.codeArr);
    },
    //多条删除
    delMore() {
      if (this.codeArr.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据!',
          type: 'warning'
        });
      }
      if (this.codeArr.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.$ajax
              .post(`${this.$apiUrls.RD_CodeRound}/Delete`, this.codeArr)
              .then((r) => {
                console.log('r111: ', r);
                const { returnData, success } = r.data;
                if (!success) {
                  return;
                }
                this.$message.success('删除成功!');
                this.getTableData();
              });
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    // 导出excel
    exportTable() {
      if (this.excelList.length == 0) {
        this.$message.warning('请选择至少一条数据进行操作!');
        return;
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const columns = [
          {
            title: '序号',
            key: 'index'
          },
          {
            title: '规则代码',
            key: 'roundCode'
          },
          {
            title: '规则名称',
            key: 'roundName'
          },
          {
            title: '下限',
            key: 'lower'
          },
          {
            title: '上限',
            key: 'higher'
          },
          {
            title: '凑整金额',
            key: 'amount'
          }
        ];
        this.excelList.map((item, i) => {
          item.index = i + 1;
        });
        const title = '凑整规则信息' + moment().format('YYYY-MM-DD');
        this.$nextTick(() => {
          export2Excel(columns, this.excelList, title);
          //this.$refs.multipleTable.clearSelection();
        });
      });
    },
    //打印
    print() {
      let tableCom_Ref = this.$refs.table_ref.$refs.tableCom_Ref;
      console.log('tableCom_Ref: ', tableCom_Ref.selection);
      if (tableCom_Ref.selection.length == 0) {
        return this.$message({
          message: '请至少选择一条数据进行打印！',
          type: 'warning',
          showClose: true
        });
      }
      let viewTableList_prop = this.$refs.table_ref._props.viewTableList;
      if (tableCom_Ref.store.states.isAllSelected) {
        this.checkTableList = viewTableList_prop;
      } else {
        this.checkTableList = tableCom_Ref.selection;
      }
      setTimeout(() => {
        printJS('printHtml');
      }, 500);
    }
  }
};
</script>
<style lang="less" scoped>
.ruleInformation {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-weight: 600;
  // padding: 0 10px;
  header {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-header,
  .el-main {
    padding: 0;
  }
  .searchWrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  // .search-btn {
  //   padding: 8px 25px;
  //   font-size: 14px;
  // }
  // /deep/.el-drawer__body {
  //   padding: 0 20px;
  //   .el-form-item {
  //     margin-bottom: 10px;
  //   }
  // }
  /deep/.el-drawer__header,
  /deep/.el-form-item__label {
    color: #2d3436;
  }
  /deep/.el-checkbox__inner {
    width: 20px;
    height: 20px;
  }
  /deep/.el-checkbox__inner::after {
    left: 7px;
    top: 3px;
  }
  /deep/.el-checkbox__inner::before {
    top: 8px;
  }
  .search-input {
    margin-right: 10px;
    // width: 322px;
  }
  .select {
    width: 100%;
  }
  .table-text {
    color: #1770df;
    // cursor: pointer;
  }
  .print_table {
    display: none;
  }
}
</style>
