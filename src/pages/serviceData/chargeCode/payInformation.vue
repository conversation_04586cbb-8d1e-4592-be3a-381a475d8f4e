<template>
  <!-- 支付方式信息 -->
  <div class="payInformation">
    <el-container>
      <el-header>
        <AllBtn
          :btnList="['查询', '新建', '删除', '打印', '导出']"
          :methodCreat="add"
          :methodDelete="delMore"
          :methodSearch="search"
          :methodPrint="print"
          :methodExport="exportTable"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          ref="table_ref"
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          @rowDblclick="handleClick"
          :cell_blue="cell_blue"
          @selectionChange="handleSelectChangeTable"
          :tableLoading.sync="loading"
          :columnWidth="columnWidth"
          isCheck
        >
          <template #isBaseCurrency="{ scope }">
            <div>
              {{ enum_abnormity[scope.row.isBaseCurrency] }}
            </div>
          </template>
          <template #isAcc="{ scope }">
            <div>
              {{ enum_abnormity[scope.row.isAcc] }}
            </div>
          </template>
          <template #isTerminal="{ scope }">
            <div>
              {{ enum_abnormity[scope.row.isTerminal] }}
            </div>
          </template>
        </PublicTable>
      </el-main>
    </el-container>
    <el-drawer
      title="支付方式信息属性"
      :visible.sync="drawer"
      :before-close="cancel"
      size="25%"
      :wrapperClosable="false"
    >
      <el-form :model="popupForm" ref="ruleForm" :rules="rules">
        <el-form-item
          label="支付代码"
          :label-width="formLabelWidth"
          prop="payCode"
        >
          <el-input
            v-model.trim="popupForm.payCode"
            autocomplete="off"
            size="small"
            placeholder="请输入支付代码"
            :disabled="disabled"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="支付名称"
          :label-width="formLabelWidth"
          prop="payName"
        >
          <el-input
            v-model.trim="popupForm.payName"
            autocomplete="off"
            size="small"
            placeholder="请输入支付名称"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="本位币标识"
          :label-width="formLabelWidth"
          prop="isBaseCurrency"
        >
          <el-select
            class="select"
            v-model.trim="popupForm.isBaseCurrency"
            placeholder="请选择本位币标识"
            size="small"
          >
            <el-option label="是" :value="true"></el-option>
            <el-option label="否" :value="false"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="级别" :label-width="formLabelWidth" prop="grade">
          <el-input
            v-model.trim="popupForm.grade"
            autocomplete="off"
            size="small"
            placeholder="请输入级别"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="父代码"
          :label-width="formLabelWidth"
          prop="parent"
        >
          <el-input
            v-model.trim="popupForm.parent"
            autocomplete="off"
            size="small"
            placeholder="请输入父代码"
          ></el-input>
        </el-form-item>
        <el-form-item label="汇率" :label-width="formLabelWidth" prop="rate">
          <el-input
            v-model.trim="popupForm.rate"
            autocomplete="off"
            size="small"
            placeholder="请输入汇率"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="记账标志"
          :label-width="formLabelWidth"
          prop="isAcc"
        >
          <el-select
            class="select"
            v-model.trim="popupForm.isAcc"
            placeholder="请选择记账标志"
            size="small"
          >
            <el-option label="是" :value="true"></el-option>
            <el-option label="否" :value="false"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="末节标志"
          :label-width="formLabelWidth"
          prop="isTerminal"
        >
          <el-select
            class="select"
            v-model.trim="popupForm.isTerminal"
            placeholder="请选择末节标志"
            size="small"
          >
            <el-option label="是" :value="true"></el-option>
            <el-option label="否" :value="false"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="cancel" size="small" class="search-btn"
          >取消</el-button
        >
        <el-button @click="submit" size="small" class="search-btn blue_btn"
          >保存</el-button
        >
      </div>
    </el-drawer>
    <!-- 打印的表格 -->
    <div class="print_table">
      <PublicTable
        id="printHtml"
        :viewTableList.sync="checkTableList"
        :theads.sync="theads"
        :cell_blue="cell_blue"
        :columnWidth="columnWidth"
      >
      </PublicTable>
    </div>
  </div>
</template>

<script>
import PublicTable from '../../../components/publicTable.vue';
import AllBtn from '../allBtn.vue';
import { export2Excel } from '@/common/excelUtil';
import moment from 'moment';
import printJS from '@/common/printJS';
export default {
  name: 'hospitalInfo',
  components: { PublicTable, AllBtn },
  data() {
    return {
      formInline: '',
      codeArr: [],
      resizeFlag: '',
      loading: false,
      disabled: false,
      tableDataCopy: [],
      tableData: [],
      pageSize: 50,
      currentPage: 1,
      dialogTitle: '',
      drawer: false,
      isAdd: true,
      formLabelWidth: '120px',
      roleList: [],
      popupForm: {
        payCode: '',
        payName: '',
        isBaseCurrency: '',
        grade: '',
        parent: '',
        rate: '',
        isAcc: '',
        isTerminal: ''
      },
      theads: {
        payCode: '支付代码',
        payName: '支付名称',
        isBaseCurrency: '本位币标识',
        grade: '级别',
        parent: '父代码',
        rate: '汇率',
        isAcc: '记账标志',
        isTerminal: '末节标志'
      },
      cell_blue: ['payName'],
      columnWidth: {
        isBaseCurrency: 90
      },
      rules: {
        payCode: [
          { required: true, message: '请输入支付代码', trigger: 'blur' }
        ],
        payName: [
          { required: true, message: '请输入支付名称', trigger: 'blur' }
        ],
        isBaseCurrency: [
          { required: true, message: '请选择本位币标识', trigger: 'change' }
        ],
        grade: [{ required: true, message: '请输入级别', trigger: 'blur' }],
        parent: [{ required: true, message: '请输入父代码', trigger: 'blur' }],
        rate: [{ required: true, message: '请输入汇率', trigger: 'blur' }],
        isAcc: [
          { required: true, message: '请选择记账标志', trigger: 'change' }
        ],
        isTerminal: [
          { required: true, message: '请选择末节标志', trigger: 'change' }
        ]
      },
      excelList: [],
      checkTableList: [],
      enum_abnormity: {
        true: '是',
        false: '否'
      }
    };
  },
  created() {},
  mounted() {
    this.getTableData();
  },
  methods: {
    // 获取表格信息数据
    getTableData() {
      this.loading = true;
      this.$ajax.post(`${this.$apiUrls.RD_CodePayment}/Read`, []).then((r) => {
        console.log('r: ', r);
        const { returnData, success } = r.data;
        if (!success) {
          return;
        }
        this.loading = false;
        // this.tableData = returnData.map(item => {
        //   return {
        //     payCode: item.payCode,
        //     payName: item.payName,
        //     isBaseCurrency: item.isBaseCurrency ? "是" : "否",
        //     grade: item.grade,
        //     parent: item.parent,
        //     rate: item.rate,
        //     isAcc: item.isAcc ? "是" : "否",
        //     isTerminal: item.isTerminal ? "是" : "否"
        //   };
        // });
        this.tableData = returnData || [];
        this.tableDataCopy = this.tableData;
      });
    },
    //查询
    search() {
      this.formInline = this.$refs.allBtn_Ref.searchInfo.trim(); //获取组件文本框的值
      if (this.formInline) {
        setTimeout(() => {
          this.tableData = this.tableDataCopy.filter((item) => {
            return (
              item.payCode.indexOf(this.formInline) !== -1 ||
              item.payName.indexOf(this.formInline) !== -1
            );
          });
          //this.loading = false;
        }, 500);
      } else {
        setTimeout(() => {
          this.getTableData();
          //this.loading = false;
        }, 500);
      }
    },

    //新增
    add() {
      this.isAdd = true;
      this.drawer = true;
      this.disabled = false;
      this.popupForm = {
        payCode: '',
        payName: '',
        isBaseCurrency: '',
        grade: '',
        parent: '',
        rate: '',
        isAcc: '',
        isTerminal: ''
      };
    },
    //新增提交
    addSubmit() {
      // this.popupForm.grade = parseInt(this.popupForm.grade);
      console.log('this.popupForm: ', this.popupForm);
      this.$ajax
        .post(`${this.$apiUrls.CU_CodePayment}/Create`, this.popupForm)
        .then((r) => {
          console.log('r: ', r);
          const { returnData, success } = r.data;
          if (!success) {
            return;
          }
          this.getTableData();
          this.$message.success('新增成功!');
          this.drawer = false;
        });
    },

    //编辑
    handleClick(row) {
      console.log('row', row);
      this.isAdd = false;
      this.drawer = true;
      this.disabled = true;
      this.popupForm = {
        payCode: row.payCode,
        payName: row.payName,
        isBaseCurrency: row.isBaseCurrency,
        grade: row.grade,
        parent: row.parent,
        rate: row.rate,
        isAcc: row.isAcc,
        isTerminal: row.isTerminal
      };
    },
    //编辑提交
    editSubmit() {
      console.log('this.popupForm: ', this.popupForm);
      this.$ajax
        .post(`${this.$apiUrls.CU_CodePayment}/Update`, this.popupForm)
        .then((r) => {
          const { returnData, success } = r.data;
          if (!success) {
            return;
          }
          this.getTableData();
          this.$message.success('修改成功!');
          this.drawer = false;
        });
    },
    //提交
    submit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.isAdd) {
            this.addSubmit();
            return;
          }
          this.editSubmit();
        } else {
          return false;
        }
      });
    },
    // 取消
    cancel() {
      this.drawer = false;
      this.$refs.ruleForm.resetFields();
    },
    //复选框勾选操作
    handleSelectChangeTable(val) {
      // console.log("val: ", val);
      this.excelList = val;
      this.codeArr = [];
      val.map((item, i) => {
        if (item.payCode != '') {
          this.codeArr.push(item.payCode);
        }
      });
      console.log('this.codeArr: ', this.codeArr);
    },
    //多条删除
    delMore() {
      if (this.codeArr.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据!',
          type: 'warning'
        });
      }
      if (this.codeArr.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.$ajax
              .post(`${this.$apiUrls.RD_CodePayment}/Delete`, this.codeArr)
              .then((r) => {
                console.log('r111: ', r);
                const { returnData, success } = r.data;
                if (!success) {
                  return;
                }
                this.$message.success('删除成功!');
                this.getTableData();
              });
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    // 导出excel
    exportTable() {
      if (this.excelList.length == 0) {
        this.$message.warning('请选择至少一条数据进行操作!');
        return;
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const columns = [
          {
            title: '序号',
            key: 'index'
          },
          {
            title: '支付代码',
            key: 'payCode'
          },
          {
            title: '支付名称',
            key: 'payName'
          },
          {
            title: '本位币标识',
            key: 'isBaseCurrency'
          },
          {
            title: '级别',
            key: 'grade'
          },
          {
            title: '父代码',
            key: 'parent'
          },
          {
            title: '汇率',
            key: 'rate'
          },
          {
            title: '记账标志',
            key: 'isAcc'
          },
          {
            title: '末节标志',
            key: 'isTerminal'
          }
        ];
        this.excelList.map((item, i) => {
          item.index = i + 1;
        });
        const title = '支付方式信息' + moment().format('YYYY-MM-DD');
        this.$nextTick(() => {
          export2Excel(columns, this.excelList, title);
          //this.$refs.multipleTable.clearSelection();
        });
      });
    },
    //打印
    print() {
      let tableCom_Ref = this.$refs.table_ref.$refs.tableCom_Ref;
      console.log('tableCom_Ref: ', tableCom_Ref.selection);
      if (tableCom_Ref.selection.length == 0) {
        return this.$message({
          message: '请至少选择一条数据进行打印！',
          type: 'warning',
          showClose: true
        });
      }
      let viewTableList_prop = this.$refs.table_ref._props.viewTableList;
      if (tableCom_Ref.store.states.isAllSelected) {
        this.checkTableList = viewTableList_prop;
      } else {
        this.checkTableList = tableCom_Ref.selection;
      }
      setTimeout(() => {
        printJS('printHtml');
      }, 500);
    }
  }
};
</script>
<style lang="less" scoped>
.payInformation {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-weight: 600;
  // padding: 0 10px;
  header {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-header,
  .el-main {
    padding: 0;
  }
  .searchWrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  // .search-btn {
  //   padding: 8px 25px;
  //   font-size: 14px;
  // }
  // /deep/.el-drawer__body {
  //   padding: 0 20px;
  //   .el-form-item {
  //     margin-bottom: 10px;
  //   }
  // }
  /deep/.el-drawer__header,
  /deep/.el-form-item__label {
    color: #2d3436;
  }
  /deep/.el-checkbox__inner {
    width: 20px;
    height: 20px;
  }
  /deep/.el-checkbox__inner::after {
    left: 7px;
    top: 3px;
  }
  /deep/.el-checkbox__inner::before {
    top: 8px;
  }
  .search-input {
    margin-right: 10px;
    // width: 322px;
  }
  .select {
    width: 100%;
  }
  .table-text {
    color: #1770df;
    // cursor: pointer;
  }
  .print_table {
    display: none;
  }
}
</style>
