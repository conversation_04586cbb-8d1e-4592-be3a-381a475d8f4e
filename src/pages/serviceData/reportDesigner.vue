<template>
  <div
    class="reportDesigner_page full_screen"
    style="width: 100% !important"
    @click="menuShowFun(false)"
  >
    <header>
      <el-page-header @back="goBack">
        <template slot="content">
          <div style="display: flex; align-items: center">
            <i
              class="report_menu"
              :class="menuShow ? 'el-icon-s-unfold' : 'el-icon-s-fold'"
              @click.stop="menuShowFun(true)"
              title="报告列表"
            ></i>
            <span>报表设计</span>
          </div>
        </template>
      </el-page-header>
    </header>
    <div class="report_wrap">
      <div
        class="report_list"
        @click.stop
        :class="{ report_wrap_show: menuShow }"
        @mouseleave.stop="menuShowFun(false)"
      >
        <div class="menu_head">
          <el-button type="primary" size="mini" @click="createReport"
            >新增</el-button
          >
        </div>
        <el-menu
          :default-active="menuActive"
          style="height: 100%"
          class="menu_wrap"
        >
          <el-submenu
            :index="item.typeCode"
            v-for="(item, idx) in menuList"
            :key="idx"
          >
            <template slot="title">
              <i class="el-icon-folder-opened"></i>
              <span>{{ item.typeName }}</span>
            </template>
            <el-menu-item
              :index="twoItem.reportCode"
              class="menu_item"
              v-for="(twoItem, twoIdx) in item.reports"
              :key="twoIdx"
              @click="menuItemClick(twoItem)"
            >
              <i class="el-icon-document"></i>
              <span slot="title">{{ twoItem.reportName }}</span>
              <p class="edit_wrap">
                <!-- <i
                  class="el-icon-edit"
                  title="编辑"
                  @click.stop="modifyReport(twoItem)"
                ></i> -->
                <i
                  class="el-icon-close"
                  title="删除"
                  @click.stop="delReport(twoItem)"
                ></i>
              </p>
            </el-menu-item>
          </el-submenu>
        </el-menu>
      </div>
      <div
        class="iframe_wrap"
        v-loading="loading"
        element-loading-background="rgba(0, 0, 0, 0.8)"
      >
        <iframe id="iframeDemo" :src="reportDesignerSrc" frameborder="0"
          >请选择报表</iframe
        >
      </div>
    </div>
    <!-- 新增报表弹窗 -->
    <el-dialog
      :title="isModify ? '修改报表' : '新建报表'"
      :visible.sync="dialogVisible"
      width="30%"
      append-to-body
    >
      <el-form
        ref="ruleForm"
        :rules="rules"
        :model="ruleForm"
        label-width="80px"
      >
        <el-form-item label="通用" prop="general">
          <el-checkbox v-model="ruleForm.general">是否通用</el-checkbox>
        </el-form-item>
        <el-form-item label="类型" prop="typeCode">
          <el-select
            v-model="ruleForm.typeCode"
            clearable
            filterable
            placeholder="请选择类型"
          >
            <el-option
              v-for="item in menuList"
              :key="item.typeCode"
              :label="item.typeName"
              :value="item.typeCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="名称" prop="reportName">
          <el-input v-model="ruleForm.reportName"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmBtn()">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { dataUtils, storage } from '@/common';

export default {
  name: 'reportDesigner',
  data() {
    return {
      menuShow: false,
      menuActive: '',
      menuList: [],
      reportDesignerSrc: '',
      ruleForm: {
        reportName: '',
        typeCode: '',
        general: false
      },
      dialogVisible: false,
      isModify: false, //是否修改
      reportTypeList: [
        {
          typeCode: '1',
          typeName: '指引单'
        }
      ],
      loading: false,
      rules: {
        reportName: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
        typeCode: [
          { required: true, message: '请选择报表类型', trigger: 'change' }
        ]
      },
      routeFrom: '', //记录从哪个页面进入报表设计页面的，然后原路返回；
      hospShortName: storage.session.get('userInfo')?.hospInfo.hospShortName,
      options: []
    };
  },
  methods: {
    menuShowFun(flag) {
      this.menuShow = flag;
    },
    // 返回上一页
    goBack() {
      this.$router.push({
        path: this.routeFrom
      });
    },
    // 获取报告设计模板列表
    getReportList() {
      this.$ajax.post(this.$apiUrls.ReadReportList).then((r) => {
        let { returnData } = r.data;
        returnData?.map((item) => {
          let child = [];
          item.reports.map((twoItem) => {
            if (twoItem.reportName.indexOf('*') == -1) {
              child.push(twoItem);
            } else {
              if (twoItem.reportName.indexOf(this.hospShortName) != -1) {
                child.push(twoItem);
              }
            }
          });
          item.reports = child;
        });
        this.menuList = returnData || [];
      });
    },
    // 新增按钮
    createReport() {
      this.dialogVisible = true;
      this.isModify = false;
      this.$nextTick(() => {
        this.ruleForm = {};
        this.resetForm('ruleForm');
      });
    },
    // 修改报表
    modifyReport(report) {
      this.dialogVisible = true;
      this.isModify = true;
      this.$nextTick(() => {
        this.ruleForm = dataUtils.deepCopy(report);
        this.resetForm('ruleForm');
      });
    },
    // 新增和修改的确定按钮
    confirmBtn() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          if (this.isModify) {
          } else {
            this.creatReport();
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    // 新建
    creatReport() {
      console.log(this.ruleForm);
      if (!this.ruleForm.general) {
        this.ruleForm.reportName =
          this.hospShortName + '*' + this.ruleForm.reportName;
      }
      this.$ajax
        .paramsPost(this.$apiUrls.NewEmptyReport, this.ruleForm)
        .then((r) => {
          let { returnData, success } = r.data;
          if (!success) return;
          this.dialogVisible = false;
          this.getReportList();
          this.$message({
            message: '新建成功！',
            type: 'success',
            showClose: true
          });
          this.reportDesignerSrc = `${this.$config.pdfFileUrl}home/designer?code=${returnData.reportCode}`;
          this.menuActive = returnData.reportCode;
        });
    },
    // 报表模板的点击事件
    menuItemClick(menu) {
      this.loading = true;
      this.menuActive = menu.reportCode;
      this.reportDesignerSrc = `${this.$config.pdfFileUrl}home/designer?code=${menu.reportCode}`;
      setTimeout(() => {
        this.loading = false;
      }, 3000);
    },
    // 删除报表设计模板
    delReport(menu) {
      this.$confirm(`是否删除${menu.reportName}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          let datas = {
            reportCode: menu.reportCode
          };

          this.$ajax.paramsPost(this.$apiUrls.DeleteReport, datas).then((r) => {
            let { returnData, success } = r.data;
            if (!success) return;
            this.getReportList();
            this.$message({
              message: '删除成功！',
              type: 'success',
              showClose: true
            });
            this.getReportList();
          });
        })
        .catch(() => {});
    },
    // 重置
    resetForm(formName) {
      console.log(this.$refs[formName]);
      this.$refs[formName].resetFields();
    },
    //获取院区列表
    getCodeHospital() {
      this.$ajax.post(this.$apiUrls.GetHospitalInfo).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.options = returnData;
      });
    }
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.routeFrom = from.path;
    });
  },
  created() {
    this.getReportList();
  }
};
</script>

<style lang="less" scoped>
.reportDesigner_page {
  overflow: hidden !important;
  display: flex;
  position: relative;
  flex-direction: column;
  .report_menu {
    font-size: 24px;
    cursor: pointer;
  }
  header {
    padding: 5px;
    font-size: 30px;
    text-align: right;
    background: #fff;
    border-bottom: 1px solid #e6e6e6;
    i {
      cursor: pointer;
    }
  }
  .report_wrap {
    flex: 1;
    flex-shrink: 0;
    display: flex;
    overflow: auto;
    position: relative;
    .report_list {
      width: 200px;
      display: flex;
      flex-direction: column;
      position: absolute;
      top: 0;
      left: -200px;
      bottom: 0;
      z-index: 2;
      transition: 200ms ease-in-out;
      // position: absolute;
      // left:-200px;
      .menu_head {
        background: #fff;
        border-right: 1px solid #e6e6e6;
        text-align: right;
        padding: 5px;
      }
      .menu_wrap {
        flex: 1;
        flex-shrink: 0;
        overflow: auto;
      }
    }
    .menu_item {
      &:hover .edit_wrap {
        display: block !important;
      }
    }
    .edit_wrap {
      position: absolute;
      display: none;
      right: 5px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
  .report_wrap_show {
    left: 0 !important;
  }
  .iframe_wrap {
    flex: 1;
    flex-shrink: 0;
    background: #fff;
    overflow: hidden;
  }
  iframe {
    height: 100%;
    width: 100%;
  }
}
.full_screen {
  padding: 0 !important;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 2000;
}
</style>
