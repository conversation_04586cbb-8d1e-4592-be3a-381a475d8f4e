<template>
  <div class="SMSTemplate">
    <!-- 短信模板 -->
    <el-container>
      <el-header>
        <AllBtn
          :btnList="['查询', '新建', '导出']"
          :methodCreat="newProject"
          :methodSearch="search"
          :methodExport="exportTable"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          @rowDblclick="handleClick"
          @selectionChange="handleSelectChangeTable"
          :tableLoading.sync="loading"
          isCheck
          :columnWidth="columnWidth"
        >
          <template #isEnable="{ scope }">
            <el-switch
              disabled
              v-model="scope.row.isEnable"
              active-color="#13ce66"
              inactive-color="#ff4949"
            >
            </el-switch>
          </template>
        </PublicTable>
      </el-main>
    </el-container>
    <el-drawer
      title="短信模板信息属性"
      :visible.sync="drawer"
      :before-close="handleClose"
      :wrapperClosable="false"
    >
      <el-form :model="popupForm" ref="ruleForm" :rules="rules">
        <el-form-item label="模板号" :label-width="formLabelWidth" prop="code">
          <el-input
            v-model.trim="popupForm.code"
            autocomplete="off"
            size="small"
            disabled
            placeholder="请输入模板号"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          label="模板名称"
          :label-width="formLabelWidth"
          prop="name"
        >
          <el-input
            v-model.trim="popupForm.name"
            autocomplete="off"
            size="small"
            placeholder="请输入模板名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          label="模板类型"
          :label-width="formLabelWidth"
          prop="type"
        >
          <el-select
            v-model.trim="popupForm.type"
            placeholder="请选择模板类型"
            size="small"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="item in typeList"
              :key="item.type"
              :label="item.typeName"
              :value="item.type"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="模板内容"
          :label-width="formLabelWidth"
          prop="content"
        >
          <el-input
            v-model.trim="popupForm.content"
            autocomplete="off"
            type="textarea"
            :row="3"
            placeholder="请输入模板内容"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="是否启用" :label-width="formLabelWidth"
          ><el-radio-group v-model="popupForm.isEnable">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="handleClose" size="small">取消</el-button>
        <el-button class="blue_btn" @click="submit" size="small"
          >保存</el-button
        >
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { export2Excel } from '@/common/excelUtil';
import moment from 'moment';
import AllBtn from './allBtn.vue';
import { mapGetters } from 'vuex';
import PublicTable from '../../components/publicTable.vue';
import { dataUtils } from '@/common';
export default {
  name: 'SMSTemplate',
  components: { PublicTable, AllBtn },
  data() {
    return {
      formInline: '',
      codeArr: [],
      loading: false,
      disabled: false,
      tableData: [],
      drawer: false,
      formLabelWidth: '120px',
      popupForm: {
        code: '',
        name: '',
        sex: '',
        type: '',
        content: '',
        isEnable: true
      },
      theads: {
        code: '模板号',
        name: '模板名称',
        typeName: '模板类型',
        content: '模板内容',
        isEnable: '是否启用'
      },
      columnWidth: {
        code: 120,
        name: 180,
        type: 120,

        isEnable: 80
      },
      rules: {
        code: [{ required: true, message: '请输入模板代码', trigger: 'blur' }],
        name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
        content: [
          { required: true, message: '请输入模板内容', trigger: 'blur' }
        ],
        type: [{ required: true, message: '请选择模板类型', trigger: 'blur' }]
      },
      typeList: [],
      excelList: []
    };
  },
  created: function () {
    this.GetShortMsgTemplateTypes();
  },
  mounted: function () {
    this.search();
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_shareSexList'])
  },
  methods: {
    handleClose() {
      this.drawer = false;
      this.$refs.ruleForm.resetFields();
    },
    //查询
    search() {
      this.formInline = this.$refs.allBtn_Ref.searchInfo.trim(); //获取组件文本框的值
      let data = {
        pageSize: 0,
        pageNumber: 0,
        keyword: this.formInline
      };
      this.$ajax.post(this.$apiUrls.GetShortMsgTemplates, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.tableData = returnData;
      });
    },

    //新增
    newProject() {
      this.drawer = true;
      this.disabled = false;
      this.popupForm = {
        code: '',
        name: '',
        sex: '',
        content: '',
        type: 0,
        isEnable: false
      };
      this.NewShortMsgTemplate();
    },
    //新建短信模板初始化内容
    NewShortMsgTemplate() {
      this.$ajax.post(this.$apiUrls.NewShortMsgTemplate).then((r) => {
        let { success, returnData } = r.data;
        if (!success) {
          return;
        }
        this.popupForm = returnData;
      });
    },
    //编辑
    handleClick(row) {
      console.log('row', row);
      this.drawer = true;
      this.disabled = true;
      this.GetShortMsgTemplate(row.code);
    },
    // 获取短信模板
    GetShortMsgTemplate(code) {
      this.$ajax
        .post(this.$apiUrls.GetShortMsgTemplate, '', {
          query: { code: code }
        })
        .then((r) => {
          this.popupForm = r.data.returnData;
        });
    },
    // 获取短信模板类型
    GetShortMsgTemplateTypes() {
      this.$ajax.post(this.$apiUrls.GetShortMsgTemplateTypes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) {
          return;
        }
        this.typeList = returnData || [];
      });
    },
    //提交
    submit() {
      console.log(this.popupForm);
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$ajax
            .post(this.$apiUrls.SaveShortMsgTemplate, this.popupForm)
            .then((r) => {
              let { success, returnMsg } = r.data;
              if (!success) {
                return;
              }
              this.tableData = [];
              this.drawer = false;
              this.$nextTick(() => {
                this.$refs.allBtn_Ref.searchInfo = '';
                this.search();
              });
              this.$message({
                message: returnMsg,
                type: 'success',
                showClose: true
              });
            });
        } else {
          return false;
        }
      });
    },
    //复选框勾选操作
    handleSelectChangeTable: function (val) {
      //console.log(val);
      this.excelList = val;
      this.codeArr = [];
      val.map((item, i) => {
        if (item.code != '') {
          this.codeArr.push(item.code);
        }
      });
      //console.log(this.codeArr);
    },
    // 导出excel
    exportTable() {
      if (this.excelList.length == 0) {
        this.$message.warning('请选择至少一条数据进行操作!');
        return;
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let columns = [];
        let theads = { index: '序号', ...this.theads };
        Object.keys(theads).map((item) => {
          columns.push({
            title: theads[item],
            key: item
          });
        });
        let data = [];
        data = dataUtils.deepCopy(this.excelList);
        data.map((item, i) => {
          item.index = i + 1;
          item.isEnable = item.isEnable ? '是' : '否';
        });

        const title = '短信模板' + moment().format('YYYY-MM-DD');
        this.$nextTick(() => {
          export2Excel(columns, data, title);
        });
      });
    },
    print() {}
  }
};
</script>
<style lang="less" scoped>
.SMSTemplate {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  font-weight: 600;
  header {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
  }
  /deep/.el-drawer__header,
  /deep/.el-form-item__label {
    color: #2d3436;
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-container.is-vertical {
    height: 100%;
  }
  .el-header,
  .el-main {
    padding: 0;
  }

  .searchWrap .el-form-item:last-child {
    margin-right: 0;
    margin-left: 10px;
  }
  .indexPage .contentWrap .tabPage {
    margin-top: 0;
  }
  .el-pagirange {
    text-align: center;
  }
  .el-drawer.rtl {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #2d3436;
    padding: 0 20px 0 10px;
  }
  .el-drawer__body {
    padding-right: 20px;

    .el-form-item {
      margin-bottom: 10px;
      .el-select {
        width: 100%;
      }
    }
  }
}
</style>
<style>
.el-table .el-table__cell {
  padding: 5px 0;
}
.el-main .el-checkbox__inner {
  width: 20px;
  height: 20px;
}
.el-drawer__body {
  padding-right: 20px;
}
</style>
