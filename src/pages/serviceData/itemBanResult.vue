<template>
  <div class="itemBanResult" ref="box" id="BasedOnTheCode">
    <div class="left" id="LeftWrap">
      <header>
        <p>项目列表</p>
        <div style="padding: 0 10px">
          <el-input
            v-model="listSearchVal"
            size="small"
            clearable
            @input="listSearch"
            placeholder="请输入内容"
          ></el-input>
        </div>
      </header>
      <div class="left_content">
        <el-menu
          ref="elMenu_Ref"
          default-active="itemCode"
          :defaultOpeneds="defaultOpened"
          class="el-menu-vertical-demo"
          style="height: calc(100% - 50px); overflow: auto"
        >
          <el-submenu
            :index="item.clsCode"
            v-for="item in muenList"
            :key="item.clsCode"
          >
            <template slot="title">
              <i class="iconfont icon-leixing1 icon"></i>
              <span>{{ item.clsName }}</span>
            </template>
            <template>
              <el-menu-item
                v-for="son in item.child"
                :key="son.itemCode"
                :index="son.itemCode"
                @click="goTo(son)"
              >
                <template slot="title">
                  <i class="iconfont icon-biaodan icons"></i>
                  <span>{{ '(' + son.itemCode + ')' + son.itemName }}</span>
                </template>
              </el-menu-item>
            </template>
          </el-submenu>
        </el-menu>
      </div>
    </div>
    <div class="right">
      <AllBtn
        :btnList="['查询', '新建', '删除', '打印']"
        :methodCreat="newProject"
        :methodDelete="deletes"
        :methodSearch="search"
        :methodPrint="prints"
        ref="allBtn_Ref"
      />
      <PublicTable
        :viewTableList.sync="tableData"
        :theads.sync="theads"
        @rowDblclick="handleClick"
        :cell_blue="cell_blue"
        @selectionChange="handleCurrentChange"
        isCheck
      ></PublicTable>
    </div>
    <el-drawer
      title="项目结果禁止规则属性"
      :visible.sync="drawer"
      :before-close="handleClose"
      :wrapperClosable="false"
    >
      <el-form :model="popupForm" ref="ruleForm" :rules="rules">
        <el-form-item label="编号" :label-width="formLabelWidth" prop="serNo">
          <el-input
            v-model.trim="popupForm.serNo"
            autocomplete="off"
            size="small"
            placeholder="请输入编号"
            :disabled="disabled"
          ></el-input>
        </el-form-item>

        <el-form-item
          label="结果禁止规则描述"
          :label-width="formLabelWidth"
          prop="result"
        >
          <el-input
            type="textarea"
            v-model.trim="popupForm.result"
            autocomplete="off"
            size="small"
            placeholder="请输入结果禁止规则描述"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="handleClose" size="small">取消</el-button>
        <el-button size="small" @click="submit" class="blue_btn"
          >保存</el-button
        >
      </div>
    </el-drawer>
    <!-- 打印的表格 -->
    <div class="print_table">
      <PublicTable
        id="printHtml"
        class="table_com"
        :viewTableList.sync="rowList"
        :theads.sync="theads"
        :cell_blue="cell_blue"
      >
      </PublicTable>
    </div>
  </div>
</template>

<script>
import AllBtn from './allBtn.vue';
import PublicTable from '@/components/publicTable';
import printJS from '@/common/printJS';
import { dataUtils } from '../../common';
export default {
  name: 'itemBanResult',
  components: {
    AllBtn,
    PublicTable
  },

  computed: {},
  data() {
    return {
      defaultOpened: [], //默认展开
      itemCode: '',
      muenList: [], //左边栏
      fixed_menuList: [],
      listSearchVal: '',
      theads: {
        serNo: '编号',
        result: '结果禁止规则描述'
      },
      cell_blue: ['result'], //加蓝
      formInline: '',
      tableDataCopy: [],
      tableData: [],
      drawer: false,
      disabled: false, //是否可编辑
      formLabelWidth: '150px',
      popupForm: {
        itemCode: '',
        serNo: '',
        result: ''
      }, //新增编辑
      rules: {
        serNo: [{ required: true, message: '请输入编号', trigger: 'blur' }],
        result: [{ required: true, message: '请输入结果', trigger: 'blur' }]
      },
      funTpye: '/Create',
      rowList: [], //放置勾选
      codeBoundTypeArr: [] //参考类型下拉
    };
  },
  created: function () {
    this.ItemGroupByItemCls();
  },
  mounted: function () {
    this.dragControllerDiv();
  },

  methods: {
    goTo(data) {
      this.itemCode = data.itemCode;
      this.getCodeItemBanResult();
    },
    // 获取左边栏项目列表
    ItemGroupByItemCls() {
      this.$ajax.post(this.$apiUrls.ItemGroupByItemCls).then((r) => {
        this.muenList = r.data.returnData;
        this.fixed_menuList = r.data.returnData;
        this.defaultOpened = [this.muenList[0].clsCode];
        this.itemCode = this.muenList[0].child[0].itemCode;
        this.search();
      });
    },
    //勾选
    handleCurrentChange(row) {
      this.rowList = row;
      // console.log(row);
    },
    handleClose() {
      this.drawer = false;
      this.$nextTick(function () {
        this.$refs.ruleForm.resetFields();
      });
    },
    // 获取右边项目结果禁止规则列表数据
    getCodeItemBanResult() {
      console.log('this.itemCode11111111111', this.itemCode);
      this.$ajax
        .post(this.$apiUrls.Read_ItemBanResult, '', {
          query: {
            itemCode: this.itemCode
          }
        })
        .then((r) => {
          //
          this.tableData = r.data.returnData;
          this.tableDataCopy = r.data.returnData;
        });
    },

    //查询
    search() {
      this.formInline = this.$refs.allBtn_Ref.searchInfo.trim(); //获取组件文本框的值
      if (this.formInline.length > 0) {
        this.tableData = this.tableDataCopy.filter((item) => {
          return (
            item.result === this.formInline ||
            item.result.indexOf(this.formInline) !== -1
          );
        });
      } else {
        this.getCodeItemBanResult();
      }
    },

    //新增
    newProject() {
      this.drawer = true;
      this.funTpye = '/Create';
      this.disabled = false;
      this.popupForm = {
        itemCode: this.itemCode,
        serNo: '',
        result: ''
      };
    },

    //编辑
    handleClick(row) {
      console.log('row', row);
      this.drawer = true;
      this.funTpye = '/Update';
      this.disabled = true;
      this.popupForm = {
        itemCode: this.itemCode,
        serNo: row.serNo,
        result: row.result
      };
    },
    // 搜索菜单
    listSearch() {
      console.log(this.muenList);
      if (this.listSearchVal.trim() === '') {
        this.muenList = dataUtils.deepCopy(this.fixed_menuList);
        return;
      }
      let muenList = [];
      this.fixed_menuList.map((item, idx) => {
        let isHave = false;
        let itemHave = {
          ...item,
          child: []
        };
        let itemChild = item.child.filter((twoItem) => {
          if (
            twoItem.itemName.includes(this.listSearchVal) ||
            twoItem.itemCode.includes(this.listSearchVal)
          ) {
            isHave = true;
            return twoItem;
          }
        });
        itemHave.child = itemChild;
        if (isHave) {
          muenList.push(itemHave);
        }
      });
      this.muenList = muenList;
    },
    //提交
    submit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$ajax
            .post(this.$apiUrls.CUD_CodeItemBanResult + this.funTpye, [
              this.popupForm
            ])
            .then((r) => {
              //
              let { success, returnData } = r.data;
              if (!success) return;
              this.drawer = false;
              if (this.funTpye == '/Create') {
                this.$message({
                  message: '新建成功',
                  type: 'success',
                  showClose: true
                });
              } else {
                this.$message({
                  message: '修改成功',
                  type: 'success',
                  showClose: true
                });
              }
              this.getCodeItemBanResult();
            });
        } else {
          return false;
        }
      });
    },

    //删除
    deletes() {
      if (this.rowList.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.$ajax
              .post(
                this.$apiUrls.CUD_CodeItemBanResult + '/Delete',
                this.rowList
              )
              .then((r) => {
                let { success } = r.data;
                if (!success) return;
                this.$message({
                  showClose: true,
                  message: '删除成功',
                  type: 'success'
                });
                this.getCodeItemBanResult();
              });
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    //打印
    prints() {
      if (this.rowList.length == 0) {
        return this.$message({
          message: '请选择至少一条数据进行操作！',
          type: 'warning',
          showClose: true
        });
      }
      setTimeout(() => {
        printJS('printHtml');
      }, 500);
    }
  }
};
</script>
<style lang="less" scoped>
.itemBanResult {
  display: flex;
  .left {
    width: 320px;
    background: white;
    display: flex;
    flex-direction: column;
    p {
      font-size: 16px;
      color: #2d3436;
      line-height: 48px;
      margin-left: 10px;
    }
    img {
      width: 16px;
    }
    /deep/.el-submenu__title {
      font-size: 16px;
      height: 30px;
      line-height: 30px;
      // background-color: #eee;
      // border-bottom: 1px solid #ccc;
    }
    .el-submenu .el-menu-item {
      height: 30px;
      line-height: 30px;
      transition: none;
    }
    .el-submenu .el-menu-item:focus,
    // .el-menu-item:hover,
    .el-menu-item.is-active {
      outline: 0;
      color: #fff;
      background-color: #1770df;
      border-radius: 4px;
    }
  }
  .left_content {
    flex: 1;
    overflow: auto;
  }
  .right {
    display: flex;
    flex-direction: column;
    flex: 1;
    flex-shrink: 0;
    // width: calc(100% - 360px);
    background: white;
    margin-left: 20px;
    padding: 0 15px 15px 15px;
  }
  /deep/.el-drawer__body {
    margin-right: 20px;
    .dialog-footer {
      text-align: right;
    }
  }
  .print_table {
    display: none;
  }
}
</style>
<style lang="less">
.itemBanResult {
  .el-menu--inline {
    // background: #cde6fa;
    // flex: 1;
    // width: calc(100% - 320px);
    background: white;
    // margin-left: 20px;
    padding: 0 4px;
  }
  .icon {
    margin-right: 8px;
    font-size: 18px;
  }
  .icons {
    font-size: 16px;
    margin-right: 8px;
  }
  .resize {
    cursor: col-resize;
    position: relative;
    top: 45%;
    background-color: #d6d6d6;
    border-radius: 5px;
    margin-top: -10px;
    width: 10px;
    height: 50px;
    background-size: cover;
    background-position: center;
    width: 10px;
    font-size: 32px;
    color: white;
  }

  .resize:hover {
    color: #444444;
  }
  .el-table {
    color: #2d3436;
  }
  /* 表格内容颜色 */
  .el-table th.el-table__cell {
    background-color: #d1e2f9;
    color: #717c86;
  }
  .el-table__body tbody tr:nth-child(even) td {
    background-color: #e7f0fb;
  }
}
</style>
