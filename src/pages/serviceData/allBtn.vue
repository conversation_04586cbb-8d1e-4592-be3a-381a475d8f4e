<template>
  <div class="allBtn">
    <header>
      <div class="searchWrap">
        <el-input
          clearable
          v-if="showSelect == false && hideSearch"
          v-model.trim="searchInfo"
          size="small"
          placeholder="请输入查找内容"
          class="search-input"
          @input="search"
        ></el-input>
        <el-select
          v-model="selectVal"
          v-if="showSelect == true && hideSearch"
          filterable
          remote
          reserve-keyword
          clearable
          size="small"
          placeholder="请输入关键词"
          :remote-method="remoteMethod"
          @change="selectChange"
          :loading="loading"
        >
          <el-option
            v-for="item in options"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          >
          </el-option>
        </el-select>
        <el-button
          size="small"
          class="blue_btn btn"
          @click="search"
          v-if="btnList.includes('查询')"
          icon="iconfont icon-search"
          >查询</el-button
        >
        <el-button
          v-if="btnList.includes('新建')"
          size="small"
          class="blue_btn btn"
          @click="creatClick"
          icon="iconfont icon-xinjian"
          >新建</el-button
        >
        <el-button
          v-if="btnList.includes('设置')"
          size="small"
          class="blue_btn btn"
          @click="setUp"
          icon="iconfont"
          >设置</el-button
        >
        <el-button
          size="small"
          class="red_btn btn"
          v-if="btnList.includes('删除')"
          @click="deletes"
          icon="iconfont icon-shanchu"
          >删除</el-button
        >
        <el-button
          size="small"
          class="blue_btn btn"
          v-if="btnList.includes('保存')"
          @click="save"
          icon="iconfont icon-baocun"
          >保存</el-button
        >
        <el-button
          size="small"
          class="green_btn btn"
          v-if="btnList.includes('打印')"
          @click="prints"
          icon="iconfont icon-dayin-"
          >打印</el-button
        >
        <el-button
          size="small"
          class="yellow_btn btn"
          v-if="btnList.includes('导出')"
          @click="exports"
          icon="iconfont icon-daochu"
          >导出</el-button
        >
        <el-button
          size="small"
          class="violet_btn btn"
          v-if="btnList.includes('跳转')"
          @click="jump"
          icon="iconfont icon-shuyi_lianjie"
          >跳转</el-button
        >
        <slot name="footAdd"></slot>
      </div>
    </header>
  </div>
</template>

<script>
export default {
  name: 'allBtn',
  props: {
    btnList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    //不显示搜索框
    hideSearch: {
      type: Boolean,
      default: () => {
        return true;
      }
    },
    // 下拉数据
    selectList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    showSelect: {
      type: Boolean,
      default: () => {
        return false;
      }
    },
    // 搜索
    methodSearch: {
      type: Function,
      default: null
    },
    // 设置
    methodSetup: {
      type: Function,
      default: null
    },
    // 删除
    methodDelete: {
      type: Function,
      default: null
    },
    // 打印
    methodPrint: {
      type: Function,
      default: null
    },
    // 导出
    methodExport: {
      type: Function,
      default: null
    },
    // 新建
    methodCreat: {
      type: Function,
      default: null
    },
    // 保存
    methodSava: {
      type: Function,
      default: null
    },
    // 跳转
    methodJump: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      searchInfo: '',
      options: [],
      selectVal: '',
      loading: false
    };
  },
  mounted: function () {},
  methods: {
    //查询
    search() {
      this.methodSearch();
    },
    //设置
    setUp() {
      this.methodSetup();
    },
    //删除
    deletes() {
      this.methodDelete();
    },
    //打印
    prints() {
      this.methodPrint();
    },
    //导出
    exports() {
      this.methodExport();
    },
    // 新建
    creatClick() {
      this.methodCreat();
    },
    // 跳转
    jump() {
      this.methodJump();
    },
    // 保存
    save() {
      this.methodSava();
    },
    // 远程搜索的回调
    remoteMethod(query) {
      if (query !== '') {
        this.loading = true;
        setTimeout(() => {
          this.loading = false;
          this.options = this.selectList.filter((item) => {
            return item.label.indexOf(query) > -1;
          });
        }, 200);
      } else {
        this.options = [];
      }
    },
    // 下拉值改变的回调
    selectChange() {
      this.$emit('selectChange', this.selectVal);
    }
  }
};
</script>
<style lang="less" scoped>
.allBtn {
  /deep/.el-container.is-vertical {
    padding-left: 20px;
  }
  header {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
    line-height: 60px;
    .el-input {
      width: 260px;
      margin-right: 10px;
    }
    .el-select {
      width: 260px;
      margin-right: 10px;
    }
  }
  .el-main {
    padding: 0;
  }
  .btn {
    padding: 6.5px 10px;
  }
}
</style>
