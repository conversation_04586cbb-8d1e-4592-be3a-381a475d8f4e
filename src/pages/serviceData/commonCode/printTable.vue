<template>
  <div class="printTable">
    <el-table
      :data="multipleSelection"
      tooltip-effect="dark"
      border
      style="width: 100%"
      height="100%"
    >
      <el-table-column label="序号" width="50" type="index"> </el-table-column>
      <el-table-column
        v-for="(item, index) in tableHeader"
        :key="index"
        :prop="item.property"
        :label="item.label"
      >
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'printTable',
  props: {
    multipleSelection: {
      // 表格数据
      type: Array,
      default: () => {
        return [];
      }
    },
    tableHeader: {
      // 表格头
      type: Array,
      require: true
    }
  },
  data() {
    return {};
  },
  mounted: function () {},
  methods: {
    //printing() {},
  }
};
</script>
<style lang="less" scoped>
.printTable {
  background: #fff;
  padding: 15px;
  width: 100%;
  display: flex;
  flex-direction: column;
  font-weight: 600;
}
</style>
