<template>
  <div class="departInformation">
    <!-- 科室信息 -->
    <el-container>
      <el-header>
        <AllBtn
          :btnList="['查询', '新建', '删除', '打印', '导出']"
          :methodCreat="add"
          :methodDelete="delMore"
          :methodSearch="search"
          :methodPrint="print"
          :methodExport="chooseExportExcel"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          @rowDblclick="handleClick"
          :cell_blue="cell_blue"
          @selectionChange="handleSelectChangeTable"
          :tableLoading.sync="loading"
          isCheck
          :columnWidth="{ deptCode: 100, deptName: 180 }"
        >
          <template #deptCls="{ scope }">
            <div>
              {{ enum_abnormity[scope.row.deptCls] }}
            </div>
          </template>
          <template #template="{ scope }">
            <div>
              {{ enum_template[scope.row.template] }}
            </div>
          </template>
          <template #columnRight>
            <el-table-column
              prop="operation"
              label="操作"
              width="118"
              fixed="right"
            >
              <template slot-scope="scope">
                <el-button
                  type="primary"
                  plain
                  size="mini"
                  class="view"
                  @click="setUpCommonWords(scope.row)"
                  >设置常用词</el-button
                >
              </template>
            </el-table-column>
          </template>
        </PublicTable>
      </el-main>
    </el-container>
    <el-drawer
      title="科室信息属性"
      :visible.sync="drawer"
      :before-close="handleClose"
      :wrapperClosable="false"
    >
      <el-form :model="popupForm" ref="ruleForm" :rules="rules">
        <el-form-item
          v-if="funTpye !== '/Create'"
          label="科室代码"
          :label-width="formLabelWidth"
          prop="deptCode"
        >
          <el-input
            v-model.trim="popupForm.deptCode"
            autocomplete="off"
            size="small"
            placeholder="请输入科室代码"
            :disabled="funTpye !== '/Create'"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="His代码"
          :label-width="formLabelWidth"
          prop="hisCode"
        >
          <el-input
            v-model.trim="popupForm.hisCode"
            autocomplete="off"
            size="small"
            placeholder="请输入His代码"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="科室名称"
          :label-width="formLabelWidth"
          prop="deptName"
        >
          <el-input
            v-model.trim="popupForm.deptName"
            autocomplete="off"
            size="small"
            placeholder="请输入科室名称"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="科室类型"
          :label-width="formLabelWidth"
          prop="deptCls"
        >
          <el-radio-group v-model.trim="popupForm.deptCls">
            <el-radio
              :label="item.value"
              :key="item.value"
              v-for="item in options"
              >{{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="科室描述"
          :label-width="formLabelWidth"
          prop="deptInfo"
        >
          <el-input
            v-model.trim="popupForm.deptInfo"
            autocomplete="off"
            size="small"
            placeholder="请输入科室描述"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="医生工作站模板"
          :label-width="formLabelWidth"
          prop="template"
        >
          <el-radio-group v-model.trim="popupForm.template">
            <el-tooltip
              class="item"
              v-for="item in templateList"
              :key="item.value"
              effect="dark"
              :content="item.note"
              placement="bottom"
            >
              <el-radio :label="item.value" :key="item.value"
                >{{ item.label }}
              </el-radio>
            </el-tooltip>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="工作站表头配置"
          :label-width="formLabelWidth"
          prop="template"
        >
          <el-checkbox v-model="popupForm.isShowUnits"
            >是否显示单位</el-checkbox
          >
          <el-checkbox v-model="popupForm.isShowReferenceRange"
            >是否显示参考范围</el-checkbox
          >
          <el-checkbox v-model="popupForm.isShowLastResult"
            >是否显示上次的结果</el-checkbox
          >
        </el-form-item>
        <el-form-item
          label="显示顺序"
          :label-width="formLabelWidth"
          prop="sortIndex"
        >
          <el-input
            v-model.trim="popupForm.sortIndex"
            autocomplete="off"
            size="small"
            placeholder="请输入显示顺序"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="拼音码"
          :label-width="formLabelWidth"
          prop="pinYinCode"
        >
          <el-input
            v-model.trim="popupForm.pinYinCode"
            autocomplete="off"
            size="small"
            placeholder="请输入拼音码"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="五笔码"
          :label-width="formLabelWidth"
          prop="wuBiCode"
        >
          <el-input
            v-model.trim="popupForm.wuBiCode"
            autocomplete="off"
            size="small"
            placeholder="请输入五笔码"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="自定义码"
          :label-width="formLabelWidth"
          prop="customCode"
        >
          <el-input
            v-model.trim="popupForm.customCode"
            autocomplete="off"
            size="small"
            placeholder="请输入自定义码"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="科室备注"
          :label-width="formLabelWidth"
          prop="deptNote"
        >
          <el-input
            v-model.trim="popupForm.deptNote"
            type="textarea"
            autocomplete="off"
            size="small"
            placeholder="请输入科室备注"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="drawer = false" size="small">取消</el-button>
        <el-button class="blue_btn" @click="submit" size="small"
          >保存</el-button
        >
      </div>
    </el-drawer>
    <el-drawer
      :title="`${wordsRow.deptName}常用词设置`"
      :visible.sync="showCommonWords"
      :before-close="handleClose"
      :wrapperClosable="false"
    >
      <el-form :model="wordsForm" ref="wordsForm" :rules="rules">
        <el-form-item
          label="常用词"
          :label-width="formLabelWidth"
          prop="commonWords"
        >
          <el-input
            v-model.trim="wordsForm.commonWords"
            autocomplete="off"
            size="small"
            placeholder="请输入常用词"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <!-- <el-button @click="showCommonWords = false" size="small"
          >取消</el-button
        > -->
        <el-button class="blue_btn" @click="saveCommonWords" size="small"
          >保存</el-button
        >
      </div>
      <div class="commonWords">
        <h3>常用词：</h3>
        <div class="commonWords-list">
          <PublicTable
            :viewTableList.sync="commonWordsData"
            :theads.sync="commonWordsTheads"
          >
            <template #operation="{ scope }">
              <el-button
                type="danger"
                plain
                size="mini"
                @click="deleteCommonWords(scope.row)"
                >删除</el-button
              >
            </template>
          </PublicTable>
        </div>
      </div>
    </el-drawer>
    <div style="display: none" id="printHtml">
      <printTable
        :tableHeader="tableHeader"
        :multipleSelection="multipleSelection"
        ref="print"
      ></printTable>
    </div>
  </div>
</template>

<script>
import { dataUtils } from '@/common';
import printTable from './printTable';
import Print from '@/common/print';
import PublicTable from '../../../components/publicTable.vue';
import AllBtn from '../allBtn.vue';
export default {
  name: 'departInformation',
  components: { printTable, PublicTable, AllBtn },
  data() {
    return {
      showCommonWords: false,
      wordsRow: {},
      formInline: '',
      deptCodeArr: [],
      loading: false,
      excelData: [],
      multipleSelection: [],
      excelName: '',
      tableDataCopy: [],
      tableData: [],
      commonWordsData: [],
      pageSize: 50,
      currentPage: 1,
      drawer: false,
      funTpye: '/Create',
      formLabelWidth: '126px',
      theads: {
        deptCode: '科室代码',
        deptName: '科室名称',
        deptCls: '科室类型',
        // dutyCode: "科室负责人",
        template: '模板',
        deptInfo: '科室描述',
        deptNote: '备注',
        sortIndex: '显示顺序',
        pinYinCode: '拼音码',
        wuBiCode: '五笔码',
        customCode: '自定义码'
      },
      commonWordsTheads: {
        words: '词条',
        operation: '操作'
      },
      cell_blue: ['deptName'],
      enum_abnormity: {
        1: '检查科室',
        2: '检验科室',
        3: '功能科室'
      },
      enum_template: {
        1: '文本',
        2: '标签',
        3: '多选'
      },
      templateList: [
        {
          value: 1,
          label: '单结果',
          note: '录入单个结果'
        },
        {
          value: 2,
          label: '多结果',
          note: '弹出标签化的候选结果与录入多个结果'
        },
        {
          value: 3,
          label: '影像结果',
          note: '针对影像的结果录入（第三方系统默认选择“单结果”）'
        }
      ],
      options: [
        {
          value: '1',
          label: '检查科室'
        },
        {
          value: '2',
          label: '检验科室'
        },
        {
          value: '3',
          label: '功能科室'
        }
      ],
      popupForm: {
        deptCode: '',
        deptName: '',
        deptCls: '',
        dutyCode: '',
        deptNote: '',
        sortIndex: '',
        pinYinCode: '',
        wuBiCode: '',
        customCode: '',
        template: '',
        deptInfo: '',
        hospCode: '',
        hisCode: '',
        isShowUnits: true,
        isShowReferenceRange: true,
        isShowLastResult: true
      },
      wordsForm: {
        commonWords: ''
      },
      rules: {
        // deptCode: [
        //   { required: true, message: "请输入科室代码", trigger: "blur" }
        // ],
        deptName: [
          { required: true, message: '请输入科室名称', trigger: 'blur' }
        ],
        hisCode: [
          { required: true, message: '请输入His代码', trigger: 'blur' }
        ],
        template: [
          { required: true, message: '请选择工作站模板', trigger: 'blur' }
        ],
        commonWords: [
          { required: true, message: '请输入常用词', trigger: 'blur' }
        ]
      },
      tableHeader: [
        {
          label: '科室代码',
          property: 'deptCode'
        },
        {
          label: '科室名称',
          property: 'deptName'
        },
        {
          label: '科室类型',
          property: 'deptCls'
        },
        {
          label: '科室描述',
          property: 'dutyCode'
        },
        {
          label: '备注',
          property: 'deptNote'
        },
        {
          label: '显示顺序',
          property: 'sortIndex'
        },
        {
          label: '拼音码',
          property: 'pinYinCode'
        },
        {
          label: '五笔码',
          property: 'wuBiCode'
        },
        {
          label: '自定义码',
          property: 'customCode'
        }
      ]
    };
  },
  created: function () {},
  mounted: function () {
    this.getTableData(); //默认查询
    this.excelName = '科室信息' + dataUtils.getNowDateTiemNo();
  },
  methods: {
    // 获取科室常用词
    getDeptWord(code) {
      this.$ajax
        .post(this.$apiUrls.ReadCodeDeptWord, '', {
          query: {
            deptCode: code
          }
        })
        .then((r) => {
          console.log('r: ', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.commonWordsData = returnData || [];
        });
    },
    // 设置常用词按钮
    setUpCommonWords(val) {
      this.showCommonWords = true;
      this.getDeptWord(val.deptCode);
      this.wordsRow = val;
    },
    // 保存常用词
    saveCommonWords() {
      this.$refs.wordsForm.validate((valid) => {
        if (valid) {
          let data = {
            deptCode: this.wordsRow.deptCode,
            words: this.wordsForm.commonWords
          };
          this.$ajax.post(this.$apiUrls.CreateCodeDeptWord, data).then((r) => {
            console.log('r: ', r);
            let { success, returnData } = r.data;
            if (!success) return;
            this.$message({
              message: '新增常用词成功!',
              type: 'success',
              showClose: true
            });
            this.getDeptWord(this.wordsRow.deptCode);
            this.wordsForm = {};
          });
        }
      });
    },
    // 删除常用词
    deleteCommonWords(val) {
      this.$confirm('是否确认删除这个常用词?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          let data = [];
          data.push(val);
          this.$ajax.post(this.$apiUrls.DeleteCodeDeptWord, data).then((r) => {
            console.log('r: ', r);
            let { success, returnData } = r.data;
            if (!success) return;
            this.$message({
              message: '删除常用词成功!',
              type: 'success',
              showClose: true
            });
            this.getDeptWord(this.wordsRow.deptCode);
          });
        })
        .catch(() => {
          return;
        });
    },
    handleClose() {
      this.drawer = false;
      this.showCommonWords = false;
    },
    //模糊查询
    search() {
      this.formInline = this.$refs.allBtn_Ref.searchInfo.trim(); //获取组件文本框的值
      if (this.formInline.length > 0) {
        this.tableData = this.tableDataCopy.filter((item) => {
          return (
            item.deptCode.includes(this.formInline) ||
            item.deptName.includes(this.formInline) ||
            item.pinYinCode.includes(this.formInline) ||
            item.wuBiCode.includes(this.formInline)
          );
        });
      } else {
        this.getTableData();
      }
    },
    //查询
    getTableData() {
      this.loading = true;
      this.$ajax
        .post(this.$apiUrls.RD_CodeDepartment + '/Read', [])
        .then((r) => {
          this.loading = false;
          this.tableData = r.data.returnData;
          this.tableDataCopy = r.data.returnData;
        });
    },
    //新增
    add() {
      this.drawer = true;
      this.funTpye = '/Create';
      this.$nextTick(function () {
        this.$refs.ruleForm.resetFields();
        this.popupForm.isShowUnits = true;
        this.popupForm.isShowReferenceRange = true;
        this.popupForm.isShowLastResult = true;
      });
    },
    //编辑
    handleClick(row) {
      this.drawer = true;
      this.funTpye = '/Update';
      this.$nextTick(() => {
        this.$refs.ruleForm.resetFields();
        this.popupForm = {
          deptCode: row.deptCode,
          deptName: row.deptName,
          deptCls: row.deptCls,
          deptInfo: row.deptInfo,
          deptNote: row.deptNote,
          sortIndex: row.sortIndex,
          pinYinCode: row.pinYinCode,
          wuBiCode: row.wuBiCode,
          customCode: row.customCode,
          template: row.template,
          hisCode: row.hisCode,
          isShowUnits: row.isShowUnits,
          isShowReferenceRange: row.isShowReferenceRange,
          isShowLastResult: row.isShowLastResult
        };
      });
    },
    //提交
    submit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$ajax
            .post(
              this.$apiUrls.CU_CodeDepartment + this.funTpye,
              this.popupForm
            )
            .then((r) => {
              r = r.data;
              if (!r.success) {
                return;
              }
              this.tableData = [];
              this.drawer = false;
              this.$nextTick(() => {
                this.$refs.allBtn_Ref.searchInfo = '';
                this.search();
              });
              if (this.funTpye == '/Create') {
                this.$message({
                  message: '新建成功',
                  type: 'success',
                  showClose: true
                });
              } else {
                this.$message({
                  message: '修改成功',
                  type: 'success',
                  showClose: true
                });
              }
            });
        } else {
          return false;
        }
      });
    },
    //复选框勾选操作
    handleSelectChangeTable: function (val) {
      //console.log(val);
      this.multipleSelection = val;
      this.deptCodeArr = [];
      val.map((item, i) => {
        if (item.deptCode != '') {
          this.deptCodeArr.push(item.deptCode);
        }
      });
      //console.log(this.deptCodeArr);
    },
    //多条删除
    delMore() {
      if (this.deptCodeArr.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据!',
          type: 'warning'
        });
      }
      if (this.deptCodeArr.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.delete(this.deptCodeArr);
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    //确认删除
    delete(deptCodeArr) {
      this.$ajax
        .post(this.$apiUrls.RD_CodeDepartment + '/Delete', deptCodeArr)
        .then((r) => {
          let { success } = r.data;
          if (!success) return;
          this.$message({
            showClose: true,
            message: '删除成功',
            type: 'success'
          });
          this.$nextTick(() => {
            this.$refs.allBtn_Ref.searchInfo = '';
            this.search();
          });
        });
    },
    //打印
    print() {
      if (this.multipleSelection.length == 0) {
        return this.$message({
          message: '请选择至少一条数据进行操作！',
          type: 'warning'
        });
      }
      const printData = document.getElementById('printHtml').innerHTML;
      Print(printData);
      // this.$refs.print.printing();
    },
    // 选中导出
    chooseExportExcel() {
      if (this.multipleSelection.length == 0) {
        return this.$message({
          message: '请选择至少一条数据进行操作！',
          type: 'warning'
        });
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.excelData = this.multipleSelection; // multipleSelection是一个数组，存储表格中选择的行的数据。
          this.excelData.map((item, i) => {
            item.index = i + 1;
          });
          this.$nextTick(function () {
            this.export2Excel();
          });
        })
        .catch(() => {});
    },
    // 数据写入excel
    export2Excel() {
      var that = this;
      require.ensure([], () => {
        const { export_json_to_excel } = require('@/utils/Export2Excel'); // 这里必须使用绝对路径，使用@/+存放export2Excel的路径
        const tHeader = [
          '序号',
          '科室代码',
          '科室名称',
          '科室类型',
          '科室描述',
          '备注',
          '显示顺序',
          '拼音码',
          '五笔码',
          '自定义码'
        ]; // 导出的表头名信息
        const filterVal = [
          'index',
          'deptCode',
          'deptName',
          'deptCls',
          'dutyCode',
          'deptNote',
          'sortIndex',
          'pinYinCode',
          'wuBiCode',
          'customCode'
        ]; // 导出的表头字段名
        const list = that.excelData;
        const data = that.formatJson(filterVal, list);
        const name = that.excelName;
        export_json_to_excel(tHeader, data, name);
      });
    },
    // 格式转换
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) => filterVal.map((j) => v[j]));
    }
  }
};
</script>
<style lang="less" scoped>
.departInformation {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-weight: 600;
  header {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
    .el-form-item {
      margin-bottom: 10px;
    }
    .el-button--small {
      width: 64px;
    }
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-container.is-vertical {
    height: 100%;
  }
  .el-header,
  .el-main {
    padding: 0;
  }

  .searchWrap .el-form-item:last-child {
    margin-right: 0;
    margin-left: 10px;
  }
  .indexPage .contentWrap .tabPage {
    margin-top: 0;
  }
  .el-pagination {
    text-align: center;
  }
  .el-drawer.rtl {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #2d3436;
    padding: 0 20px 0 10px;
  }
  .el-drawer__body {
    padding-right: 20px;

    .el-form-item {
      margin-bottom: 18px;
    }
  }
  .commonWords {
    padding: 18px 0 18px 18px;
    height: 600px;
  }
  .commonWords-list {
    margin-top: 18px;
    height: 100%;
  }
}
</style>
<style>
.el-dialog {
  width: 480px;
}
.el-table .el-table__cell {
  padding: 5px 0;
}
.el-main .el-checkbox__inner {
  width: 20px;
  height: 20px;
}
.el-drawer__body {
  padding-right: 20px;
}
.el-drawer__header,
.el-form-item__label {
  color: #2d3436;
}
</style>
