<template>
  <div class="checkPlaceInfo">
    <!-- 采集地点信息 -->
    <el-container>
      <el-header>
        <AllBtn
          :btnList="['查询', '新建', '删除', '打印', '导出']"
          :methodCreat="add"
          :methodDelete="delMore"
          :methodSearch="search"
          :methodPrint="print"
          :methodExport="chooseExportExcel"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          @rowDblclick="handleClick"
          :cell_blue="cell_blue"
          @selectionChange="handleSelectChangeTable"
          :tableLoading.sync="loading"
          isCheck
        >
        </PublicTable>
      </el-main>
    </el-container>
    <el-drawer
      title="检查地点信息属性"
      :visible.sync="drawer"
      :before-close="handleClose"
      :wrapperClosable="false"
    >
      <el-form :model="popupForm" ref="ruleForm" :rules="rules">
        <el-form-item
          v-if="funTpye !== '/Create'"
          label="地点代码"
          :label-width="formLabelWidth"
          prop="placeCode"
        >
          <el-input
            v-model.trim="popupForm.placeCode"
            autocomplete="off"
            size="small"
            placeholder="请输入地点代码"
            :disabled="funTpye !== '/Create'"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="地点名称"
          :label-width="formLabelWidth"
          prop="placeName"
        >
          <el-input
            v-model.trim="popupForm.placeName"
            autocomplete="off"
            size="small"
            placeholder="请输入地点名称"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="地点描述"
          :label-width="formLabelWidth"
          prop="note"
        >
          <el-input
            v-model.trim="popupForm.note"
            type="textarea"
            autocomplete="off"
            size="small"
            placeholder="请输入地点描述"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="drawer = false" size="small">取消</el-button>
        <el-button class="blue_btn" size="small" @click="submit"
          >保存</el-button
        >
      </div>
    </el-drawer>
    <div style="display: none; width: 100%" id="printHtml">
      <printTable
        :tableHeader="tableHeader"
        :multipleSelection="multipleSelection"
        ref="print"
      ></printTable>
    </div>
  </div>
</template>

<script>
import { dataUtils } from '@/common';
import printTable from './printTable';
import Print from '@/common/print';
import PublicTable from '../../../components/publicTable.vue';
import AllBtn from '../allBtn.vue';
export default {
  name: 'checkPlaceInfo',
  components: { printTable, PublicTable, AllBtn },
  data() {
    return {
      formInline: '',
      placeCodeArr: [],
      loading: false,
      tableDataCopy: [],
      tableData: [],
      pageSize: 50,
      currentPage: 1,
      excelData: [],
      multipleSelection: [],
      excelName: '',
      drawer: false,
      funTpye: '/Create',
      formLabelWidth: '120px',
      cell_blue: ['placeName'],
      theads: {
        placeCode: '地点代码',
        placeName: '地点名称',
        note: '地点描述'
      },
      popupForm: {
        placeCode: '',
        placeName: '',
        note: ''
      },
      rules: {
        // placeCode: [
        //   { required: true, message: "请输入地点代码", trigger: "blur" }
        // ],
        placeName: [
          { required: true, message: '请输入地点名称', trigger: 'blur' }
        ]
      },
      tableHeader: [
        {
          label: '地点代码',
          property: 'placeCode'
        },
        {
          label: '地点名称',
          property: 'placeName'
        },
        {
          label: '地点描述',
          property: 'note'
        }
      ]
    };
  },
  created: function () {},
  mounted: function () {
    this.getTableData(); //默认查询
    this.excelName = '检查地址信息' + dataUtils.getNowDateTiemNo();
  },
  methods: {
    handleClose() {
      this.drawer = false;
    },
    //模糊查询
    search() {
      this.formInline = this.$refs.allBtn_Ref.searchInfo.trim(); //获取组件文本框的值
      if (this.formInline) {
        this.tableData = this.tableDataCopy.filter((item) => {
          return (
            item.placeCode.indexOf(this.formInline) !== -1 ||
            item.placeName.indexOf(this.formInline) !== -1
          );
        });
      } else {
        this.getTableData();
      }
    },
    //查询
    getTableData() {
      this.loading = true;
      this.$ajax
        .post(this.$apiUrls.RD_CodeGatherPlace + '/Read', [])
        .then((r) => {
          this.loading = false;
          this.tableData = r.data.returnData;
          this.tableDataCopy = r.data.returnData;
        });
    },

    //新增
    add() {
      this.drawer = true;
      this.funTpye = '/Create';
      this.$nextTick(function () {
        this.$refs.ruleForm.resetFields();
      });
    },

    //编辑
    handleClick(row) {
      console.log('row', row);
      this.drawer = true;
      this.funTpye = '/Update';
      this.$nextTick(() => {
        this.$refs.ruleForm.resetFields();
        this.popupForm = {
          placeCode: row.placeCode,
          placeName: row.placeName,
          note: row.note
        };
      });
    },

    //提交
    submit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$ajax
            .post(
              this.$apiUrls.CU_CodeGatherPlace + this.funTpye,
              this.popupForm
            )
            .then((r) => {
              r = r.data;
              if (!r.success) {
                return;
              }
              this.tableData = [];
              this.drawer = false;
              this.$nextTick(() => {
                this.$refs.allBtn_Ref.searchInfo = '';
                this.search();
              });

              if (this.funTpye == '/Create') {
                this.$message({
                  message: '新建成功',
                  type: 'success',
                  showClose: true
                });
              } else {
                this.$message({
                  message: '修改成功',
                  type: 'success',
                  showClose: true
                });
              }
            });
        } else {
          return false;
        }
      });
    },
    //复选框勾选操作
    handleSelectChangeTable: function (val) {
      console.log(val);
      this.multipleSelection = val;
      this.placeCodeArr = [];
      val.map((item, i) => {
        if (item.placeCode != '') {
          this.placeCodeArr.push(item.placeCode);
        }
      });
      //console.log(this.placeCodeArr);
    },
    //多条删除
    delMore() {
      if (this.placeCodeArr.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据!',
          type: 'warning'
        });
      }
      if (this.placeCodeArr.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.delete(this.placeCodeArr);
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    //确认删除
    delete(placeCodeArr) {
      this.$ajax
        .post(this.$apiUrls.RD_CodeGatherPlace + '/Delete', placeCodeArr)
        .then((r) => {
          let { success } = r.data;
          if (!success) return;
          this.$message({
            showClose: true,
            message: '删除成功',
            type: 'success'
          });
          this.$nextTick(() => {
            this.$refs.allBtn_Ref.searchInfo = '';
            this.search();
          });
        });
    },
    //打印
    print() {
      if (this.multipleSelection.length == 0) {
        return this.$message({
          message: '请选择至少一条数据进行操作！',
          type: 'warning'
        });
      }
      const printData = document.getElementById('printHtml').innerHTML;
      Print(printData);
      // this.$refs.print.printing();
    },
    // 选中导出
    chooseExportExcel() {
      if (this.multipleSelection.length == 0) {
        return this.$message({
          message: '请选择至少一条数据进行操作！',
          type: 'warning'
        });
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.excelData = this.multipleSelection; // multipleSelection是一个数组，存储表格中选择的行的数据。
          this.excelData.map((item, i) => {
            item.index = i + 1;
          });
          this.$nextTick(function () {
            this.export2Excel();
          });
        })
        .catch(() => {});
    },
    // 数据写入excel
    export2Excel() {
      var that = this;
      require.ensure([], () => {
        const { export_json_to_excel } = require('@/utils/Export2Excel'); // 这里必须使用绝对路径，使用@/+存放export2Excel的路径
        const tHeader = ['序号', '地点代码', '地点名称', '地点描述']; // 导出的表头名信息
        const filterVal = ['index', 'placeCode', 'placeName', 'note']; // 导出的表头字段名
        const list = that.excelData;
        const data = that.formatJson(filterVal, list);
        console.log(data);
        const name = that.excelName;
        export_json_to_excel(tHeader, data, name);
      });
    },
    // 格式转换
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) => filterVal.map((j) => v[j]));
    }
  }
};
</script>
<style lang="less" scoped>
.checkPlaceInfo {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-weight: 600;
  header {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
    // .el-form-item {
    //   margin-bottom: 10px;
    // }
    .el-button--small {
      width: 64px;
    }
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-container.is-vertical {
    height: 100%;
  }
  .el-header,
  .el-main {
    padding: 0;
  }

  .searchWrap .el-form-item:last-child {
    margin-right: 0;
    margin-left: 10px;
  }
  .indexPage .contentWrap .tabPage {
    margin-top: 0;
  }
  .el-pagination {
    text-align: center;
  }
  .el-drawer.rtl {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #2d3436;
    padding: 0 20px 0 10px;
  }
  .el-drawer__body {
    padding-right: 20px;

    // .el-form-item {
    //   margin-bottom: 10px;
    // }
  }
}
</style>
<style>
.el-dialog {
  width: 480px;
}
.el-table .el-table__cell {
  padding: 5px 0;
}
.el-main .el-checkbox__inner {
  width: 20px;
  height: 20px;
}
.el-drawer__body {
  padding-right: 20px;
}
.el-drawer__header,
.el-form-item__label {
  color: #2d3436;
}
</style>
