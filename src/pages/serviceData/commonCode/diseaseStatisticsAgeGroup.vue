<template>
  <div class="sampleInfo">
    <el-container>
      <el-header>
        <AllBtn
          :btnList="['查询', '新建', '删除', '打印', '导出']"
          :methodCreat="add"
          :methodDelete="delMore"
          :methodSearch="search"
          :methodPrint="print"
          :methodExport="chooseExportExcel"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          @rowDblclick="handleClick"
          :cell_blue="cell_blue"
          @selectionChange="handleSelectChangeTable"
          :tableLoading.sync="loading"
          isCheck
        >
        </PublicTable>
      </el-main>
    </el-container>
    <el-drawer
      title="疾病统计年龄段模版"
      :visible.sync="drawer"
      :before-close="handleClose"
      :wrapperClosable="false"
    >
      <el-form :model="popupForm" ref="ruleForm" :rules="rules">
        <el-form-item
          label="排序"
          :label-width="formLabelWidth"
          prop="sortIndex"
        >
          <el-input
            v-model.trim="popupForm.sortIndex"
            autocomplete="off"
            size="small"
            placeholder="请输入排序"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="代码"
          :label-width="formLabelWidth"
          prop="rangeCode"
        >
          <el-input
            v-model.trim="popupForm.rangeCode"
            autocomplete="off"
            size="small"
            placeholder="请输入代码"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="名称"
          :label-width="formLabelWidth"
          prop="rangeName"
        >
          <el-input
            v-model.trim="popupForm.rangeName"
            autocomplete="off"
            size="small"
            placeholder="请输入名称"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="年龄下限"
          :label-width="formLabelWidth"
          prop="ageLowerLimit"
        >
          <el-input-number
            :controls="false"
            :step="1"
            :min="0"
            :max="100"
            :precision="0"
            v-model.trim="popupForm.ageLowerLimit"
            autocomplete="off"
            size="small"
            placeholder="请输入年龄下限"
          ></el-input-number>
        </el-form-item>
        <el-form-item
          label="年龄上限"
          :label-width="formLabelWidth"
          prop="ageUpperLimit"
        >
          <el-input-number
            :controls="false"
            :step="1"
            :min="0"
            :max="100"
            :precision="0"
            v-model.trim="popupForm.ageUpperLimit"
            autocomplete="off"
            size="small"
            placeholder="请输入年龄上限"
          ></el-input-number>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="drawer = false" size="small">取消</el-button>
        <el-button class="blue_btn" @click="submit" size="small"
          >保存</el-button
        >
      </div>
    </el-drawer>
    <div style="display: none" id="printHtml">
      <printTable
        :tableHeader="tableHeader"
        :multipleSelection="multipleSelection"
        ref="print"
      ></printTable>
    </div>
  </div>
</template>
<script>
import { dataUtils } from '@/common';
import printTable from './printTable';
import Print from '@/common/print';
import PublicTable from '../../../components/publicTable.vue';
import AllBtn from '../allBtn.vue';
export default {
  name: 'diseaseStatisticsAgeGroup',
  components: { printTable, PublicTable, AllBtn },
  data() {
    return {
      formInline: '',
      sampCodeArr: [],
      loading: false,
      excelData: [],
      multipleSelection: [],
      excelName: '',
      tableDataCopy: [],
      tableData: [],
      pageSize: 50,
      currentPage: 1,
      drawer: false,
      isAdd: true,
      formLabelWidth: '120px',
      popupForm: {
        sortIndex: 9999,
        crangeCode: '',
        rangeName: '',
        ageLowerLimit: undefined,
        ageUpperLimit: undefined
      },
      theads: {
        sortIndex: '排序',
        rangeCode: '代码',
        rangeName: '名称',
        ageLowerLimit: '年龄下限',
        ageUpperLimit: '年龄上限'
      },
      cell_blue: ['content'],
      rules: {
        rangeCode: [{ required: true, message: '请输入代码', trigger: 'blur' }],
        rangeName: [{ required: true, message: '请输入名字', trigger: 'blur' }],
        ageLowerLimit: [
          { required: true, message: '请输入年龄下线', trigger: 'blur' }
        ],
        ageUpperLimit: [
          { required: true, message: '请输入年龄上限', trigger: 'blur' }
        ]
      },
      tableHeader: [
        {
          label: '排序',
          property: 'sortIndex'
        },
        {
          label: '代码',
          property: 'rangeCode'
        },
        {
          label: '名称',
          property: 'rangeName'
        },
        {
          label: '年龄下限',
          property: 'ageLowerLimit'
        },
        {
          label: '年龄上限',
          property: 'ageUpperLimit'
        }
      ]
    };
  },
  created: function () {},
  mounted: function () {
    this.getTableData(); //默认查询
    this.excelName = '疾病统计年龄段' + dataUtils.getNowDateTiemNo();
  },
  methods: {
    handleClose() {
      this.drawer = false;
    },
    //模糊查询
    search() {
      this.formInline = this.$refs.allBtn_Ref.searchInfo.trim(); //获取组件文本框的值
      if (this.formInline) {
        this.tableData = this.tableDataCopy.filter((item) => {
          return item.content.indexOf(this.formInline) !== -1;
        });
      } else {
        this.getTableData();
      }
      //this.loading = false;
    },
    //查询
    getTableData() {
      this.loading = true;
      this.$ajax.post(this.$apiUrls.ReadCodeDiseaseStatAgeRange).then((r) => {
        this.tableData = r.data.returnData;
        this.tableDataCopy = r.data.returnData;
        this.loading = false;
      });
    },

    //新增
    add() {
      this.drawer = true;
      this.isAdd = true;
      this.$nextTick(function () {
        this.$refs.ruleForm.resetFields();
        this.popupForm = {
          sortIndex: 9999,
          rangeCode: '',
          rangeName: '',
          ageLowerLimit: undefined,
          ageUpperLimit: undefined
        };
      });
    },
    //编辑
    handleClick(row) {
      console.log('row', row);
      this.drawer = true;
      this.isAdd = false;
      this.$nextTick(() => {
        this.$refs.ruleForm.resetFields();
        this.popupForm = {
          sortIndex: row.sortIndex,
          rangeCode: row.rangeCode,
          rangeName: row.rangeName,
          ageLowerLimit: row.ageLowerLimit,
          ageUpperLimit: row.ageUpperLimit
        };
      });
    },
    //提交
    submit() {
      let url = this.isAdd
        ? this.$apiUrls.CreateCodeDiseaseStatAgeRange
        : this.$apiUrls.UpdateCodeDiseaseStatAgeRange;
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.popupForm.ageLowerLimit >= this.popupForm.ageUpperLimit) {
            this.$message({
              message: '年龄下限必须小于年龄上限',
              type: 'warning'
            });
            return false;
          }
          this.$ajax.post(url, this.popupForm).then((r) => {
            r = r.data;
            if (!r.success) {
              return;
            }
            this.tableData = [];
            this.drawer = false;
            this.$nextTick(() => {
              this.$refs.allBtn_Ref.searchInfo = '';
              this.search();
            });
            if (this.isAdd) {
              this.$message({
                message: '新建成功',
                type: 'success',
                showClose: true
              });
            } else {
              this.$message({
                message: '修改成功',
                type: 'success',
                showClose: true
              });
            }
          });
        } else {
          return false;
        }
      });
    },
    //复选框勾选操作
    handleSelectChangeTable: function (val) {
      //console.log(val);
      this.multipleSelection = val;
      this.sampCodeArr = [];
      val.map((item, i) => {
        this.sampCodeArr.push(item);
      });
      //console.log(this.sampCodeArr);
    },
    //多条删除
    delMore() {
      if (this.sampCodeArr.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据!',
          type: 'warning'
        });
      }
      if (this.sampCodeArr.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.delete(this.sampCodeArr);
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    //确认删除
    delete(sampCodeArr) {
      this.$ajax
        .post(this.$apiUrls.DeleteCodeDiseaseStatAgeRange, sampCodeArr)
        .then((r) => {
          let { success } = r.data;
          if (!success) return;
          this.$message({
            showClose: true,
            message: '删除成功',
            type: 'success'
          });
          this.$nextTick(() => {
            this.$refs.allBtn_Ref.searchInfo = '';
            this.search();
          });
        });
    },
    //打印
    print() {
      if (this.multipleSelection.length == 0) {
        return this.$message({
          message: '请选择至少一条数据进行操作！',
          type: 'warning'
        });
      }
      const printData = document.getElementById('printHtml').innerHTML;
      Print(printData);
      // this.$refs.print.printing();
    },
    // 选中导出
    chooseExportExcel() {
      if (this.multipleSelection.length == 0) {
        return this.$message({
          message: '请选择至少一条数据进行操作！',
          type: 'warning'
        });
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.excelData = this.multipleSelection; // multipleSelection是一个数组，存储表格中选择的行的数据。
          this.excelData.map((item, i) => {
            item.index = i + 1;
          });
          this.$nextTick(function () {
            this.export2Excel();
          });
        })
        .catch(() => {});
    },
    // 数据写入excel
    export2Excel() {
      var that = this;
      require.ensure([], () => {
        const { export_json_to_excel } = require('@/utils/Export2Excel'); // 这里必须使用绝对路径，使用@/+存放export2Excel的路径
        const tHeader = ['序号', '代码', '名称', '年龄下限', '年龄上限']; // 导出的表头名信息
        const filterVal = [
          'index',
          'rangeCode',
          'rangeName',
          'ageLowerLimit',
          'ageUpperLimit'
        ]; // 导出的表头字段名
        const list = that.excelData;
        const data = that.formatJson(filterVal, list);
        const name = that.excelName;
        export_json_to_excel(tHeader, data, name);
      });
    },
    // 格式转换
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) => filterVal.map((j) => v[j]));
    }
  }
};
</script>
<style lang="less" scoped>
.sampleInfo {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-weight: 600;

  header {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;

    .el-form-item {
      margin-bottom: 18px;
    }

    .el-button--small {
      width: 64px;
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }

  .el-container.is-vertical {
    height: 100%;
  }

  .el-header,
  .el-main {
    padding: 0;
  }

  .searchWrap .el-form-item:last-child {
    margin-right: 0;
    margin-left: 10px;
  }

  .indexPage .contentWrap .tabPage {
    margin-top: 0;
  }

  .el-pagisample {
    text-align: center;
  }

  .el-drawer.rtl {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #2d3436;
    padding: 0 20px 0 10px;
  }

  .el-drawer__body {
    padding-right: 20px;

    .el-form-item {
      margin-bottom: 18px;
    }
  }
}
</style>
<style>
.el-dialog {
  width: 480px;
}

.el-table .el-table__cell {
  padding: 5px 0;
}

.el-main .el-checkbox__inner {
  width: 20px;
  height: 20px;
}

.el-drawer__body {
  padding-right: 20px;
}

.el-drawer__header,
.el-form-item__label {
  color: #2d3436;
}
</style>
