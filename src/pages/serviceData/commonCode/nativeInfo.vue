<template>
  <div class="nativeInfo">
    <!-- 籍贯信息 -->
    <el-container>
      <el-header>
        <AllBtn
          :btnList="['查询', '新建', '删除', '打印', '导出']"
          :methodCreat="add"
          :methodDelete="delMore"
          :methodSearch="search"
          :methodPrint="print"
          :methodExport="chooseExportExcel"
          ref="allBtn_Ref"
        />
      </el-header>
      <el-main>
        <PublicTable
          :viewTableList.sync="tableData"
          :theads.sync="theads"
          @rowDblclick="handleClick"
          :cell_blue="cell_blue"
          @selectionChange="handleSelectChangeTable"
          :tableLoading.sync="loading"
          isCheck
        >
        </PublicTable>
      </el-main>
    </el-container>
    <el-drawer
      title="籍贯信息属性"
      :visible.sync="drawer"
      :before-close="handleClose"
      :wrapperClosable="false"
    >
      <el-form :model="popupForm" ref="ruleForm" :rules="rules">
        <el-form-item
          label="籍贯代码"
          :label-width="formLabelWidth"
          prop="natCode"
        >
          <el-input
            v-model.trim="popupForm.natCode"
            autocomplete="off"
            size="small"
            placeholder="请输入籍贯代码"
            :disabled="funTpye == '/Create' ? false : true"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="籍贯名称"
          :label-width="formLabelWidth"
          prop="natName"
        >
          <el-input
            v-model.trim="popupForm.natName"
            autocomplete="off"
            size="small"
            placeholder="请输入籍贯名称"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="drawer = false" size="small">取消</el-button>
        <el-button class="blue_btn" @click="submit" size="small"
          >保存</el-button
        >
      </div>
    </el-drawer>
    <div style="display: none" id="printHtml">
      <printTable
        :tableHeader="tableHeader"
        :multipleSelection="multipleSelection"
        ref="print"
      ></printTable>
    </div>
  </div>
</template>

<script>
import { dataUtils } from '@/common';
import printTable from './printTable';
import Print from '@/common/print';
import PublicTable from '../../../components/publicTable.vue';
import AllBtn from '../allBtn.vue';
export default {
  name: 'nativeInfo',
  components: { printTable, PublicTable, AllBtn },
  data() {
    return {
      formInline: '',
      codeArr: [],
      loading: false,
      excelData: [],
      multipleSelection: [],
      excelName: '',
      tableDataCopy: [],
      tableData: [],
      pageSize: 50,
      currentPage: 1,
      drawer: false,
      funTpye: '/Create',
      formLabelWidth: '120px',
      popupForm: {
        natCode: '',
        natName: ''
      },
      theads: {
        natCode: '籍贯代码',
        natName: '籍贯名称'
      },
      cell_blue: ['natName'],
      rules: {
        natCode: [
          { required: true, message: '请输入籍贯代码', trigger: 'blur' }
        ],
        natName: [
          { required: true, message: '请输入籍贯名称', trigger: 'blur' }
        ]
      },
      tableHeader: [
        {
          label: '籍贯代码',
          property: 'natCode'
        },
        {
          label: '籍贯名称',
          property: 'natName'
        }
      ]
    };
  },
  created: function () {},
  mounted: function () {
    this.getTableData(); //默认查询
    this.excelName = '籍贯信息' + dataUtils.getNowDateTiemNo();
  },
  methods: {
    handleClose() {
      this.drawer = false;
    },
    //模糊查询
    search() {
      this.formInline = this.$refs.allBtn_Ref.searchInfo.trim(); //获取组件文本框的值
      if (this.formInline) {
        this.tableData = this.tableDataCopy.filter((item) => {
          return (
            item.natCode.indexOf(this.formInline) !== -1 ||
            item.natName.indexOf(this.formInline) !== -1
          );
        });
      } else {
        this.getTableData();
      }
      //this.loading = false;
    },
    //查询
    getTableData() {
      this.loading = true;
      this.$ajax
        .post(this.$apiUrls.RD_CodeNativePlace + '/Read', [])
        .then((r) => {
          this.tableData = r.data.returnData;
          this.tableDataCopy = r.data.returnData;
          this.loading = false;
        });
    },
    //新增
    add() {
      this.drawer = true;
      this.funTpye = '/Create';
      this.$nextTick(function () {
        this.$refs.ruleForm.resetFields();
      });
    },
    //编辑
    handleClick(row) {
      console.log('row', row);
      this.drawer = true;
      this.funTpye = '/Update';
      this.$nextTick(() => {
        this.$refs.ruleForm.resetFields();
        this.popupForm = {
          natCode: row.natCode,
          natName: row.natName
        };
      });
    },
    //提交
    submit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$ajax
            .post(
              this.$apiUrls.CU_CodeNativePlace + this.funTpye,
              this.popupForm
            )
            .then((r) => {
              r = r.data;
              if (!r.success) {
                return;
              }
              this.tableData = [];
              this.drawer = false;
              this.$nextTick(() => {
                this.$refs.allBtn_Ref.searchInfo = '';
                this.search();
              });
              if (this.funTpye == '/Create') {
                this.$message({
                  message: '新建成功',
                  type: 'success',
                  showClose: true
                });
              } else {
                this.$message({
                  message: '修改成功',
                  type: 'success',
                  showClose: true
                });
              }
            });
        } else {
          return false;
        }
      });
    },
    //复选框勾选操作
    handleSelectChangeTable: function (val) {
      //console.log(val);
      this.multipleSelection = val;
      this.codeArr = [];
      val.map((item, i) => {
        if (item.natCode != '') {
          this.codeArr.push(item.natCode);
        }
      });
      //console.log(this.codeArr);
    },
    //多条删除
    delMore() {
      if (this.codeArr.length <= 0) {
        this.$message({
          showClose: true,
          message: '请勾选要删除的数据!',
          type: 'warning'
        });
      }
      if (this.codeArr.length > 0) {
        this.$confirm('是否确认删除多条文件数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.delete(this.codeArr);
          })
          .catch(() => {
            return;
          });
      } else {
        return false;
      }
    },
    //确认删除
    delete(codeArr) {
      this.$ajax
        .post(this.$apiUrls.RD_CodeNativePlace + '/Delete', codeArr)
        .then((r) => {
          let { success } = r.data;
          if (!success) return;
          this.$message({
            showClose: true,
            message: '删除成功',
            type: 'success'
          });
          this.$nextTick(() => {
            this.$refs.allBtn_Ref.searchInfo = '';
            this.search();
          });
        });
    },
    //打印
    print() {
      if (this.multipleSelection.length == 0) {
        return this.$message({
          message: '请选择至少一条数据进行操作！',
          type: 'warning'
        });
      }
      const printData = document.getElementById('printHtml').innerHTML;
      Print(printData);
      // this.$refs.print.printing();
    },
    // 选中导出
    chooseExportExcel() {
      if (this.multipleSelection.length == 0) {
        return this.$message({
          message: '请选择至少一条数据进行操作！',
          type: 'warning'
        });
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.excelData = this.multipleSelection; // multipleSelection是一个数组，存储表格中选择的行的数据。
          this.excelData.map((item, i) => {
            item.index = i + 1;
          });
          this.$nextTick(function () {
            this.export2Excel();
          });
        })
        .catch(() => {});
    },
    // 数据写入excel
    export2Excel() {
      var that = this;
      require.ensure([], () => {
        const { export_json_to_excel } = require('@/utils/Export2Excel'); // 这里必须使用绝对路径，使用@/+存放export2Excel的路径
        const tHeader = ['序号', '籍贯代码', '籍贯名称']; // 导出的表头名信息
        const filterVal = ['index', 'natCode', 'natName']; // 导出的表头字段名
        const list = that.excelData;
        const data = that.formatJson(filterVal, list);
        const name = that.excelName;
        export_json_to_excel(tHeader, data, name);
      });
    },
    // 格式转换
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) => filterVal.map((j) => v[j]));
    }
  }
};
</script>
<style lang="less" scoped>
.nativeInfo {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-weight: 600;
  header {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
    .el-form-item {
      margin-bottom: 18px;
    }
    .el-button--small {
      width: 64px;
    }
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 40px;
  }
  .el-container.is-vertical {
    height: 100%;
  }
  .el-header,
  .el-main {
    padding: 0;
  }

  .searchWrap .el-form-item:last-child {
    margin-right: 0;
    margin-left: 10px;
  }
  .indexPage .contentWrap .tabPage {
    margin-top: 0;
  }
  .el-paginative {
    text-align: center;
  }
  .el-drawer.rtl {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #2d3436;
    padding: 0 20px 0 10px;
  }
  .el-drawer__body {
    padding-right: 20px;

    .el-form-item {
      margin-bottom: 18px;
    }
  }
}
</style>
<style>
.el-dialog {
  width: 480px;
}
.el-table .el-table__cell {
  padding: 5px 0;
}
.el-main .el-checkbox__inner {
  width: 20px;
  height: 20px;
}
.el-drawer__body {
  padding-right: 20px;
}
.el-drawer__header,
.el-form-item__label {
  color: #2d3436;
}
</style>
