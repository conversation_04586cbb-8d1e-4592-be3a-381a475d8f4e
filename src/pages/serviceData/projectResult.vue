<template>
  <div class="projectResult_page" id="BasedOnTheCode">
    <div class="left_wrap" id="LeftWrap">
      <header>
        <p>项目列表</p>
        <div style="padding: 0 10px">
          <el-input
            v-model="listSearchVal"
            size="small"
            clearable
            @input="listSearch"
            placeholder="请输入内容"
          ></el-input>
        </div>
      </header>
      <div class="left_content">
        <el-menu
          v-if="menuList.length !== 0"
          ref="elMenu_Ref"
          :default-active="itemCode"
          :defaultOpeneds="defaultOpened"
          class="el-menu-vertical-demo"
          style="height: calc(100% - 50px); overflow: auto"
        >
          <el-submenu
            :index="item.clsCode"
            v-for="item in menuList"
            :key="item.clsCode"
          >
            <template slot="title">
              <i class="iconfont icon-leixing1 icon"></i>
              <span>{{ item.clsName }}</span>
            </template>
            <el-menu-item
              :index="child.itemCode"
              v-for="child in item.child"
              :key="child.itemCode"
            >
              <template slot="title">
                <div :title="child.itemName" @click="menuClick(item, child)">
                  <i class="iconfont icon-biaodan icons"></i>
                  <span>{{ `(${child.itemCode})` + child.itemName }}</span>
                </div>
              </template>
            </el-menu-item>
          </el-submenu>
        </el-menu>
        <el-empty :image-size="200" style="height: 100%" v-else></el-empty>
      </div>
    </div>
    <div class="right_wrap">
      <header>
        <h3 :title="title">{{ title }}</h3>
        <AllBtn
          ref="allBtn_Ref"
          :btnList="['查询', '新建', '删除']"
          :methodCreat="methodCreat"
          :methodSearch="methodSearch"
          :methodDelete="methodDelete"
        />
      </header>
      <PublicTable
        ref="publicTable_Ref"
        class="right_content"
        :viewTableList="searchTableList"
        isCheck
        :theads="theads"
        :isSortShow="false"
        :tableLoading="tableLoading"
        :columnWidth="columnWidth"
        @rowDblclick="rowDblclick"
      >
        <template #abnormalType="{ scope }">
          <div>
            {{ enum_abnormity[scope.row.abnormalType] }}
          </div>
        </template>
      </PublicTable>
    </div>

    <!-- 编辑抽屉 -->
    <el-drawer
      title="项目结果属性"
      size="460"
      :wrapperClosable="false"
      :visible.sync="drawerShow"
    >
      <el-form
        :model="formInfo"
        ref="form_Ref"
        :rules="rules"
        label-width="110px"
        class="form_wrap"
      >
        <!-- <el-form-item label="结果编号" prop="resultId">
          <el-input
            :readonly="creatOrModify == 2"
            v-model.trim="formInfo.resultId"
            mimlength="1"
            maxlength="9"
            show-word-limit
            size="small"
          ></el-input>
        </el-form-item> -->
        <el-form-item label="结果描述" prop="resultDesc">
          <el-input
            type="textarea"
            :autosize="{ minRows: 2 }"
            placeholder="请输入内容"
            v-model.trim="formInfo.resultDesc"
            size="small"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="对应疾病" prop="diseaseCode">
          <el-select
            v-model.trim="formInfo.diseaseCode"
            filterable
            remote
            clearable
            :remote-method="remoteMethod"
            v-loadmore="loadMore"
            placeholder="请选择"
            size="small"
            @visible-change="handleVisibleChange"
          >
            <el-option
              v-for="item in illnessList"
              :key="item.diseaseCode"
              :label="item.diseaseName"
              :value="item.diseaseCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否异常" prop="abnormalType">
          <el-radio-group v-model.trim="formInfo.abnormalType">
            <el-radio
              v-for="item in G_abnormalType"
              :key="item.value"
              :label="item.value"
              >{{ item.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="顺序" prop="displayOrder">
          <el-input
            v-model.trim="formInfo.displayOrder"
            size="small"
          ></el-input>
        </el-form-item>
        <div style="text-align: right">
          <el-button @click="drawerShow = false" size="small">取消</el-button>
          <el-button
            class="blue_btn"
            @click.native="saveBtn('form_Ref')"
            size="small"
            >保存</el-button
          >
        </div>
      </el-form>
    </el-drawer>
  </div>
</template>

<script>
import AllBtn from './allBtn';

import PublicTable from '@/components/publicTable';
import { dataUtils } from '../../common';
import { mapGetters } from 'vuex';

export default {
  name: 'projectResult',

  components: {
    AllBtn,
    PublicTable
  },
  data() {
    return {
      listSearchVal: '',
      defaultOpened: [], //默认展开
      itemCode: '',
      parameter: {
        pageNumber: 1,
        pageSize: 50,
        keyword: ''
      },
      totalPage: 0,
      enum_abnormity: {
        0: '否',
        1: '异常',
        2: '重大阳性',
        3: '危急'
      },
      tableShow: true,
      drawerShow: false,
      formInfo: {
        resultId: 0,
        resultDesc: '',
        diseaseCode: '',
        abnormalType: 0,
        displayOrder: ''
      },
      menuList: [],
      fixed_menuList: [],
      theads: {
        // resultId: "编号",
        resultDesc: '结果描述',
        diseaseName: '对应疾病',
        abnormalType: '是否异常',
        displayOrder: '顺序'
      },
      columnWidth: {
        diseaseName: 200,
        abnormalType: 80,
        displayOrder: 60
      },
      viewTableList: [],
      checkMenu: {}, //选中的菜单
      parentMenu: {}, //选中菜单的父级
      creatOrModify: 1, //1表示新建，2表示修改
      fixed_illnessList: [], //固定的疾病列表
      illnessList: [], //疾病列表
      tableLoading: false,
      searchIpt: '',
      searchTableList: [],
      rules: {
        resultId: [
          { required: true, message: '请输入编号', trigger: 'blur' }
          // { min: 1, max: 9, message: '长度在 1 到 9 个字符', trigger: 'blur' }
        ],
        resultDesc: [
          { required: true, message: '请输入结果描述', trigger: 'blur' }
        ],
        displayOrder: [
          { min: 0, max: 6, message: '长度在 1 到 6 个字符', trigger: 'blur' }
        ]
      },
      title: ''
    };
  },
  computed: {
    ...mapGetters(['G_abnormalType'])
  },
  methods: {
    // 查询
    methodSearch() {
      this.searchIpt = this.$refs.allBtn_Ref.searchInfo.trim(); //获取组件文本框的值
      this.tableLoading = true;
      if (!this.searchIpt) {
        this.$nextTick(() => {
          this.tableLoading = false;
        });
        return (this.searchTableList = this.viewTableList);
      }
      let newTableData = [];
      this.viewTableList.map((item) => {
        console.log(item);
        if (
          // (item.resultId + "").includes(this.searchIpt) ||
          item.resultDesc?.includes(this.searchIpt) ||
          item.diseaseName?.includes(this.searchIpt)
        ) {
          newTableData.push(item);
        }
      });
      this.searchTableList = newTableData;
      this.$nextTick(() => {
        this.tableLoading = false;
      });
    },
    // 删除
    methodDelete() {
      let checkList = this.$refs.publicTable_Ref.$refs.tableCom_Ref.selection;
      if (checkList.length === 0) {
        this.$message({
          message: '请点击选择结果删除',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm('是否确定删除选中的结果?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax
            .post(this.$apiUrls.Delete_CodeItemResult, checkList)
            .then((r) => {
              let { success } = r.data;
              if (!success) return;
              this.drawerShow = false;
              this.$message({
                message: '删除成功',
                type: 'success',
                showClose: true
              });
              this.getProjectList();
            });
        })
        .catch(() => {});
    },
    // 获取项目列表
    getItem_ItemCls() {
      this.$ajax.post(this.$apiUrls.ItemGroupByItemCls).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.fixed_menuList = dataUtils.deepCopy(returnData || []);
        this.menuList = returnData || [];
        this.defaultOpened = [returnData[0].clsCode];
        this.itemCode = returnData[0].child[0].itemCode;
        this.checkMenu.itemCode = this.itemCode;
        this.title = `${returnData[0].clsName}｜${returnData[0].child[0].itemName}`;
        this.getProjectList();
      });
    },
    // 获取疾病列表
    getCodeDisease(isRoll = false) {
      this.$ajax
        .post(this.$apiUrls.Disease, '', { query: this.parameter })
        .then((r) => {
          let { success, returnData, totalPage } = r.data;
          if (!success) return;
          // this.fixed_illnessList = dataUtils.deepCopy(returnData) || [];
          let filterData = returnData.filter((item) => {
            return !this.illnessList.find(
              (i) => i.diseaseCode == item.diseaseCode
            );
          });
          this.illnessList = isRoll
            ? [...this.illnessList, ...filterData]
            : dataUtils.deepCopy(returnData) || [];
          this.totalPage = totalPage || 0;
        });
    },
    // 菜单的点击回调
    menuClick(parentMenu, menu) {
      this.parentMenu = parentMenu;
      this.checkMenu = menu;
      this.tableShow = false;
      this.$nextTick(() => {
        this.tableShow = true;
      });
      this.title = `${parentMenu.clsName}｜${menu.itemName}`;
      this.getProjectList();
    },
    // 获取项目详情
    getProjectList() {
      this.tableLoading = true;
      this.$ajax
        .post(this.$apiUrls.Read_CodeItemResult, '', {
          query: { itemCode: this.checkMenu.itemCode }
        })
        .then((r) => {
          this.tableLoading = false;
          let { returnData, success } = r.data;
          if (!success) return;
          this.viewTableList = returnData || [];
          this.searchTableList = returnData || [];
        });
    },
    // 表格行的双击回调
    rowDblclick(row) {
      this.creatOrModify = 2;
      if (
        !this.illnessList.find((item) => item.diseaseCode === row.diseaseCode)
      ) {
        this.illnessList.push({
          diseaseCode: row.diseaseCode,
          diseaseName: row.diseaseName
        });
      }
      this.drawerShow = true;
      this.$nextTick(() => {
        this.formInfo = dataUtils.deepCopy(row);
        this.resetBtn('form_Ref');
      });
    },
    // 保存
    saveBtn(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.creatOrModify == 1) {
            let datas = {
              clsCode: this.parentMenu.clsCode,
              itemCode: this.checkMenu.itemCode,
              ...this.formInfo
            };
            console.log(datas);
            this.$ajax
              .post(this.$apiUrls.CU_CodeItemResult + '/Create', datas)
              .then((r) => {
                let { success, returnData } = r.data;
                if (!success) return;
                this.drawerShow = false;
                this.$message({
                  message: '新建成功',
                  type: 'success',
                  showClose: true
                });
                this.getProjectList();
              });
          } else {
            this.modify();
          }
        } else {
          return false;
        }
      });
    },
    // 新建
    methodCreat() {
      if (JSON.stringify(this.checkMenu) === '{}') {
        this.$message({
          message: '请先选中左边的项目菜单',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.creatOrModify = 1;
      this.drawerShow = true;
      this.$nextTick(() => {
        this.resetBtn('form_Ref');
      });
    },
    // 修改
    modify() {
      console.log(this.formInfo);
      this.$ajax
        .post(this.$apiUrls.CU_CodeItemResult + '/Update', this.formInfo)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.drawerShow = false;
          this.$message({
            message: '修改成功',
            type: 'success',
            showClose: true
          });
          this.getProjectList();
        });
    },
    // 重置
    resetBtn(formName) {
      this.$refs[formName].resetFields();
    },
    // 远程搜索疾病
    remoteMethod(query) {
      this.parameter.pageNumber = 1;
      this.parameter.keyword = query;
      this.getCodeDisease(false);
    },
    handleVisibleChange(value) {
      if (!value) {
        this.parameter = {
          pageNumber: 1,
          pageSize: 50,
          keyword: ''
        };
      }
    },
    //疾病列表滚动加载更多
    loadMore() {
      if (this.parameter.pageNumber >= this.totalPage) {
        return;
      }
      this.parameter.pageNumber = this.parameter.pageNumber + 1;
      this.getCodeDisease(true);
    },
    // 搜索菜单
    listSearch() {
      console.log(this.menuList);
      if (this.listSearchVal.trim() === '') {
        this.menuList = dataUtils.deepCopy(this.fixed_menuList);
        return;
      }
      let menuList = [];
      this.fixed_menuList.map((item, idx) => {
        let isHave = false;
        let itemHave = {
          ...item,
          child: []
        };
        let itemChild = item.child.filter((twoItem) => {
          if (
            twoItem.itemName.includes(this.listSearchVal) ||
            twoItem.itemCode.includes(this.listSearchVal)
          ) {
            isHave = true;
            return twoItem;
          }
        });
        itemHave.child = itemChild;
        if (isHave) {
          menuList.push(itemHave);
        }
      });
      this.menuList = menuList;
    }
  },
  created() {
    this.getItem_ItemCls();
  },
  mounted() {
    this.getCodeDisease(false);
  }
};
</script>
<style lang="less" scoped>
.projectResult_page {
  display: flex;
  .icon {
    margin-right: 8px;
    font-size: 18px;
  }
  .icons {
    font-size: 16px;
    margin-right: 8px;
  }
  .left_wrap {
    width: 360px;
    margin-right: 20px;
    background: #fff;
    display: flex;
    flex-direction: column;
    .left_content {
      flex: 1;
      overflow: auto;
    }
    p {
      font-size: 16px;
      color: #2d3436;
      height: 50px;
      line-height: 50px;
      margin-left: 10px;
    }
  }

  .right_wrap {
    flex: 1;
    flex-shrink: 0;
    background: #fff;
    padding: 0 15px 15px 15px;
    overflow: auto;
    display: flex;
    flex-direction: column;
    header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    h3 {
      color: #2d3436;
      font-size: 18px;
      font-weight: 600;
      width: 300px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .right_content {
    flex: 1;
    overflow: auto;
  }
  .form_wrap {
    padding: 0 15px;
  }
}
</style>
<style lang="less">
.projectResult_page {
  .el-submenu__title {
    font-size: 16px;
  }
  .el-submenu__title,
  .el-menu-item {
    height: 32px;
    line-height: 32px;
    transition: none !important;
  }

  .el-menu {
    border: none;
  }
  .el-submenu .el-menu-item:focus,
    // .el-menu-item:hover,
    .el-menu-item.is-active {
    outline: 0;
    color: #fff;
    background-color: #1770df;
    border-radius: 4px;
    border-radius: 4px;
  }
}
</style>
