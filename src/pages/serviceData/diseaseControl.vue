<template>
  <div class="diseaseControl box" ref="box" id="diseaseControl">
    <div class="left" id="LeftWrap">
      <p>疾病管理</p>
      <div style="padding: 0 10px">
        <el-input
          v-model="listSearchVal"
          size="small"
          clearable
          @input="listSearch"
          placeholder="请输入内容"
        ></el-input>
      </div>
      <el-empty
        :image-size="200"
        style="height: 100%"
        v-if="fixed_menuList.length === 0"
      ></el-empty>

      <!-- 使用 Element UI 的 el-tree 组件渲染数据 -->
      <el-tree
        v-else
        class="disease-tree"
        :data="menuList"
        :props="defaultProps"
        node-key="id"
        :default-expanded-keys="defaultExpandedKeys"
        :expand-on-click-node="false"
        @node-click="handleNodeClick"
        :highlight-current="true"
        :current-node-key="currentNodeKey"
      >
        <span class="custom-tree-node" slot-scope="{ node, data }">
          <!-- 根据节点层级显示不同图标 -->
          <i class="icon" v-if="data._LEVEL === 0" :class="data.icon"></i>
          <i
            class="iconfont icon-biaodan icons"
            v-if="(data._LEVEL == 1 && !data.children) || data._LEVEL == 2"
          ></i>
          <i
            class="icons"
            v-if="
              data._LEVEL == 1 && data.children && data.children.length != 0
            "
            :class="
              node.expanded
                ? 'iconfont icon-wenjianjia'
                : 'iconfont icon-folder1-fill'
            "
          ></i>

          <!-- 显示节点标题和附加信息 -->
          <span class="tree-label" :title="data.title">
            {{
              C_disease(data) +
              data.title +
              (data._LEVEL === 1
                ? `(${data.children ? data.children.length : 0})`
                : '')
            }}
          </span>
        </span>
      </el-tree>
      <el-menu
        ref="elMenu_Ref"
        :default-active="muenId"
        :defaultOpeneds="defaultOpened"
        v-if="false"
        class="el-menu-vertical-demo"
        @open="handleOpen"
        @close="handleClose"
        @select="handleSelect"
        style="height: calc(100% - 50px); overflow: auto"
      >
        <!-- 三层 -->
        <el-submenu
          v-for="(item, index) in menuList"
          :index="item.id"
          :key="index"
          v-show="item.onlyOne == 3"
        >
          <template slot="title">
            <i :class="item.icon" class="icon"></i>
            <span>{{ item.title }}</span>
          </template>
          <template v-for="(child, idx) in item.children">
            <el-submenu
              v-if="child.children.length > 0"
              :key="idx"
              :index="child.id"
            >
              <template slot="title">
                <i
                  class="icons"
                  :class="
                    openedMenus.includes(child.id)
                      ? 'iconfont icon-wenjianjia'
                      : 'iconfont icon-folder1-fill'
                  "
                ></i>
                <span class="child-title">{{ child.title }}</span>
              </template>
              <el-menu-item
                v-for="(grandson, i) in child.children"
                :index="grandson.id"
                :key="i"
                @click="goTo(item, child, grandson)"
              >
                <template slot="title">
                  <i class="iconfont icon-biaodan icons"></i>
                  <span>{{ grandson.title }}</span>
                </template>
              </el-menu-item>
            </el-submenu>
          </template>
        </el-submenu>
        <!-- 二层 -->
        <el-submenu
          v-for="(item, index) in menuList"
          :index="item.id"
          :key="'two' + index"
          v-show="item.onlyOne == 2"
        >
          <template slot="title">
            <div @click="twoLayerClick(item)">
              <i :class="item.icon" class="icon"></i>
              <span>{{ item.title }}</span>
            </div>
          </template>
          <el-menu-item
            v-for="(grandson, i) in item.children"
            :index="grandson.id"
            :key="'two' + i"
            @click="twoLayerItemClick(grandson)"
          >
            <template slot="title">
              <i class="iconfont icon-biaodan icons"></i>
              <span>{{ grandson.title }}</span>
            </template>
          </el-menu-item>
        </el-submenu>
        <!-- 一层 -->
        <el-menu-item
          class="only"
          v-for="(item, indexs) in menuList"
          :index="item.id"
          :key="'id' + indexs"
          @click="goToTitle(item)"
          v-show="item.onlyOne == 1"
        >
          <template>
            <i :class="item.icon" class="icons"></i>
            <span>{{ item.title }}</span>
          </template>
        </el-menu-item>
      </el-menu>
    </div>
    <div class="right">
      <div class="rightTitle">{{ rightTitle }}</div>
      <div class="rightCont">
        <transition name="fade-transform" mode="out-in">
          <component ref="viewConfig" :is="path" :menuList="menuList" />
        </transition>
      </div>
    </div>
  </div>
</template>
<script>
import DiseaseInformation from './diseaseControl/diseaseInformation.vue';
import DiseaseReviewConditions from './diseaseControl/diseaseReviewConditions.vue';
import DesignFormulas from './diseaseControl/designFormulas.vue';
import diseasesToInfo from './diseaseControl/diseasesToInfo.vue';
import diseasesTypeToInfo from './diseaseControl/diseasesTypeToInfo.vue';
import diseaseStatisticsAgeGroup from './commonCode/diseaseStatisticsAgeGroup.vue'; //疾病统计年龄段

import XEUtils from 'xe-utils';
export default {
  name: 'diseaseControl',
  components: {
    DiseaseInformation,
    DiseaseReviewConditions,
    DesignFormulas,
    diseasesToInfo,
    diseasesTypeToInfo,
    diseaseStatisticsAgeGroup
  },
  computed: {
    C_sum() {
      return function (row) {
        if (row._LEVEL === 1) {
          let data = this.fixed_menuList.find((item) => item.id === row.id);
          return `(${data.children.length})`;
        }
        return '';
      };
    },
    C_disease() {
      return function (row) {
        let diseaseCode = '',
          sign = '';
        if (row.diseaseCode) {
          diseaseCode = `(${row.diseaseCode})`;
        }
        if (row.positiveCode) {
          switch (row.diseaseGrade) {
            case 1:
              sign = '★★';
              break;
            case 2:
              sign = '★';
              break;
            default:
              sign = '';
              break;
          }
        }
        return sign + diseaseCode;
      };
    }
  },
  data() {
    return {
      listSearchVal: '',
      rightTitle: '',
      path: '',
      muenId: '',
      defaultOpened: [],
      openedMenus: [],
      diseaseCode: '',
      grandsonInfo: {},
      // el-tree 组件所需的属性
      defaultProps: {
        children: 'children',
        label: 'title'
      },
      defaultExpandedKeys: [], // 默认展开的节点
      currentNodeKey: '', // 当前选中的节点
      menuList: [
        {
          id: '1',
          title: '疾病信息',
          icon: 'iconfont icon-leixing1',
          _LEVEL: 0,
          onlyOne: 3,
          children: [],
          viewConfig: {}
        },
        {
          id: '2',
          title: '疾病审核条件',
          icon: 'iconfont icon-leixing1',
          _LEVEL: 0,
          onlyOne: 3,
          children: [],
          viewConfig: {}
        },
        {
          id: '3',
          title: '疾病判断计算式',
          icon: 'iconfont icon-leixing1',
          _LEVEL: 0,
          onlyOne: 2,
          path: 'DesignFormulas',
          children: [],
          viewConfig: {}
        },
        {
          id: '4',
          title: '疾病包含关系对应信息',
          icon: 'iconfont icon-leixing1',
          _LEVEL: 0,
          onlyOne: 1,
          children: null,
          path: 'diseasesToInfo',
          viewConfig: {}
        },
        {
          id: '5',
          title: '疾病分类与疾病对应信息',
          icon: 'iconfont icon-leixing1',
          _LEVEL: 0,
          onlyOne: 1,
          children: null,
          path: 'diseasesTypeToInfo',
          viewConfig: {}
        },
        {
          id: '6',
          title: '疾病统计年龄段',
          icon: 'iconfont icon-leixing1',
          _LEVEL: 0,
          onlyOne: 1,
          children: null,
          path: 'diseaseStatisticsAgeGroup',
          viewConfig: {}
        }
      ],
      fixed_menuList: [],
      list_active: {},
      illnessList: [], //疾病下拉列表
      illnessObj: {}, //疾病列表的代码对应中文对象
      searchList: [], //疾病信息下拉列表
      searchList1: [], //疾病审核条件下拉
      searchObj: {}, //列表的代码对应中文对象
      clsName: ''
    };
  },
  created() {},
  mounted() {
    // this.getDeptDisease();
    // this.getOperatorList();
    this.initData();
  },

  methods: {
    // 处理树节点点击事件
    handleNodeClick(data, node) {
      console.log('节点点击:', data, node);
      this.currentNodeKey = data.id;

      if ((data._LEVEL == 1 && !data.children) || data._LEVEL == 2) {
        this.rowClick(data);
      } else {
        this.twoLayerClick(data);
      }
    },

    initData() {
      Promise.all([this.getDeptDisease(), this.getOperatorList()]).then((r) => {
        setTimeout(async () => {
          // 将树结构拍平，构建列表树结构
          XEUtils.eachTree(
            r[1],
            (item, _index, _items, _paths, _parent, nodes) => {
              // 层级
              item._LEVEL = nodes.length - 1;
              // 是否展开
              item._EXPAND = false;
              // 是否可视
              item._VISIBLE = !item._LEVEL;
              // 是否有子节点
              item._HAS_CHILDREN = item.children && item.children.length > 0;
              // 是否叶子节点
              item._IS_LEAF = !item._HAS_CHILDREN;
            }
          );

          // 设置默认展开的节点
          if (this.menuList[0]?.children?.[0]?.children?.[0]) {
            const firstNodeId = this.menuList[0].children[0].children[0].id;
            this.defaultExpandedKeys = [
              this.menuList[0].id,
              this.menuList[0].children[0].id,
              firstNodeId
            ];
            this.currentNodeKey = firstNodeId;
          }

          // 展开第一个节点
          this.treeExpand(this.menuList[0]?.children?.[0]?.children?.[0]);
        }, 200);
      });
    },
    // 双层数据源
    async getDeptDisease() {
      let matchObj = await this.getChildList();
      console.log(matchObj);
      let infoObj = {}; //疾病信息
      let auditObj = {}; //疾病审核条件
      this.menuList[0]?.children?.map((item) => {
        infoObj[item.id] = item;
      });
      console.log(infoObj);
      // debugger
      this.menuList[1]?.children?.map((item) => {
        auditObj[item.id] = item;
      });
      return new Promise((resolve, reject) => {
        this.$ajax.post(this.$apiUrls.GetDiseaseInDept, []).then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          console.log('[ returnData ]-193', returnData);
          var newData1 = [];
          var newData2 = [];
          let selectList = [];
          let illnessObj = {};
          //把returnData第一项放到最后面
          returnData.push(returnData.shift());
          returnData.forEach((item, index) => {
            let infoItem = infoObj['id' + index];
            let auditItem = auditObj['ids' + index];
            console.log(infoItem);
            newData1.push({
              id: 'id' + index,
              title: item.clsName,
              children: [],
              _LEVEL: 1,
              // 是否展开,
              _EXPAND: infoItem ? infoItem._EXPAND : false,
              // 是否可视,
              _VISIBLE: infoItem ? infoItem._VISIBLE : false,
              // 是否有子节点
              _HAS_CHILDREN: true,
              // 是否叶子节点
              _IS_LEAF: false
            });
            newData2.push({
              id: 'ids' + index,
              title: item.clsName,
              children: [],
              _LEVEL: 1,
              // 是否展开,
              _EXPAND: auditItem ? auditItem._EXPAND : false,
              // 是否可视,
              _VISIBLE: auditItem ? auditItem._VISIBLE : false,
              // 是否有子节点
              _HAS_CHILDREN: true,
              // 是否叶子节点
              _IS_LEAF: false
            });
            item.diseases.forEach((data, idx) => {
              newData1[index].children.push({
                id: 'two_id' + data.diseaseCode,
                diseaseCode: data.diseaseCode,
                title: data.diseaseName,
                diseaseGrade: data.diseaseGrade,
                positiveCode: data.positiveCode,
                icon: 'iconfont icon-leixing1',
                path: 'DiseaseInformation',
                parentIdx: [0, index],
                _LEVEL: 2,
                // 是否展开,
                _EXPAND: false,
                // 是否可视,
                _VISIBLE: infoItem ? infoItem._EXPAND : false,
                // 是否有子节点
                _HAS_CHILDREN: false,
                // 是否叶子节点
                _IS_LEAF: true
              });
              newData2[index].children.push({
                id: 'two_ids' + data.diseaseCode,
                diseaseCode: data.diseaseCode,
                title: data.diseaseName,
                icon: 'iconfont icon-leixing1',
                path: 'DiseaseReviewConditions',
                parentIdx: [1, index],
                _LEVEL: 2,
                // 是否展开,
                _EXPAND: false,
                // 是否可视,
                _VISIBLE: auditItem ? auditItem._EXPAND : false,
                // 是否有子节点
                _HAS_CHILDREN: false,
                // 是否叶子节点
                _IS_LEAF: true
              });
              selectList.push({
                id: data.diseaseCode,
                label: data.diseaseName
              });
              illnessObj[data.diseaseCode] = data.diseaseName;
            });
          });
          this.menuList[0].children = newData1;
          this.menuList[1].children = newData2;
          // 处理默认展开第一个
          this.muenId = this.menuList[0].children[0].children[0].id;
          this.path = this.menuList[0].children[0].children[0].path;
          this.rightTitle = `${this.menuList[0].title}｜${this.menuList[0].children[0].title}｜${this.menuList[0].children[0].children[0].title}`;
          this.clsName = this.menuList[0].children[0].title;
          this.diseaseCode =
            this.menuList[0].children[0].children[0].diseaseCode;
          // 获取页面数据
          this.$nextTick(() => {
            this.$refs.viewConfig.getDiseaseInfo(this.diseaseCode);
          });
          this.searchList = selectList;
          this.searchList1 = selectList;
          this.searchObj = illnessObj;
          resolve(this.menuList);
        });
      });
    },
    // 获取疾病判断计算公式列表
    getOperatorList() {
      // let matchObj = this.getChildList();
      // console.log(matchObj);
      return new Promise((resolve, reject) => {
        this.$ajax.post(this.$apiUrls.ReadDiseaseExpressionList).then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          let arr = [];
          let selectList = [];
          let illnessObj = {};
          returnData.map((item, idx) => {
            arr.push({
              id: item.expCode,
              diseaseCode: item.expCode,
              title: item.expName,
              icon: 'iconfont icon-leixing1',
              path: 'DesignFormulas',
              parentId: '3'
            });
            arr[idx] = Object.assign(arr[idx], {
              // 层级
              _LEVEL: 1,
              // 是否展开,
              _EXPAND: false,
              // 是否可视,
              _VISIBLE: this.menuList[2]._EXPAND,
              // 是否有子节点
              _HAS_CHILDREN: undefined,
              // 是否叶子节点
              _IS_LEAF: true
            });
            selectList.push({
              id: item.expCode,
              label: item.expName
            });
            illnessObj[item.expCode] = item.expName;
          });
          this.menuList[2].children = arr;
          this.illnessList = selectList;
          this.illnessObj = illnessObj;
          resolve(this.menuList);
        });
      });
    },
    handleOpen(key, keyPath) {
      console.log(key, keyPath, this.muenId);
      this.openedMenus = this.$refs.elMenu_Ref.$data.openedMenus;
      console.log(this.openedMenus);
    },
    handleClose(key, keyPath) {
      this.openedMenus = this.$refs.elMenu_Ref.$data.openedMenus;
      console.log(this.openedMenus);
    },
    handleSelect(index, indexPath) {},
    twoLayerClick(menu) {
      console.log('menu: ', menu);
      if (
        menu.path == 'DesignFormulas' ||
        menu.id == '4' ||
        menu.id == '5' ||
        menu.id == '6'
      ) {
        this.path = menu.path;
        this.rightTitle = menu.title;
        this.list_active = menu;
      }
    },
    //双层下拉
    goTo(parent, child, grandson) {
      console.log(grandson);
      this.path = grandson.path;
      this.diseaseCode = grandson.diseaseCode;
      this.grandsonInfo = grandson;
      this.rightTitle = `${parent.title}｜${child.title}｜${grandson.title}`;
      this.clsName = child.title;
      if (parent.id === '1') {
        setTimeout(() => {
          this.$refs.viewConfig.getDiseaseInfo(this.diseaseCode);
        }, 500);
      }
      if (parent.id === '2') {
        setTimeout(() => {
          this.$refs.viewConfig.getDiseaseCriteriaItem(this.diseaseCode);
          this.$refs.viewConfig.getCodeDiseaseCriteria();
        }, 500);
      }
    },
    // 第二层的子项点击回调
    async twoLayerItemClick(menu) {
      console.log(menu);
      this.$refs.viewConfig.isCreateFlag = '/Update';
      this.path = menu.path;
      this.rightTitle = menu.title;
      await this.$refs.viewConfig.resetForm();
      this.$refs.viewConfig.RD_CodeDiseaseExpression(menu, '/Read');
    },
    //单独部分
    goToTitle(data) {
      console.log(data);
      this.rightTitle = data.title;
      this.path = data.path;
    },
    ////////////////////////////////
    toggleTreeNode(row) {
      console.log(row);
      if (row.children?.length != 0) {
        this.setTreeExpand(row, !row._EXPAND);
      }
    },
    setTreeExpand(row, isExpand) {
      const matchObj = XEUtils.findTree(this.menuList, (item) => item === row);
      row._EXPAND = isExpand;
      console.log(matchObj);
      if (matchObj) {
        XEUtils.eachTree(
          matchObj.item.children,
          (item, index, items, path, parent) => {
            // console.log(item,parent);
            if (this.listSearchVal !== '') {
              item._VISIBLE = false;
              // if(matchObj.item._LEVEL == 1){
              //   item._VISIBLE = isExpand && item.title.includes(this.listSearchVal)
              //   return;
              // }
              // item._VISIBLE = parent
              // ? parent._EXPAND && parent._VISIBLE && item.title.includes(this.listSearchVal)
              // : (isExpand && item.title.includes(this.listSearchVal));
              // if(item._LEVEL === 2 && isExpand && item.title.includes(this.listSearchVal)){
              //   matchObj.nodes[0].children[item.parentIdx[1]]._EXPAND = true;
              //   matchObj.nodes[0].children[item.parentIdx[1]]._VISIBLE = true;
              // }
              if (parent) {
                item._VISIBLE =
                  isExpand &&
                  (item.title.includes(this.listSearchVal) ||
                    item.diseaseCode?.includes(this.listSearchVal));
                if (item.title.includes(this.listSearchVal)) {
                  parent._EXPAND = isExpand && true;
                  parent._VISIBLE = isExpand && true;
                }
              }
              if (!parent && item._LEVEL == 2) {
                item._VISIBLE =
                  isExpand &&
                  (item.title.includes(this.listSearchVal) ||
                    item.diseaseCode?.includes(this.listSearchVal));
              }
              return;
            }
            item._VISIBLE = parent
              ? parent._EXPAND && parent._VISIBLE
              : isExpand;
          }
        );
      }
      this.refreshTree();
    },
    refreshTree() {
      // console.log(XEUtils.toTreeArray(this.menuList));
      // this.menuList = JSON.parse(JSON.stringify(this.fixed_menuList));
      // setTimeout(() => {
      const treeList = XEUtils.toTreeArray(this.menuList);
      //console.log(treeList);
      this.fixed_menuList = treeList.filter((item) => item._VISIBLE);
      // }, 100);
    },
    // 菜单的点击回调
    rowClick(row) {
      console.log(row, this.menuList);
      this.list_active = row;
      const matchObj = XEUtils.findTree(this.menuList, (item) => {
        return item && item.id === row.id;
      });
      //console.log(matchObj);
      let txt = '';
      matchObj.nodes?.map((item, idx) => {
        txt += `${idx === 0 ? '' : ' | '}${item.title}`;
        if (idx === 1) this.clsName = item.title;
      });
      this.rightTitle = txt;
      this.diseaseCode = matchObj.item.diseaseCode;
      this.path = matchObj.item.path;
      if (matchObj.nodes && matchObj.nodes[0].id === '1') {
        setTimeout(() => {
          this.$refs.viewConfig.getDiseaseInfo(this.diseaseCode);
        }, 100);
      }
      if (matchObj.nodes && matchObj.nodes[0].id === '2') {
        setTimeout(() => {
          this.$refs.viewConfig.getDiseaseCriteriaItem(this.diseaseCode);
          this.$refs.viewConfig.getCodeDiseaseCriteria();
        }, 200);
      }
      if (matchObj.item.parentId === '3') {
        this.$refs.viewConfig.isCreateFlag = '/Update';
        setTimeout(async () => {
          await this.$refs.viewConfig.resetForm();
          this.$refs.viewConfig.RD_CodeDiseaseExpression(
            matchObj.item,
            '/Read'
          );
        }, 200);
      }
    },
    // 展开菜单
    treeExpand(row) {
      console.log(row);
      const matchObj = XEUtils.findTree(this.menuList, (item) => {
        if (item.id === row.id) {
          console.log(item);
          this.list_active = item;
        }
        return item.id === row.id;
      });
      console.log(matchObj);
      let ids = [];
      if (matchObj.item._LEVEL == 2) {
        ids.push(matchObj.nodes[0].id);
        ids.push(matchObj.nodes[0].children[row.parentIdx[1]].id);
        matchObj.nodes[0].children.map((item) => {
          ids.push(item.id);
        });
        matchObj.items.map((item) => {
          ids.push(item.id);
        });
      }
      if (matchObj.nodes[0].id == '3') {
        ids.push(matchObj.nodes[0].id);
        matchObj.nodes[0].children.map((item) => {
          ids.push(item.id);
        });
      }

      // console.log(ids);
      XEUtils.eachTree(this.menuList, (item) => {
        if (ids.includes(item.id)) {
          item._EXPAND =
            (item._HAS_CHILDREN &&
              row.parentIdx &&
              matchObj.nodes[0].children[row.parentIdx[1]]?.id == item.id) ||
            matchObj.nodes[0].id == item.id;
          item._VISIBLE = true;
        }
      });
      this.refreshTree();
    },
    // 添加、删除、更新左边栏获取虚拟列表的同级节点
    getChildList() {
      const matchObj = XEUtils.findTree(this.menuList, (item) => {
        return item && item.id === this.list_active?.id;
      });
      console.log(matchObj);
      return matchObj;
    },
    // 菜单搜索回调
    listSearch() {
      console.log(this.menuList, this.fixed_menuList);
      if (this.listSearchVal == '') {
        this.initData();
        return;
      }
      XEUtils.eachTree(this.menuList, (item) => {
        // console.log(item);
        item._VISIBLE = false;
        if (
          !item._HAS_CHILDREN &&
          (item.title.includes(this.listSearchVal) ||
            item.diseaseCode?.includes(this.listSearchVal))
        ) {
          item._VISIBLE = true;
          if (item.parentIdx instanceof Array) {
            this.menuList[item.parentIdx[0]]._VISIBLE = true;
            this.menuList[item.parentIdx[0]]._EXPAND = true;
            this.menuList[item.parentIdx[0]].children[
              item.parentIdx[1]
            ]._VISIBLE = true;
            this.menuList[item.parentIdx[0]].children[
              item.parentIdx[1]
            ]._EXPAND = true;
          }
          if (typeof item.parentIdx === 'string') {
            this.menuList[Number(item.parentIdx[0])]._VISIBLE = true;
          }
        }
      });
      this.refreshTree();
    }
  }
};
</script>
<style lang="less" scoped>
.diseaseControl {
  display: flex;
  .left {
    width: 320px;
    background: white;
    display: flex;
    flex-direction: column;
    p {
      font-size: 16px;
      color: #2d3436;
      line-height: 48px;
      margin-left: 10px;
    }
    .icon {
      margin-right: 8px;
      font-size: 18px;
    }
    .icons {
      font-size: 16px;
      margin-right: 8px;
    }
    .child-title {
      font-size: 14px;
    }
    /deep/.el-submenu__title {
      font-size: 16px;
      height: 30px;
      line-height: 30px;
    }
    .el-submenu .el-menu-item,
    .only {
      height: 30px;
      line-height: 30px;
    }
    .only {
      font-size: 16px;
    }
    .el-submenu .el-menu-item:focus,
    .el-menu-item.is-active {
      outline: 0;
      color: #fff;
      background-color: #1770df;
      border-radius: 4px;
    }

    // 树形结构样式
    .disease-tree {
      height: calc(100% - 90px);
      overflow: auto;
      padding: 5px;

      /deep/ .el-tree-node__content {
        height: 32px;

        &:hover {
          background-color: #f5f7fa;
        }
      }

      /deep/ .el-tree-node.is-current > .el-tree-node__content {
        background-color: #e6f1fc;
        color: #409eff;
      }

      .custom-tree-node {
        flex: 1;
        display: flex;
        align-items: center;
        font-size: 14px;
        padding-right: 8px;

        .tree-label {
          margin-left: 5px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    flex: 1;
    flex-shrink: 0;
    background: white;
    margin-left: 20px;
    overflow: auto;
    padding: 18px;
    .rightTitle {
      // line-height: 48px;
      font-family: PingFangSC-Medium;
      font-size: 18px;
      color: #2d3436;
      font-weight: 600;
    }
    .rightCont {
      flex: 1;
      overflow: auto;
      overflow: auto;
    }
  }
  .my-list-item {
    height: 30px;
    display: flex;
    cursor: pointer;
    padding-right: 10px;
    align-items: center;
    .tree-label {
      flex: 1;
      width: 200px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .my-list {
    flex: 1;
    overflow: auto;
  }
  // .is-expand .menu-icon{
  //   transform: rotate(90deg);
  // }
  .list_active {
    background: #1770df;
    color: #fff;
    border-radius: 4px;
  }
}
</style>
