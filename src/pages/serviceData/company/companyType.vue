<!--
 * @FilePath: \shenshan\KrPeis\src\pages\serviceData\company\companyType.vue
 * @Description:  单位分类页面
 * @Author: justin
 * @Date: 2024-03-19 17:23:58
 * @Version: 0.0.1
 * @LastEditors: key
 * @LastEditTime: 2025-04-07 15:11:45
*
-->

<template>
  <div class="companyType_page">
    <div class="header">
      <el-descriptions class="margin-top" title="基本信息" :column="3">
        <template slot="extra">
          <el-button
            size="small"
            @click="infoClick"
            class="blue_btn"
            icon="iconfont icon-bianji"
            >编辑</el-button
          >
        </template>
        <el-descriptions-item label="单位分类代码">{{
          menuData.companyClsCode
        }}</el-descriptions-item>
        <el-descriptions-item label="单位分类名称">{{
          menuData.companyClsName
        }}</el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="companyType-info">
      <div class="info-title">
        <h4>单位管理</h4>
        <AllBtn
          :btnList="['查询', '新建', '删除', '跳转']"
          :methodSearch="searchClick"
          :methodCreat="addClick"
          :methodDelete="deleteClick"
          :methodJump="jumpClick"
          ref="allBtn_Ref"
        />
      </div>
      <div class="info-table">
        <PublicTable
          :viewTableList.sync="searchTable"
          :theads.sync="theads"
          :cell_blue="['companyName']"
          :tableLoading.sync="tableLoading"
          @rowDblclick="handleClick"
          @rowClick="rowChange"
          :columnWidth="columnWidth"
          ref="view_Ref"
        >
          <template #columnRight v-if="G_config.physicalMode.includes('职检')">
            <el-table-column prop="" label="操作" width="110" fixed="right">
              <template slot-scope="scope">
                <el-button
                  type="primary"
                  plain
                  size="mini"
                  @click="uploadCompany(scope.row)"
                  >上传职业病信息</el-button
                >
              </template>
            </el-table-column>
          </template>
        </PublicTable>
      </div>
    </div>
    <el-drawer
      title="基本信息属性"
      :visible.sync="drawerInfo"
      :before-close="cancel"
      size="25%"
      :wrapperClosable="false"
    >
      <el-form
        :model="infoForm"
        ref="infoForm"
        :rules="infoRules"
        label-width="120px"
        class="drawer-form"
      >
        <el-form-item
          label="单位分类代码"
          prop="companyClsCode"
          v-if="disabled"
        >
          <el-input
            v-model.trim="infoForm.companyClsCode"
            size="small"
            placeholder="请输入单位分类代码"
            :disabled="disabled"
          ></el-input>
        </el-form-item>
        <el-form-item label="单位分类名称" prop="companyClsName">
          <el-input
            v-model.trim="infoForm.companyClsName"
            size="small"
            placeholder="请输入单位分类名称"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="cancel" size="small" class="search-btn"
          >取消</el-button
        >
        <el-button @click="infoSave" size="small" class="blue_btn"
          >保存</el-button
        >
      </div>
    </el-drawer>
    <el-drawer
      title="单位属性"
      :visible.sync="drawer"
      :before-close="cancel"
      :wrapperClosable="false"
      size="80%"
    >
      <DrawerForm
        :popupForm="popupForm"
        :rules="rules"
        :parentOption="viewTableList"
        :disabled="isAddOrEdit"
        @cancel="cancel"
        @save="save('ruleForm')"
        ref="ruleForm"
      ></DrawerForm>
    </el-drawer>
  </div>
</template>

<script>
import AllBtn from '../allBtn';
import PublicTable from '@/components/publicTable';
import DrawerForm from './drawer/drawerForm.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
export default {
  name: 'companyType',
  components: {
    AllBtn,
    PublicTable,
    DrawerForm
  },
  computed: {
    ...mapGetters(['G_config'])
  },
  props: {
    menuData: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      companyClsCode: '',
      companyClsName: '',
      searchInput: '',
      theads: {
        companyClsCode: '单位分类',
        companyCode: '单位编号',
        companyName: '单位名称',
        companyAlias: '单位别名',
        parent: '父单位',
        companyContact: '单位负责人',
        hospContact: '院内联系人',
        tel: '联系电话',
        address: '联系地址',
        fax: '传真',
        postCode: '邮编',
        openBank: '开户银行',
        bankAccount: '银行账号',
        pinYinCode: '拼音码',
        wuBiCode: '五笔码',
        note: '备注'
      },
      columnWidth: {
        companyClsCode: 100,
        companyCode: 100,
        companyName: 210,
        companyContact: 100,
        hospContact: 100,
        tel: 150,
        address: 200,
        bankAccount: 200
      },
      drawer: false,
      popupForm: {
        companyCode: '',
        companyName: '',
        companyAlias: '',
        companyClsCode: '',
        parent: '',
        hospContact: '',
        companyContact: '',
        tel: '',
        fax: '',
        address: '',
        postCode: '',
        openBank: '',
        bankAccount: '',
        pinYinCode: '',
        wuBiCode: '',
        note: ''
      },
      disabled: false,
      tableLoading: false,
      viewTableList: [],
      searchTable: [],
      drawerInfo: false,
      isAddOrEdit: false,
      rules: {
        // companyClsCode: [
        //   { required: true, message: "请输入单位分类代码", trigger: "blur" }
        // ],
        companyClsName: [
          { required: true, message: '请输入单位分类名称', trigger: 'blur' }
        ],
        // companyCode: [
        //   { required: true, message: "请输入单位编号", trigger: "blur" }
        // ],
        companyName: [
          { required: true, message: '请输入单位名称', trigger: 'blur' }
        ]
        // parent: [{ required: true, message: "请选择父单位", trigger: "change" }]
      },
      infoRules: {
        // companyClsCode: [
        //   { required: true, message: "请输入单位分类代码", trigger: "blur" }
        // ],
        companyClsName: [
          { required: true, message: '请输入单位分类名称', trigger: 'blur' }
        ]
      },
      infoForm: {},
      selectionRow: {}
    };
  },
  created() {},
  mounted() {},
  methods: {
    //上传职业病信息
    uploadCompany(row) {
      this.tableLoading = true;
      this.$ajax
        .paramsPost(this.$apiUrls.UploadCompany, {
          companyCode: row.companyCode
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) {
            this.$message({
              message: returnData,
              type: 'error',
              showClose: true
            });
          } else {
            this.$message({
              message: '上传成功',
              type: 'success',
              showClose: true
            });
          }
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    // 获取表格数据
    getTableData() {
      this.tableLoading = true;
      let data = {
        companyClsCode: this.menuData.companyClsCode
      };
      this.$ajax.post(this.$apiUrls.R_CodeCompany, data).then((r) => {
        console.log('r : ', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.tableLoading = false;
        this.viewTableList = returnData || [];
        this.searchTable = returnData || [];
      });
    },
    // 关闭抽屉
    cancel() {
      this.drawer = false;
      this.drawerInfo = false;
    },
    // 基本信息编辑按钮
    infoClick() {
      this.drawerInfo = true;
      this.disabled = true;
      this.infoForm = {
        companyClsCode: this.menuData.companyClsCode,
        companyClsName: this.menuData.companyClsName
      };
    },
    // 查询
    searchClick() {
      this.tableLoading = true;
      this.searchInput = this.$refs.allBtn_Ref.searchInfo.trim(); //获取组件文本框的值
      if (!this.searchInput) {
        this.$nextTick(() => {
          this.tableLoading = false;
        });
        this.searchTable = this.viewTableList;
        return;
      }
      let newTableData = [];
      this.viewTableList.map((item) => {
        if (
          item.companyClsCode.includes(this.searchInput) ||
          item.companyCode.includes(this.searchInput) ||
          item.companyName.includes(this.searchInput)
        ) {
          newTableData.push(item);
        }
      });
      this.searchTable = newTableData;
      this.$nextTick(() => {
        this.tableLoading = false;
      });
    },
    // 选中行
    rowChange(row) {
      this.selectionRow = row;
    },
    // 删除
    deleteClick() {
      if (JSON.stringify(this.selectionRow) === '{}') {
        this.$message({
          message: '请至少选择一条要删除的数据!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$prompt(`是否确定删除${this.selectionRow.companyName}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        inputPlaceholder: '请输入单位编码',
        inputValidator: (value) => {
          if (value !== this.selectionRow.companyCode) {
            return '输入的单位编码不正确';
          }
        }
      })
        .then(() => {
          this.$ajax
            .post(`${this.$apiUrls.CUD_CodeCompany}/Delete`, this.selectionRow)
            .then((r) => {
              let { success } = r.data;
              if (!success) return;
              this.$message({
                message: '删除成功',
                type: 'success',
                showClose: true
              });
              this.selectionRow = {};
              this.getTableData();
              this.$parent.getCompany();
            });
        })
        .catch(() => {});
    },
    // 跳转
    jumpClick() {
      if (JSON.stringify(this.selectionRow) === '{}') {
        this.$message({
          message: '请选择一个需要跳转的单位!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let newData = this.menuData.company.filter((item) => {
        return (
          item.companyCode === this.selectionRow.companyCode &&
          item.companyName === this.selectionRow.companyName
        );
      });
      this.$parent.companySetClick('CompanySet', newData[0]);
    },
    // 基本信息保存
    infoSave() {
      this.$ajax
        .post(`${this.$apiUrls.CUD_CodeCompanyCls}/Update`, this.infoForm)
        .then((r) => {
          let { success } = r.data;
          if (!success) return;
          this.drawerInfo = false;
          this.$message({
            message: '修改成功',
            type: 'success',
            showClose: true
          });
          this.menuData.companyClsCode = this.infoForm.companyClsCode;
          this.menuData.companyClsName = this.infoForm.companyClsName;
          this.getTableData();
          this.$parent.getCompany();
        });
    },
    // 新增按钮
    addClick() {
      this.drawer = true;
      this.isAddOrEdit = false;
      this.$nextTick(() => {
        this.resetForm('ruleForm');
        // this.popupForm.companyClsCode = this.menuData.companyClsCode;
        // this.popupForm.companyClsName = this.menuData.companyClsName;
        this.popupForm = dataUtils.deepCopy(this.popupForm);
      });
    },
    // 双击行
    handleClick(row) {
      this.drawer = true;
      this.isAddOrEdit = true;
      this.$nextTick(() => {
        this.resetForm('ruleForm');
        this.popupForm = dataUtils.deepCopy(row);
      });
    },
    // 新增
    createInfo() {
      let data = {
        ...this.popupForm,
        companyClsCode: this.menuData.companyClsCode
      };
      this.$ajax
        .post(`${this.$apiUrls.CUD_CodeCompany}/Create`, data)
        .then((r) => {
          console.log('r : ', r);
          let { success } = r.data;
          if (!success) return;
          this.drawer = false;
          this.$message({
            message: '新建成功!',
            type: 'success',
            showClose: true
          });
          this.getTableData();
          this.cancel();
          this.$parent.getCompany();
        });
    },
    // 修改
    editInfo() {
      this.$ajax
        .post(`${this.$apiUrls.CUD_CodeCompany}/Update`, this.popupForm)
        .then((r) => {
          console.log('r : ', r);
          let { success } = r.data;
          if (!success) return;
          this.drawer = false;
          this.$message({
            message: '修改成功!',
            type: 'success',
            showClose: true
          });
          this.getTableData();
          this.cancel();
          this.$parent.getCompany();
        });
    },
    // 保存
    save(formName) {
      this.$refs[formName].$refs.form_ref.validate((valid) => {
        if (valid) {
          if (!this.isAddOrEdit) {
            // 新建
            this.createInfo();
          } else {
            // 修改
            this.editInfo();
          }
        } else {
          return false;
        }
      });
    },
    // 重置表单
    resetForm(formName) {
      this.$refs[formName].$refs.form_ref.resetFields();
    }
  }
};
</script>
<style lang="less" scoped>
.companyType_page {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  font-size: 18px;
  color: #2d3436;
  .header {
    background: #fff;
    padding: 16px;
    border-radius: 4px;
    margin-bottom: 18px;
  }
  .companyType-info {
    padding: 16px;
    padding-top: 0;
    background: #fff;
    border-radius: 4px;
    flex: 1;
    flex-shrink: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .info-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .info-btn {
    display: flex;
    align-items: center;
  }
  .info-table {
    flex: 1;
    flex-shrink: 0;
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
  }
  .select {
    width: 100%;
  }
  /deep/.el-descriptions__title {
    font-size: 18px;
    color: #2d3436;
  }
  /deep/.el-descriptions-item__cell {
    font-size: 14px;
    color: #2d3436;
  }
}
</style>
