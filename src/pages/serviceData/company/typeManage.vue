<template>
  <div class="typeManage_page">
    <AllBtn
      :btnList="['查询', '新建', '删除']"
      :methodCreat="methodCreat"
      :methodDelete="deleteType"
      :methodSearch="methodSearch"
      ref="allBtn_Ref"
    />
    <PublicTable
      ref="publicTable_Ref"
      :viewTableList.sync="searchTableList"
      :theads.sync="theads"
      :cell_blue="['companyClsName']"
      @rowDblclick="rowDblclick"
      @rowClick="rowClick"
      :tableLoading="tableLoading"
    ></PublicTable>

    <!-- 抽屉 -->
    <el-drawer
      title="单位分类属性"
      size="460px"
      :visible.sync="drawerShow"
      :wrapperClosable="false"
      :direction="direction"
    >
      <el-form
        ref="ruleForm"
        :model="formInfo"
        class="form_wrap"
        label-width="110px"
        :rules="rules"
      >
        <el-form-item
          label="单位分类代码"
          prop="companyClsCode"
          v-if="isAddOrModify == 2"
        >
          <el-input
            :disabled="isAddOrModify == 2"
            v-model.trim="formInfo.companyClsCode"
            size="small"
            placeholder="请输入单位分类代码"
          ></el-input>
        </el-form-item>
        <el-form-item label="单位分类名称" prop="companyClsName">
          <el-input
            v-model.trim="formInfo.companyClsName"
            size="small"
            placeholder="请输入单位分类名称"
          ></el-input>
        </el-form-item>
        <div style="text-align: right">
          <el-button @click="drawerShow = false" size="small">取消</el-button>
          <el-button
            size="small"
            class="blue_btn"
            @click.native="saveBtn('ruleForm')"
            >保存</el-button
          >
        </div>
      </el-form>
    </el-drawer>
  </div>
</template>

<script>
import AllBtn from '../allBtn';
import PublicTable from '@/components/publicTable';
import { dataUtils } from '@/common';

export default {
  name: 'typeManage',
  components: {
    AllBtn,
    PublicTable
  },
  data() {
    return {
      currentRow: {}, //表格的高亮行
      viewTableList: [],
      theads: {
        companyClsCode: '单位分类代码',
        companyClsName: '单位分类名称'
      },
      drawerShow: false,
      tableLoading: false,
      direction: 'rtl',
      formInfo: {
        companyClsCode: '',
        companyClsName: ''
      },
      rules: {
        // companyClsCode: [
        //   { required: true, message: "请输入单位分类代码", trigger: "blur" }
        // ],
        companyClsName: [
          { required: true, message: '请输入单位分类名称', trigger: 'blur' }
        ]
      },
      isAddOrModify: 1, //1表示新建，2表示修改
      searchIpt: '',
      searchTableList: []
    };
  },
  methods: {
    // 获取单位分类列表
    getTypeList() {
      this.tableLoading = true;
      this.$ajax.post(`${this.$apiUrls.R_CodeCompanyCls}`).then((r) => {
        this.tableLoading = false;
        let { success, returnData } = r.data;
        if (!success) return;
        this.viewTableList = returnData || [];
        this.searchTableList = returnData || [];
      });
    },
    // 新建按钮点击回调
    methodCreat() {
      this.isAddOrModify = 1;
      this.drawerShow = true;
      this.$nextTick(() => {
        this.resetForm('ruleForm');
      });
    },
    // 保存
    saveBtn(ruleForm) {
      this.$refs[ruleForm].validate((valid) => {
        if (valid) {
          if (this.isAddOrModify == 1) {
            // 新建
            this.creatType();
          } else {
            // 修改
            this.modifyType();
          }
        } else {
          return false;
        }
      });
    },
    // 新建
    creatType() {
      this.$ajax
        .post(`${this.$apiUrls.CUD_CodeCompanyCls}/Create`, this.formInfo)
        .then((r) => {
          let { success } = r.data;
          if (!success) return;
          this.drawerShow = false;
          this.$message({
            message: '新建成功',
            type: 'success',
            showClose: true
          });
          this.currentRow = {};
          this.getTypeList();
          this.$parent.getCompany();
        });
    },
    // 修改
    modifyType() {
      this.$ajax
        .post(`${this.$apiUrls.CUD_CodeCompanyCls}/Update`, this.formInfo)
        .then((r) => {
          let { success } = r.data;
          if (!success) return;
          this.drawerShow = false;
          this.currentRow = {};
          this.$message({
            message: '修改成功',
            type: 'success',
            showClose: true
          });
          this.getTypeList();
          this.$parent.getCompany();
        });
    },
    // 表格的双击回调
    rowDblclick(row) {
      this.isAddOrModify = 2;
      this.drawerShow = true;
      this.$nextTick(() => {
        this.resetForm('ruleForm');
        this.formInfo = dataUtils.deepCopy(row);
      });
    },
    // 表格单击的回调
    rowClick(row) {
      this.currentRow = row;
    },
    // 查询
    methodSearch() {
      this.searchIpt = this.$refs.allBtn_Ref.searchInfo.trim(); //获取组件文本框的值

      if (!this.searchIpt) {
        this.$nextTick(() => {
          this.tableLoading = false;
        });
        return (this.searchTableList = this.viewTableList);
      }
      let newTableData = [];
      this.viewTableList.map((item) => {
        if (
          item.companyClsCode.includes(this.searchIpt) ||
          item.companyClsName.includes(this.searchIpt)
        ) {
          newTableData.push(item);
        }
      });
      this.searchTableList = newTableData;
      this.$nextTick(() => {
        this.tableLoading = false;
      });
    },
    // 删除
    deleteType() {
      if (JSON.stringify(this.currentRow) === '{}') {
        this.$message({
          message: '请点击选择下列类型删除',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$prompt(`是否确定删除${this.currentRow.companyClsName}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        inputPlaceholder: '请输入单位分类代码',
        inputValidator: (value) => {
          if (value !== this.currentRow.companyClsCode) {
            return '输入的单位分类代码不正确';
          }
        }
      })
        .then(() => {
          this.$ajax
            .post(`${this.$apiUrls.CUD_CodeCompanyCls}/Delete`, this.currentRow)
            .then((r) => {
              let { success } = r.data;
              if (!success) return;
              this.$message({
                message: '删除成功',
                type: 'success',
                showClose: true
              });
              this.currentRow = {};
              this.getTypeList();
              this.$parent.getCompany();
            });
        })
        .catch(() => {});
    },
    // 重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields();
    }
  },
  created() {
    this.getTypeList();
  },
  mounted() {}
};
</script>
<style lang="less" scoped>
.typeManage_page {
  background-color: #fff;
  padding: 0 15px 15px 15px;
  .form_wrap {
    flex: 1;
  }
}
</style>
<style lang="less">
.typeManage_page {
  .el-drawer__body {
    padding: 0 !important;
    background-color: #fff !important;
  }
  .el-form-item__label {
    color: #2d3436;
    font-weight: 600;
  }
  .el-input__inner {
    color: #2d3436;
  }
}
</style>
