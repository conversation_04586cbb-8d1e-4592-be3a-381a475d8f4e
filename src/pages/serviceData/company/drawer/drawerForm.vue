<!--
 * @FilePath: \KrPeis\src\pages\serviceData\company\drawer\drawerForm.vue
 * @Description: 单位属性表单 
 * @Author: 
 * @Date: 2024-03-19 17:23:58
 * @Version: 0.0.1
 * @LastEditors: justin
 * @LastEditTime: 2024-06-21 16:24:22
*
-->
<template>
  <div class="drawer-form">
    <div class="body-wrapper">
      <el-form
        :model="popupForm"
        ref="form_ref"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <!-- <el-form-item label="单位分类代码" prop="companyClsCode">
            <el-input
              v-model.trim="popupForm.companyClsCode"
              size="small"
              placeholder="请输入单位分类代码"
              :disabled="true"
            ></el-input>
          </el-form-item> -->
            <el-form-item label="单位编号" prop="companyCode" v-if="disabled">
              <el-input
                v-model.trim="popupForm.companyCode"
                size="small"
                placeholder="请输入单位编号"
                :disabled="disabled"
              ></el-input>
            </el-form-item>
            <el-form-item label="单位名称" prop="companyName">
              <el-input
                v-model.trim="popupForm.companyName"
                size="small"
                placeholder="请输入单位名称"
              ></el-input>
            </el-form-item>
            <el-form-item label="单位别名" prop="companyAlias">
              <el-input
                v-model.trim="popupForm.companyAlias"
                size="small"
                placeholder="请输入单位别名"
              ></el-input>
            </el-form-item>
            <el-form-item label="父单位" prop="parent">
              <el-select
                class="select"
                v-model.trim="popupForm.parent"
                placeholder="请选择父单位"
                size="small"
                filterable
                remote
                reserve-keyword
                clearable
              >
                <el-option
                  v-for="(item, index) in $options.filters.filterParentCompany(
                    parentOption,
                    popupForm
                  )"
                  :key="index"
                  :label="item.companyName"
                  :value="item.companyCode"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="拼音码" prop="pinYinCode">
              <el-input
                v-model.trim="popupForm.pinYinCode"
                size="small"
                placeholder="请输入拼音码"
              ></el-input>
            </el-form-item>
            <el-form-item label="单位负责人" prop="companyContact">
              <el-input
                v-model.trim="popupForm.companyContact"
                size="small"
                placeholder="请输入单位负责人"
              ></el-input>
            </el-form-item>
            <el-form-item label="院内联系人" prop="hospContact">
              <el-select
                class="select"
                v-model.trim="popupForm.hospContact"
                placeholder="请选择院内联系人"
                size="small"
              >
                <el-option
                  v-for="(item, index) in companyContactOption"
                  :key="index"
                  :label="item.name"
                  :value="item.operatorCode"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="联系电话" prop="tel">
              <el-input
                v-model.trim="popupForm.tel"
                size="small"
                placeholder="请输入联系电话"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="联系地址" prop="address">
              <el-input
                v-model.trim="popupForm.address"
                size="small"
                placeholder="请输入联系地址"
              ></el-input>
            </el-form-item>
            <el-form-item label="传真" prop="fax">
              <el-input
                v-model.trim="popupForm.fax"
                size="small"
                placeholder="请输入传真"
              ></el-input>
            </el-form-item>
            <el-form-item label="邮编" prop="postCode">
              <el-input
                v-model.trim="popupForm.postCode"
                size="small"
                placeholder="请输入邮编"
              ></el-input>
            </el-form-item>
            <el-form-item label="开户银行" prop="openBank">
              <el-input
                v-model.trim="popupForm.openBank"
                size="small"
                placeholder="请输入开户银行"
              ></el-input>
            </el-form-item>
            <el-form-item label="银行账号" prop="bankAccount">
              <el-input
                v-model.trim="popupForm.bankAccount"
                size="small"
                placeholder="请输入银行账号"
              ></el-input>
            </el-form-item>
            <el-form-item label="五笔码" prop="wuBiCode">
              <el-input
                v-model.trim="popupForm.wuBiCode"
                size="small"
                placeholder="请输入五笔码"
              ></el-input>
            </el-form-item>

            <el-form-item label="备注" prop="note">
              <el-input
                v-model.trim="popupForm.note"
                size="small"
                placeholder="请输入备注"
              ></el-input>
            </el-form-item>

            <el-form-item label="排序" prop="sortIndex">
              <el-input-number
                v-model="popupForm.sortIndex"
                controls-position="right"
                size="small"
                placeholder="请输入排序"
                :min="0"
                :step="1"
                :precision="0"
              ></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="G_config.physicalMode.includes('职检')">
          <el-col :span="12">
            <el-form-item label="所属地区" prop="addressSelectedList">
              <el-cascader
                v-model="addressSelectedList"
                :options="addressList"
                :props="addressProps"
                :placeholder="loadingCascader ? '加载中...' : '请选择地区'"
                clearable
                @change="addressChange"
                v-if="showCascader"
              ></el-cascader>
            </el-form-item>

            <el-form-item label="行业分类" prop="industryCode">
              <el-select
                class="select"
                v-model.trim="popupForm.industryCode"
                placeholder="请选择行业分类(可检索)"
                size="small"
                filterable
                clearable
                remote
                :remote-method="getIndustryCodeList"
                :loading="loadingIndustryCode"
              >
                <el-option
                  v-for="(item, index) in industryCodeList"
                  :key="index"
                  :label="item.industryName"
                  :value="item.industryCode"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="经济类型" prop="economicCode">
              <el-select
                class="select"
                v-model.trim="popupForm.economicCode"
                placeholder="请选择经济类型(可检索)"
                size="small"
                filterable
                clearable
              >
                <el-option
                  v-for="(item, index) in economicCodeList"
                  :key="index"
                  :label="item.economicName"
                  :value="item.economicCode"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="企业规模" prop="enterpriseSizeCode">
              <el-select
                class="select"
                :value="popupForm.enterpriseSizeCode || null"
                placeholder="请选择企业规模"
                size="small"
                clearable
                @clear="popupForm.enterpriseSizeCode = 0"
                @change="
                  (val) => {
                    popupForm.enterpriseSizeCode = val;
                  }
                "
              >
                <el-option
                  v-for="(item, index) in G_occupationalEnterpriseSize"
                  :key="index"
                  :label="item.label"
                  :value="Number(item.value)"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="社会信用代码" prop="creditCode">
              <el-input
                v-model.trim="popupForm.creditCode"
                size="small"
                placeholder="请输入社会信用代码"
              ></el-input>
            </el-form-item>

            <el-form-item label="职工人数" prop="employeesNumber">
              <el-input-number
                v-model="popupForm.employeesNumber"
                :controls="false"
                :min="0"
                :precision="0"
                size="small"
                placeholder="请输入职工人数"
              ></el-input-number>
            </el-form-item>

            <el-form-item
              label="接触职业病危害因素人数"
              prop="contactHazardEmployeesNumber"
            >
              <el-input-number
                v-model="popupForm.contactHazardEmployeesNumber"
                :controls="false"
                :min="0"
                :precision="0"
                size="small"
                placeholder="请输入接触职业病危害因素人数"
              ></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="footer-wrapper">
      <el-button @click="cancel" size="small" class="search-btn"
        >取消</el-button
      >
      <el-button @click="save" size="small" class="blue_btn">保存</el-button>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  props: {
    popupForm: {
      type: Object,
      default: {}
    },
    rules: {
      type: Object,
      default: {}
    },
    disabled: {
      type: Boolean,
      default: false
    },
    parentOption: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      companyContactOption: [], // 单位负责人选项
      addressSelectedList: [], // 用于存储选中的地区信息
      addressList: [], // 用于存储地区数据
      addressProps: {
        // 地区动态远程加载数据
        lazy: true,
        lazyLoad: (node, resolve) => {
          this.getAddressList(node, resolve, null);
        }
      },
      showCascader: false, // 利用组件销毁特性，控制地区设置绑定值
      loadingCascader: false,
      industryCodeList: [],
      economicCodeList: [],
      loadingIndustryCode: false
    };
  },
  async created() {
    await this.getCompanyContact();
    await this.getEconomicCodeList();
    await this.getIndustryCodeList();
  },
  watch: {
    popupForm: {
      handler(newVal, oldVal) {
        this.dataInit();
        this.showCascader = false;
        this.$nextTick(() => {
          this.showCascader = true;
        });
        this.bindAddressInit();
      },
      immediate: true
    }
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_occupationalEnterpriseSize', 'G_config'])
  },
  methods: {
    /**
     * @author: justin
     * @description: 数据初始化
     * @return {*}
     */
    dataInit() {
      if (
        this.popupForm.codeOccupationalEconomicType &&
        !this.economicCodeList.some(
          (x) =>
            x.economicCode ==
            this.popupForm.codeOccupationalEconomicType.economicCode
        )
      ) {
        this.economicCodeList.push({
          economicCode:
            this.popupForm.codeOccupationalEconomicType.economicCode,
          economicName: this.popupForm.codeOccupationalEconomicType.economicName
        });
      }
      if (
        this.popupForm.codeOccupationalIndustry &&
        !this.industryCodeList.some(
          (x) =>
            x.industryCode ==
            this.popupForm.codeOccupationalIndustry.industryCode
        )
      ) {
        this.industryCodeList.push({
          industryCode: this.popupForm.codeOccupationalIndustry.industryCode,
          industryName: this.popupForm.codeOccupationalIndustry.industryName
        });
      }
    },
    // 获取单位负责人
    async getCompanyContact() {
      await this.$ajax.post(this.$apiUrls.GetAllOperator).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyContactOption = returnData || [];
      });
    },

    /**
     * @author: justin
     * @description: 获取经济类型列表
     * @return {*}
     */
    async getEconomicCodeList() {
      if (!this.G_config.physicalMode.includes('职检')) return;
      await this.$ajax
        .post(this.$apiUrls.ReadCodeOccupationalEconomicType, [])
        .then((res) => {
          let { success, returnData } = res.data;
          if (!success) return;
          this.economicCodeList = returnData || [];
        });
    },

    /**
     * @author: justin
     * @description: 获取行业分类列表
     * @param {*} keyword 关键字
     * @return {*}
     */
    async getIndustryCodeList(keyword) {
      if (!this.G_config.physicalMode.includes('职检')) return;
      this.loadingIndustryCode = true;
      await this.$ajax
        .post(this.$apiUrls.ReadCodeOccupationalIndustryByPage, {
          keyword: keyword,
          pageNum: 1,
          pageSize: 50
        })
        .then((res) => {
          let { success, returnData } = res.data;
          if (!success) return;

          this.industryCodeList = returnData || [];
          this.dataInit();
        })
        .finally(() => {
          this.loadingIndustryCode = false;
        });
    },

    /**
     * @author: justin
     * @description: 获取地址列表
     * @param {*} node  节点数据
     * @param {*} resolve 回调函数
     * @param {*} keyword 关键字
     * @return {*}
     */
    getAddressList(node, resolve, keyword) {
      if (!this.G_config.physicalMode.includes('职检')) return;
      // node 节点数据 node.value => 当前节点的值
      // level: 层级
      const { level, value } = node;
      this.loadingCascader = true;
      this.$ajax
        .post(this.$apiUrls.ReadCodeOccupationalAddressByPage, {
          keyword: keyword,
          parentCode: level <= 0 ? '&' : value,
          pageNum: 1,
          pageSize: 500
        })
        .then((res) => {
          const { success, returnData } = res.data;
          this.loadingCascader = false;
          if (!success || !returnData || returnData.length === 0) {
            resolve([]);
            return;
          }

          resolve(
            returnData.map((item) => {
              return {
                value: item.addressCode,
                label: item.addressShortName,
                leaf: level >= 3
              };
            })
          );
        });
    },

    /**
     * @author: justin
     * @description: 所属地区变化事件
     * @param {*} value
     * @return {*}
     */
    addressChange(value) {
      const addressCode =
        value && value.length > 0 ? value[value.length - 1] : null;
      this.$set(this.popupForm, 'addressCode', addressCode);
    },

    /**
     * @author: justin
     * @description: 绑定地址初始化
     * @return {*}
     */
    bindAddressInit() {
      if (this.popupForm.addreassRelation) {
        this.addressSelectedList =
          Object.keys(this.popupForm.addreassRelation) || [];
      }
    },

    cancel() {
      this.$emit('cancel');
    },
    save() {
      this.$emit('save');
    }
  },

  /**
   * @description: 过滤器
   * @return {*}
   */
  filters: {
    /**
     * @description: 过滤父级单位
     * @return {Array}
     */
    filterParentCompany: (arr, popupForm) => {
      if (!arr) return [];

      arr = arr.filter((x) => !x.parent || x.parent.trim().length === 0);
      if (!popupForm) return arr;

      arr = arr.filter((x) => x.companyCode !== popupForm.companyCode);
      return arr;
    }
  }
};
</script>

<style lang="less" scoped>
.drawer-form {
  .body-wrapper {
    height: 78vh;
    overflow-y: auto;
    overflow-x: hidden;

    /deep/ .el-form {
      width: calc(100% - 20px);
    }
  }

  .footer-wrapper {
    display: flex;
    justify-content: flex-end;
    padding-top: 20px;
  }
  /deep/.select,
  .el-cascader,
  .el-input-number {
    width: 100%;
  }
}
</style>
