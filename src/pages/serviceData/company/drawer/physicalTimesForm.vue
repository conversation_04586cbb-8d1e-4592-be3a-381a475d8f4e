<template>
  <div class="phytimes-form">
    <el-form
      :model="parameters"
      ref="ruleForm2"
      :rules="rules"
      label-width="110px"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="单位编码" prop="companyCode">
            <el-input
              v-model.trim="parameters.companyCode"
              size="small"
              placeholder="请输入单位编码"
              disabled
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="体检次数" prop="companyTimes">
            <el-input
              v-model.number.trim="parameters.companyTimes"
              autocomplete="off"
              size="small"
              placeholder="请输入体检次数"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-col :span="12">
            <el-form-item
              label="开始日期"
              prop="beginDate"
              style="margin-bottom: 0"
            >
              <el-date-picker
                size="small"
                type="date"
                placeholder="请选择开始日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                v-model.trim="parameters.beginDate"
                style="width: 100%"
                :picker-options="pickerOptions"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束日期" style="margin-bottom: 0">
              <el-date-picker
                size="small"
                type="date"
                placeholder="请选择结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                v-model.trim="parameters.endDate"
                style="width: 100%"
                :picker-options="pickerOptions"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-col>

        <el-col :span="12">
          <el-form-item label="预约截止日期" prop="onLineEndDate">
            <el-date-picker
              size="small"
              v-model.trim="parameters.onLineEndDate"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择预约截止日期"
              :picker-options="pickerOptions"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="院内联系人" prop="hospContact">
            <el-select
              class="select"
              v-model.trim="parameters.hospContact"
              placeholder="请选择院内联系人"
              size="small"
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in companyContactOption"
                :key="index"
                :label="item.name"
                :value="item.operatorCode"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="tel">
            <el-input
              v-model.trim="parameters.tel"
              autocomplete="off"
              size="small"
              placeholder="请输入联系电话"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="开户银行" prop="openBank">
            <el-input
              v-model.trim="parameters.openBank"
              autocomplete="off"
              size="small"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="银行账号" prop="bankAccount">
            <el-input
              v-model.trim="parameters.bankAccount"
              autocomplete="off"
              size="small"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="税号" prop="taxID">
            <el-input
              v-model.trim="parameters.taxID"
              autocomplete="off"
              size="small"
              placeholder="请输入税号"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发票抬头" prop="invoiceHeader">
            <el-input
              v-model.trim="parameters.invoiceHeader"
              autocomplete="off"
              size="small"
              placeholder="请输入发票抬头"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="外检标识" prop="isOutside">
            <el-select
              class="select"
              v-model.trim="parameters.isOutside"
              placeholder="请选择外检标识"
              size="small"
            >
              <el-option label="是" :value="true"></el-option>
              <el-option label="否" :value="false"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="备注" prop="note">
            <el-input
              v-model.trim="parameters.note"
              autocomplete="off"
              size="small"
              type="textarea"
              placeholder="请输入备注"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12"
          ><el-form-item label="单位负责人" prop="hospContact">
            <el-input
              v-model.trim="parameters.hospContact"
              autocomplete="off"
              size="small"
              placeholder="请输入单位负责人"
            ></el-input> </el-form-item
        ></el-col>
      </el-row>
    </el-form>
    <div class="dialog-footer">
      <el-button @click="cancel" size="small" class="search-btn"
        >取消</el-button
      >
      <el-button @click="save" size="small" class="blue_btn">保存</el-button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    parameters: {
      type: Object,
      default: {}
    },
    rules: {
      type: Object,
      default: {}
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      companyContactOption: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 86400000;
        }
      }
    };
  },
  created() {
    this.getCompanyContact();
  },
  methods: {
    cancel() {
      this.$emit('cancel');
    },
    save() {
      this.$emit('save');
    },
    //获取单位负责人
    getCompanyContact() {
      this.$ajax.post(this.$apiUrls.GetAllOperator).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyContactOption = returnData || [];
      });
    }
  }
};
</script>

<style lang="less" scoped>
.phytimes-form {
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
  }
  .el-input {
    width: 100%;
  }
  .select {
    width: 100%;
  }
  .line {
    text-align: center;
  }
}
</style>
