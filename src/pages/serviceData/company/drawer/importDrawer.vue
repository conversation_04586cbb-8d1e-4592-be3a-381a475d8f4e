<template>
  <div class="importDrawer">
    <div class="import-form">
      <el-form ref="form" :model="form" label-width="120px">
        <el-row :gutter="18" class="row">
          <el-col :span="18">
            <el-form-item label="文件路径">
              <el-input
                v-model.trim="form.file"
                size="small"
                readonly
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-upload
              class="upload-demo"
              action=""
              :on-change="handleChange"
              :http-request="httpRequest"
              ref="upload"
              accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
              :auto-upload="false"
              :show-file-list="false"
            >
              <el-button
                size="small"
                class="blue_btn btn"
                icon="iconfont icon-juxingxuanze"
                style="margin-bottom: 12px"
                >选择文件</el-button
              >
            </el-upload>
          </el-col>
        </el-row>
        <el-row :gutter="18" class="row">
          <el-col :span="18">
            <el-form-item label="文件名称">
              <el-input
                v-model.trim="form.fileName"
                size="small"
                readonly
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-button
              class="violet_btn btn"
              size="small"
              icon="iconfont icon-daoru"
              @click="importData"
              style="margin-bottom: 12px"
              >确认导入</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="import-table">
      <div class="error-table">
        <div class="table-title">
          <h4>信息有误人数：{{ errorTableData.length }}人</h4>
          <div>
            <el-checkbox v-model="checked">自动导出</el-checkbox>
            <el-button
              class="yellow_btn btn"
              size="small"
              icon="iconfont icon-daochu"
              @click="exportTable"
              >导出</el-button
            >
          </div>
        </div>
        <div class="table">
          <PublicTable
            :cell_red="cell_red"
            :isSortShow="false"
            :theads="C_theads"
            :viewTableList.sync="errorTableData"
            :tableLoading.sync="tableLoading"
            :columnWidth="columnWidth"
          ></PublicTable>
        </div>
      </div>
      <div class="correct-table">
        <div class="table-title">
          <h4>可导入人数：{{ tableData.length }}人</h4>
        </div>
        <div class="table">
          <PublicTable
            :isSortShow="false"
            :theads="C_thead"
            :viewTableList.sync="tableData"
            :tableLoading.sync="tableLoading"
            :columnWidth="columnWidth"
          ></PublicTable>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import PublicTable from '@/components/publicTable';
import XLSX from 'xlsx';
import { export2Excel } from '@/common/excelUtil';
import moment from 'moment';
import { mapGetters, mapMutations } from 'vuex';
export default {
  name: 'importDrawer',
  components: {
    PublicTable
  },
  props: {
    companyTimes: {
      type: Number,
      default: 0
    },
    companyCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      checked: false,
      form: {
        fileName: '',
        file: ''
      },
      options: [],
      occupationThead: {
        isOccupation: '是否职业检',
        jobId: '工号',
        totalYearsOfWork: '总工龄年',
        totalMonthsOfWork: '总工龄月',
        startDateOfHazards: '开始接害日期',
        jobStatus: '在岗状态',
        jobType: '职业工种',
        hazards: '危害因素',
        workshop: '车间',
        protectiveMeasures: '防护措施'
      },
      thead: {
        // errMsg: "错误信息",
        companyCode: '单位编码',
        companyDeptName: '单位部门',
        clusterCode: '套餐代码',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        tel: '联系电话',
        marryStatus: '婚姻状况',
        birthday: '出生日期',
        cardType: '证件类型',
        cardNo: '证件号码',
        // nativePlace: "籍贯",
        jobName: '工种',
        isVIP: '是否VIP',
        note: '备注'
      },
      theads: {
        errMsg: '错误信息',
        companyCode: '单位编码',
        companyDeptName: '单位部门',
        clusterCode: '套餐代码',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        tel: '联系电话',
        marryStatus: '婚姻状况',
        birthday: '出生日期',
        cardType: '证件类型',
        cardNo: '证件号码',
        // nativePlace: "籍贯",
        jobName: '工种',
        isVIP: '是否VIP',
        note: '备注'
      },
      columnWidth: {
        companyCode: 100,
        companyDeptName: 200,
        birthday: 120,
        tel: 120,
        cardType: 100,
        cardNo: 180,
        errMsg: 200,
        clusterCode: 120
      },
      cell_red: ['errMsg'],
      tableLoading: false,
      tableData: [],
      errorTableData: [],
      fileTemp: null
    };
  },
  created() {},
  computed: {
    ...mapGetters(['G_userInfo', 'G_config']),
    C_thead() {
      if (this.G_config.physicalMode.includes('职检')) {
        return {
          ...this.thead,
          ...this.occupationThead
        };
      }
      return this.thead;
    },
    C_theads() {
      if (this.G_config.physicalMode.includes('职检')) {
        return {
          ...this.theads,
          ...this.occupationThead
        };
      }
      return this.theads;
    }
  },
  methods: {
    ...mapMutations(['M_CompanyImport']),
    //  选择文件
    handleChange(file, fileList) {
      this.errorTableData = [];
      this.form.file =
        document.getElementsByClassName('el-upload__input')[0].value;
      let reg = /\.\w+$/;
      this.form.fileName = file.name.replace(reg, '');
      this.fileTemp = file.raw;
      if (this.fileTemp) {
        if (
          this.fileTemp.type ==
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
          this.fileTemp.type == 'application/vnd.ms-excel'
        ) {
          this.fileUpload();
        } else {
          this.$message({
            type: 'warning',
            message: '文件格式错误，请重新选择！',
            showClose: true
          });
        }
      } else {
        this.$message({
          type: 'warning',
          message: '请选择文件！',
          showClose: true
        });
      }
    },
    // 手动上传文件
    httpRequest(params) {
      const formData = new FormData();
      formData.append('files', params.file);
      this.$ajax
        .post(this.$apiUrls.UploadTeamImport, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: ''
          }
        })
        .then((r) => {
          console.log('UploadTeamImport: ', r);
          let { success, returnData } = r.data;
          if (!success) return;
        });
      // this.$message({
      //   type: "success",
      //   message: "上传文件成功！",
      //   showClose: true
      // });
    },
    // 文件导入解析
    fileUpload() {
      this.file = event.currentTarget.files[0];
      let f = this.file;
      let reader = new FileReader();
      let rABS = false; // 是否将文件读取为二进制字符串

      FileReader.prototype.readAsBinaryString = (f) => {
        let binary = '';
        let wb; // 读取完成的数据
        let outData;

        reader.onload = (e) => {
          let bytes = new Uint8Array(reader.result);
          let length = bytes.byteLength;
          for (var i = 0; i < length; i++) {
            binary += String.fromCharCode(bytes[i]);
          }

          if (rABS) {
            wb = XLSX.read(btoa(fixdata(binary)), {
              //手动转化
              type: 'base64'
            });
          } else {
            wb = XLSX.read(binary, {
              type: 'binary'
            });
          }

          // 跳过表格前三行
          outData = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]], {
            range: 2
          });

          // 表格数据
          console.log('outData：', outData);

          //去除RFC2822/ISO date formats的警告
          moment.suppressDeprecationWarnings = true;

          this.tableData = outData.map((item) => {
            return {
              tel: (item['联系电话'] ?? '').toString(),
              companyCode:
                item['单位编码']?.replace(/\s+/g, '') ?? this.companyCode,
              companyDeptName: item['单位部门'] ?? '',
              clusterCode: item['套餐代码'] ?? '',
              name: item['姓名'] ?? '',
              sex: item['性别'] ?? '',
              age: isNaN(Number(item['年龄']))
                ? undefined
                : Number(item['年龄']),
              marryStatus: item['婚姻状况'] ?? '',
              birthday:
                item['出生日期'] === undefined
                  ? ''
                  : moment(item['出生日期']).format('YYYY-MM-DD'),
              // nativePlace:
              //   item["__EMPTY_6"] === undefined ? "" : item["__EMPTY_6"],
              cardType: item['证件类型'] ?? '',
              cardNo:
                item['证件号码'] === undefined ? '' : item['证件号码'].trim(),
              jobName: item['工种'] ?? '',
              isVIP: item['VIP'] ?? '',
              note: item['备注'] ?? '',
              operatorCode: this.G_userInfo.codeOper.operatorCode,
              companyTimes: this.companyTimes,
              isOccupation:
                (item['是否职业检'] ??
                  (this.G_config.physicalMode.length == 1 &&
                  this.G_config.physicalMode.includes('职检')
                    ? true
                    : '否')) == '是'
                  ? true
                  : false,
              jobId: (item['工号'] ?? '').toString(),
              totalYearsOfWork: item['总工龄年'] ?? '',
              totalMonthsOfWork: item['总工龄月'] ?? '',
              startDateOfHazards: item['开始接害日期'] ?? '',
              jobType: item['工种'] ?? '',
              hazards: item['危害因素'] ?? '',
              jobStatus: item['在岗状态'] ?? '',
              workshop: item['车间'] ?? '',
              protectiveMeasures: item['防护措施'] ?? '',
              ageUnit: '',
              errMsg: ''
            };
          });
          console.log('this.tableData: ', this.tableData);
        };
        reader.readAsArrayBuffer(f);
      };
      if (rABS) {
        reader.readAsArrayBuffer(f);
      } else {
        reader.readAsBinaryString(f);
      }
    },
    // 确认导入
    importData() {
      if (!this.form.files && !this.form.fileName) {
        this.$message({
          type: 'warning',
          message: '请先选择文件，再点击导入！',
          showClose: true
        });
        return;
      }
      if (this.errorTableData && this.errorTableData.length > 0) {
        this.$message({
          type: 'warning',
          message: '请先处理验证不通过的信息后，再重新导入！',
          showClose: true
        });
        return;
      }
      if (
        this.tableData.some(
          (item) =>
            item.companyCode != this.companyCode && item.companyCode != ''
        )
      ) {
        this.$message({
          type: 'warning',
          message: `与选择的单位编码(${this.companyCode})不一致，请检查！`,
          showClose: true
        });
        return;
      }
      this.$ajax
        .post(this.$apiUrls.CompanyImport, this.tableData)
        .then((r) => {
          console.log('CompanyImport : ', r);
          let { success, returnData } = r.data;
          // if (!success) return;
          if (returnData.errorList.length === 0) {
            this.$message({
              type: 'success',
              message: '导入成功！',
              showClose: true
            });
            this.$nextTick(() => {
              this.$refs.upload.submit();
              this.$parent.$parent.importDrawer = false;
            });
          }
          this.tableData = returnData.correctList;
          this.errorTableData = returnData.errorList;
          this.$parent.$parent.searchClick();
          this.autoExport();
        })
        .catch((err) => {
          this.M_CompanyImport({ flag: false, msgData: {} });
        });
    },
    // 自动导出
    autoExport() {
      if (this.checked && this.errorTableData.length !== 0) {
        const entries = Object.entries(this.C_theads);
        const columns = [];
        for (const [keys, values] of entries) {
          columns.push({
            key: keys,
            title: values
          });
        }
        this.errorTableData.map((item, i) => {
          item.index = i + 1;
        });
        const title = '错误名单';
        this.$nextTick(() => {
          export2Excel(columns, this.errorTableData, title);
        });
      }
    },
    // 导出excel
    exportTable() {
      if (this.errorTableData.length === 0) {
        return;
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const entries = Object.entries(this.theads);
        const columns = [];
        for (const [keys, values] of entries) {
          columns.push({
            key: keys,
            title: values
          });
        }
        this.errorTableData.map((item, i) => {
          item.index = i + 1;
        });
        const title = '错误名单 ' + moment(new Date()).format('YYYY-MM-DD');
        this.$nextTick(() => {
          export2Excel(columns, this.errorTableData, title);
        });
      });
    },
    // 重置
    reset() {
      this.form = {};
      this.tableData = [];
      this.errorTableData = [];
      this.checked = false;
    }
  }
};
</script>

<style lang="less" scoped>
.importDrawer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .import-form {
    margin-top: 22px;
    margin-bottom: 10px;
    padding-right: 10px;
  }
  .row {
    display: flex;
    align-items: center;
  }
  .import-select {
    width: 100%;
  }
  .import-table {
    background: #fff;
    padding: 16px;
    flex: 1;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
  }

  .table-title {
    font-size: 18px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 18px;
  }
  .error-table,
  .correct-table {
    flex: 1;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
  }
  .error-table {
    margin-bottom: 28px;
  }
  .table {
    flex: 1;
    flex-shrink: 0;
  }
  .yellow_btn {
    margin-left: 28px;
  }
  .el-form-item {
    margin-bottom: 12px;
  }
  .btn {
    padding: 6.5px 10px;
  }
}
</style>
