<template>
  <div class="allocationCluster">
    <div>
      <el-form :inline="true" :model="formInline" class="demo-form-inline">
        <el-form-item label="性别">
          <el-select
            class="select"
            v-model.trim="formInline.sex"
            placeholder="请选择"
            size="small"
            clearable
          >
            <el-option
              :label="item.label"
              :value="item.value"
              v-for="item in G_sexList"
              :key="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="年龄段">
          <el-col :span="11">
            <el-input
              v-model.trim="formInline.minAge"
              size="small"
              class="select"
              placeholder="请输入"
              clearable
            ></el-input>
          </el-col>
          <el-col class="line" :span="2">-</el-col>
          <el-col :span="11">
            <el-input
              v-model.trim="formInline.maxAge"
              class="select"
              size="small"
              placeholder="请输入"
              clearable
            ></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="婚姻状态">
          <el-select
            class="select"
            v-model.trim="formInline.marryStatus"
            placeholder="请选择"
            size="small"
            clearable
          >
            <el-option
              :label="item.label"
              :value="item.value"
              v-for="item in G_marriageStatus"
              :key="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model.trim="formInline.inputValue"
            size="small"
            placeholder="姓名/证件号"
            class="input"
            clearable
            @keyup.enter.native="getOrSearchClick"
          ></el-input>
          <el-button
            size="small"
            class="blue_btn btn"
            @click="getOrSearchClick"
            icon="iconfont icon-search"
            >搜索</el-button
          >
        </el-form-item>
        <el-form-item>
          <el-button
            size="small"
            class="blue_btn btn"
            @click="saveClick"
            icon="iconfont icon-baocun"
            >保存</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <div class="allocation-table">
      <div class="table">
        <PublicTable
          isCheck
          :isSortShow="false"
          :theads="theads"
          :viewTableList.sync="tableData"
          :tableLoading.sync="tableLoading"
          :columnWidth="columnWidth"
          @selectionChange="selectionChange"
          :cell_yellow="cell_yellow"
        ></PublicTable>
      </div>
      <div class="cluster-table">
        <div class="cluster-title">选择体检套餐</div>
        <div class="cluster-btn">
          <el-input
            v-model.trim="searchCluster"
            size="small"
            placeholder="请输入搜索内容"
            clearable
            @keyup.enter.native="searchClusterClick"
          ></el-input>
          <el-button
            size="small"
            class="blue_btn btn"
            @click="searchClusterClick"
            icon="iconfont icon-search"
            >搜索</el-button
          >
        </div>
        <PublicTable
          style="overflow: auto"
          ref="singleTable"
          :viewTableList.sync="searchClusterTable"
          :theads.sync="clusterTheads"
          :showHeader="false"
          :isSortShow="false"
          :cell_red="cell_red"
          :columnWidth="columnWidth"
          @currentChange="handleCurrentChange"
        ></PublicTable>
      </div>
    </div>
  </div>
</template>

<script>
import PublicTable from '@/components/publicTable';
import { mapGetters } from 'vuex';

export default {
  name: 'allocationCluster',
  components: {
    PublicTable
  },
  data() {
    return {
      formInline: {
        sex: '',
        minAge: '',
        maxAge: '',
        marryStatus: '',
        inputValue: ''
      },
      theads: {
        regNo: '体检号',
        name: '姓名',
        cardType: '证件类型',
        cardNo: '证件号码',
        age: '年龄',
        sex: '性别',
        marryStatus: '婚姻状态',
        deptName: '单位部门',
        allotStatus: '分配状态',
        clusName: '套餐名',
        note: '备注'
        // isMain: ""
      },
      tableData: [],
      tableLoading: false,
      clusterTheads: {
        clusterCode: '套餐编号',
        clusterName: '套餐名称',
        price: '单价'
      },
      clusterTable: [],
      columnWidth: {
        regNo: 130,
        cardNo: 200,
        cardType: 100
      },
      cell_red: ['price'],
      cell_yellow: ['allotStatus'],
      currentRow: '',
      selectionRow: [],
      searchCluster: '',
      searchClusterTable: []
    };
  },
  created() {},
  computed: {
    ...mapGetters(['G_EnumList', 'G_sexList', 'G_marriageStatus'])
  },
  methods: {
    // 获取套餐列表
    getCompanyCluster() {
      this.$ajax
        .post(this.$apiUrls.GetCompanyCluster, '', {
          query: {
            companyCode: this.$parent.$parent._props.companyInfo.companyCode,
            companyTimes: this.$parent.$parent._props.companyInfo.companyTimes
          }
        })
        .then((r) => {
          console.log('GetCompanyCluster: ', r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.clusterTable = returnData || [];
          this.searchClusterTable = returnData || [];
        });
    },
    // 人员信息获取或搜索
    getOrSearchClick() {
      let data = {
        companyCode: this.$parent.$parent._props.companyInfo.companyCode,
        companyTimes: this.$parent.$parent._props.companyInfo.companyTimes,
        allotType: 0,
        sex: this.formInline.sex ? this.formInline.sex : -1,
        minAge: this.formInline.minAge ? this.formInline.minAge : -1,
        maxAge: this.formInline.maxAge ? this.formInline.maxAge : -1,
        marryStatus: this.formInline.marryStatus
          ? this.formInline.marryStatus
          : -1,
        queryValue: this.formInline.inputValue ? this.formInline.inputValue : ''
      };
      console.log('data: ', data);
      this.$ajax.post(this.$apiUrls.GetCompanyRegList, data).then((r) => {
        console.log('GetCompanyRegList : ', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.tableLoading = false;
        returnData.map((item) => {
          item.cardType = this.G_EnumList['CardType'][item.cardType];
          item.sex = this.G_EnumList['Sex'][item.sex];
          item.marryStatus = this.G_EnumList['MarryStatus'][item.marryStatus];
        });
        this.tableData = returnData || [];
      });
    },
    // 套餐搜索
    searchClusterClick() {
      this.tableLoading = true;
      if (!this.searchCluster) {
        this.$nextTick(() => {
          this.tableLoading = false;
        });
        return (this.searchClusterTable = this.clusterTable);
      }
      let newTableData = [];
      this.clusterTable.map((item) => {
        if (
          item.clusterCode.includes(this.searchCluster) ||
          item.clusterName.includes(this.searchCluster)
        ) {
          newTableData.push(item);
        }
      });
      this.searchClusterTable = newTableData;
      this.$nextTick(() => {
        this.tableLoading = false;
      });
    },
    // 人员表格勾选
    selectionChange(row) {
      console.log('row: ', row);
      this.selectionRow = [];
      row.map((item) => {
        if (item.regNo != '') {
          this.selectionRow.push(item.regNo);
        }
      });
      console.log('this.selectionRow: ', this.selectionRow);
    },
    // 套餐表格单选
    handleCurrentChange(row) {
      if (row === null) return;
      this.currentRow = row.clusterCode;
      console.log('this.currentRow: ', this.currentRow);
    },
    // 保存
    saveClick() {
      if (this.selectionRow.length === 0) {
        this.$message({
          message: '请选择分配人员！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.currentRow === '') {
        this.$message({
          message: '请选择需要分配的套餐！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      if (this.currentRow === null) {
        return;
      }
      let data = {
        regNos: this.selectionRow,
        clusterCode: this.currentRow,
        companyCode: this.$parent.$parent._props.companyInfo.companyCode,
        companyTimes: this.$parent.$parent._props.companyInfo.companyTimes
      };
      console.log('data: ', data);
      this.$ajax.post(this.$apiUrls.SetCompanyCluster, data).then((r) => {
        console.log('SetCompanyCluster : ', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.$message({
          message: '分配套餐成功！',
          type: 'success',
          showClose: true
        });
        this.getOrSearchClick();
        this.$nextTick(() => {
          this.$parent.$parent.searchClick();
          this.$refs.singleTable.$refs.tableCom_Ref.setCurrentRow();
        });
      });
    },
    // 重置
    reset() {
      this.formInline = {};
      this.searchCluster = '';
    }
  }
};
</script>

<style lang="less" scoped>
.allocationCluster {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 18px;
  color: #2d3436;
  font-weight: 600;
  display: flex;
  flex-direction: column;
  .line {
    text-align: center;
  }
  .select {
    width: 100px;
  }
  .input {
    width: 150px;
    margin-right: 8px;
  }
  .allocation-table {
    flex: 1;
    overflow: auto;
    display: flex;
  }
  .table {
    border: 1px solid #d8dee1;
    border-radius: 4px;
    flex: 2;
    overflow: auto;
    margin-right: 10px;
  }
  .cluster-title {
    font-size: 18px;
    padding: 10px 18px;
    background: rgba(23, 112, 223, 0.2);
  }
  .cluster-btn {
    display: flex;
    align-items: center;
    padding: 14px 18px;
    border-bottom: 1px solid #d8dee1;
    .blue_btn {
      margin-left: 8px;
    }
  }
  .cluster-table {
    border: 1px solid #d8dee1;
    border-radius: 4px;
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .btn {
    padding: 6.5px 10px;
  }
}
</style>
