<!--
 * @FilePath: \KrPeis\src\pages\serviceData\company\companySet.vue
 * @Description: 公司基本信息
 * @Author: justin
 * @Date: 2024-03-19 17:23:58
 * @Version: 0.0.1
 * @LastEditors: justin
 * @LastEditTime: 2024-05-23 10:33:55
*
-->
<template>
  <div class="companyType_page" ref="companySet">
    <div class="headerTitle">
      <div class="header" ref="headerTitle">
        <el-descriptions class="margin-top" title="基本信息" :column="3">
          <template slot="extra">
            <el-button
              size="small"
              @click="infoClick"
              class="blue_btn"
              icon="iconfont icon-bianji"
              >编辑</el-button
            >
          </template>
          <el-descriptions-item label="单位编号">{{
            popupForm.companyCode
          }}</el-descriptions-item>
          <el-descriptions-item label="单位名称">{{
            popupForm.companyName
          }}</el-descriptions-item>
          <el-descriptions-item label="单位别名">{{
            popupForm.companyAlias
          }}</el-descriptions-item>
          <el-descriptions-item label="父单位">{{
            popupForm.parentCompanyName
          }}</el-descriptions-item>
          <el-descriptions-item label="单位分类">{{
            popupForm.companyClsName
          }}</el-descriptions-item>
          <el-descriptions-item label="单位负责人">{{
            popupForm.companyContact
          }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions :column="3" v-show="isShows">
          <el-descriptions-item label="联系电话">{{
            popupForm.tel
          }}</el-descriptions-item>
          <el-descriptions-item label="传真">{{
            popupForm.fax
          }}</el-descriptions-item>
          <el-descriptions-item label="邮编">{{
            popupForm.postCode
          }}</el-descriptions-item>
          <el-descriptions-item label="联系地址">{{
            popupForm.address
          }}</el-descriptions-item>
          <el-descriptions-item label="开户银行">{{
            popupForm.openBank
          }}</el-descriptions-item>
          <el-descriptions-item label="银行账号">{{
            popupForm.bankAccount
          }}</el-descriptions-item>
          <el-descriptions-item label="院内联系人">{{
            popupForm.hospContact
          }}</el-descriptions-item>
          <el-descriptions-item label="拼音码">{{
            popupForm.pinYinCode
          }}</el-descriptions-item>
          <el-descriptions-item label="五笔码">{{
            popupForm.wuBiCode
          }}</el-descriptions-item>
          <el-descriptions-item label="备注">{{
            popupForm.note
          }}</el-descriptions-item>

          <template v-if="G_config.physicalMode.includes('职检')">
            <!-- 职业病相关 -->
            <el-descriptions-item label="所属地区">{{
              Object.values(popupForm.addreassRelation || {}).join('/')
            }}</el-descriptions-item>
            <el-descriptions-item label="行业分类">{{
              (popupForm.codeOccupationalIndustry || {}).industryName
            }}</el-descriptions-item>
            <el-descriptions-item label="经济类型">{{
              (popupForm.codeOccupationalEconomicType || {}).economicName
            }}</el-descriptions-item>
            <el-descriptions-item label="企业规模">{{
              G_EnumList.OccupationalEnterpriseSize &&
              G_EnumList.OccupationalEnterpriseSize[
                popupForm.enterpriseSizeCode
              ]
            }}</el-descriptions-item>
            <el-descriptions-item label="社会信用代码">{{
              popupForm.creditCode
            }}</el-descriptions-item>
            <el-descriptions-item label="职工人数">{{
              popupForm.employeesNumber
            }}</el-descriptions-item>
            <el-descriptions-item label="接触职业病危害因素人数">{{
              popupForm.contactHazardEmployeesNumber
            }}</el-descriptions-item>
          </template>
        </el-descriptions>
      </div>
      <span @click="showMore" class="isShow"
        ><i :class="isShow ? 'el-icon-caret-bottom' : 'el-icon-caret-top'"></i
        >{{ txt }}</span
      >
    </div>

    <div class="contInfo">
      <el-tabs v-model.trim="activeName" @tab-click="handleClick">
        <el-tab-pane
          v-for="(item, index) in menuList"
          :key="index"
          :label="item.title"
          :name="item.title"
        >
          <el-header>
            <div class="searchWrap">
              <span class="title">{{ item.title }}</span>
              <AllBtn
                :btnList="item.viewConfig.btn"
                :methodCreat="add"
                :methodDelete="deletes"
                :methodSearch="search"
                :methodJump="jumpClick"
                ref="allBtn_Ref"
              />
            </div>
          </el-header>
          <div class="tableCont">
            <PublicTable
              :viewTableList.sync="viewTableList"
              :theads.sync="item.viewConfig.theads"
              @rowDblclick="rowDblclicks"
              :cell_blue="item.viewConfig.cell_blue"
              @currentChange="handleCurrentChange"
              :columnWidth="columnWidth"
            >
              <template #beginDate="{ scope }" v-if="item.id === '1-1'">
                {{ dateUtils(scope.row.beginDate) }}
              </template>
              <template #endDate="{ scope }" v-if="item.id === '1-1'">
                {{ dateUtils(scope.row.endDate) }}
              </template>
              <template #onLineEndDate="{ scope }" v-if="item.id === '1-1'">
                {{ dateUtils(scope.row.endDate) }}
              </template>
              <template #columnRight v-if="item.id === '1-1'">
                <el-table-column
                  prop="operation"
                  label="操作"
                  width="78"
                  fixed="right"
                >
                  <template slot-scope="scope">
                    <el-button
                      type="primary"
                      plain
                      size="mini"
                      class="view"
                      @click="viewPhysicalTimes(scope.row)"
                      >查看</el-button
                    >
                  </template>
                </el-table-column>
              </template>
            </PublicTable>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <el-drawer
      title="单位部门信息属性"
      :visible.sync="drawer1"
      :before-close="handleClose"
      :wrapperClosable="false"
      v-if="activeName == '公司部门'"
    >
      <el-form :model="parameter" ref="ruleForm1" :rules="rules">
        <el-form-item
          label="单位部门代码"
          :label-width="formLabelWidth"
          prop="deptCode"
          v-if="funTpye == '/Create' ? false : true"
        >
          <el-input
            v-model.trim="parameter.deptCode"
            autocomplete="off"
            size="small"
            placeholder="请输入单位部门代码"
            :disabled="funTpye == '/Create' ? false : true"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="单位部门名称"
          :label-width="formLabelWidth"
          prop="deptName"
        >
          <el-input
            v-model.trim="parameter.deptName"
            autocomplete="off"
            size="small"
            placeholder="请输入单位部门名称"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button size="small" @click="drawer1 = false">取消</el-button>
        <el-button size="small" @click="submit" class="blue_btn"
          >保存</el-button
        >
      </div>
    </el-drawer>
    <el-drawer
      title="体检次数属性"
      :visible.sync="drawer1"
      :before-close="handleClose"
      :wrapperClosable="false"
      v-else-if="activeName == '体检次数'"
      size="80%"
    >
      <PhysicalTimesForm
        :parameters="parameters"
        :rules="rules2"
        @cancel="drawer1 = false"
        @save="submit2"
        :disabled="!isAdd"
        ref="ruleForm_ref"
      />
    </el-drawer>
    <el-drawer
      title="子公司属性"
      :visible.sync="drawer1"
      :before-close="handleClose"
      :wrapperClosable="false"
      size="80%"
      v-else
    >
      <el-form :model="comParameter" ref="ruleForm3" :rules="rules3">
        <el-row>
          <el-col :span="12">
            <el-form-item
              label="单位编号"
              :label-width="formLabelWidth"
              prop="companyCode"
            >
              <el-input
                v-model.trim="comParameter.companyCode"
                autocomplete="off"
                size="small"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="单位名称"
              :label-width="formLabelWidth"
              prop="companyName"
            >
              <el-input
                v-model.trim="comParameter.companyName"
                autocomplete="off"
                size="small"
                placeholder="请输入单位名称"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item
              label="单位分类"
              :label-width="formLabelWidth"
              prop="companyClsCode"
            >
              <el-select
                class="select"
                v-model.trim="comParameter.companyClsCode"
                placeholder="请选择"
                size="small"
                style="width: 100%"
              >
                <el-option
                  v-for="items in codeCompanyCls"
                  :key="items.companyClsCode"
                  :label="items.companyClsName"
                  :value="items.companyClsCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="单位别名"
              :label-width="formLabelWidth"
              prop="companyAlias"
            >
              <el-input
                class="select"
                v-model.trim="comParameter.companyAlias"
                placeholder="请输入"
                size="small"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="传真" :label-width="formLabelWidth" prop="fax">
              <el-input
                v-model.trim="comParameter.fax"
                autocomplete="off"
                size="small"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="邮编"
              :label-width="formLabelWidth"
              prop="postCode"
            >
              <el-input
                class="select"
                v-model.trim="comParameter.postCode"
                placeholder="请输入"
                size="small"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item
              label="父单位"
              :label-width="formLabelWidth"
              prop="parent"
            >
              <el-select
                class="select"
                v-model.trim="comParameter.parent"
                autocomplete="off"
                size="small"
                placeholder="请选择"
                style="width: 100%"
                filterable
                disabled
              >
                <el-option
                  v-for="(item, index) in parentOption"
                  :key="index"
                  :label="item.companyName"
                  :value="item.companyCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="开户银行"
              :label-width="formLabelWidth"
              prop="openBank"
            >
              <el-input
                v-model.trim="comParameter.openBank"
                autocomplete="off"
                size="small"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item
              label="银行账号"
              :label-width="formLabelWidth"
              prop="bankAccount"
            >
              <el-input
                v-model.trim="comParameter.bankAccount"
                autocomplete="off"
                size="small"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="单位负责人"
              :label-width="formLabelWidth"
              prop="companyContact"
            >
              <el-input
                v-model.trim="comParameter.companyContact"
                placeholder="请选择"
                size="small"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item
              label="备注"
              :label-width="formLabelWidth"
              prop="note"
            >
              <el-input
                type="textarea"
                v-model.trim="comParameter.note"
                placeholder="请输入"
                size="small"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="联系电话"
              :label-width="formLabelWidth"
              prop="tel"
            >
              <el-input
                v-model.trim="comParameter.tel"
                placeholder="请输入"
                size="small"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item
              label="拼音码"
              :label-width="formLabelWidth"
              prop="pinYinCode"
            >
              <el-input
                v-model.trim="comParameter.pinYinCode"
                autocomplete="off"
                size="small"
                placeholder="请输入拼音码"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="联系地址"
              :label-width="formLabelWidth"
              prop="address"
            >
              <el-input
                type="textarea"
                v-model.trim="comParameter.address"
                autocomplete="off"
                size="small"
                placeholder="省/市/县"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item
              label="五笔码"
              :label-width="formLabelWidth"
              prop="wuBiCode"
            >
              <el-input
                v-model.trim="comParameter.wuBiCode"
                autocomplete="off"
                size="small"
                placeholder="请输入五笔码"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="院内联系人"
              :label-width="formLabelWidth"
              prop="hospContact"
            >
              <el-select
                class="select"
                v-model.trim="comParameter.hospContact"
                placeholder="请选择"
                size="small"
                style="width: 100%"
              >
                <el-option
                  v-for="(item, index) in companyContactOption"
                  :key="index"
                  :label="item.name"
                  :value="item.operatorCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="dialog-footer">
        <el-button @click="drawer1 = false" size="small">取消</el-button>
        <el-button size="small" @click="submit3" class="blue_btn"
          >保存</el-button
        >
      </div>
    </el-drawer>
    <el-drawer
      title="单位属性"
      :visible.sync="drawer"
      :before-close="handleClose"
      :wrapperClosable="false"
      size="80%"
    >
      <DrawerForm
        :popupForm="popupForm"
        :parentOption="parentOption"
        :rules="rules4"
        :disabled="true"
        @cancel="handleClose"
        @save="editTitleInfo('ruleForm')"
        ref="ruleForm"
      ></DrawerForm>
    </el-drawer>
  </div>
</template>

<script>
import AllBtn from '../allBtn';
import PublicTable from '@/components/publicTable';
import DrawerForm from './drawer/drawerForm.vue';
import PhysicalTimesForm from './drawer/physicalTimesForm.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';

export default {
  name: 'companyType',
  components: {
    PublicTable,
    AllBtn,
    DrawerForm,
    PhysicalTimesForm
  },
  props: {
    companyCode: {
      type: String
    },
    muenId: {
      type: String
    },
    menuData: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      drawer: false,
      popupForm: {},
      parent: '',
      newArr: [],
      isShow: true,
      txt: '展开',
      num: 6,
      activeName: '',
      tableLoading: false,
      editTitleData: [],
      searchIpt: '',
      tableDataCopy: [],
      tableDataCopy2: [],
      tableDataCopy3: [],
      codeCompanyCls: [],
      parentOption: [], //父级
      companyContactOption: [],
      viewTableList: [],
      viewTableLists: [],
      isAdd: true,
      columnWidth: {
        onLineEndDate: 110,
        hospContact: 150,
        companyName: 180,
        companyAlias: 160,
        address: 200,
        beginDate: 110,
        endDate: 110
      },
      menuList: [
        {
          id: '1-1',
          title: '体检次数',
          viewConfig: {
            btn: ['查询', '新建', '删除'],
            cell_blue: [], // 蓝色字体的字段
            cell_red: [], // 红色字体的字段
            title: '单位体检次数属性',
            theads: {
              companyTimes: '体检次数',
              beginDate: '起始日期',
              endDate: '截止日期',
              onLineEndDate: '预约截止日期',
              hospContact: '院内联系人',
              tel: '电话',
              isOutside: '外检标识',
              taxID: '税号',
              invoiceHeader: '发票抬头'
            }
          }
        },
        {
          id: '1-2',
          title: '公司部门',
          viewConfig: {
            btn: ['查询', '新建', '删除'],
            cell_blue: ['deptName'], // 蓝色字体的字段
            cell_red: [], // 红色字体的字段
            title: '单位部门信息属性',
            theads: {
              deptCode: '部门代码',
              deptName: '部门名称'
            },
            viewTableList: []
          }
        }
      ],
      formLabelWidth: '120px',
      funTpye: '/Read',
      parameter: {
        companyCode: '',
        deptCode: '',
        deptName: ''
      },

      parameters: {
        companyCode: '',
        companyTimes: '',
        beginDate: '',
        endDate: '',
        onLineEndDate: '',
        invoiceHeader: '',
        taxID: '',
        hospContact: '',
        companyContact: '',
        openBank: '',
        bankAccount: '',
        tel: '',
        isOutside: false,
        note: ''
      },
      comParameter: {
        companyCode: '',
        companyName: '',
        companyAlias: '',
        companyClsCode: '',
        parent: '',
        hospContact: '',
        companyContact: '',
        tel: '',
        fax: '',
        address: '',
        postCode: '',
        openBank: '',
        bankAccount: '',
        pinYinCode: '',
        wuBiCode: '',
        note: ''
      },
      delParam: '',
      rowCont: [],
      drawer1: false,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
        }
      },
      rules: {
        deptCode: [
          { required: true, message: '请输入单位代码', trigger: 'blur' }
        ],
        deptName: [
          { required: true, message: '请输入单位名称', trigger: 'blur' }
        ]
      },
      rules2: {
        companyTimes: [
          { required: true, message: '请输入体检次数', trigger: 'blur' }
        ],
        companyCode: [
          { required: true, message: '请输入单位编码', trigger: 'blur' }
        ],
        isOutside: [
          { required: true, message: '请选择外检标识', trigger: 'blur' }
        ],
        beginDate: [
          { required: true, message: '请输入开始日期', trigger: 'change' }
        ]
      },
      rules3: {
        companyClsCode: [
          { required: true, message: '请输入单位分类', trigger: 'blur' }
        ],
        companyCode: [
          { required: true, message: '请输入单位编码', trigger: 'blur' }
        ],
        companyName: [
          { required: true, message: '请输入单位名称', trigger: 'blur' }
        ]
      },
      rules4: {
        // companyClsCode: [
        //   { required: true, message: "请输入单位分类代码", trigger: "blur" }
        // ],
        companyClsName: [
          { required: true, message: '请输入单位分类名称', trigger: 'blur' }
        ],
        companyCode: [
          { required: true, message: '请输入单位编号', trigger: 'blur' }
        ],
        companyName: [
          { required: true, message: '请输入单位名称', trigger: 'blur' }
        ]
      },
      isShows: false
    };
  },
  created() {
    this.getTypeList2();
    this.activeName = '体检次数';
    this.getCodeCompanyCls();
    this.getCompanyContact();
  },
  watch: {
    companyCode() {
      //console.log(this.companyCode);
      this.viewTableList = [];
      this.$nextTick(() => {
        this.search();
      });
      //let height= this.$refs.element.offsetHeight;  //100
    },
    activeName() {
      // console.log(this.activeName);
      this.viewTableList = [];
      this.$nextTick(() => {
        this.search();
        this.parameter.companyCode = this.companyCode;
      });
    }
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_config'])
  },
  methods: {
    //头部数据显示
    getTitleInfo() {
      //接收的数据
      this.newArr = [];
      let data = {
        companyCode: this.companyCode
      };
      this.$ajax.post(this.$apiUrls.R_CodeCompany, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.editTitleData = returnData || [];
        this.popupForm = this.editTitleData[0];
      });
    },
    // 基本信息编辑按钮
    infoClick() {
      this.drawer = true;
      this.getParentOption();
    },
    //提交编辑头部信息
    editTitleInfo() {
      console.log('this.popupForm', this.popupForm);
      this.$refs.ruleForm.$refs.form_ref.validate((valid) => {
        if (valid) {
          this.$ajax
            .post(`${this.$apiUrls.CUD_CodeCompany}/Update`, this.popupForm)
            .then((r) => {
              // console.log("r : ", r);
              let { success } = r.data;
              if (!success) return;
              this.$message({
                message: '修改成功!',
                type: 'success',
                showClose: true
              });
              this.getTitleInfo();
              this.handleClose();
              this.$parent.getCompany();
            });
        } else {
          return false;
        }
      });
    },
    //展开与收起
    showMore() {
      this.isShow = !this.isShow;
      this.isShows = !this.isShows;
      this.txt = this.isShow ? '展开' : '收起';
      //console.log(this.isShows);
    },
    //查询过滤后数据
    search() {
      if (this.activeName == '公司部门') {
        this.search1();
      } else if (this.activeName == '体检次数') {
        this.search2();
      } else {
        this.search3();
      }
    },
    // 时间格式
    dateUtils(date) {
      if (date) {
        return dataUtils.subBlankDate(date);
      }
    },
    //过滤公司部门信息
    search1() {
      // console.log(this);
      this.searchIpt = this.$refs.allBtn_Ref[0].searchInfo.trim(); //获取组件文本框的值
      if (this.searchIpt) {
        this.viewTableList = this.tableDataCopy.filter((item) => {
          return (
            item.deptCode.indexOf(this.searchIpt) !== -1 ||
            item.deptName.indexOf(this.searchIpt) !== -1
          );
        });
      } else {
        this.getTypeList1();
      }
      this.loading = false;
    },
    //过滤体检次数信息
    search2() {
      this.searchIpt = this.$refs.allBtn_Ref[0].searchInfo.trim(); //获取组件文本框的值
      if (this.searchIpt) {
        this.viewTableList = this.tableDataCopy2.filter((item) => {
          return (
            item.companyTimes.indexOf(this.searchIpt) !== -1 ||
            item.companyCode.indexOf(this.searchIpt) !== -1
          );
        });
      } else {
        this.getTypeList2();
      }
      this.loading = false;
    },
    //过滤子公司信息
    search3() {
      this.searchIpt = this.$refs.allBtn_Ref[0].searchInfo.trim(); //获取组件文本框的值
      if (this.searchIpt) {
        this.viewTableList = this.tableDataCopy3.filter((item) => {
          return (
            item.companyCode.indexOf(this.searchIpt) !== -1 ||
            item.companyName.indexOf(this.searchIpt) !== -1
          );
        });
      } else {
        this.getTypeList3();
      }
      this.loading = false;
    },
    //查询公司部门信息
    getTypeList1() {
      this.viewTableList = [];
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanyDepartment, {
          companyCode: this.companyCode,
          deptCode: '',
          deptName: ''
        })
        .then((r) => {
          //console.log("r", r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.viewTableList = returnData || [];
          this.tableDataCopy = returnData || [];
          this.menuList[0].viewConfig.viewTableList = returnData;
        });
    },
    //查询体检次数信息
    getTypeList2() {
      this.viewTableList = [];
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanyTimes, '', {
          query: { companyCode: this.companyCode }
        })
        .then((r) => {
          //
          let { success, returnData } = r.data;
          if (!success) return;
          returnData.map((item) => {
            item.isOutside = item.isOutside ? '是' : '否';
          });
          this.viewTableList = returnData || [];
          this.editTitleData = returnData || [];
          this.menuList[1].viewConfig.viewTableList = returnData;
          this.tableDataCopy2 = returnData || [];
        });
    },
    //查询子公司信息
    getTypeList3() {
      this.viewTableList = [];
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanySimple, {
          parent: this.companyCode
        })
        .then((r) => {
          //
          let { success, returnData } = r.data;
          if (!success) return;
          this.viewTableList = returnData || [];
          this.tableDataCopy3 = returnData || [];
        });
    },
    //获取单位分类
    getCodeCompanyCls() {
      this.$ajax.post(this.$apiUrls.R_CodeCompanyCls).then((r) => {
        //
        let { success, returnData } = r.data;
        if (!success) return;
        this.codeCompanyCls = returnData || [];
      });
    },
    //新增
    add() {
      this.drawer1 = true;
      this.isAdd = true;
      this.$nextTick(function () {
        if (this.activeName == '公司部门') {
          this.$refs.ruleForm1.resetFields();
        } else if (this.activeName == '体检次数') {
          this.$refs.ruleForm_ref.$refs.ruleForm2.resetFields();
          this.$refs.ruleForm_ref.parameters.endDate = '';
          this.parameters.companyCode = this.companyCode;
          if (this.viewTableList.length != 0) {
            this.parameters.companyTimes =
              this.viewTableList[this.viewTableList.length - 1].companyTimes +
              1;
          } else {
            this.parameters.companyTimes = 1;
          }
          console.log(this.parameters, this.viewTableList);
        } else {
          this.$refs.ruleForm3.resetFields();
          //console.log(this.companyCode);
          this.comParameter.parent = this.companyCode;
        }
      });
      this.funTpye = '/Create';
    },
    //编辑
    rowDblclicks(row) {
      //console.log("row", row);
      this.rowCont = row;
      this.drawer1 = true;
      this.isAdd = false;
      this.funTpye = '/Update';
      this.$nextTick(() => {
        if (this.activeName == '公司部门') {
          this.parameter = {
            companyCode: row.companyCode,
            deptCode: row.deptCode,
            deptName: row.deptName
          };
        } else if (this.activeName == '体检次数') {
          this.parameters = {
            companyCode: row.companyCode,
            companyTimes: row.companyTimes + '',
            beginDate: row.beginDate,
            endDate: row.endDate,
            onLineEndDate: row.onLineEndDate,
            invoiceHeader: row.invoiceHeader,
            taxID: row.taxID,
            hospContact: row.hospContact,
            companyContact: row.companyContact,
            openBank: row.openBank,
            bankAccount: row.bankAccount,
            tel: row.tel,
            isOutside: row.isOutside == '是' ? true : false,
            note: row.note
          };
          console.log('this.parameters', this.parameters);
        } else {
          this.comParameter = {
            companyCode: row.companyCode,
            companyName: row.companyName,
            companyAlias: row.companyAlias,
            companyClsCode: row.companyClsCode,
            parent: row.parent,
            hospContact: row.hospContact,
            companyContact: row.companyContact,
            tel: row.tel,
            fax: row.fax,
            address: row.address,
            postCode: row.postCode,
            openBank: row.openBank,
            bankAccount: row.bankAccount,
            pinYinCode: row.pinYinCode,
            wuBiCode: row.wuBiCode,
            note: row.note
          };
        }
      });
    },
    // //获取父级
    getParentOption() {
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanySimple, {
          companyClsCode: this.$parent.menuData.companyClsCode
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.parentOption = returnData || [];
        });
    },
    //获取单位负责人
    getCompanyContact() {
      this.$ajax.post(this.$apiUrls.GetAllOperator).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyContactOption = returnData || [];
      });
    },
    //关闭抽屉
    handleClose() {
      this.drawer = false;
      this.drawer1 = false;
    },
    //提交
    // 新增
    createInfo() {
      this.$ajax
        .post(
          `${this.$apiUrls.CUD_CodeCompanyDepartment}/Create`,
          this.parameter
        )
        .then((r) => {
          // console.log("r : ", r);
          let { success } = r.data;
          if (!success) return;
          this.drawer = false;
          this.$message({
            message: '新建成功!',
            type: 'success',
            showClose: true
          });
          this.search();
          this.handleClose();
        });
    },
    // 修改
    editInfo() {
      this.$ajax
        .post(
          `${this.$apiUrls.CUD_CodeCompanyDepartment}/Update`,
          this.parameter
        )
        .then((r) => {
          // console.log("r : ", r);
          let { success } = r.data;
          if (!success) return;
          this.drawer = false;
          this.$message({
            message: '修改成功!',
            type: 'success',
            showClose: true
          });
          this.search();
          this.handleClose();
        });
    },
    // 保存
    submit() {
      //console.log("this.parameter", this.parameter);
      this.$refs.ruleForm1.validate((valid) => {
        if (valid) {
          if (this.isAdd) {
            // 新建
            this.createInfo();
          } else {
            // 修改
            this.editInfo();
          }
        } else {
          return false;
        }
      });
    },
    // 新增
    createInfo2() {
      // if (this.parameters.beginDate > this.parameters.endDate) {
      //   return this.$message({
      //     message: "开始时间不能大于结束时间!",
      //     type: "warning",
      //     showClose: true,
      //   });
      // }
      this.$ajax
        .post(`${this.$apiUrls.CUD_CodeCompanyTimes}/Create`, this.parameters)
        .then((r) => {
          // console.log("r : ", r);
          let { success } = r.data;
          if (!success) return;
          this.drawer = false;
          this.$message({
            message: '新建成功!',
            type: 'success',
            showClose: true
          });
          this.search();
          this.handleClose();
        });
    },
    // 修改
    editInfo2() {
      // if (this.parameters.beginDate > this.parameters.endDate) {
      //   return this.$message({
      //     message: "开始时间不能大于结束时间!",
      //     type: "warning",
      //     showClose: true,
      //   });
      // }
      this.$ajax
        .post(`${this.$apiUrls.CUD_CodeCompanyTimes}/Update`, this.parameters)
        .then((r) => {
          //// console.log("r : ", r);
          let { success } = r.data;
          if (!success) return;
          this.drawer = false;
          this.$message({
            message: '修改成功!',
            type: 'success',
            showClose: true
          });
          this.search();
          this.handleClose();
        });
    },
    // 保存
    submit2() {
      //console.log("this.parameters", this.parameters);
      //console.log("this.parameters.beginDate", this.parameters.beginDate);
      this.$refs.ruleForm_ref.$refs.ruleForm2.validate((valid) => {
        if (valid) {
          if (this.isAdd) {
            // 新建
            this.createInfo2();
          } else {
            // 修改
            this.editInfo2();
          }
        } else {
          return false;
        }
      });
    },
    // 新增
    createInfo3() {
      this.$ajax
        .post(`${this.$apiUrls.CUD_CodeCompany}/Create`, this.comParameter)
        .then((r) => {
          // console.log("r : ", r);
          let { success } = r.data;
          if (!success) return;
          this.drawer = false;
          this.$message({
            message: '新建成功!',
            type: 'success',
            showClose: true
          });
          this.search();
          this.handleClose();
          this.$parent.getCompany();
        });
    },
    // 修改
    editInfo3() {
      this.$ajax
        .post(`${this.$apiUrls.CUD_CodeCompany}/Update`, this.comParameter)
        .then((r) => {
          // console.log("r : ", r);
          let { success } = r.data;
          if (!success) return;
          this.drawer = false;
          this.$message({
            message: '修改成功!',
            type: 'success',
            showClose: true
          });
          this.search();
          this.handleClose();
          this.$parent.getCompany();
        });
    },
    // 保存
    submit3() {
      //console.log("this.comParameter", this.comParameter);
      this.$refs.ruleForm3.validate((valid) => {
        if (valid) {
          if (this.isAdd) {
            // 新建
            this.createInfo3();
          } else {
            // 修改
            this.editInfo3();
          }
        } else {
          return false;
        }
      });
    },

    //删除
    handleCurrentChange(rows) {
      console.log(rows);
      this.delParam = rows;
      //console.log("this.delParam", this.delParam);
    },
    //删除
    deletes() {
      console.log('this.delParam', this.delParam);
      if (!this.delParam) {
        this.$message({
          message: '请选择需要删除的数据',
          type: 'warning',
          duration: this.$config.messageTime,
          showClose: true
        });
        return;
      }
      var url = ''; //删除路径
      var delInfo = ''; //删除提示信息
      if (this.activeName == '公司部门') {
        url = this.$apiUrls.CUD_CodeCompanyDepartment;
        delInfo = `是否确定删除部门名称为${this.delParam.deptName}?`;
      } else if (this.activeName == '体检次数') {
        url = this.$apiUrls.CUD_CodeCompanyTimes;
        delInfo = `是否确定删除体检次数为${this.delParam.companyTimes}?`;
        this.delParam.isOutside =
          this.delParam.isOutside == '是' ? true : false;
      } else {
        url = this.$apiUrls.CUD_CodeCompany;
        delInfo = `是否确定删除单位名称为${this.delParam.companyName}?`;
      }
      this.$confirm(delInfo, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax.post(url + '/Delete', this.delParam).then((r) => {
            //
            let { success } = r.data;
            if (!success) return;
            this.$message({
              showClose: true,
              message: '删除成功',
              type: 'success'
            });
            this.search();
            this.$parent.getCompany();
          });
        })
        .catch(() => {});
    },
    handleClick(tab, event) {
      console.log(tab, event);
    },
    // 查看体检次数详情
    viewPhysicalTimes(row) {
      row.isOutside = row.isOutside === '是' ? true : false;
      this.$emit('jumpDetail', row);
    },
    // 跳转
    jumpClick() {
      if (!this.delParam) {
        this.$message({
          message: '请选择一个需要跳转的子公司!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      let newData = this.menuData.company.filter((item) => {
        return (
          item.companyCode === this.delParam.companyCode &&
          item.companyName === this.delParam.companyName
        );
      });
      // console.log(newData);
      this.$parent.companySetClick('CompanySet', newData[0]);
    },
    infoSave() {
      this.$ajax
        .post(`${this.$apiUrls.CUD_CodeCompanyCls}/Update`, this.infoForm)
        .then((r) => {
          let { success } = r.data;
          if (!success) return;
          this.drawerInfo = false;
          this.$message({
            message: '修改成功',
            type: 'success',
            showClose: true
          });
          //   this.companyClsCode = this.infoForm.companyClsCode;
          //   this.companyClsName = this.infoForm.companyClsName;
          // this.getTableData();
          this.$parent.getCompany();
        });
    }
  }
};
</script>
<style lang="less" scoped>
.companyType_page {
  display: flex;
  flex: 1;
  flex-shrink: 0;
  height: 100%;
  flex-direction: column;
  .header {
    padding: 16px;
    padding-bottom: 0;
  }
  .headerTitle {
    // padding: 15px;
    min-height: 158px;
    border-radius: 4px;
    background: #fff;
    padding-bottom: 0;

    /deep/.el-descriptions-item__container .el-descriptions-item__content {
      color: #000;
      font-weight: 600;
    }
    .titleInfo {
      display: flex;
      flex-direction: column;
      .el-form-item {
        width: 32%;
        margin-bottom: 0;
        /deep/.el-form-item__content {
          line-height: 26px;
        }
      }
    }
  }
  .isShow {
    display: block;
    cursor: pointer;
    color: #1770df;
    text-align: center;
    background: rgba(23, 112, 223, 0.1);
    border-radius: 0 0 4px 4px;
    font-size: 14px;
    padding: 5px 0;
    i {
      margin-right: 6px;
    }
  }

  .contInfo {
    margin-top: 20px;
    padding: 16px;
    padding-bottom: 4px;
    flex: 1;
    padding-top: 0;
    background: #fff;
    border-radius: 4px;
    overflow: auto;
    header {
      display: flex;
      justify-content: space-between;
      line-height: 60px;
      padding: 0;
      form {
        display: contents;
      }
    }
    .view {
      font-size: 14px;
      padding: 7px 13px;
    }
    .searchWrap {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      .title {
        width: 100px;
      }
      .el-input--small {
        width: 380px;
        margin-right: 10px;
      }
    }
    /deep/.tableCont {
      flex: 1;
      height: calc(100% - 60px);
    }
    /deep/.el-tabs {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    /deep/.el-tabs__header {
      width: 100%;
      margin: 0;
    }
  }
  /deep/.el-tabs__content {
    height: calc(100% - 50px);
    .el-tab-pane {
      height: 100%;
    }
  }
  /deep/.el-date-editor--daterange.el-input__inner {
    width: auto;
  }
  /deep/.el-form-item {
    margin-bottom: 18px;
  }
  /deep/.el-date-editor.el-input,
  /deep/.el-select {
    width: 100%;
  }
  .dialog-footer {
    text-align: end;
  }
  .transition-dom {
    transition: all 0.2s linear 0s;
  }
}
</style>
