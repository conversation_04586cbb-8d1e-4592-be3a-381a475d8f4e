import { dataUtils } from '@/common';
import moment from 'moment';
import { mapGetters } from 'vuex';
import Exceljs from '@/common/excel/groupDetailedExpenseReportExcel';
export default {
  mixins: [Exceljs],
  data() {
    return {
      inputRight: '',
      activeName: '',
      timesDrawer: false,
      popupForm: {},
      drawer: false,
      checkType: [],
      checkPackage: '',
      tabList: [],
      batchDiscount: 1,
      theads: {
        clusterCode: '套餐代码',
        combCode: '组合代码',
        combName: '组合名称',
        count: '数量',
        price: '单价',
        discount: '折扣',
        isPayBySelf: '自费'
      },
      leftThead: {
        combCode: '组合代码',
        combName: '组合名称',
        count: '数量',
        originalPrice: '原价',
        price: '单价',
        discount: '折扣',
        isPayBySelf: '自费'
      },
      rigthThead: {
        combCode: '组合代码',
        combName: '组合名称',
        price: '单价'
      },
      columnWidth: {
        combName: 200
      },
      searchTable: [],
      options: [
        {
          value: 'personal',
          label: '个人'
        },
        {
          value: 'group',
          label: '团体'
        }
      ],
      mealTypeVal: '',
      companyWithTimesList: [],
      checkCompanyVal: [],
      parameters: {},
      rules: {
        clusterCode: [
          { required: true, message: '请输入套餐代码', trigger: 'blur' }
        ],
        clusterName: [
          { required: true, message: '请输入套餐名称', trigger: 'blur' }
        ],
        guidanceType: [
          { required: true, message: '请选择指引单格式', trigger: 'change' }
        ],
        reportType: [
          { required: true, message: '请选择报告单格式', trigger: 'change' }
        ],
        sex: [{ required: true, message: '请选择性别', trigger: 'change' }],
        hazardFactors: [
          { required: true, message: '请选择危害因素', trigger: 'change' }
        ],
        jobStatus: [
          { required: true, message: '请选择在岗状态', trigger: 'change' }
        ]
        // marryStatus: [
        //   { required: true, message: "请选择婚姻状况", trigger: "change" }
        // ]
      },
      rules2: {
        companyTimes: [
          { required: true, message: '请输入体检次数', trigger: 'blur' }
        ],
        companyCode: [
          { required: true, message: '请输入单位编码', trigger: 'blur' }
        ],
        isOutside: [
          { required: true, message: '请选择外检标识', trigger: 'blur' }
        ],
        beginDate: [
          { required: true, message: '请输入开始日期', trigger: 'blur' }
        ]
      },
      packageInfo: {
        companyCluster: {
          clusterCode: '',
          clusterName: '',
          companyCode: '',
          companyTimes: 0,
          sex: '',
          marryStatus: '',
          upperAgeLimit: 0,
          lowerAgeLimit: 0,
          price: 0,
          guidanceType: '',
          reportType: '',
          addItemDiscount: 0,
          allowChangeItem: true,
          note: '',
          isOccupation: false,
          hazardFactors: null,
          jobStatus: null
        },
        companyComb: []
      },
      harmList: [], //危害因素
      companyOption: [],
      clusterOption: [],
      packageOption: [],
      groupOption: [],
      rightTable: [],
      leftTable: [],
      leftTableCopy: [],
      tableLoading: false,
      isAddOrEdit: false,
      active: '1',
      isCompany: false,
      props: {
        lazy: true,
        lazyLoad: this.lazyLoad
      },
      leftTableBodyWrapper: {},
      searchTableBodyWrapper: {},
      leftTableScroll: {},
      searchTableScroll: {},
      exportLoading: false
    };
  },
  computed: {
    ...mapGetters([
      'G_EnumList',
      'G_shareSexList',
      'G_marriageStatus',
      'G_userInfo',
      'G_codeReportType',
      'G_codeGuidanceType',
      'G_CodeOccupationalPositionStatus'
    ]),
    // 抽屉组合计算总价
    totalSumAll() {
      let totalSumAll = 0;
      this.leftTable.map((item) => {
        if (!isNaN(item.price)) totalSumAll += Number(item.price) * item.count;
      });
      if (isNaN(totalSumAll)) {
        return 0;
      }
      this.popupForm.price = totalSumAll.toFixed(2);
      return totalSumAll.toFixed(2);
    },
    // 组合计算总价
    totalSum() {
      let totalSumAll = 0;
      this.packageInfo.companyComb.map((item) => {
        if (!isNaN(item.price)) totalSumAll += item.price;
      });
      if (isNaN(totalSumAll)) {
        return 0;
      }
      return totalSumAll.toFixed(2);
    },
    isAllPayBySelf() {
      if (this.leftTable.length === 0) return true;
      return this.leftTable.some((item) => item.isPayBySelf === false);
    }
  },
  created() {
    this.getCluster();
    this.getItemComb();
    this.getInfoData(this.companyCode);
    this.GetCompaniesWithTimes();
    this.getHarmList();
  },
  methods: {
    //危害因素带出的组合数据
    autoAddCombination() {
      let { hazardFactors, jobStatus } = this.popupForm;
      if (!hazardFactors || hazardFactors.length === 0 || !jobStatus) return;
      let combinationCode = [];
      hazardFactors.forEach((item) => {
        let combsByStatus = this.harmList.find(
          (h) => h.hazardousCode === item
        ).combsByStatus;
        combinationCode.push(...combsByStatus[jobStatus]);
      });
      combinationCode = [...new Set(combinationCode)];
      let addData = this.searchTable.filter((item) =>
        combinationCode.includes(item.combCode)
      );
      this.batchIncreaseCombinations(addData);
    },
    //批量增加组合
    async batchIncreaseCombinations(data) {
      const combCodeSet = new Set(this.leftTableCopy.map((i) => i.combCode));
      data = data
        .filter((item) => !combCodeSet.has(item.combCode))
        .map((item) => {
          return {
            ...item,
            isPayBySelf: false,
            discount: 1,
            isCheckComb: true,
            count: 1,
            originalPrice: item.price
          };
        });
      this.recordTableScroll();
      this.leftTableCopy.unshift(...data);
      await this.sexChange();
      this.CalculateTustTubeCombs(this.leftTableCopy);
    },
    //批打折隐藏回调
    closePopover() {
      this.batchDiscount = 1;
    },
    //批打折显示回调
    showDiscount() {
      this.$nextTick(() => {
        this.$refs.ref_input.focus();
      });
    },
    //批打折
    discountConfirm() {
      if (!(this.batchDiscount >= 0)) {
        this.showDiscount();
        return this.$message.error('请输入正确的折扣！');
      }
      this.leftTable.forEach((item) => {
        let price = dataUtils.multiply(item.originalPrice, this.batchDiscount);
        item.discount = this.batchDiscount;
        item.price = price;
      });
    },
    // 自费全选/全不选
    selfFundedSwitching() {
      let tabData = this.leftTable.map((item) => {
        return {
          ...item,
          isPayBySelf: this.isAllPayBySelf
        };
      });
      this.leftTable = dataUtils.deepCopy(tabData);
      this.leftTableCopy = dataUtils.deepCopy(tabData);
      this.CalculateTustTubeCombs(this.leftTableCopy);
    },
    // 套餐下拉
    getCluster() {
      this.$ajax.post(this.$apiUrls.Cluster).then((r) => {
        // console.log("Cluster: ", r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.clusterOption = returnData || [];
      });
    },
    // 套餐信息列表
    getInfoData(value) {
      console.log('value: ', value);
      if (!this.parentValue.companyTimes) {
        return;
      }
      this.$ajax
        .post(this.$apiUrls.ReadCompanyClusSimpleInfos, '', {
          query: {
            companyCode: value,
            companyTimes: this.parentValue.companyTimes
          }
        })
        .then((r) => {
          console.log('ReadCompanyClusSimpleInfos: ', r);
          this.tableLoading = false;
          let { success, returnData } = r.data;
          if (!success) return;
          this.tabList = returnData || [];
          if (this.tabList.length === 0) {
            return;
          }
          this.activeName = this.tabList[0].clusterCode;
          this.getClusAndCombs();
        });
    },
    // 联级选择器获取套餐
    getInfoDatas(code, times) {
      if (!times) {
        this.packageOption = [];
        this.groupOption = [];
        return;
      }
      this.$ajax
        .post(this.$apiUrls.ReadCompanyClusSimpleInfos, '', {
          query: {
            companyCode: code,
            companyTimes: times
          }
        })
        .then((r) => {
          console.log('ReadCompanyClusSimpleInfos: ', r);
          this.tableLoading = false;
          let { success, returnData } = r.data;
          if (!success) return;
          this.groupOption = returnData || [];
          this.packageOption = this.groupOption;
        });
    },
    // 套餐信息和组合列表
    getClusAndCombs() {
      this.$ajax
        .post(this.$apiUrls.ReadCompanyClusAndCombs, '', {
          query: { clusterCode: this.activeName }
        })
        .then((r) => {
          console.log('ReadCompanyClusAndCombs: ', r);
          this.tableLoading = false;
          let { success, returnData } = r.data;
          if (!success) return;
          this.packageInfo = returnData || {};
        });
    },
    // 套餐组合列表
    getCopyCompanyClusterCombs() {
      this.$ajax
        .post(this.$apiUrls.CopyCompanyClusterCombs, '', {
          query: { isCompany: this.isCompany, clusterCode: this.checkPackage }
        })
        .then((r) => {
          console.log('CopyCompanyClusterCombs: ', r);
          this.tableLoading = false;
          let { success, returnData } = r.data;
          if (!success) return;
          this.CalculateTustTubeCombs(returnData);
          // this.leftTable = returnData || [];
          //console.log('[ this.leftTable ]-231', this.leftTable);
          // this.leftTableCopy = returnData || [];
          //console.log('[ this.leftTableCopy ]-233', this.leftTableCopy);
        });
    },
    // 获取右边表格数据
    getItemComb() {
      this.$ajax.post(this.$apiUrls.ItemComb).then((r) => {
        console.log('ItemComb: ', r);
        this.tableLoading = false;
        let { success, returnData } = r.data;
        if (!success) return;
        let returnDatas = returnData || [];
        returnDatas = returnDatas.map((item) => {
          item.detailList = [];
          return item;
        });
        let sex = this.popupForm.sex;
        if (sex) {
          this.searchTable = returnDatas.filter(
            (item) => item.sex == sex || item.sex == 0
          );
          this.rightTable = returnDatas;
        } else {
          this.rightTable = returnData || [];
          this.searchTable = returnData || [];
        }
      });
    },
    filterMethod(option, input) {
      // 自定义搜索逻辑
      // 动态加载数据
      console.log(option, input);
      return option.text.indexOf(input) > -1;
    },
    // 返回
    goBack() {
      let newData = this.menuData.company.filter((item) => {
        return item.companyCode === this.parentValue.companyCode;
      });
      this.$parent.companySetClick('CompanySet', newData[0]);
    },
    // 标签页点击
    handleClick(tab, event) {
      //console.log("this.activeName: ", this.activeName);
      this.activeName = tab._props.name;
      this.getClusAndCombs();
    },
    // 关闭
    cancel() {
      this.drawer = false;
      this.timesDrawer = false;
      this.checkType = [];
      this.checkPackage = '';
      this.leftTable = [];
      this.leftTableCopy = [];
      this.inputRight = '';
    },
    // 选择套餐
    changeSelect(value) {
      console.log('value: ', value);
      this.getCopyCompanyClusterCombs();
    },
    // 动态加载联级选择器 套餐模板
    lazyLoad(node, resolve) {
      if (node.level == 0) {
        this.getOption(node, resolve);
      } else if (node.level == 1) {
        this.getOptionChild(node, resolve);
      } else if (node.level == 2) {
        node.level = 2;
        this.getOptionChilds(node, resolve);
      }
    },
    // 三级联动 - 一级
    getOption(node, resolve) {
      this.options = [
        {
          value: 'personal',
          label: '个人',
          leaf: true
        },
        {
          value: 'group',
          label: '团体',
          children: []
        }
      ];
      resolve(this.options);
    },
    // 三级联动 - 二级
    getOptionChild(node, resolve) {
      this.$ajax.post(this.$apiUrls.Company).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        let optionChild = returnData || [];
        optionChild = optionChild.map((item) => {
          return {
            label: item.companyName,
            value: item.companyCode
          };
        });
        resolve(optionChild);
      });
    },
    // 三级联动 - 三级
    getOptionChilds(node, resolve) {
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanyTimes, '', {
          query: { companyCode: node.data.value }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          let optionChilds = returnData.map((item) => {
            item.leaf = !item.hasChildren;
            return {
              label: `体检次数：${item.companyTimes}`,
              value: item.companyTimes,
              leaf: item.leaf
            };
          });
          resolve(optionChilds);
        });
    },
    // 联级选择器
    handleChange(value) {
      this.checkPackage = '';
      if (value[0] === 'personal') {
        this.isCompany = false;
        this.packageOption = this.clusterOption.map((item) => {
          return {
            clusterCode: item.clusCode,
            clusterName: item.clusName,
            price: item.price
          };
        });
      } else if (value[0] === 'group') {
        this.isCompany = true;
        this.getInfoDatas(value[1], value[2]);
      }
    }, //性别切换事件
    sexChange() {
      this.tableLoading = true;
      let sex = this.popupForm.sex;
      console.log(sex, 8888888888);
      this.leftTable = [];
      this.searchTable = [];
      let newLeftTable = [];
      let newTableData = [];
      if (!sex && !this.inputRight) {
        this.searchTable = this.rightTable;
        this.leftTable = this.leftTableCopy;
      } else {
        if (sex === 0) {
          newLeftTable = this.leftTableCopy;
        } else {
          newLeftTable = this.leftTableCopy.filter(
            (item) => item.sex === sex || item.sex === 0
          );
        }
        this.leftTable = newLeftTable;
        newTableData = this.rightTable.filter(
          (item) =>
            (item.combCode.includes(this.inputRight) ||
              item.combName.includes(this.inputRight)) &&
            (item.sex === sex || item.sex === 0)
        );
        // this.rightTable.map(item => {
        //   if (
        //     (item.combCode.includes(this.inputRight) ||
        //       item.combName.includes(this.inputRight)) &&
        //     (item.sex === sex || item.sex === 0)
        //   ) {
        //     newTableData.push(item);
        //   }
        // });
        //console.log('[  ]-373', newTableData);
        this.searchTable = newTableData;
      }

      this.$nextTick(() => {
        this.tableLoading = false;
      });
    },
    // 查找
    drawerSearchClick() {
      this.tableLoading = true;
      let inputRight = this.inputRight.toLowerCase();
      if (!this.inputRight) {
        this.$nextTick(() => {
          this.tableLoading = false;
        });
        this.searchTable = this.rightTable;
        return;
      }
      let newTableData = [];
      if (!this.popupForm.sex) {
        newTableData = this.rightTable.filter((item) => {
          return (
            item.combCode.toLowerCase().includes(inputRight) ||
            item.combName.toLowerCase().includes(inputRight) ||
            item.pinYinCode?.toLowerCase().includes(inputRight)
          );
        });
      } else {
        newTableData = this.rightTable.filter((item) => {
          return (
            (item.combCode.toLowerCase().includes(inputRight) ||
              item.pinYinCode?.toLowerCase().includes(inputRight) ||
              item.combName.toLowerCase().includes(inputRight)) &&
            (item.sex === this.popupForm.sex || item.sex === 0)
          );
        });
      }
      this.searchTable = newTableData;
      this.$nextTick(() => {
        this.tableLoading = false;
      });
    },
    // 左边表格移动
    leftRemove(row) {
      this.recordTableScroll();
      this.leftTable = this.leftTable.filter(
        (item) => item.combCode !== row.combCode
      );
      this.leftTableCopy = this.leftTableCopy.filter(
        (item) => item.combCode !== row.combCode
      );
      this.CalculateTustTubeCombs(this.leftTableCopy);
    },
    // 右边表格移动
    async rightRemove(row) {
      let mutexCombsTips = '';
      this.leftTableCopy.forEach((item) => {
        if (row.mutexCombs.includes(item.combCode)) {
          mutexCombsTips += (mutexCombsTips ? '、' : '') + item.combName;
        }
      });
      if (mutexCombsTips !== '') {
        this.$message({
          message: `选择的组合和 ${mutexCombsTips} 存在互斥!`,
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.recordTableScroll();
      const flag =
        this.leftTableCopy.findIndex((i) => i.combCode === row.combCode) === -1;
      if (flag) {
        this.leftTableCopy.unshift({
          ...row,
          isPayBySelf: false,
          discount: 1,
          isCheckComb: true,
          count: 1,
          originalPrice: row.price
        });
        await this.sexChange();
        this.CalculateTustTubeCombs(this.leftTableCopy);
      } else {
        this.$message({
          message: '选择的组合已存在!',
          type: 'warning',
          showClose: true
        });
      }
    },
    //计算套餐试管、采血费用
    CalculateTustTubeCombs(leftTableCopy) {
      this.$ajax
        .post(this.$apiUrls.CalculateTustTubeCombs, leftTableCopy)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.leftTableCopy = returnData || this.leftTableCopy;
          this.leftTable = this.leftTableCopy;
        })
        .finally((_) => {
          this.recoverTableScroll();
        });
    },
    //自费标识改变的回调
    handleIsPayBySelfChange() {
      this.CalculateTustTubeCombs(this.leftTableCopy);
    },
    // 清除
    clearClick() {
      this.leftTable = [];
      this.leftTableCopy = [];
    },
    // 编辑套餐抽屉打开
    editClick() {
      //console.log('[ this.packageInfo.companyComb ]-475', this.packageInfo.companyComb);
      this.leftTable = [];
      this.leftTablecOPY = [];
      this.drawer = true;
      this.isAddOrEdit = true;
      this.$nextTick(() => {
        this.resetForm('ruleForm');
        // this.popupForm = this.packageInfo.companyCluster;
        this.leftTable = this.packageInfo.companyComb;
        this.leftTableCopy = this.packageInfo.companyComb;
        this.popupForm = dataUtils.deepCopy(this.packageInfo.companyCluster);
        this.popupForm.isOccupation =
          !this.G_config.physicalMode.includes('普检');
        this.getClusAndCombs();
        this.getItemComb();
      });
    },
    // 添加套餐抽屉打开
    addClick() {
      this.drawer = true;
      this.isAddOrEdit = false;
      this.$nextTick(() => {
        this.resetForm('ruleForm');
        this.popupForm.companyCode = this.parentValue.companyCode;
        this.popupForm.companyTimes = this.parentValue.companyTimes;
        this.popupForm = dataUtils.deepCopy(this.popupForm);
        this.popupForm.isOccupation =
          !this.G_config.physicalMode.includes('普检');
        this.getItemComb();
      });
    },
    // 新建套餐
    createInfo() {
      let data = {
        companyCluster: this.popupForm,
        companyComb: this.leftTable
      };
      console.log('data: ', data);
      this.$ajax
        .post(`${this.$apiUrls.CompanyClusterAndComb}/Create`, data)
        .then((r) => {
          console.log('r: ', r);
          this.tableLoading = false;
          let { success, returnData } = r.data;
          if (!success) return;
          this.cancel();
          this.$message({
            message: '新建成功!',
            type: 'success',
            showClose: true
          });
          this.getInfoData(this.companyCode);
        });
    },
    // 编辑套餐
    editInfo() {
      let data = {
        companyCluster: this.popupForm,
        companyComb: this.leftTable
      };
      this.$ajax
        .post(`${this.$apiUrls.CompanyClusterAndComb}/Update`, data)
        .then((r) => {
          console.log('r: ', r);
          this.tableLoading = false;
          let { success, returnData } = r.data;
          if (!success) return;
          this.cancel();
          this.$message({
            message: '修改成功!',
            type: 'success',
            showClose: true
          });
          this.getInfoData(this.companyCode);
        });
    },
    // 保存套餐
    save(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (!this.isAddOrEdit) {
            // 新建
            this.createInfo();
          } else {
            // 修改
            this.editInfo();
          }
        } else {
          return false;
        }
      });
    },
    // 删除套餐
    deletes() {
      let list = this.tabList.filter(
        (item) => item.clusterCode === this.activeName
      );
      this.$confirm(`是否确定删除${list[0].clusterName}吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$ajax
          .post(this.$apiUrls.DeleteCompanyCluster, '', {
            query: { clusterCode: this.activeName }
          })
          .then((r) => {
            console.log('r: ', r);
            this.tableLoading = false;
            let { success, returnData } = r.data;
            if (!success) return;
            this.drawer = false;
            this.$message({
              message: '删除成功!',
              type: 'success',
              showClose: true
            });
            this.getInfoData(this.companyCode);
          });
      });
    },
    // 单价复原
    priceReset() {
      let newArr = [];
      newArr = this.rightTable.filter((item) =>
        this.leftTable.some((i) => i.combName === item.combName)
      );
      this.leftTable = newArr.map((item) => {
        return {
          ...item,
          discount: 1
        };
      });
    },
    //打折
    discount() {
      this.leftTable.map((item) => {
        item.price = (item.price * (item.discount / 100)).toFixed(2);
      });
    },
    // 更新组合
    updateClick() {
      let combs = [];
      this.leftTable.map((item) => {
        combs.push(item.combCode);
      });
      let data = {
        companyCode: this.parentValue.companyCode,
        companyTimes: this.parentValue.companyTimes,
        clusterCode: this.popupForm.clusterCode,
        combs: combs,
        opratorCode: this.G_userInfo.codeOper.operatorCode,
        opratorName: this.G_userInfo.codeOper.name
      };
      console.log('data: ', data);
      this.$ajax.post(this.$apiUrls.UpdateCompanyCluster, data).then((r) => {
        console.log('r: ', r);
        this.tableLoading = false;
        let { success, returnData } = r.data;
        if (!success) return;
        this.$message({
          message: '更新组合成功!',
          type: 'success',
          showClose: true
        });
      });
    },
    //恢复原价
    restorePrice() {
      this.leftTable.forEach((element) => {
        element.discount = 1;
        element.price = element.originalPrice;
      });
    },
    // 体检次数抽屉打开
    editTimesDrawer(val) {
      this.timesDrawer = true;
      this.parameters = dataUtils.deepCopy(val);
    },
    // 编辑体检次数保存
    timesDrawerSave() {
      if (this.parameters.beginDate && this.parameters.endDate) {
        if (this.parameters.endDate < this.parameters.beginDate) {
          this.$message({
            message: '结束时间不能小于开始时间!',
            type: 'warning',
            showClose: true
          });
          return;
        }
      }
      this.$refs.ruleForm_ref.$refs.ruleForm2.validate((valid) => {
        if (valid) {
          this.$ajax
            .post(
              `${this.$apiUrls.CUD_CodeCompanyTimes}/Update`,
              this.parameters
            )
            .then((r) => {
              let { success, returnData } = r.data;
              if (!success) {
                return;
              }
              this.$message({
                message: '修改成功!',
                type: 'success',
                showClose: true
              });
              this.timesDrawer = false;
              this.parentValueLocal = this.parameters;
            });
        } else {
          return false;
        }
      });
    },
    // 时间格式化
    dateFormat(date) {
      if (!date) return '';
      return moment(date).format('YYYY-MM-DD') || '';
    },
    // 重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.popupForm.isOccupation =
        !this.G_config.physicalMode.includes('普检');
      this.popupForm.allowChangeItem = false;
    },
    // 折扣改变的回调
    discountChange(argument, row) {
      console.log(argument, row, this.parentValue.companyCode);
      let price = dataUtils.multiply(row.originalPrice, row.discount);
      row.price = price;
      // let datas={
      //   combCode: row.combCode,
      //   discount: row.discount,
      //   companyCode: this.parentValue.companyCode,
      //   companyTimes: this.parentValue.companyTimes
      // }
      // this.$ajax.post(this.$apiUrls.CalculateDiscountPrice,datas).then(r=>{
      //
      //   let {returnData} = r.data;
      //   row.price = returnData;
      // })
    },
    // 获取单位信息和单位次数
    GetCompaniesWithTimes() {
      let datas = {
        pageSize: 0,
        pageNumber: 1
        // companyClsCode,
        // companyCode,
        // parent,
        // keyword
      };
      this.$ajax.post(this.$apiUrls.GetCompaniesWithTimes, datas).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.companyWithTimesList = returnData?.map((item) => {
          let childArr = item.codeCompanyTimes.map((twoItem) => {
            return {
              value: `${twoItem.companyTimes}`,
              label: `体检次数：${twoItem.companyTimes}`
            };
          });
          return {
            value: `${item.companyCode}`,
            label: `${item.companyName}`,
            children: childArr
          };
        });
        console.log(this.companyWithTimesList);
      });
    },
    // 团体和个人选择的回调
    mealTypeChange() {
      console.log(this.mealTypeVal);
      this.checkPackage = '';
      if (this.mealTypeVal === 'personal') {
        this.isCompany = false;
        this.packageOption = this.clusterOption.map((item) => {
          return {
            clusterCode: item.clusCode,
            clusterName: item.clusName,
            price: item.price
          };
        });
      } else if (this.mealTypeVal === 'group') {
        this.checkCompanyVal = [];
        this.packageOption = [];
        this.isCompany = true;
      }
    },
    // 单位和次数改变的回调
    companyChange() {
      console.log(this.checkCompanyVal);
      this.checkPackage = '';
      this.getInfoDatas(this.checkCompanyVal[0], this.checkCompanyVal[1]);
    },

    /**
     * <AUTHOR> justin
     * @description  : 记录表格滚动条位置
     * @return        {*}
     **/
    recordTableScroll() {
      const that = this;
      that.leftTableBodyWrapper =
        that.$refs.leftTable_ref.$refs.tableCom_Ref.bodyWrapper;

      that.searchTableBodyWrapper =
        that.$refs.searchTable_ref.$refs.tableCom_Ref.bodyWrapper;
      that.leftTableScroll = {
        top: that.leftTableBodyWrapper.scrollTop,
        left: that.leftTableBodyWrapper.scrollLeft
      };

      that.searchTableScroll = {
        top: that.searchTableBodyWrapper.scrollTop,
        left: that.searchTableBodyWrapper.scrollLeft
      };
    },

    /**
     * <AUTHOR> justin
     * @description  : 恢复滚动条位置
     * @return        {*}
     **/
    recoverTableScroll() {
      const that = this;
      that.$nextTick(() => {
        that.leftTableBodyWrapper.scrollTop = that.leftTableScroll.top;
        that.leftTableBodyWrapper.scrollLeft = that.leftTableScroll.left;

        that.searchTableBodyWrapper.scrollTop = that.searchTableScroll.top;
        that.searchTableBodyWrapper.scrollLeft = that.searchTableScroll.left;
      });
    },

    /**
     * <AUTHOR> @description  : 获取职业病害因素列表
     */
    getHarmList() {
      let datas = {
        pageSize: 0,
        pageNumber: 0
      };
      this.$ajax
        .post(this.$apiUrls.ReadCodeOccupationalHazardousByPage, datas)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.harmList = returnData;
        });
    },
    // 导出单位套餐
    exportClus() {
      let datas = {
        companyTimes: this.parentValue.companyTimes,
        companyCode: this.companyCode
      };
      this.exportLoading = true;
      this.$ajax
        .paramsPost(this.$apiUrls.GetCompanyClusWithDetails, datas)
        .then((r) => {
          let { success, returnData } = r.data;
          if (returnData?.length == 0) {
            this.$message({
              message: '暂无套餐!',
              type: 'warning',
              showClose: true
            });
            return;
          }
          returnData.forEach((item) => {
            switch (item.sex) {
              case 1:
                item.sex = '男';
                break;
              case 2:
                item.sex = '女';
                break;

              default:
                item.sex = '通用';
                break;
            }
            item.mapCompanyClusterCombs.forEach((twoItem) => {
              twoItem.isPayBySelf = twoItem.isPayBySelf ? '是' : '否';
            });
          });
          let hazardTxt = this.G_config.physicalMode.includes('职检')
            ? '   上岗状态：${item.jobStatusName}   危害因素：${item.hazardNames}'
            : '';
          let clusInfo =
            '`套餐名称：${item.clusterName}   套餐总价：${item.price}   性别：${item.sex}' +
            hazardTxt +
            '`';
          this.exportExcel(
            this.theads,
            returnData || [],
            `体检次数：${this.parentValue.companyTimes}`,
            clusInfo,
            `${this.companyName}单位套餐`,
            'mapCompanyClusterCombs'
          );
        })
        .finally((r) => {
          this.exportLoading = false;
        });
    }
  }
};
