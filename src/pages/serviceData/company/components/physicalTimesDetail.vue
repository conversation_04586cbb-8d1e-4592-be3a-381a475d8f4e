<template>
  <div class="physicalTimesDetail">
    <div class="detail-header">
      <div class="detail-title">
        <span class="header-back" @click="goBack">
          <i class="iconfont icon-fanhui"></i>返回
        </span>
        <span class="times"
          >体检次数{{ this.parentValueLocal.companyTimes }}</span
        >
      </div>
      <el-descriptions class="margin-top" title="基本信息" :column="3">
        <template slot="extra">
          <el-button
            size="small"
            class="blue_btn"
            @click="editTimesDrawer(parentValueLocal)"
            icon="iconfont icon-bianji"
            >编辑</el-button
          >
        </template>
        <el-descriptions-item label="体检次数">{{
          this.parentValueLocal.companyTimes
        }}</el-descriptions-item>
        <el-descriptions-item label="起止日期">{{
          `${dateFormat(this.parentValueLocal.beginDate)} - ${dateFormat(
            this.parentValueLocal.endDate
          )}`
        }}</el-descriptions-item>
        <el-descriptions-item label="预约截止日期">{{
          dateFormat(this.parentValueLocal.onLineEndDate)
        }}</el-descriptions-item>
        <el-descriptions-item label="院内联系人">{{
          this.parentValueLocal.hospContact
        }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{
          this.parentValueLocal.tel
        }}</el-descriptions-item>
        <el-descriptions-item label="外检标示">{{
          this.parentValueLocal.isOutside ? '是' : '否'
        }}</el-descriptions-item>
        <el-descriptions-item label="开户银行">{{
          this.parentValueLocal.openBank
        }}</el-descriptions-item>
        <el-descriptions-item label="发票抬头">{{
          this.parentValueLocal.invoiceHeader
        }}</el-descriptions-item>
        <el-descriptions-item label="税号">{{
          this.parentValueLocal.taxID
        }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <!-- <span class="type">已完成</span> -->
        </el-descriptions-item>
        <el-descriptions-item label="备注">{{
          this.parentValueLocal.note
        }}</el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="package-info">
      <el-tabs v-model.trim="active">
        <el-tab-pane label="套餐信息" name="1">
          <div class="clusterInfo">
            <div class="info-title">
              <div>
                <el-button
                  size="small"
                  class="blue_btn"
                  @click="editClick"
                  v-if="tabList.length !== 0"
                  icon="iconfont icon-bianji"
                  >编辑</el-button
                >
                <el-button
                  size="small"
                  class="blue_btn"
                  @click="addClick"
                  icon="iconfont icon-xinjian"
                  >添加</el-button
                >
                <el-button
                  class="red_btn"
                  size="small"
                  @click="deletes"
                  v-if="tabList.length !== 0"
                  icon="iconfont icon-shanchu"
                  >删除</el-button
                >
              </div>
            </div>
            <!-- <div class="empty" v-if="tabList.length === 0">暂无套餐数据</div> -->
            <div class="cluster-tab">
              <!-- <el-tabs
                v-model.trim="activeName"
                type="card"
                @tab-click="handleClick"
                class="tabs"
              >
                <el-tab-pane
                  :label="item.clusterName"
                  :name="item.clusterCode"
                  class="tab-pane"
                  v-for="(item, index) in tabList"
                  :key="index"
                > -->
              <div class="cluster_wrap">
                <div class="cluster_list">
                  <h4>
                    <span>套餐列表</span>
                    <el-button
                      size="small"
                      class="blue_btn"
                      @click="exportClus"
                      :loading="exportLoading"
                      >导出</el-button
                    >
                  </h4>
                  <ul>
                    <el-empty
                      description="暂无套餐数据"
                      :image-size="150"
                      v-if="tabList.length === 0"
                    ></el-empty>
                    <li
                      v-for="(item, index) in tabList"
                      :title="item.clusterName"
                      :class="{ active_li: activeName === item.clusterCode }"
                      :key="index"
                      @click="
                        handleClick({ _props: { name: item.clusterCode } })
                      "
                    >
                      {{ item.clusterName }}
                    </li>
                  </ul>
                </div>
                <div class="cluster-info">
                  <!-- v-if="activeName === item.clusterCode" -->
                  <el-descriptions
                    class="margin-top descriptions"
                    title=""
                    :column="3"
                  >
                    <el-descriptions-item label="套餐代码">{{
                      packageInfo.companyCluster.clusterCode
                    }}</el-descriptions-item>
                    <el-descriptions-item label="套餐名称">{{
                      packageInfo.companyCluster.clusterName
                    }}</el-descriptions-item>
                    <el-descriptions-item label="单价">{{
                      packageInfo.companyCluster.price
                    }}</el-descriptions-item>
                    <el-descriptions-item label="指引单格式">{{
                      G_EnumList['CodeGuidanceType'][
                        packageInfo.companyCluster.guidanceType
                      ]
                    }}</el-descriptions-item>
                    <el-descriptions-item label="婚姻状况">{{
                      G_EnumList['MarryStatus'][
                        packageInfo.companyCluster.marryStatus
                      ]
                    }}</el-descriptions-item>
                    <el-descriptions-item label="年龄上限">{{
                      packageInfo.companyCluster.upperAgeLimit
                    }}</el-descriptions-item>
                    <el-descriptions-item label="报告单格式">{{
                      G_EnumList['CodeReportType'][
                        packageInfo.companyCluster.reportType
                      ]
                    }}</el-descriptions-item>
                    <el-descriptions-item label="性别">{{
                      G_EnumList['Sex'][packageInfo.companyCluster.sex]
                    }}</el-descriptions-item>
                    <el-descriptions-item label="年龄下限">{{
                      packageInfo.companyCluster.lowerAgeLimit
                    }}</el-descriptions-item>
                    <el-descriptions-item label="单位次数">{{
                      packageInfo.companyCluster.companyTimes
                    }}</el-descriptions-item>
                    <el-descriptions-item label="加项折扣">{{
                      packageInfo.companyCluster.addItemDiscount
                    }}</el-descriptions-item>
                    <el-descriptions-item label="额度内项目可替">{{
                      packageInfo.companyCluster.allowChangeItem ? '是' : '否'
                    }}</el-descriptions-item>
                    <el-descriptions-item label="备注">{{
                      packageInfo.companyCluster.note
                    }}</el-descriptions-item>
                  </el-descriptions>
                  <h4 class="table-title">已选组合列表</h4>
                  <div class="info-table">
                    <PublicTable
                      :theads.sync="theads"
                      :viewTableList.sync="packageInfo.companyComb"
                      :cell_red="['price']"
                      :tableLoading.sync="tableLoading"
                      :isSortShow="false"
                      :highlightCurrentRow="false"
                      :columnWidth="columnWidth"
                      :columnAlign="{ price: 'right' }"
                    >
                      <template #isPayBySelf="{ scope }">
                        <el-checkbox
                          v-model.trim="scope.row.isPayBySelf"
                          disabled
                        ></el-checkbox>
                      </template>
                    </PublicTable>
                  </div>
                  <div class="sum">
                    <h4>合计</h4>
                    <h4 class="text">
                      项目总数：<span class="sum-numb">{{
                        packageInfo.companyComb.length
                      }}</span>
                    </h4>
                    <h4 class="text">
                      总金额：<span class="sum-numb">{{
                        packageInfo.companyCluster.price
                      }}</span>
                    </h4>
                  </div>
                </div>
              </div>
              <!-- </el-tab-pane>
              </el-tabs> -->
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="人员信息" name="2">
          <PersonInfo :companyInfo="parentValueLocal"></PersonInfo>
        </el-tab-pane>
        <!-- 团体结算 -->
        <el-tab-pane label="结算记录" name="3">
          <BalanceRecord :companyInfo="parentValueLocal"></BalanceRecord>
        </el-tab-pane>
      </el-tabs>
    </div>
    <el-drawer
      title="套餐信息属性"
      :visible.sync="drawer"
      :before-close="cancel"
      :wrapperClosable="false"
      size="90%"
      class="package-drawer"
    >
      <div class="drawer-box">
        <el-form
          :model="popupForm"
          ref="ruleForm"
          label-width="100px"
          class="drawer-form"
          :rules="rules"
        >
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="套餐代码"
                v-if="isAddOrEdit"
                prop="clusterCode"
              >
                <el-input
                  v-model.trim="popupForm.clusterCode"
                  size="small"
                  placeholder="请输入套餐代码"
                  :disabled="isAddOrEdit"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="套餐名称" prop="clusterName">
                <el-input
                  v-model.trim="popupForm.clusterName"
                  size="small"
                  placeholder="请输入套餐名称"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="单价" prop="price">
                <el-input
                  v-model.trim="popupForm.price"
                  size="small"
                  placeholder="请输入单价"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="婚姻状况" prop="marryStatus">
                <el-select
                  class="select"
                  v-model.trim="popupForm.marryStatus"
                  placeholder="请选择婚姻状况"
                  size="small"
                >
                  <el-option
                    v-for="(item, index) in G_marriageStatus"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="指引单格式" prop="guidanceType">
                <el-select
                  class="select"
                  v-model.trim="popupForm.guidanceType"
                  placeholder="请选择指引单格式"
                  size="small"
                >
                  <el-option
                    v-for="(item, index) in G_codeGuidanceType"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="报告单格式" prop="reportType">
                <el-select
                  class="select"
                  v-model.trim="popupForm.reportType"
                  placeholder="请选择报告单格式"
                  size="small"
                >
                  <el-option
                    v-for="(item, index) in G_codeReportType"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="性别" prop="sex">
                <el-select
                  class="select"
                  v-model.trim="popupForm.sex"
                  placeholder="请选择性别"
                  size="small"
                  @change="sexChange"
                >
                  <el-option
                    v-for="(item, index) in G_shareSexList"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="年龄上限" prop="upperAgeLimit">
                <el-input
                  v-model.trim="popupForm.upperAgeLimit"
                  size="small"
                  placeholder="请输入年龄上限"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="年龄下限" prop="lowerAgeLimit">
                <el-input
                  v-model.trim="popupForm.lowerAgeLimit"
                  size="small"
                  placeholder="请输入年龄下限"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="单位次数" prop="companyTimes">
                <el-input
                  v-model.trim="popupForm.companyTimes"
                  size="small"
                  placeholder="请输入单位次数"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="加项折扣" prop="addItemDiscount">
                <el-input-number
                  size="small"
                  v-model.trim="popupForm.addItemDiscount"
                  :precision="1"
                  :step="0.1"
                  :min="0"
                  :max="1"
                  class="select"
                  placeholder="输入0-1之间的数"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-row>
                <el-col :span="12">
                  <el-form-item prop="allowChangeItem" class="allowChangeItem">
                    <el-checkbox
                      v-model.trim="popupForm.allowChangeItem"
                      class="checkbox"
                      >额度内项目可替</el-checkbox
                    >
                  </el-form-item>
                </el-col>
                <el-col
                  :span="12"
                  v-if="
                    G_config.physicalMode.includes('职检') &&
                    G_config.physicalMode.includes('普检')
                  "
                >
                  <el-form-item prop="isOccupation" class="allowChangeItem">
                    <el-checkbox
                      v-model.trim="popupForm.isOccupation"
                      class="checkbox"
                      >职</el-checkbox
                    >
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <template v-if="popupForm.isOccupation">
              <el-col :span="8">
                <el-form-item label="在岗状态" prop="jobStatus">
                  <el-select
                    clearable
                    class="select"
                    style="width: 100%"
                    v-model.trim="popupForm.jobStatus"
                    placeholder="请选择"
                    size="small"
                    filterable
                    @change="autoAddCombination"
                  >
                    <el-option
                      v-for="(item, index) in G_CodeOccupationalPositionStatus"
                      :key="index"
                      :value="item.value"
                      :label="item.label"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="危害因素" prop="hazardFactors">
                  <el-select
                    class="select"
                    style="width: 100%"
                    multiple
                    v-model.trim="popupForm.hazardFactors"
                    placeholder="请选择"
                    size="small"
                    filterable
                    @change="autoAddCombination"
                  >
                    <el-option
                      v-for="(item, index) in harmList"
                      :key="index"
                      :value="item.hazardousCode"
                      :label="item.hazardousName"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </template>
            <el-col :span="8">
              <el-form-item label="备注" prop="note">
                <el-input
                  v-model.trim="popupForm.note"
                  size="small"
                  placeholder="请输入备注"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="drawer-option">
          <h4>选择套餐项目</h4>
          <div>
            <span class="option-lable">套餐模板</span>
            <!-- <el-cascader
              class="cascader"
              v-model.trim="checkType"
              :options="options"
              placeholder="选择类型"
              size="small"
              @change="handleChange"
              filterable
              clearable
              ref="cascader"
              :props="props"
              :filter-method="filterMethod"
            ></el-cascader> -->
            <el-select
              size="small"
              @change="mealTypeChange"
              v-model="mealTypeVal"
              placeholder="请选择"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
            <el-cascader
              v-if="mealTypeVal == 'group'"
              v-model="checkCompanyVal"
              size="small"
              placeholder="请选择单位和次数"
              :options="companyWithTimesList"
              @change="companyChange"
              clearable
              filterable
            >
            </el-cascader>
            <el-select
              class="select-input"
              v-model.trim="checkPackage"
              placeholder="选择套餐"
              size="small"
              filterable
              clearable
              @change="changeSelect"
            >
              <el-option
                v-for="(item, index) in packageOption"
                :key="index"
                :label="item.clusterName"
                :value="item.clusterCode"
              ></el-option>
            </el-select>
            <el-button
              size="small"
              class="green_btn btn"
              icon="iconfont icon-qingkonghuancun"
              @click="clearClick"
              >清空</el-button
            >
            <el-button
              size="small"
              class="blue_btn btn"
              icon="iconfont icon-baocun"
              @click="save('ruleForm')"
              >保存</el-button
            >
            <!-- <el-button size="small" @click="cancel">取消</el-button> -->
          </div>
        </div>
        <div class="drawer-table">
          <el-row :gutter="20" class="row">
            <el-col :span="12" class="col">
              <div class="table-left">
                <div class="left-title">
                  <h4>已选组合列表</h4>
                  <div>
                    <!-- <span class="price" @click="priceReset">单价复原</span>
                    <span class="discount" @click="discount">打折</span> -->
                    <el-popover
                      placement="bottom"
                      width="290"
                      trigger="click"
                      @after-enter="showDiscount"
                      @after-leave="closePopover"
                    >
                      <div style="display: flex; align-items: center">
                        批量折扣：
                        <el-input-number
                          ref="ref_input"
                          @keyup.enter.native.stop="discountConfirm"
                          size="mini"
                          v-model="batchDiscount"
                          controls-position="right"
                          :precision="2"
                          :min="0"
                          :step="0.01"
                          :max="1"
                        ></el-input-number>
                        <el-button
                          type="primary"
                          style="margin-left: 3px"
                          size="mini"
                          @click="discountConfirm"
                          >确 定</el-button
                        >
                      </div>

                      <span
                        v-show="leftTable.length > 0"
                        slot="reference"
                        type="text"
                        style="
                          color: #1770df;
                          margin-left: 3px;
                          cursor: pointer;
                          margin-right: 3px;
                        "
                        icon="iconfont icon-shanchu"
                      >
                        <i class="el-icon-price-tag"></i>
                        批打折
                      </span>
                    </el-popover>
                    <el-button
                      size="small"
                      class="violet_btn btn"
                      @click="selfFundedSwitching"
                      >{{ isAllPayBySelf ? '自费全选' : '取消自费' }}</el-button
                    >
                    <el-button
                      size="small"
                      class="violet_btn btn"
                      icon="iconfont icon-huifuxitongmoren"
                      v-show="isAddOrEdit"
                      @click="restorePrice"
                      >重置折扣</el-button
                    >
                  </div>
                </div>
                <div class="left-table">
                  <PublicTable
                    ref="leftTable_ref"
                    :theads.sync="leftThead"
                    :viewTableList.sync="leftTable"
                    :cell_red="['price', 'originalPrice']"
                    :tableLoading.sync="tableLoading"
                    :isSortShow="false"
                    :columnWidth="columnWidth"
                    :columnAlign="{ price: 'right', originalPrice: 'right' }"
                    @rowDblclick="leftRemove"
                  >
                    <template #isPayBySelf="{ scope }">
                      <el-checkbox
                        v-model.trim="scope.row.isPayBySelf"
                        @change="handleIsPayBySelfChange"
                      ></el-checkbox>
                    </template>
                    <template #discount="{ scope }">
                      <div @dblclick.stop>
                        <el-input-number
                          size="mini"
                          @change="discountChange(arguments, scope.row)"
                          :precision="2"
                          v-model="scope.row.discount"
                          controls-position="right"
                          :min="0.01"
                          :step="0.01"
                          :max="1"
                        ></el-input-number>
                      </div>
                    </template>
                    <template #columnRight>
                      <el-table-column
                        prop="operation"
                        label=""
                        width="44"
                        fixed="right"
                      >
                        <template slot-scope="scope">
                          <div
                            class="left-remove"
                            @click="leftRemove(scope.row)"
                          >
                            <i class="iconfont icon-Rightxiangyou34"></i>
                          </div>
                        </template>
                      </el-table-column>
                    </template>
                  </PublicTable>
                </div>
                <div class="sum">
                  <h4>合计</h4>
                  <h4 class="text">
                    项目总数：<span class="sum-numb">{{
                      leftTable.length
                    }}</span>
                  </h4>
                  <h4 class="text">
                    总金额：<span class="sum-numb">{{ totalSumAll }}</span>
                  </h4>
                </div>
              </div>
            </el-col>
            <el-col :span="12" class="col">
              <div class="table-right">
                <div class="right-title">
                  <h4>组合列表</h4>
                  <div class="right-btn">
                    <el-input
                      clearable
                      v-model.trim="inputRight"
                      size="small"
                      placeholder="请输入搜索内容"
                      class="search-input"
                      @keyup.enter.native="drawerSearchClick"
                      @input="drawerSearchClick"
                      @clear="drawerSearchClick"
                    ></el-input>
                    <el-button
                      size="small"
                      class="blue_btn btn"
                      icon="iconfont icon-search"
                      @click="drawerSearchClick"
                      >搜索</el-button
                    >
                  </div>
                </div>
                <div class="right-table">
                  <PublicTable
                    ref="searchTable_ref"
                    :theads.sync="rigthThead"
                    :viewTableList.sync="searchTable"
                    :cell_red="['price']"
                    :tableLoading.sync="tableLoading"
                    :isSortShow="false"
                    :columnWidth="columnWidth"
                    @rowDblclick="rightRemove"
                  >
                    <template #columnLeft>
                      <el-table-column
                        prop="operation"
                        label=""
                        width="44"
                        fixed="left"
                      >
                        <template slot-scope="scope">
                          <div class="remove" @click="rightRemove(scope.row)">
                            <i class="iconfont icon-Leftxiangzuo35"></i>
                          </div>
                        </template>
                      </el-table-column>
                    </template>
                    <template #columnRight>
                      <el-table-column width="50" label="明细">
                        <template slot-scope="scope">
                          <el-popover
                            placement="right"
                            width="400"
                            @show="combExpandChange(scope.row)"
                            trigger="click"
                          >
                            <el-table
                              :data="scope.row.detailList"
                              height="300px"
                            >
                              <el-table-column
                                width="80"
                                property="itemCode"
                                label="项目代码"
                              ></el-table-column>
                              <el-table-column
                                property="itemName"
                                label="项目名称"
                              ></el-table-column>
                            </el-table>
                            <span
                              slot="reference"
                              style="color: #1770df; cursor: pointer"
                              >查看</span
                            >
                          </el-popover>
                        </template>
                      </el-table-column>
                    </template>
                  </PublicTable>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-drawer>
    <el-drawer
      title="基本信息属性"
      :visible.sync="timesDrawer"
      :before-close="cancel"
      :wrapperClosable="false"
      size="80%"
    >
      <PhysicalTimesForm
        :parameters="parameters"
        :rules="rules2"
        @cancel="cancel"
        @save="timesDrawerSave"
        :disabled="true"
        ref="ruleForm_ref"
      ></PhysicalTimesForm>
    </el-drawer>
  </div>
</template>

<script>
import PublicTable from '@/components/publicTable';
import PhysicalTimesForm from '../drawer/physicalTimesForm.vue';
import PersonInfo from './personInfo.vue';
import detail from '../mixins/detail';
import BalanceRecord from './balanceRecord.vue';
import { mapGetters } from 'vuex';
export default {
  name: 'physicalTimesDetail',
  components: {
    PublicTable,
    PhysicalTimesForm,
    PersonInfo,
    BalanceRecord
  },
  mixins: [detail],
  computed: {
    ...mapGetters(['G_config'])
  },
  props: {
    parentValue: {
      type: Object,
      default: {}
    },
    menuData: {
      type: Object,
      default: {}
    },
    companyCode: {
      type: String,
      default: ''
    },
    companyName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      parentValueLocal: this.parentValue,
      random: Math.random()
    };
  },
  created() {},
  methods: {
    // 获取组合明细
    combExpandChange(row) {
      console.log(row);
      this.$ajax
        .paramsPost(this.$apiUrls.ReadCandidateCombItems, {
          combCode: row.combCode
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) {
            return;
          }
          row.detailList = returnData;
          // this.$set(row, 'detailList', returnData);
        });
    }
  },
  mounted() {
    this.popupForm.guidanceType = this.G_codeGuidanceType[0]?.value;
    this.popupForm.sex = 0;
    this.popupForm.reportType = this.G_config.physicalMode.includes('普检')
      ? this.G_codeReportType[0]?.value
      : this.G_codeReportType[this.G_codeReportType.length - 1]?.value;
  },
  watch: {
    parentValue(newVal, oldVal) {
      this.parentValueLocal = newVal;
    }
  }
};
</script>

<style lang="less" scoped>
.physicalTimesDetail {
  height: 100%;
  display: flex;
  flex-direction: column;
  font-size: 18px;
  color: #001618;
  .detail-header {
    background: #fff;
    padding: 16px;
    padding-bottom: 4px;
    font-size: 18px;
    color: #001618;
    margin-bottom: 5px;
    border-radius: 4px;
  }
  .detail-title {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    font-weight: 600;
    line-height: 19px;
  }
  .header-back {
    color: #1770df;
    display: flex;
    align-items: center;
    cursor: pointer;
    i {
      margin-right: 10px;
    }
  }
  .type {
    color: #3cb34f;
  }
  .times {
    padding-left: 15px;
    margin-left: 15px;
    border-left: 2px solid #001618;
  }
  .package-info {
    height: calc(100% - 267px);
    min-height: 560px;
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    background: #fff;
    border-radius: 4px;
    padding: 0 5px;
    h4 {
      color: #2d3436;
    }
  }
  .info-title {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 5px;
  }
  .info-table {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    width: 100%;
    min-height: 290px;
  }
  .drawer-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .drawer-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 20px;
    width: 100%;
    h4 {
      font-size: 18px;
      color: #2d3436;
    }
  }
  .drawer-form {
    background: #fff;
    padding-right: 20px;
    padding-top: 4px;
  }
  .checkbox {
    height: 40px;
    line-height: 40px;
  }
  .cascader {
    width: 180px;
    margin: 0 10px;
  }
  .select {
    width: 100%;
  }
  .select-input {
    width: 160px;
    margin-right: 10px;
  }
  .option-lable {
    font-weight: 600;
  }
  .drawer-table {
    background: #fff;
    padding: 8px 18px;
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
  }
  .table-left,
  .table-right {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .row,
  .col {
    height: 100%;
  }
  .left-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    line-height: 40px;
    span {
      color: #1770df;
      cursor: pointer;
    }
  }
  .price {
    margin-right: 20px;
  }
  .discount {
    margin-right: 50px;
  }
  .right-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    line-height: 40px;
  }
  .right-btn {
    display: flex;
    align-items: center;
  }
  .search-input {
    width: 160px;
    margin-right: 10px;
  }
  .select {
    width: 100%;
  }
  .times-drawer {
    background: #fff;
  }
  .remove {
    width: 24px;
    height: 24px;
    background: rgba(23, 112, 223, 0.2);
    text-align: center;
    color: #1770df;
    cursor: pointer;
    font-size: 16px;
    font-weight: normal;
  }
  .left-remove {
    width: 24px;
    height: 24px;
    background: rgba(214, 48, 49, 0.2);
    text-align: center;
    color: #d63031;
    cursor: pointer;
    font-size: 16px;
    font-weight: normal;
  }
  .sum {
    background: rgba(23, 112, 223, 0.2);
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 18px;
    .text {
      color: #d63031;
    }
    .sum-numb {
      font-size: 18px;
    }
  }
  .left-table,
  .right-table {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
  }
  .empty {
    text-align: center;
    color: #5e6d82;
    font-size: 16px;
    height: 360px;
    line-height: 360px;
  }
  .package-info/deep/.el-tabs {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .package-info/deep/.el-tabs__content {
    flex: 1;
    overflow: auto;
  }
  .package-info/deep/.el-tab-pane {
    height: 100%;
  }
  .package-drawer/deep/.el-drawer__header {
    background: #d1e2f9;
    margin-bottom: 0;
    padding: 8px 18px;
  }
  .cluster-tab {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
  }
  .cluster-tab/deep/.el-tabs {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .cluster-tab/deep/.el-tab-pane {
    height: 100%;
  }
  .cluster-tab/deep/.el-tabs__content {
    height: 100%;
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
  }
  .cluster_wrap {
    display: flex;
    height: 100%;
    overflow: auto;
    .cluster_list {
      width: 200px;
      display: flex;
      flex-direction: column;
      margin-right: 5px;
      h4 {
        display: flex;
        justify-content: space-between;
      }
      ul {
        flex: 1;
        flex-shrink: 0;
        overflow: auto;
        border-radius: 4px;
        border: 1px solid #e4e7ed;
        li {
          padding: 10px 5px;
          border-bottom: 1px solid #e4e7ed;
          cursor: pointer;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .active_li {
          background: #1770df;
          color: #fff;
        }
      }
    }
  }
  .cluster-info {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    display: flex;
    flex-direction: column;
    .table-title {
      margin-bottom: 14px;
    }
  }
  .clusterInfo {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .package-drawer/deep/.el-drawer__body {
    background: #f0f2f3;
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden;
  }
  .allowChangeItem {
    /deep/.el-form-item__content {
      margin-left: 0 !important;
    }
  }
  /deep/.el-descriptions__title {
    font-size: 18px;
    color: #2d3436;
  }
  /deep/.el-descriptions-item__cell {
    font-size: 14px;
    color: #2d3436;
    padding-bottom: 0px;
  }
  /deep/.el-tabs__item {
    font-size: 16px;
  }
  /deep/.el-tabs--card > .el-tabs__header {
    border-bottom: none;
    margin: 0 0 10px;
  }
  /deep/.el-tabs--card > .el-tabs__header .el-tabs__item {
    transition: 0s;
  }
  /deep/.el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
    color: #fff;
    background: #1770df;
    border: 1px solid #1770df;
  }
  /deep/.el-tabs--card > .el-tabs__header .el-tabs__nav {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
  }
  .drawer-form/deep/.el-form-item {
    margin-bottom: 4px;
  }
  .btn {
    padding: 6.5px 10px;
  }
}
</style>
<style lang="less">
.physicalTimesDetail {
  .el-input-number--mini {
    width: 75px;
  }
  .el-input-number.is-controls-right .el-input__inner {
    padding-left: 0 !important;
    padding-right: 32px !important;
  }
}
</style>
