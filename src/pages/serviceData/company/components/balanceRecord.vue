<template>
  <div class="balanceRecord_page">
    <header>
      <h4>结算记录列表</h4>
      <p>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-plus"
          @click="addBalanceRecord"
          >添加结算</el-button
        >
      </p>
    </header>
    <div class="table_wrap">
      <PublicTable
        :theads="theads"
        :viewTableList.sync="balanceRecordList"
        :columnWidth="columnWidth"
        :cell_red="['originalPrice', 'actuallyPrice']"
        :tableCellClassName="tableCellClassName"
        @cell-click="cellClick"
      >
        <template #payStatus="{ scope }">
          <div>
            {{ G_EnumList['PayStatus'][scope.row.payStatus] }}
          </div>
        </template>
        <template #feeType="{ scope }">
          <div>
            {{ feeType_enum[scope.row.feeType] }}
          </div>
        </template>
        <template #caluateType="{ scope }">
          <div>
            {{ caluateType_enum[scope.row.caluateType] }}
          </div>
        </template>
        <template #columnRight>
          <el-table-column prop="" label="操作" width="200" fixed="right">
            <template slot-scope="scope">
              <div class="btn_wrap">
                <el-button
                  plain
                  size="mini"
                  class="red_btn"
                  :title="scope.row.auditOperator ? '撤销审核' : '审核'"
                  @click="auditBalanceRecord(scope.row)"
                  v-if="scope.row.payStatus === 0"
                  >{{ scope.row.auditOperator ? '撤审' : '审核' }}</el-button
                >
                <el-button
                  plain
                  size="mini"
                  class="red_btn"
                  title="撤销退费"
                  @click="feeDetailBtn(scope.row)"
                  v-if="scope.row.auditOperator"
                  >明细</el-button
                >
                <el-button
                  plain
                  size="mini"
                  class="red_btn"
                  title="退费"
                  @click="refund(scope.row)"
                  v-if="scope.row.payStatus === 1 && scope.row.feeType !== 1"
                  >退费</el-button
                >
                <el-button
                  plain
                  size="mini"
                  class="red_btn"
                  @click="cancelRefund(scope.row)"
                  title="撤销退费"
                  v-if="scope.row.feeType === 1"
                  >撤退</el-button
                >
                <el-button
                  plain
                  size="mini"
                  class="red_btn"
                  title="打印"
                  @click="printBtn(scope.row)"
                  v-if="scope.row.auditOperator"
                  >打印</el-button
                >
                <el-button
                  plain
                  size="mini"
                  class="red_btn"
                  title="删除"
                  v-if="!scope.row.auditOperator"
                  @click="delBalanceRecord(scope.row)"
                  >删除</el-button
                >
              </div>
            </template>
          </el-table-column>
        </template>
      </PublicTable>
    </div>
    <el-dialog
      title="添加结算"
      width="1020px"
      :visible.sync="balanceRecordShow"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closeBalanceRecord"
    >
      <div class="dialog_content">
        <div class="dialog_search">
          <el-input
            size="small"
            style="width: 200px; margin-right: 10px"
            v-model.trim="balanceRecordVal"
            placeholder="请输入体检号/姓名搜索"
            @input="balanceRecordSearch"
          ></el-input>
          <div class="search-item">
            <span>部门</span>
            <el-select
              class="select"
              multiple
              collapse-tags
              v-model.trim="companyDeptCode"
              placeholder="请选择"
              size="small"
              filterable
              clearable
              @change="QueryPersonListForSettlement"
            >
              <el-option
                v-for="(item, index) in companyDeptList"
                :key="index"
                :label="item.deptName"
                :value="item.deptCode"
              ></el-option>
            </el-select>
          </div>
          <div class="search-item">
            <span>套餐</span>
            <el-select
              placeholder="请选择"
              size="small"
              collapse-tags
              filterable
              clearable
              multiple
              v-model="clusCode"
              class="input"
              @change="QueryPersonListForSettlement"
            >
              <el-option
                v-for="item in clusterList"
                :key="item.clusCode"
                :label="item.clusName"
                :value="item.clusCode"
              >
              </el-option>
            </el-select>
          </div>
          <el-select
            v-model="type"
            placeholder="请选择"
            @change="QueryPersonListForSettlement"
            size="small"
            style="width: 120px"
          >
            <el-option label="按登记时间" :value="0"></el-option>
            <el-option label="按审核时间" :value="1"></el-option>
          </el-select>
          <el-date-picker
            :picker-options="{ shortcuts: G_datePickerShortcuts }"
            size="small"
            v-model="dateVal"
            type="daterange"
            range-separator="-"
            :clearable="false"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="QueryPersonListForSettlement"
            @input="$forceUpdate()"
          >
          </el-date-picker>
        </div>
        <div class="search_result">
          <el-button
            size="small"
            style="margin-right: 10px"
            :disabled="unAddList.length === 0"
            @click="batchEdit"
            >{{ isSelectAll ? '全选' : '全不选' }}</el-button
          >
          <div class="status-wrap">
            <span>总条数：{{ totalNumber }}</span>
            <span class="cell_blue">当前表格条数：{{ unAddList.length }}</span>
            <span class="cell_red">已选：{{ checkList.length }}</span>
            <span class="cell_green">未选：{{ notSelected }}</span>
          </div>
          <el-radio-group v-model.trim="radioVal" @change="balanceRecordSearch">
            <el-radio :label="0">全部</el-radio>
            <el-radio :label="1">已选</el-radio>
            <el-radio :label="2">未选</el-radio>
          </el-radio-group>
        </div>
        <div class="dialog_table" style="display: flex">
          <PublicTable
            ref="settlementTable_Ref"
            :theads="unAddTheads"
            :viewTableList.sync="unAddList"
            :tableLoading="tableLoading"
            :columnWidth="unAddColumnWidth"
            :columnSort="['peStatus', 'regNo']"
            @rowClick="rowClick"
            :tableRowClassName="tableRowClassName"
          >
            <template #sex="{ scope }">
              <div>
                {{ G_EnumList['Sex'][scope.row.sex] }}
              </div>
            </template>
            <template #peStatus="{ scope }">
              <div>
                {{ G_EnumList['PeStatus'][scope.row.peStatus] }}
              </div>
            </template>
          </PublicTable>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="balanceRecordShow = false">取 消</el-button>
        <el-button type="primary" @click="confirmAdd">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 费用明细 -->
    <el-dialog
      title="结算费用明细"
      width="60%"
      :visible.sync="feeDetailShow"
      :close-on-click-modal="false"
    >
      <div class="dialog_content">
        <div class="dialog_table">
          <PublicTable
            :theads="feeDetailTheads"
            :viewTableList.sync="feeDetailList"
            :columnWidth="feeDetailWidth"
            :cell_red="['price']"
          >
            <template #sex="{ scope }">
              <div>
                {{ G_EnumList['Sex'][scope.row.sex] }}
              </div>
            </template>
          </PublicTable>
        </div>
        <ul class="feeDetail_bottom">
          <li>
            <span>数量：</span>
            <i>{{ feeDetailInfo.count }}</i>
          </li>
          <li>
            <span>总计：</span>
            <i>{{ feeDetailInfo.price }}</i> 元
          </li>
        </ul>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="feeDetailShow = false">取 消</el-button>
        <el-button @click="exportTable">导 出</el-button>
        <!-- <el-button type="primary" @click="confirmAdd">确 定</el-button> -->
      </div>
    </el-dialog>
    <!-- 修改发票抬头弹窗 -->
    <el-dialog
      :title="'修改' + modifyTitle"
      :visible.sync="modifyShow"
      :close-on-click-modal="false"
    >
      <el-form :model="form" :rules="rules" ref="modify_Ref">
        <el-form-item
          :label="modifyTitle"
          label-width="110px"
          prop="invoiceName"
        >
          <el-input
            ref="modifyRef_inp"
            clearable
            v-model="form.invoiceName"
            autocomplete="off"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="modifyShow = false">取 消</el-button>
        <el-button type="primary" @click="confirmModify">确 定</el-button>
      </div>
    </el-dialog>
    <PrintPreview
      v-model="previewShow"
      :dataInfo.sync="dataInfo"
      :printerTypeList.sync="printerTypeList"
      isBalanceRecord
    />
  </div>
</template>
<script>
import PublicTable from '@/components/publicTable';
import PublicTable2 from '@/components/publicTable2';
import { mapGetters } from 'vuex';
import { dataUtils } from '../../../../common';
import { export2Excel } from '@/common/excelUtil';
import PrintPreview from '@/components/printPreview';
import ExportExcel from '@/common/excel/exportExcel';
import moment from 'moment';
export default {
  name: 'balanceRecord',
  mixins: [ExportExcel],
  props: {
    companyInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  components: {
    PublicTable,
    PrintPreview,
    PublicTable2
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_datePickerShortcuts']),
    isSelectAll() {
      if (this.unAddList?.length === 0) return true;
      const checkSet = new Set(this.checkList?.map((item) => item.regNo));
      return this.unAddList?.some((item) => !checkSet.has(item.regNo));
    },
    notSelected() {
      const checkSet = new Set(this.checkList?.map((item) => item.regNo));
      return (
        this.fixed_unAddList?.filter((item) => !checkSet.has(item.regNo))
          ?.length || 0
      );
    },
    totalNumber() {
      let totalData = dataUtils.deepCopy(this.fixed_unAddList);
      const existingRegNos = new Set(
        this.fixed_unAddList.map((item) => item.regNo)
      );
      this.checkList.forEach((item) => {
        if (!existingRegNos.has(item.regNo)) {
          totalData.push(item);
        }
      });
      return totalData.length;
    }
  },
  data() {
    return {
      dataInfo: {},
      printerTypeList: [
        {
          printerType: 0, //打印机类型 0 普通打印机，1 条码打印机
          label: '结算记录',
          checked: true
        }
      ],
      previewShow: false,
      balanceRecordVal: '',
      modifyShow: false,
      form: {
        invoiceName: ''
      },
      rules: {
        invoiceName: [
          { required: true, message: '请输入内容', trigger: 'blur' }
        ]
      },
      modifyTitle: '',
      checkRow: {},
      balanceRecordShow: false,
      feeDetailShow: false,
      feeDetailInfo: {}, //费用明细信息
      feeDetailList: [], //费用明细列表
      feeDetailTheads: {
        billSeqNo: '结算批次号',
        companyCode: '单位代码',
        companyName: '单位名称',
        regNo: '体检号',
        name: '姓名',
        cardNo: '身份证',
        age: '年龄',
        sex: '性别',
        tel: '电话',
        clusterName: '套餐名',
        price: '金额'
      },
      feeDetailWidth: {
        billSeqNo: '100',
        companyCode: '90',
        regNo: '120',
        name: '100',
        cardNo: '165',
        age: '50',
        sex: '50',
        tel: '110',
        clusterName: '100',
        price: '100'
      },
      rechargeTheads: [
        {
          prop: 'name',
          label: '姓名',
          align: '',
          width: '100px'
        },
        {
          prop: 'regNo',
          label: '体检号',
          align: '',
          width: '120px'
        }
      ],
      type: 0,
      dateVal: [
        this.companyInfo.beginDate || new Date().toISOString().split('T')[0],
        this.companyInfo?.endDate || new Date().toISOString().split('T')[0]
      ],
      theads: {
        billSeqNo: '当次结算流水号',
        companyCode: '单位代码',
        companyTimes: '单位次数',
        billTimes: '结算次数',
        billPersonCount: '体检人数',
        taxID: '社会信用代码',
        invoiceName: '发票名称',
        // invoiceId:'发票ID',
        // invoiceNo:'发票编号',
        electronicInvoiceNo: '电子发票号',
        sendOperator: '姓名（工号）',
        createTime: '创建时间',
        payStatus: '支付状态',
        chargeTime: '结算时间',
        feeType: '订单状态',
        originalPrice: '原始金额',
        actuallyPrice: '实收金额',
        caluateType: '结算方式',
        auditOperator: '审核人',
        auditTime: '审核时间',
        chargeDateScope: '统计时间段',
        note: '备注'
      },
      feeType_enum: {
        0: '正常',
        1: '待退费',
        2: '已退费'
      },
      caluateType_enum: {
        0: '按登记时间',
        1: '按审核时间'
      },
      balanceRecordList: [],
      columnWidth: {
        createTime: 155,
        chargeTime: 155,
        auditTime: 155,
        invoiceName: 150,
        caluateType: 90,
        sendOperator: 120,
        taxID: 150,
        chargeDateScope: 155,
        note: 150,
        electronicInvoiceNo: 100
      },
      unAddTheads: {
        peStatus: '体检状态',
        regNo: '体检号',
        name: '姓名',
        sex: '性别',
        age: '年龄',
        cardNo: '证件号',
        registerTime: '登记时间',
        activeTime: '激活时间'
      },
      unAddColumnWidth: {
        regNo: 120,
        sex: 50,
        age: 50,
        cardNo: 170,
        peStatus: 100
      },
      unAddList: [],
      fixed_unAddList: [],
      checkList: [],
      tableLoading: false,
      radioVal: 0,
      clusCode: [],
      companyDeptCode: [],
      companyDeptList: [],
      clusterList: [],
      isShift: false,
      isMultipleChoice: '',
      isCheck: ''
    };
  },
  methods: {
    getDepartList() {
      this.companyDeptCode = [];
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanyDepartment, {
          companyCode: this.companyInfo.companyCode,
          deptCode: '',
          deptName: ''
        })
        .then((r) => {
          //console.log("r", r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.companyDeptList = returnData || [];
        });
    },
    companyTimesChange() {
      this.clusCode = [];
      this.$ajax
        .post(this.$apiUrls.ReadCompanyCandidateCluster, '', {
          query: {
            companyCode: this.companyInfo.companyCode,
            companyTimes: this.companyInfo.companyTimes
          }
        })
        .then(async (r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.clusterList = returnData || [];
        });
    },
    // 获取结算记录
    GetCompanySettlement() {
      console.log(this.companyInfo);
      let datas = {
        companyCode: this.companyInfo.companyCode,
        companyTimes: this.companyInfo.companyTimes
      };
      this.$ajax
        .paramsPost(this.$apiUrls.GetCompanySettlement, datas)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.balanceRecordList = returnData;
        });
    },
    // 显示添加待结算人员弹窗
    addBalanceRecord() {
      this.balanceRecordShow = true;
      this.balanceRecordVal = '';
      this.QueryPersonListForSettlement();
    },
    // 获取待结算人员信息
    QueryPersonListForSettlement() {
      let datas = {
        startTime: dataUtils.dateToStrStart(this.dateVal[0] || new Date()),
        endTime: dataUtils.dateToStrEnd(this.dateVal[1] || new Date()),
        type: this.type,
        companyCode: this.companyInfo.companyCode,
        companyTimes: this.companyInfo.companyTimes,
        clusCodes: this.clusCode,
        companyDeptCodes: this.companyDeptCode
      };
      console.log(datas);

      this.tableLoading = true;
      this.$ajax
        .post(this.$apiUrls.QueryPersonListForSettlement, datas, { timeout: 0 })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.fixed_unAddList = dataUtils.deepCopy(returnData);
          this.unAddList = returnData;
          // this.checkList = this.checkList.filter(item => {
          //   return this.fixed_unAddList.some(item2 => item2.regNo == item.regNo)
          // })
          this.balanceRecordSearch();
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    // 结算记录的搜索
    balanceRecordSearch() {
      let totalData = dataUtils.deepCopy(this.fixed_unAddList);
      const fixedRegNos = new Set(
        this.fixed_unAddList.map((item) => item.regNo)
      );
      const checkRegNos = new Set(this.checkList.map((item) => item.regNo));
      this.checkList.forEach((item) => {
        if (!fixedRegNos.has(item.regNo)) {
          totalData.push(item);
        }
      });
      const isMatch = (item) => {
        return (
          item.name.includes(this.balanceRecordVal) ||
          item.regNo.includes(this.balanceRecordVal)
        );
      };
      const checkRegNo = (item) => {
        const existsInCheckList = checkRegNos.has(item.regNo);
        switch (this.radioVal) {
          case 0:
            return isMatch(item);
          case 1:
            return isMatch(item) && existsInCheckList;
          case 2:
            return isMatch(item) && !existsInCheckList;
          default:
            return true;
        }
      };
      this.unAddList = totalData.filter(checkRegNo);
    },
    //添加结算记录表格行点击
    rowClick(row) {
      const checkListRegNos = new Set(this.checkList.map((item) => item.regNo));
      const unAddListRegNos = new Set(this.unAddList.map((item) => item.regNo));

      let state = checkListRegNos.has(row.regNo);
      let idx = unAddListRegNos.has(row.regNo)
        ? this.unAddList.findIndex((item) => item.regNo === row.regNo)
        : -1;

      this.$nextTick(() => {
        if (this.isShift) {
          if (this.isCheck === '') {
            this.isCheck = !state;
          }
          if (this.isMultipleChoice !== '') {
            this.shiftClick([this.isMultipleChoice, idx]);
          } else {
            this.isMultipleChoice = idx;
          }
        } else {
          this.isMultipleChoice = '';
          this.isCheck = '';
        }
      });
      if (state) {
        this.checkList = this.checkList.filter(
          (item) => item.regNo !== row.regNo
        );
      } else {
        this.checkList.push(row);
      }
    },
    // 按住shift多选操作
    shiftClick(datas) {
      let selectArr = this.unAddList.slice(
        Math.min(datas[0], datas[1]),
        Math.max(datas[0], datas[1]) + 1
      );
      if (this.isCheck) {
        selectArr.map((item) => {
          if (
            !this.checkList.some((checkItem) => checkItem.regNo === item.regNo)
          ) {
            this.checkList.push(item);
          }
        });
      } else {
        this.checkList = this.checkList.filter(
          (item) => !selectArr.some((item2) => item2.regNo === item.regNo)
        );
      }
      this.isMultipleChoice = '';
      this.isCheck = '';
    },
    //全选/全不选
    batchEdit() {
      if (this.isSelectAll) {
        const checkSet = new Set(this.checkList.map((item) => item.regNo));
        this.unAddList.forEach((item) => {
          if (!checkSet.has(item.regNo)) {
            this.checkList.push(item);
          }
        });
      } else {
        const unAddSet = new Set(this.unAddList.map((item) => item.regNo));
        this.checkList = this.checkList.filter(
          (item) => !unAddSet.has(item.regNo)
        );
      }
    },
    //清空表单
    closeBalanceRecord() {
      this.checkList = [];
      this.unAddList = [];
      this.radioVal = 0;
      this.balanceRecordVal = '';
      this.companyDeptCode = [];
      this.clusCode = [];
      this.type = 0;
    },
    // 确定添加记录
    confirmAdd() {
      if (this.checkList.length == 0) {
        this.$message({
          message: '请先选择需要结算的人！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      const loading = this.$loading({
        lock: true,
        text: '处理中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      let regNos = this.checkList.map((item) => item.regNo);
      console.log(this.dateVal);
      let datas = {
        companyCode: this.companyInfo.companyCode,
        companyTimes: this.companyInfo.companyTimes,
        type: this.type,
        invoiceName: '',
        regNos: regNos,
        chargeDateScope: `${moment(this.dateVal[0]).format('YYYY.MM.DD')}-${moment(this.dateVal[1]).format('YYYY.MM.DD')}`
      };
      console.log(datas);
      this.$ajax
        .post(this.$apiUrls.CreateCompanySettlement, datas, { timeout: 0 })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.balanceRecordShow = false;
          this.GetCompanySettlement();
        })
        .finally(() => {
          loading.close();
        });
    },
    // 删除结算记录
    delBalanceRecord(row) {
      this.$confirm(`是否删除${row.billSeqNo}结算记录?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax
            .paramsPost(this.$apiUrls.DeleteCompanySettlement, {
              seqNo: row.billSeqNo
            })
            .then((r) => {
              let { success, returnData } = r.data;
              if (!success) return;
              this.$message({
                type: 'success',
                message: '删除成功!',
                showClose: true
              });
              this.GetCompanySettlement();
            });
        })
        .catch(() => {});
    },
    // 审核
    auditBalanceRecord(row) {
      let url, tipsText;
      if (row.auditOperator) {
        url = this.$apiUrls.CancelAuditCompanySettlement;
        tipsText = '取消审核';
      } else {
        url = this.$apiUrls.AuditCompanySettlement;
        tipsText = '审核';
      }
      this.$confirm(`是否${tipsText}${row.billSeqNo}结算记录?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const loading = this.$loading({
            lock: true,
            text: '处理中...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          this.$ajax
            .paramsPost(url, { seqNo: row.billSeqNo }, { timeout: 0 })
            .then((r) => {
              let { success, returnData } = r.data;
              if (!success) return;
              this.$message({
                type: 'success',
                message: `${tipsText}成功!`,
                showClose: true
              });
              this.GetCompanySettlement();
            })
            .finally(() => {
              loading.close();
            });
        })
        .catch(() => {});
    },
    // 退费
    refund(row) {
      this.$confirm(`是否确定退款?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax
            .paramsPost(this.$apiUrls.RefundCompanySettlement, {
              seqNo: row.billSeqNo
            })
            .then((r) => {
              let { success, returnData } = r.data;
              if (!success) return;
              this.$message({
                type: 'success',
                message: '申请退款成功!',
                showClose: true
              });
              this.GetCompanySettlement();
            });
        })
        .catch(() => {});
    },
    // 撤销退费
    cancelRefund(row) {
      this.$confirm(`是否确定撤销退款?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax
            .paramsPost(this.$apiUrls.CancelRefundCompanySettlement, {
              seqNo: row.billSeqNo
            })
            .then((r) => {
              let { success, returnData } = r.data;
              if (!success) return;
              this.$message({
                type: 'success',
                message: '撤销退款成功!',
                showClose: true
              });
              this.GetCompanySettlement();
            });
        })
        .catch(() => {});
    },
    // 查看费用明细
    feeDetailBtn(row) {
      this.$ajax
        .paramsPost(this.$apiUrls.QueryCompanySettlementDetailByPerson, {
          seqNo: row.billSeqNo
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.feeDetailInfo = returnData || {};
          this.feeDetailList = returnData.list;
          this.feeDetailShow = true;
        });
    },
    // 修改发票抬头
    cellClick(row, column, cell, event) {
      console.log(row, column, cell, event);
      this.checkRow = row;
      if (
        column.property == 'invoiceName' ||
        column.property == 'taxID' ||
        column.property == 'electronicInvoiceNo'
      ) {
        this.modifyTitle = this.theads[column.property];
        this.modifyShow = true;
        this.$nextTick(() => {
          this.$refs['modify_Ref'].resetFields();
          this.$refs.modifyRef_inp.focus();
          this.form.invoiceName = this.checkRow[column.property];
        });
      }
    },
    // 修改 发票抬头/社会信用代码 的保存
    confirmModify() {
      this.$refs['modify_Ref'].validate((valid) => {
        if (valid) {
          let invoiceName = '',
            electronicInvoiceNo = '',
            taxID = '';
          if (this.modifyTitle == '发票名称') {
            (invoiceName = this.form.invoiceName), (taxID = '');
            electronicInvoiceNo = '';
          } else if (this.modifyTitle == '社会信用代码') {
            (invoiceName = ''), (taxID = this.form.invoiceName);
            electronicInvoiceNo = '';
          } else {
            (invoiceName = ''), (taxID = '');
            electronicInvoiceNo = this.form.invoiceName;
          }
          let datas = {
            seqNo: this.checkRow.billSeqNo,
            invoiceName,
            taxID,
            electronicInvoiceNo
          };
          console.log(datas);
          this.$ajax
            .post(this.$apiUrls.UpdateCompanySettlement, datas)
            .then((r) => {
              let { success, returnData } = r.data;
              if (!success) return;
              this.$message({
                type: 'success',
                message: '更新成功!',
                showClose: true
              });
              this.modifyShow = false;
              this.GetCompanySettlement();
            });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    tableCellClassName({ row, column, rowIndex, columnIndex }) {
      if (column.property == 'invoiceName') {
        return {
          cursor: 'pointer'
        };
      }
    },
    // 表格行的样式回调
    tableRowClassName({ row }) {
      const checkListRegNos = new Set(this.checkList.map((item) => item.regNo));
      if (checkListRegNos.has(row.regNo)) {
        return 'search_class';
      }
    },
    //导出
    exportTable() {
      if (this.feeDetailList.length == 0) {
        this.$message({
          message: '费用明细列表不能为空！',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm(
        `确定导出${this.feeDetailList[0].billSeqNo}费用明细?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        let columns = [];
        Object.keys(this.feeDetailTheads).map((item) => {
          columns.push({
            title: this.feeDetailTheads[item],
            key: item
          });
        });
        let data = dataUtils.deepCopy(this.feeDetailList);
        let pushJson = {
          billSeqNo: '',
          companyCode: '',
          companyName: '',
          regNo: '',
          name: '',
          cardNo: '',
          age: '',
          sex: '',
          tel: '',
          clusterName: `数量：${this.feeDetailInfo.count}`,
          price: `总金额：${this.feeDetailInfo.price} 元`
        };
        data.push(pushJson);
        const title = `${this.feeDetailList[0].billSeqNo}费用明细`;
        let thead = {
          billSeqNo: '结算批次号',
          companyCode: '单位代码',
          companyName: '单位名称',
          cardNo: '身份证',
          name: '姓名',
          sex: '性别',
          tel: '电话',
          clusterName: '套餐名',
          price: '金额'
        };
        this.$nextTick(() => {
          // export2Excel(columns, data, title);
          this.exportExcel(title, '费用明细', thead, data, '');
          this.feeDetailShow = false;
        });
      });
    },
    // 打印结算记录
    printBtn(row) {
      // let datas = {
      //     queryString:`seqNo=${row.billSeqNo}`,
      //     reportCode:"b3b3589831154f7eba4367d634258e03"
      // };
      // this.$ajax.post(`${this.$config.pdfFileUrl}Home/ExportToPdf`,datas, {
      //     responseType: "arraybuffer",
      // }).then(r=>{
      //
      // })
      this.dataInfo = row;
      this.previewShow = true;
    },
    //键盘监听
    keyUp(e) {
      if (e.keyCode == 16) {
        e.preventDefault();
        e.stopPropagation();
        this.isShift = false;
      }
    },
    keyDown(e) {
      if (e.keyCode == 16) {
        e.preventDefault();
        e.stopPropagation();
        this.isShift = true;
      }
    }
  },
  mounted() {
    this.GetCompanySettlement();
    this.getDepartList();
    this.companyTimesChange();
    window.addEventListener('keyup', this.keyUp);
    window.addEventListener('keydown', this.keyDown);
  },
  activated() {
    window.addEventListener('keyup', this.keyUp);
    window.addEventListener('keydown', this.keyDown);
  },
  beforeDestroy() {
    window.removeEventListener('keyup', this.keyUp);
    window.removeEventListener('keydown', this.keyDown);
  },
  deactivated() {
    window.removeEventListener('keyup', this.keyUp);
    window.removeEventListener('keydown', this.keyDown);
  }
};
</script>
<style lang="less" scoped>
.search-item {
  display: flex;
  align-items: center;
  margin-right: 10px;
  &:last-child {
    margin-right: 0;
  }
  span {
    // width: 40px;
    min-width: fit-content;
    font-weight: 600;
    font-size: 14px;
    margin-right: 5px;
    min-width: fit-content;
  }
}
.balanceRecord_page {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
  header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
  }
  .table_wrap {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
  }
  /deep/.dialog_content {
    height: 500px;
    display: flex;
    flex-direction: column;
    overflow: auto;
    .dialog_search {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-bottom: 5px;
    }
    .dialog_table {
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
      user-select: none;
    }
    .feeDetail_bottom {
      display: flex;
      justify-content: flex-end;
      li {
        font-size: 20px;
        & + li {
          margin-left: 20px;
        }
        i {
          font-style: normal;
          color: #d63031;
        }
      }
    }
  }
  .btn_wrap {
    .el-button + .el-button {
      margin-left: 3px;
    }
  }
  /deep/.search_class {
    td {
      background: #6cbcf5c4 !important;
    }
  }
  .search_result {
    display: flex;
    align-items: center;
  }
  .status-wrap {
    // flex: 1;
    font-weight: 600;
    font-size: 14px;
    margin-right: 20px;
    span {
      margin-right: 30px;
      &:last-child {
        margin-right: 0;
      }
    }
  }
  /deep/.el-dialog__body {
    padding: 10px 20px;
  }
}
</style>
