<template>
  <div class="personInfo">
    <div class="hearder">
      <h4>人员列表</h4>
      <div class="hearder-operation">
        <div style="margin-right: 10px">
          <span style="font-size: 14px; margin-right: 10px">部门</span>
          <el-select
            class="select"
            v-model.trim="companyDeptCode"
            placeholder="请选择"
            size="small"
            filterable
            clearable
            @change="searchClick"
          >
            <el-option
              v-for="(item, index) in companyDeptList"
              :key="index"
              :label="item.deptName"
              :value="item.deptCode"
            ></el-option>
          </el-select>
        </div>
        <div>
          <label>状态</label>
          <el-select
            v-model.trim="type"
            placeholder="请选择"
            size="small"
            class="operation-select"
            @change="searchClick"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <el-input
          v-model.trim="input"
          placeholder="姓名/证件号"
          size="small"
          clearable
          class="operation-input"
          @keyup.enter.native="searchClick"
        ></el-input>
        <el-button
          size="small"
          class="blue_btn"
          @click="searchClick"
          icon="iconfont icon-search"
          >搜索</el-button
        >
        <el-button
          v-show="type !== 1"
          size="small"
          class="yellow_btn"
          @click="allocationClick"
          icon="iconfont icon-fenpei"
          >分配套餐</el-button
        >
        <el-button
          v-show="type === 1"
          size="small"
          class="red_btn"
          @click="unAllocationClick"
          icon="iconfont icon-fenpei"
          >取消分配</el-button
        >
        <el-button
          class="violet_btn"
          size="small"
          @click="importClick"
          icon="iconfont icon-daoru"
          >导入文件</el-button
        >
        <el-button
          class="red_btn"
          size="small"
          @click="deletesClick"
          icon="iconfont icon-shanchu"
          >删除</el-button
        >
      </div>
    </div>
    <div class="personInfo-table">
      <PublicTable
        isCheck
        :theads="theads"
        :viewTableList.sync="viewTableList"
        :tableLoading.sync="tableLoading"
        :columnWidth="columnWidth"
        @selectionChange="selectionChange"
      >
        <template #allotStatus="{ scope }">
          <span
            :class="
              scope.row.allotStatus === '未分配' ? 'cell_yellow' : 'cell_green'
            "
          >
            {{ scope.row.allotStatus }}
          </span>
        </template>
        <template #peStatus="{ scope }">
          <div>
            {{ G_EnumList['PeStatus'][scope.row.peStatus] }}
          </div>
        </template>
        <template #jobStatus="{ scope }">
          <div>
            {{
              G_EnumList['CodeOccupationalPositionStatus'][scope.row.jobStatus]
            }}
          </div>
        </template>
      </PublicTable>
    </div>
    <el-drawer
      title=""
      :visible.sync="importDrawer"
      :before-close="cancel"
      :wrapperClosable="false"
      size="60%"
      class="import-drawer"
      destroy-on-close
    >
      <div slot="title">
        导入文件
        <el-button
          size="small"
          class="green_btn btn"
          icon="el-icon-download"
          @click="importTemplate"
          >下载模板</el-button
        >
      </div>
      <ImportDrawer
        ref="importDrawer_ref"
        :companyTimes="companyInfo.companyTimes"
        :companyCode="companyInfo.companyCode"
      ></ImportDrawer>
      <a href="./普通团体导入模板.xlsx" download="普通团体导入模板.xlsx"></a>
    </el-drawer>
    <el-drawer
      title="分配套餐"
      :visible.sync="allocationDrawer"
      :before-close="cancel"
      :wrapperClosable="false"
      size="80%"
      class="import-drawer"
    >
      <AllocationCluster ref="allocationCluster_ref"></AllocationCluster>
    </el-drawer>
  </div>
</template>

<script>
import PublicTable from '@/components/publicTable';
import ImportDrawer from '../drawer/importDrawer.vue';
import AllocationCluster from '../drawer/allocationCluster.vue';
import { mapGetters } from 'vuex';
export default {
  name: 'personInfo',
  components: {
    PublicTable,
    ImportDrawer,
    AllocationCluster
  },
  props: {
    companyInfo: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      type: -1,
      input: '',
      companyDeptCode: '',
      companyDeptList: [],
      options: [
        {
          label: '全部',
          value: -1
        },
        {
          label: '未分配',
          value: 0
        },
        {
          label: '已分配',
          value: 1
        }
      ],
      theads: {
        regNo: '体检号',
        name: '姓名',
        cardType: '证件类型',
        cardNo: '证件号码',
        age: '年龄',
        sex: '性别',
        tel: '电话号码',
        marryStatus: '婚姻状态',
        deptName: '单位部门',
        peStatus: '体检状态',
        allotStatus: '分配状态',
        jobStatus: '岗位状态',
        hazards: '危害因素',
        clusName: '套餐名',
        note: '备注'
        // isMain: ""
      },
      columnWidth: {
        regNo: 130,
        cardType: 100,
        cardNo: 200,
        clusName: 220,
        peStatus: 80,
        tel: 110,
        age: 50,
        sex: 50,
        cardNo: 165
      },
      importDrawer: false,
      tableLoading: false,
      selectionList: [],
      allocationDrawer: false,
      cell_green: [],
      cell_yellow: [],
      viewTableList: [],
      fileurl: './static/普通团体导入模板.xlsx'
    };
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_config'])
  },
  created() {
    this.searchClick();
    this.getDepartList();
    if (!this.G_config.physicalMode.includes('职检')) {
      delete this.theads.hazards;
      delete this.theads.jobStatus;
    }
  },
  methods: {
    // 获取或搜索
    searchClick() {
      let data = {
        companyCode: this.companyInfo.companyCode,
        companyTimes: this.companyInfo.companyTimes,
        allotType: this.type,
        sex: -1,
        minAge: -1,
        maxAge: -1,
        marryStatus: -1,
        queryValue: this.input ? this.input : '',
        companyDeptCode: this.companyDeptCode
      };
      console.log('data: ', data);
      this.$ajax.post(this.$apiUrls.GetCompanyRegList, data).then((r) => {
        console.log('GetCompanyRegList : ', r);
        let { success, returnData } = r.data;
        if (!success) return;
        this.tableLoading = false;
        returnData.map((item) => {
          item.cardType = this.G_EnumList['CardType'][item.cardType];
          item.sex = this.G_EnumList['Sex'][item.sex];
          item.marryStatus = this.G_EnumList['MarryStatus'][item.marryStatus];
        });
        this.viewTableList = returnData || [];
      });
    },
    // 分配套餐
    allocationClick() {
      this.allocationDrawer = true;
      this.$nextTick(() => {
        this.$refs.allocationCluster_ref.getOrSearchClick();
        this.$refs.allocationCluster_ref.getCompanyCluster();
        this.$refs.allocationCluster_ref.reset();
      });
    },
    // 取消分配套餐
    unAllocationClick() {
      if (this.selectionList.length === 0) {
        this.$message({
          message: '请至少选择一个数据取消分配套餐!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm(`是否确定取消分配套餐?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let data = {
          regNos: this.selectionList,
          clusterCode: '',
          companyCode: '',
          companyTimes: 0
        };
        this.$ajax
          .post(this.$apiUrls.CancelSetCompanyCluster, data)
          .then((r) => {
            let { success } = r.data;
            if (!success) return;
            this.$message({
              message: '取消分配套餐成功!',
              type: 'success',
              showClose: true
            });
            this.searchClick();
          });
      });
    },
    // 导入文件
    importClick() {
      this.importDrawer = true;
      this.$nextTick(() => {
        this.$refs.importDrawer_ref.reset();
      });
    },
    // 表格选择
    selectionChange(row) {
      console.log('row: ', row);
      this.selectionList = [];
      row.map((item) => {
        if (item.regNo != '') {
          this.selectionList.push(item.regNo);
        }
      });
      console.log('this.selectionList: ', this.selectionList);
    },
    // 删除
    deletesClick() {
      if (this.selectionList.length === 0) {
        this.$message({
          message: '请至少选择一条信息删除!',
          type: 'warning',
          showClose: true
        });
        return;
      }
      this.$confirm(`是否确定删除数据?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$ajax
          .post(this.$apiUrls.DelCompanyRegList, this.selectionList)
          .then((r) => {
            let { success } = r.data;
            if (!success) return;
            this.$message({
              message: '删除成功',
              type: 'success',
              showClose: true
            });
            this.searchClick();
          });
      });
    },
    // 关闭抽屉
    cancel() {
      this.importDrawer = false;
      this.allocationDrawer = false;
    },
    //下载导入模板
    importTemplate() {
      let a = document.createElement('a');
      let fileName = this.G_config.physicalMode.includes('职检')
        ? '职检团体导入模板.xlsx'
        : '普通团体导入模板.xlsx';
      a.href = `./${fileName}`;
      a.download = fileName;
      a.style.display = 'none';
      document.body.appendChild(a);
      a.click();
      a.remove();
    },
    //查询公司部门信息
    getDepartList() {
      this.$ajax
        .post(this.$apiUrls.R_CodeCompanyDepartment, {
          companyCode: this.companyInfo.companyCode,
          deptCode: '',
          deptName: ''
        })
        .then((r) => {
          //console.log("r", r);
          let { success, returnData } = r.data;
          if (!success) return;
          this.companyDeptList = returnData || [];
        });
    }
  }
};
</script>

<style lang="less" scoped>
.personInfo {
  display: flex;
  flex-direction: column;
  height: 100%;
  .hearder {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 18px;
  }
  .hearder-operation {
    display: flex;
    align-items: center;
    label {
      font-size: 14px;
      margin-right: 8px;
    }
  }
  .operation-select {
    width: 90px;
  }
  .operation-input {
    width: 150px;
    margin-left: 8px;
  }
  .personInfo-table {
    flex: 1;
    flex-shrink: 0;
    height: calc(100% - 51px);
  }
  .el-button--small {
    padding: 7px 9px;
    margin-left: 8px;
  }
  .import-drawer/deep/.el-drawer__body {
    background: #f0f2f3;
    margin: 0 !important;
    padding: 0 !important;
  }
  .import-drawer/deep/.el-drawer__header {
    background: #d1e2f9;
    margin-bottom: 0;
    padding: 18px;
  }
  .cell_yellow {
    color: #fab63b;
  }
  .cell_green {
    color: #3cb34f;
  }
}
</style>
