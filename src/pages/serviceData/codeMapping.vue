<template>
  <div class="codeMapping" id="BasedOnTheCode">
    <div class="left" id="LeftWrap">
      <p>代码对应维护</p>
      <el-menu
        ref="elMenu_Ref"
        :default-active="muenId"
        :defaultOpeneds="defaultOpened"
        class="el-menu-vertical-demo"
        @open="handleOpen"
        @close="handleClose"
        @select="handleSelect"
        style="height: calc(100% - 50px); overflow: auto"
      >
        <el-submenu :index="item.id" v-for="item in menuList" :key="item.id">
          <template slot="title">
            <i class="iconfont icon-leixing1 icon"></i>
            <span>{{ item.title }}</span>
          </template>

          <template v-for="child in item.children">
            <el-submenu
              v-if="child.children.length > 0"
              :index="child.id"
              :key="child.id"
            >
              <template slot="title">
                <i
                  :class="
                    openedMenus.includes(child.id)
                      ? 'el-icon-folder-opened'
                      : 'el-icon-folder'
                  "
                ></i>
                <span>{{ child.title }}</span>
              </template>
              <el-menu-item
                :index="grandson.id"
                v-for="grandson in child.children"
                :key="grandson.id"
                @click="goTo(grandson)"
                ><template slot="title">
                  <i class="el-icon-document"></i>
                  <span>{{ grandson.title }}</span>
                </template></el-menu-item
              >
            </el-submenu>
          </template>

          <template v-for="child in item.children">
            <el-menu-item
              v-if="child.children.length == 0"
              :key="child.id"
              :index="child.id"
              @click="goTo(child)"
            >
              <template slot="title">
                <i class="iconfont icon-biaodan icons"></i>
                <span>{{ child.title }}</span>
              </template>
            </el-menu-item>
          </template>
        </el-submenu>
      </el-menu>
    </div>
    <div class="right">
      <allBtn
        ref="btnDoing"
        :methodSearch="search"
        :methodSetup="setUp"
        :methodDelete="deletes"
        :methodPrint="prints"
        :methodExport="exports"
        :btnList="['查询', '设置', '删除', '打印', '导出']"
      ></allBtn>

      <component
        class="table_com"
        :is="viewConfig.tableComponent"
        :mergeKeys="viewConfig.mergeKeys"
        :theads="viewConfig.theads"
        isCheck
        :viewConfig="viewConfig"
        :viewTableList.sync="searchTableList"
        @rowDblclick="setUp"
        :tableLoading.sync="tableLoading"
        ref="view_Ref"
      >
      </component>

      <!-- 打印的表格 -->
      <div class="print_table">
        <component
          id="printHtml"
          class="table_com"
          :is="viewConfig.tableComponent"
          :mergeKeys="viewConfig.mergeKeys"
          :theads="viewConfig.theads"
          :viewConfig="viewConfig"
          :viewTableList.sync="checkTableList"
          :isOpenPage="false"
          :tableLoading.sync="tableLoading"
        >
        </component>
      </div>
    </div>
    <el-drawer
      :title="titleList[0]"
      :visible.sync="drawer"
      :before-close="drawerClose"
      :wrapperClosable="false"
      size="72%"
    >
      <DrawerInfo
        ref="drawers"
        :drawerConfig="drawerConfig"
        :theads="viewConfig.theads"
        :viewConfig="viewConfig"
        :drawerTableList="drawerTableList"
        :drawerTableList1.sync="searchTableList1"
        :drawerTableList2.sync="searchTableList2"
        :drawerOptionList="drawerOptionList"
        :viewTableList.sync="searchTableList3"
        :methodSearch1="getDrawerFilterTable1"
        :methodSearch2="getDrawerFilterTable2"
        :methodSearch3="getDrawerFilterTable3"
        :tableLoading.sync="tableLoading"
      ></DrawerInfo>
    </el-drawer>
  </div>
</template>

<script>
import AllBtn from './allBtn.vue';
import MergeTable from './codeMapping/tableCom/mergeTable';
import TotalTable from './codeMapping/tableCom/totalTable';
import DrawerInfo from './codeMapping/drawer/drawerInfo.vue';
import MenuList from './codeMapping/mixins/menuList';
import Drawer from './codeMapping/mixins/drawer';
import TreeTable from './codeMapping/tableCom/treeTable';
import printJS from '@/common/printJS';

export default {
  name: 'codeMapping',
  components: {
    AllBtn,
    MergeTable,
    TotalTable,
    DrawerInfo,
    TreeTable
  },
  mixins: [MenuList, Drawer, LeftDrag],
  data() {
    return {
      //文字描述
      titleList: [],
      muenId: '1-1',
      defaultOpened: ['1'],
      openedMenus: [],
      viewConfig: {}, //页面配置
      viewTableList: [],
      searchIpt: '', //查询值
      searchSel: '',
      searchTableList: [],
      checkTableList: [], //选中的数据
      tableLoading: false
    };
  },
  created() {
    let menuData = this.menuList[0].children[0];
    this.viewConfig = menuData.viewConfig;
    this.drawerConfig = menuData.drawerConfig;
    this.getViewTableList();
  },

  methods: {
    //查询
    search() {
      console.log('测试search');
      this.searchIpt = this.$refs.btnDoing.searchInfo; //获取组件文本框的值
      this.tableLoading = true;
      console.log(this.searchIpt);
      if (!this.searchIpt) {
        this.$nextTick(() => {
          this.tableLoading = false;
        });

        return (this.searchTableList = this.viewTableList);
      }
      let newTableData = [];
      this.viewTableList.map((item) => {
        let flag = new Function(
          'item',
          'searchVal',
          `return ${this.viewConfig.order}`
        )(item, this.searchIpt.trim());
        if (flag) {
          newTableData.push(item);
        }
      });
      this.searchTableList = newTableData;
      this.$nextTick(() => {
        this.tableLoading = false;
      });
    },
    //设置
    setUp() {
      console.log('测试setUp');
      this.drawer = true;
      this.titleList = this.drawerConfig.titleList;
      this.getDrawerTableList1(); //获取左边表格数据
      this.getDrawerTableList2(); //获取右边表格数据
      this.getDrawerTableList3(); //获取下边表格数据
      this.optionList(); //获取项目分类下拉
      console.log('this.drawerTableList2', this.drawerTableList2);
    },
    //删除
    deletes() {
      console.log(this.$refs.view_Ref.$refs.tableCom_Ref);
      let checkboxRecords = this.$refs.view_Ref.$refs.tableCom_Ref.selection;
      console.log(checkboxRecords);
      if (checkboxRecords.length === 0) {
        this.$message({
          message: '请选择需要删除的数据',
          type: 'warning',
          duration: this.$config.messageTime,
          showClose: true
        });
        return;
      }
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$ajax
            .post(this.viewConfig.apiUrls.del, checkboxRecords)
            .then((r) => {
              let { success } = r.data;
              if (!success) return;
              this.$message({
                showClose: true,
                message: '删除成功',
                type: 'success'
              });
              this.getViewTableList();
            });
        })
        .catch(() => {});
    },
    //打印
    prints() {
      let tableCom_Ref = this.$refs.view_Ref.$refs.tableCom_Ref;
      console.log('tableCom_Ref: ', tableCom_Ref);
      if (tableCom_Ref.selection.length == 0) {
        return this.$message({
          message: '请选择至少一条数据进行操作！',
          type: 'warning',
          showClose: true
        });
      }
      let viewTableList_prop = this.$refs.view_Ref._props.viewTableList;
      console.log(tableCom_Ref);
      if (tableCom_Ref.store.states.isAllSelected) {
        this.checkTableList = viewTableList_prop;
      } else {
        this.checkTableList = tableCom_Ref.selection;
      }
      setTimeout(() => {
        printJS('printHtml');
      }, 500);
    },
    //导出
    exports() {
      let tableCom_Ref = this.$refs.view_Ref.$refs.tableCom_Ref;
      if (tableCom_Ref.selection.length == 0) {
        return this.$message({
          message: '请选择至少一条数据进行操作！',
          type: 'warning',
          showClose: true
        });
      }
      this.$confirm('确定下载列表文件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.excelData = tableCom_Ref.selection; // multipleSelection是一个数组，存储表格中选择的行的数据。
          this.excelData.map((item, i) => {
            item.index = i + 1;
          });
          this.$nextTick(function () {
            this.export2Excel();
          });
        })
        .catch(() => {});
    },
    // 数据写入excel
    export2Excel() {
      var that = this;
      require.ensure([], () => {
        const { export_json_to_excel } = require('@/utils/Export2Excel'); // 这里必须使用绝对路径，使用@/+存放export2Excel的路径
        const tHeader = Object.values(this.viewConfig.theads); // 导出的表头名信息
        const filterVal = Object.keys(this.viewConfig.theads); // 导出的表头字段名
        const list = that.excelData;
        const data = that.formatJson(filterVal, list);
        console.log(data);
        const name = that.excelName;
        export_json_to_excel(tHeader, data, name);
      });
    },
    // 格式转换
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) => filterVal.map((j) => v[j]));
    },
    handleOpen(key, keyPath) {
      this.openedMenus = this.$refs.elMenu_Ref.$data.openedMenus;
      console.log(this.openedMenus);
      // console.log(key, keyPath);
    },
    handleClose(key, keyPath) {
      this.openedMenus = this.$refs.elMenu_Ref.$data.openedMenus;
      console.log(this.openedMenus);
      // console.log(key, keyPath);
    },
    handleSelect(index, indexPath) {
      console.log(index);
      console.log(indexPath);
    },
    goTo(data) {
      console.log(data);
      this.viewConfig = data.viewConfig;
      this.drawerConfig = data.drawerConfig;
      this.getViewTableList();
      console.log(this.drawerConfig);
    },
    // 获取视图的表格数据
    getViewTableList() {
      this.tableLoading = true;
      this.$ajax.post(this.viewConfig.apiUrls.viewTableList).then((r) => {
        this.tableLoading = false;
        let { returnData, success } = r.data;
        if (!success) return;
        returnData.map((item) => {
          if (item.fitemClsType) {
            item.fitemClsType =
              item.fitemClsType === 0 ? '发票分类' : '财务分类';
          }
        });
        this.viewTableList = returnData || [];
        this.searchTableList = returnData || [];
        console.log('returnData: ', returnData);
      });
    },
    // 主页表格的双击事件
    viewTableDBL(row) {
      console.log(row);
    }
  }
};
</script>
<style lang="less" scoped>
.codeMapping {
  display: flex;

  .left {
    width: 360px;
    background: white;

    p {
      font-size: 16px;
      color: #2d3436;
      line-height: 48px;
      margin-left: 10px;
    }

    img {
      width: 16px;
    }

    .icon {
      margin-right: 8px;
      font-size: 18px;
    }
    .icons {
      font-size: 16px;
      margin-right: 8px;
    }
    /deep/.el-submenu__title {
      font-size: 16px;
      height: 30px;
      line-height: 30px;
      // background-color: #eee;
      // border-bottom: 1px solid #ccc;
    }

    .el-submenu .el-menu-item {
      height: 30px;
      line-height: 30px;
    }

    .el-submenu .el-menu-item:focus,
    // .el-menu-item:hover,
    .el-menu-item.is-active {
      outline: 0;
      color: #fff;
      background-color: #1770df;
      border-radius: 4px;
      border-radius: 4px;
    }
  }

  .right {
    display: flex;
    flex: 1;
    flex-shrink: 0;
    // width: calc(100% - 360px);
    flex-direction: column;
    background: white;
    margin-left: 20px;
    overflow: auto;
    padding: 15px;
    /deep/.el-container.is-vertical {
      padding-left: 20px;
    }
    header {
      display: flex;
      // justify-content: space-between;
      justify-content: flex-start;
      line-height: 60px;
      .el-input {
        width: 293px;
        margin-right: 20px;
      }
      /deep/.el-input--small .el-input__inner {
        height: 36px;
        line-height: 36px;
      }
    }

    .table_com {
      flex: 1;
      overflow: auto;
    }

    .el-header,
    .el-main {
      padding: 0;
    }
  }
  /deep/.el-drawer__header {
    font-family: PingFangSC-Medium;
    font-size: 18px;
    color: #2d3436;
    margin-bottom: 28px;
  }
  /deep/.el-drawer .el-drawer__body {
    background-color: #f3f3f5;
    padding: 0 10px 10px 10px;
    border-radius: 4px;
    margin: 0 18px 18px 18px;
  }
  /deep/.el-drawer__body {
    display: flex;
    flex-direction: column;
    padding-bottom: 20px;
    .el-input--small .el-input__inner {
      height: 36px;
      line-height: 36px;
    }
    .upperHalf,
    .lowerHalf {
      height: 50%;
      width: 100%;
      .search-input {
        margin-right: 10px;
        display: flex;
        width: auto;
      }
      // /deep/.el-select {
      //   padding-right: 10px;
      //   width: 130px;
      // }
      /deep/.el-input__inner {
        height: 36px;
      }
    }
    .upperHalf {
      display: flex;
      justify-content: flex-end;

      .leftCont {
        width: 50%;
        margin-right: 20px;
        display: flex;
        flex-direction: column;
        .tableCont {
          height: calc(100% - 50px);
          background: #fff;
          flex: 1;
        }
        header {
          height: 50px !important;
          display: flex;
          justify-content: space-between;
          line-height: 50px;
          margin-bottom: 10px;
          h4 {
            font-size: 16px;
            color: #2d3436;
          }
        }
        .searchWrap {
          display: flex;
          align-items: center;
        }
      }
      .rightCont,
      .rightCont1 {
        width: 50%;
        display: flex;
        flex-direction: column;
        header {
          display: flex;
          height: 50px !important;
          display: flex;
          justify-content: space-between;
          line-height: 50px;
          margin-bottom: 10px;
          h4 {
            font-size: 16px;
            color: #2d3436;
          }
        }
        .searchWrap {
          display: flex;
          align-items: center;
        }
        .tableCont {
          height: calc(100% - 60px);
          background: #fff;
          flex: 1;
        }
      }
      .rightCont1 {
        flex-direction: row;
        .rightTable1 {
          margin-right: 18px;
        }
        .rightTable1,
        .rightTable2 {
          flex: 1;
          flex-shrink: 0;
          display: flex;
          flex-direction: column;
        }
      }
      .rightCont2 {
        flex: 1;
        display: flex;

        .symbol-box {
          margin-right: 20px;
          margin-top: 90px;
          margin-bottom: 30px;
          text-align: center;
          display: flex;
          flex-direction: column;
          justify-content: space-around;
        }
        .check {
          width: 172px;
          height: 48px;
          line-height: 48px;
          background: #1770df;
          color: #fff;
          border-radius: 2px;
          margin-bottom: 28px;
          font-size: 24px;
          font-weight: normal;
        }
        .symbol {
          width: 174px;
          display: flex;
          flex-wrap: wrap;
          margin-bottom: 48px;
          div {
            width: 86px;
            height: 48px;
            line-height: 48px;
            background: #1770df;
            color: #fff;
            border-radius: 2px;
            margin-right: 1px;
            margin-bottom: 1px;
            font-size: 28px;
            font-weight: normal;
          }
        }
        .clear {
          width: 172px;
          height: 48px;
          line-height: 48px;
          background: #fff;
          color: #1770df;
          border: 1px solid #1770df;
          border-radius: 2px;
          font-size: 24px;
          font-weight: normal;
        }
        .check:hover,
        .symbol div:hover,
        .clear:hover {
          background: #66b1ff;
          border-color: #66b1ff;
          color: #fff;
        }
        .right-input {
          flex: 1;
          header {
            height: 50px;
            line-height: 50px;
            margin-bottom: 10px;
            flex: 1;
            display: flex;
            justify-content: space-between;
            .lable {
              font-size: 14px;
              // margin-right: 10px;
            }
          }
        }
        .textarea-box {
          height: calc(100% - 60px);
          .el-textarea,
          .el-textarea__inner {
            height: 100%;
          }
        }
      }
    }
    .lowerHalf {
      display: flex;
      justify-content: flex-end;
      padding-top: 20px;
      flex-direction: column;
      header {
        display: flex;
        height: 50px !important;
        display: flex;
        justify-content: space-between;
        line-height: 50px;
        margin-bottom: 10px;
        h4 {
          font-size: 16px;
          color: #2d3436;
        }
      }
      .searchWrap {
        display: flex;
        align-items: center;
        .search {
          margin-right: 10px;
        }
        .number-text {
          font-size: 14px;
          color: #2d3436;
          margin-right: 10px;
        }
        .number-input {
          width: 68px;
          margin-right: 10px;
          .el-input__inner {
            padding-left: 6px;
            padding-right: 30px;
          }
        }
        .discount-input {
          width: 168px;
          margin-right: 10px;
        }
      }
      .tableCont {
        height: calc(100% - 50px);
        background: #fff;
        flex: 1;
      }
    }
  }
  .print_table {
    display: none;
  }
}
</style>
<style lang="less">
// .basedOnTheCode {
//   .el-menu--inline {
//     background: #cde6fa;
//   }
// }
</style>
