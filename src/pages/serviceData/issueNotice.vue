<template>
  <div class="pages issueNotice_page">
    <div class="right_wrap">
      <h3>公告记录：</h3>
      <ul>
        <li v-for="item in noticeList" :key="item.noticeId">
          <div class="title_div">
            <span>{{ item.title }}</span>
            <i>{{ item.createTime }}</i>
          </div>
          <p class="contP">
            <span
              v-html="item.content"
              class="contSpan"
              @dblclick="moreCont(item)"
            >
            </span>
            <i class="iconfont icon-xiayige more" @click="moreCont(item)"></i>
          </p>
          <div class="receiver_div">
            接收科室：{{
              item.targetType === 0 ? '所有科室' : C_receiver(item.targetDepts)
            }}
          </div>
        </li>
      </ul>
    </div>
    <div class="left_wrap">
      <el-form
        label-position="top"
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="公告标题" prop="title">
          <el-input
            v-model="ruleForm.title"
            placeholder="请输入标题"
          ></el-input>
        </el-form-item>
        <el-form-item label="公告内容" prop="content">
          <el-input
            type="textarea"
            :autosize="{ minRows: 8, maxRows: 8 }"
            placeholder="请输入内容"
            v-model="ruleForm.content"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="接收科室">
          <el-radio-group v-model="ruleForm.targetType">
            <el-radio :label="0">所有科室</el-radio>
            <el-radio :label="1">部分科室</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="receiver" v-if="ruleForm.targetType != 0">
          <el-select
            multiple
            filterable
            clearable
            v-model="ruleForm.receiver"
            style="width: 100%"
            placeholder="请选择科室"
          >
            <!-- <el-option label="所有科室" value="0"></el-option> -->
            <el-option
              :label="item.label"
              :value="item.value"
              v-for="item in G_codeDepartment"
              :key="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item style="text-align: right">
          <el-button
            icon="iconfont icon-fasong"
            size="small"
            type="primary"
            class="blue_btn"
            @click="issueClick()"
            >发布</el-button
          >
          <!-- <el-button @click="resetForm('ruleForm')">重置</el-button> -->
        </el-form-item>
      </el-form>
    </div>
    <el-dialog :title="contTitle" :visible.sync="noticeShow" width="50%" center>
      <span v-html="content"></span>
      <span
        slot="footer"
        class="dialog-footer"
        style="text-align: right; display: block"
      >
        <el-button type="primary" @click="noticeShow = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  name: 'issueNotice',
  computed: {
    ...mapGetters(['G_codeDepartment', 'G_userInfo', 'G_EnumList']),
    C_receiver() {
      return function (arr) {
        let txt = '';
        arr.map((item, idx) => {
          let text = this.G_EnumList['CodeDepartment'][item];
          txt += idx === 0 ? text : '、' + text;
        });
        return txt;
      };
    }
  },
  data() {
    return {
      ruleForm: {
        title: '',
        content: '',
        receiver: [],
        targetType: 0
      },
      noticeList: [],
      rules: {
        title: [{ required: true, message: '请输入公告标题', trigger: 'blur' }],
        content: [
          { required: true, message: '请输入公告内容', trigger: 'blur' }
        ],
        receiver: [
          { required: true, message: '请选择接收科室', trigger: 'change' }
        ]
      },
      contTitle: '',
      noticeShow: false
    };
  },
  methods: {
    moreCont(cont) {
      this.noticeShow = true;
      this.contTitle = cont.title;
      this.content = cont.content;
      console.log('[ cont ]-139', cont);
    },
    // 获取公告列表
    getNoticeList() {
      let datas = {
        pageNumber: 1,
        pageSize: 50
      };
      this.$ajax.paramsPost(this.$apiUrls.ReadNotices, datas).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.noticeList = returnData || [];
      });
    },
    // 发布公告
    issueClick() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          let datas = {
            noticeId: 0,
            title: this.ruleForm.title,
            content: this.ruleForm.content,
            operCode: this.G_userInfo.codeOper.operatorCode,
            targetType: this.ruleForm.targetType,
            targetDepts:
              this.ruleForm.targetType === 0 ? [] : this.ruleForm.receiver
          };
          console.log(datas);
          this.$ajax.post(this.$apiUrls.CreateNotice, datas).then((r) => {
            let { success } = r.data;
            if (!success) return;
            this.$message({
              message: '发布成功！',
              type: 'success',
              showClose: true
            });
            this.getNoticeList();
            this.resetForm();
          });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    // 重置
    resetForm() {
      this.$refs['ruleForm'].resetFields();
    }
  },
  created() {
    this.getNoticeList();
  }
};
</script>

<style lang="less" scoped>
.issueNotice_page {
  display: flex;

  .left_wrap {
    width: 710px;
    overflow: auto;
    border-radius: 4px;
    background: #fff;
    margin-right: 10px;
    padding: 10px;
  }

  .right_wrap {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
    background: #fff;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    h3 {
      height: 60px;
      font-size: 18px;
      line-height: 59px;
      border-bottom: 1px solid #b2bec3;
      padding: 0 20px;
    }
    ul {
      flex: 1;
      flex-shrink: 0;
      overflow: auto;
      padding: 0 20px;
    }
    li {
      border-bottom: 1px solid #b2bec3;
      padding: 10px 0;
      .title_div {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        span {
          flex: 1;
          flex-shrink: 0;
          color: #1770df;
          font-size: 14px;
        }
        i {
          font-style: normal;
          font-size: 14px;
          color: rgba(45, 52, 54, 0.6);
        }
      }
      p {
        display: flex;
        margin-bottom: 8px;
        span {
          flex: 1;
          flex-shrink: 0;
          font-size: 14px;
          color: #2d3436;
        }
        i {
          margin-top: 10px;
        }
      }
      .receiver_div {
        font-size: 14px;
        color: rgba(45, 52, 54, 0.6);
      }
    }
  }
  .showTwoLine {
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; /* 这里是超出几行省略 */
    overflow: hidden;
  }
  /deep/.el-form-item__label {
    font-weight: 600;
  }
  .contSpan {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    cursor: pointer;
  }
  .more {
    width: 24px;
    cursor: pointer;
  }
  /deep/.el-dialog {
    min-height: 40%;
    max-height: 70%;
    display: flex;
    flex-direction: column;
  }
  /deep/.el-dialog__body {
    flex: 1;
    overflow: auto;
  }
}
</style>
