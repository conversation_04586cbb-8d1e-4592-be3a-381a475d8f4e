<template>
  <div id="app">
    <router-view class="pages" />
  </div>
</template>

<script>
export default {
  name: 'App'
};
</script>

<style lang="less">
@import url('./assets/icon/iconfont.css');
@import url('./assets/css/common.less');

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}
html,
body,
#app,
.pages {
  height: 100%;
  width: 100%;
  // position: relative;
  background: #f5f5f5;
}

ul,
li {
  list-style: none;
}

.elTabel {
  font-size: 14px !important;
}

.el-message--info,
.el-message--error,
.el-message--warning,
.el-message--success {
  // top: 45% !important;
  i {
    font-size: 16px;
  }
  p {
    font-size: 16px;
  }
}

.el-submenu .el-submenu__title > div,
.el-submenu .el-menu-item > div {
  overflow: hidden;
  text-overflow: ellipsis;
}
input,
textarea {
  &:focus {
    background-color: rgba(23, 112, 223, 0.1);
  }
}
// 按钮自定义的图标样式
.el-button .iconfont {
  margin-right: 6px;
}
// 滚动条样式
::-webkit-scrollbar {
  width: 9px;
  height: 8px;
  border-radius: 10px;
  background-color: #f5f5f5;
}
//滑块
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  border-style: dashed;
  border-color: transparent;
  border-width: 3px;
  background-color: #ccc;
  background-clip: padding-box;
}
::-webkit-scrollbar-thumb:hover {
  background: rgba(157, 165, 183, 0.7);
}
//外层轨道
::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: #fff;
}
//边角
::-webkit-scrollbar-corner {
  background-color: #f5f5f5;
}
// form 表单提示 样式
.el-form-item__error {
  padding-top: 0px !important;
}
// small button 样式
.el-button--small {
  font-size: 14px !important;
}
.my_mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 999;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  /deep/.el-progress__text {
    color: #fff;
  }
  .tips_p {
    color: #fff;
    font-size: 16px;
    margin-top: 10px;
  }
}

.register_popverClass {
  padding: 0 !important;
  height: calc(100vh - 220px) !important;
}
@media (min-width: 1501px) {
  .register_popverClass {
    display: none;
  }
}
//设置文本不可选
.no-select {
  user-select: none; /* 标准属性 */
  -webkit-user-select: none; /* 针对 WebKit 浏览器（如 Safari） */
  -moz-user-select: none; /* 针对 Firefox */
  -ms-user-select: none; /* 针对 IE 和 Edge */
}
</style>
