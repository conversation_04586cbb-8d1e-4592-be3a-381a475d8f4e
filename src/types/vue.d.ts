import Vue from 'vue';

declare module 'vue/types/vue' {
  interface Vue {
    $config: {
      branchCode: string;
      messageTime: number;
      [key: string]: any;
    };
    $router: {
      push: (path: string) => void;
      replace: (path: string) => void;
      [key: string]: any;
    };
    $message: (options: {
      message: string;
      type: 'success' | 'warning' | 'info' | 'error';
      duration?: number;
      showClose?: boolean;
      [key: string]: any;
    }) => void;
  }
}
