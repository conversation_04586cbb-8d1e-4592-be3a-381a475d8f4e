declare module '*.vue' {
  import Vue from 'vue';
  export default Vue;
}

// 声明全局变量和方法
declare module 'vue/types/vue' {
  interface Vue {
    $message: any;
    $confirm: any;
    $notify: any;
    $loading: any;
    $store: any;
    $router: any;
    $route: any;
    // 这些属性在main.ts中已经有更具体的类型定义
    // $ajax: any;
    // $apiUrls: any;
    // $config: Record<string, any>;
    // $bus: Vue;
  }
}

// 声明第三方模块
declare module 'element-ui';
declare module 'vxe-table';
declare module 'xe-utils';
declare module 'vue-cropper';
declare module 'vue-clipboard2';
declare module 'vue-print-nb-jeecg';
declare module 'vuedraggable';
declare module 'vue-pdf';
declare module 'js-cookie';
declare module 'moment';
declare module 'echarts';
declare module 'file-saver';
declare module 'exceljs';
declare module 'jszip';
declare module 'xlsx';
declare module 'tinymce';
declare module '@tinymce/tinymce-vue';
declare module 'monaco-editor';
declare module 'markdown-it';
declare module 'markdown-it-highlightjs';
declare module 'highlight.js';
declare module 'github-markdown-css';
declare module 'pretty-data';
declare module 'knockout';
declare module 'sm-crypto';
declare module 'el-select-v2';
declare module 'el-table-infinite-scroll';
declare module '@microsoft/fetch-event-source';
declare module '@devexpress/analytics-core';
declare module 'devexpress-reporting';
declare module 'devextreme';
