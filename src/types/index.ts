// 通用接口定义
export interface ApiResponse<T = any> {
  success: boolean;
  returnData: T;
  message?: string;
  code?: string | number;
}

// 用户相关接口
export interface UserInfo {
  id?: string;
  username?: string;
  realName?: string;
  avatar?: string;
  phone?: string;
  email?: string;
  roles?: string[];
  permissions?: string[];
  [key: string]: any;
}

// 疾病相关接口
export interface Disease {
  diseaseCode: string;
  diseaseName: string;
  diseaseType?: string;
  icd10?: string;
  [key: string]: any;
}

// 检查项目相关接口
export interface ExamItem {
  id: string;
  name: string;
  code?: string;
  type?: string;
  unit?: string;
  normalRange?: string;
  [key: string]: any;
}

// 表单相关接口
export interface FormField {
  field: string;
  label: string;
  value?: any;
  type?: string;
  options?: Array<{
    label: string;
    value: any;
  }>;
  required?: boolean;
  disabled?: boolean;
  placeholder?: string;
  [key: string]: any;
}

// 分页相关接口
export interface Pagination {
  current: number;
  pageSize: number;
  total: number;
}

export interface PaginationRequest {
  current: number;
  pageSize: number;
  [key: string]: any;
}

export interface PaginationResponse<T = any> {
  records: T[];
  total: number;
  pages: number;
  current: number;
  pageSize: number;
}

// 文件相关接口
export interface FileInfo {
  id?: string;
  name: string;
  url: string;
  size?: number;
  type?: string;
  createTime?: string;
  [key: string]: any;
}

// 医疗指导相关接口
export interface MedicalGuidance {
  specialistVisit?: string;
  regularReview?: string;
  [key: string]: any;
}

// Monaco Editor相关接口
export interface EditorOptions {
  value?: string;
  language?: string;
  theme?: string;
  readOnly?: boolean;
  automaticLayout?: boolean;
  minimap?: {
    enabled?: boolean;
  };
  [key: string]: any;
}
