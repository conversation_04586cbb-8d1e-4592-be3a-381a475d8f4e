import { ajax, storage } from '@/common';
import store from '@/store';
import { apiUrls } from '@/common/apiUrls';
let promise;
// 是否刷新token
let isRefreshToken = true;

/**
 * @author: justin
 * @description: 刷新token
 * @return {Promise}
 **/
export async function refreshTokenAsync() {
  if (promise) return promise;

  let userInfo = storage.session.get('userInfo');
  if (!userInfo || !userInfo.token || !userInfo.token.access_token)
    return Promise.resolve(false);

  promise = new Promise(async (resolve) => {
    await ajax
      .post(`${apiUrls.RefreshToken}/${userInfo.token.access_token}`)
      .then((res) => {
        const { success, returnData } = res.data;
        if (!success || !returnData) {
          resolve(false);
          return;
        }

        let userInfo = storage.session.get('userInfo');
        userInfo.token = returnData;
        storage.session.set('userInfo', userInfo);
        store.commit('M_userInfo', userInfo);
        resolve(true);
      })
      .catch(() => {
        resolve(false);
      });
  });

  promise.finally(() => {
    promise = null;
  });

  return promise;
}

/**
 * @author: justin
 * @description: 刷新token方法
 * @param {*} response 响应内容
 * @return {Promise}  刷新token成功返回Promise，刷新token失败显示登录内容
 **/
export async function refreshTokenRequest(response) {
  if (isRefreshToken) {
    // 刷新token
    isRefreshToken = false;
    const success = await refreshTokenAsync();
    isRefreshToken = true; //isRefreshToken is not defined
    // ReferenceError: isRefreshToken is not defined
    if (success) {
      // 刷新token成功，重新请求
      setTimeout(() => {
        retryCacheRequest.trigger();
      }, 0);
      return ajax.instance(response.config);
    } else {
      // 刷新token失败，显示登录内容
      store.commit('M_loginDialogShow', true);
      return await retryCacheRequest.listen(response);
    }
  } else {
    // 缓存等待刷新token的请求
    return await retryCacheRequest.listen(response);
  }
}

/**
 * @author: justin
 * @description: 因为token失效，缓存等待刷新token的请求
 * @return {*}
 **/
export const retryCacheRequest = {
  //维护失败请求的response
  requests: new Map(),

  //添加订阅者
  listen(response) {
    return new Promise((resolve) => {
      const config = response.config;
      if (
        this.requests
          .keys()
          .some(
            (item) =>
              item.url === config.url &&
              item.data === config.data &&
              item.params === config.params
          )
      )
        return;

      this.requests.set(config, () => {
        resolve(ajax.instance(config));
      });
    });
  },

  //发布消息
  trigger() {
    Promise.resolve().then(() => {
      this.requests.forEach((cb, key) => {
        cb();
      });
      this.requests.clear();
    });
  }
};
