/**
 * @Description: 版本更新检测
 */
import Vue from 'vue';
import axios from 'axios';
let notify = null;
let useCheck = true;

/**
 
 * @description: 检查版本更新
 * @return {*}
 **/
const checkVersion = () => {
  if (!useCheck || process.env.NODE_ENV === 'development') return false;

  let url = `./version.json?t=` + new Date().getTime();
  let isNew = false;
  axios.get(url).then((res) => {
    let version = res.data.version;
    console.warn(
      '系统版本检测',
      '最新版本:',
      version,
      '本地版本:',
      process.env.VUE_APP_VERSION
    );
    isNew = process.env.VUE_APP_VERSION != version;
    if (isNew) {
      upgradeNotice();
    }
  });

  return isNew;
};

/**
 * @author: justin
 * @description: 提示用户刷新
 * @param {String} msg 提示信息
 * @param {Number} seconds 倒计时 单位：秒，默认10秒
 * @return {*}
 **/
const upgradeNotice = (
  msg = '检测到有新的版本，请刷新页面！',
  seconds = 30 * 1000
) => {
  if (notify) return;

  notify = Vue.prototype.$notify({
    type: 'warning',
    title: '更新提示',
    message: `<div style='padding: 10px 0;'><p>${msg}</p></div> <div style='margin-left: 80px; width: 200px;'> <button type='button' class='el-button el-button--default el-button--mini' id='waitNotice'>稍后</button> <button type='button' class='el-button el-button--primary el-button--mini' id='refreshNow'>刷新</button> </div> `,
    dangerouslyUseHTMLString: true,
    duration: 0,
    showClose: true,
    onClose: (el) => {
      notify = null;
      switch (el.colseType) {
        case 'close':
        case 'waitNotice':
          useCheck = false;
          break;
      }
    }
  });

  notify.colseType = 'close';
  notify.$el.querySelector('#waitNotice').onclick = () => {
    notify.colseType = 'waitNotice';
    notify.close();
    setTimeout(() => {
      upgradeNotice(msg, seconds);
    }, seconds);
  };

  notify.$el.querySelector('#refreshNow').onclick = () => {
    notify.colseType = 'refreshNow';
    notify.close();
    window.location.reload(true);
  };

  useCheck = false;
};

/**
 * @author: justin
 * @description: 关闭提示
 * @return {*}
 **/
const closeNotify = () => {
  if (notify) {
    notify.close();
  }
};

export default {
  checkVersion,
  upgradeNotice,
  closeNotify
};
