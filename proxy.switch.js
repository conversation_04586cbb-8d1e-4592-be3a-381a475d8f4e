const { readFileSync } = require('fs');
const { resolve } = require('path');
/**
 * @desc 配置文件说明
 * @param {*} key 匹配key
 * @param {*} target 代理地址目标地址
 * @param {*} token 直连平台使用的 X-CSRF-TOKEN
 * @param {*} cookie 直连平台使用的 Cookie
 */
const configPath = './proxy.config.json';

/**
 *
 * @description  :  获取配置文件
 * @param         {*} key:
 * @return        {*}
 **/
function getConfig(key) {
  try {
    const jsonStr = readFileSync(resolve(__dirname, configPath), 'utf-8');
    const arr = JSON.parse(jsonStr) || [];
    return arr.find((item) => item.key === key) || {};
  } catch {
    console.error('get proxy.config.json failed');
  }
}

/**
 *
 * @description  :  获取路由地址
 * @param         {*} key: 匹配key
 * @param         {*} req: 请求对象
 * @return        {*}
 **/
function getRouter(key, req) {
  const { target } = getConfig(key);

  return target;
}

/**
 *
 * @description  :  设置请求头
 * @param         {*} key: 匹配key
 * @param         {*} proxyReq: 请求对象
 * @return        {*}
 **/
function getReq(key, proxyReq) {
  const { token, cookie } = getConfig(key);
  if (token) proxyReq.setHeader('X-CSRF-TOKEN', token);
  if (cookie) proxyReq.setHeader('Cookie', cookie);
}
module.exports = {
  getRouter,
  getReq
};
