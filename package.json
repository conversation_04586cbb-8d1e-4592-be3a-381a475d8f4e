{"name": "pe.doctor", "version": "0.1.1", "private": true, "scripts": {"dev": "pnpm exec vue-cli-service serve", "serve": "pnpm exec vue-cli-service serve", "build": "node ./src/addVersion.js && pnpm exec vue-cli-service build", "check": "pnpm prettier --write .", "type-check": "vue-tsc --noEmit --diagnostics", "prepare": "pnpm check"}, "dependencies": {"@devexpress/analytics-core": "^23.1.4", "@microsoft/fetch-event-source": "^2.0.1", "@tinymce/tinymce-vue": "^3.0.1", "axios": "^0.27.2", "core-js": "^3.6.5", "date-fns": "^4.1.0", "devexpress-reporting": "^23.1.4", "devextreme": "^23.1.4", "echarts": "^5.3.2", "el-select-v2": "^1.3.0", "el-table-infinite-scroll": "^2.0.0", "element-ui": "^2.15.8", "encryptlong": "^3.1.4", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "github-markdown-css": "^5.8.1", "highlight.js": "^11.11.1", "js-cookie": "^3.0.5", "jszip": "^3.10.1", "knockout": "^3.5.1", "markdown-it": "^14.1.0", "markdown-it-highlightjs": "^4.2.0", "mockjs": "^1.1.0", "moment": "^2.29.3", "monaco-editor": "^0.30.0", "prettier": "^3.5.3", "pretty-data": "^0.40.0", "process": "^0.11.10", "sm-crypto": "^0.3.10", "tinymce": "^5.1.0", "vue": "^2.7.16", "vue-clipboard2": "^0.3.3", "vue-cropper": "^0.5.8", "vue-pdf": "^4.3.0", "vue-print-nb-jeecg": "^1.0.11", "vue-router": "^3.2.0", "vue-tsc": "^2.2.10", "vuedraggable": "^2.24.3", "vuex": "^3.4.0", "vxe-table": "~3.8.25", "xe-utils": "^3.5.4", "xlsx": "^0.16.9"}, "devDependencies": {"@types/node": "^22.15.17", "@types/webpack-env": "^1.18.8", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-pwa": "~5.0.8", "@vue/cli-plugin-router": "~5.0.8", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-plugin-vuex": "~5.0.8", "@vue/cli-service": "~5.0.8", "@vue/eslint-config-typescript": "^7.0.0", "crypto-js": "^4.1.1", "jsencrypt": "^3.2.1", "less": "^3.0.4", "less-loader": "^10.2.0", "script-loader": "^0.7.2", "typescript": "^5.8.3", "vue-class-component": "^7.2.6", "vue-property-decorator": "^9.1.2", "vue-template-compiler": "^2.6.11", "vuex-class": "^0.3.2", "webpack": "^5.89.0"}, "engines": {"node": ">=18.18.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}