{
  "compilerOptions": {
    // 指定ECMAScript目标版本: 'ES3' (默认), 'ES5', 'ES2015', 'ES2016', 'ES2017', 'ES2018', 'ES2019', 'ES2020', 'ES2021', 或 'ESNEXT'
    // 使用最新的ECMAScript特性
    "target": "esnext",

    // 指定生成的模块代码: 'none', 'commonjs', 'amd', 'system', 'umd', 'es2015', 'es2020', 或 'ESNext'
    // 使用ES模块语法，与现代打包工具兼容
    "module": "esnext",

    // 启用所有严格类型检查选项
    // 设置为false以允许更灵活的类型检查，适合逐步迁移到TypeScript的项目
    "strict": false,

    // 指定JSX代码的生成方式: 'preserve', 'react-native', 'react' 或 'react-jsx'
    // 保留JSX以便后续转换
    "jsx": "preserve",

    // 决定如何处理模块: 'node' (Node.js) 或 'classic' (TypeScript pre-1.6)
    // 使用Node.js风格的模块解析
    "moduleResolution": "node",

    // 跳过声明文件的类型检查
    // 可以提高编译性能，特别是使用第三方库时
    "skipLibCheck": true,

    // 启用CommonJS和ES模块之间的互操作性
    // 允许使用import语法导入CommonJS模块
    "esModuleInterop": true,

    // 允许从没有默认导出的模块中导入默认值
    // 简化了从非TypeScript库导入的语法
    "allowSyntheticDefaultImports": true,

    // 强制文件名大小写一致
    // 避免在不同操作系统间的文件名大小写问题
    "forceConsistentCasingInFileNames": true,

    // 使用ES2022+中的类字段标准行为
    // 影响类属性的初始化方式
    "useDefineForClassFields": true,

    // 生成相应的'.map'文件
    // 便于调试，可以在浏览器开发工具中查看原始TypeScript代码
    "sourceMap": true,

    // 解析非相对模块名的基准目录
    // 设置为当前目录，使导入路径更简洁
    "baseUrl": ".",

    // 要包含的类型声明文件
    // webpack-env: Webpack环境的类型定义
    // node: Node.js API的类型定义
    "types": ["webpack-env", "node"],

    // 模块名到基于baseUrl的路径映射
    // 允许使用@/作为src/的别名，简化导入路径
    "paths": {
      "@/*": ["src/*"]
    },

    // 编译过程中需要包含的库文件
    // esnext: 最新的ECMAScript特性
    // dom: DOM定义(如document, window等)
    // dom.iterable: DOM可迭代对象(如NodeList)
    // scripthost: Windows Script Host API
    "lib": ["esnext", "dom", "dom.iterable", "scripthost"],

    // 允许JavaScript文件被编译
    // 对于混合了JS和TS的项目很有用
    "allowJs": true,

    // 指定输出目录
    // 编译后的文件将放在此目录下
    "outDir": "dist",

    // 启用实验性的装饰器
    // Vue Class Component和其他库需要此选项
    "experimentalDecorators": true,

    // 为装饰器启用元数据API
    // 某些装饰器库需要此选项
    "emitDecoratorMetadata": true,

    // 解析JSON模块
    // 允许直接导入JSON文件
    "resolveJsonModule": true,

    // 不要在增量编译中删除注释
    "removeComments": false,

    // 生成声明文件
    // 为TypeScript文件生成对应的.d.ts文件
    "declaration": false,

    // 启用增量编译
    // 可以提高重复编译的性能
    "incremental": true
  },

  // 指定要包含的文件匹配模式
  // 包含所有src和tests目录下的TypeScript和Vue文件
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "tests/**/*.ts",
    "tests/**/*.tsx"
  ],

  // 指定要排除的文件匹配模式
  // 排除node_modules目录，提高编译性能
  "exclude": ["node_modules"]
}
