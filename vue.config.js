const webpack = require('webpack');
const { getRouter } = require('./proxy.switch.js');
const version = require('./package.json').version;
process.env.VUE_APP_VERSION = version;
const hash = `v${version}.${new Date().getTime()}`;
const isProduction = process.env.NODE_ENV === 'production';

module.exports = {
  publicPath: './',
  runtimeCompiler: true,
  configureWebpack: (config) => {
    // 忽略async_hooks模块
    config.plugins.push(
      new webpack.IgnorePlugin({
        resourceRegExp: /async_hooks/
      })
    );

    // 添加process的fallback
    config.resolve = {
      ...config.resolve,
      fallback: {
        ...config.resolve.fallback,
        process: require.resolve('process/browser')
      }
    };

    // 添加process的ProvidePlugin
    config.plugins.push(
      new webpack.ProvidePlugin({
        process: 'process/browser'
      })
    );

    if (isProduction) {
      config.optimization.minimizer[0].options.terserOptions.compress.drop_console = true;
      (config.output.filename = `js/[name].${hash}.js`),
        (config.output.chunkFilename = `js/[name].${hash}.js?hash=${hash}`);
    }
  },
  chainWebpack: (config) => {
    // 如果filenameHashing设置为了false，可以通过这段代码给打包出的css文件增加hash值
    if (isProduction) {
      config.plugin('extract-css').tap((args) => [
        {
          filename: `css/[name].${hash}.css`,
          chunkFilename: `css/[name].${hash}.css?hash=${hash}`
        }
      ]);
    }
  },
  // 生产环境是否生成 sourceMap 文件
  productionSourceMap: false,
  // css相关配置
  css: {
    // 是否使用css分离插件 ExtractTextPlugin
    extract: isProduction,
    // 开启 CSS source maps?
    sourceMap: false,
    // css预设器配置项
    loaderOptions: {
      less: {
        lessOptions: {
          javascriptEnabled: true
        }
      }
    }
  },

  // webpack-dev-server 相关配置
  devServer: {
    host: '0.0.0.0', // web主机地址
    port: 9000, // web端口号

    https: false,
    hot: true, // hot配置是否启用模块的热替换功能，devServer的默认行为是在发现源代码被变更后，通过自动刷新整个页面来做到事实预览，开启hot后，将在不刷新整个页面的情况下通过新模块替换老模块来做到实时预览
    // hotOnly选项在webpack 5中已被移除
    proxy: {
      '/api/*': {
        // 后端api
        target: 'localhost', // 目标服务器地址
        changeOrigin: true, // 是否改变请求源
        logLevel: 'warn', // 日志级别
        router: (req) => {
          // 自定义路由函数，用于动态设置目标服务器地址
          return getRouter('api', req);
        }
      },
      '/socket/*': {
        // 后端-socket
        target: 'localhost',
        changeOrigin: true,
        ws: true,
        logLevel: 'warn',
        router: (req) => {
          return getRouter('socket', req);
        }
      },
      '/static-file/*': {
        // 静态文件
        target: 'localhost',
        changeOrigin: true,
        logLevel: 'warn',
        pathRewrite: {
          '^/static-file/': '/'
        },
        router: (req) => {
          return getRouter('static-file', req);
        }
      },
      '/dev-report/*': {
        // dev报告
        target: 'localhost',
        changeOrigin: true,
        logLevel: 'warn',
        pathRewrite: {
          '^/dev-report/': '/'
        },
        router: (req) => {
          return getRouter('dev-report', req);
        }
      }
    }
  }
};
