###  开发规范
    ---class类名、methods方法名：小驼峰命名  如：loginPage;
    ---id：大驼峰命名  如：TestId;
    ---ref: 下划线命名  如：test_Ref


###  common文件夹
    ---ajax.js： 请求数据插件的封装；
    ---apiUrls.js： 接口列表；
    ---dataUtils.js: 一些处理数据的工具类；
    ---storage.js: 浏览器缓存的封装；

###  调用示例
    <!-- 接口 -->
    this.$ajax.post(this.$apiUrls.login,data).then((r)=>{

    })

    <!-- 浏览器缓存 -->
    import {storage} from '@/common'

    storage.session.set('test',data)
    storage.session.get('test')


### 多页面
    ---主入口文件： views/index.vue
    ---多页面切换的页面全部放在pages文件夹里，注：这个文件夹下的vue文件必须加上name，name需跟文件名字一样；
